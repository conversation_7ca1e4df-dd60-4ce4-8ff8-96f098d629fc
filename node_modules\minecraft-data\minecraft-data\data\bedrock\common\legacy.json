{"blocks": {"0:0": "minecraft:air", "1:0": "minecraft:stone[stone_type=stone]", "1:1": "minecraft:stone[stone_type=granite]", "1:2": "minecraft:stone[stone_type=granite_smooth]", "1:3": "minecraft:stone[stone_type=diorite]", "1:4": "minecraft:stone[stone_type=diorite_smooth]", "1:5": "minecraft:stone[stone_type=andesite]", "1:6": "minecraft:stone[stone_type=andesite_smooth]", "2:0": "minecraft:grass", "3:0": "minecraft:dirt[dirt_type=normal]", "3:1": "minecraft:dirt[dirt_type=coarse]", "4:0": "minecraft:cobblestone", "5:0": "minecraft:planks[wood_type=oak]", "5:1": "minecraft:planks[wood_type=spruce]", "5:2": "minecraft:planks[wood_type=birch]", "5:3": "minecraft:planks[wood_type=jungle]", "5:4": "minecraft:planks[wood_type=acacia]", "5:5": "minecraft:planks[wood_type=dark_oak]", "6:0": "minecraft:sapling[age_bit=0,sapling_type=oak]", "6:1": "minecraft:sapling[age_bit=0,sapling_type=spruce]", "6:2": "minecraft:sapling[age_bit=0,sapling_type=birch]", "6:3": "minecraft:sapling[age_bit=0,sapling_type=jungle]", "6:4": "minecraft:sapling[age_bit=0,sapling_type=acacia]", "6:5": "minecraft:sapling[age_bit=0,sapling_type=dark_oak]", "6:6": "minecraft:sapling[age_bit=1,sapling_type=oak]", "6:7": "minecraft:sapling[age_bit=1,sapling_type=spruce]", "6:8": "minecraft:sapling[age_bit=1,sapling_type=birch]", "6:9": "minecraft:sapling[age_bit=1,sapling_type=jungle]", "6:10": "minecraft:sapling[age_bit=1,sapling_type=acacia]", "6:11": "minecraft:sapling[age_bit=1,sapling_type=dark_oak]", "7:0": "minecraft:bedrock[infiniburn_bit=0]", "7:1": "minecraft:bedrock[infiniburn_bit=1]", "8:0": "minecraft:flowing_water[liquid_depth=0]", "8:1": "minecraft:flowing_water[liquid_depth=1]", "8:2": "minecraft:flowing_water[liquid_depth=2]", "8:3": "minecraft:flowing_water[liquid_depth=3]", "8:4": "minecraft:flowing_water[liquid_depth=4]", "8:5": "minecraft:flowing_water[liquid_depth=5]", "8:6": "minecraft:flowing_water[liquid_depth=6]", "8:7": "minecraft:flowing_water[liquid_depth=7]", "8:8": "minecraft:flowing_water[liquid_depth=8]", "8:9": "minecraft:flowing_water[liquid_depth=9]", "8:10": "minecraft:flowing_water[liquid_depth=10]", "8:11": "minecraft:flowing_water[liquid_depth=11]", "8:12": "minecraft:flowing_water[liquid_depth=12]", "8:13": "minecraft:flowing_water[liquid_depth=13]", "8:14": "minecraft:flowing_water[liquid_depth=14]", "8:15": "minecraft:flowing_water[liquid_depth=15]", "9:0": "minecraft:water[liquid_depth=0]", "9:1": "minecraft:water[liquid_depth=1]", "9:2": "minecraft:water[liquid_depth=2]", "9:3": "minecraft:water[liquid_depth=3]", "9:4": "minecraft:water[liquid_depth=4]", "9:5": "minecraft:water[liquid_depth=5]", "9:6": "minecraft:water[liquid_depth=6]", "9:7": "minecraft:water[liquid_depth=7]", "9:8": "minecraft:water[liquid_depth=8]", "9:9": "minecraft:water[liquid_depth=9]", "9:10": "minecraft:water[liquid_depth=10]", "9:11": "minecraft:water[liquid_depth=11]", "9:12": "minecraft:water[liquid_depth=12]", "9:13": "minecraft:water[liquid_depth=13]", "9:14": "minecraft:water[liquid_depth=14]", "9:15": "minecraft:water[liquid_depth=15]", "10:0": "minecraft:flowing_lava[liquid_depth=0]", "10:1": "minecraft:flowing_lava[liquid_depth=1]", "10:2": "minecraft:flowing_lava[liquid_depth=2]", "10:3": "minecraft:flowing_lava[liquid_depth=3]", "10:4": "minecraft:flowing_lava[liquid_depth=4]", "10:5": "minecraft:flowing_lava[liquid_depth=5]", "10:6": "minecraft:flowing_lava[liquid_depth=6]", "10:7": "minecraft:flowing_lava[liquid_depth=7]", "10:8": "minecraft:flowing_lava[liquid_depth=8]", "10:9": "minecraft:flowing_lava[liquid_depth=9]", "10:10": "minecraft:flowing_lava[liquid_depth=10]", "10:11": "minecraft:flowing_lava[liquid_depth=11]", "10:12": "minecraft:flowing_lava[liquid_depth=12]", "10:13": "minecraft:flowing_lava[liquid_depth=13]", "10:14": "minecraft:flowing_lava[liquid_depth=14]", "10:15": "minecraft:flowing_lava[liquid_depth=15]", "11:0": "minecraft:lava[liquid_depth=0]", "11:1": "minecraft:lava[liquid_depth=1]", "11:2": "minecraft:lava[liquid_depth=2]", "11:3": "minecraft:lava[liquid_depth=3]", "11:4": "minecraft:lava[liquid_depth=4]", "11:5": "minecraft:lava[liquid_depth=5]", "11:6": "minecraft:lava[liquid_depth=6]", "11:7": "minecraft:lava[liquid_depth=7]", "11:8": "minecraft:lava[liquid_depth=8]", "11:9": "minecraft:lava[liquid_depth=9]", "11:10": "minecraft:lava[liquid_depth=10]", "11:11": "minecraft:lava[liquid_depth=11]", "11:12": "minecraft:lava[liquid_depth=12]", "11:13": "minecraft:lava[liquid_depth=13]", "11:14": "minecraft:lava[liquid_depth=14]", "11:15": "minecraft:lava[liquid_depth=15]", "12:0": "minecraft:sand[sand_type=normal]", "12:1": "minecraft:sand[sand_type=red]", "13:0": "minecraft:gravel", "14:0": "minecraft:gold_ore", "15:0": "minecraft:iron_ore", "16:0": "minecraft:coal_ore", "17:0": "minecraft:log[old_log_type=oak,pillar_axis=y]", "17:1": "minecraft:log[old_log_type=spruce,pillar_axis=y]", "17:2": "minecraft:log[old_log_type=birch,pillar_axis=y]", "17:3": "minecraft:log[old_log_type=jungle,pillar_axis=y]", "17:4": "minecraft:log[old_log_type=oak,pillar_axis=x]", "17:5": "minecraft:log[old_log_type=spruce,pillar_axis=x]", "17:6": "minecraft:log[old_log_type=birch,pillar_axis=x]", "17:7": "minecraft:log[old_log_type=jungle,pillar_axis=x]", "17:8": "minecraft:log[old_log_type=oak,pillar_axis=z]", "17:9": "minecraft:log[old_log_type=spruce,pillar_axis=z]", "17:10": "minecraft:log[old_log_type=birch,pillar_axis=z]", "17:11": "minecraft:log[old_log_type=jungle,pillar_axis=z]", "18:0": "minecraft:leaves[old_leaf_type=oak,persistent_bit=0,update_bit=0]", "18:1": "minecraft:leaves[old_leaf_type=spruce,persistent_bit=0,update_bit=0]", "18:2": "minecraft:leaves[old_leaf_type=birch,persistent_bit=0,update_bit=0]", "18:3": "minecraft:leaves[old_leaf_type=jungle,persistent_bit=0,update_bit=0]", "18:4": "minecraft:leaves[old_leaf_type=oak,persistent_bit=0,update_bit=1]", "18:5": "minecraft:leaves[old_leaf_type=spruce,persistent_bit=0,update_bit=1]", "18:6": "minecraft:leaves[old_leaf_type=birch,persistent_bit=0,update_bit=1]", "18:7": "minecraft:leaves[old_leaf_type=jungle,persistent_bit=0,update_bit=1]", "18:8": "minecraft:leaves[old_leaf_type=oak,persistent_bit=1,update_bit=0]", "18:9": "minecraft:leaves[old_leaf_type=spruce,persistent_bit=1,update_bit=0]", "18:10": "minecraft:leaves[old_leaf_type=birch,persistent_bit=1,update_bit=0]", "18:11": "minecraft:leaves[old_leaf_type=jungle,persistent_bit=1,update_bit=0]", "18:12": "minecraft:leaves[old_leaf_type=oak,persistent_bit=1,update_bit=1]", "18:13": "minecraft:leaves[old_leaf_type=spruce,persistent_bit=1,update_bit=1]", "18:14": "minecraft:leaves[old_leaf_type=birch,persistent_bit=1,update_bit=1]", "18:15": "minecraft:leaves[old_leaf_type=jungle,persistent_bit=1,update_bit=1]", "19:0": "minecraft:sponge[sponge_type=dry]", "19:1": "minecraft:sponge[sponge_type=wet]", "20:0": "minecraft:glass", "21:0": "minecraft:lapis_ore", "22:0": "minecraft:lapis_block", "23:0": "minecraft:dispenser[facing_direction=0,triggered_bit=0]", "23:1": "minecraft:dispenser[facing_direction=1,triggered_bit=0]", "23:2": "minecraft:dispenser[facing_direction=2,triggered_bit=0]", "23:3": "minecraft:dispenser[facing_direction=3,triggered_bit=0]", "23:4": "minecraft:dispenser[facing_direction=4,triggered_bit=0]", "23:5": "minecraft:dispenser[facing_direction=5,triggered_bit=0]", "23:6": "minecraft:dispenser[facing_direction=0,triggered_bit=1]", "23:7": "minecraft:dispenser[facing_direction=1,triggered_bit=1]", "23:8": "minecraft:dispenser[facing_direction=2,triggered_bit=1]", "23:9": "minecraft:dispenser[facing_direction=3,triggered_bit=1]", "23:10": "minecraft:dispenser[facing_direction=4,triggered_bit=1]", "23:11": "minecraft:dispenser[facing_direction=5,triggered_bit=1]", "24:0": "minecraft:sandstone[sand_stone_type=default]", "24:1": "minecraft:sandstone[sand_stone_type=heiroglyphs]", "24:2": "minecraft:sandstone[sand_stone_type=cut]", "24:3": "minecraft:sandstone[sand_stone_type=smooth]", "25:0": "minecraft:noteblock", "26:0": "minecraft:bed[direction=0,head_piece_bit=0,occupied_bit=0]", "26:1": "minecraft:bed[direction=1,head_piece_bit=0,occupied_bit=0]", "26:2": "minecraft:bed[direction=2,head_piece_bit=0,occupied_bit=0]", "26:3": "minecraft:bed[direction=3,head_piece_bit=0,occupied_bit=0]", "26:4": "minecraft:bed[direction=0,head_piece_bit=0,occupied_bit=1]", "26:5": "minecraft:bed[direction=1,head_piece_bit=0,occupied_bit=1]", "26:6": "minecraft:bed[direction=2,head_piece_bit=0,occupied_bit=1]", "26:7": "minecraft:bed[direction=3,head_piece_bit=0,occupied_bit=1]", "26:8": "minecraft:bed[direction=0,head_piece_bit=1,occupied_bit=0]", "26:9": "minecraft:bed[direction=1,head_piece_bit=1,occupied_bit=0]", "26:10": "minecraft:bed[direction=2,head_piece_bit=1,occupied_bit=0]", "26:11": "minecraft:bed[direction=3,head_piece_bit=1,occupied_bit=0]", "26:12": "minecraft:bed[direction=0,head_piece_bit=1,occupied_bit=1]", "26:13": "minecraft:bed[direction=1,head_piece_bit=1,occupied_bit=1]", "26:14": "minecraft:bed[direction=2,head_piece_bit=1,occupied_bit=1]", "26:15": "minecraft:bed[direction=3,head_piece_bit=1,occupied_bit=1]", "27:0": "minecraft:golden_rail[rail_data_bit=0,rail_direction=0]", "27:1": "minecraft:golden_rail[rail_data_bit=0,rail_direction=1]", "27:2": "minecraft:golden_rail[rail_data_bit=0,rail_direction=2]", "27:3": "minecraft:golden_rail[rail_data_bit=0,rail_direction=3]", "27:4": "minecraft:golden_rail[rail_data_bit=0,rail_direction=4]", "27:5": "minecraft:golden_rail[rail_data_bit=0,rail_direction=5]", "27:6": "minecraft:golden_rail[rail_data_bit=1,rail_direction=0]", "27:7": "minecraft:golden_rail[rail_data_bit=1,rail_direction=1]", "27:8": "minecraft:golden_rail[rail_data_bit=1,rail_direction=2]", "27:9": "minecraft:golden_rail[rail_data_bit=1,rail_direction=3]", "27:10": "minecraft:golden_rail[rail_data_bit=1,rail_direction=4]", "27:11": "minecraft:golden_rail[rail_data_bit=1,rail_direction=5]", "28:0": "minecraft:detector_rail[rail_data_bit=0,rail_direction=0]", "28:1": "minecraft:detector_rail[rail_data_bit=0,rail_direction=1]", "28:2": "minecraft:detector_rail[rail_data_bit=0,rail_direction=2]", "28:3": "minecraft:detector_rail[rail_data_bit=0,rail_direction=3]", "28:4": "minecraft:detector_rail[rail_data_bit=0,rail_direction=4]", "28:5": "minecraft:detector_rail[rail_data_bit=0,rail_direction=5]", "28:6": "minecraft:detector_rail[rail_data_bit=1,rail_direction=0]", "28:7": "minecraft:detector_rail[rail_data_bit=1,rail_direction=1]", "28:8": "minecraft:detector_rail[rail_data_bit=1,rail_direction=2]", "28:9": "minecraft:detector_rail[rail_data_bit=1,rail_direction=3]", "28:10": "minecraft:detector_rail[rail_data_bit=1,rail_direction=4]", "28:11": "minecraft:detector_rail[rail_data_bit=1,rail_direction=5]", "29:0": "minecraft:sticky_piston[facing_direction=0]", "29:1": "minecraft:sticky_piston[facing_direction=1]", "29:2": "minecraft:sticky_piston[facing_direction=2]", "29:3": "minecraft:sticky_piston[facing_direction=3]", "29:4": "minecraft:sticky_piston[facing_direction=4]", "29:5": "minecraft:sticky_piston[facing_direction=5]", "30:0": "minecraft:web", "31:0": "minecraft:tallgrass[tall_grass_type=default]", "31:1": "minecraft:tallgrass[tall_grass_type=tall]", "31:2": "minecraft:tallgrass[tall_grass_type=fern]", "31:3": "minecraft:tallgrass[tall_grass_type=snow]", "32:0": "minecraft:deadbush", "33:0": "minecraft:piston[facing_direction=0]", "33:1": "minecraft:piston[facing_direction=1]", "33:2": "minecraft:piston[facing_direction=2]", "33:3": "minecraft:piston[facing_direction=3]", "33:4": "minecraft:piston[facing_direction=4]", "33:5": "minecraft:piston[facing_direction=5]", "34:0": "minecraft:pistonArmCollision[facing_direction=0]", "34:1": "minecraft:pistonArmCollision[facing_direction=1]", "34:2": "minecraft:pistonArmCollision[facing_direction=2]", "34:3": "minecraft:pistonArmCollision[facing_direction=3]", "34:4": "minecraft:pistonArmCollision[facing_direction=4]", "34:5": "minecraft:pistonArmCollision[facing_direction=5]", "35:0": "minecraft:wool[color=white]", "35:1": "minecraft:wool[color=orange]", "35:2": "minecraft:wool[color=magenta]", "35:3": "minecraft:wool[color=light_blue]", "35:4": "minecraft:wool[color=yellow]", "35:5": "minecraft:wool[color=lime]", "35:6": "minecraft:wool[color=pink]", "35:7": "minecraft:wool[color=gray]", "35:8": "minecraft:wool[color=silver]", "35:9": "minecraft:wool[color=cyan]", "35:10": "minecraft:wool[color=purple]", "35:11": "minecraft:wool[color=blue]", "35:12": "minecraft:wool[color=brown]", "35:13": "minecraft:wool[color=green]", "35:14": "minecraft:wool[color=red]", "35:15": "minecraft:wool[color=black]", "36:0": "minecraft:element_0", "37:0": "minecraft:yellow_flower", "38:0": "minecraft:red_flower[flower_type=poppy]", "38:1": "minecraft:red_flower[flower_type=orchid]", "38:2": "minecraft:red_flower[flower_type=allium]", "38:3": "minecraft:red_flower[flower_type=houstonia]", "38:4": "minecraft:red_flower[flower_type=tulip_red]", "38:5": "minecraft:red_flower[flower_type=tulip_orange]", "38:6": "minecraft:red_flower[flower_type=tulip_white]", "38:7": "minecraft:red_flower[flower_type=tulip_pink]", "38:8": "minecraft:red_flower[flower_type=oxeye]", "38:9": "minecraft:red_flower[flower_type=cornflower]", "38:10": "minecraft:red_flower[flower_type=lily_of_the_valley]", "39:0": "minecraft:brown_mushroom", "40:0": "minecraft:red_mushroom", "41:0": "minecraft:gold_block", "42:0": "minecraft:iron_block", "43:0": "minecraft:double_stone_slab[stone_slab_type=smooth_stone,top_slot_bit=0]", "43:1": "minecraft:double_stone_slab[stone_slab_type=sandstone,top_slot_bit=0]", "43:2": "minecraft:double_stone_slab[stone_slab_type=wood,top_slot_bit=0]", "43:3": "minecraft:double_stone_slab[stone_slab_type=cobblestone,top_slot_bit=0]", "43:4": "minecraft:double_stone_slab[stone_slab_type=brick,top_slot_bit=0]", "43:5": "minecraft:double_stone_slab[stone_slab_type=stone_brick,top_slot_bit=0]", "43:6": "minecraft:double_stone_slab[stone_slab_type=quartz,top_slot_bit=0]", "43:7": "minecraft:double_stone_slab[stone_slab_type=nether_brick,top_slot_bit=0]", "43:8": "minecraft:double_stone_slab[stone_slab_type=smooth_stone,top_slot_bit=1]", "43:9": "minecraft:double_stone_slab[stone_slab_type=sandstone,top_slot_bit=1]", "43:10": "minecraft:double_stone_slab[stone_slab_type=wood,top_slot_bit=1]", "43:11": "minecraft:double_stone_slab[stone_slab_type=cobblestone,top_slot_bit=1]", "43:12": "minecraft:double_stone_slab[stone_slab_type=brick,top_slot_bit=1]", "43:13": "minecraft:double_stone_slab[stone_slab_type=stone_brick,top_slot_bit=1]", "43:14": "minecraft:double_stone_slab[stone_slab_type=quartz,top_slot_bit=1]", "43:15": "minecraft:double_stone_slab[stone_slab_type=nether_brick,top_slot_bit=1]", "44:0": "minecraft:stone_slab[stone_slab_type=smooth_stone,top_slot_bit=0]", "44:1": "minecraft:stone_slab[stone_slab_type=sandstone,top_slot_bit=0]", "44:2": "minecraft:stone_slab[stone_slab_type=wood,top_slot_bit=0]", "44:3": "minecraft:stone_slab[stone_slab_type=cobblestone,top_slot_bit=0]", "44:4": "minecraft:stone_slab[stone_slab_type=brick,top_slot_bit=0]", "44:5": "minecraft:stone_slab[stone_slab_type=stone_brick,top_slot_bit=0]", "44:6": "minecraft:stone_slab[stone_slab_type=quartz,top_slot_bit=0]", "44:7": "minecraft:stone_slab[stone_slab_type=nether_brick,top_slot_bit=0]", "44:8": "minecraft:stone_slab[stone_slab_type=smooth_stone,top_slot_bit=1]", "44:9": "minecraft:stone_slab[stone_slab_type=sandstone,top_slot_bit=1]", "44:10": "minecraft:stone_slab[stone_slab_type=wood,top_slot_bit=1]", "44:11": "minecraft:stone_slab[stone_slab_type=cobblestone,top_slot_bit=1]", "44:12": "minecraft:stone_slab[stone_slab_type=brick,top_slot_bit=1]", "44:13": "minecraft:stone_slab[stone_slab_type=stone_brick,top_slot_bit=1]", "44:14": "minecraft:stone_slab[stone_slab_type=quartz,top_slot_bit=1]", "44:15": "minecraft:stone_slab[stone_slab_type=nether_brick,top_slot_bit=1]", "45:0": "minecraft:brick_block", "46:0": "minecraft:tnt[allow_underwater_bit=0,explode_bit=0]", "46:1": "minecraft:tnt[allow_underwater_bit=0,explode_bit=1]", "46:2": "minecraft:tnt[allow_underwater_bit=1,explode_bit=0]", "46:3": "minecraft:tnt[allow_underwater_bit=1,explode_bit=1]", "47:0": "minecraft:bookshelf", "48:0": "minecraft:mossy_cobblestone", "49:0": "minecraft:obsidian", "50:0": "minecraft:torch[torch_facing_direction=unknown]", "50:1": "minecraft:torch[torch_facing_direction=west]", "50:2": "minecraft:torch[torch_facing_direction=east]", "50:3": "minecraft:torch[torch_facing_direction=north]", "50:4": "minecraft:torch[torch_facing_direction=south]", "50:5": "minecraft:torch[torch_facing_direction=top]", "51:0": "minecraft:fire[age=0]", "51:1": "minecraft:fire[age=1]", "51:2": "minecraft:fire[age=2]", "51:3": "minecraft:fire[age=3]", "51:4": "minecraft:fire[age=4]", "51:5": "minecraft:fire[age=5]", "51:6": "minecraft:fire[age=6]", "51:7": "minecraft:fire[age=7]", "51:8": "minecraft:fire[age=8]", "51:9": "minecraft:fire[age=9]", "51:10": "minecraft:fire[age=10]", "51:11": "minecraft:fire[age=11]", "51:12": "minecraft:fire[age=12]", "51:13": "minecraft:fire[age=13]", "51:14": "minecraft:fire[age=14]", "51:15": "minecraft:fire[age=15]", "52:0": "minecraft:mob_spawner", "53:0": "minecraft:oak_stairs[upside_down_bit=0,weirdo_direction=0]", "53:1": "minecraft:oak_stairs[upside_down_bit=0,weirdo_direction=1]", "53:2": "minecraft:oak_stairs[upside_down_bit=0,weirdo_direction=2]", "53:3": "minecraft:oak_stairs[upside_down_bit=0,weirdo_direction=3]", "53:4": "minecraft:oak_stairs[upside_down_bit=1,weirdo_direction=0]", "53:5": "minecraft:oak_stairs[upside_down_bit=1,weirdo_direction=1]", "53:6": "minecraft:oak_stairs[upside_down_bit=1,weirdo_direction=2]", "53:7": "minecraft:oak_stairs[upside_down_bit=1,weirdo_direction=3]", "54:0": "minecraft:chest[facing_direction=0]", "54:1": "minecraft:chest[facing_direction=1]", "54:2": "minecraft:chest[facing_direction=2]", "54:3": "minecraft:chest[facing_direction=3]", "54:4": "minecraft:chest[facing_direction=4]", "54:5": "minecraft:chest[facing_direction=5]", "55:0": "minecraft:redstone_wire[redstone_signal=0]", "55:1": "minecraft:redstone_wire[redstone_signal=1]", "55:2": "minecraft:redstone_wire[redstone_signal=2]", "55:3": "minecraft:redstone_wire[redstone_signal=3]", "55:4": "minecraft:redstone_wire[redstone_signal=4]", "55:5": "minecraft:redstone_wire[redstone_signal=5]", "55:6": "minecraft:redstone_wire[redstone_signal=6]", "55:7": "minecraft:redstone_wire[redstone_signal=7]", "55:8": "minecraft:redstone_wire[redstone_signal=8]", "55:9": "minecraft:redstone_wire[redstone_signal=9]", "55:10": "minecraft:redstone_wire[redstone_signal=10]", "55:11": "minecraft:redstone_wire[redstone_signal=11]", "55:12": "minecraft:redstone_wire[redstone_signal=12]", "55:13": "minecraft:redstone_wire[redstone_signal=13]", "55:14": "minecraft:redstone_wire[redstone_signal=14]", "55:15": "minecraft:redstone_wire[redstone_signal=15]", "56:0": "minecraft:diamond_ore", "57:0": "minecraft:diamond_block", "58:0": "minecraft:crafting_table", "59:0": "minecraft:wheat[growth=0]", "59:1": "minecraft:wheat[growth=1]", "59:2": "minecraft:wheat[growth=2]", "59:3": "minecraft:wheat[growth=3]", "59:4": "minecraft:wheat[growth=4]", "59:5": "minecraft:wheat[growth=5]", "59:6": "minecraft:wheat[growth=6]", "59:7": "minecraft:wheat[growth=7]", "60:0": "minecraft:farmland[moisturized_amount=0]", "60:1": "minecraft:farmland[moisturized_amount=1]", "60:2": "minecraft:farmland[moisturized_amount=2]", "60:3": "minecraft:farmland[moisturized_amount=3]", "60:4": "minecraft:farmland[moisturized_amount=4]", "60:5": "minecraft:farmland[moisturized_amount=5]", "60:6": "minecraft:farmland[moisturized_amount=6]", "60:7": "minecraft:farmland[moisturized_amount=7]", "61:0": "minecraft:furnace[facing_direction=0]", "61:1": "minecraft:furnace[facing_direction=1]", "61:2": "minecraft:furnace[facing_direction=2]", "61:3": "minecraft:furnace[facing_direction=3]", "61:4": "minecraft:furnace[facing_direction=4]", "61:5": "minecraft:furnace[facing_direction=5]", "62:0": "minecraft:lit_furnace[facing_direction=0]", "62:1": "minecraft:lit_furnace[facing_direction=1]", "62:2": "minecraft:lit_furnace[facing_direction=2]", "62:3": "minecraft:lit_furnace[facing_direction=3]", "62:4": "minecraft:lit_furnace[facing_direction=4]", "62:5": "minecraft:lit_furnace[facing_direction=5]", "63:0": "minecraft:standing_sign[ground_sign_direction=0]", "63:1": "minecraft:standing_sign[ground_sign_direction=1]", "63:2": "minecraft:standing_sign[ground_sign_direction=2]", "63:3": "minecraft:standing_sign[ground_sign_direction=3]", "63:4": "minecraft:standing_sign[ground_sign_direction=4]", "63:5": "minecraft:standing_sign[ground_sign_direction=5]", "63:6": "minecraft:standing_sign[ground_sign_direction=6]", "63:7": "minecraft:standing_sign[ground_sign_direction=7]", "63:8": "minecraft:standing_sign[ground_sign_direction=8]", "63:9": "minecraft:standing_sign[ground_sign_direction=9]", "63:10": "minecraft:standing_sign[ground_sign_direction=10]", "63:11": "minecraft:standing_sign[ground_sign_direction=11]", "63:12": "minecraft:standing_sign[ground_sign_direction=12]", "63:13": "minecraft:standing_sign[ground_sign_direction=13]", "63:14": "minecraft:standing_sign[ground_sign_direction=14]", "63:15": "minecraft:standing_sign[ground_sign_direction=15]", "64:0": "minecraft:wooden_door[direction=0,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "64:1": "minecraft:wooden_door[direction=1,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "64:2": "minecraft:wooden_door[direction=2,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "64:3": "minecraft:wooden_door[direction=3,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "64:4": "minecraft:wooden_door[direction=0,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "64:5": "minecraft:wooden_door[direction=1,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "64:6": "minecraft:wooden_door[direction=2,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "64:7": "minecraft:wooden_door[direction=3,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "64:8": "minecraft:wooden_door[direction=0,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "64:9": "minecraft:wooden_door[direction=1,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "64:10": "minecraft:wooden_door[direction=2,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "64:11": "minecraft:wooden_door[direction=3,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "64:12": "minecraft:wooden_door[direction=0,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "64:13": "minecraft:wooden_door[direction=1,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "64:14": "minecraft:wooden_door[direction=2,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "64:15": "minecraft:wooden_door[direction=3,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "65:0": "minecraft:ladder[facing_direction=0]", "65:1": "minecraft:ladder[facing_direction=1]", "65:2": "minecraft:ladder[facing_direction=2]", "65:3": "minecraft:ladder[facing_direction=3]", "65:4": "minecraft:ladder[facing_direction=4]", "65:5": "minecraft:ladder[facing_direction=5]", "66:0": "minecraft:rail[rail_direction=0]", "66:1": "minecraft:rail[rail_direction=1]", "66:2": "minecraft:rail[rail_direction=2]", "66:3": "minecraft:rail[rail_direction=3]", "66:4": "minecraft:rail[rail_direction=4]", "66:5": "minecraft:rail[rail_direction=5]", "66:6": "minecraft:rail[rail_direction=6]", "66:7": "minecraft:rail[rail_direction=7]", "66:8": "minecraft:rail[rail_direction=8]", "66:9": "minecraft:rail[rail_direction=9]", "67:0": "minecraft:stone_stairs[upside_down_bit=0,weirdo_direction=0]", "67:1": "minecraft:stone_stairs[upside_down_bit=0,weirdo_direction=1]", "67:2": "minecraft:stone_stairs[upside_down_bit=0,weirdo_direction=2]", "67:3": "minecraft:stone_stairs[upside_down_bit=0,weirdo_direction=3]", "67:4": "minecraft:stone_stairs[upside_down_bit=1,weirdo_direction=0]", "67:5": "minecraft:stone_stairs[upside_down_bit=1,weirdo_direction=1]", "67:6": "minecraft:stone_stairs[upside_down_bit=1,weirdo_direction=2]", "67:7": "minecraft:stone_stairs[upside_down_bit=1,weirdo_direction=3]", "68:0": "minecraft:wall_sign[facing_direction=0]", "68:1": "minecraft:wall_sign[facing_direction=1]", "68:2": "minecraft:wall_sign[facing_direction=2]", "68:3": "minecraft:wall_sign[facing_direction=3]", "68:4": "minecraft:wall_sign[facing_direction=4]", "68:5": "minecraft:wall_sign[facing_direction=5]", "69:0": "minecraft:lever[lever_direction=down_east_west,open_bit=0]", "69:1": "minecraft:lever[lever_direction=east,open_bit=0]", "69:2": "minecraft:lever[lever_direction=west,open_bit=0]", "69:3": "minecraft:lever[lever_direction=south,open_bit=0]", "69:4": "minecraft:lever[lever_direction=north,open_bit=0]", "69:5": "minecraft:lever[lever_direction=up_north_south,open_bit=0]", "69:6": "minecraft:lever[lever_direction=up_east_west,open_bit=0]", "69:7": "minecraft:lever[lever_direction=down_north_south,open_bit=0]", "69:8": "minecraft:lever[lever_direction=down_east_west,open_bit=1]", "69:9": "minecraft:lever[lever_direction=east,open_bit=1]", "69:10": "minecraft:lever[lever_direction=west,open_bit=1]", "69:11": "minecraft:lever[lever_direction=south,open_bit=1]", "69:12": "minecraft:lever[lever_direction=north,open_bit=1]", "69:13": "minecraft:lever[lever_direction=up_north_south,open_bit=1]", "69:14": "minecraft:lever[lever_direction=up_east_west,open_bit=1]", "69:15": "minecraft:lever[lever_direction=down_north_south,open_bit=1]", "70:0": "minecraft:stone_pressure_plate[redstone_signal=0]", "70:1": "minecraft:stone_pressure_plate[redstone_signal=1]", "70:2": "minecraft:stone_pressure_plate[redstone_signal=2]", "70:3": "minecraft:stone_pressure_plate[redstone_signal=3]", "70:4": "minecraft:stone_pressure_plate[redstone_signal=4]", "70:5": "minecraft:stone_pressure_plate[redstone_signal=5]", "70:6": "minecraft:stone_pressure_plate[redstone_signal=6]", "70:7": "minecraft:stone_pressure_plate[redstone_signal=7]", "70:8": "minecraft:stone_pressure_plate[redstone_signal=8]", "70:9": "minecraft:stone_pressure_plate[redstone_signal=9]", "70:10": "minecraft:stone_pressure_plate[redstone_signal=10]", "70:11": "minecraft:stone_pressure_plate[redstone_signal=11]", "70:12": "minecraft:stone_pressure_plate[redstone_signal=12]", "70:13": "minecraft:stone_pressure_plate[redstone_signal=13]", "70:14": "minecraft:stone_pressure_plate[redstone_signal=14]", "70:15": "minecraft:stone_pressure_plate[redstone_signal=15]", "71:0": "minecraft:iron_door[direction=0,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "71:1": "minecraft:iron_door[direction=1,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "71:2": "minecraft:iron_door[direction=2,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "71:3": "minecraft:iron_door[direction=3,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "71:4": "minecraft:iron_door[direction=0,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "71:5": "minecraft:iron_door[direction=1,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "71:6": "minecraft:iron_door[direction=2,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "71:7": "minecraft:iron_door[direction=3,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "71:8": "minecraft:iron_door[direction=0,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "71:9": "minecraft:iron_door[direction=1,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "71:10": "minecraft:iron_door[direction=2,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "71:11": "minecraft:iron_door[direction=3,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "71:12": "minecraft:iron_door[direction=0,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "71:13": "minecraft:iron_door[direction=1,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "71:14": "minecraft:iron_door[direction=2,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "71:15": "minecraft:iron_door[direction=3,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "72:0": "minecraft:wooden_pressure_plate[redstone_signal=0]", "72:1": "minecraft:wooden_pressure_plate[redstone_signal=1]", "72:2": "minecraft:wooden_pressure_plate[redstone_signal=2]", "72:3": "minecraft:wooden_pressure_plate[redstone_signal=3]", "72:4": "minecraft:wooden_pressure_plate[redstone_signal=4]", "72:5": "minecraft:wooden_pressure_plate[redstone_signal=5]", "72:6": "minecraft:wooden_pressure_plate[redstone_signal=6]", "72:7": "minecraft:wooden_pressure_plate[redstone_signal=7]", "72:8": "minecraft:wooden_pressure_plate[redstone_signal=8]", "72:9": "minecraft:wooden_pressure_plate[redstone_signal=9]", "72:10": "minecraft:wooden_pressure_plate[redstone_signal=10]", "72:11": "minecraft:wooden_pressure_plate[redstone_signal=11]", "72:12": "minecraft:wooden_pressure_plate[redstone_signal=12]", "72:13": "minecraft:wooden_pressure_plate[redstone_signal=13]", "72:14": "minecraft:wooden_pressure_plate[redstone_signal=14]", "72:15": "minecraft:wooden_pressure_plate[redstone_signal=15]", "73:0": "minecraft:redstone_ore", "74:0": "minecraft:lit_redstone_ore", "75:0": "minecraft:unlit_redstone_torch[torch_facing_direction=unknown]", "75:1": "minecraft:unlit_redstone_torch[torch_facing_direction=west]", "75:2": "minecraft:unlit_redstone_torch[torch_facing_direction=east]", "75:3": "minecraft:unlit_redstone_torch[torch_facing_direction=north]", "75:4": "minecraft:unlit_redstone_torch[torch_facing_direction=south]", "75:5": "minecraft:unlit_redstone_torch[torch_facing_direction=top]", "76:0": "minecraft:redstone_torch[torch_facing_direction=unknown]", "76:1": "minecraft:redstone_torch[torch_facing_direction=west]", "76:2": "minecraft:redstone_torch[torch_facing_direction=east]", "76:3": "minecraft:redstone_torch[torch_facing_direction=north]", "76:4": "minecraft:redstone_torch[torch_facing_direction=south]", "76:5": "minecraft:redstone_torch[torch_facing_direction=top]", "77:0": "minecraft:stone_button[button_pressed_bit=0,facing_direction=0]", "77:1": "minecraft:stone_button[button_pressed_bit=0,facing_direction=1]", "77:2": "minecraft:stone_button[button_pressed_bit=0,facing_direction=2]", "77:3": "minecraft:stone_button[button_pressed_bit=0,facing_direction=3]", "77:4": "minecraft:stone_button[button_pressed_bit=0,facing_direction=4]", "77:5": "minecraft:stone_button[button_pressed_bit=0,facing_direction=5]", "77:6": "minecraft:stone_button[button_pressed_bit=1,facing_direction=0]", "77:7": "minecraft:stone_button[button_pressed_bit=1,facing_direction=1]", "77:8": "minecraft:stone_button[button_pressed_bit=1,facing_direction=2]", "77:9": "minecraft:stone_button[button_pressed_bit=1,facing_direction=3]", "77:10": "minecraft:stone_button[button_pressed_bit=1,facing_direction=4]", "77:11": "minecraft:stone_button[button_pressed_bit=1,facing_direction=5]", "78:0": "minecraft:snow_layer[covered_bit=0,height=0]", "78:1": "minecraft:snow_layer[covered_bit=0,height=1]", "78:2": "minecraft:snow_layer[covered_bit=0,height=2]", "78:3": "minecraft:snow_layer[covered_bit=0,height=3]", "78:4": "minecraft:snow_layer[covered_bit=0,height=4]", "78:5": "minecraft:snow_layer[covered_bit=0,height=5]", "78:6": "minecraft:snow_layer[covered_bit=0,height=6]", "78:7": "minecraft:snow_layer[covered_bit=0,height=7]", "78:8": "minecraft:snow_layer[covered_bit=1,height=0]", "78:9": "minecraft:snow_layer[covered_bit=1,height=1]", "78:10": "minecraft:snow_layer[covered_bit=1,height=2]", "78:11": "minecraft:snow_layer[covered_bit=1,height=3]", "78:12": "minecraft:snow_layer[covered_bit=1,height=4]", "78:13": "minecraft:snow_layer[covered_bit=1,height=5]", "78:14": "minecraft:snow_layer[covered_bit=1,height=6]", "78:15": "minecraft:snow_layer[covered_bit=1,height=7]", "79:0": "minecraft:ice", "80:0": "minecraft:snow", "81:0": "minecraft:cactus[age=0]", "81:1": "minecraft:cactus[age=1]", "81:2": "minecraft:cactus[age=2]", "81:3": "minecraft:cactus[age=3]", "81:4": "minecraft:cactus[age=4]", "81:5": "minecraft:cactus[age=5]", "81:6": "minecraft:cactus[age=6]", "81:7": "minecraft:cactus[age=7]", "81:8": "minecraft:cactus[age=8]", "81:9": "minecraft:cactus[age=9]", "81:10": "minecraft:cactus[age=10]", "81:11": "minecraft:cactus[age=11]", "81:12": "minecraft:cactus[age=12]", "81:13": "minecraft:cactus[age=13]", "81:14": "minecraft:cactus[age=14]", "81:15": "minecraft:cactus[age=15]", "82:0": "minecraft:clay", "83:0": "minecraft:reeds[age=0]", "83:1": "minecraft:reeds[age=1]", "83:2": "minecraft:reeds[age=2]", "83:3": "minecraft:reeds[age=3]", "83:4": "minecraft:reeds[age=4]", "83:5": "minecraft:reeds[age=5]", "83:6": "minecraft:reeds[age=6]", "83:7": "minecraft:reeds[age=7]", "83:8": "minecraft:reeds[age=8]", "83:9": "minecraft:reeds[age=9]", "83:10": "minecraft:reeds[age=10]", "83:11": "minecraft:reeds[age=11]", "83:12": "minecraft:reeds[age=12]", "83:13": "minecraft:reeds[age=13]", "83:14": "minecraft:reeds[age=14]", "83:15": "minecraft:reeds[age=15]", "84:0": "minecraft:jukebox", "85:0": "minecraft:fence[wood_type=oak]", "85:1": "minecraft:fence[wood_type=spruce]", "85:2": "minecraft:fence[wood_type=birch]", "85:3": "minecraft:fence[wood_type=jungle]", "85:4": "minecraft:fence[wood_type=acacia]", "85:5": "minecraft:fence[wood_type=dark_oak]", "86:0": "minecraft:pumpkin[direction=0]", "86:1": "minecraft:pumpkin[direction=1]", "86:2": "minecraft:pumpkin[direction=2]", "86:3": "minecraft:pumpkin[direction=3]", "87:0": "minecraft:netherrack", "88:0": "minecraft:soul_sand", "89:0": "minecraft:glowstone", "90:0": "minecraft:portal[portal_axis=unknown]", "90:1": "minecraft:portal[portal_axis=x]", "90:2": "minecraft:portal[portal_axis=z]", "91:0": "minecraft:lit_pumpkin[direction=0]", "91:1": "minecraft:lit_pumpkin[direction=1]", "91:2": "minecraft:lit_pumpkin[direction=2]", "91:3": "minecraft:lit_pumpkin[direction=3]", "92:0": "minecraft:cake[bite_counter=0]", "92:1": "minecraft:cake[bite_counter=1]", "92:2": "minecraft:cake[bite_counter=2]", "92:3": "minecraft:cake[bite_counter=3]", "92:4": "minecraft:cake[bite_counter=4]", "92:5": "minecraft:cake[bite_counter=5]", "92:6": "minecraft:cake[bite_counter=6]", "93:0": "minecraft:unpowered_repeater[direction=0,repeater_delay=0]", "93:1": "minecraft:unpowered_repeater[direction=1,repeater_delay=0]", "93:2": "minecraft:unpowered_repeater[direction=2,repeater_delay=0]", "93:3": "minecraft:unpowered_repeater[direction=3,repeater_delay=0]", "93:4": "minecraft:unpowered_repeater[direction=0,repeater_delay=1]", "93:5": "minecraft:unpowered_repeater[direction=1,repeater_delay=1]", "93:6": "minecraft:unpowered_repeater[direction=2,repeater_delay=1]", "93:7": "minecraft:unpowered_repeater[direction=3,repeater_delay=1]", "93:8": "minecraft:unpowered_repeater[direction=0,repeater_delay=2]", "93:9": "minecraft:unpowered_repeater[direction=1,repeater_delay=2]", "93:10": "minecraft:unpowered_repeater[direction=2,repeater_delay=2]", "93:11": "minecraft:unpowered_repeater[direction=3,repeater_delay=2]", "93:12": "minecraft:unpowered_repeater[direction=0,repeater_delay=3]", "93:13": "minecraft:unpowered_repeater[direction=1,repeater_delay=3]", "93:14": "minecraft:unpowered_repeater[direction=2,repeater_delay=3]", "93:15": "minecraft:unpowered_repeater[direction=3,repeater_delay=3]", "94:0": "minecraft:powered_repeater[direction=0,repeater_delay=0]", "94:1": "minecraft:powered_repeater[direction=1,repeater_delay=0]", "94:2": "minecraft:powered_repeater[direction=2,repeater_delay=0]", "94:3": "minecraft:powered_repeater[direction=3,repeater_delay=0]", "94:4": "minecraft:powered_repeater[direction=0,repeater_delay=1]", "94:5": "minecraft:powered_repeater[direction=1,repeater_delay=1]", "94:6": "minecraft:powered_repeater[direction=2,repeater_delay=1]", "94:7": "minecraft:powered_repeater[direction=3,repeater_delay=1]", "94:8": "minecraft:powered_repeater[direction=0,repeater_delay=2]", "94:9": "minecraft:powered_repeater[direction=1,repeater_delay=2]", "94:10": "minecraft:powered_repeater[direction=2,repeater_delay=2]", "94:11": "minecraft:powered_repeater[direction=3,repeater_delay=2]", "94:12": "minecraft:powered_repeater[direction=0,repeater_delay=3]", "94:13": "minecraft:powered_repeater[direction=1,repeater_delay=3]", "94:14": "minecraft:powered_repeater[direction=2,repeater_delay=3]", "94:15": "minecraft:powered_repeater[direction=3,repeater_delay=3]", "95:0": "minecraft:invisibleBedrock", "96:0": "minecraft:trapdoor[direction=0,open_bit=0,upside_down_bit=0]", "96:1": "minecraft:trapdoor[direction=1,open_bit=0,upside_down_bit=0]", "96:2": "minecraft:trapdoor[direction=2,open_bit=0,upside_down_bit=0]", "96:3": "minecraft:trapdoor[direction=3,open_bit=0,upside_down_bit=0]", "96:4": "minecraft:trapdoor[direction=0,open_bit=0,upside_down_bit=1]", "96:5": "minecraft:trapdoor[direction=1,open_bit=0,upside_down_bit=1]", "96:6": "minecraft:trapdoor[direction=2,open_bit=0,upside_down_bit=1]", "96:7": "minecraft:trapdoor[direction=3,open_bit=0,upside_down_bit=1]", "96:8": "minecraft:trapdoor[direction=0,open_bit=1,upside_down_bit=0]", "96:9": "minecraft:trapdoor[direction=1,open_bit=1,upside_down_bit=0]", "96:10": "minecraft:trapdoor[direction=2,open_bit=1,upside_down_bit=0]", "96:11": "minecraft:trapdoor[direction=3,open_bit=1,upside_down_bit=0]", "96:12": "minecraft:trapdoor[direction=0,open_bit=1,upside_down_bit=1]", "96:13": "minecraft:trapdoor[direction=1,open_bit=1,upside_down_bit=1]", "96:14": "minecraft:trapdoor[direction=2,open_bit=1,upside_down_bit=1]", "96:15": "minecraft:trapdoor[direction=3,open_bit=1,upside_down_bit=1]", "97:0": "minecraft:monster_egg[monster_egg_stone_type=stone]", "97:1": "minecraft:monster_egg[monster_egg_stone_type=cobblestone]", "97:2": "minecraft:monster_egg[monster_egg_stone_type=stone_brick]", "97:3": "minecraft:monster_egg[monster_egg_stone_type=mossy_stone_brick]", "97:4": "minecraft:monster_egg[monster_egg_stone_type=cracked_stone_brick]", "97:5": "minecraft:monster_egg[monster_egg_stone_type=chiseled_stone_brick]", "98:0": "minecraft:stonebrick[stone_brick_type=default]", "98:1": "minecraft:stonebrick[stone_brick_type=mossy]", "98:2": "minecraft:stonebrick[stone_brick_type=cracked]", "98:3": "minecraft:stonebrick[stone_brick_type=chiseled]", "98:4": "minecraft:stonebrick[stone_brick_type=smooth]", "99:0": "minecraft:brown_mushroom_block[huge_mushroom_bits=0]", "99:1": "minecraft:brown_mushroom_block[huge_mushroom_bits=1]", "99:2": "minecraft:brown_mushroom_block[huge_mushroom_bits=2]", "99:3": "minecraft:brown_mushroom_block[huge_mushroom_bits=3]", "99:4": "minecraft:brown_mushroom_block[huge_mushroom_bits=4]", "99:5": "minecraft:brown_mushroom_block[huge_mushroom_bits=5]", "99:6": "minecraft:brown_mushroom_block[huge_mushroom_bits=6]", "99:7": "minecraft:brown_mushroom_block[huge_mushroom_bits=7]", "99:8": "minecraft:brown_mushroom_block[huge_mushroom_bits=8]", "99:9": "minecraft:brown_mushroom_block[huge_mushroom_bits=9]", "99:10": "minecraft:brown_mushroom_block[huge_mushroom_bits=10]", "99:11": "minecraft:brown_mushroom_block[huge_mushroom_bits=11]", "99:12": "minecraft:brown_mushroom_block[huge_mushroom_bits=12]", "99:13": "minecraft:brown_mushroom_block[huge_mushroom_bits=13]", "99:14": "minecraft:brown_mushroom_block[huge_mushroom_bits=14]", "99:15": "minecraft:brown_mushroom_block[huge_mushroom_bits=15]", "100:0": "minecraft:red_mushroom_block[huge_mushroom_bits=0]", "100:1": "minecraft:red_mushroom_block[huge_mushroom_bits=1]", "100:2": "minecraft:red_mushroom_block[huge_mushroom_bits=2]", "100:3": "minecraft:red_mushroom_block[huge_mushroom_bits=3]", "100:4": "minecraft:red_mushroom_block[huge_mushroom_bits=4]", "100:5": "minecraft:red_mushroom_block[huge_mushroom_bits=5]", "100:6": "minecraft:red_mushroom_block[huge_mushroom_bits=6]", "100:7": "minecraft:red_mushroom_block[huge_mushroom_bits=7]", "100:8": "minecraft:red_mushroom_block[huge_mushroom_bits=8]", "100:9": "minecraft:red_mushroom_block[huge_mushroom_bits=9]", "100:10": "minecraft:red_mushroom_block[huge_mushroom_bits=10]", "100:11": "minecraft:red_mushroom_block[huge_mushroom_bits=11]", "100:12": "minecraft:red_mushroom_block[huge_mushroom_bits=12]", "100:13": "minecraft:red_mushroom_block[huge_mushroom_bits=13]", "100:14": "minecraft:red_mushroom_block[huge_mushroom_bits=14]", "100:15": "minecraft:red_mushroom_block[huge_mushroom_bits=15]", "101:0": "minecraft:iron_bars", "102:0": "minecraft:glass_pane", "103:0": "minecraft:melon_block", "104:0": "minecraft:pumpkin_stem[facing_direction=0,growth=0]", "104:1": "minecraft:pumpkin_stem[facing_direction=0,growth=1]", "104:2": "minecraft:pumpkin_stem[facing_direction=0,growth=2]", "104:3": "minecraft:pumpkin_stem[facing_direction=0,growth=3]", "104:4": "minecraft:pumpkin_stem[facing_direction=0,growth=4]", "104:5": "minecraft:pumpkin_stem[facing_direction=0,growth=5]", "104:6": "minecraft:pumpkin_stem[facing_direction=0,growth=6]", "104:7": "minecraft:pumpkin_stem[facing_direction=0,growth=7]", "104:8": "minecraft:pumpkin_stem[facing_direction=1,growth=0]", "104:9": "minecraft:pumpkin_stem[facing_direction=1,growth=1]", "104:10": "minecraft:pumpkin_stem[facing_direction=1,growth=2]", "104:11": "minecraft:pumpkin_stem[facing_direction=1,growth=3]", "104:12": "minecraft:pumpkin_stem[facing_direction=1,growth=4]", "104:13": "minecraft:pumpkin_stem[facing_direction=1,growth=5]", "104:14": "minecraft:pumpkin_stem[facing_direction=1,growth=6]", "104:15": "minecraft:pumpkin_stem[facing_direction=1,growth=7]", "105:0": "minecraft:melon_stem[facing_direction=0,growth=0]", "105:1": "minecraft:melon_stem[facing_direction=0,growth=1]", "105:2": "minecraft:melon_stem[facing_direction=0,growth=2]", "105:3": "minecraft:melon_stem[facing_direction=0,growth=3]", "105:4": "minecraft:melon_stem[facing_direction=0,growth=4]", "105:5": "minecraft:melon_stem[facing_direction=0,growth=5]", "105:6": "minecraft:melon_stem[facing_direction=0,growth=6]", "105:7": "minecraft:melon_stem[facing_direction=0,growth=7]", "105:8": "minecraft:melon_stem[facing_direction=1,growth=0]", "105:9": "minecraft:melon_stem[facing_direction=1,growth=1]", "105:10": "minecraft:melon_stem[facing_direction=1,growth=2]", "105:11": "minecraft:melon_stem[facing_direction=1,growth=3]", "105:12": "minecraft:melon_stem[facing_direction=1,growth=4]", "105:13": "minecraft:melon_stem[facing_direction=1,growth=5]", "105:14": "minecraft:melon_stem[facing_direction=1,growth=6]", "105:15": "minecraft:melon_stem[facing_direction=1,growth=7]", "106:0": "minecraft:vine[vine_direction_bits=0]", "106:1": "minecraft:vine[vine_direction_bits=1]", "106:2": "minecraft:vine[vine_direction_bits=2]", "106:3": "minecraft:vine[vine_direction_bits=3]", "106:4": "minecraft:vine[vine_direction_bits=4]", "106:5": "minecraft:vine[vine_direction_bits=5]", "106:6": "minecraft:vine[vine_direction_bits=6]", "106:7": "minecraft:vine[vine_direction_bits=7]", "106:8": "minecraft:vine[vine_direction_bits=8]", "106:9": "minecraft:vine[vine_direction_bits=9]", "106:10": "minecraft:vine[vine_direction_bits=10]", "106:11": "minecraft:vine[vine_direction_bits=11]", "106:12": "minecraft:vine[vine_direction_bits=12]", "106:13": "minecraft:vine[vine_direction_bits=13]", "106:14": "minecraft:vine[vine_direction_bits=14]", "106:15": "minecraft:vine[vine_direction_bits=15]", "107:0": "minecraft:fence_gate[direction=0,in_wall_bit=0,open_bit=0]", "107:1": "minecraft:fence_gate[direction=1,in_wall_bit=0,open_bit=0]", "107:2": "minecraft:fence_gate[direction=2,in_wall_bit=0,open_bit=0]", "107:3": "minecraft:fence_gate[direction=3,in_wall_bit=0,open_bit=0]", "107:4": "minecraft:fence_gate[direction=0,in_wall_bit=0,open_bit=1]", "107:5": "minecraft:fence_gate[direction=1,in_wall_bit=0,open_bit=1]", "107:6": "minecraft:fence_gate[direction=2,in_wall_bit=0,open_bit=1]", "107:7": "minecraft:fence_gate[direction=3,in_wall_bit=0,open_bit=1]", "107:8": "minecraft:fence_gate[direction=0,in_wall_bit=1,open_bit=0]", "107:9": "minecraft:fence_gate[direction=1,in_wall_bit=1,open_bit=0]", "107:10": "minecraft:fence_gate[direction=2,in_wall_bit=1,open_bit=0]", "107:11": "minecraft:fence_gate[direction=3,in_wall_bit=1,open_bit=0]", "107:12": "minecraft:fence_gate[direction=0,in_wall_bit=1,open_bit=1]", "107:13": "minecraft:fence_gate[direction=1,in_wall_bit=1,open_bit=1]", "107:14": "minecraft:fence_gate[direction=2,in_wall_bit=1,open_bit=1]", "107:15": "minecraft:fence_gate[direction=3,in_wall_bit=1,open_bit=1]", "108:0": "minecraft:brick_stairs[upside_down_bit=0,weirdo_direction=0]", "108:1": "minecraft:brick_stairs[upside_down_bit=0,weirdo_direction=1]", "108:2": "minecraft:brick_stairs[upside_down_bit=0,weirdo_direction=2]", "108:3": "minecraft:brick_stairs[upside_down_bit=0,weirdo_direction=3]", "108:4": "minecraft:brick_stairs[upside_down_bit=1,weirdo_direction=0]", "108:5": "minecraft:brick_stairs[upside_down_bit=1,weirdo_direction=1]", "108:6": "minecraft:brick_stairs[upside_down_bit=1,weirdo_direction=2]", "108:7": "minecraft:brick_stairs[upside_down_bit=1,weirdo_direction=3]", "109:0": "minecraft:stone_brick_stairs[upside_down_bit=0,weirdo_direction=0]", "109:1": "minecraft:stone_brick_stairs[upside_down_bit=0,weirdo_direction=1]", "109:2": "minecraft:stone_brick_stairs[upside_down_bit=0,weirdo_direction=2]", "109:3": "minecraft:stone_brick_stairs[upside_down_bit=0,weirdo_direction=3]", "109:4": "minecraft:stone_brick_stairs[upside_down_bit=1,weirdo_direction=0]", "109:5": "minecraft:stone_brick_stairs[upside_down_bit=1,weirdo_direction=1]", "109:6": "minecraft:stone_brick_stairs[upside_down_bit=1,weirdo_direction=2]", "109:7": "minecraft:stone_brick_stairs[upside_down_bit=1,weirdo_direction=3]", "110:0": "minecraft:mycelium", "111:0": "minecraft:waterlily", "112:0": "minecraft:nether_brick", "113:0": "minecraft:nether_brick_fence", "114:0": "minecraft:nether_brick_stairs[upside_down_bit=0,weirdo_direction=0]", "114:1": "minecraft:nether_brick_stairs[upside_down_bit=0,weirdo_direction=1]", "114:2": "minecraft:nether_brick_stairs[upside_down_bit=0,weirdo_direction=2]", "114:3": "minecraft:nether_brick_stairs[upside_down_bit=0,weirdo_direction=3]", "114:4": "minecraft:nether_brick_stairs[upside_down_bit=1,weirdo_direction=0]", "114:5": "minecraft:nether_brick_stairs[upside_down_bit=1,weirdo_direction=1]", "114:6": "minecraft:nether_brick_stairs[upside_down_bit=1,weirdo_direction=2]", "114:7": "minecraft:nether_brick_stairs[upside_down_bit=1,weirdo_direction=3]", "115:0": "minecraft:nether_wart[age=0]", "115:1": "minecraft:nether_wart[age=1]", "115:2": "minecraft:nether_wart[age=2]", "115:3": "minecraft:nether_wart[age=3]", "116:0": "minecraft:enchanting_table", "117:0": "minecraft:brewing_stand[brewing_stand_slot_a_bit=0,brewing_stand_slot_b_bit=0,brewing_stand_slot_c_bit=0]", "117:1": "minecraft:brewing_stand[brewing_stand_slot_a_bit=1,brewing_stand_slot_b_bit=0,brewing_stand_slot_c_bit=0]", "117:2": "minecraft:brewing_stand[brewing_stand_slot_a_bit=0,brewing_stand_slot_b_bit=1,brewing_stand_slot_c_bit=0]", "117:3": "minecraft:brewing_stand[brewing_stand_slot_a_bit=1,brewing_stand_slot_b_bit=1,brewing_stand_slot_c_bit=0]", "117:4": "minecraft:brewing_stand[brewing_stand_slot_a_bit=0,brewing_stand_slot_b_bit=0,brewing_stand_slot_c_bit=1]", "117:5": "minecraft:brewing_stand[brewing_stand_slot_a_bit=1,brewing_stand_slot_b_bit=0,brewing_stand_slot_c_bit=1]", "117:6": "minecraft:brewing_stand[brewing_stand_slot_a_bit=0,brewing_stand_slot_b_bit=1,brewing_stand_slot_c_bit=1]", "117:7": "minecraft:brewing_stand[brewing_stand_slot_a_bit=1,brewing_stand_slot_b_bit=1,brewing_stand_slot_c_bit=1]", "118:0": "minecraft:cauldron[cauldron_liquid=water,fill_level=0]", "118:1": "minecraft:cauldron[cauldron_liquid=water,fill_level=1]", "118:2": "minecraft:cauldron[cauldron_liquid=water,fill_level=2]", "118:3": "minecraft:cauldron[cauldron_liquid=water,fill_level=3]", "118:4": "minecraft:cauldron[cauldron_liquid=water,fill_level=4]", "118:5": "minecraft:cauldron[cauldron_liquid=water,fill_level=5]", "118:6": "minecraft:cauldron[cauldron_liquid=water,fill_level=6]", "118:7": "minecraft:cauldron[cauldron_liquid=lava,fill_level=0]", "118:8": "minecraft:cauldron[cauldron_liquid=lava,fill_level=1]", "118:9": "minecraft:cauldron[cauldron_liquid=lava,fill_level=2]", "118:10": "minecraft:cauldron[cauldron_liquid=lava,fill_level=3]", "118:11": "minecraft:cauldron[cauldron_liquid=lava,fill_level=4]", "118:12": "minecraft:cauldron[cauldron_liquid=lava,fill_level=5]", "118:13": "minecraft:cauldron[cauldron_liquid=lava,fill_level=6]", "118:14": "minecraft:cauldron[cauldron_liquid=powder_snow,fill_level=0]", "118:15": "minecraft:cauldron[cauldron_liquid=powder_snow,fill_level=1]", "119:0": "minecraft:end_portal", "120:0": "minecraft:end_portal_frame[direction=0,end_portal_eye_bit=0]", "120:1": "minecraft:end_portal_frame[direction=1,end_portal_eye_bit=0]", "120:2": "minecraft:end_portal_frame[direction=2,end_portal_eye_bit=0]", "120:3": "minecraft:end_portal_frame[direction=3,end_portal_eye_bit=0]", "120:4": "minecraft:end_portal_frame[direction=0,end_portal_eye_bit=1]", "120:5": "minecraft:end_portal_frame[direction=1,end_portal_eye_bit=1]", "120:6": "minecraft:end_portal_frame[direction=2,end_portal_eye_bit=1]", "120:7": "minecraft:end_portal_frame[direction=3,end_portal_eye_bit=1]", "121:0": "minecraft:end_stone", "122:0": "minecraft:dragon_egg", "123:0": "minecraft:redstone_lamp", "124:0": "minecraft:lit_redstone_lamp", "125:0": "minecraft:dropper[facing_direction=0,triggered_bit=0]", "125:1": "minecraft:dropper[facing_direction=1,triggered_bit=0]", "125:2": "minecraft:dropper[facing_direction=2,triggered_bit=0]", "125:3": "minecraft:dropper[facing_direction=3,triggered_bit=0]", "125:4": "minecraft:dropper[facing_direction=4,triggered_bit=0]", "125:5": "minecraft:dropper[facing_direction=5,triggered_bit=0]", "125:6": "minecraft:dropper[facing_direction=0,triggered_bit=1]", "125:7": "minecraft:dropper[facing_direction=1,triggered_bit=1]", "125:8": "minecraft:dropper[facing_direction=2,triggered_bit=1]", "125:9": "minecraft:dropper[facing_direction=3,triggered_bit=1]", "125:10": "minecraft:dropper[facing_direction=4,triggered_bit=1]", "125:11": "minecraft:dropper[facing_direction=5,triggered_bit=1]", "126:0": "minecraft:activator_rail[rail_data_bit=0,rail_direction=0]", "126:1": "minecraft:activator_rail[rail_data_bit=0,rail_direction=1]", "126:2": "minecraft:activator_rail[rail_data_bit=0,rail_direction=2]", "126:3": "minecraft:activator_rail[rail_data_bit=0,rail_direction=3]", "126:4": "minecraft:activator_rail[rail_data_bit=0,rail_direction=4]", "126:5": "minecraft:activator_rail[rail_data_bit=0,rail_direction=5]", "126:6": "minecraft:activator_rail[rail_data_bit=1,rail_direction=0]", "126:7": "minecraft:activator_rail[rail_data_bit=1,rail_direction=1]", "126:8": "minecraft:activator_rail[rail_data_bit=1,rail_direction=2]", "126:9": "minecraft:activator_rail[rail_data_bit=1,rail_direction=3]", "126:10": "minecraft:activator_rail[rail_data_bit=1,rail_direction=4]", "126:11": "minecraft:activator_rail[rail_data_bit=1,rail_direction=5]", "127:0": "minecraft:cocoa[age=0,direction=0]", "127:1": "minecraft:cocoa[age=0,direction=1]", "127:2": "minecraft:cocoa[age=0,direction=2]", "127:3": "minecraft:cocoa[age=0,direction=3]", "127:4": "minecraft:cocoa[age=1,direction=0]", "127:5": "minecraft:cocoa[age=1,direction=1]", "127:6": "minecraft:cocoa[age=1,direction=2]", "127:7": "minecraft:cocoa[age=1,direction=3]", "127:8": "minecraft:cocoa[age=2,direction=0]", "127:9": "minecraft:cocoa[age=2,direction=1]", "127:10": "minecraft:cocoa[age=2,direction=2]", "127:11": "minecraft:cocoa[age=2,direction=3]", "128:0": "minecraft:sandstone_stairs[upside_down_bit=0,weirdo_direction=0]", "128:1": "minecraft:sandstone_stairs[upside_down_bit=0,weirdo_direction=1]", "128:2": "minecraft:sandstone_stairs[upside_down_bit=0,weirdo_direction=2]", "128:3": "minecraft:sandstone_stairs[upside_down_bit=0,weirdo_direction=3]", "128:4": "minecraft:sandstone_stairs[upside_down_bit=1,weirdo_direction=0]", "128:5": "minecraft:sandstone_stairs[upside_down_bit=1,weirdo_direction=1]", "128:6": "minecraft:sandstone_stairs[upside_down_bit=1,weirdo_direction=2]", "128:7": "minecraft:sandstone_stairs[upside_down_bit=1,weirdo_direction=3]", "129:0": "minecraft:emerald_ore", "130:0": "minecraft:ender_chest[facing_direction=0]", "130:1": "minecraft:ender_chest[facing_direction=1]", "130:2": "minecraft:ender_chest[facing_direction=2]", "130:3": "minecraft:ender_chest[facing_direction=3]", "130:4": "minecraft:ender_chest[facing_direction=4]", "130:5": "minecraft:ender_chest[facing_direction=5]", "131:0": "minecraft:tripwire_hook[attached_bit=0,direction=0,powered_bit=0]", "131:1": "minecraft:tripwire_hook[attached_bit=0,direction=1,powered_bit=0]", "131:2": "minecraft:tripwire_hook[attached_bit=0,direction=2,powered_bit=0]", "131:3": "minecraft:tripwire_hook[attached_bit=0,direction=3,powered_bit=0]", "131:4": "minecraft:tripwire_hook[attached_bit=1,direction=0,powered_bit=0]", "131:5": "minecraft:tripwire_hook[attached_bit=1,direction=1,powered_bit=0]", "131:6": "minecraft:tripwire_hook[attached_bit=1,direction=2,powered_bit=0]", "131:7": "minecraft:tripwire_hook[attached_bit=1,direction=3,powered_bit=0]", "131:8": "minecraft:tripwire_hook[attached_bit=0,direction=0,powered_bit=1]", "131:9": "minecraft:tripwire_hook[attached_bit=0,direction=1,powered_bit=1]", "131:10": "minecraft:tripwire_hook[attached_bit=0,direction=2,powered_bit=1]", "131:11": "minecraft:tripwire_hook[attached_bit=0,direction=3,powered_bit=1]", "131:12": "minecraft:tripwire_hook[attached_bit=1,direction=0,powered_bit=1]", "131:13": "minecraft:tripwire_hook[attached_bit=1,direction=1,powered_bit=1]", "131:14": "minecraft:tripwire_hook[attached_bit=1,direction=2,powered_bit=1]", "131:15": "minecraft:tripwire_hook[attached_bit=1,direction=3,powered_bit=1]", "132:0": "minecraft:tripWire[attached_bit=0,disarmed_bit=0,powered_bit=0,suspended_bit=0]", "132:1": "minecraft:tripWire[attached_bit=0,disarmed_bit=0,powered_bit=1,suspended_bit=0]", "132:2": "minecraft:tripWire[attached_bit=0,disarmed_bit=0,powered_bit=0,suspended_bit=1]", "132:3": "minecraft:tripWire[attached_bit=0,disarmed_bit=0,powered_bit=1,suspended_bit=1]", "132:4": "minecraft:tripWire[attached_bit=1,disarmed_bit=0,powered_bit=0,suspended_bit=0]", "132:5": "minecraft:tripWire[attached_bit=1,disarmed_bit=0,powered_bit=1,suspended_bit=0]", "132:6": "minecraft:tripWire[attached_bit=1,disarmed_bit=0,powered_bit=0,suspended_bit=1]", "132:7": "minecraft:tripWire[attached_bit=1,disarmed_bit=0,powered_bit=1,suspended_bit=1]", "132:8": "minecraft:tripWire[attached_bit=0,disarmed_bit=1,powered_bit=0,suspended_bit=0]", "132:9": "minecraft:tripWire[attached_bit=0,disarmed_bit=1,powered_bit=1,suspended_bit=0]", "132:10": "minecraft:tripWire[attached_bit=0,disarmed_bit=1,powered_bit=0,suspended_bit=1]", "132:11": "minecraft:tripWire[attached_bit=0,disarmed_bit=1,powered_bit=1,suspended_bit=1]", "132:12": "minecraft:tripWire[attached_bit=1,disarmed_bit=1,powered_bit=0,suspended_bit=0]", "132:13": "minecraft:tripWire[attached_bit=1,disarmed_bit=1,powered_bit=1,suspended_bit=0]", "132:14": "minecraft:tripWire[attached_bit=1,disarmed_bit=1,powered_bit=0,suspended_bit=1]", "132:15": "minecraft:tripWire[attached_bit=1,disarmed_bit=1,powered_bit=1,suspended_bit=1]", "133:0": "minecraft:emerald_block", "134:0": "minecraft:spruce_stairs[upside_down_bit=0,weirdo_direction=0]", "134:1": "minecraft:spruce_stairs[upside_down_bit=0,weirdo_direction=1]", "134:2": "minecraft:spruce_stairs[upside_down_bit=0,weirdo_direction=2]", "134:3": "minecraft:spruce_stairs[upside_down_bit=0,weirdo_direction=3]", "134:4": "minecraft:spruce_stairs[upside_down_bit=1,weirdo_direction=0]", "134:5": "minecraft:spruce_stairs[upside_down_bit=1,weirdo_direction=1]", "134:6": "minecraft:spruce_stairs[upside_down_bit=1,weirdo_direction=2]", "134:7": "minecraft:spruce_stairs[upside_down_bit=1,weirdo_direction=3]", "135:0": "minecraft:birch_stairs[upside_down_bit=0,weirdo_direction=0]", "135:1": "minecraft:birch_stairs[upside_down_bit=0,weirdo_direction=1]", "135:2": "minecraft:birch_stairs[upside_down_bit=0,weirdo_direction=2]", "135:3": "minecraft:birch_stairs[upside_down_bit=0,weirdo_direction=3]", "135:4": "minecraft:birch_stairs[upside_down_bit=1,weirdo_direction=0]", "135:5": "minecraft:birch_stairs[upside_down_bit=1,weirdo_direction=1]", "135:6": "minecraft:birch_stairs[upside_down_bit=1,weirdo_direction=2]", "135:7": "minecraft:birch_stairs[upside_down_bit=1,weirdo_direction=3]", "136:0": "minecraft:jungle_stairs[upside_down_bit=0,weirdo_direction=0]", "136:1": "minecraft:jungle_stairs[upside_down_bit=0,weirdo_direction=1]", "136:2": "minecraft:jungle_stairs[upside_down_bit=0,weirdo_direction=2]", "136:3": "minecraft:jungle_stairs[upside_down_bit=0,weirdo_direction=3]", "136:4": "minecraft:jungle_stairs[upside_down_bit=1,weirdo_direction=0]", "136:5": "minecraft:jungle_stairs[upside_down_bit=1,weirdo_direction=1]", "136:6": "minecraft:jungle_stairs[upside_down_bit=1,weirdo_direction=2]", "136:7": "minecraft:jungle_stairs[upside_down_bit=1,weirdo_direction=3]", "137:0": "minecraft:command_block[conditional_bit=0,facing_direction=0]", "137:1": "minecraft:command_block[conditional_bit=0,facing_direction=1]", "137:2": "minecraft:command_block[conditional_bit=0,facing_direction=2]", "137:3": "minecraft:command_block[conditional_bit=0,facing_direction=3]", "137:4": "minecraft:command_block[conditional_bit=0,facing_direction=4]", "137:5": "minecraft:command_block[conditional_bit=0,facing_direction=5]", "137:6": "minecraft:command_block[conditional_bit=1,facing_direction=0]", "137:7": "minecraft:command_block[conditional_bit=1,facing_direction=1]", "137:8": "minecraft:command_block[conditional_bit=1,facing_direction=2]", "137:9": "minecraft:command_block[conditional_bit=1,facing_direction=3]", "137:10": "minecraft:command_block[conditional_bit=1,facing_direction=4]", "137:11": "minecraft:command_block[conditional_bit=1,facing_direction=5]", "138:0": "minecraft:beacon", "139:0": "minecraft:cobblestone_wall[wall_block_type=cobblestone,wall_connection_type_east=none,wall_connection_type_north=none,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=0]", "139:1": "minecraft:cobblestone_wall[wall_block_type=mossy_cobblestone,wall_connection_type_east=none,wall_connection_type_north=none,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=0]", "139:2": "minecraft:cobblestone_wall[wall_block_type=granite,wall_connection_type_east=none,wall_connection_type_north=none,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=0]", "139:3": "minecraft:cobblestone_wall[wall_block_type=diorite,wall_connection_type_east=none,wall_connection_type_north=none,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=0]", "139:4": "minecraft:cobblestone_wall[wall_block_type=andesite,wall_connection_type_east=none,wall_connection_type_north=none,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=0]", "139:5": "minecraft:cobblestone_wall[wall_block_type=sandstone,wall_connection_type_east=none,wall_connection_type_north=none,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=0]", "139:6": "minecraft:cobblestone_wall[wall_block_type=brick,wall_connection_type_east=none,wall_connection_type_north=none,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=0]", "139:7": "minecraft:cobblestone_wall[wall_block_type=stone_brick,wall_connection_type_east=none,wall_connection_type_north=none,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=0]", "139:8": "minecraft:cobblestone_wall[wall_block_type=mossy_stone_brick,wall_connection_type_east=none,wall_connection_type_north=none,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=0]", "139:9": "minecraft:cobblestone_wall[wall_block_type=nether_brick,wall_connection_type_east=none,wall_connection_type_north=none,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=0]", "139:10": "minecraft:cobblestone_wall[wall_block_type=end_brick,wall_connection_type_east=none,wall_connection_type_north=none,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=0]", "139:11": "minecraft:cobblestone_wall[wall_block_type=prismarine,wall_connection_type_east=none,wall_connection_type_north=none,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=0]", "139:12": "minecraft:cobblestone_wall[wall_block_type=red_sandstone,wall_connection_type_east=none,wall_connection_type_north=none,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=0]", "139:13": "minecraft:cobblestone_wall[wall_block_type=red_nether_brick,wall_connection_type_east=none,wall_connection_type_north=none,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=0]", "139:14": "minecraft:cobblestone_wall[wall_block_type=cobblestone,wall_connection_type_east=none,wall_connection_type_north=none,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=1]", "139:15": "minecraft:cobblestone_wall[wall_block_type=mossy_cobblestone,wall_connection_type_east=none,wall_connection_type_north=none,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=1]", "140:0": "minecraft:flower_pot[update_bit=0]", "140:1": "minecraft:flower_pot[update_bit=1]", "141:0": "minecraft:carrots[growth=0]", "141:1": "minecraft:carrots[growth=1]", "141:2": "minecraft:carrots[growth=2]", "141:3": "minecraft:carrots[growth=3]", "141:4": "minecraft:carrots[growth=4]", "141:5": "minecraft:carrots[growth=5]", "141:6": "minecraft:carrots[growth=6]", "141:7": "minecraft:carrots[growth=7]", "142:0": "minecraft:potatoes[growth=0]", "142:1": "minecraft:potatoes[growth=1]", "142:2": "minecraft:potatoes[growth=2]", "142:3": "minecraft:potatoes[growth=3]", "142:4": "minecraft:potatoes[growth=4]", "142:5": "minecraft:potatoes[growth=5]", "142:6": "minecraft:potatoes[growth=6]", "142:7": "minecraft:potatoes[growth=7]", "143:0": "minecraft:wooden_button[button_pressed_bit=0,facing_direction=0]", "143:1": "minecraft:wooden_button[button_pressed_bit=0,facing_direction=1]", "143:2": "minecraft:wooden_button[button_pressed_bit=0,facing_direction=2]", "143:3": "minecraft:wooden_button[button_pressed_bit=0,facing_direction=3]", "143:4": "minecraft:wooden_button[button_pressed_bit=0,facing_direction=4]", "143:5": "minecraft:wooden_button[button_pressed_bit=0,facing_direction=5]", "143:6": "minecraft:wooden_button[button_pressed_bit=1,facing_direction=0]", "143:7": "minecraft:wooden_button[button_pressed_bit=1,facing_direction=1]", "143:8": "minecraft:wooden_button[button_pressed_bit=1,facing_direction=2]", "143:9": "minecraft:wooden_button[button_pressed_bit=1,facing_direction=3]", "143:10": "minecraft:wooden_button[button_pressed_bit=1,facing_direction=4]", "143:11": "minecraft:wooden_button[button_pressed_bit=1,facing_direction=5]", "144:0": "minecraft:skull[facing_direction=0]", "144:1": "minecraft:skull[facing_direction=1]", "144:2": "minecraft:skull[facing_direction=2]", "144:3": "minecraft:skull[facing_direction=3]", "144:4": "minecraft:skull[facing_direction=4]", "144:5": "minecraft:skull[facing_direction=5]", "145:0": "minecraft:anvil[damage=undamaged,direction=0]", "145:1": "minecraft:anvil[damage=undamaged,direction=1]", "145:2": "minecraft:anvil[damage=undamaged,direction=2]", "145:3": "minecraft:anvil[damage=undamaged,direction=3]", "145:4": "minecraft:anvil[damage=slightly_damaged,direction=0]", "145:5": "minecraft:anvil[damage=slightly_damaged,direction=1]", "145:6": "minecraft:anvil[damage=slightly_damaged,direction=2]", "145:7": "minecraft:anvil[damage=slightly_damaged,direction=3]", "145:8": "minecraft:anvil[damage=very_damaged,direction=0]", "145:9": "minecraft:anvil[damage=very_damaged,direction=1]", "145:10": "minecraft:anvil[damage=very_damaged,direction=2]", "145:11": "minecraft:anvil[damage=very_damaged,direction=3]", "145:12": "minecraft:anvil[damage=broken,direction=0]", "145:13": "minecraft:anvil[damage=broken,direction=1]", "145:14": "minecraft:anvil[damage=broken,direction=2]", "145:15": "minecraft:anvil[damage=broken,direction=3]", "146:0": "minecraft:trapped_chest[facing_direction=0]", "146:1": "minecraft:trapped_chest[facing_direction=1]", "146:2": "minecraft:trapped_chest[facing_direction=2]", "146:3": "minecraft:trapped_chest[facing_direction=3]", "146:4": "minecraft:trapped_chest[facing_direction=4]", "146:5": "minecraft:trapped_chest[facing_direction=5]", "147:0": "minecraft:light_weighted_pressure_plate[redstone_signal=0]", "147:1": "minecraft:light_weighted_pressure_plate[redstone_signal=1]", "147:2": "minecraft:light_weighted_pressure_plate[redstone_signal=2]", "147:3": "minecraft:light_weighted_pressure_plate[redstone_signal=3]", "147:4": "minecraft:light_weighted_pressure_plate[redstone_signal=4]", "147:5": "minecraft:light_weighted_pressure_plate[redstone_signal=5]", "147:6": "minecraft:light_weighted_pressure_plate[redstone_signal=6]", "147:7": "minecraft:light_weighted_pressure_plate[redstone_signal=7]", "147:8": "minecraft:light_weighted_pressure_plate[redstone_signal=8]", "147:9": "minecraft:light_weighted_pressure_plate[redstone_signal=9]", "147:10": "minecraft:light_weighted_pressure_plate[redstone_signal=10]", "147:11": "minecraft:light_weighted_pressure_plate[redstone_signal=11]", "147:12": "minecraft:light_weighted_pressure_plate[redstone_signal=12]", "147:13": "minecraft:light_weighted_pressure_plate[redstone_signal=13]", "147:14": "minecraft:light_weighted_pressure_plate[redstone_signal=14]", "147:15": "minecraft:light_weighted_pressure_plate[redstone_signal=15]", "148:0": "minecraft:heavy_weighted_pressure_plate[redstone_signal=0]", "148:1": "minecraft:heavy_weighted_pressure_plate[redstone_signal=1]", "148:2": "minecraft:heavy_weighted_pressure_plate[redstone_signal=2]", "148:3": "minecraft:heavy_weighted_pressure_plate[redstone_signal=3]", "148:4": "minecraft:heavy_weighted_pressure_plate[redstone_signal=4]", "148:5": "minecraft:heavy_weighted_pressure_plate[redstone_signal=5]", "148:6": "minecraft:heavy_weighted_pressure_plate[redstone_signal=6]", "148:7": "minecraft:heavy_weighted_pressure_plate[redstone_signal=7]", "148:8": "minecraft:heavy_weighted_pressure_plate[redstone_signal=8]", "148:9": "minecraft:heavy_weighted_pressure_plate[redstone_signal=9]", "148:10": "minecraft:heavy_weighted_pressure_plate[redstone_signal=10]", "148:11": "minecraft:heavy_weighted_pressure_plate[redstone_signal=11]", "148:12": "minecraft:heavy_weighted_pressure_plate[redstone_signal=12]", "148:13": "minecraft:heavy_weighted_pressure_plate[redstone_signal=13]", "148:14": "minecraft:heavy_weighted_pressure_plate[redstone_signal=14]", "148:15": "minecraft:heavy_weighted_pressure_plate[redstone_signal=15]", "149:0": "minecraft:unpowered_comparator[direction=0,output_lit_bit=0,output_subtract_bit=0]", "149:1": "minecraft:unpowered_comparator[direction=1,output_lit_bit=0,output_subtract_bit=0]", "149:2": "minecraft:unpowered_comparator[direction=2,output_lit_bit=0,output_subtract_bit=0]", "149:3": "minecraft:unpowered_comparator[direction=3,output_lit_bit=0,output_subtract_bit=0]", "149:4": "minecraft:unpowered_comparator[direction=0,output_lit_bit=0,output_subtract_bit=1]", "149:5": "minecraft:unpowered_comparator[direction=1,output_lit_bit=0,output_subtract_bit=1]", "149:6": "minecraft:unpowered_comparator[direction=2,output_lit_bit=0,output_subtract_bit=1]", "149:7": "minecraft:unpowered_comparator[direction=3,output_lit_bit=0,output_subtract_bit=1]", "149:8": "minecraft:unpowered_comparator[direction=0,output_lit_bit=1,output_subtract_bit=0]", "149:9": "minecraft:unpowered_comparator[direction=1,output_lit_bit=1,output_subtract_bit=0]", "149:10": "minecraft:unpowered_comparator[direction=2,output_lit_bit=1,output_subtract_bit=0]", "149:11": "minecraft:unpowered_comparator[direction=3,output_lit_bit=1,output_subtract_bit=0]", "149:12": "minecraft:unpowered_comparator[direction=0,output_lit_bit=1,output_subtract_bit=1]", "149:13": "minecraft:unpowered_comparator[direction=1,output_lit_bit=1,output_subtract_bit=1]", "149:14": "minecraft:unpowered_comparator[direction=2,output_lit_bit=1,output_subtract_bit=1]", "149:15": "minecraft:unpowered_comparator[direction=3,output_lit_bit=1,output_subtract_bit=1]", "150:0": "minecraft:powered_comparator[direction=0,output_lit_bit=0,output_subtract_bit=0]", "150:1": "minecraft:powered_comparator[direction=1,output_lit_bit=0,output_subtract_bit=0]", "150:2": "minecraft:powered_comparator[direction=2,output_lit_bit=0,output_subtract_bit=0]", "150:3": "minecraft:powered_comparator[direction=3,output_lit_bit=0,output_subtract_bit=0]", "150:4": "minecraft:powered_comparator[direction=0,output_lit_bit=0,output_subtract_bit=1]", "150:5": "minecraft:powered_comparator[direction=1,output_lit_bit=0,output_subtract_bit=1]", "150:6": "minecraft:powered_comparator[direction=2,output_lit_bit=0,output_subtract_bit=1]", "150:7": "minecraft:powered_comparator[direction=3,output_lit_bit=0,output_subtract_bit=1]", "150:8": "minecraft:powered_comparator[direction=0,output_lit_bit=1,output_subtract_bit=0]", "150:9": "minecraft:powered_comparator[direction=1,output_lit_bit=1,output_subtract_bit=0]", "150:10": "minecraft:powered_comparator[direction=2,output_lit_bit=1,output_subtract_bit=0]", "150:11": "minecraft:powered_comparator[direction=3,output_lit_bit=1,output_subtract_bit=0]", "150:12": "minecraft:powered_comparator[direction=0,output_lit_bit=1,output_subtract_bit=1]", "150:13": "minecraft:powered_comparator[direction=1,output_lit_bit=1,output_subtract_bit=1]", "150:14": "minecraft:powered_comparator[direction=2,output_lit_bit=1,output_subtract_bit=1]", "150:15": "minecraft:powered_comparator[direction=3,output_lit_bit=1,output_subtract_bit=1]", "151:0": "minecraft:daylight_detector[redstone_signal=0]", "151:1": "minecraft:daylight_detector[redstone_signal=1]", "151:2": "minecraft:daylight_detector[redstone_signal=2]", "151:3": "minecraft:daylight_detector[redstone_signal=3]", "151:4": "minecraft:daylight_detector[redstone_signal=4]", "151:5": "minecraft:daylight_detector[redstone_signal=5]", "151:6": "minecraft:daylight_detector[redstone_signal=6]", "151:7": "minecraft:daylight_detector[redstone_signal=7]", "151:8": "minecraft:daylight_detector[redstone_signal=8]", "151:9": "minecraft:daylight_detector[redstone_signal=9]", "151:10": "minecraft:daylight_detector[redstone_signal=10]", "151:11": "minecraft:daylight_detector[redstone_signal=11]", "151:12": "minecraft:daylight_detector[redstone_signal=12]", "151:13": "minecraft:daylight_detector[redstone_signal=13]", "151:14": "minecraft:daylight_detector[redstone_signal=14]", "151:15": "minecraft:daylight_detector[redstone_signal=15]", "152:0": "minecraft:redstone_block", "153:0": "minecraft:quartz_ore", "154:0": "minecraft:hopper[facing_direction=0,toggle_bit=0]", "154:1": "minecraft:hopper[facing_direction=1,toggle_bit=0]", "154:2": "minecraft:hopper[facing_direction=2,toggle_bit=0]", "154:3": "minecraft:hopper[facing_direction=3,toggle_bit=0]", "154:4": "minecraft:hopper[facing_direction=4,toggle_bit=0]", "154:5": "minecraft:hopper[facing_direction=5,toggle_bit=0]", "154:6": "minecraft:hopper[facing_direction=0,toggle_bit=1]", "154:7": "minecraft:hopper[facing_direction=1,toggle_bit=1]", "154:8": "minecraft:hopper[facing_direction=2,toggle_bit=1]", "154:9": "minecraft:hopper[facing_direction=3,toggle_bit=1]", "154:10": "minecraft:hopper[facing_direction=4,toggle_bit=1]", "154:11": "minecraft:hopper[facing_direction=5,toggle_bit=1]", "155:0": "minecraft:quartz_block[chisel_type=default,pillar_axis=y]", "155:1": "minecraft:quartz_block[chisel_type=chiseled,pillar_axis=y]", "155:2": "minecraft:quartz_block[chisel_type=lines,pillar_axis=y]", "155:3": "minecraft:quartz_block[chisel_type=smooth,pillar_axis=y]", "155:4": "minecraft:quartz_block[chisel_type=default,pillar_axis=x]", "155:5": "minecraft:quartz_block[chisel_type=chiseled,pillar_axis=x]", "155:6": "minecraft:quartz_block[chisel_type=lines,pillar_axis=x]", "155:7": "minecraft:quartz_block[chisel_type=smooth,pillar_axis=x]", "155:8": "minecraft:quartz_block[chisel_type=default,pillar_axis=z]", "155:9": "minecraft:quartz_block[chisel_type=chiseled,pillar_axis=z]", "155:10": "minecraft:quartz_block[chisel_type=lines,pillar_axis=z]", "155:11": "minecraft:quartz_block[chisel_type=smooth,pillar_axis=z]", "156:0": "minecraft:quartz_stairs[upside_down_bit=0,weirdo_direction=0]", "156:1": "minecraft:quartz_stairs[upside_down_bit=0,weirdo_direction=1]", "156:2": "minecraft:quartz_stairs[upside_down_bit=0,weirdo_direction=2]", "156:3": "minecraft:quartz_stairs[upside_down_bit=0,weirdo_direction=3]", "156:4": "minecraft:quartz_stairs[upside_down_bit=1,weirdo_direction=0]", "156:5": "minecraft:quartz_stairs[upside_down_bit=1,weirdo_direction=1]", "156:6": "minecraft:quartz_stairs[upside_down_bit=1,weirdo_direction=2]", "156:7": "minecraft:quartz_stairs[upside_down_bit=1,weirdo_direction=3]", "157:0": "minecraft:double_wooden_slab[top_slot_bit=0,wood_type=oak]", "157:1": "minecraft:double_wooden_slab[top_slot_bit=0,wood_type=spruce]", "157:2": "minecraft:double_wooden_slab[top_slot_bit=0,wood_type=birch]", "157:3": "minecraft:double_wooden_slab[top_slot_bit=0,wood_type=jungle]", "157:4": "minecraft:double_wooden_slab[top_slot_bit=0,wood_type=acacia]", "157:5": "minecraft:double_wooden_slab[top_slot_bit=0,wood_type=dark_oak]", "157:6": "minecraft:double_wooden_slab[top_slot_bit=1,wood_type=oak]", "157:7": "minecraft:double_wooden_slab[top_slot_bit=1,wood_type=spruce]", "157:8": "minecraft:double_wooden_slab[top_slot_bit=1,wood_type=birch]", "157:9": "minecraft:double_wooden_slab[top_slot_bit=1,wood_type=jungle]", "157:10": "minecraft:double_wooden_slab[top_slot_bit=1,wood_type=acacia]", "157:11": "minecraft:double_wooden_slab[top_slot_bit=1,wood_type=dark_oak]", "158:0": "minecraft:wooden_slab[top_slot_bit=0,wood_type=oak]", "158:1": "minecraft:wooden_slab[top_slot_bit=0,wood_type=spruce]", "158:2": "minecraft:wooden_slab[top_slot_bit=0,wood_type=birch]", "158:3": "minecraft:wooden_slab[top_slot_bit=0,wood_type=jungle]", "158:4": "minecraft:wooden_slab[top_slot_bit=0,wood_type=acacia]", "158:5": "minecraft:wooden_slab[top_slot_bit=0,wood_type=dark_oak]", "158:6": "minecraft:wooden_slab[top_slot_bit=1,wood_type=oak]", "158:7": "minecraft:wooden_slab[top_slot_bit=1,wood_type=spruce]", "158:8": "minecraft:wooden_slab[top_slot_bit=1,wood_type=birch]", "158:9": "minecraft:wooden_slab[top_slot_bit=1,wood_type=jungle]", "158:10": "minecraft:wooden_slab[top_slot_bit=1,wood_type=acacia]", "158:11": "minecraft:wooden_slab[top_slot_bit=1,wood_type=dark_oak]", "159:0": "minecraft:stained_hardened_clay[color=white]", "159:1": "minecraft:stained_hardened_clay[color=orange]", "159:2": "minecraft:stained_hardened_clay[color=magenta]", "159:3": "minecraft:stained_hardened_clay[color=light_blue]", "159:4": "minecraft:stained_hardened_clay[color=yellow]", "159:5": "minecraft:stained_hardened_clay[color=lime]", "159:6": "minecraft:stained_hardened_clay[color=pink]", "159:7": "minecraft:stained_hardened_clay[color=gray]", "159:8": "minecraft:stained_hardened_clay[color=silver]", "159:9": "minecraft:stained_hardened_clay[color=cyan]", "159:10": "minecraft:stained_hardened_clay[color=purple]", "159:11": "minecraft:stained_hardened_clay[color=blue]", "159:12": "minecraft:stained_hardened_clay[color=brown]", "159:13": "minecraft:stained_hardened_clay[color=green]", "159:14": "minecraft:stained_hardened_clay[color=red]", "159:15": "minecraft:stained_hardened_clay[color=black]", "160:0": "minecraft:stained_glass_pane[color=white]", "160:1": "minecraft:stained_glass_pane[color=orange]", "160:2": "minecraft:stained_glass_pane[color=magenta]", "160:3": "minecraft:stained_glass_pane[color=light_blue]", "160:4": "minecraft:stained_glass_pane[color=yellow]", "160:5": "minecraft:stained_glass_pane[color=lime]", "160:6": "minecraft:stained_glass_pane[color=pink]", "160:7": "minecraft:stained_glass_pane[color=gray]", "160:8": "minecraft:stained_glass_pane[color=silver]", "160:9": "minecraft:stained_glass_pane[color=cyan]", "160:10": "minecraft:stained_glass_pane[color=purple]", "160:11": "minecraft:stained_glass_pane[color=blue]", "160:12": "minecraft:stained_glass_pane[color=brown]", "160:13": "minecraft:stained_glass_pane[color=green]", "160:14": "minecraft:stained_glass_pane[color=red]", "160:15": "minecraft:stained_glass_pane[color=black]", "161:0": "minecraft:leaves2[new_leaf_type=acacia,persistent_bit=0,update_bit=0]", "161:1": "minecraft:leaves2[new_leaf_type=dark_oak,persistent_bit=0,update_bit=0]", "161:2": "minecraft:leaves2[new_leaf_type=acacia,persistent_bit=0,update_bit=1]", "161:3": "minecraft:leaves2[new_leaf_type=dark_oak,persistent_bit=0,update_bit=1]", "161:4": "minecraft:leaves2[new_leaf_type=acacia,persistent_bit=1,update_bit=0]", "161:5": "minecraft:leaves2[new_leaf_type=dark_oak,persistent_bit=1,update_bit=0]", "161:6": "minecraft:leaves2[new_leaf_type=acacia,persistent_bit=1,update_bit=1]", "161:7": "minecraft:leaves2[new_leaf_type=dark_oak,persistent_bit=1,update_bit=1]", "162:0": "minecraft:log2[new_log_type=acacia,pillar_axis=y]", "162:1": "minecraft:log2[new_log_type=dark_oak,pillar_axis=y]", "162:2": "minecraft:log2[new_log_type=acacia,pillar_axis=x]", "162:3": "minecraft:log2[new_log_type=dark_oak,pillar_axis=x]", "162:4": "minecraft:log2[new_log_type=acacia,pillar_axis=z]", "162:5": "minecraft:log2[new_log_type=dark_oak,pillar_axis=z]", "163:0": "minecraft:acacia_stairs[upside_down_bit=0,weirdo_direction=0]", "163:1": "minecraft:acacia_stairs[upside_down_bit=0,weirdo_direction=1]", "163:2": "minecraft:acacia_stairs[upside_down_bit=0,weirdo_direction=2]", "163:3": "minecraft:acacia_stairs[upside_down_bit=0,weirdo_direction=3]", "163:4": "minecraft:acacia_stairs[upside_down_bit=1,weirdo_direction=0]", "163:5": "minecraft:acacia_stairs[upside_down_bit=1,weirdo_direction=1]", "163:6": "minecraft:acacia_stairs[upside_down_bit=1,weirdo_direction=2]", "163:7": "minecraft:acacia_stairs[upside_down_bit=1,weirdo_direction=3]", "164:0": "minecraft:dark_oak_stairs[upside_down_bit=0,weirdo_direction=0]", "164:1": "minecraft:dark_oak_stairs[upside_down_bit=0,weirdo_direction=1]", "164:2": "minecraft:dark_oak_stairs[upside_down_bit=0,weirdo_direction=2]", "164:3": "minecraft:dark_oak_stairs[upside_down_bit=0,weirdo_direction=3]", "164:4": "minecraft:dark_oak_stairs[upside_down_bit=1,weirdo_direction=0]", "164:5": "minecraft:dark_oak_stairs[upside_down_bit=1,weirdo_direction=1]", "164:6": "minecraft:dark_oak_stairs[upside_down_bit=1,weirdo_direction=2]", "164:7": "minecraft:dark_oak_stairs[upside_down_bit=1,weirdo_direction=3]", "165:0": "minecraft:slime", "167:0": "minecraft:iron_trapdoor[direction=0,open_bit=0,upside_down_bit=0]", "167:1": "minecraft:iron_trapdoor[direction=1,open_bit=0,upside_down_bit=0]", "167:2": "minecraft:iron_trapdoor[direction=2,open_bit=0,upside_down_bit=0]", "167:3": "minecraft:iron_trapdoor[direction=3,open_bit=0,upside_down_bit=0]", "167:4": "minecraft:iron_trapdoor[direction=0,open_bit=0,upside_down_bit=1]", "167:5": "minecraft:iron_trapdoor[direction=1,open_bit=0,upside_down_bit=1]", "167:6": "minecraft:iron_trapdoor[direction=2,open_bit=0,upside_down_bit=1]", "167:7": "minecraft:iron_trapdoor[direction=3,open_bit=0,upside_down_bit=1]", "167:8": "minecraft:iron_trapdoor[direction=0,open_bit=1,upside_down_bit=0]", "167:9": "minecraft:iron_trapdoor[direction=1,open_bit=1,upside_down_bit=0]", "167:10": "minecraft:iron_trapdoor[direction=2,open_bit=1,upside_down_bit=0]", "167:11": "minecraft:iron_trapdoor[direction=3,open_bit=1,upside_down_bit=0]", "167:12": "minecraft:iron_trapdoor[direction=0,open_bit=1,upside_down_bit=1]", "167:13": "minecraft:iron_trapdoor[direction=1,open_bit=1,upside_down_bit=1]", "167:14": "minecraft:iron_trapdoor[direction=2,open_bit=1,upside_down_bit=1]", "167:15": "minecraft:iron_trapdoor[direction=3,open_bit=1,upside_down_bit=1]", "168:0": "minecraft:prismarine[prismarine_block_type=default]", "168:1": "minecraft:prismarine[prismarine_block_type=dark]", "168:2": "minecraft:prismarine[prismarine_block_type=bricks]", "169:0": "minecraft:seaLantern", "170:0": "minecraft:hay_block[deprecated=0,pillar_axis=y]", "170:1": "minecraft:hay_block[deprecated=1,pillar_axis=y]", "170:2": "minecraft:hay_block[deprecated=2,pillar_axis=y]", "170:3": "minecraft:hay_block[deprecated=3,pillar_axis=y]", "170:4": "minecraft:hay_block[deprecated=0,pillar_axis=x]", "170:5": "minecraft:hay_block[deprecated=1,pillar_axis=x]", "170:6": "minecraft:hay_block[deprecated=2,pillar_axis=x]", "170:7": "minecraft:hay_block[deprecated=3,pillar_axis=x]", "170:8": "minecraft:hay_block[deprecated=0,pillar_axis=z]", "170:9": "minecraft:hay_block[deprecated=1,pillar_axis=z]", "170:10": "minecraft:hay_block[deprecated=2,pillar_axis=z]", "170:11": "minecraft:hay_block[deprecated=3,pillar_axis=z]", "171:0": "minecraft:carpet[color=white]", "171:1": "minecraft:carpet[color=orange]", "171:2": "minecraft:carpet[color=magenta]", "171:3": "minecraft:carpet[color=light_blue]", "171:4": "minecraft:carpet[color=yellow]", "171:5": "minecraft:carpet[color=lime]", "171:6": "minecraft:carpet[color=pink]", "171:7": "minecraft:carpet[color=gray]", "171:8": "minecraft:carpet[color=silver]", "171:9": "minecraft:carpet[color=cyan]", "171:10": "minecraft:carpet[color=purple]", "171:11": "minecraft:carpet[color=blue]", "171:12": "minecraft:carpet[color=brown]", "171:13": "minecraft:carpet[color=green]", "171:14": "minecraft:carpet[color=red]", "171:15": "minecraft:carpet[color=black]", "172:0": "minecraft:hardened_clay", "173:0": "minecraft:coal_block", "174:0": "minecraft:packed_ice", "175:0": "minecraft:double_plant[double_plant_type=sunflower,upper_block_bit=0]", "175:1": "minecraft:double_plant[double_plant_type=syringa,upper_block_bit=0]", "175:2": "minecraft:double_plant[double_plant_type=grass,upper_block_bit=0]", "175:3": "minecraft:double_plant[double_plant_type=fern,upper_block_bit=0]", "175:4": "minecraft:double_plant[double_plant_type=rose,upper_block_bit=0]", "175:5": "minecraft:double_plant[double_plant_type=paeonia,upper_block_bit=0]", "175:6": "minecraft:double_plant[double_plant_type=sunflower,upper_block_bit=1]", "175:7": "minecraft:double_plant[double_plant_type=syringa,upper_block_bit=1]", "175:8": "minecraft:double_plant[double_plant_type=grass,upper_block_bit=1]", "175:9": "minecraft:double_plant[double_plant_type=fern,upper_block_bit=1]", "175:10": "minecraft:double_plant[double_plant_type=rose,upper_block_bit=1]", "175:11": "minecraft:double_plant[double_plant_type=paeonia,upper_block_bit=1]", "176:0": "minecraft:standing_banner[ground_sign_direction=0]", "176:1": "minecraft:standing_banner[ground_sign_direction=1]", "176:2": "minecraft:standing_banner[ground_sign_direction=2]", "176:3": "minecraft:standing_banner[ground_sign_direction=3]", "176:4": "minecraft:standing_banner[ground_sign_direction=4]", "176:5": "minecraft:standing_banner[ground_sign_direction=5]", "176:6": "minecraft:standing_banner[ground_sign_direction=6]", "176:7": "minecraft:standing_banner[ground_sign_direction=7]", "176:8": "minecraft:standing_banner[ground_sign_direction=8]", "176:9": "minecraft:standing_banner[ground_sign_direction=9]", "176:10": "minecraft:standing_banner[ground_sign_direction=10]", "176:11": "minecraft:standing_banner[ground_sign_direction=11]", "176:12": "minecraft:standing_banner[ground_sign_direction=12]", "176:13": "minecraft:standing_banner[ground_sign_direction=13]", "176:14": "minecraft:standing_banner[ground_sign_direction=14]", "176:15": "minecraft:standing_banner[ground_sign_direction=15]", "177:0": "minecraft:wall_banner[facing_direction=0]", "177:1": "minecraft:wall_banner[facing_direction=1]", "177:2": "minecraft:wall_banner[facing_direction=2]", "177:3": "minecraft:wall_banner[facing_direction=3]", "177:4": "minecraft:wall_banner[facing_direction=4]", "177:5": "minecraft:wall_banner[facing_direction=5]", "178:0": "minecraft:daylight_detector_inverted[redstone_signal=0]", "178:1": "minecraft:daylight_detector_inverted[redstone_signal=1]", "178:2": "minecraft:daylight_detector_inverted[redstone_signal=2]", "178:3": "minecraft:daylight_detector_inverted[redstone_signal=3]", "178:4": "minecraft:daylight_detector_inverted[redstone_signal=4]", "178:5": "minecraft:daylight_detector_inverted[redstone_signal=5]", "178:6": "minecraft:daylight_detector_inverted[redstone_signal=6]", "178:7": "minecraft:daylight_detector_inverted[redstone_signal=7]", "178:8": "minecraft:daylight_detector_inverted[redstone_signal=8]", "178:9": "minecraft:daylight_detector_inverted[redstone_signal=9]", "178:10": "minecraft:daylight_detector_inverted[redstone_signal=10]", "178:11": "minecraft:daylight_detector_inverted[redstone_signal=11]", "178:12": "minecraft:daylight_detector_inverted[redstone_signal=12]", "178:13": "minecraft:daylight_detector_inverted[redstone_signal=13]", "178:14": "minecraft:daylight_detector_inverted[redstone_signal=14]", "178:15": "minecraft:daylight_detector_inverted[redstone_signal=15]", "179:0": "minecraft:red_sandstone[sand_stone_type=default]", "179:1": "minecraft:red_sandstone[sand_stone_type=heiroglyphs]", "179:2": "minecraft:red_sandstone[sand_stone_type=cut]", "179:3": "minecraft:red_sandstone[sand_stone_type=smooth]", "180:0": "minecraft:red_sandstone_stairs[upside_down_bit=0,weirdo_direction=0]", "180:1": "minecraft:red_sandstone_stairs[upside_down_bit=0,weirdo_direction=1]", "180:2": "minecraft:red_sandstone_stairs[upside_down_bit=0,weirdo_direction=2]", "180:3": "minecraft:red_sandstone_stairs[upside_down_bit=0,weirdo_direction=3]", "180:4": "minecraft:red_sandstone_stairs[upside_down_bit=1,weirdo_direction=0]", "180:5": "minecraft:red_sandstone_stairs[upside_down_bit=1,weirdo_direction=1]", "180:6": "minecraft:red_sandstone_stairs[upside_down_bit=1,weirdo_direction=2]", "180:7": "minecraft:red_sandstone_stairs[upside_down_bit=1,weirdo_direction=3]", "181:0": "minecraft:double_stone_slab2[stone_slab_type_2=red_sandstone,top_slot_bit=0]", "181:1": "minecraft:double_stone_slab2[stone_slab_type_2=purpur,top_slot_bit=0]", "181:2": "minecraft:double_stone_slab2[stone_slab_type_2=prismarine_rough,top_slot_bit=0]", "181:3": "minecraft:double_stone_slab2[stone_slab_type_2=prismarine_dark,top_slot_bit=0]", "181:4": "minecraft:double_stone_slab2[stone_slab_type_2=prismarine_brick,top_slot_bit=0]", "181:5": "minecraft:double_stone_slab2[stone_slab_type_2=mossy_cobblestone,top_slot_bit=0]", "181:6": "minecraft:double_stone_slab2[stone_slab_type_2=smooth_sandstone,top_slot_bit=0]", "181:7": "minecraft:double_stone_slab2[stone_slab_type_2=red_nether_brick,top_slot_bit=0]", "181:8": "minecraft:double_stone_slab2[stone_slab_type_2=red_sandstone,top_slot_bit=1]", "181:9": "minecraft:double_stone_slab2[stone_slab_type_2=purpur,top_slot_bit=1]", "181:10": "minecraft:double_stone_slab2[stone_slab_type_2=prismarine_rough,top_slot_bit=1]", "181:11": "minecraft:double_stone_slab2[stone_slab_type_2=prismarine_dark,top_slot_bit=1]", "181:12": "minecraft:double_stone_slab2[stone_slab_type_2=prismarine_brick,top_slot_bit=1]", "181:13": "minecraft:double_stone_slab2[stone_slab_type_2=mossy_cobblestone,top_slot_bit=1]", "181:14": "minecraft:double_stone_slab2[stone_slab_type_2=smooth_sandstone,top_slot_bit=1]", "181:15": "minecraft:double_stone_slab2[stone_slab_type_2=red_nether_brick,top_slot_bit=1]", "182:0": "minecraft:stone_slab2[stone_slab_type_2=red_sandstone,top_slot_bit=0]", "182:1": "minecraft:stone_slab2[stone_slab_type_2=purpur,top_slot_bit=0]", "182:2": "minecraft:stone_slab2[stone_slab_type_2=prismarine_rough,top_slot_bit=0]", "182:3": "minecraft:stone_slab2[stone_slab_type_2=prismarine_dark,top_slot_bit=0]", "182:4": "minecraft:stone_slab2[stone_slab_type_2=prismarine_brick,top_slot_bit=0]", "182:5": "minecraft:stone_slab2[stone_slab_type_2=mossy_cobblestone,top_slot_bit=0]", "182:6": "minecraft:stone_slab2[stone_slab_type_2=smooth_sandstone,top_slot_bit=0]", "182:7": "minecraft:stone_slab2[stone_slab_type_2=red_nether_brick,top_slot_bit=0]", "182:8": "minecraft:stone_slab2[stone_slab_type_2=red_sandstone,top_slot_bit=1]", "182:9": "minecraft:stone_slab2[stone_slab_type_2=purpur,top_slot_bit=1]", "182:10": "minecraft:stone_slab2[stone_slab_type_2=prismarine_rough,top_slot_bit=1]", "182:11": "minecraft:stone_slab2[stone_slab_type_2=prismarine_dark,top_slot_bit=1]", "182:12": "minecraft:stone_slab2[stone_slab_type_2=prismarine_brick,top_slot_bit=1]", "182:13": "minecraft:stone_slab2[stone_slab_type_2=mossy_cobblestone,top_slot_bit=1]", "182:14": "minecraft:stone_slab2[stone_slab_type_2=smooth_sandstone,top_slot_bit=1]", "182:15": "minecraft:stone_slab2[stone_slab_type_2=red_nether_brick,top_slot_bit=1]", "183:0": "minecraft:spruce_fence_gate[direction=0,in_wall_bit=0,open_bit=0]", "183:1": "minecraft:spruce_fence_gate[direction=1,in_wall_bit=0,open_bit=0]", "183:2": "minecraft:spruce_fence_gate[direction=2,in_wall_bit=0,open_bit=0]", "183:3": "minecraft:spruce_fence_gate[direction=3,in_wall_bit=0,open_bit=0]", "183:4": "minecraft:spruce_fence_gate[direction=0,in_wall_bit=0,open_bit=1]", "183:5": "minecraft:spruce_fence_gate[direction=1,in_wall_bit=0,open_bit=1]", "183:6": "minecraft:spruce_fence_gate[direction=2,in_wall_bit=0,open_bit=1]", "183:7": "minecraft:spruce_fence_gate[direction=3,in_wall_bit=0,open_bit=1]", "183:8": "minecraft:spruce_fence_gate[direction=0,in_wall_bit=1,open_bit=0]", "183:9": "minecraft:spruce_fence_gate[direction=1,in_wall_bit=1,open_bit=0]", "183:10": "minecraft:spruce_fence_gate[direction=2,in_wall_bit=1,open_bit=0]", "183:11": "minecraft:spruce_fence_gate[direction=3,in_wall_bit=1,open_bit=0]", "183:12": "minecraft:spruce_fence_gate[direction=0,in_wall_bit=1,open_bit=1]", "183:13": "minecraft:spruce_fence_gate[direction=1,in_wall_bit=1,open_bit=1]", "183:14": "minecraft:spruce_fence_gate[direction=2,in_wall_bit=1,open_bit=1]", "183:15": "minecraft:spruce_fence_gate[direction=3,in_wall_bit=1,open_bit=1]", "184:0": "minecraft:birch_fence_gate[direction=0,in_wall_bit=0,open_bit=0]", "184:1": "minecraft:birch_fence_gate[direction=1,in_wall_bit=0,open_bit=0]", "184:2": "minecraft:birch_fence_gate[direction=2,in_wall_bit=0,open_bit=0]", "184:3": "minecraft:birch_fence_gate[direction=3,in_wall_bit=0,open_bit=0]", "184:4": "minecraft:birch_fence_gate[direction=0,in_wall_bit=0,open_bit=1]", "184:5": "minecraft:birch_fence_gate[direction=1,in_wall_bit=0,open_bit=1]", "184:6": "minecraft:birch_fence_gate[direction=2,in_wall_bit=0,open_bit=1]", "184:7": "minecraft:birch_fence_gate[direction=3,in_wall_bit=0,open_bit=1]", "184:8": "minecraft:birch_fence_gate[direction=0,in_wall_bit=1,open_bit=0]", "184:9": "minecraft:birch_fence_gate[direction=1,in_wall_bit=1,open_bit=0]", "184:10": "minecraft:birch_fence_gate[direction=2,in_wall_bit=1,open_bit=0]", "184:11": "minecraft:birch_fence_gate[direction=3,in_wall_bit=1,open_bit=0]", "184:12": "minecraft:birch_fence_gate[direction=0,in_wall_bit=1,open_bit=1]", "184:13": "minecraft:birch_fence_gate[direction=1,in_wall_bit=1,open_bit=1]", "184:14": "minecraft:birch_fence_gate[direction=2,in_wall_bit=1,open_bit=1]", "184:15": "minecraft:birch_fence_gate[direction=3,in_wall_bit=1,open_bit=1]", "185:0": "minecraft:jungle_fence_gate[direction=0,in_wall_bit=0,open_bit=0]", "185:1": "minecraft:jungle_fence_gate[direction=1,in_wall_bit=0,open_bit=0]", "185:2": "minecraft:jungle_fence_gate[direction=2,in_wall_bit=0,open_bit=0]", "185:3": "minecraft:jungle_fence_gate[direction=3,in_wall_bit=0,open_bit=0]", "185:4": "minecraft:jungle_fence_gate[direction=0,in_wall_bit=0,open_bit=1]", "185:5": "minecraft:jungle_fence_gate[direction=1,in_wall_bit=0,open_bit=1]", "185:6": "minecraft:jungle_fence_gate[direction=2,in_wall_bit=0,open_bit=1]", "185:7": "minecraft:jungle_fence_gate[direction=3,in_wall_bit=0,open_bit=1]", "185:8": "minecraft:jungle_fence_gate[direction=0,in_wall_bit=1,open_bit=0]", "185:9": "minecraft:jungle_fence_gate[direction=1,in_wall_bit=1,open_bit=0]", "185:10": "minecraft:jungle_fence_gate[direction=2,in_wall_bit=1,open_bit=0]", "185:11": "minecraft:jungle_fence_gate[direction=3,in_wall_bit=1,open_bit=0]", "185:12": "minecraft:jungle_fence_gate[direction=0,in_wall_bit=1,open_bit=1]", "185:13": "minecraft:jungle_fence_gate[direction=1,in_wall_bit=1,open_bit=1]", "185:14": "minecraft:jungle_fence_gate[direction=2,in_wall_bit=1,open_bit=1]", "185:15": "minecraft:jungle_fence_gate[direction=3,in_wall_bit=1,open_bit=1]", "186:0": "minecraft:dark_oak_fence_gate[direction=0,in_wall_bit=0,open_bit=0]", "186:1": "minecraft:dark_oak_fence_gate[direction=1,in_wall_bit=0,open_bit=0]", "186:2": "minecraft:dark_oak_fence_gate[direction=2,in_wall_bit=0,open_bit=0]", "186:3": "minecraft:dark_oak_fence_gate[direction=3,in_wall_bit=0,open_bit=0]", "186:4": "minecraft:dark_oak_fence_gate[direction=0,in_wall_bit=0,open_bit=1]", "186:5": "minecraft:dark_oak_fence_gate[direction=1,in_wall_bit=0,open_bit=1]", "186:6": "minecraft:dark_oak_fence_gate[direction=2,in_wall_bit=0,open_bit=1]", "186:7": "minecraft:dark_oak_fence_gate[direction=3,in_wall_bit=0,open_bit=1]", "186:8": "minecraft:dark_oak_fence_gate[direction=0,in_wall_bit=1,open_bit=0]", "186:9": "minecraft:dark_oak_fence_gate[direction=1,in_wall_bit=1,open_bit=0]", "186:10": "minecraft:dark_oak_fence_gate[direction=2,in_wall_bit=1,open_bit=0]", "186:11": "minecraft:dark_oak_fence_gate[direction=3,in_wall_bit=1,open_bit=0]", "186:12": "minecraft:dark_oak_fence_gate[direction=0,in_wall_bit=1,open_bit=1]", "186:13": "minecraft:dark_oak_fence_gate[direction=1,in_wall_bit=1,open_bit=1]", "186:14": "minecraft:dark_oak_fence_gate[direction=2,in_wall_bit=1,open_bit=1]", "186:15": "minecraft:dark_oak_fence_gate[direction=3,in_wall_bit=1,open_bit=1]", "187:0": "minecraft:acacia_fence_gate[direction=0,in_wall_bit=0,open_bit=0]", "187:1": "minecraft:acacia_fence_gate[direction=1,in_wall_bit=0,open_bit=0]", "187:2": "minecraft:acacia_fence_gate[direction=2,in_wall_bit=0,open_bit=0]", "187:3": "minecraft:acacia_fence_gate[direction=3,in_wall_bit=0,open_bit=0]", "187:4": "minecraft:acacia_fence_gate[direction=0,in_wall_bit=0,open_bit=1]", "187:5": "minecraft:acacia_fence_gate[direction=1,in_wall_bit=0,open_bit=1]", "187:6": "minecraft:acacia_fence_gate[direction=2,in_wall_bit=0,open_bit=1]", "187:7": "minecraft:acacia_fence_gate[direction=3,in_wall_bit=0,open_bit=1]", "187:8": "minecraft:acacia_fence_gate[direction=0,in_wall_bit=1,open_bit=0]", "187:9": "minecraft:acacia_fence_gate[direction=1,in_wall_bit=1,open_bit=0]", "187:10": "minecraft:acacia_fence_gate[direction=2,in_wall_bit=1,open_bit=0]", "187:11": "minecraft:acacia_fence_gate[direction=3,in_wall_bit=1,open_bit=0]", "187:12": "minecraft:acacia_fence_gate[direction=0,in_wall_bit=1,open_bit=1]", "187:13": "minecraft:acacia_fence_gate[direction=1,in_wall_bit=1,open_bit=1]", "187:14": "minecraft:acacia_fence_gate[direction=2,in_wall_bit=1,open_bit=1]", "187:15": "minecraft:acacia_fence_gate[direction=3,in_wall_bit=1,open_bit=1]", "188:0": "minecraft:repeating_command_block[conditional_bit=0,facing_direction=0]", "188:1": "minecraft:repeating_command_block[conditional_bit=0,facing_direction=1]", "188:2": "minecraft:repeating_command_block[conditional_bit=0,facing_direction=2]", "188:3": "minecraft:repeating_command_block[conditional_bit=0,facing_direction=3]", "188:4": "minecraft:repeating_command_block[conditional_bit=0,facing_direction=4]", "188:5": "minecraft:repeating_command_block[conditional_bit=0,facing_direction=5]", "188:6": "minecraft:repeating_command_block[conditional_bit=1,facing_direction=0]", "188:7": "minecraft:repeating_command_block[conditional_bit=1,facing_direction=1]", "188:8": "minecraft:repeating_command_block[conditional_bit=1,facing_direction=2]", "188:9": "minecraft:repeating_command_block[conditional_bit=1,facing_direction=3]", "188:10": "minecraft:repeating_command_block[conditional_bit=1,facing_direction=4]", "188:11": "minecraft:repeating_command_block[conditional_bit=1,facing_direction=5]", "189:0": "minecraft:chain_command_block[conditional_bit=0,facing_direction=0]", "189:1": "minecraft:chain_command_block[conditional_bit=0,facing_direction=1]", "189:2": "minecraft:chain_command_block[conditional_bit=0,facing_direction=2]", "189:3": "minecraft:chain_command_block[conditional_bit=0,facing_direction=3]", "189:4": "minecraft:chain_command_block[conditional_bit=0,facing_direction=4]", "189:5": "minecraft:chain_command_block[conditional_bit=0,facing_direction=5]", "189:6": "minecraft:chain_command_block[conditional_bit=1,facing_direction=0]", "189:7": "minecraft:chain_command_block[conditional_bit=1,facing_direction=1]", "189:8": "minecraft:chain_command_block[conditional_bit=1,facing_direction=2]", "189:9": "minecraft:chain_command_block[conditional_bit=1,facing_direction=3]", "189:10": "minecraft:chain_command_block[conditional_bit=1,facing_direction=4]", "189:11": "minecraft:chain_command_block[conditional_bit=1,facing_direction=5]", "190:0": "minecraft:hard_glass_pane", "191:0": "minecraft:hard_stained_glass_pane[color=white]", "191:1": "minecraft:hard_stained_glass_pane[color=orange]", "191:2": "minecraft:hard_stained_glass_pane[color=magenta]", "191:3": "minecraft:hard_stained_glass_pane[color=light_blue]", "191:4": "minecraft:hard_stained_glass_pane[color=yellow]", "191:5": "minecraft:hard_stained_glass_pane[color=lime]", "191:6": "minecraft:hard_stained_glass_pane[color=pink]", "191:7": "minecraft:hard_stained_glass_pane[color=gray]", "191:8": "minecraft:hard_stained_glass_pane[color=silver]", "191:9": "minecraft:hard_stained_glass_pane[color=cyan]", "191:10": "minecraft:hard_stained_glass_pane[color=purple]", "191:11": "minecraft:hard_stained_glass_pane[color=blue]", "191:12": "minecraft:hard_stained_glass_pane[color=brown]", "191:13": "minecraft:hard_stained_glass_pane[color=green]", "191:14": "minecraft:hard_stained_glass_pane[color=red]", "191:15": "minecraft:hard_stained_glass_pane[color=black]", "192:0": "minecraft:chemical_heat", "193:0": "minecraft:spruce_door[direction=0,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "193:1": "minecraft:spruce_door[direction=1,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "193:2": "minecraft:spruce_door[direction=2,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "193:3": "minecraft:spruce_door[direction=3,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "193:4": "minecraft:spruce_door[direction=0,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "193:5": "minecraft:spruce_door[direction=1,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "193:6": "minecraft:spruce_door[direction=2,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "193:7": "minecraft:spruce_door[direction=3,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "193:8": "minecraft:spruce_door[direction=0,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "193:9": "minecraft:spruce_door[direction=1,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "193:10": "minecraft:spruce_door[direction=2,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "193:11": "minecraft:spruce_door[direction=3,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "193:12": "minecraft:spruce_door[direction=0,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "193:13": "minecraft:spruce_door[direction=1,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "193:14": "minecraft:spruce_door[direction=2,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "193:15": "minecraft:spruce_door[direction=3,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "194:0": "minecraft:birch_door[direction=0,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "194:1": "minecraft:birch_door[direction=1,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "194:2": "minecraft:birch_door[direction=2,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "194:3": "minecraft:birch_door[direction=3,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "194:4": "minecraft:birch_door[direction=0,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "194:5": "minecraft:birch_door[direction=1,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "194:6": "minecraft:birch_door[direction=2,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "194:7": "minecraft:birch_door[direction=3,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "194:8": "minecraft:birch_door[direction=0,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "194:9": "minecraft:birch_door[direction=1,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "194:10": "minecraft:birch_door[direction=2,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "194:11": "minecraft:birch_door[direction=3,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "194:12": "minecraft:birch_door[direction=0,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "194:13": "minecraft:birch_door[direction=1,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "194:14": "minecraft:birch_door[direction=2,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "194:15": "minecraft:birch_door[direction=3,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "195:0": "minecraft:jungle_door[direction=0,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "195:1": "minecraft:jungle_door[direction=1,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "195:2": "minecraft:jungle_door[direction=2,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "195:3": "minecraft:jungle_door[direction=3,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "195:4": "minecraft:jungle_door[direction=0,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "195:5": "minecraft:jungle_door[direction=1,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "195:6": "minecraft:jungle_door[direction=2,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "195:7": "minecraft:jungle_door[direction=3,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "195:8": "minecraft:jungle_door[direction=0,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "195:9": "minecraft:jungle_door[direction=1,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "195:10": "minecraft:jungle_door[direction=2,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "195:11": "minecraft:jungle_door[direction=3,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "195:12": "minecraft:jungle_door[direction=0,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "195:13": "minecraft:jungle_door[direction=1,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "195:14": "minecraft:jungle_door[direction=2,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "195:15": "minecraft:jungle_door[direction=3,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "196:0": "minecraft:acacia_door[direction=0,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "196:1": "minecraft:acacia_door[direction=1,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "196:2": "minecraft:acacia_door[direction=2,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "196:3": "minecraft:acacia_door[direction=3,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "196:4": "minecraft:acacia_door[direction=0,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "196:5": "minecraft:acacia_door[direction=1,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "196:6": "minecraft:acacia_door[direction=2,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "196:7": "minecraft:acacia_door[direction=3,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "196:8": "minecraft:acacia_door[direction=0,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "196:9": "minecraft:acacia_door[direction=1,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "196:10": "minecraft:acacia_door[direction=2,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "196:11": "minecraft:acacia_door[direction=3,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "196:12": "minecraft:acacia_door[direction=0,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "196:13": "minecraft:acacia_door[direction=1,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "196:14": "minecraft:acacia_door[direction=2,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "196:15": "minecraft:acacia_door[direction=3,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "197:0": "minecraft:dark_oak_door[direction=0,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "197:1": "minecraft:dark_oak_door[direction=1,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "197:2": "minecraft:dark_oak_door[direction=2,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "197:3": "minecraft:dark_oak_door[direction=3,door_hinge_bit=0,open_bit=0,upper_block_bit=0]", "197:4": "minecraft:dark_oak_door[direction=0,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "197:5": "minecraft:dark_oak_door[direction=1,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "197:6": "minecraft:dark_oak_door[direction=2,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "197:7": "minecraft:dark_oak_door[direction=3,door_hinge_bit=0,open_bit=1,upper_block_bit=0]", "197:8": "minecraft:dark_oak_door[direction=0,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "197:9": "minecraft:dark_oak_door[direction=1,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "197:10": "minecraft:dark_oak_door[direction=2,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "197:11": "minecraft:dark_oak_door[direction=3,door_hinge_bit=0,open_bit=0,upper_block_bit=1]", "197:12": "minecraft:dark_oak_door[direction=0,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "197:13": "minecraft:dark_oak_door[direction=1,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "197:14": "minecraft:dark_oak_door[direction=2,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "197:15": "minecraft:dark_oak_door[direction=3,door_hinge_bit=0,open_bit=1,upper_block_bit=1]", "198:0": "minecraft:grass_path", "199:0": "minecraft:frame[facing_direction=0,item_frame_map_bit=0,item_frame_photo_bit=0]", "199:1": "minecraft:frame[facing_direction=1,item_frame_map_bit=0,item_frame_photo_bit=0]", "199:2": "minecraft:frame[facing_direction=2,item_frame_map_bit=0,item_frame_photo_bit=0]", "199:3": "minecraft:frame[facing_direction=3,item_frame_map_bit=0,item_frame_photo_bit=0]", "199:4": "minecraft:frame[facing_direction=4,item_frame_map_bit=0,item_frame_photo_bit=0]", "199:5": "minecraft:frame[facing_direction=5,item_frame_map_bit=0,item_frame_photo_bit=0]", "199:6": "minecraft:frame[facing_direction=0,item_frame_map_bit=1,item_frame_photo_bit=0]", "199:7": "minecraft:frame[facing_direction=1,item_frame_map_bit=1,item_frame_photo_bit=0]", "199:8": "minecraft:frame[facing_direction=2,item_frame_map_bit=1,item_frame_photo_bit=0]", "199:9": "minecraft:frame[facing_direction=3,item_frame_map_bit=1,item_frame_photo_bit=0]", "199:10": "minecraft:frame[facing_direction=4,item_frame_map_bit=1,item_frame_photo_bit=0]", "199:11": "minecraft:frame[facing_direction=5,item_frame_map_bit=1,item_frame_photo_bit=0]", "199:12": "minecraft:frame[facing_direction=0,item_frame_map_bit=0,item_frame_photo_bit=1]", "199:13": "minecraft:frame[facing_direction=1,item_frame_map_bit=0,item_frame_photo_bit=1]", "199:14": "minecraft:frame[facing_direction=2,item_frame_map_bit=0,item_frame_photo_bit=1]", "199:15": "minecraft:frame[facing_direction=3,item_frame_map_bit=0,item_frame_photo_bit=1]", "200:0": "minecraft:chorus_flower[age=0]", "200:1": "minecraft:chorus_flower[age=1]", "200:2": "minecraft:chorus_flower[age=2]", "200:3": "minecraft:chorus_flower[age=3]", "200:4": "minecraft:chorus_flower[age=4]", "200:5": "minecraft:chorus_flower[age=5]", "201:0": "minecraft:purpur_block[chisel_type=default,pillar_axis=y]", "201:1": "minecraft:purpur_block[chisel_type=chiseled,pillar_axis=y]", "201:2": "minecraft:purpur_block[chisel_type=lines,pillar_axis=y]", "201:3": "minecraft:purpur_block[chisel_type=smooth,pillar_axis=y]", "201:4": "minecraft:purpur_block[chisel_type=default,pillar_axis=x]", "201:5": "minecraft:purpur_block[chisel_type=chiseled,pillar_axis=x]", "201:6": "minecraft:purpur_block[chisel_type=lines,pillar_axis=x]", "201:7": "minecraft:purpur_block[chisel_type=smooth,pillar_axis=x]", "201:8": "minecraft:purpur_block[chisel_type=default,pillar_axis=z]", "201:9": "minecraft:purpur_block[chisel_type=chiseled,pillar_axis=z]", "201:10": "minecraft:purpur_block[chisel_type=lines,pillar_axis=z]", "201:11": "minecraft:purpur_block[chisel_type=smooth,pillar_axis=z]", "202:0": "minecraft:colored_torch_rg[color_bit=0,torch_facing_direction=unknown]", "202:1": "minecraft:colored_torch_rg[color_bit=0,torch_facing_direction=west]", "202:2": "minecraft:colored_torch_rg[color_bit=0,torch_facing_direction=east]", "202:3": "minecraft:colored_torch_rg[color_bit=0,torch_facing_direction=north]", "202:4": "minecraft:colored_torch_rg[color_bit=0,torch_facing_direction=south]", "202:5": "minecraft:colored_torch_rg[color_bit=0,torch_facing_direction=top]", "202:6": "minecraft:colored_torch_rg[color_bit=1,torch_facing_direction=unknown]", "202:7": "minecraft:colored_torch_rg[color_bit=1,torch_facing_direction=west]", "202:8": "minecraft:colored_torch_rg[color_bit=1,torch_facing_direction=east]", "202:9": "minecraft:colored_torch_rg[color_bit=1,torch_facing_direction=north]", "202:10": "minecraft:colored_torch_rg[color_bit=1,torch_facing_direction=south]", "202:11": "minecraft:colored_torch_rg[color_bit=1,torch_facing_direction=top]", "203:0": "minecraft:purpur_stairs[upside_down_bit=0,weirdo_direction=0]", "203:1": "minecraft:purpur_stairs[upside_down_bit=0,weirdo_direction=1]", "203:2": "minecraft:purpur_stairs[upside_down_bit=0,weirdo_direction=2]", "203:3": "minecraft:purpur_stairs[upside_down_bit=0,weirdo_direction=3]", "203:4": "minecraft:purpur_stairs[upside_down_bit=1,weirdo_direction=0]", "203:5": "minecraft:purpur_stairs[upside_down_bit=1,weirdo_direction=1]", "203:6": "minecraft:purpur_stairs[upside_down_bit=1,weirdo_direction=2]", "203:7": "minecraft:purpur_stairs[upside_down_bit=1,weirdo_direction=3]", "204:0": "minecraft:colored_torch_bp[color_bit=0,torch_facing_direction=unknown]", "204:1": "minecraft:colored_torch_bp[color_bit=0,torch_facing_direction=west]", "204:2": "minecraft:colored_torch_bp[color_bit=0,torch_facing_direction=east]", "204:3": "minecraft:colored_torch_bp[color_bit=0,torch_facing_direction=north]", "204:4": "minecraft:colored_torch_bp[color_bit=0,torch_facing_direction=south]", "204:5": "minecraft:colored_torch_bp[color_bit=0,torch_facing_direction=top]", "204:6": "minecraft:colored_torch_bp[color_bit=1,torch_facing_direction=unknown]", "204:7": "minecraft:colored_torch_bp[color_bit=1,torch_facing_direction=west]", "204:8": "minecraft:colored_torch_bp[color_bit=1,torch_facing_direction=east]", "204:9": "minecraft:colored_torch_bp[color_bit=1,torch_facing_direction=north]", "204:10": "minecraft:colored_torch_bp[color_bit=1,torch_facing_direction=south]", "204:11": "minecraft:colored_torch_bp[color_bit=1,torch_facing_direction=top]", "205:0": "minecraft:undyed_shulker_box", "206:0": "minecraft:end_bricks", "207:0": "minecraft:frosted_ice[age=0]", "207:1": "minecraft:frosted_ice[age=1]", "207:2": "minecraft:frosted_ice[age=2]", "207:3": "minecraft:frosted_ice[age=3]", "208:0": "minecraft:end_rod[facing_direction=0]", "208:1": "minecraft:end_rod[facing_direction=1]", "208:2": "minecraft:end_rod[facing_direction=2]", "208:3": "minecraft:end_rod[facing_direction=3]", "208:4": "minecraft:end_rod[facing_direction=4]", "208:5": "minecraft:end_rod[facing_direction=5]", "209:0": "minecraft:end_gateway", "210:0": "minecraft:allow", "211:0": "minecraft:deny", "212:0": "minecraft:border_block[wall_connection_type_east=none,wall_connection_type_north=none,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=0]", "212:1": "minecraft:border_block[wall_connection_type_east=none,wall_connection_type_north=none,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=1]", "212:2": "minecraft:border_block[wall_connection_type_east=none,wall_connection_type_north=short,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=0]", "212:3": "minecraft:border_block[wall_connection_type_east=none,wall_connection_type_north=short,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=1]", "212:4": "minecraft:border_block[wall_connection_type_east=none,wall_connection_type_north=tall,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=0]", "212:5": "minecraft:border_block[wall_connection_type_east=none,wall_connection_type_north=tall,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=1]", "212:6": "minecraft:border_block[wall_connection_type_east=short,wall_connection_type_north=none,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=0]", "212:7": "minecraft:border_block[wall_connection_type_east=short,wall_connection_type_north=none,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=1]", "212:8": "minecraft:border_block[wall_connection_type_east=short,wall_connection_type_north=short,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=0]", "212:9": "minecraft:border_block[wall_connection_type_east=short,wall_connection_type_north=short,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=1]", "212:10": "minecraft:border_block[wall_connection_type_east=short,wall_connection_type_north=tall,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=0]", "212:11": "minecraft:border_block[wall_connection_type_east=short,wall_connection_type_north=tall,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=1]", "212:12": "minecraft:border_block[wall_connection_type_east=tall,wall_connection_type_north=none,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=0]", "212:13": "minecraft:border_block[wall_connection_type_east=tall,wall_connection_type_north=none,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=1]", "212:14": "minecraft:border_block[wall_connection_type_east=tall,wall_connection_type_north=short,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=0]", "212:15": "minecraft:border_block[wall_connection_type_east=tall,wall_connection_type_north=short,wall_connection_type_south=none,wall_connection_type_west=none,wall_post_bit=1]", "213:0": "minecraft:magma", "214:0": "minecraft:nether_wart_block", "215:0": "minecraft:red_nether_brick", "216:0": "minecraft:bone_block[deprecated=0,pillar_axis=y]", "216:1": "minecraft:bone_block[deprecated=1,pillar_axis=y]", "216:2": "minecraft:bone_block[deprecated=2,pillar_axis=y]", "216:3": "minecraft:bone_block[deprecated=3,pillar_axis=y]", "216:4": "minecraft:bone_block[deprecated=0,pillar_axis=x]", "216:5": "minecraft:bone_block[deprecated=1,pillar_axis=x]", "216:6": "minecraft:bone_block[deprecated=2,pillar_axis=x]", "216:7": "minecraft:bone_block[deprecated=3,pillar_axis=x]", "216:8": "minecraft:bone_block[deprecated=0,pillar_axis=z]", "216:9": "minecraft:bone_block[deprecated=1,pillar_axis=z]", "216:10": "minecraft:bone_block[deprecated=2,pillar_axis=z]", "216:11": "minecraft:bone_block[deprecated=3,pillar_axis=z]", "217:0": "minecraft:structure_void[structure_void_type=void]", "217:1": "minecraft:structure_void[structure_void_type=air]", "218:0": "minecraft:shulker_box[color=white]", "218:1": "minecraft:shulker_box[color=orange]", "218:2": "minecraft:shulker_box[color=magenta]", "218:3": "minecraft:shulker_box[color=light_blue]", "218:4": "minecraft:shulker_box[color=yellow]", "218:5": "minecraft:shulker_box[color=lime]", "218:6": "minecraft:shulker_box[color=pink]", "218:7": "minecraft:shulker_box[color=gray]", "218:8": "minecraft:shulker_box[color=silver]", "218:9": "minecraft:shulker_box[color=cyan]", "218:10": "minecraft:shulker_box[color=purple]", "218:11": "minecraft:shulker_box[color=blue]", "218:12": "minecraft:shulker_box[color=brown]", "218:13": "minecraft:shulker_box[color=green]", "218:14": "minecraft:shulker_box[color=red]", "218:15": "minecraft:shulker_box[color=black]", "219:0": "minecraft:purple_glazed_terracotta[facing_direction=0]", "219:1": "minecraft:purple_glazed_terracotta[facing_direction=1]", "219:2": "minecraft:purple_glazed_terracotta[facing_direction=2]", "219:3": "minecraft:purple_glazed_terracotta[facing_direction=3]", "219:4": "minecraft:purple_glazed_terracotta[facing_direction=4]", "219:5": "minecraft:purple_glazed_terracotta[facing_direction=5]", "220:0": "minecraft:white_glazed_terracotta[facing_direction=0]", "220:1": "minecraft:white_glazed_terracotta[facing_direction=1]", "220:2": "minecraft:white_glazed_terracotta[facing_direction=2]", "220:3": "minecraft:white_glazed_terracotta[facing_direction=3]", "220:4": "minecraft:white_glazed_terracotta[facing_direction=4]", "220:5": "minecraft:white_glazed_terracotta[facing_direction=5]", "221:0": "minecraft:orange_glazed_terracotta[facing_direction=0]", "221:1": "minecraft:orange_glazed_terracotta[facing_direction=1]", "221:2": "minecraft:orange_glazed_terracotta[facing_direction=2]", "221:3": "minecraft:orange_glazed_terracotta[facing_direction=3]", "221:4": "minecraft:orange_glazed_terracotta[facing_direction=4]", "221:5": "minecraft:orange_glazed_terracotta[facing_direction=5]", "222:0": "minecraft:magenta_glazed_terracotta[facing_direction=0]", "222:1": "minecraft:magenta_glazed_terracotta[facing_direction=1]", "222:2": "minecraft:magenta_glazed_terracotta[facing_direction=2]", "222:3": "minecraft:magenta_glazed_terracotta[facing_direction=3]", "222:4": "minecraft:magenta_glazed_terracotta[facing_direction=4]", "222:5": "minecraft:magenta_glazed_terracotta[facing_direction=5]", "223:0": "minecraft:light_blue_glazed_terracotta[facing_direction=0]", "223:1": "minecraft:light_blue_glazed_terracotta[facing_direction=1]", "223:2": "minecraft:light_blue_glazed_terracotta[facing_direction=2]", "223:3": "minecraft:light_blue_glazed_terracotta[facing_direction=3]", "223:4": "minecraft:light_blue_glazed_terracotta[facing_direction=4]", "223:5": "minecraft:light_blue_glazed_terracotta[facing_direction=5]", "224:0": "minecraft:yellow_glazed_terracotta[facing_direction=0]", "224:1": "minecraft:yellow_glazed_terracotta[facing_direction=1]", "224:2": "minecraft:yellow_glazed_terracotta[facing_direction=2]", "224:3": "minecraft:yellow_glazed_terracotta[facing_direction=3]", "224:4": "minecraft:yellow_glazed_terracotta[facing_direction=4]", "224:5": "minecraft:yellow_glazed_terracotta[facing_direction=5]", "225:0": "minecraft:lime_glazed_terracotta[facing_direction=0]", "225:1": "minecraft:lime_glazed_terracotta[facing_direction=1]", "225:2": "minecraft:lime_glazed_terracotta[facing_direction=2]", "225:3": "minecraft:lime_glazed_terracotta[facing_direction=3]", "225:4": "minecraft:lime_glazed_terracotta[facing_direction=4]", "225:5": "minecraft:lime_glazed_terracotta[facing_direction=5]", "226:0": "minecraft:pink_glazed_terracotta[facing_direction=0]", "226:1": "minecraft:pink_glazed_terracotta[facing_direction=1]", "226:2": "minecraft:pink_glazed_terracotta[facing_direction=2]", "226:3": "minecraft:pink_glazed_terracotta[facing_direction=3]", "226:4": "minecraft:pink_glazed_terracotta[facing_direction=4]", "226:5": "minecraft:pink_glazed_terracotta[facing_direction=5]", "227:0": "minecraft:gray_glazed_terracotta[facing_direction=0]", "227:1": "minecraft:gray_glazed_terracotta[facing_direction=1]", "227:2": "minecraft:gray_glazed_terracotta[facing_direction=2]", "227:3": "minecraft:gray_glazed_terracotta[facing_direction=3]", "227:4": "minecraft:gray_glazed_terracotta[facing_direction=4]", "227:5": "minecraft:gray_glazed_terracotta[facing_direction=5]", "228:0": "minecraft:silver_glazed_terracotta[facing_direction=0]", "228:1": "minecraft:silver_glazed_terracotta[facing_direction=1]", "228:2": "minecraft:silver_glazed_terracotta[facing_direction=2]", "228:3": "minecraft:silver_glazed_terracotta[facing_direction=3]", "228:4": "minecraft:silver_glazed_terracotta[facing_direction=4]", "228:5": "minecraft:silver_glazed_terracotta[facing_direction=5]", "229:0": "minecraft:cyan_glazed_terracotta[facing_direction=0]", "229:1": "minecraft:cyan_glazed_terracotta[facing_direction=1]", "229:2": "minecraft:cyan_glazed_terracotta[facing_direction=2]", "229:3": "minecraft:cyan_glazed_terracotta[facing_direction=3]", "229:4": "minecraft:cyan_glazed_terracotta[facing_direction=4]", "229:5": "minecraft:cyan_glazed_terracotta[facing_direction=5]", "231:0": "minecraft:blue_glazed_terracotta[facing_direction=0]", "231:1": "minecraft:blue_glazed_terracotta[facing_direction=1]", "231:2": "minecraft:blue_glazed_terracotta[facing_direction=2]", "231:3": "minecraft:blue_glazed_terracotta[facing_direction=3]", "231:4": "minecraft:blue_glazed_terracotta[facing_direction=4]", "231:5": "minecraft:blue_glazed_terracotta[facing_direction=5]", "232:0": "minecraft:brown_glazed_terracotta[facing_direction=0]", "232:1": "minecraft:brown_glazed_terracotta[facing_direction=1]", "232:2": "minecraft:brown_glazed_terracotta[facing_direction=2]", "232:3": "minecraft:brown_glazed_terracotta[facing_direction=3]", "232:4": "minecraft:brown_glazed_terracotta[facing_direction=4]", "232:5": "minecraft:brown_glazed_terracotta[facing_direction=5]", "233:0": "minecraft:green_glazed_terracotta[facing_direction=0]", "233:1": "minecraft:green_glazed_terracotta[facing_direction=1]", "233:2": "minecraft:green_glazed_terracotta[facing_direction=2]", "233:3": "minecraft:green_glazed_terracotta[facing_direction=3]", "233:4": "minecraft:green_glazed_terracotta[facing_direction=4]", "233:5": "minecraft:green_glazed_terracotta[facing_direction=5]", "234:0": "minecraft:red_glazed_terracotta[facing_direction=0]", "234:1": "minecraft:red_glazed_terracotta[facing_direction=1]", "234:2": "minecraft:red_glazed_terracotta[facing_direction=2]", "234:3": "minecraft:red_glazed_terracotta[facing_direction=3]", "234:4": "minecraft:red_glazed_terracotta[facing_direction=4]", "234:5": "minecraft:red_glazed_terracotta[facing_direction=5]", "235:0": "minecraft:black_glazed_terracotta[facing_direction=0]", "235:1": "minecraft:black_glazed_terracotta[facing_direction=1]", "235:2": "minecraft:black_glazed_terracotta[facing_direction=2]", "235:3": "minecraft:black_glazed_terracotta[facing_direction=3]", "235:4": "minecraft:black_glazed_terracotta[facing_direction=4]", "235:5": "minecraft:black_glazed_terracotta[facing_direction=5]", "236:0": "minecraft:concrete[color=white]", "236:1": "minecraft:concrete[color=orange]", "236:2": "minecraft:concrete[color=magenta]", "236:3": "minecraft:concrete[color=light_blue]", "236:4": "minecraft:concrete[color=yellow]", "236:5": "minecraft:concrete[color=lime]", "236:6": "minecraft:concrete[color=pink]", "236:7": "minecraft:concrete[color=gray]", "236:8": "minecraft:concrete[color=silver]", "236:9": "minecraft:concrete[color=cyan]", "236:10": "minecraft:concrete[color=purple]", "236:11": "minecraft:concrete[color=blue]", "236:12": "minecraft:concrete[color=brown]", "236:13": "minecraft:concrete[color=green]", "236:14": "minecraft:concrete[color=red]", "236:15": "minecraft:concrete[color=black]", "237:0": "minecraft:concretePowder[color=white]", "237:1": "minecraft:concretePowder[color=orange]", "237:2": "minecraft:concretePowder[color=magenta]", "237:3": "minecraft:concretePowder[color=light_blue]", "237:4": "minecraft:concretePowder[color=yellow]", "237:5": "minecraft:concretePowder[color=lime]", "237:6": "minecraft:concretePowder[color=pink]", "237:7": "minecraft:concretePowder[color=gray]", "237:8": "minecraft:concretePowder[color=silver]", "237:9": "minecraft:concretePowder[color=cyan]", "237:10": "minecraft:concretePowder[color=purple]", "237:11": "minecraft:concretePowder[color=blue]", "237:12": "minecraft:concretePowder[color=brown]", "237:13": "minecraft:concretePowder[color=green]", "237:14": "minecraft:concretePowder[color=red]", "237:15": "minecraft:concretePowder[color=black]", "238:0": "minecraft:chemistry_table[chemistry_table_type=compound_creator,direction=0]", "238:1": "minecraft:chemistry_table[chemistry_table_type=compound_creator,direction=1]", "238:2": "minecraft:chemistry_table[chemistry_table_type=compound_creator,direction=2]", "238:3": "minecraft:chemistry_table[chemistry_table_type=compound_creator,direction=3]", "238:4": "minecraft:chemistry_table[chemistry_table_type=material_reducer,direction=0]", "238:5": "minecraft:chemistry_table[chemistry_table_type=material_reducer,direction=1]", "238:6": "minecraft:chemistry_table[chemistry_table_type=material_reducer,direction=2]", "238:7": "minecraft:chemistry_table[chemistry_table_type=material_reducer,direction=3]", "238:8": "minecraft:chemistry_table[chemistry_table_type=element_constructor,direction=0]", "238:9": "minecraft:chemistry_table[chemistry_table_type=element_constructor,direction=1]", "238:10": "minecraft:chemistry_table[chemistry_table_type=element_constructor,direction=2]", "238:11": "minecraft:chemistry_table[chemistry_table_type=element_constructor,direction=3]", "238:12": "minecraft:chemistry_table[chemistry_table_type=lab_table,direction=0]", "238:13": "minecraft:chemistry_table[chemistry_table_type=lab_table,direction=1]", "238:14": "minecraft:chemistry_table[chemistry_table_type=lab_table,direction=2]", "238:15": "minecraft:chemistry_table[chemistry_table_type=lab_table,direction=3]", "239:0": "minecraft:underwater_torch[torch_facing_direction=unknown]", "239:1": "minecraft:underwater_torch[torch_facing_direction=west]", "239:2": "minecraft:underwater_torch[torch_facing_direction=east]", "239:3": "minecraft:underwater_torch[torch_facing_direction=north]", "239:4": "minecraft:underwater_torch[torch_facing_direction=south]", "239:5": "minecraft:underwater_torch[torch_facing_direction=top]", "240:0": "minecraft:chorus_plant", "241:0": "minecraft:stained_glass[color=white]", "241:1": "minecraft:stained_glass[color=orange]", "241:2": "minecraft:stained_glass[color=magenta]", "241:3": "minecraft:stained_glass[color=light_blue]", "241:4": "minecraft:stained_glass[color=yellow]", "241:5": "minecraft:stained_glass[color=lime]", "241:6": "minecraft:stained_glass[color=pink]", "241:7": "minecraft:stained_glass[color=gray]", "241:8": "minecraft:stained_glass[color=silver]", "241:9": "minecraft:stained_glass[color=cyan]", "241:10": "minecraft:stained_glass[color=purple]", "241:11": "minecraft:stained_glass[color=blue]", "241:12": "minecraft:stained_glass[color=brown]", "241:13": "minecraft:stained_glass[color=green]", "241:14": "minecraft:stained_glass[color=red]", "241:15": "minecraft:stained_glass[color=black]", "242:0": "minecraft:camera", "243:0": "minecraft:podzol", "244:0": "minecraft:beetroot[growth=0]", "244:1": "minecraft:beetroot[growth=1]", "244:2": "minecraft:beetroot[growth=2]", "244:3": "minecraft:beetroot[growth=3]", "244:4": "minecraft:beetroot[growth=4]", "244:5": "minecraft:beetroot[growth=5]", "244:6": "minecraft:beetroot[growth=6]", "244:7": "minecraft:beetroot[growth=7]", "245:0": "minecraft:stonecutter", "246:0": "minecraft:glowingobsidian", "247:0": "minecraft:netherreactor", "248:0": "minecraft:info_update", "249:0": "minecraft:info_update2", "250:0": "minecraft:movingBlock", "251:0": "minecraft:observer[facing_direction=0,powered_bit=0]", "251:1": "minecraft:observer[facing_direction=1,powered_bit=0]", "251:2": "minecraft:observer[facing_direction=2,powered_bit=0]", "251:3": "minecraft:observer[facing_direction=3,powered_bit=0]", "251:4": "minecraft:observer[facing_direction=4,powered_bit=0]", "251:5": "minecraft:observer[facing_direction=5,powered_bit=0]", "251:6": "minecraft:observer[facing_direction=0,powered_bit=1]", "251:7": "minecraft:observer[facing_direction=1,powered_bit=1]", "251:8": "minecraft:observer[facing_direction=2,powered_bit=1]", "251:9": "minecraft:observer[facing_direction=3,powered_bit=1]", "251:10": "minecraft:observer[facing_direction=4,powered_bit=1]", "251:11": "minecraft:observer[facing_direction=5,powered_bit=1]", "252:0": "minecraft:structure_block[structure_block_type=data]", "252:1": "minecraft:structure_block[structure_block_type=save]", "252:2": "minecraft:structure_block[structure_block_type=load]", "252:3": "minecraft:structure_block[structure_block_type=corner]", "252:4": "minecraft:structure_block[structure_block_type=invalid]", "252:5": "minecraft:structure_block[structure_block_type=export]", "253:0": "minecraft:hard_glass", "254:0": "minecraft:hard_stained_glass[color=white]", "254:1": "minecraft:hard_stained_glass[color=orange]", "254:2": "minecraft:hard_stained_glass[color=magenta]", "254:3": "minecraft:hard_stained_glass[color=light_blue]", "254:4": "minecraft:hard_stained_glass[color=yellow]", "254:5": "minecraft:hard_stained_glass[color=lime]", "254:6": "minecraft:hard_stained_glass[color=pink]", "254:7": "minecraft:hard_stained_glass[color=gray]", "254:8": "minecraft:hard_stained_glass[color=silver]", "254:9": "minecraft:hard_stained_glass[color=cyan]", "254:10": "minecraft:hard_stained_glass[color=purple]", "254:11": "minecraft:hard_stained_glass[color=blue]", "254:12": "minecraft:hard_stained_glass[color=brown]", "254:13": "minecraft:hard_stained_glass[color=green]", "254:14": "minecraft:hard_stained_glass[color=red]", "254:15": "minecraft:hard_stained_glass[color=black]", "255:0": "minecraft:reserved6"}, "items": {}}