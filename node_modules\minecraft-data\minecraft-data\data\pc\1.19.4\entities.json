[{"id": 0, "internalId": 0, "name": "allay", "displayName": "Allay", "width": 0.35, "height": 0.6, "type": "mob", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "dancing", "can_duplicate"]}, {"id": 1, "internalId": 1, "name": "area_effect_cloud", "displayName": "Area Effect Cloud", "width": 6, "height": 0.5, "type": "other", "category": "UNKNOWN", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "radius", "color", "waiting", "particle"]}, {"id": 2, "internalId": 2, "name": "armor_stand", "displayName": "Armor Stand", "width": 0.5, "height": 1.975, "type": "living", "category": "Immobile", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "client_flags", "head_pose", "body_pose", "left_arm_pose", "right_arm_pose", "left_leg_pose", "right_leg_pose"]}, {"id": 3, "internalId": 3, "name": "arrow", "displayName": "Arrow", "width": 0.5, "height": 0.5, "type": "projectile", "category": "Projectiles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "flags", "pierce_level", "effect_color"]}, {"id": 4, "internalId": 4, "name": "axolotl", "displayName": "Axolotl", "width": 0.75, "height": 0.42, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "variant", "playing_dead", "from_bucket"]}, {"id": 5, "internalId": 5, "name": "bat", "displayName": "Bat", "width": 0.5, "height": 0.9, "type": "ambient", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "flags"]}, {"id": 6, "internalId": 6, "name": "bee", "displayName": "Bee", "width": 0.7, "height": 0.6, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "flags", "remaining_anger_time"]}, {"id": 7, "internalId": 7, "name": "blaze", "displayName": "Blaze", "width": 0.6, "height": 1.8, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "flags"]}, {"id": 8, "internalId": 8, "name": "block_display", "displayName": "Block Display", "width": 0, "height": 0, "type": "other", "category": "Immobile", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "interpolation_start_delta_ticks", "interpolation_duration", "translation", "scale", "left_rotation", "right_rotation", "billboard_render_constraints", "brightness_override", "view_range", "shadow_radius", "shadow_strength", "width", "height", "glow_color_override", "block_state"]}, {"id": 9, "internalId": 9, "name": "boat", "displayName": "Boat", "width": 1.375, "height": 0.5625, "type": "other", "category": "Vehicles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "hurt", "hurtdir", "damage", "type", "paddle_left", "paddle_right", "bubble_time"]}, {"id": 10, "internalId": 10, "name": "camel", "displayName": "Camel", "width": 1.7, "height": 2.375, "type": "player", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "flags", "dash", "last_pose_change_tick"]}, {"id": 11, "internalId": 11, "name": "cat", "displayName": "Cat", "width": 0.6, "height": 0.7, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "flags", "owner<PERSON><PERSON>", "variant", "is_lying", "relax_state_one", "collar_color"]}, {"id": 12, "internalId": 12, "name": "cave_spider", "displayName": "<PERSON> Spider", "width": 0.7, "height": 0.5, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "flags"]}, {"id": 13, "internalId": 13, "name": "chest_boat", "displayName": "Boat with Chest", "width": 1.375, "height": 0.5625, "type": "other", "category": "Vehicles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "hurt", "hurtdir", "damage", "type", "paddle_left", "paddle_right", "bubble_time"]}, {"id": 14, "internalId": 14, "name": "chest_minecart", "displayName": "Minecart with Chest", "width": 0.98, "height": 0.7, "type": "other", "category": "Vehicles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "hurt", "hurtdir", "damage", "display_block", "display_offset", "custom_display"]}, {"id": 15, "internalId": 15, "name": "chicken", "displayName": "Chicken", "width": 0.4, "height": 0.7, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby"]}, {"id": 16, "internalId": 16, "name": "cod", "displayName": "Cod", "width": 0.5, "height": 0.3, "type": "water_creature", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "from_bucket"]}, {"id": 17, "internalId": 17, "name": "command_block_minecart", "displayName": "Minecart with Command Block", "width": 0.98, "height": 0.7, "type": "other", "category": "Vehicles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "hurt", "hurtdir", "damage", "display_block", "display_offset", "custom_display", "command_name", "last_output"]}, {"id": 18, "internalId": 18, "name": "cow", "displayName": "Cow", "width": 0.9, "height": 1.4, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby"]}, {"id": 19, "internalId": 19, "name": "creeper", "displayName": "C<PERSON>per", "width": 0.6, "height": 1.7, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "swell_dir", "is_powered", "is_ignited"]}, {"id": 20, "internalId": 20, "name": "dolphin", "displayName": "Dolphin", "width": 0.9, "height": 0.6, "type": "water_creature", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "treasure_pos", "got_fish", "moistness_level"]}, {"id": 21, "internalId": 21, "name": "donkey", "displayName": "<PERSON><PERSON>", "width": 1.3964844, "height": 1.5, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "flags", "chest"]}, {"id": 22, "internalId": 22, "name": "dragon_fireball", "displayName": "Dragon Fireball", "width": 1, "height": 1, "type": "projectile", "category": "Projectiles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen"]}, {"id": 23, "internalId": 23, "name": "drowned", "displayName": "Drowned", "width": 0.6, "height": 1.95, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "special_type", "drowned_conversion"]}, {"id": 24, "internalId": 24, "name": "egg", "displayName": "Thrown Egg", "width": 0.25, "height": 0.25, "type": "projectile", "category": "Projectiles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "item_stack"]}, {"id": 25, "internalId": 25, "name": "elder_guardian", "displayName": "Elder Guardian", "width": 1.9975, "height": 1.9975, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "moving", "attack_target"]}, {"id": 26, "internalId": 26, "name": "end_crystal", "displayName": "End Crystal", "width": 2, "height": 2, "type": "other", "category": "Immobile", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "beam_target", "show_bottom"]}, {"id": 27, "internalId": 27, "name": "ender_dragon", "displayName": "<PERSON><PERSON>", "width": 16, "height": 8, "type": "mob", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "phase"]}, {"id": 28, "internalId": 28, "name": "ender_pearl", "displayName": "<PERSON><PERSON><PERSON> <PERSON>", "width": 0.25, "height": 0.25, "type": "projectile", "category": "Projectiles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "item_stack"]}, {"id": 29, "internalId": 29, "name": "enderman", "displayName": "<PERSON><PERSON>", "width": 0.6, "height": 2.9, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "carry_state", "creepy", "stared_at"]}, {"id": 30, "internalId": 30, "name": "endermite", "displayName": "Endermite", "width": 0.4, "height": 0.3, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags"]}, {"id": 31, "internalId": 31, "name": "evoker", "displayName": "Evoker", "width": 0.6, "height": 1.95, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "is_celebrating", "spell_casting"]}, {"id": 32, "internalId": 32, "name": "evoker_fangs", "displayName": "Evoker <PERSON>s", "width": 0.5, "height": 0.8, "type": "other", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen"]}, {"id": 33, "internalId": 33, "name": "experience_bottle", "displayName": "T<PERSON><PERSON> Bottle o' Enchanting", "width": 0.25, "height": 0.25, "type": "projectile", "category": "Projectiles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "item_stack"]}, {"id": 34, "internalId": 34, "name": "experience_orb", "displayName": "Experience Orb", "width": 0.5, "height": 0.5, "type": "other", "category": "UNKNOWN", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen"]}, {"id": 35, "internalId": 35, "name": "eye_of_ender", "displayName": "Eye of <PERSON>er", "width": 0.25, "height": 0.25, "type": "other", "category": "UNKNOWN", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "item_stack"]}, {"id": 36, "internalId": 36, "name": "falling_block", "displayName": "Falling Block", "width": 0.98, "height": 0.98, "type": "other", "category": "UNKNOWN", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "start_pos"]}, {"id": 37, "internalId": 37, "name": "firework_rocket", "displayName": "Firework Rocket", "width": 0.25, "height": 0.25, "type": "projectile", "category": "Projectiles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "fireworks_item", "attached_to_target", "shot_at_angle"]}, {"id": 38, "internalId": 38, "name": "fox", "displayName": "Fox", "width": 0.6, "height": 0.7, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "type", "flags", "trusted_0", "trusted_1"]}, {"id": 39, "internalId": 39, "name": "frog", "displayName": "<PERSON>", "width": 0.5, "height": 0.5, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "variant", "tongue_target"]}, {"id": 40, "internalId": 40, "name": "furnace_minecart", "displayName": "Minecart with Furnace", "width": 0.98, "height": 0.7, "type": "other", "category": "Vehicles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "hurt", "hurtdir", "damage", "display_block", "display_offset", "custom_display", "fuel"]}, {"id": 41, "internalId": 41, "name": "ghast", "displayName": "<PERSON><PERSON><PERSON>", "width": 4, "height": 4, "type": "mob", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "is_charging"]}, {"id": 42, "internalId": 42, "name": "giant", "displayName": "Giant", "width": 3.6, "height": 12, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags"]}, {"id": 43, "internalId": 43, "name": "glow_item_frame", "displayName": "G<PERSON> Item <PERSON>", "width": 0.5, "height": 0.5, "type": "other", "category": "Immobile", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "item", "rotation"]}, {"id": 44, "internalId": 44, "name": "glow_squid", "displayName": "Glow Squid", "width": 0.8, "height": 0.8, "type": "water_creature", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "dark_ticks_remaining"]}, {"id": 45, "internalId": 45, "name": "goat", "displayName": "Goa<PERSON>", "width": 0.9, "height": 1.3, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "is_screaming_goat", "has_left_horn", "has_right_horn"]}, {"id": 46, "internalId": 46, "name": "guardian", "displayName": "Guardian", "width": 0.85, "height": 0.85, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "moving", "attack_target"]}, {"id": 47, "internalId": 47, "name": "hoglin", "displayName": "<PERSON><PERSON><PERSON>", "width": 1.3964844, "height": 1.4, "type": "animal", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "immune_to_zombification"]}, {"id": 48, "internalId": 48, "name": "hopper_minecart", "displayName": "Minecart with <PERSON>", "width": 0.98, "height": 0.7, "type": "other", "category": "Vehicles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "hurt", "hurtdir", "damage", "display_block", "display_offset", "custom_display"]}, {"id": 49, "internalId": 49, "name": "horse", "displayName": "Horse", "width": 1.3964844, "height": 1.6, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "flags", "type_variant"]}, {"id": 50, "internalId": 50, "name": "husk", "displayName": "Husk", "width": 0.6, "height": 1.95, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "special_type", "drowned_conversion"]}, {"id": 51, "internalId": 51, "name": "illusioner", "displayName": "<PERSON><PERSON><PERSON>", "width": 0.6, "height": 1.95, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "is_celebrating", "spell_casting"]}, {"id": 52, "internalId": 52, "name": "interaction", "displayName": "Interaction", "width": 0, "height": 0, "type": "other", "category": "Immobile", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "width", "height", "response"]}, {"id": 53, "internalId": 53, "name": "iron_golem", "displayName": "Iron Golem", "width": 1.4, "height": 2.7, "type": "mob", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "flags"]}, {"id": 54, "internalId": 54, "name": "item", "displayName": "<PERSON><PERSON>", "width": 0.25, "height": 0.25, "type": "other", "category": "UNKNOWN", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "item"]}, {"id": 55, "internalId": 55, "name": "item_display", "displayName": "<PERSON><PERSON>", "width": 0, "height": 0, "type": "other", "category": "Immobile", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "interpolation_start_delta_ticks", "interpolation_duration", "translation", "scale", "left_rotation", "right_rotation", "billboard_render_constraints", "brightness_override", "view_range", "shadow_radius", "shadow_strength", "width", "height", "glow_color_override", "item_stack", "item_display"]}, {"id": 56, "internalId": 56, "name": "item_frame", "displayName": "<PERSON><PERSON>", "width": 0.5, "height": 0.5, "type": "other", "category": "Immobile", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "item", "rotation"]}, {"id": 57, "internalId": 57, "name": "fireball", "displayName": "Fireball", "width": 1, "height": 1, "type": "projectile", "category": "Projectiles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "item_stack"]}, {"id": 58, "internalId": 58, "name": "leash_knot", "displayName": "<PERSON><PERSON>", "width": 0.375, "height": 0.5, "type": "other", "category": "Immobile", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen"]}, {"id": 59, "internalId": 59, "name": "lightning_bolt", "displayName": "Lightning Bolt", "width": 0, "height": 0, "type": "other", "category": "UNKNOWN", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen"]}, {"id": 60, "internalId": 60, "name": "llama", "displayName": "Llama", "width": 0.9, "height": 1.87, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "flags", "chest", "strength", "swag", "variant"]}, {"id": 61, "internalId": 61, "name": "llama_spit", "displayName": "Llama <PERSON>", "width": 0.25, "height": 0.25, "type": "projectile", "category": "Projectiles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen"]}, {"id": 62, "internalId": 62, "name": "magma_cube", "displayName": "Magma Cube", "width": 2.04, "height": 2.04, "type": "mob", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "size"]}, {"id": 63, "internalId": 63, "name": "marker", "displayName": "<PERSON><PERSON>", "width": 0, "height": 0, "type": "other", "category": "UNKNOWN", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen"]}, {"id": 64, "internalId": 64, "name": "minecart", "displayName": "Minecart", "width": 0.98, "height": 0.7, "type": "other", "category": "Vehicles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "hurt", "hurtdir", "damage", "display_block", "display_offset", "custom_display"]}, {"id": 65, "internalId": 65, "name": "mooshroom", "displayName": "Mooshroom", "width": 0.9, "height": 1.4, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "type"]}, {"id": 66, "internalId": 66, "name": "mule", "displayName": "<PERSON><PERSON>", "width": 1.3964844, "height": 1.6, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "flags", "chest"]}, {"id": 67, "internalId": 67, "name": "ocelot", "displayName": "Ocelot", "width": 0.6, "height": 0.7, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "trusting"]}, {"id": 68, "internalId": 68, "name": "painting", "displayName": "Painting", "width": 0.5, "height": 0.5, "type": "other", "category": "Immobile", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "painting_variant"]}, {"id": 69, "internalId": 69, "name": "panda", "displayName": "Panda", "width": 1.3, "height": 1.25, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "unhappy_counter", "sneeze_counter", "eat_counter", "main_gene", "hidden_gene", "flags"]}, {"id": 70, "internalId": 70, "name": "parrot", "displayName": "<PERSON><PERSON><PERSON>", "width": 0.5, "height": 0.9, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "flags", "owner<PERSON><PERSON>", "variant"]}, {"id": 71, "internalId": 71, "name": "phantom", "displayName": "Phantom", "width": 0.9, "height": 0.5, "type": "mob", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "size"]}, {"id": 72, "internalId": 72, "name": "pig", "displayName": "Pig", "width": 0.9, "height": 0.9, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "saddle", "boost_time"]}, {"id": 73, "internalId": 73, "name": "piglin", "displayName": "<PERSON><PERSON>", "width": 0.6, "height": 1.95, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "immune_to_zombification", "baby", "is_charging_crossbow", "is_dancing"]}, {"id": 74, "internalId": 74, "name": "piglin_brute", "displayName": "<PERSON><PERSON> B<PERSON>", "width": 0.6, "height": 1.95, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "immune_to_zombification"]}, {"id": 75, "internalId": 75, "name": "pillager", "displayName": "Pillager", "width": 0.6, "height": 1.95, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "is_celebrating", "is_charging_crossbow"]}, {"id": 76, "internalId": 76, "name": "polar_bear", "displayName": "Polar Bear", "width": 1.4, "height": 1.4, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "standing"]}, {"id": 77, "internalId": 77, "name": "potion", "displayName": "Potion", "width": 0.25, "height": 0.25, "type": "projectile", "category": "Projectiles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "item_stack"]}, {"id": 78, "internalId": 78, "name": "pufferfish", "displayName": "Pufferfish", "width": 0.7, "height": 0.7, "type": "water_creature", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "from_bucket", "puff_state"]}, {"id": 79, "internalId": 79, "name": "rabbit", "displayName": "Rabbit", "width": 0.4, "height": 0.5, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "type"]}, {"id": 80, "internalId": 80, "name": "ravager", "displayName": "<PERSON><PERSON><PERSON>", "width": 1.95, "height": 2.2, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "is_celebrating"]}, {"id": 81, "internalId": 81, "name": "salmon", "displayName": "Salmon", "width": 0.7, "height": 0.4, "type": "water_creature", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "from_bucket"]}, {"id": 82, "internalId": 82, "name": "sheep", "displayName": "Sheep", "width": 0.9, "height": 1.3, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "wool"]}, {"id": 83, "internalId": 83, "name": "s<PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "width": 1, "height": 1, "type": "mob", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "attach_face", "peek", "color"]}, {"id": 84, "internalId": 84, "name": "shulker_bullet", "displayName": "<PERSON><PERSON><PERSON> Bullet", "width": 0.3125, "height": 0.3125, "type": "projectile", "category": "Projectiles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen"]}, {"id": 85, "internalId": 85, "name": "silverfish", "displayName": "Silverfish", "width": 0.4, "height": 0.3, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags"]}, {"id": 86, "internalId": 86, "name": "skeleton", "displayName": "Skeleton", "width": 0.6, "height": 1.99, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "stray_conversion"]}, {"id": 87, "internalId": 87, "name": "skeleton_horse", "displayName": "Skeleton Horse", "width": 1.3964844, "height": 1.6, "type": "animal", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "flags"]}, {"id": 88, "internalId": 88, "name": "slime", "displayName": "Slime", "width": 2.04, "height": 2.04, "type": "mob", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "size"]}, {"id": 89, "internalId": 89, "name": "small_fireball", "displayName": "Small Fireball", "width": 0.3125, "height": 0.3125, "type": "projectile", "category": "Projectiles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "item_stack"]}, {"id": 90, "internalId": 90, "name": "sniffer", "displayName": "<PERSON><PERSON><PERSON>", "width": 1.9, "height": 1.75, "type": "player", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "state", "drop_seed_at_tick"]}, {"id": 91, "internalId": 91, "name": "snow_golem", "displayName": "Snow Golem", "width": 0.7, "height": 1.9, "type": "mob", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "pumpkin"]}, {"id": 92, "internalId": 92, "name": "snowball", "displayName": "Snowball", "width": 0.25, "height": 0.25, "type": "projectile", "category": "Projectiles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "item_stack"]}, {"id": 93, "internalId": 93, "name": "spawner_minecart", "displayName": "<PERSON><PERSON><PERSON> with <PERSON> Spawner", "width": 0.98, "height": 0.7, "type": "other", "category": "Vehicles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "hurt", "hurtdir", "damage", "display_block", "display_offset", "custom_display"]}, {"id": 94, "internalId": 94, "name": "spectral_arrow", "displayName": "Spectral Arrow", "width": 0.5, "height": 0.5, "type": "projectile", "category": "Projectiles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "flags", "pierce_level"]}, {"id": 95, "internalId": 95, "name": "spider", "displayName": "Spider", "width": 1.4, "height": 0.9, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "flags"]}, {"id": 96, "internalId": 96, "name": "squid", "displayName": "Squid", "width": 0.8, "height": 0.8, "type": "water_creature", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags"]}, {"id": 97, "internalId": 97, "name": "stray", "displayName": "Stray", "width": 0.6, "height": 1.99, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags"]}, {"id": 98, "internalId": 98, "name": "strider", "displayName": "Strider", "width": 0.9, "height": 1.7, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "boost_time", "suffocating", "saddle"]}, {"id": 99, "internalId": 99, "name": "tadpole", "displayName": "Tadpole", "width": 0.4, "height": 0.3, "type": "water_creature", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "from_bucket"]}, {"id": 100, "internalId": 100, "name": "text_display", "displayName": "Text Display", "width": 0, "height": 0, "type": "other", "category": "Immobile", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "interpolation_start_delta_ticks", "interpolation_duration", "translation", "scale", "left_rotation", "right_rotation", "billboard_render_constraints", "brightness_override", "view_range", "shadow_radius", "shadow_strength", "width", "height", "glow_color_override", "text", "line_width", "background_color", "text_opacity", "style_flags"]}, {"id": 101, "internalId": 101, "name": "tnt", "displayName": "Primed TNT", "width": 0.98, "height": 0.98, "type": "other", "category": "UNKNOWN", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "fuse"]}, {"id": 102, "internalId": 102, "name": "tnt_minecart", "displayName": "Minecart with TNT", "width": 0.98, "height": 0.7, "type": "other", "category": "Vehicles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "hurt", "hurtdir", "damage", "display_block", "display_offset", "custom_display"]}, {"id": 103, "internalId": 103, "name": "trader_llama", "displayName": "Trader <PERSON><PERSON><PERSON>", "width": 0.9, "height": 1.87, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "flags", "chest", "strength", "swag", "variant"]}, {"id": 104, "internalId": 104, "name": "trident", "displayName": "Trident", "width": 0.5, "height": 0.5, "type": "projectile", "category": "Projectiles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "flags", "pierce_level", "loyalty", "foil"]}, {"id": 105, "internalId": 105, "name": "tropical_fish", "displayName": "Tropical Fish", "width": 0.5, "height": 0.4, "type": "water_creature", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "from_bucket", "type_variant"]}, {"id": 106, "internalId": 106, "name": "turtle", "displayName": "Turtle", "width": 1.2, "height": 0.4, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "home_pos", "has_egg", "laying_egg", "travel_pos", "going_home", "travelling"]}, {"id": 107, "internalId": 107, "name": "vex", "displayName": "Vex", "width": 0.4, "height": 0.8, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "flags"]}, {"id": 108, "internalId": 108, "name": "villager", "displayName": "Villager", "width": 0.6, "height": 1.95, "type": "passive", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "unhappy_counter", "villager_data"]}, {"id": 109, "internalId": 109, "name": "vindicator", "displayName": "Vindicator", "width": 0.6, "height": 1.95, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "is_celebrating"]}, {"id": 110, "internalId": 110, "name": "wandering_trader", "displayName": "Wandering Trader", "width": 0.6, "height": 1.95, "type": "passive", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "unhappy_counter"]}, {"id": 111, "internalId": 111, "name": "warden", "displayName": "Warden", "width": 0.9, "height": 2.9, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "client_anger_level"]}, {"id": 112, "internalId": 112, "name": "witch", "displayName": "Witch", "width": 0.6, "height": 1.95, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "is_celebrating", "using_item"]}, {"id": 113, "internalId": 113, "name": "wither", "displayName": "<PERSON>er", "width": 0.9, "height": 3.5, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "target_a", "target_b", "target_c", "inv"]}, {"id": 114, "internalId": 114, "name": "wither_skeleton", "displayName": "<PERSON><PERSON>", "width": 0.7, "height": 2.4, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags"]}, {"id": 115, "internalId": 115, "name": "wither_skull", "displayName": "<PERSON><PERSON>", "width": 0.3125, "height": 0.3125, "type": "projectile", "category": "Projectiles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "dangerous"]}, {"id": 116, "internalId": 116, "name": "wolf", "displayName": "<PERSON>", "width": 0.6, "height": 0.85, "type": "animal", "category": "Passive mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "flags", "owner<PERSON><PERSON>", "interested", "collar_color", "remaining_anger_time"]}, {"id": 117, "internalId": 117, "name": "<PERSON>oglin", "displayName": "<PERSON><PERSON><PERSON>", "width": 1.3964844, "height": 1.4, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby"]}, {"id": 118, "internalId": 118, "name": "zombie", "displayName": "Zombie", "width": 0.6, "height": 1.95, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "special_type", "drowned_conversion"]}, {"id": 119, "internalId": 119, "name": "zombie_horse", "displayName": "Zombie Horse", "width": 1.3964844, "height": 1.6, "type": "animal", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "flags"]}, {"id": 120, "internalId": 120, "name": "zombie_villager", "displayName": "Zombie Villager", "width": 0.6, "height": 1.95, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "special_type", "drowned_conversion", "converting", "villager_data"]}, {"id": 121, "internalId": 121, "name": "zombified_piglin", "displayName": "Zombified Piglin", "width": 0.6, "height": 1.95, "type": "hostile", "category": "Hostile mobs", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "mob_flags", "baby", "special_type", "drowned_conversion"]}, {"id": 122, "internalId": 122, "name": "player", "displayName": "Player", "width": 0.6, "height": 1.8, "type": "player", "category": "UNKNOWN", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "living_entity_flags", "health", "effect_color", "effect_ambience", "arrow_count", "stinger_count", "sleeping_pos", "player_absorption", "score", "player_mode_customisation", "player_main_hand", "shoulder_left", "shoulder_right"]}, {"id": 123, "internalId": 123, "name": "fishing_bobber", "displayName": "Fishing Bobber", "width": 0.25, "height": 0.25, "type": "projectile", "category": "Projectiles", "metadataKeys": ["shared_flags", "air_supply", "custom_name", "custom_name_visible", "silent", "no_gravity", "pose", "ticks_frozen", "hooked_entity", "biting"]}]