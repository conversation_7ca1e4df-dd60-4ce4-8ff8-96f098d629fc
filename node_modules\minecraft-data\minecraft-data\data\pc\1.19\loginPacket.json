{"entityId": 227, "isHardcore": false, "gameMode": 0, "previousGameMode": -1, "worldNames": ["minecraft:overworld", "minecraft:the_nether", "minecraft:the_end"], "dimensionCodec": {"type": "compound", "name": "", "value": {"minecraft:chat_type": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:chat_type"}, "value": {"type": "list", "value": {"type": "compound", "value": [{"name": {"type": "string", "value": "minecraft:chat"}, "id": {"type": "int", "value": 0}, "element": {"type": "compound", "value": {"chat": {"type": "compound", "value": {"decoration": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "chat.type.text"}, "style": {"type": "compound", "value": {}}, "parameters": {"type": "list", "value": {"type": "string", "value": ["sender", "content"]}}}}}}, "narration": {"type": "compound", "value": {"priority": {"type": "string", "value": "chat"}, "decoration": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "chat.type.text.narrate"}, "style": {"type": "compound", "value": {}}, "parameters": {"type": "list", "value": {"type": "string", "value": ["sender", "content"]}}}}}}}}}, {"name": {"type": "string", "value": "minecraft:system"}, "id": {"type": "int", "value": 1}, "element": {"type": "compound", "value": {"chat": {"type": "compound", "value": {}}, "narration": {"type": "compound", "value": {"priority": {"type": "string", "value": "system"}}}}}}, {"name": {"type": "string", "value": "minecraft:game_info"}, "id": {"type": "int", "value": 2}, "element": {"type": "compound", "value": {"overlay": {"type": "compound", "value": {}}}}}, {"name": {"type": "string", "value": "minecraft:say_command"}, "id": {"type": "int", "value": 3}, "element": {"type": "compound", "value": {"chat": {"type": "compound", "value": {"decoration": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "chat.type.announcement"}, "style": {"type": "compound", "value": {}}, "parameters": {"type": "list", "value": {"type": "string", "value": ["sender", "content"]}}}}}}, "narration": {"type": "compound", "value": {"priority": {"type": "string", "value": "chat"}, "decoration": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "chat.type.text.narrate"}, "style": {"type": "compound", "value": {}}, "parameters": {"type": "list", "value": {"type": "string", "value": ["sender", "content"]}}}}}}}}}, {"name": {"type": "string", "value": "minecraft:msg_command"}, "id": {"type": "int", "value": 4}, "element": {"type": "compound", "value": {"chat": {"type": "compound", "value": {"decoration": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "commands.message.display.incoming"}, "style": {"type": "compound", "value": {"color": {"type": "string", "value": "gray"}, "italic": {"type": "byte", "value": 1}}}, "parameters": {"type": "list", "value": {"type": "string", "value": ["sender", "content"]}}}}}}, "narration": {"type": "compound", "value": {"priority": {"type": "string", "value": "chat"}, "decoration": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "chat.type.text.narrate"}, "style": {"type": "compound", "value": {}}, "parameters": {"type": "list", "value": {"type": "string", "value": ["sender", "content"]}}}}}}}}}, {"name": {"type": "string", "value": "minecraft:team_msg_command"}, "id": {"type": "int", "value": 5}, "element": {"type": "compound", "value": {"chat": {"type": "compound", "value": {"decoration": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "chat.type.team.text"}, "style": {"type": "compound", "value": {}}, "parameters": {"type": "list", "value": {"type": "string", "value": ["team_name", "sender", "content"]}}}}}}, "narration": {"type": "compound", "value": {"priority": {"type": "string", "value": "chat"}, "decoration": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "chat.type.text.narrate"}, "style": {"type": "compound", "value": {}}, "parameters": {"type": "list", "value": {"type": "string", "value": ["sender", "content"]}}}}}}}}}, {"name": {"type": "string", "value": "minecraft:emote_command"}, "id": {"type": "int", "value": 6}, "element": {"type": "compound", "value": {"chat": {"type": "compound", "value": {"decoration": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "chat.type.emote"}, "style": {"type": "compound", "value": {}}, "parameters": {"type": "list", "value": {"type": "string", "value": ["sender", "content"]}}}}}}, "narration": {"type": "compound", "value": {"priority": {"type": "string", "value": "chat"}, "decoration": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "chat.type.emote"}, "style": {"type": "compound", "value": {}}, "parameters": {"type": "list", "value": {"type": "string", "value": ["sender", "content"]}}}}}}}}}, {"name": {"type": "string", "value": "minecraft:tellraw_command"}, "id": {"type": "int", "value": 7}, "element": {"type": "compound", "value": {"chat": {"type": "compound", "value": {}}, "narration": {"type": "compound", "value": {"priority": {"type": "string", "value": "chat"}}}}}}]}}}}, "minecraft:dimension_type": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:dimension_type"}, "value": {"type": "list", "value": {"type": "compound", "value": [{"name": {"type": "string", "value": "minecraft:overworld"}, "id": {"type": "int", "value": 0}, "element": {"type": "compound", "value": {"piglin_safe": {"type": "byte", "value": 0}, "natural": {"type": "byte", "value": 1}, "ambient_light": {"type": "float", "value": 0}, "monster_spawn_block_light_limit": {"type": "int", "value": 0}, "infiniburn": {"type": "string", "value": "#minecraft:infiniburn_overworld"}, "respawn_anchor_works": {"type": "byte", "value": 0}, "has_skylight": {"type": "byte", "value": 1}, "bed_works": {"type": "byte", "value": 1}, "effects": {"type": "string", "value": "minecraft:overworld"}, "has_raids": {"type": "byte", "value": 1}, "logical_height": {"type": "int", "value": 384}, "coordinate_scale": {"type": "double", "value": 1}, "monster_spawn_light_level": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:uniform"}, "value": {"type": "compound", "value": {"min_inclusive": {"type": "int", "value": 0}, "max_inclusive": {"type": "int", "value": 7}}}}}, "min_y": {"type": "int", "value": -64}, "ultrawarm": {"type": "byte", "value": 0}, "has_ceiling": {"type": "byte", "value": 0}, "height": {"type": "int", "value": 384}}}}, {"name": {"type": "string", "value": "minecraft:the_nether"}, "id": {"type": "int", "value": 1}, "element": {"type": "compound", "value": {"piglin_safe": {"type": "byte", "value": 1}, "natural": {"type": "byte", "value": 0}, "ambient_light": {"type": "float", "value": 0.10000000149011612}, "monster_spawn_block_light_limit": {"type": "int", "value": 15}, "infiniburn": {"type": "string", "value": "#minecraft:infiniburn_nether"}, "respawn_anchor_works": {"type": "byte", "value": 1}, "has_skylight": {"type": "byte", "value": 0}, "bed_works": {"type": "byte", "value": 0}, "effects": {"type": "string", "value": "minecraft:the_nether"}, "fixed_time": {"type": "long", "value": [0, 18000]}, "has_raids": {"type": "byte", "value": 0}, "logical_height": {"type": "int", "value": 128}, "coordinate_scale": {"type": "double", "value": 8}, "monster_spawn_light_level": {"type": "int", "value": 11}, "min_y": {"type": "int", "value": 0}, "ultrawarm": {"type": "byte", "value": 1}, "has_ceiling": {"type": "byte", "value": 1}, "height": {"type": "int", "value": 256}}}}, {"name": {"type": "string", "value": "minecraft:the_end"}, "id": {"type": "int", "value": 2}, "element": {"type": "compound", "value": {"piglin_safe": {"type": "byte", "value": 0}, "natural": {"type": "byte", "value": 0}, "ambient_light": {"type": "float", "value": 0}, "monster_spawn_block_light_limit": {"type": "int", "value": 0}, "infiniburn": {"type": "string", "value": "#minecraft:infiniburn_end"}, "respawn_anchor_works": {"type": "byte", "value": 0}, "has_skylight": {"type": "byte", "value": 0}, "bed_works": {"type": "byte", "value": 0}, "effects": {"type": "string", "value": "minecraft:the_end"}, "fixed_time": {"type": "long", "value": [0, 6000]}, "has_raids": {"type": "byte", "value": 1}, "logical_height": {"type": "int", "value": 256}, "coordinate_scale": {"type": "double", "value": 1}, "monster_spawn_light_level": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:uniform"}, "value": {"type": "compound", "value": {"min_inclusive": {"type": "int", "value": 0}, "max_inclusive": {"type": "int", "value": 7}}}}}, "min_y": {"type": "int", "value": 0}, "ultrawarm": {"type": "byte", "value": 0}, "has_ceiling": {"type": "byte", "value": 0}, "height": {"type": "int", "value": 256}}}}, {"name": {"type": "string", "value": "minecraft:overworld_caves"}, "id": {"type": "int", "value": 3}, "element": {"type": "compound", "value": {"piglin_safe": {"type": "byte", "value": 0}, "natural": {"type": "byte", "value": 1}, "ambient_light": {"type": "float", "value": 0}, "monster_spawn_block_light_limit": {"type": "int", "value": 0}, "infiniburn": {"type": "string", "value": "#minecraft:infiniburn_overworld"}, "respawn_anchor_works": {"type": "byte", "value": 0}, "has_skylight": {"type": "byte", "value": 1}, "bed_works": {"type": "byte", "value": 1}, "effects": {"type": "string", "value": "minecraft:overworld"}, "has_raids": {"type": "byte", "value": 1}, "logical_height": {"type": "int", "value": 384}, "coordinate_scale": {"type": "double", "value": 1}, "monster_spawn_light_level": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:uniform"}, "value": {"type": "compound", "value": {"min_inclusive": {"type": "int", "value": 0}, "max_inclusive": {"type": "int", "value": 7}}}}}, "min_y": {"type": "int", "value": -64}, "ultrawarm": {"type": "byte", "value": 0}, "has_ceiling": {"type": "byte", "value": 1}, "height": {"type": "int", "value": 384}}}}]}}}}, "minecraft:worldgen/biome": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:worldgen/biome"}, "value": {"type": "list", "value": {"type": "compound", "value": [{"name": {"type": "string", "value": "minecraft:the_void"}, "id": {"type": "int", "value": 0}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "none"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:plains"}, "id": {"type": "int", "value": 1}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 7907327}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.800000011920929}, "downfall": {"type": "float", "value": 0.4000000059604645}}}}, {"name": {"type": "string", "value": "minecraft:sunflower_plains"}, "id": {"type": "int", "value": 2}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 7907327}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.800000011920929}, "downfall": {"type": "float", "value": 0.4000000059604645}}}}, {"name": {"type": "string", "value": "minecraft:snowy_plains"}, "id": {"type": "int", "value": 3}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "snow"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8364543}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:ice_spikes"}, "id": {"type": "int", "value": 4}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "snow"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8364543}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:desert"}, "id": {"type": "int", "value": 5}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "none"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 7254527}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:swamp"}, "id": {"type": "int", "value": 6}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"grass_color_modifier": {"type": "string", "value": "swamp"}, "music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.swamp"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7907327}, "foliage_color": {"type": "int", "value": 6975545}, "water_fog_color": {"type": "int", "value": 2302743}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 6388580}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.800000011920929}, "downfall": {"type": "float", "value": 0.8999999761581421}}}}, {"name": {"type": "string", "value": "minecraft:mangrove_swamp"}, "id": {"type": "int", "value": 7}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"grass_color_modifier": {"type": "string", "value": "swamp"}, "music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.swamp"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7907327}, "foliage_color": {"type": "int", "value": 9285927}, "water_fog_color": {"type": "int", "value": 5077600}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 3832426}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.800000011920929}, "downfall": {"type": "float", "value": 0.8999999761581421}}}}, {"name": {"type": "string", "value": "minecraft:forest"}, "id": {"type": "int", "value": 8}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.jungle_and_forest"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7972607}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.699999988079071}, "downfall": {"type": "float", "value": 0.800000011920929}}}}, {"name": {"type": "string", "value": "minecraft:flower_forest"}, "id": {"type": "int", "value": 9}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.jungle_and_forest"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7972607}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.699999988079071}, "downfall": {"type": "float", "value": 0.800000011920929}}}}, {"name": {"type": "string", "value": "minecraft:birch_forest"}, "id": {"type": "int", "value": 10}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.jungle_and_forest"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 8037887}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.6000000238418579}, "downfall": {"type": "float", "value": 0.6000000238418579}}}}, {"name": {"type": "string", "value": "minecraft:dark_forest"}, "id": {"type": "int", "value": 11}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"grass_color_modifier": {"type": "string", "value": "dark_forest"}, "music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.jungle_and_forest"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7972607}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.699999988079071}, "downfall": {"type": "float", "value": 0.800000011920929}}}}, {"name": {"type": "string", "value": "minecraft:old_growth_birch_forest"}, "id": {"type": "int", "value": 12}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.jungle_and_forest"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 8037887}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.6000000238418579}, "downfall": {"type": "float", "value": 0.6000000238418579}}}}, {"name": {"type": "string", "value": "minecraft:old_growth_pine_taiga"}, "id": {"type": "int", "value": 13}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.old_growth_taiga"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 8168447}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.30000001192092896}, "downfall": {"type": "float", "value": 0.800000011920929}}}}, {"name": {"type": "string", "value": "minecraft:old_growth_spruce_taiga"}, "id": {"type": "int", "value": 14}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.old_growth_taiga"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 8233983}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.25}, "downfall": {"type": "float", "value": 0.800000011920929}}}}, {"name": {"type": "string", "value": "minecraft:taiga"}, "id": {"type": "int", "value": 15}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8233983}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.25}, "downfall": {"type": "float", "value": 0.800000011920929}}}}, {"name": {"type": "string", "value": "minecraft:snowy_taiga"}, "id": {"type": "int", "value": 16}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "snow"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8625919}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4020182}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": -0.5}, "downfall": {"type": "float", "value": 0.4000000059604645}}}}, {"name": {"type": "string", "value": "minecraft:savanna"}, "id": {"type": "int", "value": 17}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "none"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 7254527}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:savanna_plateau"}, "id": {"type": "int", "value": 18}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "none"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 7254527}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:windswept_hills"}, "id": {"type": "int", "value": 19}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8233727}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.20000000298023224}, "downfall": {"type": "float", "value": 0.30000001192092896}}}}, {"name": {"type": "string", "value": "minecraft:windswept_gravelly_hills"}, "id": {"type": "int", "value": 20}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8233727}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.20000000298023224}, "downfall": {"type": "float", "value": 0.30000001192092896}}}}, {"name": {"type": "string", "value": "minecraft:windswept_forest"}, "id": {"type": "int", "value": 21}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8233727}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.20000000298023224}, "downfall": {"type": "float", "value": 0.30000001192092896}}}}, {"name": {"type": "string", "value": "minecraft:windswept_savanna"}, "id": {"type": "int", "value": 22}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "none"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 7254527}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:jungle"}, "id": {"type": "int", "value": 23}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.jungle_and_forest"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7842047}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.949999988079071}, "downfall": {"type": "float", "value": 0.8999999761581421}}}}, {"name": {"type": "string", "value": "minecraft:sparse_jungle"}, "id": {"type": "int", "value": 24}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.jungle_and_forest"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7842047}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.949999988079071}, "downfall": {"type": "float", "value": 0.800000011920929}}}}, {"name": {"type": "string", "value": "minecraft:bamboo_jungle"}, "id": {"type": "int", "value": 25}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.jungle_and_forest"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7842047}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.949999988079071}, "downfall": {"type": "float", "value": 0.8999999761581421}}}}, {"name": {"type": "string", "value": "minecraft:badlands"}, "id": {"type": "int", "value": 26}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "none"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 7254527}, "grass_color": {"type": "int", "value": 9470285}, "foliage_color": {"type": "int", "value": 10387789}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:eroded_badlands"}, "id": {"type": "int", "value": 27}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "none"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 7254527}, "grass_color": {"type": "int", "value": 9470285}, "foliage_color": {"type": "int", "value": 10387789}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:wooded_badlands"}, "id": {"type": "int", "value": 28}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "none"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 7254527}, "grass_color": {"type": "int", "value": 9470285}, "foliage_color": {"type": "int", "value": 10387789}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:meadow"}, "id": {"type": "int", "value": 29}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.meadow"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 937679}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.800000011920929}}}}, {"name": {"type": "string", "value": "minecraft:grove"}, "id": {"type": "int", "value": 30}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "snow"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.grove"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 8495359}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": -0.20000000298023224}, "downfall": {"type": "float", "value": 0.800000011920929}}}}, {"name": {"type": "string", "value": "minecraft:snowy_slopes"}, "id": {"type": "int", "value": 31}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "snow"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.snowy_slopes"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 8560639}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": -0.30000001192092896}, "downfall": {"type": "float", "value": 0.8999999761581421}}}}, {"name": {"type": "string", "value": "minecraft:frozen_peaks"}, "id": {"type": "int", "value": 32}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "snow"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.frozen_peaks"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 8756735}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": -0.699999988079071}, "downfall": {"type": "float", "value": 0.8999999761581421}}}}, {"name": {"type": "string", "value": "minecraft:jagged_peaks"}, "id": {"type": "int", "value": 33}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "snow"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.jagged_peaks"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 8756735}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": -0.699999988079071}, "downfall": {"type": "float", "value": 0.8999999761581421}}}}, {"name": {"type": "string", "value": "minecraft:stony_peaks"}, "id": {"type": "int", "value": 34}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.stony_peaks"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7776511}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 1}, "downfall": {"type": "float", "value": 0.30000001192092896}}}}, {"name": {"type": "string", "value": "minecraft:river"}, "id": {"type": "int", "value": 35}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:frozen_river"}, "id": {"type": "int", "value": 36}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "snow"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8364543}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 3750089}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:beach"}, "id": {"type": "int", "value": 37}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 7907327}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.800000011920929}, "downfall": {"type": "float", "value": 0.4000000059604645}}}}, {"name": {"type": "string", "value": "minecraft:snowy_beach"}, "id": {"type": "int", "value": 38}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "snow"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8364543}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4020182}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.05000000074505806}, "downfall": {"type": "float", "value": 0.30000001192092896}}}}, {"name": {"type": "string", "value": "minecraft:stony_shore"}, "id": {"type": "int", "value": 39}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8233727}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.20000000298023224}, "downfall": {"type": "float", "value": 0.30000001192092896}}}}, {"name": {"type": "string", "value": "minecraft:warm_ocean"}, "id": {"type": "int", "value": 40}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 270131}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4445678}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:lukewarm_ocean"}, "id": {"type": "int", "value": 41}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 267827}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4566514}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:deep_lukewarm_ocean"}, "id": {"type": "int", "value": 42}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 267827}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4566514}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:ocean"}, "id": {"type": "int", "value": 43}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:deep_ocean"}, "id": {"type": "int", "value": 44}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:cold_ocean"}, "id": {"type": "int", "value": 45}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4020182}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:deep_cold_ocean"}, "id": {"type": "int", "value": 46}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4020182}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:frozen_ocean"}, "id": {"type": "int", "value": 47}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "snow"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8364543}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 3750089}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0}, "downfall": {"type": "float", "value": 0.5}, "temperature_modifier": {"type": "string", "value": "frozen"}}}}, {"name": {"type": "string", "value": "minecraft:deep_frozen_ocean"}, "id": {"type": "int", "value": 48}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 3750089}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}, "temperature_modifier": {"type": "string", "value": "frozen"}}}}, {"name": {"type": "string", "value": "minecraft:mushroom_fields"}, "id": {"type": "int", "value": 49}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 7842047}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.8999999761581421}, "downfall": {"type": "float", "value": 1}}}}, {"name": {"type": "string", "value": "minecraft:dripstone_caves"}, "id": {"type": "int", "value": 50}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.dripstone_caves"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7907327}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.800000011920929}, "downfall": {"type": "float", "value": 0.4000000059604645}}}}, {"name": {"type": "string", "value": "minecraft:lush_caves"}, "id": {"type": "int", "value": 51}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.lush_caves"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:deep_dark"}, "id": {"type": "int", "value": 52}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "rain"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.deep_dark"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7907327}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.800000011920929}, "downfall": {"type": "float", "value": 0.4000000059604645}}}}, {"name": {"type": "string", "value": "minecraft:nether_wastes"}, "id": {"type": "int", "value": 53}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "none"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.nether.nether_wastes"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7254527}, "ambient_sound": {"type": "string", "value": "minecraft:ambient.nether_wastes.loop"}, "additions_sound": {"type": "compound", "value": {"sound": {"type": "string", "value": "minecraft:ambient.nether_wastes.additions"}, "tick_chance": {"type": "double", "value": 0.0111}}}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 3344392}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.nether_wastes.mood"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:warped_forest"}, "id": {"type": "int", "value": 54}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "none"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.nether.warped_forest"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7254527}, "ambient_sound": {"type": "string", "value": "minecraft:ambient.warped_forest.loop"}, "additions_sound": {"type": "compound", "value": {"sound": {"type": "string", "value": "minecraft:ambient.warped_forest.additions"}, "tick_chance": {"type": "double", "value": 0.0111}}}, "particle": {"type": "compound", "value": {"probability": {"type": "float", "value": 0.014279999770224094}, "options": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:warped_spore"}}}}}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 1705242}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.warped_forest.mood"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:crimson_forest"}, "id": {"type": "int", "value": 55}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "none"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.nether.crimson_forest"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7254527}, "ambient_sound": {"type": "string", "value": "minecraft:ambient.crimson_forest.loop"}, "additions_sound": {"type": "compound", "value": {"sound": {"type": "string", "value": "minecraft:ambient.crimson_forest.additions"}, "tick_chance": {"type": "double", "value": 0.0111}}}, "particle": {"type": "compound", "value": {"probability": {"type": "float", "value": 0.02500000037252903}, "options": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:crimson_spore"}}}}}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 3343107}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.crimson_forest.mood"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:soul_sand_valley"}, "id": {"type": "int", "value": 56}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "none"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.nether.soul_sand_valley"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7254527}, "ambient_sound": {"type": "string", "value": "minecraft:ambient.soul_sand_valley.loop"}, "additions_sound": {"type": "compound", "value": {"sound": {"type": "string", "value": "minecraft:ambient.soul_sand_valley.additions"}, "tick_chance": {"type": "double", "value": 0.0111}}}, "particle": {"type": "compound", "value": {"probability": {"type": "float", "value": 0.0062500000931322575}, "options": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:ash"}}}}}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 1787717}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.soul_sand_valley.mood"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:basalt_deltas"}, "id": {"type": "int", "value": 57}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "none"}, "effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.nether.basalt_deltas"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7254527}, "ambient_sound": {"type": "string", "value": "minecraft:ambient.basalt_deltas.loop"}, "additions_sound": {"type": "compound", "value": {"sound": {"type": "string", "value": "minecraft:ambient.basalt_deltas.additions"}, "tick_chance": {"type": "double", "value": 0.0111}}}, "particle": {"type": "compound", "value": {"probability": {"type": "float", "value": 0.1180933341383934}, "options": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:white_ash"}}}}}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 6840176}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.basalt_deltas.mood"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:the_end"}, "id": {"type": "int", "value": 58}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "none"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 0}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 10518688}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:end_highlands"}, "id": {"type": "int", "value": 59}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "none"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 0}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 10518688}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:end_midlands"}, "id": {"type": "int", "value": 60}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "none"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 0}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 10518688}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:small_end_islands"}, "id": {"type": "int", "value": 61}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "none"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 0}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 10518688}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:end_barrens"}, "id": {"type": "int", "value": 62}, "element": {"type": "compound", "value": {"precipitation": {"type": "string", "value": "none"}, "effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 0}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 10518688}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}]}}}}}}, "worldType": "minecraft:overworld", "worldName": "minecraft:overworld", "hashedSeed": [-480297713, -2128446749], "maxPlayers": 20, "viewDistance": 10, "simulationDistance": 10, "reducedDebugInfo": false, "enableRespawnScreen": true, "isDebug": false, "isFlat": false}