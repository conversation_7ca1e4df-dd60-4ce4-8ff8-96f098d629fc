[{"id": 0, "name": "protection", "displayName": "Protection", "maxLevel": 4, "minCost": {"a": 11, "b": -10}, "maxCost": {"a": 11, "b": 1}, "treasureOnly": false, "curse": false, "exclude": ["fire_protection", "blast_protection", "projectile_protection"], "category": "armor", "weight": 10, "tradeable": true, "discoverable": true}, {"id": 1, "name": "fire_protection", "displayName": "Fire Protection", "maxLevel": 4, "minCost": {"a": 8, "b": 2}, "maxCost": {"a": 8, "b": 10}, "treasureOnly": false, "curse": false, "exclude": ["protection", "blast_protection", "projectile_protection"], "category": "armor", "weight": 5, "tradeable": true, "discoverable": true}, {"id": 2, "name": "feather_falling", "displayName": "Feather Falling", "maxLevel": 4, "minCost": {"a": 6, "b": -1}, "maxCost": {"a": 6, "b": 5}, "treasureOnly": false, "curse": false, "exclude": [], "category": "armor_feet", "weight": 5, "tradeable": true, "discoverable": true}, {"id": 3, "name": "blast_protection", "displayName": "Blast Protection", "maxLevel": 4, "minCost": {"a": 8, "b": -3}, "maxCost": {"a": 8, "b": 5}, "treasureOnly": false, "curse": false, "exclude": ["protection", "fire_protection", "projectile_protection"], "category": "armor", "weight": 2, "tradeable": true, "discoverable": true}, {"id": 4, "name": "projectile_protection", "displayName": "Projectile Protection", "maxLevel": 4, "minCost": {"a": 6, "b": -3}, "maxCost": {"a": 6, "b": 3}, "treasureOnly": false, "curse": false, "exclude": ["protection", "fire_protection", "blast_protection"], "category": "armor", "weight": 5, "tradeable": true, "discoverable": true}, {"id": 5, "name": "thorns", "displayName": "Thorns", "maxLevel": 3, "minCost": {"a": 20, "b": -10}, "maxCost": {"a": 10, "b": 51}, "treasureOnly": false, "curse": false, "exclude": [], "category": "armor_chest", "weight": 1, "tradeable": true, "discoverable": true}, {"id": 6, "name": "respiration", "displayName": "Respiration", "maxLevel": 3, "minCost": {"a": 10, "b": 0}, "maxCost": {"a": 10, "b": 30}, "treasureOnly": false, "curse": false, "exclude": [], "category": "armor_head", "weight": 2, "tradeable": true, "discoverable": true}, {"id": 7, "name": "depth_strider", "displayName": "Depth Strider", "maxLevel": 3, "minCost": {"a": 10, "b": 0}, "maxCost": {"a": 10, "b": 15}, "treasureOnly": false, "curse": false, "exclude": ["frost_walker"], "category": "armor_feet", "weight": 2, "tradeable": true, "discoverable": true}, {"id": 8, "name": "aqua_affinity", "displayName": "Aqua Affinity", "maxLevel": 1, "minCost": {"a": 0, "b": 1}, "maxCost": {"a": 0, "b": 41}, "treasureOnly": false, "curse": false, "exclude": [], "category": "armor_head", "weight": 2, "tradeable": true, "discoverable": true}, {"id": 9, "name": "sharpness", "displayName": "Sharpness", "maxLevel": 5, "minCost": {"a": 11, "b": -10}, "maxCost": {"a": 11, "b": 10}, "treasureOnly": false, "curse": false, "exclude": ["smite", "bane_of_arthropods"], "category": "weapon", "weight": 10, "tradeable": true, "discoverable": true}, {"id": 10, "name": "smite", "displayName": "Smite", "maxLevel": 5, "minCost": {"a": 8, "b": -3}, "maxCost": {"a": 8, "b": 17}, "treasureOnly": false, "curse": false, "exclude": ["sharpness", "bane_of_arthropods"], "category": "weapon", "weight": 5, "tradeable": true, "discoverable": true}, {"id": 11, "name": "bane_of_arthropods", "displayName": "Bane of Arthropods", "maxLevel": 5, "minCost": {"a": 8, "b": -3}, "maxCost": {"a": 8, "b": 17}, "treasureOnly": false, "curse": false, "exclude": ["sharpness", "smite"], "category": "weapon", "weight": 5, "tradeable": true, "discoverable": true}, {"id": 12, "name": "knockback", "displayName": "K<PERSON><PERSON>", "maxLevel": 2, "minCost": {"a": 20, "b": -15}, "maxCost": {"a": 10, "b": 51}, "treasureOnly": false, "curse": false, "exclude": [], "category": "weapon", "weight": 5, "tradeable": true, "discoverable": true}, {"id": 13, "name": "fire_aspect", "displayName": "Fire Aspect", "maxLevel": 2, "minCost": {"a": 20, "b": -10}, "maxCost": {"a": 10, "b": 51}, "treasureOnly": false, "curse": false, "exclude": [], "category": "weapon", "weight": 2, "tradeable": true, "discoverable": true}, {"id": 14, "name": "looting", "displayName": "Looting", "maxLevel": 3, "minCost": {"a": 9, "b": 6}, "maxCost": {"a": 10, "b": 51}, "treasureOnly": false, "curse": false, "exclude": ["silk_touch"], "category": "weapon", "weight": 2, "tradeable": true, "discoverable": true}, {"id": 15, "name": "efficiency", "displayName": "Efficiency", "maxLevel": 5, "minCost": {"a": 10, "b": -9}, "maxCost": {"a": 10, "b": 51}, "treasureOnly": false, "curse": false, "exclude": [], "category": "digger", "weight": 10, "tradeable": true, "discoverable": true}, {"id": 16, "name": "silk_touch", "displayName": "Silk Touch", "maxLevel": 1, "minCost": {"a": 0, "b": 15}, "maxCost": {"a": 10, "b": 51}, "treasureOnly": false, "curse": false, "exclude": ["looting", "fortune", "luck_of_the_sea"], "category": "digger", "weight": 1, "tradeable": true, "discoverable": true}, {"id": 17, "name": "unbreaking", "displayName": "Unbreaking", "maxLevel": 3, "minCost": {"a": 8, "b": -3}, "maxCost": {"a": 10, "b": 51}, "treasureOnly": false, "curse": false, "exclude": [], "category": "breakable", "weight": 5, "tradeable": true, "discoverable": true}, {"id": 18, "name": "fortune", "displayName": "Fortune", "maxLevel": 3, "minCost": {"a": 9, "b": 6}, "maxCost": {"a": 10, "b": 51}, "treasureOnly": false, "curse": false, "exclude": ["silk_touch"], "category": "digger", "weight": 2, "tradeable": true, "discoverable": true}, {"id": 19, "name": "power", "displayName": "Power", "maxLevel": 5, "minCost": {"a": 10, "b": -9}, "maxCost": {"a": 10, "b": 6}, "treasureOnly": false, "curse": false, "exclude": [], "category": "bow", "weight": 10, "tradeable": true, "discoverable": true}, {"id": 20, "name": "punch", "displayName": "Punch", "maxLevel": 2, "minCost": {"a": 20, "b": -8}, "maxCost": {"a": 20, "b": 17}, "treasureOnly": false, "curse": false, "exclude": [], "category": "bow", "weight": 2, "tradeable": true, "discoverable": true}, {"id": 21, "name": "flame", "displayName": "Flame", "maxLevel": 1, "minCost": {"a": 0, "b": 20}, "maxCost": {"a": 0, "b": 50}, "treasureOnly": false, "curse": false, "exclude": [], "category": "bow", "weight": 2, "tradeable": true, "discoverable": true}, {"id": 22, "name": "infinity", "displayName": "Infinity", "maxLevel": 1, "minCost": {"a": 0, "b": 20}, "maxCost": {"a": 0, "b": 50}, "treasureOnly": false, "curse": false, "exclude": ["mending"], "category": "bow", "weight": 1, "tradeable": true, "discoverable": true}, {"id": 23, "name": "luck_of_the_sea", "displayName": "Luck of the Sea", "maxLevel": 3, "minCost": {"a": 9, "b": 6}, "maxCost": {"a": 10, "b": 51}, "treasureOnly": false, "curse": false, "exclude": ["silk_touch"], "category": "fishing_rod", "weight": 2, "tradeable": true, "discoverable": true}, {"id": 24, "name": "lure", "displayName": "<PERSON><PERSON>", "maxLevel": 3, "minCost": {"a": 9, "b": 6}, "maxCost": {"a": 10, "b": 51}, "treasureOnly": false, "curse": false, "exclude": [], "category": "fishing_rod", "weight": 2, "tradeable": true, "discoverable": true}, {"id": 25, "name": "frost_walker", "displayName": "<PERSON>", "maxLevel": 2, "minCost": {"a": 10, "b": 0}, "maxCost": {"a": 10, "b": 15}, "treasureOnly": true, "curse": false, "exclude": ["depth_strider"], "category": "armor_feet", "weight": 2, "tradeable": true, "discoverable": true}, {"id": 26, "name": "mending", "displayName": "Mending", "maxLevel": 1, "minCost": {"a": 25, "b": 0}, "maxCost": {"a": 25, "b": 50}, "treasureOnly": true, "curse": false, "exclude": ["infinity"], "category": "breakable", "weight": 2, "tradeable": true, "discoverable": true}, {"id": 27, "name": "binding_curse", "displayName": "Curse of Binding", "maxLevel": 1, "minCost": {"a": 0, "b": 25}, "maxCost": {"a": 0, "b": 50}, "treasureOnly": true, "curse": true, "exclude": [], "category": "wearable", "weight": 1, "tradeable": true, "discoverable": true}, {"id": 28, "name": "vanishing_curse", "displayName": "Curse of Vanishing", "maxLevel": 1, "minCost": {"a": 0, "b": 25}, "maxCost": {"a": 0, "b": 50}, "treasureOnly": true, "curse": true, "exclude": [], "category": "vanishable", "weight": 1, "tradeable": true, "discoverable": true}, {"id": 29, "name": "impaling", "displayName": "Impaling", "maxLevel": 5, "minCost": {"a": 8, "b": -7}, "maxCost": {"a": 8, "b": 13}, "treasureOnly": false, "curse": false, "exclude": [], "category": "trident", "weight": 2, "tradeable": true, "discoverable": true}, {"id": 30, "name": "riptide", "displayName": "Riptide", "maxLevel": 3, "minCost": {"a": 7, "b": 10}, "maxCost": {"a": 0, "b": 50}, "treasureOnly": false, "curse": false, "exclude": ["loyalty", "channeling"], "category": "trident", "weight": 2, "tradeable": true, "discoverable": true}, {"id": 31, "name": "loyalty", "displayName": "Loyalty", "maxLevel": 3, "minCost": {"a": 7, "b": 5}, "maxCost": {"a": 0, "b": 50}, "treasureOnly": false, "curse": false, "exclude": ["riptide"], "category": "trident", "weight": 5, "tradeable": true, "discoverable": true}, {"id": 32, "name": "channeling", "displayName": "Channeling", "maxLevel": 1, "minCost": {"a": 0, "b": 25}, "maxCost": {"a": 0, "b": 50}, "treasureOnly": false, "curse": false, "exclude": ["riptide"], "category": "trident", "weight": 1, "tradeable": true, "discoverable": true}, {"id": 33, "name": "multishot", "displayName": "Multishot", "maxLevel": 1, "minCost": {"a": 0, "b": 20}, "maxCost": {"a": 0, "b": 50}, "treasureOnly": false, "curse": false, "exclude": ["piercing"], "category": "crossbow", "weight": 2, "tradeable": true, "discoverable": true}, {"id": 34, "name": "piercing", "displayName": "Piercing", "maxLevel": 4, "minCost": {"a": 10, "b": -9}, "maxCost": {"a": 0, "b": 50}, "treasureOnly": false, "curse": false, "exclude": ["multishot"], "category": "crossbow", "weight": 10, "tradeable": true, "discoverable": true}, {"id": 35, "name": "quick_charge", "displayName": "Quick Charge", "maxLevel": 3, "minCost": {"a": 20, "b": -8}, "maxCost": {"a": 0, "b": 50}, "treasureOnly": false, "curse": false, "exclude": [], "category": "crossbow", "weight": 5, "tradeable": true, "discoverable": true}, {"id": 36, "name": "soul_speed", "displayName": "Soul Speed", "maxLevel": 3, "minCost": {"a": 10, "b": 0}, "maxCost": {"a": 10, "b": 15}, "treasureOnly": true, "curse": false, "exclude": [], "category": "armor_feet", "weight": 1, "tradeable": false, "discoverable": false}, {"id": 37, "name": "swift_sneak", "displayName": "Swift Sneak", "maxLevel": 3, "minCost": {"a": 25, "b": 0}, "maxCost": {"a": 25, "b": 50}, "treasureOnly": true, "curse": false, "exclude": [], "category": "armor_legs", "weight": 1, "tradeable": false, "discoverable": false}]