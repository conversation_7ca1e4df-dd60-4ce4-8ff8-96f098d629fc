[{"id": 0, "displayName": "Air", "name": "air", "hardness": 0, "stackSize": 0, "diggable": true, "boundingBox": "empty", "drops": [{"drop": 0}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 1, "displayName": "Stone", "name": "stone", "hardness": 1.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 4}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 6}, {"id": 2, "displayName": "Grass Block", "name": "grass", "hardness": 0.6, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "dirt", "drops": [{"drop": {"id": 3, "metadata": 0}}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.6}, {"id": 3, "displayName": "Dirt", "name": "dirt", "hardness": 0.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "dirt", "variations": [{"metadata": 0, "displayName": "Dirt"}, {"metadata": 1, "displayName": "Coarse Dirt"}, {"metadata": 2, "displayName": "Podzol"}], "drops": [{"drop": 3}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.5}, {"id": 4, "displayName": "Cobblestone", "name": "cobblestone", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 4}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 6}, {"id": 5, "displayName": "Wood Planks", "name": "planks", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"metadata": 0, "displayName": "Oak Wood Planks"}, {"metadata": 1, "displayName": "Spruce Wood Planks"}, {"metadata": 2, "displayName": "Birch Wood Planks"}, {"metadata": 3, "displayName": "Jungle Wood Planks"}, {"metadata": 4, "displayName": "Acacia Wood Planks"}, {"metadata": 5, "displayName": "Dark Oak Wood Planks"}], "drops": [{"drop": 5}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 3}, {"id": 6, "displayName": "Sapling", "name": "sapling", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "plant", "drops": [{"drop": 6}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 7, "displayName": "Bedrock", "name": "bedrock", "hardness": null, "stackSize": 64, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 3600000}, {"id": 8, "displayName": "Water", "name": "flowing_water", "hardness": 100, "stackSize": 0, "diggable": false, "boundingBox": "empty", "drops": [], "transparent": true, "emitLight": 0, "filterLight": 2, "resistance": 100}, {"id": 9, "displayName": "Stationary Water", "name": "water", "hardness": 100, "stackSize": 0, "diggable": false, "boundingBox": "empty", "drops": [], "transparent": true, "emitLight": 0, "filterLight": 2, "resistance": 100}, {"id": 10, "displayName": "<PERSON><PERSON>", "name": "flowing_lava", "hardness": 100, "stackSize": 0, "diggable": false, "boundingBox": "empty", "drops": [], "transparent": true, "emitLight": 15, "filterLight": 0, "resistance": 100}, {"id": 11, "displayName": "Stationary Lava", "name": "lava", "hardness": 100, "stackSize": 0, "diggable": false, "boundingBox": "empty", "drops": [], "transparent": true, "emitLight": 15, "filterLight": 0, "resistance": 100}, {"id": 12, "displayName": "Sand", "name": "sand", "hardness": 0.5, "stackSize": 0, "diggable": true, "boundingBox": "block", "material": "dirt", "variations": [{"metadata": 0, "displayName": "Sand"}, {"metadata": 1, "displayName": "Red sand"}], "drops": [{"drop": 12}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.5}, {"id": 13, "displayName": "<PERSON>l", "name": "gravel", "hardness": 0.6, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "dirt", "drops": [{"drop": 13, "minCount": 0.9}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.6}, {"id": 14, "displayName": "Gold Ore", "name": "gold_ore", "hardness": 3, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "278": true}, "drops": [{"drop": 14}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 3}, {"id": 15, "displayName": "Iron Ore", "name": "iron_ore", "hardness": 3, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "274": true, "278": true}, "drops": [{"drop": 15}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 3}, {"id": 16, "displayName": "Coal Ore", "name": "coal_ore", "hardness": 3, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": {"id": 263, "metadata": 0}}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 3}, {"id": 17, "displayName": "<PERSON>", "name": "log", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "Oak Wood", "metadata": 0, "description": "Facing up and down"}, {"displayName": "Spruce Wood", "metadata": 1, "description": "Facing up and down"}, {"displayName": "Birch Wood", "metadata": 2, "description": "Facing up and down"}, {"displayName": "Jungle Wood", "metadata": 3, "description": "Facing up and down"}, {"displayName": "Oak Wood", "metadata": 4, "description": "Facing west and east"}, {"displayName": "Spruce Wood", "metadata": 5, "description": "Facing west and east"}, {"displayName": "Birch Wood", "metadata": 6, "description": "Facing west and east"}, {"displayName": "Jungle Wood", "metadata": 7, "description": "Facing west and east"}, {"displayName": "Oak Wood", "metadata": 8, "description": "Facing north and south"}, {"displayName": "Spruce Wood", "metadata": 9, "description": "Facing north and south"}, {"displayName": "Birch Wood", "metadata": 10, "description": "Facing north and south"}, {"displayName": "Jungle Wood", "metadata": 11, "description": "Facing north and south"}], "drops": [{"drop": 17}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 18, "displayName": "Leaves", "name": "leaves", "hardness": 0.2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "plant", "variations": [{"metadata": 0, "displayName": "Oak Leaves"}, {"metadata": 1, "displayName": "Spruce Leaves"}, {"metadata": 2, "displayName": "Birch Leaves"}, {"metadata": 3, "displayName": "Jungle Leaves"}, {"metadata": 4, "displayName": "Oak Leaves", "description": "No decay"}, {"metadata": 5, "displayName": "Spruce Leaves", "description": "No decay"}, {"metadata": 6, "displayName": "Birch Leaves", "description": "No decay"}, {"metadata": 7, "displayName": "Jungle Leaves", "description": "No decay"}, {"metadata": 8, "displayName": "Oak Leaves", "description": "Check decay"}, {"metadata": 9, "displayName": "Spruce Leaves", "description": "Check decay"}, {"metadata": 10, "displayName": "Birch Leaves", "description": "Check decay"}, {"metadata": 11, "displayName": "Jungle Leaves", "description": "Check decay"}, {"metadata": 12, "displayName": "Oak Leaves", "description": "No decay and check decay"}, {"metadata": 13, "displayName": "Spruce Leaves", "description": "No decay and check decay"}, {"metadata": 14, "displayName": "Birch Leaves", "description": "No decay and check decay"}, {"metadata": 15, "displayName": "Jungle Leaves", "description": "No decay and check decay"}], "drops": [{"drop": 6, "minCount": 0, "maxCount": 1}, {"drop": 260, "minCount": 0, "maxCount": 1}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 19, "displayName": "Sponge", "name": "sponge", "hardness": 0.6, "stackSize": 64, "diggable": true, "boundingBox": "block", "variations": [{"metadata": 0, "displayName": "Sponge"}, {"metadata": 1, "displayName": "Wet Sponge"}], "drops": [{"drop": 19}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.6}, {"id": 20, "displayName": "Glass", "name": "glass", "hardness": 0.3, "stackSize": 64, "diggable": true, "boundingBox": "block", "drops": [], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.3}, {"id": 21, "displayName": "Lapis <PERSON> Ore", "name": "lapis_ore", "hardness": 3, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "274": true, "278": true}, "drops": [{"drop": {"id": 351, "metadata": 4}, "minCount": 4, "maxCount": 8}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 3}, {"id": 22, "displayName": "Lapis <PERSON>", "name": "lapis_block", "hardness": 3, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "274": true, "278": true}, "drops": [{"drop": 22}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 3}, {"id": 23, "displayName": "Dispenser", "name": "dispenser", "hardness": 3.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 23}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 3.5}, {"id": 24, "displayName": "Sandstone", "name": "sandstone", "hardness": 0.8, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "variations": [{"metadata": 0, "displayName": "Sandstone"}, {"metadata": 1, "displayName": "Chiseled sandstone"}, {"metadata": 2, "displayName": "Smooth sandstone"}], "drops": [{"drop": 24}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.8}, {"id": 25, "displayName": "Note Block", "name": "noteblock", "hardness": 0.8, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "drops": [{"drop": 25}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.8}, {"id": 26, "displayName": "Bed", "name": "bed", "hardness": 0.2, "stackSize": 1, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "Bed", "metadata": 0, "description": "Lower half, facing south"}, {"displayName": "Bed", "metadata": 1, "description": "Lower half, facing west"}, {"displayName": "Bed", "metadata": 2, "description": "Lower half, facing north"}, {"displayName": "Bed", "metadata": 3, "description": "Lower half, facing east"}, {"displayName": "Bed", "metadata": 8, "description": "Upper half, facing south"}, {"displayName": "Bed", "metadata": 9, "description": "Upper half, facing west"}, {"displayName": "Bed", "metadata": 10, "description": "Upper half, facing north"}, {"displayName": "Bed", "metadata": 11, "description": "Upper half, facing east"}], "drops": [{"drop": 26}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.2}, {"id": 27, "displayName": "Powered Rail", "name": "golden_rail", "hardness": 0.7, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "rock", "variations": [{"displayName": "Powered Rail", "metadata": 0, "description": "Not activated, shape: north and south"}, {"displayName": "Powered Rail", "metadata": 1, "description": "Not activated, shape: west and east"}, {"displayName": "Powered Rail", "metadata": 2, "description": "Not activated, shape: ascending to east"}, {"displayName": "Powered Rail", "metadata": 3, "description": "Not activated, shape: ascending to west"}, {"displayName": "Powered Rail", "metadata": 4, "description": "Not activated, shape: ascending to north"}, {"displayName": "Powered Rail", "metadata": 5, "description": "Not activated, shape: ascending to south"}, {"displayName": "Powered Rail", "metadata": 8, "description": "Activated, shape: north and south"}, {"displayName": "Powered Rail", "metadata": 9, "description": "Activated, shape: west and east"}, {"displayName": "Powered Rail", "metadata": 10, "description": "Activated, shape: ascending to east"}, {"displayName": "Powered Rail", "metadata": 11, "description": "Activated, shape: ascending to west"}, {"displayName": "Powered Rail", "metadata": 12, "description": "Activated, shape: ascending to north"}, {"displayName": "Powered Rail", "metadata": 13, "description": "Activated, shape: ascending to south"}], "drops": [{"drop": 27}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.7}, {"id": 28, "displayName": "Detector Rail", "name": "detector_rail", "hardness": 0.7, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "rock", "variations": [{"displayName": "Detector Rail", "metadata": 0, "description": "Not activated, shape: north and south"}, {"displayName": "Detector Rail", "metadata": 1, "description": "Not activated, shape: west and east"}, {"displayName": "Detector Rail", "metadata": 2, "description": "Not activated, shape: ascending to east"}, {"displayName": "Detector Rail", "metadata": 3, "description": "Not activated, shape: ascending to west"}, {"displayName": "Detector Rail", "metadata": 4, "description": "Not activated, shape: ascending to north"}, {"displayName": "Detector Rail", "metadata": 5, "description": "Not activated, shape: ascending to south"}, {"displayName": "Detector Rail", "metadata": 8, "description": "Activated, shape: north and south"}, {"displayName": "Detector Rail", "metadata": 9, "description": "Activated, shape: west and east"}, {"displayName": "Detector Rail", "metadata": 10, "description": "Activated, shape: ascending to east"}, {"displayName": "Detector Rail", "metadata": 11, "description": "Activated, shape: ascending to west"}, {"displayName": "Detector Rail", "metadata": 12, "description": "Activated, shape: ascending to north"}, {"displayName": "Detector Rail", "metadata": 13, "description": "Activated, shape: ascending to south"}], "drops": [{"drop": 28}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.7}, {"id": 29, "displayName": "<PERSON><PERSON>", "name": "sticky_piston", "hardness": 0.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "<PERSON><PERSON>", "metadata": 0, "description": "Not extended, facing down"}, {"displayName": "<PERSON><PERSON>", "metadata": 1, "description": "Not extended, facing up"}, {"displayName": "<PERSON><PERSON>", "metadata": 2, "description": "Not extended, facing north"}, {"displayName": "<PERSON><PERSON>", "metadata": 3, "description": "Not extended, facing south"}, {"displayName": "<PERSON><PERSON>", "metadata": 4, "description": "Not extended, facing west"}, {"displayName": "<PERSON><PERSON>", "metadata": 5, "description": "Not extended, facing east"}, {"displayName": "<PERSON><PERSON>", "metadata": 8, "description": "Extended, facing down"}, {"displayName": "<PERSON><PERSON>", "metadata": 9, "description": "Extended, facing up"}, {"displayName": "<PERSON><PERSON>", "metadata": 10, "description": "Extended, facing north"}, {"displayName": "<PERSON><PERSON>", "metadata": 11, "description": "Extended, facing south"}, {"displayName": "<PERSON><PERSON>", "metadata": 12, "description": "Extended, facing west"}, {"displayName": "<PERSON><PERSON>", "metadata": 13, "description": "Extended, facing east"}], "drops": [{"drop": 29}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 30, "displayName": "Cobweb", "name": "web", "hardness": 4, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "web", "harvestTools": {"267": true, "268": true, "272": true, "276": true, "283": true, "359": true}, "drops": [{"drop": 287}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 4}, {"id": 31, "displayName": "Grass", "name": "tallgrass", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "plant", "variations": [{"metadata": 0, "displayName": "<PERSON><PERSON><PERSON>"}, {"metadata": 1, "displayName": "Tall Grass"}, {"metadata": 2, "displayName": "Fern"}], "drops": [{"drop": 295, "minCount": 0, "maxCount": 1}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 32, "displayName": "Dead Bush", "name": "deadbush", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "plant", "drops": [{"drop": 280, "minCount": 0, "maxCount": 2}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 33, "displayName": "<PERSON><PERSON>", "name": "piston", "hardness": 0.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "<PERSON><PERSON>", "metadata": 0, "description": "Not extended, facing down"}, {"displayName": "<PERSON><PERSON>", "metadata": 1, "description": "Not extended, facing up"}, {"displayName": "<PERSON><PERSON>", "metadata": 2, "description": "Not extended, facing north"}, {"displayName": "<PERSON><PERSON>", "metadata": 3, "description": "Not extended, facing south"}, {"displayName": "<PERSON><PERSON>", "metadata": 4, "description": "Not extended, facing west"}, {"displayName": "<PERSON><PERSON>", "metadata": 5, "description": "Not extended, facing east"}, {"displayName": "<PERSON><PERSON>", "metadata": 8, "description": "Extended, facing down"}, {"displayName": "<PERSON><PERSON>", "metadata": 9, "description": "Extended, facing up"}, {"displayName": "<PERSON><PERSON>", "metadata": 10, "description": "Extended, facing north"}, {"displayName": "<PERSON><PERSON>", "metadata": 11, "description": "Extended, facing south"}, {"displayName": "<PERSON><PERSON>", "metadata": 12, "description": "Extended, facing west"}, {"displayName": "<PERSON><PERSON>", "metadata": 13, "description": "Extended, facing east"}], "drops": [{"drop": 33}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 34, "displayName": "Piston Head", "name": "piston_head", "hardness": 0.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "Piston Head", "metadata": 0, "description": "Normal, facing down"}, {"displayName": "Piston Head", "metadata": 1, "description": "Normal, facing down"}, {"displayName": "Piston Head", "metadata": 2, "description": "Normal, facing north"}, {"displayName": "Piston Head", "metadata": 3, "description": "Normal, facing south"}, {"displayName": "Piston Head", "metadata": 4, "description": "Normal, facing west"}, {"displayName": "Piston Head", "metadata": 5, "description": "Normal, facing east"}, {"displayName": "Piston Head", "metadata": 8, "description": "Sticky, facing down"}, {"displayName": "Piston Head", "metadata": 9, "description": "Sticky, facing up"}, {"displayName": "Piston Head", "metadata": 10, "description": "Sticky, facing north"}, {"displayName": "Piston Head", "metadata": 11, "description": "Sticky, facing south"}, {"displayName": "Piston Head", "metadata": 12, "description": "Sticky, facing west"}, {"displayName": "Piston Head", "metadata": 13, "description": "Sticky, facing east"}], "drops": [{"drop": 34}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 35, "displayName": "Wool", "name": "wool", "hardness": 0.8, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wool", "variations": [{"metadata": 0, "displayName": "White Wool"}, {"metadata": 1, "displayName": "Orange Wool"}, {"metadata": 2, "displayName": "Magenta Wool"}, {"metadata": 3, "displayName": "Light blue Wool"}, {"metadata": 4, "displayName": "Yellow Wool"}, {"metadata": 5, "displayName": "Lime Wool"}, {"metadata": 6, "displayName": "Pink Wool"}, {"metadata": 7, "displayName": "Gray <PERSON>"}, {"metadata": 8, "displayName": "Light gray Wool"}, {"metadata": 9, "displayName": "<PERSON><PERSON>"}, {"metadata": 10, "displayName": "Purple Wool"}, {"metadata": 11, "displayName": "Blue Wool"}, {"metadata": 12, "displayName": "Brown Wool"}, {"metadata": 13, "displayName": "Green Wool"}, {"metadata": 14, "displayName": "Red Wool"}, {"metadata": 15, "displayName": "Black Wool"}], "drops": [{"drop": 35}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.8}, {"id": 36, "displayName": "Block moved by <PERSON><PERSON>", "name": "piston_extension", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "drops": [{"drop": 36}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 37, "displayName": "Dandelion", "name": "yellow_flower", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "plant", "drops": [{"drop": 37}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 38, "displayName": "<PERSON><PERSON>", "name": "red_flower", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "plant", "variations": [{"metadata": 0, "displayName": "<PERSON><PERSON>"}, {"metadata": 1, "displayName": "Blue Orchid"}, {"metadata": 2, "displayName": "Allium"}, {"metadata": 3, "displayName": "Azure Bluet"}, {"metadata": 4, "displayName": "<PERSON>lip"}, {"metadata": 5, "displayName": "Orange Tulip"}, {"metadata": 6, "displayName": "White Tulip"}, {"metadata": 7, "displayName": "<PERSON> Tulip"}, {"metadata": 8, "displayName": "Oxeye Daisy"}], "drops": [{"drop": 38}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 39, "displayName": "Mushroom", "name": "brown_mushroom", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "plant", "drops": [{"drop": 39}], "transparent": false, "emitLight": 1, "filterLight": 15, "resistance": 0}, {"id": 40, "displayName": "Mushroom", "name": "red_mushroom", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "plant", "drops": [{"drop": 40}], "transparent": false, "emitLight": 1, "filterLight": 15, "resistance": 0}, {"id": 41, "displayName": "Block of Gold", "name": "gold_block", "hardness": 3, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "278": true}, "drops": [{"drop": 41}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 6}, {"id": 42, "displayName": "Block of Iron", "name": "iron_block", "hardness": 5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "274": true, "278": true}, "drops": [{"drop": 42}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 6}, {"id": 43, "displayName": "Double Stone Slab", "name": "double_stone_slab", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "variations": [{"metadata": 0, "displayName": "Double Stone Slab"}, {"metadata": 1, "displayName": "Double Sandstone Slab"}, {"metadata": 2, "displayName": "Double (Stone) <PERSON><PERSON>b"}, {"metadata": 3, "displayName": "Double Cobblestone Slab"}, {"metadata": 4, "displayName": "Double Bricks Slab"}, {"metadata": 5, "displayName": "Double Stone Brick Slab"}, {"metadata": 6, "displayName": "Double Nether Brick Slab"}, {"metadata": 7, "displayName": "Double Quartz Slab"}, {"metadata": 8, "displayName": "Smooth Double Stone Slab"}, {"metadata": 9, "displayName": "Smooth Double Sandstone Slab"}, {"metadata": 15, "displayName": "Tile Double Quartz Slab", "description": "Note the underside"}], "drops": [{"drop": {"id": 44, "metadata": 0}}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 6}, {"id": 44, "displayName": "<PERSON> Slab", "name": "stone_slab", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "variations": [{"metadata": 0, "displayName": "<PERSON> Slab", "description": "Lower half"}, {"metadata": 1, "displayName": "Sandstone Slab", "description": "Lower half"}, {"metadata": 2, "displayName": "(<PERSON>) <PERSON><PERSON>b", "description": "Lower half"}, {"metadata": 3, "displayName": "Cobblestone Slab", "description": "Lower half"}, {"metadata": 4, "displayName": "Bricks Slab", "description": "Lower half"}, {"metadata": 5, "displayName": "Stone Brick Slab", "description": "Lower half"}, {"metadata": 6, "displayName": "Nether Brick Slab", "description": "Lower half"}, {"metadata": 7, "displayName": "Quartz Slab", "description": "Lower half"}, {"metadata": 8, "displayName": "<PERSON> Slab", "description": "Upper half"}, {"metadata": 9, "displayName": "Sandstone Slab", "description": "Upper half"}, {"metadata": 10, "displayName": "(<PERSON>) <PERSON><PERSON>b", "description": "Upper half"}, {"metadata": 11, "displayName": "Cobblestone Slab", "description": "Upper half"}, {"metadata": 12, "displayName": "Bricks Slab", "description": "Upper half"}, {"metadata": 13, "displayName": "Stone Brick Slab", "description": "Upper half"}, {"metadata": 14, "displayName": "Nether Brick Slab", "description": "Upper half"}, {"metadata": 15, "displayName": "Quartz Slab", "description": "Upper half"}], "drops": [{"drop": {"id": 44, "metadata": 0}}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 6}, {"id": 45, "displayName": "Bricks", "name": "brick_block", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 45}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 6}, {"id": 46, "displayName": "TNT", "name": "tnt", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "TNT", "metadata": 0, "description": "Drops a TNT item when broken"}, {"displayName": "TNT", "metadata": 1, "description": "Activates when broken"}], "drops": [{"drop": 46}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 47, "displayName": "Bookshelf", "name": "bookshelf", "hardness": 1.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "drops": [{"drop": 340, "minCount": 3}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 1.5}, {"id": 48, "displayName": "<PERSON>", "name": "mossy_cobblestone", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 48}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 6}, {"id": 49, "displayName": "Obsidian", "name": "obsidian", "hardness": 50, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"278": true}, "drops": [{"drop": 49}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 1200}, {"id": 50, "displayName": "<PERSON>ch", "name": "torch", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "variations": [{"displayName": "<PERSON>ch", "metadata": 0, "description": "Facing east (attached to a block to its west)"}, {"displayName": "<PERSON>ch", "metadata": 1, "description": "facing west (attached to a block to its east)"}, {"displayName": "<PERSON>ch", "metadata": 2, "description": "Facing south (attached to a block to its north)"}, {"displayName": "<PERSON>ch", "metadata": 3, "description": "Facing north (attached to a block to its south)"}, {"displayName": "<PERSON>ch", "metadata": 4, "description": "Facing up (attached to a block beneath it)"}], "drops": [{"drop": 50}], "transparent": true, "emitLight": 14, "filterLight": 0, "resistance": 0}, {"id": 51, "displayName": "Fire", "name": "fire", "hardness": 0, "stackSize": 0, "diggable": true, "boundingBox": "empty", "drops": [], "transparent": true, "emitLight": 15, "filterLight": 0, "resistance": 0}, {"id": 52, "displayName": "Monster Spawner", "name": "mob_spawner", "hardness": 5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 5}, {"id": 53, "displayName": "Oak Wood Stairs", "name": "oak_stairs", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "Oak Wood Stairs", "metadata": 0, "description": "Lower half, facing east"}, {"displayName": "Oak Wood Stairs", "metadata": 1, "description": "Lower half, facing west"}, {"displayName": "Oak Wood Stairs", "metadata": 2, "description": "Lower half, facing south"}, {"displayName": "Oak Wood Stairs", "metadata": 3, "description": "Lower half, facing north"}, {"displayName": "Oak Wood Stairs", "metadata": 4, "description": "Upper half, facing east"}, {"displayName": "Oak Wood Stairs", "metadata": 5, "description": "Upper half, facing west"}, {"displayName": "Oak Wood Stairs", "metadata": 6, "description": "Upper half, facing south"}, {"displayName": "Oak Wood Stairs", "metadata": 7, "description": "Upper half, facing north"}], "drops": [{"drop": 53}], "transparent": true, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 54, "displayName": "Chest", "name": "chest", "hardness": 2.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "Chest", "metadata": 2, "description": "Facing north"}, {"displayName": "Chest", "metadata": 3, "description": "Facing south"}, {"displayName": "Chest", "metadata": 4, "description": "Facing west"}, {"displayName": "Chest", "metadata": 5, "description": "Facing east"}], "drops": [{"drop": 54}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 2.5}, {"id": 55, "displayName": "Redstone Wire", "name": "redstone_wire", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "variations": [{"displayName": "Redstone Wire", "metadata": 0, "description": "Signal power 0"}, {"displayName": "Redstone Wire", "metadata": 1, "description": "Signal power 1"}, {"displayName": "Redstone Wire", "metadata": 2, "description": "Signal power 2"}, {"displayName": "Redstone Wire", "metadata": 3, "description": "Signal power 3"}, {"displayName": "Redstone Wire", "metadata": 4, "description": "Signal power 4"}, {"displayName": "Redstone Wire", "metadata": 5, "description": "Signal power 5"}, {"displayName": "Redstone Wire", "metadata": 6, "description": "Signal power 6"}, {"displayName": "Redstone Wire", "metadata": 7, "description": "Signal power 7"}, {"displayName": "Redstone Wire", "metadata": 8, "description": "Signal power 8"}, {"displayName": "Redstone Wire", "metadata": 9, "description": "Signal power 9"}, {"displayName": "Redstone Wire", "metadata": 10, "description": "Signal power 10"}, {"displayName": "Redstone Wire", "metadata": 11, "description": "Signal power 11"}, {"displayName": "Redstone Wire", "metadata": 12, "description": "Signal power 12"}, {"displayName": "Redstone Wire", "metadata": 13, "description": "Signal power 13"}, {"displayName": "Redstone Wire", "metadata": 14, "description": "Signal power 14"}, {"displayName": "Redstone Wire", "metadata": 15, "description": "Signal power 15"}], "drops": [{"drop": 331}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 56, "displayName": "Diamond Ore", "name": "diamond_ore", "hardness": 3, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "278": true}, "drops": [{"drop": 264}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 3}, {"id": 57, "displayName": "Block of Diamond", "name": "diamond_block", "hardness": 5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "278": true}, "drops": [{"drop": 57}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 6}, {"id": 58, "displayName": "Crafting Table", "name": "crafting_table", "hardness": 2.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "drops": [{"drop": 58}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 2.5}, {"id": 59, "displayName": "Wheat", "name": "wheat", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "plant", "variations": [{"displayName": "Wheat", "metadata": 0, "description": "Age: 0"}, {"displayName": "Wheat", "metadata": 1, "description": "Age: 1"}, {"displayName": "Wheat", "metadata": 2, "description": "Age: 2"}, {"displayName": "Wheat", "metadata": 3, "description": "Age: 3"}, {"displayName": "Wheat", "metadata": 4, "description": "Age: 4"}, {"displayName": "Wheat", "metadata": 5, "description": "Age: 5"}, {"displayName": "Wheat", "metadata": 6, "description": "Age: 6"}, {"displayName": "Wheat", "metadata": 7, "description": "Age: 7"}], "drops": [{"drop": 295}, {"drop": 295, "minCount": 0, "maxCount": 3}, {"drop": 296}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 60, "displayName": "Farmland", "name": "farmland", "hardness": 0.6, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "dirt", "variations": [{"displayName": "Farmland", "metadata": 0, "description": "Moisture: 0"}, {"displayName": "Farmland", "metadata": 1, "description": "Moisture: 1"}, {"displayName": "Farmland", "metadata": 2, "description": "Moisture: 2"}, {"displayName": "Farmland", "metadata": 3, "description": "Moisture: 3"}, {"displayName": "Farmland", "metadata": 4, "description": "Moisture: 4"}, {"displayName": "Farmland", "metadata": 5, "description": "Moisture: 5"}, {"displayName": "Farmland", "metadata": 6, "description": "Moisture: 6"}, {"displayName": "Farmland", "metadata": 7, "description": "Moisture: 7"}], "drops": [{"drop": {"id": 3, "metadata": 0}}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.6}, {"id": 61, "displayName": "Furnace", "name": "furnace", "hardness": 3.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Furnace", "metadata": 2, "description": "Facing north"}, {"displayName": "Furnace", "metadata": 3, "description": "Facing south"}, {"displayName": "Furnace", "metadata": 4, "description": "Facing west"}, {"displayName": "Furnace", "metadata": 5, "description": "Facing east"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 61}], "transparent": true, "emitLight": 13, "filterLight": 0, "resistance": 3.5}, {"id": 62, "displayName": "Burning Furnace", "name": "lit_furnace", "hardness": 3.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Burning Furnace", "metadata": 2, "description": "Facing north"}, {"displayName": "Burning Furnace", "metadata": 3, "description": "Facing south"}, {"displayName": "Burning Furnace", "metadata": 4, "description": "Facing west"}, {"displayName": "Burning Furnace", "metadata": 5, "description": "Facing east"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 61}], "transparent": true, "emitLight": 13, "filterLight": 0, "resistance": 3.5}, {"id": 63, "displayName": "Standing Sign", "name": "standing_sign", "hardness": 1, "stackSize": 16, "diggable": true, "boundingBox": "empty", "material": "wood", "variations": [{"displayName": "Standing Sign", "metadata": 0, "description": "Facing south"}, {"displayName": "Standing Sign", "metadata": 1, "description": "Facing south - south-west"}, {"displayName": "Standing Sign", "metadata": 2, "description": "Facing south-west"}, {"displayName": "Standing Sign", "metadata": 3, "description": "Facing west - south-west"}, {"displayName": "Standing Sign", "metadata": 4, "description": "Facing west"}, {"displayName": "Standing Sign", "metadata": 5, "description": "Facing west - north-west"}, {"displayName": "Standing Sign", "metadata": 6, "description": "Facing north-west"}, {"displayName": "Standing Sign", "metadata": 7, "description": "Facing north - north-west"}, {"displayName": "Standing Sign", "metadata": 8, "description": "Facing north"}, {"displayName": "Standing Sign", "metadata": 9, "description": "Facing north - north-east"}, {"displayName": "Standing Sign", "metadata": 10, "description": "Facing north-east"}, {"displayName": "Standing Sign", "metadata": 11, "description": "Facing east - north-east"}, {"displayName": "Standing Sign", "metadata": 12, "description": "Facing east"}, {"displayName": "Standing Sign", "metadata": 13, "description": "Facing east - south-east"}, {"displayName": "Standing Sign", "metadata": 14, "description": "Facing south-east"}, {"displayName": "Standing Sign", "metadata": 15, "description": "Facing south - south-east"}], "drops": [{"drop": 323}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 1}, {"id": 64, "displayName": "Oak Door", "name": "wooden_door", "hardness": 3, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "Oak Door", "metadata": 0, "description": "Lower half, facing east"}, {"displayName": "Oak Door", "metadata": 1, "description": "Lower half, facing south"}, {"displayName": "Oak Door", "metadata": 2, "description": "Lower half, facing west"}, {"displayName": "Oak Door", "metadata": 3, "description": "Lower half, facing north"}, {"displayName": "Oak Door", "metadata": 4, "description": "Lower half, facing east, opened"}, {"displayName": "Oak Door", "metadata": 5, "description": "Lower half, facing south, opened"}, {"displayName": "Oak Door", "metadata": 6, "description": "Lower half, facing west, opened"}, {"displayName": "Oak Door", "metadata": 7, "description": "Lower half, facing north, opened"}, {"displayName": "Oak Door", "metadata": 8, "description": "Upper half, hinge left"}, {"displayName": "Oak Door", "metadata": 9, "description": "Upper half, hinge right"}], "drops": [{"drop": 64}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 65, "displayName": "Ladder", "name": "ladder", "hardness": 0.4, "stackSize": 64, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "Ladder", "metadata": 2, "description": "Facing north"}, {"displayName": "Ladder", "metadata": 3, "description": "Facing south"}, {"displayName": "Ladder", "metadata": 4, "description": "Facing west"}, {"displayName": "Ladder", "metadata": 5, "description": "Facing east"}], "drops": [{"drop": 65}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.4}, {"id": 66, "displayName": "Rail", "name": "rail", "hardness": 0.7, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "rock", "variations": [{"displayName": "Rail", "metadata": 0, "description": "Shape: north and south"}, {"displayName": "Rail", "metadata": 1, "description": "Shape: west and east"}, {"displayName": "Rail", "metadata": 2, "description": "Shape: ascending to east"}, {"displayName": "Rail", "metadata": 3, "description": "Shape: ascending to west"}, {"displayName": "Rail", "metadata": 4, "description": "Shape: ascending to north"}, {"displayName": "Rail", "metadata": 5, "description": "Shape: ascending to south"}, {"displayName": "Rail", "metadata": 6, "description": "Shape: south and east"}, {"displayName": "Rail", "metadata": 7, "description": "Shape: south and west"}, {"displayName": "Rail", "metadata": 8, "description": "Shape: north and west"}, {"displayName": "Rail", "metadata": 9, "description": "Shape: north and east"}], "drops": [{"drop": 66}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.7}, {"id": 67, "displayName": "Cobblestone Stairs", "name": "stone_stairs", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Cobblestone Stairs", "metadata": 0, "description": "Lower half, facing east"}, {"displayName": "Cobblestone Stairs", "metadata": 1, "description": "Lower half, facing west"}, {"displayName": "Cobblestone Stairs", "metadata": 2, "description": "Lower half, facing south"}, {"displayName": "Cobblestone Stairs", "metadata": 3, "description": "Lower half, facing north"}, {"displayName": "Cobblestone Stairs", "metadata": 4, "description": "Upper half, facing east"}, {"displayName": "Cobblestone Stairs", "metadata": 5, "description": "Upper half, facing west"}, {"displayName": "Cobblestone Stairs", "metadata": 6, "description": "Upper half, facing south"}, {"displayName": "Cobblestone Stairs", "metadata": 7, "description": "Upper half, facing north"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 67}], "transparent": true, "emitLight": 0, "filterLight": 15, "resistance": 6}, {"id": 68, "displayName": "Wall Sign", "name": "wall_sign", "hardness": 1, "stackSize": 16, "diggable": true, "boundingBox": "empty", "material": "wood", "variations": [{"displayName": "Wall Sign", "metadata": 2, "description": "Facing north"}, {"displayName": "Wall Sign", "metadata": 3, "description": "Facing south"}, {"displayName": "Wall Sign", "metadata": 4, "description": "Facing west"}, {"displayName": "Wall Sign", "metadata": 5, "description": "Facing east"}], "drops": [{"drop": 323}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 1}, {"id": 69, "displayName": "Lever", "name": "lever", "hardness": 0.5, "stackSize": 64, "diggable": true, "boundingBox": "empty", "variations": [{"displayName": "Lever", "metadata": 0, "description": "Not activated, Facing down_x"}, {"displayName": "Lever", "metadata": 1, "description": "Not activated, Facing east"}, {"displayName": "Lever", "metadata": 2, "description": "Not activated, Facing west"}, {"displayName": "Lever", "metadata": 3, "description": "Not activated, Facing south"}, {"displayName": "Lever", "metadata": 4, "description": "Not activated, Facing north"}, {"displayName": "Lever", "metadata": 5, "description": "Not activated, Facing up_z"}, {"displayName": "Lever", "metadata": 6, "description": "Not activated, Facing up_x"}, {"displayName": "Lever", "metadata": 7, "description": "Not activated, Facing down_z"}, {"displayName": "Lever", "metadata": 8, "description": "Activated, Facing down_x"}, {"displayName": "Lever", "metadata": 9, "description": "Activated, Facing east"}, {"displayName": "Lever", "metadata": 10, "description": "Activated, Facing west"}, {"displayName": "Lever", "metadata": 11, "description": "Activated, Facing south"}, {"displayName": "Lever", "metadata": 12, "description": "Activated, Facing north"}, {"displayName": "Lever", "metadata": 13, "description": "Activated, Facing up_z"}, {"displayName": "Lever", "metadata": 14, "description": "Activated, Facing up_x"}, {"displayName": "Lever", "metadata": 15, "description": "Activated, Facing down_z"}], "drops": [{"drop": 69}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.5}, {"id": 70, "displayName": "Stone Pressure Plate", "name": "stone_pressure_plate", "hardness": 0.5, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "rock", "variations": [{"displayName": "Stone Pressure Plate", "metadata": 0, "description": "Not actived"}, {"displayName": "Stone Pressure Plate", "metadata": 1, "description": "Activated"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 70}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.5}, {"id": 71, "displayName": "Iron Door", "name": "iron_door", "hardness": 5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Iron Door", "metadata": 0, "description": "Lower half, facing east"}, {"displayName": "Iron Door", "metadata": 1, "description": "Lower half, facing south"}, {"displayName": "Iron Door", "metadata": 2, "description": "Lower half, facing west"}, {"displayName": "Iron Door", "metadata": 3, "description": "Lower half, facing north"}, {"displayName": "Iron Door", "metadata": 4, "description": "Lower half, facing east, opened"}, {"displayName": "Iron Door", "metadata": 5, "description": "Lower half, facing south, opened"}, {"displayName": "Iron Door", "metadata": 6, "description": "Lower half, facing west, opened"}, {"displayName": "Iron Door", "metadata": 7, "description": "Lower half, facing north, opened"}, {"displayName": "Iron Door", "metadata": 8, "description": "Upper half, hinge left"}, {"displayName": "Iron Door", "metadata": 9, "description": "Upper half, hinge right"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 71}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 5}, {"id": 72, "displayName": "Wooden Pressure Plate", "name": "wooden_pressure_plate", "hardness": 0.5, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "wood", "variations": [{"displayName": "Wooden Pressure Plate", "metadata": 0, "description": "Not activated"}, {"displayName": "Wooden Pressure Plate", "metadata": 1, "description": "Activated"}], "drops": [{"drop": 72}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.5}, {"id": 73, "displayName": "Redstone Ore", "name": "redstone_ore", "hardness": 3, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "278": true}, "drops": [{"drop": 331, "minCount": 4, "maxCount": 5}], "transparent": true, "emitLight": 9, "filterLight": 0, "resistance": 3}, {"id": 74, "displayName": "Glowing Redstone Ore", "name": "lit_redstone_ore", "hardness": 3, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "278": true}, "drops": [{"drop": 331, "minCount": 4, "maxCount": 5}], "transparent": true, "emitLight": 9, "filterLight": 0, "resistance": 3}, {"id": 75, "displayName": "Redstone Torch (inactive)", "name": "unlit_redstone_torch", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "variations": [{"metadata": 0, "displayName": "Redstone Torch", "description": "Inactive, facing east (attached to a block to its west)"}, {"metadata": 1, "displayName": "Redstone Torch", "description": "Inactive, facing west (attached to a block to its east)"}, {"metadata": 2, "displayName": "Redstone Torch", "description": "Inactive, facing south (attached to a block to its north)"}, {"metadata": 3, "displayName": "Redstone Torch", "description": "Inactive, facing north (attached to a block to its south)"}, {"metadata": 4, "displayName": "Redstone Torch", "description": "Inactive, facing up (attached to a block beneath it)"}], "drops": [{"drop": 75}], "transparent": true, "emitLight": 7, "filterLight": 0, "resistance": 0}, {"id": 76, "displayName": "Redstone Torch (active)", "name": "redstone_torch", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "variations": [{"metadata": 0, "displayName": "Redstone Torch", "description": "Active, facing east (attached to a block to its west)"}, {"metadata": 1, "displayName": "Redstone Torch", "description": "Active, facing west (attached to a block to its east)"}, {"metadata": 2, "displayName": "Redstone Torch", "description": "Active, facing south (attached to a block to its north)"}, {"metadata": 3, "displayName": "Redstone Torch", "description": "Active, facing north (attached to a block to its south)"}, {"metadata": 4, "displayName": "Redstone Torch", "description": "Active, facing up (attached to a block beneath it)"}], "drops": [{"drop": 76}], "transparent": true, "emitLight": 7, "filterLight": 0, "resistance": 0}, {"id": 77, "displayName": "<PERSON>", "name": "stone_button", "hardness": 0.5, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "rock", "drops": [{"drop": 77}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.5}, {"id": 78, "displayName": "Snow", "name": "snow_layer", "hardness": 0.2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "dirt", "variations": [{"displayName": "Snow", "metadata": 0, "description": "1 layer"}, {"displayName": "Snow", "metadata": 1, "description": "2 layers"}, {"displayName": "Snow", "metadata": 2, "description": "3 layers"}, {"displayName": "Snow", "metadata": 3, "description": "4 layers"}, {"displayName": "Snow", "metadata": 4, "description": "5 layers"}, {"displayName": "Snow", "metadata": 5, "description": "6 layers"}, {"displayName": "Snow", "metadata": 6, "description": "7 layers"}, {"displayName": "Snow", "metadata": 7, "description": "8 layers"}], "harvestTools": {"256": true, "269": true, "273": true, "277": true, "284": true}, "drops": [{"drop": 332, "minCount": 2, "maxCount": 9}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.1}, {"id": 79, "displayName": "Ice", "name": "ice", "hardness": 0.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "drops": [], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.5}, {"id": 80, "displayName": "Snow", "name": "snow", "hardness": 0.2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "dirt", "harvestTools": {"256": true, "269": true, "273": true, "277": true, "284": true}, "drops": [{"drop": 332, "minCount": 4}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.2}, {"id": 81, "displayName": "Cactus", "name": "cactus", "hardness": 0.4, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "plant", "drops": [{"drop": 81}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.4}, {"id": 82, "displayName": "<PERSON>", "name": "clay", "hardness": 0.6, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "dirt", "drops": [{"drop": 337, "minCount": 4}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.6}, {"id": 83, "displayName": "Sugar Cane", "name": "reeds", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "plant", "drops": [{"drop": 83}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 84, "displayName": "Jukebox", "name": "jukebox", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "Jukebox", "metadata": 0, "description": "No disc inserted"}, {"displayName": "Jukebox", "metadata": 1, "description": "Contains a disc"}], "drops": [{"drop": 84}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 6}, {"id": 85, "displayName": "Oak Fence", "name": "fence", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "drops": [{"drop": 85}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 86, "displayName": "<PERSON><PERSON><PERSON>", "name": "pumpkin", "hardness": 1, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "plant", "variations": [{"displayName": "<PERSON><PERSON><PERSON>", "metadata": 0, "description": "Facing south"}, {"displayName": "<PERSON><PERSON><PERSON>", "metadata": 1, "description": "Facing west"}, {"displayName": "<PERSON><PERSON><PERSON>", "metadata": 2, "description": "Facing north"}, {"displayName": "<PERSON><PERSON><PERSON>", "metadata": 3, "description": "Facing east"}], "drops": [{"drop": 86}], "transparent": true, "emitLight": 0, "filterLight": 15, "resistance": 1}, {"id": 87, "displayName": "Netherrack", "name": "netherrack", "hardness": 0.4, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 87}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.4}, {"id": 88, "displayName": "Soul Sand", "name": "soul_sand", "hardness": 0.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "dirt", "drops": [{"drop": 88}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.5}, {"id": 89, "displayName": "Glowstone", "name": "glowstone", "hardness": 0.3, "stackSize": 64, "diggable": true, "boundingBox": "block", "drops": [{"drop": 348, "minCount": 2, "maxCount": 4}], "transparent": true, "emitLight": 15, "filterLight": 0, "resistance": 0.3}, {"id": 90, "displayName": "Nether Portal", "name": "portal", "hardness": null, "stackSize": 0, "diggable": false, "boundingBox": "empty", "drops": [], "transparent": true, "emitLight": 11, "filterLight": 0, "resistance": -1}, {"id": 91, "displayName": "<PERSON>'<PERSON>", "name": "lit_pumpkin", "hardness": 1, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "plant", "variations": [{"displayName": "<PERSON><PERSON><PERSON>", "metadata": 0, "description": "Facing south"}, {"displayName": "<PERSON><PERSON><PERSON>", "metadata": 1, "description": "Facing west"}, {"displayName": "<PERSON><PERSON><PERSON>", "metadata": 2, "description": "Facing north"}, {"displayName": "<PERSON><PERSON><PERSON>", "metadata": 3, "description": "Facing east"}], "drops": [{"drop": 91}], "transparent": true, "emitLight": 15, "filterLight": 15, "resistance": 1}, {"id": 92, "displayName": "Cake", "name": "cake", "hardness": 0.5, "stackSize": 1, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "Cake", "metadata": 0, "description": "Bites: 0"}, {"displayName": "Cake", "metadata": 1, "description": "Bites: 1"}, {"displayName": "Cake", "metadata": 2, "description": "Bites: 2"}, {"displayName": "Cake", "metadata": 3, "description": "Bites: 3"}, {"displayName": "Cake", "metadata": 4, "description": "Bites: 4"}, {"displayName": "Cake", "metadata": 5, "description": "Bites: 5"}, {"displayName": "Cake", "metadata": 6, "description": "Bites: 6"}], "drops": [], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.5}, {"id": 93, "displayName": "Redstone Repeater", "name": "unpowered_repeater", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "Redstone Repeater", "metadata": 0, "description": "Inactive, facing south, delay 1"}, {"displayName": "Redstone Repeater", "metadata": 1, "description": "Inactive, facing west, delay 1"}, {"displayName": "Redstone Repeater", "metadata": 2, "description": "Inactive, facing north, delay 1"}, {"displayName": "Redstone Repeater", "metadata": 3, "description": "Inactive, facing east, delay 1"}, {"displayName": "Redstone Repeater", "metadata": 4, "description": "Inactive, facing south, delay 2"}, {"displayName": "Redstone Repeater", "metadata": 5, "description": "Inactive, facing west, delay 2"}, {"displayName": "Redstone Repeater", "metadata": 6, "description": "Inactive, facing north, delay 2"}, {"displayName": "Redstone Repeater", "metadata": 7, "description": "Inactive, facing east, delay 2"}, {"displayName": "Redstone Repeater", "metadata": 8, "description": "Inactive, facing south, delay 3"}, {"displayName": "Redstone Repeater", "metadata": 9, "description": "Inactive, facing west, delay 3"}, {"displayName": "Redstone Repeater", "metadata": 10, "description": "Inactive, facing north, delay 3"}, {"displayName": "Redstone Repeater", "metadata": 11, "description": "Inactive, facing east, delay 3"}, {"displayName": "Redstone Repeater", "metadata": 12, "description": "Inactive, facing south, delay 4"}, {"displayName": "Redstone Repeater", "metadata": 13, "description": "Inactive, facing west, delay 4"}, {"displayName": "Redstone Repeater", "metadata": 14, "description": "Inactive, facing north, delay 4"}, {"displayName": "Redstone Repeater", "metadata": 15, "description": "Inactive, facing east, delay 4"}], "drops": [{"drop": 356}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 94, "displayName": "Redstone Repeater", "name": "powered_repeater", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "Redstone Repeater", "metadata": 0, "description": "Active, facing south, delay 1"}, {"displayName": "Redstone Repeater", "metadata": 1, "description": "Active, facing west, delay 1"}, {"displayName": "Redstone Repeater", "metadata": 2, "description": "Active, facing north, delay 1"}, {"displayName": "Redstone Repeater", "metadata": 3, "description": "Active, facing east, delay 1"}, {"displayName": "Redstone Repeater", "metadata": 4, "description": "Active, facing south, delay 2"}, {"displayName": "Redstone Repeater", "metadata": 5, "description": "Active, facing west, delay 2"}, {"displayName": "Redstone Repeater", "metadata": 6, "description": "Active, facing north, delay 2"}, {"displayName": "Redstone Repeater", "metadata": 7, "description": "Active, facing east, delay 2"}, {"displayName": "Redstone Repeater", "metadata": 8, "description": "Active, facing south, delay 3"}, {"displayName": "Redstone Repeater", "metadata": 9, "description": "Active, facing west, delay 3"}, {"displayName": "Redstone Repeater", "metadata": 10, "description": "Active, facing north, delay 3"}, {"displayName": "Redstone Repeater", "metadata": 11, "description": "Active, facing east, delay 3"}, {"displayName": "Redstone Repeater", "metadata": 12, "description": "Active, facing south, delay 4"}, {"displayName": "Redstone Repeater", "metadata": 13, "description": "Active, facing west, delay 4"}, {"displayName": "Redstone Repeater", "metadata": 14, "description": "Active, facing north, delay 4"}, {"displayName": "Redstone Repeater", "metadata": 15, "description": "Active, facing east, delay 4"}], "drops": [{"drop": 356}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 95, "displayName": "Stained Glass", "name": "stained_glass", "hardness": 0.3, "stackSize": 64, "diggable": true, "boundingBox": "block", "variations": [{"metadata": 0, "displayName": "White Stained Glass"}, {"metadata": 1, "displayName": "Orange Stained Glass"}, {"metadata": 2, "displayName": "Magenta Stained Glass"}, {"metadata": 3, "displayName": "Light Blue Stained Glass"}, {"metadata": 4, "displayName": "Yellow Stained Glass"}, {"metadata": 5, "displayName": "Lime Stained Glass"}, {"metadata": 6, "displayName": "Pink Stained Glass"}, {"metadata": 7, "displayName": "<PERSON> Stained Glass"}, {"metadata": 8, "displayName": "Light Gray Stained Glass"}, {"metadata": 9, "displayName": "<PERSON><PERSON>"}, {"metadata": 10, "displayName": "Purple Stained Glass"}, {"metadata": 11, "displayName": "Blue Stained Glass"}, {"metadata": 12, "displayName": "<PERSON> Stained Glass"}, {"metadata": 13, "displayName": "Green Stained Glass"}, {"metadata": 14, "displayName": "Red Stained Glass"}, {"metadata": 15, "displayName": "Black Stained Glass"}], "drops": [], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.3}, {"id": 96, "displayName": "<PERSON><PERSON>", "name": "trapdoor", "hardness": 3, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "<PERSON><PERSON>", "metadata": 0, "description": "Lower half, facing north"}, {"displayName": "<PERSON><PERSON>", "metadata": 1, "description": "Lower half, facing south"}, {"displayName": "<PERSON><PERSON>", "metadata": 2, "description": "Lower half, facing west"}, {"displayName": "<PERSON><PERSON>", "metadata": 3, "description": "Lower half, facing east"}, {"displayName": "<PERSON><PERSON>", "metadata": 4, "description": "Lower half, facing north, opened"}, {"displayName": "<PERSON><PERSON>", "metadata": 5, "description": "Lower half, facing south, opened"}, {"displayName": "<PERSON><PERSON>", "metadata": 6, "description": "Lower half, facing west, opened"}, {"displayName": "<PERSON><PERSON>", "metadata": 7, "description": "Lower half, facing east, opened"}, {"displayName": "<PERSON><PERSON>", "metadata": 8, "description": "Upper half, facing north"}, {"displayName": "<PERSON><PERSON>", "metadata": 9, "description": "Upper half, facing south"}, {"displayName": "<PERSON><PERSON>", "metadata": 10, "description": "Upper half, facing west"}, {"displayName": "<PERSON><PERSON>", "metadata": 11, "description": "Upper half, facing east"}, {"displayName": "<PERSON><PERSON>", "metadata": 12, "description": "Upper half, facing north, opened"}, {"displayName": "<PERSON><PERSON>", "metadata": 13, "description": "Upper half, facing south, opened"}, {"displayName": "<PERSON><PERSON>", "metadata": 14, "description": "Upper half, facing west, opened"}, {"displayName": "<PERSON><PERSON>", "metadata": 15, "description": "Upper half, facing east, opened"}], "drops": [{"drop": 96}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 97, "displayName": "Monster Egg", "name": "monster_egg", "hardness": 0.75, "stackSize": 64, "diggable": true, "boundingBox": "block", "variations": [{"metadata": 0, "displayName": "Stone Monster Egg"}, {"metadata": 1, "displayName": "Cobblestone Monster Egg"}, {"metadata": 2, "displayName": "Stone Brick Monster Egg"}, {"metadata": 3, "displayName": "<PERSON><PERSON> Brick Monster Egg"}, {"metadata": 4, "displayName": "Cracked Stone Brick Monster Egg"}, {"metadata": 5, "displayName": "Chiseled Stone Brick Monster Egg"}], "drops": [], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.75}, {"id": 98, "displayName": "Stone Bricks", "name": "stonebrick", "hardness": 1.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "variations": [{"metadata": 0, "displayName": "Stone brick"}, {"metadata": 1, "displayName": "Mossy stone brick"}, {"metadata": 2, "displayName": "Cracked stone brick"}, {"metadata": 3, "displayName": "Chiseled stone brick"}], "drops": [{"drop": 98}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 6}, {"id": 99, "displayName": "Mushroom", "name": "brown_mushroom_block", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "Mushroom", "metadata": 0, "description": "Pores on all sides"}, {"displayName": "Mushroom", "metadata": 1, "description": "Cap texture on top, west and north"}, {"displayName": "Mushroom", "metadata": 2, "description": "Cap texture on top and north"}, {"displayName": "Mushroom", "metadata": 3, "description": "Cap texture on top, north and east"}, {"displayName": "Mushroom", "metadata": 4, "description": "Cap texture on top and west"}, {"displayName": "Mushroom", "metadata": 5, "description": "Cap texture on top"}, {"displayName": "Mushroom", "metadata": 6, "description": "Cap texture on top and east"}, {"displayName": "Mushroom", "metadata": 7, "description": "Cap texture on top, south and west"}, {"displayName": "Mushroom", "metadata": 8, "description": "Cap texture on top and south"}, {"displayName": "Mushroom", "metadata": 9, "description": "Cap texture on top, east and south"}, {"displayName": "Mushroom", "metadata": 10, "description": "Stem texture on all four sides, pores on top and bottom"}, {"displayName": "Mushroom", "metadata": 14, "description": "Cap texture on all six sides"}, {"displayName": "Mushroom", "metadata": 15, "description": "Stem texture on all six sides"}], "drops": [{"drop": 40, "minCount": 0, "maxCount": 2}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.2}, {"id": 100, "displayName": "Mushroom", "name": "red_mushroom_block", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "Mushroom", "metadata": 0, "description": "Pores on all sides"}, {"displayName": "Mushroom", "metadata": 1, "description": "Cap texture on top, west and north"}, {"displayName": "Mushroom", "metadata": 2, "description": "Cap texture on top and north"}, {"displayName": "Mushroom", "metadata": 3, "description": "Cap texture on top, north and east"}, {"displayName": "Mushroom", "metadata": 4, "description": "Cap texture on top and west"}, {"displayName": "Mushroom", "metadata": 5, "description": "Cap texture on top"}, {"displayName": "Mushroom", "metadata": 6, "description": "Cap texture on top and east"}, {"displayName": "Mushroom", "metadata": 7, "description": "Cap texture on top, south and west"}, {"displayName": "Mushroom", "metadata": 8, "description": "Cap texture on top and south"}, {"displayName": "Mushroom", "metadata": 9, "description": "Cap texture on top, east and south"}, {"displayName": "Mushroom", "metadata": 10, "description": "Stem texture on all four sides, pores on top and bottom"}, {"displayName": "Mushroom", "metadata": 14, "description": "Cap texture on all six sides"}, {"displayName": "Mushroom", "metadata": 15, "description": "Stem texture on all six sides"}], "drops": [{"drop": 40, "minCount": 0, "maxCount": 2}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.2}, {"id": 101, "displayName": "Iron Bars", "name": "iron_bars", "hardness": 5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 101}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 6}, {"id": 102, "displayName": "Glass Pane", "name": "glass_pane", "hardness": 0.3, "stackSize": 64, "diggable": true, "boundingBox": "block", "drops": [], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.3}, {"id": 103, "displayName": "Melon", "name": "melon_block", "hardness": 1, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "plant", "drops": [{"drop": 360, "minCount": 3, "maxCount": 7}], "transparent": true, "emitLight": 0, "filterLight": 15, "resistance": 1}, {"id": 104, "displayName": "<PERSON><PERSON><PERSON>", "name": "pumpkin_stem", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "plant", "variations": [{"displayName": "<PERSON><PERSON><PERSON>", "metadata": 0, "description": "Age: 0"}, {"displayName": "<PERSON><PERSON><PERSON>", "metadata": 1, "description": "Age: 1"}, {"displayName": "<PERSON><PERSON><PERSON>", "metadata": 2, "description": "Age: 2"}, {"displayName": "<PERSON><PERSON><PERSON>", "metadata": 3, "description": "Age: 3"}, {"displayName": "<PERSON><PERSON><PERSON>", "metadata": 4, "description": "Age: 4"}, {"displayName": "<PERSON><PERSON><PERSON>", "metadata": 5, "description": "Age: 5"}, {"displayName": "<PERSON><PERSON><PERSON>", "metadata": 6, "description": "Age: 6"}, {"displayName": "<PERSON><PERSON><PERSON>", "metadata": 7, "description": "Age: 7"}], "drops": [{"drop": 361, "minCount": 0, "maxCount": 3}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 105, "displayName": "Melon Stem", "name": "melon_stem", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "plant", "variations": [{"displayName": "Melon Stem", "metadata": 0, "description": "Age: 0"}, {"displayName": "Melon Stem", "metadata": 1, "description": "Age: 1"}, {"displayName": "Melon Stem", "metadata": 2, "description": "Age: 2"}, {"displayName": "Melon Stem", "metadata": 3, "description": "Age: 3"}, {"displayName": "Melon Stem", "metadata": 4, "description": "Age: 4"}, {"displayName": "Melon Stem", "metadata": 5, "description": "Age: 5"}, {"displayName": "Melon Stem", "metadata": 6, "description": "Age: 6"}, {"displayName": "Melon Stem", "metadata": 7, "description": "Age: 7"}], "drops": [{"drop": 362, "minCount": 0, "maxCount": 3}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 106, "displayName": "Vines", "name": "vine", "hardness": 0.2, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "plant", "variations": [{"metadata": 0, "displayName": "Vines", "description": "Facing up (attached to a block beneath it)"}, {"metadata": 1, "displayName": "Vines", "description": "Facing north (attached to a block to its south)"}, {"metadata": 2, "displayName": "Vines", "description": "Facing east (attached to a block to its west)"}, {"metadata": 4, "displayName": "Vines", "description": "Facing south (attached to a block to its north)"}, {"metadata": 8, "displayName": "Vines", "description": "Facing west (attached to a block to its east)"}], "drops": [{"drop": 106}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.2}, {"id": 107, "displayName": "Fence Gate", "name": "fence_gate", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "Fence Gate", "metadata": 0, "description": "Closed, facing south"}, {"displayName": "Fence Gate", "metadata": 1, "description": "Closed, facing west"}, {"displayName": "Fence Gate", "metadata": 2, "description": "Closed, facing north"}, {"displayName": "Fence Gate", "metadata": 3, "description": "Closed, facing east"}, {"displayName": "Fence Gate", "metadata": 4, "description": "Opened, facing south"}, {"displayName": "Fence Gate", "metadata": 5, "description": "Opened, facing west"}, {"displayName": "Fence Gate", "metadata": 6, "description": "Opened, facing north"}, {"displayName": "Fence Gate", "metadata": 7, "description": "Opened, facing east"}], "drops": [{"drop": 107}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 108, "displayName": "Brick Stairs", "name": "brick_stairs", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Brick Stairs", "metadata": 0, "description": "Lower half, facing east"}, {"displayName": "Brick Stairs", "metadata": 1, "description": "Lower half, facing west"}, {"displayName": "Brick Stairs", "metadata": 2, "description": "Lower half, facing south"}, {"displayName": "Brick Stairs", "metadata": 3, "description": "Lower half, facing north"}, {"displayName": "Brick Stairs", "metadata": 4, "description": "Upper half, facing east"}, {"displayName": "Brick Stairs", "metadata": 5, "description": "Upper half, facing west"}, {"displayName": "Brick Stairs", "metadata": 6, "description": "Upper half, facing south"}, {"displayName": "Brick Stairs", "metadata": 7, "description": "Upper half, facing north"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 108}], "transparent": true, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 109, "displayName": "Stone Brick Stairs", "name": "stone_brick_stairs", "hardness": 1.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Stone Brick Stairs", "metadata": 0, "description": "Lower half, facing east"}, {"displayName": "Stone Brick Stairs", "metadata": 1, "description": "Lower half, facing west"}, {"displayName": "Stone Brick Stairs", "metadata": 2, "description": "Lower half, facing south"}, {"displayName": "Stone Brick Stairs", "metadata": 3, "description": "Lower half, facing north"}, {"displayName": "Stone Brick Stairs", "metadata": 4, "description": "Upper half, facing east"}, {"displayName": "Stone Brick Stairs", "metadata": 5, "description": "Upper half, facing west"}, {"displayName": "Stone Brick Stairs", "metadata": 6, "description": "Upper half, facing south"}, {"displayName": "Stone Brick Stairs", "metadata": 7, "description": "Upper half, facing north"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 109}], "transparent": true, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 110, "displayName": "Mycelium", "name": "mycelium", "hardness": 0.6, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "dirt", "drops": [{"drop": {"id": 3, "metadata": 0}}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.6}, {"id": 111, "displayName": "<PERSON>", "name": "waterlily", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "plant", "drops": [{"drop": 111}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 112, "displayName": "Nether Brick", "name": "nether_brick", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 112}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 6}, {"id": 113, "displayName": "Nether Brick Fence", "name": "nether_brick_fence", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 113}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 6}, {"id": 114, "displayName": "Nether Brick Stairs", "name": "nether_brick_stairs", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Nether Brick Stairs", "metadata": 0, "description": "Lower half, facing east"}, {"displayName": "Nether Brick Stairs", "metadata": 1, "description": "Lower half, facing west"}, {"displayName": "Nether Brick Stairs", "metadata": 2, "description": "Lower half, facing south"}, {"displayName": "Nether Brick Stairs", "metadata": 3, "description": "Lower half, facing north"}, {"displayName": "Nether Brick Stairs", "metadata": 4, "description": "Upper half, facing east"}, {"displayName": "Nether Brick Stairs", "metadata": 5, "description": "Upper half, facing west"}, {"displayName": "Nether Brick Stairs", "metadata": 6, "description": "Upper half, facing south"}, {"displayName": "Nether Brick Stairs", "metadata": 7, "description": "Upper half, facing north"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 114}], "transparent": true, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 115, "displayName": "Nether Wart", "name": "nether_wart", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "plant", "variations": [{"displayName": "Nether Wart", "metadata": 0, "description": "Age: 0"}, {"displayName": "Nether Wart", "metadata": 1, "description": "Age: 1"}, {"displayName": "Nether Wart", "metadata": 2, "description": "Age: 2"}, {"displayName": "Nether Wart", "metadata": 3, "description": "Age: 3"}], "drops": [{"drop": 372}, {"drop": 372, "minCount": 2, "maxCount": 4}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 116, "displayName": "Enchantment Table", "name": "enchanting_table", "hardness": 5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 116}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 1200}, {"id": 117, "displayName": "Brewing Stand", "name": "brewing_stand", "hardness": 0.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Brewing Stand", "metadata": 0, "description": "No bottles"}, {"displayName": "Brewing Stand", "metadata": 1, "description": "Has bottle in first slot"}, {"displayName": "Brewing Stand", "metadata": 2, "description": "Has bottle in second slot"}, {"displayName": "Brewing Stand", "metadata": 4, "description": "Has bottle in third slot"}, {"displayName": "Brewing Stand", "metadata": 5, "description": "Has bottle in first and third slots"}, {"displayName": "Brewing Stand", "metadata": 6, "description": "Has bottle in second and third slost"}, {"displayName": "Brewing Stand", "metadata": 7, "description": "Has bottle in first, second and third slots"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 379}], "transparent": true, "emitLight": 1, "filterLight": 0, "resistance": 0.5}, {"id": 118, "displayName": "<PERSON><PERSON><PERSON>", "name": "cauldron", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "<PERSON><PERSON><PERSON>", "metadata": 0, "description": "No water"}, {"displayName": "<PERSON><PERSON><PERSON>", "metadata": 1, "description": "Water level - 1"}, {"displayName": "<PERSON><PERSON><PERSON>", "metadata": 2, "description": "Water level - 2"}, {"displayName": "C<PERSON><PERSON>t", "metadata": 3, "description": "Water level - 3"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 380}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 2}, {"id": 119, "displayName": "End Portal", "name": "end_portal", "hardness": null, "stackSize": 0, "diggable": false, "boundingBox": "empty", "drops": [], "transparent": true, "emitLight": 15, "filterLight": 0, "resistance": 3600000}, {"id": 120, "displayName": "End Portal", "name": "end_portal_frame", "hardness": null, "stackSize": 64, "diggable": false, "boundingBox": "block", "variations": [{"displayName": "End Portal Frame", "metadata": 0, "description": "Facing south, without eye"}, {"displayName": "End Portal Frame", "metadata": 1, "description": "Facing west, without eye"}, {"displayName": "End Portal Frame", "metadata": 2, "description": "Facing north, without eye"}, {"displayName": "End Portal Frame", "metadata": 3, "description": "Facing east, without eye"}, {"displayName": "End Portal Frame", "metadata": 4, "description": "Facing south, with eye"}, {"displayName": "End Portal Frame", "metadata": 5, "description": "Facing west, with eye"}, {"displayName": "End Portal Frame", "metadata": 6, "description": "Facing north, with eye"}, {"displayName": "End Portal Frame", "metadata": 7, "description": "Facing east, with eye"}], "drops": [{"drop": 120}], "transparent": true, "emitLight": 1, "filterLight": 0, "resistance": 3600000}, {"id": 121, "displayName": "End Stone", "name": "end_stone", "hardness": 3, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 121}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 9}, {"id": 122, "displayName": "Dragon Egg", "name": "dragon_egg", "hardness": 3, "stackSize": 64, "diggable": true, "boundingBox": "block", "drops": [{"drop": 122}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 9}, {"id": 123, "displayName": "Redstone Lamp", "name": "redstone_lamp", "hardness": 0.3, "stackSize": 64, "diggable": true, "boundingBox": "block", "drops": [{"drop": 123}], "transparent": true, "emitLight": 15, "filterLight": 0, "resistance": 0.3}, {"id": 124, "displayName": "Redstone Lamp", "name": "lit_redstone_lamp", "hardness": 0.3, "stackSize": 64, "diggable": true, "boundingBox": "block", "drops": [{"drop": 124}], "transparent": true, "emitLight": 15, "filterLight": 0, "resistance": 0.3}, {"id": 125, "displayName": "Double Wooden Slab", "name": "double_wooden_slab", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"metadata": 0, "displayName": "Double Oak Wood Slab"}, {"metadata": 1, "displayName": "Double Spruce Wood Slab"}, {"metadata": 2, "displayName": "Double <PERSON> Wood Slab"}, {"metadata": 3, "displayName": "Double Jungle Wood Slab"}, {"metadata": 4, "displayName": "Double Acacia Wood Slab"}, {"metadata": 5, "displayName": "Double Dark Oak Wood Slab"}], "drops": [{"drop": {"id": 44, "metadata": 0}}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 126, "displayName": "<PERSON><PERSON>", "name": "wooden_slab", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"metadata": 0, "displayName": "Oak Wood Slab"}, {"metadata": 1, "displayName": "Spruce Wood Slab"}, {"metadata": 2, "displayName": "<PERSON> Slab"}, {"metadata": 3, "displayName": "Jungle Wood Slab"}, {"metadata": 4, "displayName": "Acacia <PERSON> Slab"}, {"metadata": 5, "displayName": "Dark Oak Wood Slab"}, {"metadata": 8, "displayName": "Oak Wood Slab", "description": "Upper half"}, {"metadata": 9, "displayName": "Spruce Wood Slab", "description": "Upper half"}, {"metadata": 10, "displayName": "<PERSON> Slab", "description": "Upper half"}, {"metadata": 11, "displayName": "Jungle Wood Slab", "description": "Upper half"}, {"metadata": 12, "displayName": "Acacia <PERSON> Slab", "description": "Upper half"}, {"metadata": 13, "displayName": "Dark Oak Wood Slab", "description": "Upper half"}], "drops": [{"drop": {"id": 44, "metadata": 0}}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 127, "displayName": "Cocoa", "name": "cocoa", "hardness": 0.2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "plant", "drops": [{"drop": {"id": 351, "metadata": 3}}, {"drop": {"id": 351, "metadata": 3}, "minCount": 3}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 128, "displayName": "Sandstone Stairs", "name": "sandstone_stairs", "hardness": 0.8, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Sandstone Stairs", "metadata": 0, "description": "Lower half, facing east"}, {"displayName": "Sandstone Stairs", "metadata": 1, "description": "Lower half, facing west"}, {"displayName": "Sandstone Stairs", "metadata": 2, "description": "Lower half, facing south"}, {"displayName": "Sandstone Stairs", "metadata": 3, "description": "Lower half, facing north"}, {"displayName": "Sandstone Stairs", "metadata": 4, "description": "Upper half, facing east"}, {"displayName": "Sandstone Stairs", "metadata": 5, "description": "Upper half, facing west"}, {"displayName": "Sandstone Stairs", "metadata": 6, "description": "Upper half, facing south"}, {"displayName": "Sandstone Stairs", "metadata": 7, "description": "Upper half, facing north"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 128}], "transparent": true, "emitLight": 0, "filterLight": 15, "resistance": 0.8}, {"id": 129, "displayName": "Emerald Ore", "name": "emerald_ore", "hardness": 3, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "278": true}, "drops": [{"drop": 388}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 3}, {"id": 130, "displayName": "<PERSON><PERSON> Chest", "name": "ender_chest", "hardness": 22.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "<PERSON><PERSON> Chest", "metadata": 2, "description": "Facing north"}, {"displayName": "<PERSON><PERSON> Chest", "metadata": 3, "description": "Facing south"}, {"displayName": "<PERSON><PERSON> Chest", "metadata": 4, "description": "Facing west"}, {"displayName": "<PERSON><PERSON> Chest", "metadata": 5, "description": "Facing east"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 49, "minCount": 8}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 600}, {"id": 131, "displayName": "Tripwire Hook", "name": "tripwire_hook", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "variations": [{"displayName": "Tripwire Hook", "metadata": 0, "description": "Facing south, not attached, not powered"}, {"displayName": "Tripwire Hook", "metadata": 1, "description": "Facing west, not attached, not powered"}, {"displayName": "Tripwire Hook", "metadata": 2, "description": "Facing north, not attached, not powered"}, {"displayName": "Tripwire Hook", "metadata": 3, "description": "Facing east, not attached, not powered"}, {"displayName": "Tripwire Hook", "metadata": 4, "description": "Facing south, attached, not powered"}, {"displayName": "Tripwire Hook", "metadata": 5, "description": "Facing west, attached, not powered"}, {"displayName": "Tripwire Hook", "metadata": 6, "description": "Facing north, attached, not powered"}, {"displayName": "Tripwire Hook", "metadata": 7, "description": "Facing east, attached, not powered"}, {"displayName": "Tripwire Hook", "metadata": 8, "description": "Facing south, attached, powered"}, {"displayName": "Tripwire Hook", "metadata": 9, "description": "Facing west, attached, powered"}, {"displayName": "Tripwire Hook", "metadata": 10, "description": "Facing north, attached, powered"}, {"displayName": "Tripwire Hook", "metadata": 11, "description": "Facing east, attached, powered"}], "drops": [{"drop": 131}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 132, "displayName": "Tripwire", "name": "tripwire", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "variations": [{"displayName": "Tripwire", "metadata": 2, "description": "Not attached, not powered"}, {"displayName": "Tripwire", "metadata": 3, "description": "Not attached, powered"}, {"displayName": "Tripwire", "metadata": 6, "description": "Attached, not powered"}, {"displayName": "Tripwire", "metadata": 7, "description": "Attached, powered"}], "drops": [{"drop": 287}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 133, "displayName": "Block of Emerald", "name": "emerald_block", "hardness": 5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "278": true}, "drops": [{"drop": 133}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 6}, {"id": 134, "displayName": "Spruce Wood Stairs", "name": "spruce_stairs", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "Spruce Wood Stairs", "metadata": 0, "description": "Lower half, facing east"}, {"displayName": "Spruce Wood Stairs", "metadata": 1, "description": "Lower half, facing west"}, {"displayName": "Spruce Wood Stairs", "metadata": 2, "description": "Lower half, facing south"}, {"displayName": "Spruce Wood Stairs", "metadata": 3, "description": "Lower half, facing north"}, {"displayName": "Spruce Wood Stairs", "metadata": 4, "description": "Upper half, facing east"}, {"displayName": "Spruce Wood Stairs", "metadata": 5, "description": "Upper half, facing west"}, {"displayName": "Spruce Wood Stairs", "metadata": 6, "description": "Upper half, facing south"}, {"displayName": "Spruce Wood Stairs", "metadata": 7, "description": "Upper half, facing north"}], "drops": [{"drop": 134}], "transparent": true, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 135, "displayName": "Birch Wood Stairs", "name": "birch_stairs", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "Birch Wood Stairs", "metadata": 0, "description": "Lower half, facing east"}, {"displayName": "Birch Wood Stairs", "metadata": 1, "description": "Lower half, facing west"}, {"displayName": "Birch Wood Stairs", "metadata": 2, "description": "Lower half, facing south"}, {"displayName": "Birch Wood Stairs", "metadata": 3, "description": "Lower half, facing north"}, {"displayName": "Birch Wood Stairs", "metadata": 4, "description": "Upper half, facing east"}, {"displayName": "Birch Wood Stairs", "metadata": 5, "description": "Upper half, facing west"}, {"displayName": "Birch Wood Stairs", "metadata": 6, "description": "Upper half, facing south"}, {"displayName": "Birch Wood Stairs", "metadata": 7, "description": "Upper half, facing north"}], "drops": [{"drop": 135}], "transparent": true, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 136, "displayName": "Jungle Wood Stairs", "name": "jungle_stairs", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "Jungle Wood Stairs", "metadata": 0, "description": "Lower half, facing east"}, {"displayName": "Jungle Wood Stairs", "metadata": 1, "description": "Lower half, facing west"}, {"displayName": "Jungle Wood Stairs", "metadata": 2, "description": "Lower half, facing south"}, {"displayName": "Jungle Wood Stairs", "metadata": 3, "description": "Lower half, facing north"}, {"displayName": "Jungle Wood Stairs", "metadata": 4, "description": "Upper half, facing east"}, {"displayName": "Jungle Wood Stairs", "metadata": 5, "description": "Upper half, facing west"}, {"displayName": "Jungle Wood Stairs", "metadata": 6, "description": "Upper half, facing south"}, {"displayName": "Jungle Wood Stairs", "metadata": 7, "description": "Upper half, facing north"}], "drops": [{"drop": 136}], "transparent": true, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 137, "displayName": "Command Block", "name": "command_block", "hardness": null, "stackSize": 64, "diggable": false, "boundingBox": "block", "variations": [{"displayName": "Command Block", "metadata": 0, "description": "Facing down, unconditional"}, {"displayName": "Command Block", "metadata": 1, "description": "Facing up, unconditional"}, {"displayName": "Command Block", "metadata": 2, "description": "Facing north, unconditional"}, {"displayName": "Command Block", "metadata": 3, "description": "Facing south, unconditional"}, {"displayName": "Command Block", "metadata": 4, "description": "Facing west, unconditional"}, {"displayName": "Command Block", "metadata": 5, "description": "Facing east, unconditional"}, {"displayName": "Command Block", "metadata": 8, "description": "Facing down, conditional"}, {"displayName": "Command Block", "metadata": 9, "description": "Facing up, conditional"}, {"displayName": "Command Block", "metadata": 12, "description": "Facing west, conditional"}, {"displayName": "Command Block", "metadata": 13, "description": "Facing east, conditional"}, {"displayName": "Command Block", "metadata": 14, "description": "Facing north, conditional"}, {"displayName": "Command Block", "metadata": 15, "description": "Facing south, conditional"}], "drops": [], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 3600000}, {"id": 138, "displayName": "Beacon", "name": "beacon", "hardness": 3, "stackSize": 64, "diggable": true, "boundingBox": "block", "drops": [{"drop": 138}], "transparent": true, "emitLight": 15, "filterLight": 0, "resistance": 0}, {"id": 139, "displayName": "Cobblestone Wall", "name": "cobblestone_wall", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "variations": [{"metadata": 0, "displayName": "Cobblestone Wall"}, {"metadata": 1, "displayName": "<PERSON><PERSON>"}], "drops": [{"drop": 139}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 6}, {"id": 140, "displayName": "Flower Pot", "name": "flower_pot", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "block", "drops": [{"drop": 140}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 141, "displayName": "Carrot", "name": "carrots", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "plant", "variations": [{"displayName": "Carrot", "metadata": 0, "description": "Age: 0"}, {"displayName": "Carrot", "metadata": 1, "description": "Age: 1"}, {"displayName": "Carrot", "metadata": 2, "description": "Age: 2"}, {"displayName": "Carrot", "metadata": 3, "description": "Age: 3"}, {"displayName": "Carrot", "metadata": 4, "description": "Age: 4"}, {"displayName": "Carrot", "metadata": 5, "description": "Age: 5"}, {"displayName": "Carrot", "metadata": 6, "description": "Age: 6"}, {"displayName": "Carrot", "metadata": 7, "description": "Age: 7"}], "drops": [{"drop": 141}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 142, "displayName": "Potato", "name": "potatoes", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "plant", "variations": [{"displayName": "Potato", "metadata": 0, "description": "Age: 0"}, {"displayName": "Potato", "metadata": 1, "description": "Age: 1"}, {"displayName": "Potato", "metadata": 2, "description": "Age: 2"}, {"displayName": "Potato", "metadata": 3, "description": "Age: 3"}, {"displayName": "Potato", "metadata": 4, "description": "Age: 4"}, {"displayName": "Potato", "metadata": 5, "description": "Age: 5"}, {"displayName": "Potato", "metadata": 6, "description": "Age: 6"}, {"displayName": "Potato", "metadata": 7, "description": "Age: 7"}], "drops": [{"drop": 142}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 143, "displayName": "<PERSON><PERSON>", "name": "wooden_button", "hardness": 0.5, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "wood", "variations": [{"displayName": "<PERSON><PERSON>", "metadata": 0, "description": "Not activated, Facing down"}, {"displayName": "<PERSON><PERSON>", "metadata": 1, "description": "Not activated, Facing east"}, {"displayName": "<PERSON><PERSON>", "metadata": 2, "description": "Not activated, Facing west"}, {"displayName": "<PERSON><PERSON>", "metadata": 3, "description": "Not activated, Facing south"}, {"displayName": "<PERSON><PERSON>", "metadata": 4, "description": "Not activated, Facing north"}, {"displayName": "<PERSON><PERSON>", "metadata": 5, "description": "Not activated, Facing up"}, {"displayName": "<PERSON><PERSON>", "metadata": 8, "description": "Activated, Facing down"}, {"displayName": "<PERSON><PERSON>", "metadata": 9, "description": "Activated, Facing east"}, {"displayName": "<PERSON><PERSON>", "metadata": 10, "description": "Activated, Facing west"}, {"displayName": "<PERSON><PERSON>", "metadata": 11, "description": "Activated, Facing south"}, {"displayName": "<PERSON><PERSON>", "metadata": 12, "description": "Activated, Facing north"}, {"displayName": "<PERSON><PERSON>", "metadata": 13, "description": "Activated, Facing up"}], "drops": [{"drop": 143}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.5}, {"id": 144, "displayName": "Mob head", "name": "skull", "hardness": 1, "stackSize": 64, "diggable": true, "boundingBox": "block", "variations": [{"metadata": 0, "displayName": "Skeleton Skull"}, {"metadata": 1, "displayName": "Wither Skeleton Skull"}, {"metadata": 2, "displayName": "Zombie Head"}, {"metadata": 3, "displayName": "Head"}, {"metadata": 4, "displayName": "Creeper Head"}], "drops": [{"drop": 144}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 1}, {"id": 145, "displayName": "An<PERSON>", "name": "anvil", "hardness": 5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "variations": [{"metadata": 0, "displayName": "An<PERSON>"}, {"metadata": 1, "displayName": "Slightly Damaged Anvil"}, {"metadata": 2, "displayName": "Very Damaged Anvil"}, {"metadata": 3, "displayName": "An<PERSON>", "description": "North/South"}, {"metadata": 4, "displayName": "An<PERSON> ", "description": "East/West"}, {"metadata": 5, "displayName": "An<PERSON>", "description": "South/North"}, {"metadata": 6, "displayName": "An<PERSON>", "description": "West/East"}, {"metadata": 7, "displayName": "Slightly Damaged Anvil", "description": "North/South"}, {"metadata": 8, "displayName": "Slightly Damaged Anvil", "description": "East/West"}, {"metadata": 9, "displayName": "Slightly Damaged Anvil", "description": "West/East"}, {"metadata": 10, "displayName": "Slightly Damaged Anvil", "description": "South/North"}, {"metadata": 11, "displayName": "Very Damaged Anvil", "description": "North/South"}, {"metadata": 12, "displayName": "Very Damaged Anvil", "description": "East/West"}, {"metadata": 13, "displayName": "Very Damaged Anvil", "description": "West/East"}, {"metadata": 14, "displayName": "Very Damaged Anvil", "description": "South/North"}], "drops": [{"drop": 145}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 1200}, {"id": 146, "displayName": "Trapped Chest", "name": "trapped_chest", "hardness": 2.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "Trapped Chest", "metadata": 2, "description": "Facing north"}, {"displayName": "Trapped Chest", "metadata": 3, "description": "Facing south"}, {"displayName": "Trapped Chest", "metadata": 4, "description": "Facing west"}, {"displayName": "Trapped Chest", "metadata": 5, "description": "Facing east"}], "drops": [{"drop": 146}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 2.5}, {"id": 147, "displayName": "Weighted Pressure Plate (Light)", "name": "light_weighted_pressure_plate", "hardness": 0.5, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "rock", "variations": [{"displayName": "Weighted Pressure Plate (Light)", "metadata": 0, "description": "Redstone power level - 0"}, {"displayName": "Weighted Pressure Plate (Light)", "metadata": 1, "description": "Redstone power level - 1"}, {"displayName": "Weighted Pressure Plate (Light)", "metadata": 2, "description": "Redstone power level - 2"}, {"displayName": "Weighted Pressure Plate (Light)", "metadata": 3, "description": "Redstone power level - 3"}, {"displayName": "Weighted Pressure Plate (Light)", "metadata": 4, "description": "Redstone power level - 4"}, {"displayName": "Weighted Pressure Plate (Light)", "metadata": 5, "description": "Redstone power level - 5"}, {"displayName": "Weighted Pressure Plate (Light)", "metadata": 6, "description": "Redstone power level - 6"}, {"displayName": "Weighted Pressure Plate (Light)", "metadata": 7, "description": "Redstone power level - 7"}, {"displayName": "Weighted Pressure Plate (Light)", "metadata": 8, "description": "Redstone power level - 8"}, {"displayName": "Weighted Pressure Plate (Light)", "metadata": 9, "description": "Redstone power level - 9"}, {"displayName": "Weighted Pressure Plate (Light)", "metadata": 10, "description": "Redstone power level - 10"}, {"displayName": "Weighted Pressure Plate (Light)", "metadata": 11, "description": "Redstone power level - 11"}, {"displayName": "Weighted Pressure Plate (Light)", "metadata": 12, "description": "Redstone power level - 12"}, {"displayName": "Weighted Pressure Plate (Light)", "metadata": 13, "description": "Redstone power level - 13"}, {"displayName": "Weighted Pressure Plate (Light)", "metadata": 14, "description": "Redstone power level - 14"}, {"displayName": "Weighted Pressure Plate (Light)", "metadata": 15, "description": "Redstone power level - 15"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 147}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.5}, {"id": 148, "displayName": "Weighted Pressure Plate (Heavy)", "name": "heavy_weighted_pressure_plate", "hardness": 0.5, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "rock", "variations": [{"displayName": "Weighted Pressure Plate (Heavy)", "metadata": 0, "description": "Redstone power level - 0"}, {"displayName": "Weighted Pressure Plate (Heavy)", "metadata": 1, "description": "Redstone power level - 1"}, {"displayName": "Weighted Pressure Plate (Heavy)", "metadata": 2, "description": "Redstone power level - 2"}, {"displayName": "Weighted Pressure Plate (Heavy)", "metadata": 3, "description": "Redstone power level - 3"}, {"displayName": "Weighted Pressure Plate (Heavy)", "metadata": 4, "description": "Redstone power level - 4"}, {"displayName": "Weighted Pressure Plate (Heavy)", "metadata": 5, "description": "Redstone power level - 5"}, {"displayName": "Weighted Pressure Plate (Heavy)", "metadata": 6, "description": "Redstone power level - 6"}, {"displayName": "Weighted Pressure Plate (Heavy)", "metadata": 7, "description": "Redstone power level - 7"}, {"displayName": "Weighted Pressure Plate (Heavy)", "metadata": 8, "description": "Redstone power level - 8"}, {"displayName": "Weighted Pressure Plate (Heavy)", "metadata": 9, "description": "Redstone power level - 9"}, {"displayName": "Weighted Pressure Plate (Heavy)", "metadata": 10, "description": "Redstone power level - 10"}, {"displayName": "Weighted Pressure Plate (Heavy)", "metadata": 11, "description": "Redstone power level - 11"}, {"displayName": "Weighted Pressure Plate (Heavy)", "metadata": 12, "description": "Redstone power level - 12"}, {"displayName": "Weighted Pressure Plate (Heavy)", "metadata": 13, "description": "Redstone power level - 13"}, {"displayName": "Weighted Pressure Plate (Heavy)", "metadata": 14, "description": "Redstone power level - 14"}, {"displayName": "Weighted Pressure Plate (Heavy)", "metadata": 15, "description": "Redstone power level - 15"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 148}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.5}, {"id": 149, "displayName": "Redstone Comparator", "name": "unpowered_comparator", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "Redstone Comparator", "metadata": 0, "description": "Facing south, mode: compare"}, {"displayName": "Redstone Comparator", "metadata": 1, "description": "Facing west, mode: compare"}, {"displayName": "Redstone Comparator", "metadata": 2, "description": "Facing north, mode: compare"}, {"displayName": "Redstone Comparator", "metadata": 3, "description": "Facing east, mode: compare"}, {"displayName": "Redstone Comparator", "metadata": 4, "description": "Facing south, mode: subtract"}, {"displayName": "Redstone Comparator", "metadata": 5, "description": "Facing west, mode: subtract"}, {"displayName": "Redstone Comparator", "metadata": 6, "description": "Facing north, mode: subtract"}, {"displayName": "Redstone Comparator", "metadata": 7, "description": "Facing east, mode: subtract"}], "drops": [{"drop": 404}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 150, "displayName": "Redstone Comparator (deprecated)", "name": "powered_comparator", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "block", "drops": [{"drop": 404}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 151, "displayName": "Daylight Sensor", "name": "daylight_detector", "hardness": 0.2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "Daylight Sensor", "metadata": 0, "description": "Redstone power level - 0"}, {"displayName": "Daylight Sensor", "metadata": 1, "description": "Redstone power level - 1"}, {"displayName": "Daylight Sensor", "metadata": 2, "description": "Redstone power level - 2"}, {"displayName": "Daylight Sensor", "metadata": 3, "description": "Redstone power level - 3"}, {"displayName": "Daylight Sensor", "metadata": 4, "description": "Redstone power level - 4"}, {"displayName": "Daylight Sensor", "metadata": 5, "description": "Redstone power level - 5"}, {"displayName": "Daylight Sensor", "metadata": 6, "description": "Redstone power level - 6"}, {"displayName": "Daylight Sensor", "metadata": 7, "description": "Redstone power level - 7"}, {"displayName": "Daylight Sensor", "metadata": 8, "description": "Redstone power level - 8"}, {"displayName": "Daylight Sensor", "metadata": 9, "description": "Redstone power level - 9"}, {"displayName": "Daylight Sensor", "metadata": 10, "description": "Redstone power level - 10"}, {"displayName": "Daylight Sensor", "metadata": 11, "description": "Redstone power level - 11"}, {"displayName": "Daylight Sensor", "metadata": 12, "description": "Redstone power level - 12"}, {"displayName": "Daylight Sensor", "metadata": 13, "description": "Redstone power level - 13"}, {"displayName": "Daylight Sensor", "metadata": 14, "description": "Redstone power level - 14"}, {"displayName": "Daylight Sensor", "metadata": 15, "description": "Redstone power level - 15"}], "drops": [{"drop": 151}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 152, "displayName": "Block of Redstone", "name": "redstone_block", "hardness": 5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 152}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 6}, {"id": 153, "displayName": "<PERSON><PERSON>", "name": "quartz_ore", "hardness": 3, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 406}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 3}, {"id": 154, "displayName": "<PERSON>", "name": "hopper", "hardness": 3, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "<PERSON>", "metadata": 0, "description": "Facing down"}, {"displayName": "<PERSON>", "metadata": 2, "description": "Facing north"}, {"displayName": "<PERSON>", "metadata": 3, "description": "Facing south"}, {"displayName": "<PERSON>", "metadata": 4, "description": "Facing west"}, {"displayName": "<PERSON>", "metadata": 5, "description": "Facing east"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 154}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 4.8}, {"id": 155, "displayName": "Block of Quartz", "name": "quartz_block", "hardness": 0.8, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "variations": [{"metadata": 0, "displayName": "Block of Quartz"}, {"metadata": 1, "displayName": "Chiseled Quartz Block"}, {"metadata": 2, "displayName": "Pillar Quartz Block", "description": "Vertical"}, {"metadata": 3, "displayName": "Pillar Quartz Block", "description": "North-south"}, {"metadata": 4, "displayName": "Pillar Quartz Block", "description": "East-west"}], "drops": [{"drop": 155}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.8}, {"id": 156, "displayName": "Quartz Stairs", "name": "quartz_stairs", "hardness": 0.8, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Quartz Stairs", "metadata": 0, "description": "Lower half, facing east"}, {"displayName": "Quartz Stairs", "metadata": 1, "description": "Lower half, facing west"}, {"displayName": "Quartz Stairs", "metadata": 2, "description": "Lower half, facing south"}, {"displayName": "Quartz Stairs", "metadata": 3, "description": "Lower half, facing north"}, {"displayName": "Quartz Stairs", "metadata": 4, "description": "Upper half, facing east"}, {"displayName": "Quartz Stairs", "metadata": 5, "description": "Upper half, facing west"}, {"displayName": "Quartz Stairs", "metadata": 6, "description": "Upper half, facing south"}, {"displayName": "Quartz Stairs", "metadata": 7, "description": "Upper half, facing north"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 156}], "transparent": true, "emitLight": 0, "filterLight": 15, "resistance": 0.8}, {"id": 157, "displayName": "Activator Rail", "name": "activator_rail", "hardness": 0.7, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "rock", "variations": [{"displayName": "Activator Rail", "metadata": 0, "description": "Not activated, shape: north and south"}, {"displayName": "Activator Rail", "metadata": 1, "description": "Not activated, shape: west and east"}, {"displayName": "Activator Rail", "metadata": 2, "description": "Not activated, shape: ascending to east"}, {"displayName": "Activator Rail", "metadata": 3, "description": "Not activated, shape: ascending to west"}, {"displayName": "Activator Rail", "metadata": 4, "description": "Not activated, shape: ascending to north"}, {"displayName": "Activator Rail", "metadata": 5, "description": "Not activated, shape: ascending to south"}, {"displayName": "Activator Rail", "metadata": 8, "description": "Activated, shape: north and south"}, {"displayName": "Activator Rail", "metadata": 9, "description": "Activated, shape: west and east"}, {"displayName": "Activator Rail", "metadata": 10, "description": "Activated, shape: ascending to east"}, {"displayName": "Activator Rail", "metadata": 11, "description": "Activated, shape: ascending to west"}, {"displayName": "Activator Rail", "metadata": 12, "description": "Activated, shape: ascending to north"}, {"displayName": "Activator Rail", "metadata": 13, "description": "Activated, shape: ascending to south"}], "drops": [{"drop": 157}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.7}, {"id": 158, "displayName": "Dropper", "name": "dropper", "hardness": 3.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Dropper", "metadata": 0, "description": "Facing down"}, {"displayName": "Dropper", "metadata": 1, "description": "Facing up"}, {"displayName": "Dropper", "metadata": 2, "description": "Facing north"}, {"displayName": "Dropper", "metadata": 3, "description": "Facing south"}, {"displayName": "Dropper", "metadata": 4, "description": "Facing west"}, {"displayName": "Dropper", "metadata": 5, "description": "Facing east"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 158}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 3.5}, {"id": 159, "displayName": "Stained Clay", "name": "stained_hardened_clay", "hardness": 1.25, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "dirt", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "variations": [{"metadata": 0, "displayName": "White hardened clay"}, {"metadata": 1, "displayName": "Orange hardened clay"}, {"metadata": 2, "displayName": "Magenta hardened clay"}, {"metadata": 3, "displayName": "Light blue hardened clay"}, {"metadata": 4, "displayName": "Yellow hardened clay"}, {"metadata": 5, "displayName": "Lime hardened clay"}, {"metadata": 6, "displayName": "Pink hardened clay"}, {"metadata": 7, "displayName": "Gray hardened clay"}, {"metadata": 8, "displayName": "Light gray hardened clay"}, {"metadata": 9, "displayName": "Cyan hardened clay"}, {"metadata": 10, "displayName": "Purple hardened clay"}, {"metadata": 11, "displayName": "Blue hardened clay"}, {"metadata": 12, "displayName": "Brown hardened clay"}, {"metadata": 13, "displayName": "Green hardened clay"}, {"metadata": 14, "displayName": "Red hardened clay"}, {"metadata": 15, "displayName": "Black hardened clay"}], "drops": [{"drop": 159}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 4.2}, {"id": 160, "displayName": "Stained Glass Pane", "name": "stained_glass_pane", "hardness": 0.3, "stackSize": 64, "diggable": true, "boundingBox": "block", "variations": [{"metadata": 0, "displayName": "White Stained Glass Pane"}, {"metadata": 1, "displayName": "Orange Stained Glass Pane"}, {"metadata": 2, "displayName": "Magenta Stained Glass Pane"}, {"metadata": 3, "displayName": "Light Blue Stained Glass Pane"}, {"metadata": 4, "displayName": "Yellow Stained Glass Pane"}, {"metadata": 5, "displayName": "Lime Stained Glass Pane"}, {"metadata": 6, "displayName": "Pink Stained Glass Pane"}, {"metadata": 7, "displayName": "Gray Stained Glass Pane"}, {"metadata": 8, "displayName": "Light Gray Stained Glass Pane"}, {"metadata": 9, "displayName": "<PERSON><PERSON> Stained Glass Pane"}, {"metadata": 10, "displayName": "Purple Stained Glass Pane"}, {"metadata": 11, "displayName": "Blue Stained Glass Pane"}, {"metadata": 12, "displayName": "<PERSON> Stained Glass Pane"}, {"metadata": 13, "displayName": "Green Stained Glass Pane"}, {"metadata": 14, "displayName": "Red Stained Glass Pane"}, {"metadata": 15, "displayName": "Black Stained Glass Pane"}], "drops": [], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.3}, {"id": 161, "displayName": "Leaves (Acacia/Dark Oak)", "name": "leaves2", "hardness": 0.2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "plant", "variations": [{"metadata": 0, "displayName": "Acacia Leaves"}, {"metadata": 1, "displayName": "Dark Oak Leaves"}, {"metadata": 4, "displayName": "Acacia Leaves", "description": "No decay"}, {"metadata": 5, "displayName": "Dark Oak Leaves", "description": "No decay"}, {"metadata": 8, "displayName": "Acacia Leaves", "description": "Check decay"}, {"metadata": 9, "displayName": "Dark Oak Leaves", "description": "Check decay"}, {"metadata": 12, "displayName": "Acacia Leaves", "description": "No decay and check decay"}, {"metadata": 13, "displayName": "Dark Oak Leaves", "description": "No decay and check decay"}], "drops": [{"drop": 6, "minCount": 0, "maxCount": 1}, {"drop": 260, "minCount": 0, "maxCount": 1}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 162, "displayName": "Wood (Acacia/Dark Oak)", "name": "log2", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "Acacia Wood", "metadata": 0, "description": "Facing up and down"}, {"displayName": "Dark Oak Wood", "metadata": 1, "description": "Facing up and down"}, {"displayName": "Acacia Wood", "metadata": 8, "description": "Facing north and south"}, {"displayName": "Dark Oak Wood", "metadata": 9, "description": "Facing north and south"}, {"displayName": "Acacia Wood", "metadata": 4, "description": "Facing west and east"}, {"displayName": "Dark Oak Wood", "metadata": 5, "description": "Facing west and east"}], "drops": [{"drop": 162}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 163, "displayName": "Acacia Wood Stairs", "name": "acacia_stairs", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "Acacia Wood Stairs", "metadata": 0, "description": "Lower half, facing east"}, {"displayName": "Acacia Wood Stairs", "metadata": 1, "description": "Lower half, facing west"}, {"displayName": "Acacia Wood Stairs", "metadata": 2, "description": "Lower half, facing south"}, {"displayName": "Acacia Wood Stairs", "metadata": 3, "description": "Lower half, facing north"}, {"displayName": "Acacia Wood Stairs", "metadata": 4, "description": "Upper half, facing east"}, {"displayName": "Acacia Wood Stairs", "metadata": 5, "description": "Upper half, facing west"}, {"displayName": "Acacia Wood Stairs", "metadata": 6, "description": "Upper half, facing south"}, {"displayName": "Acacia Wood Stairs", "metadata": 7, "description": "Upper half, facing north"}], "drops": [{"drop": 163}], "transparent": true, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 164, "displayName": "Dark Oak Wood Stairs", "name": "dark_oak_stairs", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "Dark Oak Wood Stairs", "metadata": 0, "description": "Lower half, facing east"}, {"displayName": "Dark Oak Wood Stairs", "metadata": 1, "description": "Lower half, facing west"}, {"displayName": "Dark Oak Wood Stairs", "metadata": 2, "description": "Lower half, facing south"}, {"displayName": "Dark Oak Wood Stairs", "metadata": 3, "description": "Lower half, facing north"}, {"displayName": "Dark Oak Wood Stairs", "metadata": 4, "description": "Upper half, facing east"}, {"displayName": "Dark Oak Wood Stairs", "metadata": 5, "description": "Upper half, facing west"}, {"displayName": "Dark Oak Wood Stairs", "metadata": 6, "description": "Upper half, facing south"}, {"displayName": "Dark Oak Wood Stairs", "metadata": 7, "description": "Upper half, facing north"}], "drops": [{"drop": 164}], "transparent": true, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 165, "displayName": "Slime Block", "name": "slime", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "block", "drops": [{"drop": 165}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 166, "displayName": "Barrier", "name": "barrier", "hardness": null, "stackSize": 64, "diggable": false, "boundingBox": "block", "drops": [], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 167, "displayName": "Iron Trapdoor", "name": "iron_trapdoor", "hardness": 5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Iron Trapdoor", "metadata": 0, "description": "Lower half, facing north"}, {"displayName": "Iron Trapdoor", "metadata": 1, "description": "Lower half, facing south"}, {"displayName": "Iron Trapdoor", "metadata": 2, "description": "Lower half, facing west"}, {"displayName": "Iron Trapdoor", "metadata": 3, "description": "Lower half, facing east"}, {"displayName": "Iron Trapdoor", "metadata": 4, "description": "Lower half, facing north, opened"}, {"displayName": "Iron Trapdoor", "metadata": 5, "description": "Lower half, facing south, opened"}, {"displayName": "Iron Trapdoor", "metadata": 6, "description": "Lower half, facing west, opened"}, {"displayName": "Iron Trapdoor", "metadata": 7, "description": "Lower half, facing east, opened"}, {"displayName": "Iron Trapdoor", "metadata": 8, "description": "Upper half, facing north"}, {"displayName": "Iron Trapdoor", "metadata": 9, "description": "Upper half, facing south"}, {"displayName": "Iron Trapdoor", "metadata": 10, "description": "Upper half, facing west"}, {"displayName": "Iron Trapdoor", "metadata": 11, "description": "Upper half, facing east"}, {"displayName": "Iron Trapdoor", "metadata": 12, "description": "Upper half, facing north, opened"}, {"displayName": "Iron Trapdoor", "metadata": 13, "description": "Upper half, facing south, opened"}, {"displayName": "Iron Trapdoor", "metadata": 14, "description": "Upper half, facing west, opened"}, {"displayName": "Iron Trapdoor", "metadata": 15, "description": "Upper half, facing east, opened"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 167}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 5}, {"id": 168, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "prismarine", "hardness": 1.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "variations": [{"metadata": 0, "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"metadata": 1, "displayName": "Prismarine <PERSON>s"}, {"metadata": 2, "displayName": "<PERSON>"}], "drops": [{"drop": 168}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 6}, {"id": 169, "displayName": "Sea Lantern", "name": "sea_lantern", "hardness": 0.3, "stackSize": 64, "diggable": true, "boundingBox": "block", "drops": [{"drop": 410, "minCount": 2, "maxCount": 3}], "transparent": true, "emitLight": 15, "filterLight": 0, "resistance": 0.3}, {"id": 170, "displayName": "<PERSON>", "name": "hay_block", "hardness": 0.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "<PERSON>", "metadata": 0, "description": "Facing up and down"}, {"displayName": "<PERSON>", "metadata": 4, "description": "Facing west and east"}, {"displayName": "<PERSON>", "metadata": 8, "description": "Facing north and south"}], "drops": [{"drop": 170}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.5}, {"id": 171, "displayName": "Carpet", "name": "carpet", "hardness": 0.1, "stackSize": 64, "diggable": true, "boundingBox": "block", "variations": [{"metadata": 0, "displayName": "White Carpet"}, {"metadata": 1, "displayName": "Orange Carpet"}, {"metadata": 2, "displayName": "Magenta Carpet"}, {"metadata": 3, "displayName": "Light Blue Carpet"}, {"metadata": 4, "displayName": "Yellow Carpet"}, {"metadata": 5, "displayName": "Lime Carpet"}, {"metadata": 6, "displayName": "Pink Carpet"}, {"metadata": 7, "displayName": "<PERSON> Carpet"}, {"metadata": 8, "displayName": "Light Gray Carpet"}, {"metadata": 9, "displayName": "<PERSON><PERSON>"}, {"metadata": 10, "displayName": "Purple Carpet"}, {"metadata": 11, "displayName": "Blue Carpet"}, {"metadata": 12, "displayName": "Brown Carpet"}, {"metadata": 13, "displayName": "Green Carpet"}, {"metadata": 14, "displayName": "Red Carpet"}, {"metadata": 15, "displayName": "Black Carpet"}], "drops": [{"drop": 171}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.1}, {"id": 172, "displayName": "Hardened Clay", "name": "hardened_clay", "hardness": 1.25, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "dirt", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "variations": [{"metadata": 0, "displayName": "White hardened clay"}, {"metadata": 1, "displayName": "Orange hardened clay"}, {"metadata": 2, "displayName": "Magenta hardened clay"}, {"metadata": 3, "displayName": "Light blue hardened clay"}, {"metadata": 4, "displayName": "Yellow hardened clay"}, {"metadata": 5, "displayName": "Lime hardened clay"}, {"metadata": 6, "displayName": "Pink hardened clay"}, {"metadata": 7, "displayName": "Gray hardened clay"}, {"metadata": 8, "displayName": "Light gray hardened clay"}, {"metadata": 9, "displayName": "Cyan hardened clay"}, {"metadata": 10, "displayName": "Purple hardened clay"}, {"metadata": 11, "displayName": "Blue hardened clay"}, {"metadata": 12, "displayName": "Brown hardened clay"}, {"metadata": 13, "displayName": "Green hardened clay"}, {"metadata": 14, "displayName": "Red hardened clay"}, {"metadata": 15, "displayName": "Black hardened clay"}], "drops": [{"drop": 172}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 4.2}, {"id": 173, "displayName": "Block of Coal", "name": "coal_block", "hardness": 5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 173}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 6}, {"id": 174, "displayName": "Packed Ice", "name": "packed_ice", "hardness": 0.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.5}, {"id": 175, "displayName": "Large Flowers", "name": "double_plant", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "plant", "variations": [{"metadata": 0, "displayName": "Sunflower"}, {"metadata": 1, "displayName": "Lilac"}, {"metadata": 2, "displayName": "Double Tallgrass"}, {"metadata": 3, "displayName": "Large Fern"}, {"metadata": 4, "displayName": "<PERSON>"}, {"metadata": 5, "displayName": "Peony"}, {"metadata": 8, "displayName": "Top Half of any Large Plant; low three bits 0x7 are derived from the block below."}], "drops": [{"drop": 175}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 176, "displayName": "Standing Banner", "name": "standing_banner", "hardness": 1, "stackSize": 16, "diggable": true, "boundingBox": "empty", "material": "wood", "variations": [{"displayName": "Standing Banner", "metadata": 0, "description": "Facing south"}, {"displayName": "Standing Banner", "metadata": 1, "description": "Facing south - south-west"}, {"displayName": "Standing Banner", "metadata": 2, "description": "Facing south-west"}, {"displayName": "Standing Banner", "metadata": 3, "description": "Facing west - south-west"}, {"displayName": "Standing Banner", "metadata": 4, "description": "Facing west"}, {"displayName": "Standing Banner", "metadata": 5, "description": "Facing west - north-west"}, {"displayName": "Standing Banner", "metadata": 6, "description": "Facing north-west"}, {"displayName": "Standing Banner", "metadata": 7, "description": "Facing north - north-west"}, {"displayName": "Standing Banner", "metadata": 8, "description": "Facing north"}, {"displayName": "Standing Banner", "metadata": 9, "description": "Facing north - north-east"}, {"displayName": "Standing Banner", "metadata": 10, "description": "Facing north-east"}, {"displayName": "Standing Banner", "metadata": 11, "description": "Facing east - north-east"}, {"displayName": "Standing Banner", "metadata": 12, "description": "Facing east"}, {"displayName": "Standing Banner", "metadata": 13, "description": "Facing east - south-east"}, {"displayName": "Standing Banner", "metadata": 14, "description": "Facing south-east"}, {"displayName": "Standing Banner", "metadata": 15, "description": "Facing south - south-east"}], "drops": [{"drop": 176}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 1}, {"id": 177, "displayName": "<PERSON>", "name": "wall_banner", "hardness": 1, "stackSize": 16, "diggable": true, "boundingBox": "empty", "material": "wood", "variations": [{"displayName": "<PERSON>", "metadata": 2, "description": "Facing north"}, {"displayName": "<PERSON>", "metadata": 3, "description": "Facing south"}, {"displayName": "<PERSON>", "metadata": 4, "description": "Facing west"}, {"displayName": "<PERSON>", "metadata": 5, "description": "Facing east"}], "drops": [{"drop": 177}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 1}, {"id": 178, "displayName": "Inverted Daylight Sensor", "name": "daylight_detector_inverted", "hardness": 0.2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "Inverted Daylight Sensor", "metadata": 0, "description": "Redstone power level - 0"}, {"displayName": "Inverted Daylight Sensor", "metadata": 1, "description": "Redstone power level - 1"}, {"displayName": "Inverted Daylight Sensor", "metadata": 2, "description": "Redstone power level - 2"}, {"displayName": "Inverted Daylight Sensor", "metadata": 3, "description": "Redstone power level - 3"}, {"displayName": "Inverted Daylight Sensor", "metadata": 4, "description": "Redstone power level - 4"}, {"displayName": "Inverted Daylight Sensor", "metadata": 5, "description": "Redstone power level - 5"}, {"displayName": "Inverted Daylight Sensor", "metadata": 6, "description": "Redstone power level - 6"}, {"displayName": "Inverted Daylight Sensor", "metadata": 7, "description": "Redstone power level - 7"}, {"displayName": "Inverted Daylight Sensor", "metadata": 8, "description": "Redstone power level - 8"}, {"displayName": "Inverted Daylight Sensor", "metadata": 9, "description": "Redstone power level - 9"}, {"displayName": "Inverted Daylight Sensor", "metadata": 10, "description": "Redstone power level - 10"}, {"displayName": "Inverted Daylight Sensor", "metadata": 11, "description": "Redstone power level - 11"}, {"displayName": "Inverted Daylight Sensor", "metadata": 12, "description": "Redstone power level - 12"}, {"displayName": "Inverted Daylight Sensor", "metadata": 13, "description": "Redstone power level - 13"}, {"displayName": "Inverted Daylight Sensor", "metadata": 14, "description": "Redstone power level - 14"}, {"displayName": "Inverted Daylight Sensor", "metadata": 15, "description": "Redstone power level - 15"}], "drops": [{"drop": 178}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 179, "displayName": "Red Sandstone", "name": "red_sandstone", "hardness": 0.8, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "variations": [{"metadata": 0, "displayName": "Red Sandstone"}, {"metadata": 1, "displayName": "Chiseled Red Sandstone"}, {"metadata": 2, "displayName": "Smooth Red Sandstone"}], "drops": [{"drop": 179}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.8}, {"id": 180, "displayName": "Red Sandstone Stairs", "name": "red_sandstone_stairs", "hardness": 0.8, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Red Sandstone Stairs", "metadata": 0, "description": "Lower half, facing east"}, {"displayName": "Red Sandstone Stairs", "metadata": 1, "description": "Lower half, facing west"}, {"displayName": "Red Sandstone Stairs", "metadata": 2, "description": "Lower half, facing south"}, {"displayName": "Red Sandstone Stairs", "metadata": 3, "description": "Lower half, facing north"}, {"displayName": "Red Sandstone Stairs", "metadata": 4, "description": "Upper half, facing east"}, {"displayName": "Red Sandstone Stairs", "metadata": 5, "description": "Upper half, facing west"}, {"displayName": "Red Sandstone Stairs", "metadata": 6, "description": "Upper half, facing south"}, {"displayName": "Red Sandstone Stairs", "metadata": 7, "description": "Upper half, facing north"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 180}], "transparent": true, "emitLight": 0, "filterLight": 15, "resistance": 0.8}, {"id": 181, "displayName": "Double Red Sandstone Slab", "name": "double_stone_slab2", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "variations": [{"metadata": 0, "displayName": "Double Red Sandstone Slab"}, {"metadata": 8, "displayName": "Smooth Double Red Sandstone Slab"}], "drops": [{"drop": {"id": 44, "metadata": 0}}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 6}, {"id": 182, "displayName": "Red Sandstone Slab", "name": "stone_slab2", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "variations": [{"metadata": 0, "displayName": "Red Sandstone Slab"}, {"metadata": 8, "displayName": "Upper Red Sandstone Slab"}], "drops": [{"drop": {"id": 44, "metadata": 0}}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 6}, {"id": 183, "displayName": "Spruce Fence Gate", "name": "spruce_fence_gate", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "drops": [{"drop": 183}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 184, "displayName": "Birch Fence Gate", "name": "birch_fence_gate", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "drops": [{"drop": 184}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 185, "displayName": "Jungle Fence Gate", "name": "jungle_fence_gate", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "drops": [{"drop": 185}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 186, "displayName": "Dark Oak Fence Gate", "name": "dark_oak_fence_gate", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "drops": [{"drop": 186}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 187, "displayName": "Acacia Fence Gate", "name": "acacia_fence_gate", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "drops": [{"drop": 187}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 188, "displayName": "Spruce Fence", "name": "spruce_fence", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "drops": [{"drop": 188}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 189, "displayName": "<PERSON>", "name": "birch_fence", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "drops": [{"drop": 189}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 190, "displayName": "Jungle Fence", "name": "jungle_fence", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "drops": [{"drop": 190}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 191, "displayName": "Dark Oak Fence", "name": "dark_oak_fence", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "drops": [{"drop": 191}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 192, "displayName": "Acacia Fence", "name": "acacia_fence", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "drops": [{"drop": 192}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 193, "displayName": "Spruce Door", "name": "spruce_door", "hardness": 3, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "Spruce Door", "metadata": 0, "description": "Lower half, facing east"}, {"displayName": "Spruce Door", "metadata": 1, "description": "Lower half, facing south"}, {"displayName": "Spruce Door", "metadata": 2, "description": "Lower half, facing west"}, {"displayName": "Spruce Door", "metadata": 3, "description": "Lower half, facing north"}, {"displayName": "Spruce Door", "metadata": 4, "description": "Lower half, facing east, opened"}, {"displayName": "Spruce Door", "metadata": 5, "description": "Lower half, facing south, opened"}, {"displayName": "Spruce Door", "metadata": 6, "description": "Lower half, facing west, opened"}, {"displayName": "Spruce Door", "metadata": 7, "description": "Lower half, facing north, opened"}, {"displayName": "Spruce Door", "metadata": 8, "description": "Upper half, hinge left"}, {"displayName": "Spruce Door", "metadata": 9, "description": "Upper half, hinge right"}], "drops": [{"drop": 193}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 194, "displayName": "<PERSON>", "name": "birch_door", "hardness": 3, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "<PERSON>", "metadata": 0, "description": "Lower half, facing east"}, {"displayName": "<PERSON>", "metadata": 1, "description": "Lower half, facing south"}, {"displayName": "<PERSON>", "metadata": 2, "description": "Lower half, facing west"}, {"displayName": "<PERSON>", "metadata": 3, "description": "Lower half, facing north"}, {"displayName": "<PERSON>", "metadata": 4, "description": "Lower half, facing east, opened"}, {"displayName": "<PERSON>", "metadata": 5, "description": "Lower half, facing south, opened"}, {"displayName": "<PERSON>", "metadata": 6, "description": "Lower half, facing west, opened"}, {"displayName": "<PERSON>", "metadata": 7, "description": "Lower half, facing north, opened"}, {"displayName": "<PERSON>", "metadata": 8, "description": "Upper half, hinge left"}, {"displayName": "<PERSON>", "metadata": 9, "description": "Upper half, hinge right"}], "drops": [{"drop": 194}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 195, "displayName": "Jungle Door", "name": "jungle_door", "hardness": 3, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "Jungle Door", "metadata": 0, "description": "Lower half, facing east"}, {"displayName": "Jungle Door", "metadata": 1, "description": "Lower half, facing south"}, {"displayName": "Jungle Door", "metadata": 2, "description": "Lower half, facing west"}, {"displayName": "Jungle Door", "metadata": 3, "description": "Lower half, facing north"}, {"displayName": "Jungle Door", "metadata": 4, "description": "Lower half, facing east, opened"}, {"displayName": "Jungle Door", "metadata": 5, "description": "Lower half, facing south, opened"}, {"displayName": "Jungle Door", "metadata": 6, "description": "Lower half, facing west, opened"}, {"displayName": "Jungle Door", "metadata": 7, "description": "Lower half, facing north, opened"}, {"displayName": "Jungle Door", "metadata": 8, "description": "Upper half, hinge left"}, {"displayName": "Jungle Door", "metadata": 9, "description": "Upper half, hinge right"}], "drops": [{"drop": 195}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 196, "displayName": "Acacia Door", "name": "acacia_door", "hardness": 3, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "Acacia Door", "metadata": 0, "description": "Lower half, facing east"}, {"displayName": "Acacia Door", "metadata": 1, "description": "Lower half, facing south"}, {"displayName": "Acacia Door", "metadata": 2, "description": "Lower half, facing west"}, {"displayName": "Acacia Door", "metadata": 3, "description": "Lower half, facing north"}, {"displayName": "Acacia Door", "metadata": 4, "description": "Lower half, facing east, opened"}, {"displayName": "Acacia Door", "metadata": 5, "description": "Lower half, facing south, opened"}, {"displayName": "Acacia Door", "metadata": 6, "description": "Lower half, facing west, opened"}, {"displayName": "Acacia Door", "metadata": 7, "description": "Lower half, facing north, opened"}, {"displayName": "Acacia Door", "metadata": 8, "description": "Upper half, hinge left"}, {"displayName": "Acacia Door", "metadata": 9, "description": "Upper half, hinge right"}], "drops": [{"drop": 196}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 197, "displayName": "Dark Oak Door", "name": "dark_oak_door", "hardness": 3, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "wood", "variations": [{"displayName": "Dark Oak Door", "metadata": 0, "description": "Lower half, facing east"}, {"displayName": "Dark Oak Door", "metadata": 1, "description": "Lower half, facing south"}, {"displayName": "Dark Oak Door", "metadata": 2, "description": "Lower half, facing west"}, {"displayName": "Dark Oak Door", "metadata": 3, "description": "Lower half, facing north"}, {"displayName": "Dark Oak Door", "metadata": 4, "description": "Lower half, facing east, opened"}, {"displayName": "Dark Oak Door", "metadata": 5, "description": "Lower half, facing south, opened"}, {"displayName": "Dark Oak Door", "metadata": 6, "description": "Lower half, facing west, opened"}, {"displayName": "Dark Oak Door", "metadata": 7, "description": "Lower half, facing north, opened"}, {"displayName": "Dark Oak Door", "metadata": 8, "description": "Upper half, hinge left"}, {"displayName": "Dark Oak Door", "metadata": 9, "description": "Upper half, hinge right"}], "drops": [{"drop": 197}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 198, "displayName": "End Rod", "name": "end_rod", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "block", "variations": [{"metadata": 0, "displayName": "End Rod", "description": "Facing down"}, {"metadata": 1, "displayName": "End Rod", "description": "Facing up"}, {"metadata": 2, "displayName": "End Rod", "description": "Facing north"}, {"metadata": 3, "displayName": "End Rod", "description": "Facing south"}, {"metadata": 4, "displayName": "End Rod", "description": "Facing west"}, {"metadata": 5, "displayName": "End Rod", "description": "Facing east"}], "drops": [{"drop": 198}], "transparent": false, "emitLight": 14, "filterLight": 15, "resistance": 0}, {"id": 199, "displayName": "Chorus Plant", "name": "chorus_plant", "hardness": 0.4, "stackSize": 64, "diggable": true, "boundingBox": "block", "drops": [{"drop": 432, "minCount": 0, "maxCount": 1}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.4}, {"id": 200, "displayName": "Chorus Flower", "name": "chorus_flower", "hardness": 0.4, "stackSize": 64, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "Chorus Flower", "metadata": 0, "description": "Age: 0"}, {"displayName": "Chorus Flower", "metadata": 1, "description": "Age: 1"}, {"displayName": "Chorus Flower", "metadata": 2, "description": "Age: 2"}, {"displayName": "Chorus Flower", "metadata": 3, "description": "Age: 3"}, {"displayName": "Chorus Flower", "metadata": 4, "description": "Age: 4"}, {"displayName": "Chorus Flower", "metadata": 5, "description": "Age: 5"}], "drops": [{"drop": 200}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.4}, {"id": 201, "displayName": "Purpur Block", "name": "purpur_block", "hardness": 1.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 201}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 6}, {"id": 202, "displayName": "Purpur Pillar", "name": "purpur_pillar", "hardness": 1.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "Purpur Pillar", "metadata": 0, "description": "Facing up and down"}, {"displayName": "Purpur Pillar", "metadata": 4, "description": "Facing west and east"}, {"displayName": "Purpur Pillar", "metadata": 8, "description": "Facing north and south"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 202}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 6}, {"id": 203, "displayName": "Purpur Stairs", "name": "purpur_stairs", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Purpur Stairs", "metadata": 0, "description": "Lower half, facing east"}, {"displayName": "Purpur Stairs", "metadata": 1, "description": "Lower half, facing west"}, {"displayName": "Purpur Stairs", "metadata": 2, "description": "Lower half, facing south"}, {"displayName": "Purpur Stairs", "metadata": 3, "description": "Lower half, facing north"}, {"displayName": "Purpur Stairs", "metadata": 4, "description": "Upper half, facing east"}, {"displayName": "Purpur Stairs", "metadata": 5, "description": "Upper half, facing west"}, {"displayName": "Purpur Stairs", "metadata": 6, "description": "Upper half, facing south"}, {"displayName": "Purpur Stairs", "metadata": 7, "description": "Upper half, facing north"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 203}], "transparent": true, "emitLight": 0, "filterLight": 15, "resistance": 6}, {"id": 204, "displayName": "Purpur Double Slab", "name": "purpur_double_slab", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": {"id": 44, "metadata": 0}}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 6}, {"id": 205, "displayName": "Purpur Slab", "name": "purpur_slab", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"metadata": 0, "displayName": "Purpur Slab", "description": "Lower half"}, {"metadata": 8, "displayName": "Purpur Slab", "description": "Upper half"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": {"id": 44, "metadata": 0}}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 6}, {"id": 206, "displayName": "End Stone Bricks", "name": "end_bricks", "hardness": 0.8, "stackSize": 64, "diggable": true, "boundingBox": "block", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 206}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.8}, {"id": 207, "displayName": "Beetroot Seeds", "name": "beetroots", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "material": "plant", "variations": [{"displayName": "Beetroot Seeds", "metadata": 0, "description": "Age: 0"}, {"displayName": "Beetroot Seeds", "metadata": 1, "description": "Age: 1"}, {"displayName": "Beetroot Seeds", "metadata": 2, "description": "Age: 2"}, {"displayName": "Beetroot Seeds", "metadata": 3, "description": "Age: 3"}], "drops": [], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0}, {"id": 208, "displayName": "Grass Path", "name": "grass_path", "hardness": 0.6, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "dirt", "drops": [], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 0.65}, {"id": 209, "displayName": "End Gateway", "name": "end_gateway", "hardness": null, "stackSize": 0, "diggable": false, "boundingBox": "empty", "drops": [], "transparent": false, "emitLight": 15, "filterLight": 15, "resistance": 3600000}, {"id": 210, "displayName": "Repeating Command Block", "name": "repeating_command_block", "hardness": null, "stackSize": 64, "diggable": false, "boundingBox": "block", "variations": [{"displayName": "Repeating Command Block", "metadata": 0, "description": "Facing down, unconditional"}, {"displayName": "Repeating Command Block", "metadata": 1, "description": "Facing up, unconditional"}, {"displayName": "Repeating Command Block", "metadata": 2, "description": "Facing north, unconditional"}, {"displayName": "Repeating Command Block", "metadata": 3, "description": "Facing south, unconditional"}, {"displayName": "Repeating Command Block", "metadata": 4, "description": "Facing west, unconditional"}, {"displayName": "Repeating Command Block", "metadata": 5, "description": "Facing east, unconditional"}, {"displayName": "Repeating Command Block", "metadata": 8, "description": "Facing down, conditional"}, {"displayName": "Repeating Command Block", "metadata": 9, "description": "Facing up, conditional"}, {"displayName": "Repeating Command Block", "metadata": 12, "description": "Facing west, conditional"}, {"displayName": "Repeating Command Block", "metadata": 13, "description": "Facing east, conditional"}, {"displayName": "Repeating Command Block", "metadata": 14, "description": "Facing north, conditional"}, {"displayName": "Repeating Command Block", "metadata": 15, "description": "Facing south, conditional"}], "drops": [], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 3600000}, {"id": 211, "displayName": "Chain Command Block", "name": "chain_command_block", "hardness": null, "stackSize": 64, "diggable": false, "boundingBox": "block", "variations": [{"displayName": "Chain Command Block", "metadata": 0, "description": "Facing down, unconditional"}, {"displayName": "Chain Command Block", "metadata": 1, "description": "Facing up, unconditional"}, {"displayName": "Chain Command Block", "metadata": 2, "description": "Facing north, unconditional"}, {"displayName": "Chain Command Block", "metadata": 3, "description": "Facing south, unconditional"}, {"displayName": "Chain Command Block", "metadata": 4, "description": "Facing west, unconditional"}, {"displayName": "Chain Command Block", "metadata": 5, "description": "Facing east, unconditional"}, {"displayName": "Chain Command Block", "metadata": 8, "description": "Facing down, conditional"}, {"displayName": "Chain Command Block", "metadata": 9, "description": "Facing up, conditional"}, {"displayName": "Chain Command Block", "metadata": 12, "description": "Facing west, conditional"}, {"displayName": "Chain Command Block", "metadata": 13, "description": "Facing east, conditional"}, {"displayName": "Chain Command Block", "metadata": 14, "description": "Facing north, conditional"}, {"displayName": "Chain Command Block", "metadata": 15, "description": "Facing south, conditional"}], "drops": [], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 3600000}, {"id": 212, "displayName": "Frosted Ice", "name": "frosted_ice", "hardness": 0.5, "stackSize": 0, "diggable": true, "boundingBox": "block", "drops": [], "transparent": true, "emitLight": 0, "filterLight": 2, "resistance": 0.5}, {"id": 213, "displayName": "Magma Block", "name": "magma", "hardness": 0.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 213}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.5}, {"id": 214, "displayName": "Nether Wart Block", "name": "nether_wart_block", "hardness": 1, "stackSize": 64, "diggable": true, "boundingBox": "block", "drops": [{"drop": 214}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 1}, {"id": 215, "displayName": "Red Nether Brick", "name": "red_nether_brick", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 215}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 6}, {"id": 216, "displayName": "Bone Block", "name": "bone_block", "hardness": 2, "stackSize": 64, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "Bone Block", "metadata": 0, "description": "Facing up and down"}, {"displayName": "Bone Block", "metadata": 4, "description": "Facing west and east"}, {"displayName": "Bone Block", "metadata": 8, "description": "Facing north and south"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 216}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 217, "displayName": "Structure Void", "name": "structure_void", "hardness": 0, "stackSize": 64, "diggable": true, "boundingBox": "empty", "variations": [{"metadata": 0, "displayName": "Save"}, {"metadata": 1, "displayName": "Load"}, {"metadata": 2, "displayName": "Corner"}, {"metadata": 3, "displayName": "Data"}], "drops": [], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 218, "displayName": "Observer", "name": "observer", "hardness": 3.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "Observer", "metadata": 0, "description": "Facing down"}, {"displayName": "Observer", "metadata": 1, "description": "Facing up"}, {"displayName": "Observer", "metadata": 2, "description": "Facing north"}, {"displayName": "Observer", "metadata": 3, "description": "Facing south"}, {"displayName": "Observer", "metadata": 4, "description": "Facing west"}, {"displayName": "Observer", "metadata": 5, "description": "Facing east"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 218}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 3}, {"id": 219, "displayName": "White Shulker Box", "name": "white_shulker_box", "hardness": 2, "stackSize": 1, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "White Shulker Box", "metadata": 0, "description": "Facing down"}, {"displayName": "White Shulker Box", "metadata": 1, "description": "Facing up"}, {"displayName": "White Shulker Box", "metadata": 2, "description": "Facing north"}, {"displayName": "White Shulker Box", "metadata": 3, "description": "Facing south"}, {"displayName": "White Shulker Box", "metadata": 4, "description": "Facing west"}, {"displayName": "White Shulker Box", "metadata": 5, "description": "Facing east"}], "drops": [{"drop": 219}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 2}, {"id": 220, "displayName": "Orange Shulker Box", "name": "orange_shulker_box", "hardness": 2, "stackSize": 1, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "Orange Shulker Box", "metadata": 0, "description": "Facing down"}, {"displayName": "Orange Shulker Box", "metadata": 1, "description": "Facing up"}, {"displayName": "Orange Shulker Box", "metadata": 2, "description": "Facing north"}, {"displayName": "Orange Shulker Box", "metadata": 3, "description": "Facing south"}, {"displayName": "Orange Shulker Box", "metadata": 4, "description": "Facing west"}, {"displayName": "Orange Shulker Box", "metadata": 5, "description": "Facing east"}], "drops": [{"drop": 220}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 2}, {"id": 221, "displayName": "<PERSON><PERSON>a <PERSON>er Box", "name": "magenta_shulker_box", "hardness": 2, "stackSize": 1, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "<PERSON><PERSON>a <PERSON>er Box", "metadata": 0, "description": "Facing down"}, {"displayName": "<PERSON><PERSON>a <PERSON>er Box", "metadata": 1, "description": "Facing up"}, {"displayName": "<PERSON><PERSON>a <PERSON>er Box", "metadata": 2, "description": "Facing north"}, {"displayName": "<PERSON><PERSON>a <PERSON>er Box", "metadata": 3, "description": "Facing south"}, {"displayName": "<PERSON><PERSON>a <PERSON>er Box", "metadata": 4, "description": "Facing west"}, {"displayName": "<PERSON><PERSON>a <PERSON>er Box", "metadata": 5, "description": "Facing east"}], "drops": [{"drop": 221}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 2}, {"id": 222, "displayName": "Light Blue Shulker Box", "name": "light_blue_shulker_box", "hardness": 2, "stackSize": 1, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "Light Blue Shulker Box", "metadata": 0, "description": "Facing down"}, {"displayName": "Light Blue Shulker Box", "metadata": 1, "description": "Facing up"}, {"displayName": "Light Blue Shulker Box", "metadata": 2, "description": "Facing north"}, {"displayName": "Light Blue Shulker Box", "metadata": 3, "description": "Facing south"}, {"displayName": "Light Blue Shulker Box", "metadata": 4, "description": "Facing west"}, {"displayName": "Light Blue Shulker Box", "metadata": 5, "description": "Facing east"}], "drops": [{"drop": 222}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 2}, {"id": 223, "displayName": "Yellow Shulker Box", "name": "yellow_shulker_box", "hardness": 2, "stackSize": 1, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "Yellow Shulker Box", "metadata": 0, "description": "Facing down"}, {"displayName": "Yellow Shulker Box", "metadata": 1, "description": "Facing up"}, {"displayName": "Yellow Shulker Box", "metadata": 2, "description": "Facing north"}, {"displayName": "Yellow Shulker Box", "metadata": 3, "description": "Facing south"}, {"displayName": "Yellow Shulker Box", "metadata": 4, "description": "Facing west"}, {"displayName": "Yellow Shulker Box", "metadata": 5, "description": "Facing east"}], "drops": [{"drop": 223}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 2}, {"id": 224, "displayName": "<PERSON>e <PERSON>er Box", "name": "lime_shulker_box", "hardness": 2, "stackSize": 1, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "<PERSON>e <PERSON>er Box", "metadata": 0, "description": "Facing down"}, {"displayName": "<PERSON>e <PERSON>er Box", "metadata": 1, "description": "Facing up"}, {"displayName": "<PERSON>e <PERSON>er Box", "metadata": 2, "description": "Facing north"}, {"displayName": "<PERSON>e <PERSON>er Box", "metadata": 3, "description": "Facing south"}, {"displayName": "<PERSON>e <PERSON>er Box", "metadata": 4, "description": "Facing west"}, {"displayName": "<PERSON>e <PERSON>er Box", "metadata": 5, "description": "Facing east"}], "drops": [{"drop": 224}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 2}, {"id": 225, "displayName": "Pink Shulker Box", "name": "pink_shulker_box", "hardness": 2, "stackSize": 1, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "Pink Shulker Box", "metadata": 0, "description": "Facing down"}, {"displayName": "Pink Shulker Box", "metadata": 1, "description": "Facing up"}, {"displayName": "Pink Shulker Box", "metadata": 2, "description": "Facing north"}, {"displayName": "Pink Shulker Box", "metadata": 3, "description": "Facing south"}, {"displayName": "Pink Shulker Box", "metadata": 4, "description": "Facing west"}, {"displayName": "Pink Shulker Box", "metadata": 5, "description": "Facing east"}], "drops": [{"drop": 225}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 2}, {"id": 226, "displayName": "<PERSON>", "name": "gray_shulker_box", "hardness": 2, "stackSize": 1, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "<PERSON>", "metadata": 0, "description": "Facing down"}, {"displayName": "<PERSON>", "metadata": 1, "description": "Facing up"}, {"displayName": "<PERSON>", "metadata": 2, "description": "Facing north"}, {"displayName": "<PERSON>", "metadata": 3, "description": "Facing south"}, {"displayName": "<PERSON>", "metadata": 4, "description": "Facing west"}, {"displayName": "<PERSON>", "metadata": 5, "description": "Facing east"}], "drops": [{"drop": 226}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 2}, {"id": 227, "displayName": "Light Gray Shulker Box", "name": "light_gray_shulker_box", "hardness": 2, "stackSize": 1, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "Light Gray Shulker Box", "metadata": 0, "description": "Facing down"}, {"displayName": "Light Gray Shulker Box", "metadata": 1, "description": "Facing up"}, {"displayName": "Light Gray Shulker Box", "metadata": 2, "description": "Facing north"}, {"displayName": "Light Gray Shulker Box", "metadata": 3, "description": "Facing south"}, {"displayName": "Light Gray Shulker Box", "metadata": 4, "description": "Facing west"}, {"displayName": "Light Gray Shulker Box", "metadata": 5, "description": "Facing east"}], "drops": [{"drop": 227}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 2}, {"id": 228, "displayName": "<PERSON><PERSON>", "name": "cyan_shulker_box", "hardness": 2, "stackSize": 1, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "<PERSON><PERSON>", "metadata": 0, "description": "Facing down"}, {"displayName": "<PERSON><PERSON>", "metadata": 1, "description": "Facing up"}, {"displayName": "<PERSON><PERSON>", "metadata": 2, "description": "Facing north"}, {"displayName": "<PERSON><PERSON>", "metadata": 3, "description": "Facing south"}, {"displayName": "<PERSON><PERSON>", "metadata": 4, "description": "Facing west"}, {"displayName": "<PERSON><PERSON>", "metadata": 5, "description": "Facing east"}], "drops": [{"drop": 228}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 2}, {"id": 229, "displayName": "Purple Shulker Box", "name": "purple_shulker_box", "hardness": 2, "stackSize": 1, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "Purple Shulker Box", "metadata": 0, "description": "Facing down"}, {"displayName": "Purple Shulker Box", "metadata": 1, "description": "Facing up"}, {"displayName": "Purple Shulker Box", "metadata": 2, "description": "Facing north"}, {"displayName": "Purple Shulker Box", "metadata": 3, "description": "Facing south"}, {"displayName": "Purple Shulker Box", "metadata": 4, "description": "Facing west"}, {"displayName": "Purple Shulker Box", "metadata": 5, "description": "Facing east"}], "drops": [{"drop": 229}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 2}, {"id": 230, "displayName": "Blue Shulker Box", "name": "blue_shulker_box", "hardness": 2, "stackSize": 1, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "Blue Shulker Box", "metadata": 0, "description": "Facing down"}, {"displayName": "Blue Shulker Box", "metadata": 1, "description": "Facing up"}, {"displayName": "Blue Shulker Box", "metadata": 2, "description": "Facing north"}, {"displayName": "Blue Shulker Box", "metadata": 3, "description": "Facing south"}, {"displayName": "Blue Shulker Box", "metadata": 4, "description": "Facing west"}, {"displayName": "Blue Shulker Box", "metadata": 5, "description": "Facing east"}], "drops": [{"drop": 230}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 2}, {"id": 231, "displayName": "<PERSON> Shulker Box", "name": "brown_shulker_box", "hardness": 2, "stackSize": 1, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "<PERSON> Shulker Box", "metadata": 0, "description": "Facing down"}, {"displayName": "<PERSON> Shulker Box", "metadata": 1, "description": "Facing up"}, {"displayName": "<PERSON> Shulker Box", "metadata": 2, "description": "Facing north"}, {"displayName": "<PERSON> Shulker Box", "metadata": 3, "description": "Facing south"}, {"displayName": "<PERSON> Shulker Box", "metadata": 4, "description": "Facing west"}, {"displayName": "<PERSON> Shulker Box", "metadata": 5, "description": "Facing east"}], "drops": [{"drop": 231}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 2}, {"id": 232, "displayName": "Green Shulker Box", "name": "green_shulker_box", "hardness": 2, "stackSize": 1, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "Green Shulker Box", "metadata": 0, "description": "Facing down"}, {"displayName": "Green Shulker Box", "metadata": 1, "description": "Facing up"}, {"displayName": "Green Shulker Box", "metadata": 2, "description": "Facing north"}, {"displayName": "Green Shulker Box", "metadata": 3, "description": "Facing south"}, {"displayName": "Green Shulker Box", "metadata": 4, "description": "Facing west"}, {"displayName": "Green Shulker Box", "metadata": 5, "description": "Facing east"}], "drops": [{"drop": 232}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 2}, {"id": 233, "displayName": "Red Shulker Box", "name": "red_shulker_box", "hardness": 2, "stackSize": 1, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "Red Shulker Box", "metadata": 0, "description": "Facing down"}, {"displayName": "Red Shulker Box", "metadata": 1, "description": "Facing up"}, {"displayName": "Red Shulker Box", "metadata": 2, "description": "Facing north"}, {"displayName": "Red Shulker Box", "metadata": 3, "description": "Facing south"}, {"displayName": "Red Shulker Box", "metadata": 4, "description": "Facing west"}, {"displayName": "Red Shulker Box", "metadata": 5, "description": "Facing east"}], "drops": [{"drop": 233}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 2}, {"id": 234, "displayName": "Black Shulker Box", "name": "black_shulker_box", "hardness": 2, "stackSize": 1, "diggable": true, "boundingBox": "block", "variations": [{"displayName": "Black Shulker Box", "metadata": 0, "description": "Facing down"}, {"displayName": "Black Shulker Box", "metadata": 1, "description": "Facing up"}, {"displayName": "Black Shulker Box", "metadata": 2, "description": "Facing north"}, {"displayName": "Black Shulker Box", "metadata": 3, "description": "Facing south"}, {"displayName": "Black Shulker Box", "metadata": 4, "description": "Facing west"}, {"displayName": "Black Shulker Box", "metadata": 5, "description": "Facing east"}], "drops": [{"drop": 234}], "transparent": true, "emitLight": 0, "filterLight": 0, "resistance": 2}, {"id": 235, "displayName": "White Glazed Terracotta", "name": "white_glazed_terracotta", "hardness": 1.4, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "White Glazed Terracotta", "metadata": 0, "description": "Facing south"}, {"displayName": "White Glazed Terracotta", "metadata": 1, "description": "Facing west"}, {"displayName": "White Glazed Terracotta", "metadata": 2, "description": "Facing north"}, {"displayName": "White Glazed Terracotta", "metadata": 3, "description": "Facing east"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 235}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 236, "displayName": "Orange Glazed Terracotta", "name": "orange_glazed_terracotta", "hardness": 1.4, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Orange Glazed Terracotta", "metadata": 0, "description": "Facing south"}, {"displayName": "Orange Glazed Terracotta", "metadata": 1, "description": "Facing west"}, {"displayName": "Orange Glazed Terracotta", "metadata": 2, "description": "Facing north"}, {"displayName": "Orange Glazed Terracotta", "metadata": 3, "description": "Facing east"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 236}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 237, "displayName": "Magenta Glazed Terracotta", "name": "magenta_glazed_terracotta", "hardness": 1.4, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Magenta Glazed Terracotta", "metadata": 0, "description": "Facing south"}, {"displayName": "Magenta Glazed Terracotta", "metadata": 1, "description": "Facing west"}, {"displayName": "Magenta Glazed Terracotta", "metadata": 2, "description": "Facing north"}, {"displayName": "Magenta Glazed Terracotta", "metadata": 3, "description": "Facing east"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 237}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 238, "displayName": "Light Blue Glazed Terracotta", "name": "light_blue_glazed_terracotta", "hardness": 1.4, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Light Blue Glazed Terracotta", "metadata": 0, "description": "Facing south"}, {"displayName": "Light Blue Glazed Terracotta", "metadata": 1, "description": "Facing west"}, {"displayName": "Light Blue Glazed Terracotta", "metadata": 2, "description": "Facing north"}, {"displayName": "Light Blue Glazed Terracotta", "metadata": 3, "description": "Facing east"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 238}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 239, "displayName": "Yellow Glazed Terracotta", "name": "yellow_glazed_terracotta", "hardness": 1.4, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Yellow Glazed Terracotta", "metadata": 0, "description": "Facing south"}, {"displayName": "Yellow Glazed Terracotta", "metadata": 1, "description": "Facing west"}, {"displayName": "Yellow Glazed Terracotta", "metadata": 2, "description": "Facing north"}, {"displayName": "Yellow Glazed Terracotta", "metadata": 3, "description": "Facing east"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 239}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 240, "displayName": "Lime Glazed Terracotta", "name": "lime_glazed_terracotta", "hardness": 1.4, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Lime Glazed Terracotta", "metadata": 0, "description": "Facing south"}, {"displayName": "Lime Glazed Terracotta", "metadata": 1, "description": "Facing west"}, {"displayName": "Lime Glazed Terracotta", "metadata": 2, "description": "Facing north"}, {"displayName": "Lime Glazed Terracotta", "metadata": 3, "description": "Facing east"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 240}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 241, "displayName": "Pink Glazed Terracotta", "name": "pink_glazed_terracotta", "hardness": 1.4, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Pink Glazed Terracotta", "metadata": 0, "description": "Facing south"}, {"displayName": "Pink Glazed Terracotta", "metadata": 1, "description": "Facing west"}, {"displayName": "Pink Glazed Terracotta", "metadata": 2, "description": "Facing north"}, {"displayName": "Pink Glazed Terracotta", "metadata": 3, "description": "Facing east"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 241}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 242, "displayName": "Gray Glazed Terracotta", "name": "gray_glazed_terracotta", "hardness": 1.4, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Gray Glazed Terracotta", "metadata": 0, "description": "Facing south"}, {"displayName": "Gray Glazed Terracotta", "metadata": 1, "description": "Facing west"}, {"displayName": "Gray Glazed Terracotta", "metadata": 2, "description": "Facing north"}, {"displayName": "Gray Glazed Terracotta", "metadata": 3, "description": "Facing east"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 242}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 243, "displayName": "Light Gray Glazed Terracotta", "name": "light_gray_glazed_terracotta", "hardness": 1.4, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Light Gray Glazed Terracotta", "metadata": 0, "description": "Facing south"}, {"displayName": "Light Gray Glazed Terracotta", "metadata": 1, "description": "Facing west"}, {"displayName": "Light Gray Glazed Terracotta", "metadata": 2, "description": "Facing north"}, {"displayName": "Light Gray Glazed Terracotta", "metadata": 3, "description": "Facing east"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 243}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 244, "displayName": "<PERSON><PERSON>zed Terracotta", "name": "cyan_glazed_terracotta", "hardness": 1.4, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "<PERSON><PERSON>zed Terracotta", "metadata": 0, "description": "Facing south"}, {"displayName": "<PERSON><PERSON>zed Terracotta", "metadata": 1, "description": "Facing west"}, {"displayName": "<PERSON><PERSON>zed Terracotta", "metadata": 2, "description": "Facing north"}, {"displayName": "<PERSON><PERSON>zed Terracotta", "metadata": 3, "description": "Facing east"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 244}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 245, "displayName": "Purple Glazed Terracotta", "name": "purple_glazed_terracotta", "hardness": 1.4, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Purple Glazed Terracotta", "metadata": 0, "description": "Facing south"}, {"displayName": "Purple Glazed Terracotta", "metadata": 1, "description": "Facing west"}, {"displayName": "Purple Glazed Terracotta", "metadata": 2, "description": "Facing north"}, {"displayName": "Purple Glazed Terracotta", "metadata": 3, "description": "Facing east"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 245}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 246, "displayName": "Blue Glazed Terracotta", "name": "blue_glazed_terracotta", "hardness": 1.4, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Blue Glazed Terracotta", "metadata": 0, "description": "Facing south"}, {"displayName": "Blue Glazed Terracotta", "metadata": 1, "description": "Facing west"}, {"displayName": "Blue Glazed Terracotta", "metadata": 2, "description": "Facing north"}, {"displayName": "Blue Glazed Terracotta", "metadata": 3, "description": "Facing east"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 246}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 247, "displayName": "Brown Glazed Terracotta", "name": "brown_glazed_terracotta", "hardness": 1.4, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Brown Glazed Terracotta", "metadata": 0, "description": "Facing south"}, {"displayName": "Brown Glazed Terracotta", "metadata": 1, "description": "Facing west"}, {"displayName": "Brown Glazed Terracotta", "metadata": 2, "description": "Facing north"}, {"displayName": "Brown Glazed Terracotta", "metadata": 3, "description": "Facing east"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 247}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 248, "displayName": "Green Glazed Terracotta", "name": "green_glazed_terracotta", "hardness": 1.4, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Green Glazed Terracotta", "metadata": 0, "description": "Facing south"}, {"displayName": "Green Glazed Terracotta", "metadata": 1, "description": "Facing west"}, {"displayName": "Green Glazed Terracotta", "metadata": 2, "description": "Facing north"}, {"displayName": "Green Glazed Terracotta", "metadata": 3, "description": "Facing east"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 248}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 249, "displayName": "Red Glazed Terracotta", "name": "red_glazed_terracotta", "hardness": 1.4, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Red Glazed Terracotta", "metadata": 0, "description": "Facing south"}, {"displayName": "Red Glazed Terracotta", "metadata": 1, "description": "Facing west"}, {"displayName": "Red Glazed Terracotta", "metadata": 2, "description": "Facing north"}, {"displayName": "Red Glazed Terracotta", "metadata": 3, "description": "Facing east"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 249}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 250, "displayName": "Black Glazed Terracotta", "name": "black_glazed_terracotta", "hardness": 1.4, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "variations": [{"displayName": "Black Glazed Terracotta", "metadata": 0, "description": "Facing south"}, {"displayName": "Black Glazed Terracotta", "metadata": 1, "description": "Facing west"}, {"displayName": "Black Glazed Terracotta", "metadata": 2, "description": "Facing north"}, {"displayName": "Black Glazed Terracotta", "metadata": 3, "description": "Facing east"}], "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "drops": [{"drop": 250}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0}, {"id": 251, "displayName": "Concrete", "name": "concrete", "hardness": 1.8, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "rock", "harvestTools": {"257": true, "270": true, "274": true, "278": true, "285": true}, "variations": [{"metadata": 0, "displayName": "White Concrete"}, {"metadata": 1, "displayName": "Orange Concrete"}, {"metadata": 2, "displayName": "Magenta Concrete"}, {"metadata": 3, "displayName": "Light blue Concrete"}, {"metadata": 4, "displayName": "Yellow Concrete"}, {"metadata": 5, "displayName": "Lime Concrete"}, {"metadata": 6, "displayName": "Pink Concrete"}, {"metadata": 7, "displayName": "<PERSON>"}, {"metadata": 8, "displayName": "Light gray Concrete"}, {"metadata": 9, "displayName": "<PERSON><PERSON>"}, {"metadata": 10, "displayName": "Purple Concrete"}, {"metadata": 11, "displayName": "Blue Concrete"}, {"metadata": 12, "displayName": "<PERSON> Concrete"}, {"metadata": 13, "displayName": "Green Concrete"}, {"metadata": 14, "displayName": "Red Concrete"}, {"metadata": 15, "displayName": "Black Concrete"}], "drops": [{"drop": 251}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 1.8}, {"id": 252, "displayName": "<PERSON><PERSON><PERSON>", "name": "concrete_powder", "hardness": 0.5, "stackSize": 64, "diggable": true, "boundingBox": "block", "material": "dirt", "variations": [{"metadata": 0, "displayName": "White Concrete Powder"}, {"metadata": 1, "displayName": "Orange Concrete Powder"}, {"metadata": 2, "displayName": "Magenta Concrete Powder"}, {"metadata": 3, "displayName": "Light blue Concrete Powder"}, {"metadata": 4, "displayName": "Yellow Concrete Powder"}, {"metadata": 5, "displayName": "Lime Concrete <PERSON>"}, {"metadata": 6, "displayName": "Pink Concrete Powder"}, {"metadata": 7, "displayName": "<PERSON> Concre<PERSON>"}, {"metadata": 8, "displayName": "Light gray Concrete Powder"}, {"metadata": 9, "displayName": "<PERSON><PERSON>"}, {"metadata": 10, "displayName": "Purple Concrete Powder"}, {"metadata": 11, "displayName": "Blue Concrete Powder"}, {"metadata": 12, "displayName": "<PERSON> Concrete <PERSON>"}, {"metadata": 13, "displayName": "Green Concrete Powder"}, {"metadata": 14, "displayName": "Red Concrete Powder"}, {"metadata": 15, "displayName": "Black Concrete Powder"}], "drops": [{"drop": 252}], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 0.5}, {"id": 255, "displayName": "Structure Block", "name": "structure_block", "hardness": null, "stackSize": 64, "diggable": false, "boundingBox": "block", "variations": [{"metadata": 0, "displayName": "Save"}, {"metadata": 1, "displayName": "Load"}, {"metadata": 2, "displayName": "Corner"}, {"metadata": 3, "displayName": "Data"}], "drops": [], "transparent": false, "emitLight": 0, "filterLight": 15, "resistance": 3600000}]