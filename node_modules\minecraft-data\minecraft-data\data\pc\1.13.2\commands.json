{"root": {"type": "root", "name": "root", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "advancement", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "grant", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "everything", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "from", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "advancement", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "only", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "advancement", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "criterion", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:string", "modifier": {"type": "greedy"}}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "through", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "advancement", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "until", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "advancement", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "revoke", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "everything", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "from", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "advancement", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "only", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "advancement", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "criterion", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:string", "modifier": {"type": "greedy"}}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "through", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "advancement", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "until", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "advancement", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}]}, {"type": "literal", "name": "attribute", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "attribute", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "base", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "get", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "set", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}]}, {"type": "literal", "name": "get", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "modifier", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "add", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "uuid", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "name", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "add", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "multiply", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "multiply_base", "executable": true, "redirects": [], "children": []}], "parser": {"parser": "brigadier:double", "modifier": null}}], "parser": {"parser": "brigadier:string", "modifier": {"type": "phrase"}}}], "parser": {"parser": "minecraft:uuid", "modifier": null}}]}, {"type": "literal", "name": "remove", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "uuid", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:uuid", "modifier": null}}]}, {"type": "literal", "name": "value", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "get", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "uuid", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}], "parser": {"parser": "minecraft:uuid", "modifier": null}}]}]}]}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "ban", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "reason", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:message", "modifier": null}}], "parser": {"parser": "minecraft:game_profile", "modifier": null}}]}, {"type": "literal", "name": "ban-ip", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "reason", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:message", "modifier": null}}], "parser": {"parser": "brigadier:string", "modifier": {"type": "word"}}}]}, {"type": "literal", "name": "banlist", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "ips", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "players", "executable": true, "redirects": [], "children": []}]}, {"type": "literal", "name": "bossbar", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "add", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "id", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "name", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:component", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "get", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "id", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "max", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "players", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "value", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "visible", "executable": true, "redirects": [], "children": []}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "list", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "remove", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "id", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "set", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "id", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "color", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "blue", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "green", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "pink", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "purple", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "red", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "white", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "yellow", "executable": true, "redirects": [], "children": []}]}, {"type": "literal", "name": "max", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "max", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": {"min": 1}}}]}, {"type": "literal", "name": "name", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "name", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:component", "modifier": null}}]}, {"type": "literal", "name": "players", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "style", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "notched_10", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "notched_12", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "notched_20", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "notched_6", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "progress", "executable": true, "redirects": [], "children": []}]}, {"type": "literal", "name": "value", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0}}}]}, {"type": "literal", "name": "visible", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "visible", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}, {"type": "literal", "name": "clear", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "item", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "maxCount", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0}}}], "parser": {"parser": "minecraft:item_predicate", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "clone", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "begin", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "end", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "destination", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "filtered", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "filter", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "force", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "move", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "normal", "executable": true, "redirects": [], "children": []}], "parser": {"parser": "minecraft:block_predicate", "modifier": null}}]}, {"type": "literal", "name": "masked", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "force", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "move", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "normal", "executable": true, "redirects": [], "children": []}]}, {"type": "literal", "name": "replace", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "force", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "move", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "normal", "executable": true, "redirects": [], "children": []}]}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "data", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "get", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targetPos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "path", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "path", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "path", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}, {"type": "literal", "name": "merge", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targetPos", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "nbt", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_compound_tag", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "nbt", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_compound_tag", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "nbt", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_compound_tag", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}, {"type": "literal", "name": "modify", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targetPos", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targetPath", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "append", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "from", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourcePos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}, {"type": "literal", "name": "value", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_tag", "modifier": null}}]}]}, {"type": "literal", "name": "insert", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "index", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "from", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourcePos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}, {"type": "literal", "name": "value", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_tag", "modifier": null}}]}], "parser": {"parser": "brigadier:integer", "modifier": null}}]}, {"type": "literal", "name": "merge", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "from", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourcePos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}, {"type": "literal", "name": "value", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_tag", "modifier": null}}]}]}, {"type": "literal", "name": "prepend", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "from", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourcePos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}, {"type": "literal", "name": "value", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_tag", "modifier": null}}]}]}, {"type": "literal", "name": "set", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "from", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourcePos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}, {"type": "literal", "name": "value", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_tag", "modifier": null}}]}]}], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targetPath", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "append", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "from", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourcePos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}, {"type": "literal", "name": "value", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_tag", "modifier": null}}]}]}, {"type": "literal", "name": "insert", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "index", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "from", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourcePos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}, {"type": "literal", "name": "value", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_tag", "modifier": null}}]}], "parser": {"parser": "brigadier:integer", "modifier": null}}]}, {"type": "literal", "name": "merge", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "from", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourcePos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}, {"type": "literal", "name": "value", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_tag", "modifier": null}}]}]}, {"type": "literal", "name": "prepend", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "from", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourcePos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}, {"type": "literal", "name": "value", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_tag", "modifier": null}}]}]}, {"type": "literal", "name": "set", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "from", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourcePos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}, {"type": "literal", "name": "value", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_tag", "modifier": null}}]}]}], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targetPath", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "append", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "from", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourcePos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}, {"type": "literal", "name": "value", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_tag", "modifier": null}}]}]}, {"type": "literal", "name": "insert", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "index", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "from", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourcePos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}, {"type": "literal", "name": "value", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_tag", "modifier": null}}]}], "parser": {"parser": "brigadier:integer", "modifier": null}}]}, {"type": "literal", "name": "merge", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "from", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourcePos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}, {"type": "literal", "name": "value", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_tag", "modifier": null}}]}]}, {"type": "literal", "name": "prepend", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "from", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourcePos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}, {"type": "literal", "name": "value", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_tag", "modifier": null}}]}]}, {"type": "literal", "name": "set", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "from", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourcePos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sourcePath", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}, {"type": "literal", "name": "value", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_tag", "modifier": null}}]}]}], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}, {"type": "literal", "name": "remove", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targetPos", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "path", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "path", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "path", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}]}, {"type": "literal", "name": "datapack", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "disable", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "name", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:string", "modifier": {"type": "phrase"}}}]}, {"type": "literal", "name": "enable", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "name", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "after", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "existing", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:string", "modifier": {"type": "phrase"}}}]}, {"type": "literal", "name": "before", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "existing", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:string", "modifier": {"type": "phrase"}}}]}, {"type": "literal", "name": "first", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "last", "executable": true, "redirects": [], "children": []}], "parser": {"parser": "brigadier:string", "modifier": {"type": "phrase"}}}]}, {"type": "literal", "name": "list", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "available", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "enabled", "executable": true, "redirects": [], "children": []}]}]}, {"type": "literal", "name": "debug", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "report", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "start", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "stop", "executable": true, "redirects": [], "children": []}]}, {"type": "literal", "name": "defaultgamemode", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "adventure", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "creative", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "spectator", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "survival", "executable": true, "redirects": [], "children": []}]}, {"type": "literal", "name": "deop", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:game_profile", "modifier": null}}]}, {"type": "literal", "name": "difficulty", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "easy", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "hard", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "normal", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "peaceful", "executable": true, "redirects": [], "children": []}]}, {"type": "literal", "name": "effect", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "clear", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "effect", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:mob_effect", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "entities"}}}]}, {"type": "literal", "name": "give", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "effect", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "seconds", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "amplifier", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "hideParticles", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0, "max": 255}}}], "parser": {"parser": "brigadier:integer", "modifier": {"min": 1, "max": 1000000}}}], "parser": {"parser": "minecraft:mob_effect", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "entities"}}}]}]}, {"type": "literal", "name": "enchant", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "enchantment", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "level", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0}}}], "parser": {"parser": "minecraft:item_enchantment", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "entities"}}}]}, {"type": "literal", "name": "execute", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "align", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "axes", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:swizzle", "modifier": null}}]}, {"type": "literal", "name": "anchored", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "anchor", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:entity_anchor", "modifier": null}}]}, {"type": "literal", "name": "as", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "entities"}}}]}, {"type": "literal", "name": "at", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "entities"}}}]}, {"type": "literal", "name": "facing", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "anchor", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:entity_anchor", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "entities"}}}]}, {"type": "argument", "name": "pos", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:vec3", "modifier": null}}]}, {"type": "literal", "name": "if", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "block", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:block_predicate", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "blocks", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "start", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "end", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "destination", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "all", "executable": true, "redirects": ["execute"], "children": []}, {"type": "literal", "name": "masked", "executable": true, "redirects": ["execute"], "children": []}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "data", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourcePos", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "path", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "path", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "path", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "entities", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "entities"}}}]}, {"type": "literal", "name": "predicate", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "predicate", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "score", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targetObjective", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "<", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourceObjective", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "single"}}}]}, {"type": "literal", "name": "<=", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourceObjective", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "single"}}}]}, {"type": "literal", "name": "=", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourceObjective", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "single"}}}]}, {"type": "literal", "name": ">", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourceObjective", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "single"}}}]}, {"type": "literal", "name": ">=", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourceObjective", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "single"}}}]}, {"type": "literal", "name": "matches", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "range", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:int_range", "modifier": null}}]}], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "single"}}}]}]}, {"type": "literal", "name": "in", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "dimension", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:dimension", "modifier": null}}]}, {"type": "literal", "name": "positioned", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "as", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "entities"}}}]}, {"type": "argument", "name": "pos", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:vec3", "modifier": null}}]}, {"type": "literal", "name": "rotated", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "as", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "entities"}}}]}, {"type": "argument", "name": "rot", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:rotation", "modifier": null}}]}, {"type": "literal", "name": "run", "executable": false, "redirects": [], "children": []}, {"type": "literal", "name": "store", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "result", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targetPos", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "path", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "byte", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "double", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "float", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "int", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "long", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "short", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "bossbar", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "id", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "max", "executable": false, "redirects": ["execute"], "children": []}, {"type": "literal", "name": "value", "executable": false, "redirects": ["execute"], "children": []}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "path", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "byte", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "double", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "float", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "int", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "long", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "short", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "score", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "objective", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "multiple"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "path", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "byte", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "double", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "float", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "int", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "long", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "short", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}, {"type": "literal", "name": "success", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targetPos", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "path", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "byte", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "double", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "float", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "int", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "long", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "short", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "bossbar", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "id", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "max", "executable": false, "redirects": ["execute"], "children": []}, {"type": "literal", "name": "value", "executable": false, "redirects": ["execute"], "children": []}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "path", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "byte", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "double", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "float", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "int", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "long", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "short", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "score", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "objective", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "multiple"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "path", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "byte", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "double", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "float", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "int", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "long", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}, {"type": "literal", "name": "short", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "scale", "executable": false, "redirects": ["execute"], "children": [], "parser": {"parser": "brigadier:double", "modifier": null}}]}], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}]}, {"type": "literal", "name": "unless", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "block", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:block_predicate", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "blocks", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "start", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "end", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "destination", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "all", "executable": true, "redirects": ["execute"], "children": []}, {"type": "literal", "name": "masked", "executable": true, "redirects": ["execute"], "children": []}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "data", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourcePos", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "path", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "path", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "storage", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "path", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:nbt_path", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "entities", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "entities"}}}]}, {"type": "literal", "name": "predicate", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "predicate", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "score", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targetObjective", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "<", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourceObjective", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "single"}}}]}, {"type": "literal", "name": "<=", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourceObjective", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "single"}}}]}, {"type": "literal", "name": "=", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourceObjective", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "single"}}}]}, {"type": "literal", "name": ">", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourceObjective", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "single"}}}]}, {"type": "literal", "name": ">=", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourceObjective", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "single"}}}]}, {"type": "literal", "name": "matches", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "range", "executable": true, "redirects": ["execute"], "children": [], "parser": {"parser": "minecraft:int_range", "modifier": null}}]}], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "single"}}}]}]}]}, {"type": "literal", "name": "experience", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "add", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "amount", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "levels", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "points", "executable": true, "redirects": [], "children": []}], "parser": {"parser": "brigadier:integer", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "query", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "levels", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "points", "executable": true, "redirects": [], "children": []}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "players"}}}]}, {"type": "literal", "name": "set", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "amount", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "levels", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "points", "executable": true, "redirects": [], "children": []}], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0}}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}]}, {"type": "literal", "name": "fill", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "from", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "to", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "block", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "destroy", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "hollow", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "keep", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "outline", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "replace", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "filter", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:block_predicate", "modifier": null}}]}], "parser": {"parser": "minecraft:block_state", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "forceload", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "add", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "from", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "to", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:column_pos", "modifier": null}}], "parser": {"parser": "minecraft:column_pos", "modifier": null}}]}, {"type": "literal", "name": "query", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:column_pos", "modifier": null}}]}, {"type": "literal", "name": "remove", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "all", "executable": true, "redirects": [], "children": []}, {"type": "argument", "name": "from", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "to", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:column_pos", "modifier": null}}], "parser": {"parser": "minecraft:column_pos", "modifier": null}}]}]}, {"type": "literal", "name": "function", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "name", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:function", "modifier": null}}]}, {"type": "literal", "name": "gamemode", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "adventure", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "creative", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "spectator", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "survival", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}]}, {"type": "literal", "name": "game<PERSON>le", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "announceAdvancements", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "commandBlockOutput", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "disableElytraMovementCheck", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "disableRaids", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "doDaylightCycle", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "doEntityDrops", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "doFireTick", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "doImmediateRespawn", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "doInsomnia", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "doLimitedCrafting", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "doMobLoot", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "doMobSpawning", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "doPatrolSpawning", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "doTileDrops", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "doTraderSpawning", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "doWeatherCycle", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "drowningDamage", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "fallDamage", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "fireDamage", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "forgiveDeadPlayers", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "keepInventory", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "logAdminCommands", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": null}}]}, {"type": "literal", "name": "maxEntityCramming", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": null}}]}, {"type": "literal", "name": "mobGriefing", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "naturalRegeneration", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "randomTickSpeed", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": null}}]}, {"type": "literal", "name": "reducedDebugInfo", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "sendCommandFeedback", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "showDeathMessages", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "spawnRadius", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": null}}]}, {"type": "literal", "name": "spectatorsGenerateChunks", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "universalAnger", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}]}, {"type": "literal", "name": "give", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "item", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "count", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": {"min": 1}}}], "parser": {"parser": "minecraft:item_stack", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "help", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "command", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:string", "modifier": {"type": "greedy"}}}]}, {"type": "literal", "name": "kick", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "reason", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:message", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "kill", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "entities"}}}]}, {"type": "literal", "name": "list", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "uuids", "executable": true, "redirects": [], "children": []}]}, {"type": "literal", "name": "locate", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "bastion_remnant", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "buried_treasure", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "desert_pyramid", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "endcity", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "fortress", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "igloo", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "jungle_pyramid", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "mansion", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "mineshaft", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "monument", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "nether_fossil", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "ocean_ruin", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "pillager_outpost", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "ruined_portal", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "shipwreck", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "stronghold", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "swamp_hut", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "village", "executable": true, "redirects": [], "children": []}]}, {"type": "literal", "name": "locatebiome", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "biome", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "loot", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "give", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "players", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "fish", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "loot_table", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "mainhand", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "offhand", "executable": true, "redirects": [], "children": []}, {"type": "argument", "name": "tool", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:item_stack", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "kill", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "loot", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "loot_table", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "mine", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "mainhand", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "offhand", "executable": true, "redirects": [], "children": []}, {"type": "argument", "name": "tool", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:item_stack", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "insert", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targetPos", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "fish", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "loot_table", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "mainhand", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "offhand", "executable": true, "redirects": [], "children": []}, {"type": "argument", "name": "tool", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:item_stack", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "kill", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "loot", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "loot_table", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "mine", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "mainhand", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "offhand", "executable": true, "redirects": [], "children": []}, {"type": "argument", "name": "tool", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:item_stack", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "replace", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targetPos", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "slot", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "fish", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "loot_table", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "mainhand", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "offhand", "executable": true, "redirects": [], "children": []}, {"type": "argument", "name": "tool", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:item_stack", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "kill", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "loot", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "loot_table", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "mine", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "mainhand", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "offhand", "executable": true, "redirects": [], "children": []}, {"type": "argument", "name": "tool", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:item_stack", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "argument", "name": "count", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "fish", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "loot_table", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "mainhand", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "offhand", "executable": true, "redirects": [], "children": []}, {"type": "argument", "name": "tool", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:item_stack", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "kill", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "loot", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "loot_table", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "mine", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "mainhand", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "offhand", "executable": true, "redirects": [], "children": []}, {"type": "argument", "name": "tool", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:item_stack", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0}}}], "parser": {"parser": "minecraft:item_slot", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "entities", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "slot", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "fish", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "loot_table", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "mainhand", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "offhand", "executable": true, "redirects": [], "children": []}, {"type": "argument", "name": "tool", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:item_stack", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "kill", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "loot", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "loot_table", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "mine", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "mainhand", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "offhand", "executable": true, "redirects": [], "children": []}, {"type": "argument", "name": "tool", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:item_stack", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "argument", "name": "count", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "fish", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "loot_table", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "mainhand", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "offhand", "executable": true, "redirects": [], "children": []}, {"type": "argument", "name": "tool", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:item_stack", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "kill", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "loot", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "loot_table", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "mine", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "mainhand", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "offhand", "executable": true, "redirects": [], "children": []}, {"type": "argument", "name": "tool", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:item_stack", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0}}}], "parser": {"parser": "minecraft:item_slot", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "entities"}}}]}]}, {"type": "literal", "name": "spawn", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targetPos", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "fish", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "loot_table", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "mainhand", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "offhand", "executable": true, "redirects": [], "children": []}, {"type": "argument", "name": "tool", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:item_stack", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "kill", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "loot", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "loot_table", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "mine", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "mainhand", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "offhand", "executable": true, "redirects": [], "children": []}, {"type": "argument", "name": "tool", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:item_stack", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}], "parser": {"parser": "minecraft:vec3", "modifier": null}}]}]}, {"type": "literal", "name": "me", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "action", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:string", "modifier": {"type": "greedy"}}}]}, {"type": "literal", "name": "msg", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "message", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:message", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "op", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:game_profile", "modifier": null}}]}, {"type": "literal", "name": "pardon", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:game_profile", "modifier": null}}]}, {"type": "literal", "name": "pardon-ip", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:string", "modifier": {"type": "word"}}}]}, {"type": "literal", "name": "particle", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "name", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "delta", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "speed", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "count", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "force", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "viewers", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "normal", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "viewers", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0}}}], "parser": {"parser": "minecraft:vec3", "modifier": null}}], "parser": {"parser": "minecraft:vec3", "modifier": null}}], "parser": {"parser": "minecraft:particle", "modifier": null}}]}, {"type": "literal", "name": "playsound", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sound", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "ambient", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "volume", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pitch", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "minVolume", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0, "max": 1.0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0, "max": 2.0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0}}}], "parser": {"parser": "minecraft:vec3", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "volume", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pitch", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "minVolume", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0, "max": 1.0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0, "max": 2.0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0}}}], "parser": {"parser": "minecraft:vec3", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "hostile", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "volume", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pitch", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "minVolume", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0, "max": 1.0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0, "max": 2.0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0}}}], "parser": {"parser": "minecraft:vec3", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "master", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "volume", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pitch", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "minVolume", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0, "max": 1.0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0, "max": 2.0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0}}}], "parser": {"parser": "minecraft:vec3", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "music", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "volume", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pitch", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "minVolume", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0, "max": 1.0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0, "max": 2.0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0}}}], "parser": {"parser": "minecraft:vec3", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "neutral", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "volume", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pitch", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "minVolume", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0, "max": 1.0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0, "max": 2.0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0}}}], "parser": {"parser": "minecraft:vec3", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "player", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "volume", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pitch", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "minVolume", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0, "max": 1.0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0, "max": 2.0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0}}}], "parser": {"parser": "minecraft:vec3", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "record", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "volume", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pitch", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "minVolume", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0, "max": 1.0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0, "max": 2.0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0}}}], "parser": {"parser": "minecraft:vec3", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "voice", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "volume", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pitch", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "minVolume", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0, "max": 1.0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0, "max": 2.0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0}}}], "parser": {"parser": "minecraft:vec3", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "weather", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "volume", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pitch", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "minVolume", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0, "max": 1.0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0, "max": 2.0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0}}}], "parser": {"parser": "minecraft:vec3", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "publish", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "port", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0, "max": 65535}}}]}, {"type": "literal", "name": "recipe", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "give", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "*", "executable": true, "redirects": [], "children": []}, {"type": "argument", "name": "recipe", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "take", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "*", "executable": true, "redirects": [], "children": []}, {"type": "argument", "name": "recipe", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}]}, {"type": "literal", "name": "reload", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "replaceitem", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "block", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "slot", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "item", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "count", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": {"min": 1, "max": 64}}}], "parser": {"parser": "minecraft:item_stack", "modifier": null}}], "parser": {"parser": "minecraft:item_slot", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "slot", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "item", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "count", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": {"min": 1, "max": 64}}}], "parser": {"parser": "minecraft:item_stack", "modifier": null}}], "parser": {"parser": "minecraft:item_slot", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "entities"}}}]}]}, {"type": "literal", "name": "save-all", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "flush", "executable": true, "redirects": [], "children": []}]}, {"type": "literal", "name": "save-off", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "save-on", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "say", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "message", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:message", "modifier": null}}]}, {"type": "literal", "name": "schedule", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "clear", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "function", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:string", "modifier": {"type": "greedy"}}}]}, {"type": "literal", "name": "function", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "function", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "time", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "append", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "replace", "executable": true, "redirects": [], "children": []}], "parser": {"parser": "minecraft:time", "modifier": null}}], "parser": {"parser": "minecraft:function", "modifier": null}}]}]}, {"type": "literal", "name": "scoreboard", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "objectives", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "add", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "objective", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "criteria", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "displayName", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:component", "modifier": null}}], "parser": {"parser": "minecraft:objective_criteria", "modifier": null}}], "parser": {"parser": "brigadier:string", "modifier": {"type": "word"}}}]}, {"type": "literal", "name": "list", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "modify", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "objective", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "displayname", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "displayName", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:component", "modifier": null}}]}, {"type": "literal", "name": "rendertype", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "hearts", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "integer", "executable": true, "redirects": [], "children": []}]}], "parser": {"parser": "minecraft:objective", "modifier": null}}]}, {"type": "literal", "name": "remove", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "objective", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:objective", "modifier": null}}]}, {"type": "literal", "name": "setdisplay", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "slot", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "objective", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:scoreboard_slot", "modifier": null}}]}]}, {"type": "literal", "name": "players", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "add", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "objective", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "score", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0}}}], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "multiple"}}}]}, {"type": "literal", "name": "enable", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "objective", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "multiple"}}}]}, {"type": "literal", "name": "get", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "objective", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "single"}}}]}, {"type": "literal", "name": "list", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "single"}}}]}, {"type": "literal", "name": "operation", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targetObjective", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "operation", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "source", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sourceObjective", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "multiple"}}}], "parser": {"parser": "minecraft:operation", "modifier": null}}], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "multiple"}}}]}, {"type": "literal", "name": "remove", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "objective", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "score", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0}}}], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "multiple"}}}]}, {"type": "literal", "name": "reset", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "objective", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "multiple"}}}]}, {"type": "literal", "name": "set", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "objective", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "score", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": null}}], "parser": {"parser": "minecraft:objective", "modifier": null}}], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "multiple"}}}]}]}]}, {"type": "literal", "name": "seed", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "setblock", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "block", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "destroy", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "keep", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "replace", "executable": true, "redirects": [], "children": []}], "parser": {"parser": "minecraft:block_state", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "setidletimeout", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "minutes", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0}}}]}, {"type": "literal", "name": "setworldspawn", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "angle", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:angle", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}]}, {"type": "literal", "name": "spawnpoint", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "angle", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:angle", "modifier": null}}], "parser": {"parser": "minecraft:block_pos", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "spectate", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "target", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "player", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "players"}}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "literal", "name": "spreadplayers", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "center", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "spreadDistance", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "max<PERSON><PERSON><PERSON>", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "under", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "maxHeight", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "respectTeams", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "entities"}}}], "parser": {"parser": "brigadier:bool", "modifier": null}}], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0}}}]}, {"type": "argument", "name": "respectTeams", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "entities"}}}], "parser": {"parser": "brigadier:bool", "modifier": null}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 1.0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0}}}], "parser": {"parser": "minecraft:vec2", "modifier": null}}]}, {"type": "literal", "name": "stop", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "stopsound", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "*", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "sound", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "ambient", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sound", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "block", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sound", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "hostile", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sound", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "master", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sound", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "music", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sound", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "neutral", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sound", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "player", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sound", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "record", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sound", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "voice", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sound", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}, {"type": "literal", "name": "weather", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "sound", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:resource_location", "modifier": null}}]}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "summon", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "entity", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "nbt", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:nbt_compound_tag", "modifier": null}}], "parser": {"parser": "minecraft:vec3", "modifier": null}}], "parser": {"parser": "minecraft:entity_summon", "modifier": null}}]}, {"type": "literal", "name": "tag", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "add", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "name", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:string", "modifier": {"type": "word"}}}]}, {"type": "literal", "name": "list", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "remove", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "name", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:string", "modifier": {"type": "word"}}}]}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "entities"}}}]}, {"type": "literal", "name": "team", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "add", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "team", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "displayName", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:component", "modifier": null}}], "parser": {"parser": "brigadier:string", "modifier": {"type": "word"}}}]}, {"type": "literal", "name": "empty", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "team", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:team", "modifier": null}}]}, {"type": "literal", "name": "join", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "team", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "members", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "multiple"}}}], "parser": {"parser": "minecraft:team", "modifier": null}}]}, {"type": "literal", "name": "leave", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "members", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:score_holder", "modifier": {"amount": "multiple"}}}]}, {"type": "literal", "name": "list", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "team", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:team", "modifier": null}}]}, {"type": "literal", "name": "modify", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "team", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "collisionRule", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "always", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "never", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "pushOtherTeams", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "pushOwnTeam", "executable": true, "redirects": [], "children": []}]}, {"type": "literal", "name": "color", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:color", "modifier": null}}]}, {"type": "literal", "name": "deathMessageVisibility", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "always", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "hideForOtherTeams", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "hideForOwnTeam", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "never", "executable": true, "redirects": [], "children": []}]}, {"type": "literal", "name": "displayName", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "displayName", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:component", "modifier": null}}]}, {"type": "literal", "name": "friendlyFire", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "allowed", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "nametagVisibility", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "always", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "hideForOtherTeams", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "hideForOwnTeam", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "never", "executable": true, "redirects": [], "children": []}]}, {"type": "literal", "name": "prefix", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "prefix", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:component", "modifier": null}}]}, {"type": "literal", "name": "seeFriendlyInvisibles", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "allowed", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:bool", "modifier": null}}]}, {"type": "literal", "name": "suffix", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "suffix", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:component", "modifier": null}}]}], "parser": {"parser": "minecraft:team", "modifier": null}}]}, {"type": "literal", "name": "remove", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "team", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:team", "modifier": null}}]}]}, {"type": "literal", "name": "teammsg", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "message", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:message", "modifier": null}}]}, {"type": "literal", "name": "teleport", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "destination", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}, {"type": "argument", "name": "location", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:vec3", "modifier": null}}, {"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "destination", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}, {"type": "argument", "name": "location", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "facing", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "facingEntity", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "facingAnchor", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity_anchor", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "argument", "name": "facingLocation", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:vec3", "modifier": null}}]}, {"type": "argument", "name": "rotation", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:rotation", "modifier": null}}], "parser": {"parser": "minecraft:vec3", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "entities"}}}]}, {"type": "literal", "name": "tell", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "message", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:message", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "tellraw", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "message", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:component", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "time", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "add", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "time", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:time", "modifier": null}}]}, {"type": "literal", "name": "query", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "day", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "daytime", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "gametime", "executable": true, "redirects": [], "children": []}]}, {"type": "literal", "name": "set", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "day", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "midnight", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "night", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "noon", "executable": true, "redirects": [], "children": []}, {"type": "argument", "name": "time", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:time", "modifier": null}}]}]}, {"type": "literal", "name": "title", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "actionbar", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "title", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:component", "modifier": null}}]}, {"type": "literal", "name": "clear", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "reset", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "subtitle", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "title", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:component", "modifier": null}}]}, {"type": "literal", "name": "times", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "fadeIn", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "stay", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "fadeOut", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0}}}], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0}}}], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0}}}]}, {"type": "literal", "name": "title", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "title", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:component", "modifier": null}}]}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "tm", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "message", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:message", "modifier": null}}]}, {"type": "literal", "name": "tp", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "destination", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}, {"type": "argument", "name": "location", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:vec3", "modifier": null}}, {"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "destination", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}, {"type": "argument", "name": "location", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "facing", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "entity", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "facingEntity", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "facingAnchor", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:entity_anchor", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}}}]}, {"type": "argument", "name": "facingLocation", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:vec3", "modifier": null}}]}, {"type": "argument", "name": "rotation", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:rotation", "modifier": null}}], "parser": {"parser": "minecraft:vec3", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "entities"}}}]}, {"type": "literal", "name": "trigger", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "objective", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "add", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": null}}]}, {"type": "literal", "name": "set", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "value", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": null}}]}], "parser": {"parser": "minecraft:objective", "modifier": null}}]}, {"type": "literal", "name": "w", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "message", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:message", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "weather", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "clear", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "duration", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0, "max": 1000000}}}]}, {"type": "literal", "name": "rain", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "duration", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0, "max": 1000000}}}]}, {"type": "literal", "name": "thunder", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "duration", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0, "max": 1000000}}}]}]}, {"type": "literal", "name": "whitelist", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "add", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:game_profile", "modifier": null}}]}, {"type": "literal", "name": "list", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "off", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "on", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "reload", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "remove", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:game_profile", "modifier": null}}]}]}, {"type": "literal", "name": "worldborder", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "add", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "distance", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "time", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": -60000000.0, "max": 60000000.0}}}]}, {"type": "literal", "name": "center", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "pos", "executable": true, "redirects": [], "children": [], "parser": {"parser": "minecraft:vec2", "modifier": null}}]}, {"type": "literal", "name": "damage", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "amount", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "damagePerBlock", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0}}}]}, {"type": "literal", "name": "buffer", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "distance", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:float", "modifier": {"min": 0.0}}}]}]}, {"type": "literal", "name": "get", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "set", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "distance", "executable": true, "redirects": [], "children": [{"type": "argument", "name": "time", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0}}}], "parser": {"parser": "brigadier:float", "modifier": {"min": -60000000.0, "max": 60000000.0}}}]}, {"type": "literal", "name": "warning", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "distance", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "distance", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0}}}]}, {"type": "literal", "name": "time", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "time", "executable": true, "redirects": [], "children": [], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0}}}]}]}]}, {"type": "literal", "name": "xp", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "add", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "amount", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "levels", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "points", "executable": true, "redirects": [], "children": []}], "parser": {"parser": "brigadier:integer", "modifier": null}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}, {"type": "literal", "name": "query", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "literal", "name": "levels", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "points", "executable": true, "redirects": [], "children": []}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "players"}}}]}, {"type": "literal", "name": "set", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "targets", "executable": false, "redirects": [], "children": [{"type": "argument", "name": "amount", "executable": true, "redirects": [], "children": [{"type": "literal", "name": "levels", "executable": true, "redirects": [], "children": []}, {"type": "literal", "name": "points", "executable": true, "redirects": [], "children": []}], "parser": {"parser": "brigadier:integer", "modifier": {"min": 0}}}], "parser": {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}}}]}]}]}, "parsers": [{"parser": "minecraft:resource_location", "modifier": null, "examples": []}, {"parser": "brigadier:string", "modifier": {"type": "greedy"}, "examples": ["word", "words with spaces", "\\\" and symbols\\\""]}, {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "players"}, "examples": ["@a"]}, {"parser": "brigadier:double", "modifier": null, "examples": []}, {"parser": "brigadier:string", "modifier": {"type": "phrase"}, "examples": ["\\\"quoted phrase\\\"", "word", ""]}, {"parser": "minecraft:uuid", "modifier": null, "examples": []}, {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "entities"}, "examples": ["@p"]}, {"parser": "minecraft:message", "modifier": null, "examples": []}, {"parser": "minecraft:game_profile", "modifier": null, "examples": ["Player", "0123", "dd12be42-52a9-4a91-a8a1-11c01849e498", "@e"]}, {"parser": "brigadier:string", "modifier": {"type": "word"}, "examples": ["word", "word_with_underscore"]}, {"parser": "minecraft:component", "modifier": null, "examples": ["\"hello world\"", "\"\"", "\"{\"text\":\"hello world\"}\"", "[\"\"]"]}, {"parser": "brigadier:integer", "modifier": {"min": 1}, "examples": ["1"]}, {"parser": "brigadier:integer", "modifier": {"min": 0}, "examples": ["0", "1"]}, {"parser": "brigadier:bool", "modifier": null, "examples": ["true", "false"]}, {"parser": "minecraft:item_predicate", "modifier": null, "examples": ["stick", "minecraft:stick", "#stick", "diamond_sword{ench:[{id:16,lvl:5}]}"]}, {"parser": "minecraft:block_predicate", "modifier": null, "examples": ["stone", "minecraft:stone", "#stone", "minecraft:redstone_wire[power=15,north=up,south=side]", "minecraft:furnace[facing=north]{BurnTime:200}"]}, {"parser": "minecraft:block_pos", "modifier": null, "examples": ["0 0 0", "~ ~ ~", "^ ^ ^", "^1 ^ ^-5", "~0.5 ~1 ~-5"]}, {"parser": "minecraft:nbt_path", "modifier": null, "examples": []}, {"parser": "minecraft:nbt_compound_tag", "modifier": null, "examples": []}, {"parser": "minecraft:nbt_tag", "modifier": null, "examples": []}, {"parser": "brigadier:integer", "modifier": null, "examples": ["1", "0", "10"]}, {"parser": "minecraft:mob_effect", "modifier": null, "examples": []}, {"parser": "minecraft:entity", "modifier": {"amount": "multiple", "type": "entities"}, "examples": ["@e", "@e[type=cow]", "dd12be42-52a9-4a91-a8a1-11c01849e498", "Player"]}, {"parser": "brigadier:integer", "modifier": {"min": 0, "max": 255}, "examples": ["0", "255"]}, {"parser": "brigadier:integer", "modifier": {"min": 1, "max": 1000000}, "examples": ["1", "1000000"]}, {"parser": "minecraft:item_enchantment", "modifier": null, "examples": ["unbreaking", "minecraft:slik_touch"]}, {"parser": "minecraft:swizzle", "modifier": null, "examples": []}, {"parser": "minecraft:entity_anchor", "modifier": null, "examples": ["eyes", "feet"]}, {"parser": "minecraft:vec3", "modifier": null, "examples": []}, {"parser": "minecraft:objective", "modifier": null, "examples": []}, {"parser": "minecraft:score_holder", "modifier": {"amount": "single"}, "examples": []}, {"parser": "minecraft:int_range", "modifier": null, "examples": ["0..5", "0", "-5", "-100..", "..100"]}, {"parser": "minecraft:dimension", "modifier": null, "examples": ["minecraft:overworld", "minecraft:the_nether", "minecraft:the_end", "overworld", "the_end", "the_nether"]}, {"parser": "minecraft:rotation", "modifier": null, "examples": []}, {"parser": "minecraft:score_holder", "modifier": {"amount": "multiple"}, "examples": []}, {"parser": "minecraft:entity", "modifier": {"amount": "single", "type": "players"}, "examples": ["@p"]}, {"parser": "minecraft:block_state", "modifier": null, "examples": ["stone", "minecraft:stone", "minecraft:redstone_wire[power=15,north=up,south=side]", "minecraft:furnace[facing=north]{BurnTime:200}"]}, {"parser": "minecraft:column_pos", "modifier": null, "examples": ["0 0", "~ ~", "~1 ~-2", "^ ^", "^-1 ^0"]}, {"parser": "minecraft:function", "modifier": null, "examples": ["foo", "foo:bar", "#foo"]}, {"parser": "minecraft:item_stack", "modifier": null, "examples": []}, {"parser": "minecraft:item_slot", "modifier": null, "examples": []}, {"parser": "brigadier:float", "modifier": {"min": 0.0}, "examples": ["0.0", "0", "1", "0.01"]}, {"parser": "minecraft:particle", "modifier": null, "examples": []}, {"parser": "brigadier:float", "modifier": {"min": 0.0, "max": 1.0}, "examples": ["0.0", "0", "1", "1.0", "0.5"]}, {"parser": "brigadier:float", "modifier": {"min": 0.0, "max": 2.0}, "examples": ["0", "2", "1.0", "2.0"]}, {"parser": "brigadier:integer", "modifier": {"min": 0, "max": 65535}, "examples": ["0", "65535"]}, {"parser": "brigadier:integer", "modifier": {"min": 1, "max": 64}, "examples": ["1", "64"]}, {"parser": "minecraft:time", "modifier": null, "examples": []}, {"parser": "minecraft:objective_criteria", "modifier": null, "examples": []}, {"parser": "minecraft:scoreboard_slot", "modifier": null, "examples": []}, {"parser": "minecraft:operation", "modifier": null, "examples": []}, {"parser": "minecraft:angle", "modifier": null, "examples": ["0", "~", "~-0.5"]}, {"parser": "brigadier:float", "modifier": {"min": 1.0}, "examples": ["1", "1.0", "1.000"]}, {"parser": "minecraft:vec2", "modifier": null, "examples": []}, {"parser": "minecraft:entity_summon", "modifier": null, "examples": ["minecraft:pig", "cow"]}, {"parser": "minecraft:team", "modifier": null, "examples": []}, {"parser": "minecraft:color", "modifier": null, "examples": ["aqua", "black", "blue", "dark_aqua", "dark_blue", "dark_gray", "dark_green", "dark_purple", "dark_red", "gold", "gray", "green", "light_purple", "red", "reset", "yellow", "white"]}, {"parser": "brigadier:integer", "modifier": {"min": 0, "max": 1000000}, "examples": ["0", "1000000"]}, {"parser": "brigadier:float", "modifier": {"min": -60000000.0, "max": 60000000.0}, "examples": []}]}