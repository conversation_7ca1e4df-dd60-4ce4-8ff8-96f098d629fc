{"entityId": 209, "isHardcore": false, "gameMode": 0, "previousGameMode": -1, "worldNames": ["minecraft:overworld", "minecraft:the_end", "minecraft:the_nether"], "dimensionCodec": {"type": "compound", "name": "", "value": {"minecraft:trim_pattern": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:trim_pattern"}, "value": {"type": "list", "value": {"type": "compound", "value": [{"name": {"type": "string", "value": "minecraft:coast"}, "id": {"type": "int", "value": 0}, "element": {"type": "compound", "value": {"template_item": {"type": "string", "value": "minecraft:coast_armor_trim_smithing_template"}, "description": {"type": "compound", "value": {"translate": {"type": "string", "value": "trim_pattern.minecraft.coast"}}}, "asset_id": {"type": "string", "value": "minecraft:coast"}}}}, {"name": {"type": "string", "value": "minecraft:dune"}, "id": {"type": "int", "value": 1}, "element": {"type": "compound", "value": {"template_item": {"type": "string", "value": "minecraft:dune_armor_trim_smithing_template"}, "description": {"type": "compound", "value": {"translate": {"type": "string", "value": "trim_pattern.minecraft.dune"}}}, "asset_id": {"type": "string", "value": "minecraft:dune"}}}}, {"name": {"type": "string", "value": "minecraft:eye"}, "id": {"type": "int", "value": 2}, "element": {"type": "compound", "value": {"template_item": {"type": "string", "value": "minecraft:eye_armor_trim_smithing_template"}, "description": {"type": "compound", "value": {"translate": {"type": "string", "value": "trim_pattern.minecraft.eye"}}}, "asset_id": {"type": "string", "value": "minecraft:eye"}}}}, {"name": {"type": "string", "value": "minecraft:host"}, "id": {"type": "int", "value": 3}, "element": {"type": "compound", "value": {"template_item": {"type": "string", "value": "minecraft:host_armor_trim_smithing_template"}, "description": {"type": "compound", "value": {"translate": {"type": "string", "value": "trim_pattern.minecraft.host"}}}, "asset_id": {"type": "string", "value": "minecraft:host"}}}}, {"name": {"type": "string", "value": "minecraft:raiser"}, "id": {"type": "int", "value": 4}, "element": {"type": "compound", "value": {"template_item": {"type": "string", "value": "minecraft:raiser_armor_trim_smithing_template"}, "description": {"type": "compound", "value": {"translate": {"type": "string", "value": "trim_pattern.minecraft.raiser"}}}, "asset_id": {"type": "string", "value": "minecraft:raiser"}}}}, {"name": {"type": "string", "value": "minecraft:rib"}, "id": {"type": "int", "value": 5}, "element": {"type": "compound", "value": {"template_item": {"type": "string", "value": "minecraft:rib_armor_trim_smithing_template"}, "description": {"type": "compound", "value": {"translate": {"type": "string", "value": "trim_pattern.minecraft.rib"}}}, "asset_id": {"type": "string", "value": "minecraft:rib"}}}}, {"name": {"type": "string", "value": "minecraft:sentry"}, "id": {"type": "int", "value": 6}, "element": {"type": "compound", "value": {"template_item": {"type": "string", "value": "minecraft:sentry_armor_trim_smithing_template"}, "description": {"type": "compound", "value": {"translate": {"type": "string", "value": "trim_pattern.minecraft.sentry"}}}, "asset_id": {"type": "string", "value": "minecraft:sentry"}}}}, {"name": {"type": "string", "value": "minecraft:shaper"}, "id": {"type": "int", "value": 7}, "element": {"type": "compound", "value": {"template_item": {"type": "string", "value": "minecraft:shaper_armor_trim_smithing_template"}, "description": {"type": "compound", "value": {"translate": {"type": "string", "value": "trim_pattern.minecraft.shaper"}}}, "asset_id": {"type": "string", "value": "minecraft:shaper"}}}}, {"name": {"type": "string", "value": "minecraft:silence"}, "id": {"type": "int", "value": 8}, "element": {"type": "compound", "value": {"template_item": {"type": "string", "value": "minecraft:silence_armor_trim_smithing_template"}, "description": {"type": "compound", "value": {"translate": {"type": "string", "value": "trim_pattern.minecraft.silence"}}}, "asset_id": {"type": "string", "value": "minecraft:silence"}}}}, {"name": {"type": "string", "value": "minecraft:snout"}, "id": {"type": "int", "value": 9}, "element": {"type": "compound", "value": {"template_item": {"type": "string", "value": "minecraft:snout_armor_trim_smithing_template"}, "description": {"type": "compound", "value": {"translate": {"type": "string", "value": "trim_pattern.minecraft.snout"}}}, "asset_id": {"type": "string", "value": "minecraft:snout"}}}}, {"name": {"type": "string", "value": "minecraft:spire"}, "id": {"type": "int", "value": 10}, "element": {"type": "compound", "value": {"template_item": {"type": "string", "value": "minecraft:spire_armor_trim_smithing_template"}, "description": {"type": "compound", "value": {"translate": {"type": "string", "value": "trim_pattern.minecraft.spire"}}}, "asset_id": {"type": "string", "value": "minecraft:spire"}}}}, {"name": {"type": "string", "value": "minecraft:tide"}, "id": {"type": "int", "value": 11}, "element": {"type": "compound", "value": {"template_item": {"type": "string", "value": "minecraft:tide_armor_trim_smithing_template"}, "description": {"type": "compound", "value": {"translate": {"type": "string", "value": "trim_pattern.minecraft.tide"}}}, "asset_id": {"type": "string", "value": "minecraft:tide"}}}}, {"name": {"type": "string", "value": "minecraft:vex"}, "id": {"type": "int", "value": 12}, "element": {"type": "compound", "value": {"template_item": {"type": "string", "value": "minecraft:vex_armor_trim_smithing_template"}, "description": {"type": "compound", "value": {"translate": {"type": "string", "value": "trim_pattern.minecraft.vex"}}}, "asset_id": {"type": "string", "value": "minecraft:vex"}}}}, {"name": {"type": "string", "value": "minecraft:ward"}, "id": {"type": "int", "value": 13}, "element": {"type": "compound", "value": {"template_item": {"type": "string", "value": "minecraft:ward_armor_trim_smithing_template"}, "description": {"type": "compound", "value": {"translate": {"type": "string", "value": "trim_pattern.minecraft.ward"}}}, "asset_id": {"type": "string", "value": "minecraft:ward"}}}}, {"name": {"type": "string", "value": "minecraft:wayfinder"}, "id": {"type": "int", "value": 14}, "element": {"type": "compound", "value": {"template_item": {"type": "string", "value": "minecraft:wayfinder_armor_trim_smithing_template"}, "description": {"type": "compound", "value": {"translate": {"type": "string", "value": "trim_pattern.minecraft.wayfinder"}}}, "asset_id": {"type": "string", "value": "minecraft:wayfinder"}}}}, {"name": {"type": "string", "value": "minecraft:wild"}, "id": {"type": "int", "value": 15}, "element": {"type": "compound", "value": {"template_item": {"type": "string", "value": "minecraft:wild_armor_trim_smithing_template"}, "description": {"type": "compound", "value": {"translate": {"type": "string", "value": "trim_pattern.minecraft.wild"}}}, "asset_id": {"type": "string", "value": "minecraft:wild"}}}}]}}}}, "minecraft:trim_material": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:trim_material"}, "value": {"type": "list", "value": {"type": "compound", "value": [{"name": {"type": "string", "value": "minecraft:amethyst"}, "id": {"type": "int", "value": 0}, "element": {"type": "compound", "value": {"ingredient": {"type": "string", "value": "minecraft:amethyst_shard"}, "asset_name": {"type": "string", "value": "amethyst"}, "item_model_index": {"type": "float", "value": 1}, "description": {"type": "compound", "value": {"color": {"type": "string", "value": "#9A5CC6"}, "translate": {"type": "string", "value": "trim_material.minecraft.amethyst"}}}}}}, {"name": {"type": "string", "value": "minecraft:copper"}, "id": {"type": "int", "value": 1}, "element": {"type": "compound", "value": {"ingredient": {"type": "string", "value": "minecraft:copper_ingot"}, "asset_name": {"type": "string", "value": "copper"}, "item_model_index": {"type": "float", "value": 0.5}, "description": {"type": "compound", "value": {"color": {"type": "string", "value": "#B4684D"}, "translate": {"type": "string", "value": "trim_material.minecraft.copper"}}}}}}, {"name": {"type": "string", "value": "minecraft:diamond"}, "id": {"type": "int", "value": 2}, "element": {"type": "compound", "value": {"override_armor_materials": {"type": "compound", "value": {"diamond": {"type": "string", "value": "diamond_darker"}}}, "ingredient": {"type": "string", "value": "minecraft:diamond"}, "asset_name": {"type": "string", "value": "diamond"}, "item_model_index": {"type": "float", "value": 0.800000011920929}, "description": {"type": "compound", "value": {"color": {"type": "string", "value": "#6EECD2"}, "translate": {"type": "string", "value": "trim_material.minecraft.diamond"}}}}}}, {"name": {"type": "string", "value": "minecraft:emerald"}, "id": {"type": "int", "value": 3}, "element": {"type": "compound", "value": {"ingredient": {"type": "string", "value": "minecraft:emerald"}, "asset_name": {"type": "string", "value": "emerald"}, "item_model_index": {"type": "float", "value": 0.699999988079071}, "description": {"type": "compound", "value": {"color": {"type": "string", "value": "#11A036"}, "translate": {"type": "string", "value": "trim_material.minecraft.emerald"}}}}}}, {"name": {"type": "string", "value": "minecraft:gold"}, "id": {"type": "int", "value": 4}, "element": {"type": "compound", "value": {"override_armor_materials": {"type": "compound", "value": {"gold": {"type": "string", "value": "gold_darker"}}}, "ingredient": {"type": "string", "value": "minecraft:gold_ingot"}, "asset_name": {"type": "string", "value": "gold"}, "item_model_index": {"type": "float", "value": 0.6000000238418579}, "description": {"type": "compound", "value": {"color": {"type": "string", "value": "#DEB12D"}, "translate": {"type": "string", "value": "trim_material.minecraft.gold"}}}}}}, {"name": {"type": "string", "value": "minecraft:iron"}, "id": {"type": "int", "value": 5}, "element": {"type": "compound", "value": {"override_armor_materials": {"type": "compound", "value": {"iron": {"type": "string", "value": "iron_darker"}}}, "ingredient": {"type": "string", "value": "minecraft:iron_ingot"}, "asset_name": {"type": "string", "value": "iron"}, "item_model_index": {"type": "float", "value": 0.20000000298023224}, "description": {"type": "compound", "value": {"color": {"type": "string", "value": "#ECECEC"}, "translate": {"type": "string", "value": "trim_material.minecraft.iron"}}}}}}, {"name": {"type": "string", "value": "minecraft:lapis"}, "id": {"type": "int", "value": 6}, "element": {"type": "compound", "value": {"ingredient": {"type": "string", "value": "minecraft:lapis_lazuli"}, "asset_name": {"type": "string", "value": "lapis"}, "item_model_index": {"type": "float", "value": 0.8999999761581421}, "description": {"type": "compound", "value": {"color": {"type": "string", "value": "#416E97"}, "translate": {"type": "string", "value": "trim_material.minecraft.lapis"}}}}}}, {"name": {"type": "string", "value": "minecraft:netherite"}, "id": {"type": "int", "value": 7}, "element": {"type": "compound", "value": {"override_armor_materials": {"type": "compound", "value": {"netherite": {"type": "string", "value": "netherite_darker"}}}, "ingredient": {"type": "string", "value": "minecraft:netherite_ingot"}, "asset_name": {"type": "string", "value": "netherite"}, "item_model_index": {"type": "float", "value": 0.30000001192092896}, "description": {"type": "compound", "value": {"color": {"type": "string", "value": "#625859"}, "translate": {"type": "string", "value": "trim_material.minecraft.netherite"}}}}}}, {"name": {"type": "string", "value": "minecraft:quartz"}, "id": {"type": "int", "value": 8}, "element": {"type": "compound", "value": {"ingredient": {"type": "string", "value": "minecraft:quartz"}, "asset_name": {"type": "string", "value": "quartz"}, "item_model_index": {"type": "float", "value": 0.10000000149011612}, "description": {"type": "compound", "value": {"color": {"type": "string", "value": "#E3D4C4"}, "translate": {"type": "string", "value": "trim_material.minecraft.quartz"}}}}}}, {"name": {"type": "string", "value": "minecraft:redstone"}, "id": {"type": "int", "value": 9}, "element": {"type": "compound", "value": {"ingredient": {"type": "string", "value": "minecraft:redstone"}, "asset_name": {"type": "string", "value": "redstone"}, "item_model_index": {"type": "float", "value": 0.4000000059604645}, "description": {"type": "compound", "value": {"color": {"type": "string", "value": "#971607"}, "translate": {"type": "string", "value": "trim_material.minecraft.redstone"}}}}}}]}}}}, "minecraft:chat_type": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:chat_type"}, "value": {"type": "list", "value": {"type": "compound", "value": [{"name": {"type": "string", "value": "minecraft:chat"}, "id": {"type": "int", "value": 0}, "element": {"type": "compound", "value": {"chat": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "chat.type.text"}, "parameters": {"type": "list", "value": {"type": "string", "value": ["sender", "content"]}}}}, "narration": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "chat.type.text.narrate"}, "parameters": {"type": "list", "value": {"type": "string", "value": ["sender", "content"]}}}}}}}, {"name": {"type": "string", "value": "minecraft:emote_command"}, "id": {"type": "int", "value": 1}, "element": {"type": "compound", "value": {"chat": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "chat.type.emote"}, "parameters": {"type": "list", "value": {"type": "string", "value": ["sender", "content"]}}}}, "narration": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "chat.type.emote"}, "parameters": {"type": "list", "value": {"type": "string", "value": ["sender", "content"]}}}}}}}, {"name": {"type": "string", "value": "minecraft:msg_command_incoming"}, "id": {"type": "int", "value": 2}, "element": {"type": "compound", "value": {"chat": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "commands.message.display.incoming"}, "style": {"type": "compound", "value": {"color": {"type": "string", "value": "gray"}, "italic": {"type": "byte", "value": 1}}}, "parameters": {"type": "list", "value": {"type": "string", "value": ["sender", "content"]}}}}, "narration": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "chat.type.text.narrate"}, "parameters": {"type": "list", "value": {"type": "string", "value": ["sender", "content"]}}}}}}}, {"name": {"type": "string", "value": "minecraft:msg_command_outgoing"}, "id": {"type": "int", "value": 3}, "element": {"type": "compound", "value": {"chat": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "commands.message.display.outgoing"}, "style": {"type": "compound", "value": {"color": {"type": "string", "value": "gray"}, "italic": {"type": "byte", "value": 1}}}, "parameters": {"type": "list", "value": {"type": "string", "value": ["target", "content"]}}}}, "narration": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "chat.type.text.narrate"}, "parameters": {"type": "list", "value": {"type": "string", "value": ["sender", "content"]}}}}}}}, {"name": {"type": "string", "value": "minecraft:say_command"}, "id": {"type": "int", "value": 4}, "element": {"type": "compound", "value": {"chat": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "chat.type.announcement"}, "parameters": {"type": "list", "value": {"type": "string", "value": ["sender", "content"]}}}}, "narration": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "chat.type.text.narrate"}, "parameters": {"type": "list", "value": {"type": "string", "value": ["sender", "content"]}}}}}}}, {"name": {"type": "string", "value": "minecraft:team_msg_command_incoming"}, "id": {"type": "int", "value": 5}, "element": {"type": "compound", "value": {"chat": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "chat.type.team.text"}, "parameters": {"type": "list", "value": {"type": "string", "value": ["target", "sender", "content"]}}}}, "narration": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "chat.type.text.narrate"}, "parameters": {"type": "list", "value": {"type": "string", "value": ["sender", "content"]}}}}}}}, {"name": {"type": "string", "value": "minecraft:team_msg_command_outgoing"}, "id": {"type": "int", "value": 6}, "element": {"type": "compound", "value": {"chat": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "chat.type.team.sent"}, "parameters": {"type": "list", "value": {"type": "string", "value": ["target", "sender", "content"]}}}}, "narration": {"type": "compound", "value": {"translation_key": {"type": "string", "value": "chat.type.text.narrate"}, "parameters": {"type": "list", "value": {"type": "string", "value": ["sender", "content"]}}}}}}}]}}}}, "minecraft:dimension_type": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:dimension_type"}, "value": {"type": "list", "value": {"type": "compound", "value": [{"name": {"type": "string", "value": "minecraft:overworld"}, "id": {"type": "int", "value": 0}, "element": {"type": "compound", "value": {"piglin_safe": {"type": "byte", "value": 0}, "natural": {"type": "byte", "value": 1}, "ambient_light": {"type": "float", "value": 0}, "monster_spawn_block_light_limit": {"type": "int", "value": 0}, "infiniburn": {"type": "string", "value": "#minecraft:infiniburn_overworld"}, "respawn_anchor_works": {"type": "byte", "value": 0}, "has_skylight": {"type": "byte", "value": 1}, "bed_works": {"type": "byte", "value": 1}, "effects": {"type": "string", "value": "minecraft:overworld"}, "has_raids": {"type": "byte", "value": 1}, "logical_height": {"type": "int", "value": 384}, "coordinate_scale": {"type": "double", "value": 1}, "monster_spawn_light_level": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:uniform"}, "value": {"type": "compound", "value": {"min_inclusive": {"type": "int", "value": 0}, "max_inclusive": {"type": "int", "value": 7}}}}}, "min_y": {"type": "int", "value": -64}, "ultrawarm": {"type": "byte", "value": 0}, "has_ceiling": {"type": "byte", "value": 0}, "height": {"type": "int", "value": 384}}}}, {"name": {"type": "string", "value": "minecraft:overworld_caves"}, "id": {"type": "int", "value": 1}, "element": {"type": "compound", "value": {"piglin_safe": {"type": "byte", "value": 0}, "natural": {"type": "byte", "value": 1}, "ambient_light": {"type": "float", "value": 0}, "monster_spawn_block_light_limit": {"type": "int", "value": 0}, "infiniburn": {"type": "string", "value": "#minecraft:infiniburn_overworld"}, "respawn_anchor_works": {"type": "byte", "value": 0}, "has_skylight": {"type": "byte", "value": 1}, "bed_works": {"type": "byte", "value": 1}, "effects": {"type": "string", "value": "minecraft:overworld"}, "has_raids": {"type": "byte", "value": 1}, "logical_height": {"type": "int", "value": 384}, "coordinate_scale": {"type": "double", "value": 1}, "monster_spawn_light_level": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:uniform"}, "value": {"type": "compound", "value": {"min_inclusive": {"type": "int", "value": 0}, "max_inclusive": {"type": "int", "value": 7}}}}}, "min_y": {"type": "int", "value": -64}, "ultrawarm": {"type": "byte", "value": 0}, "has_ceiling": {"type": "byte", "value": 1}, "height": {"type": "int", "value": 384}}}}, {"name": {"type": "string", "value": "minecraft:the_end"}, "id": {"type": "int", "value": 2}, "element": {"type": "compound", "value": {"piglin_safe": {"type": "byte", "value": 0}, "natural": {"type": "byte", "value": 0}, "ambient_light": {"type": "float", "value": 0}, "monster_spawn_block_light_limit": {"type": "int", "value": 0}, "infiniburn": {"type": "string", "value": "#minecraft:infiniburn_end"}, "respawn_anchor_works": {"type": "byte", "value": 0}, "has_skylight": {"type": "byte", "value": 0}, "bed_works": {"type": "byte", "value": 0}, "effects": {"type": "string", "value": "minecraft:the_end"}, "fixed_time": {"type": "long", "value": [0, 6000]}, "has_raids": {"type": "byte", "value": 1}, "logical_height": {"type": "int", "value": 256}, "coordinate_scale": {"type": "double", "value": 1}, "monster_spawn_light_level": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:uniform"}, "value": {"type": "compound", "value": {"min_inclusive": {"type": "int", "value": 0}, "max_inclusive": {"type": "int", "value": 7}}}}}, "min_y": {"type": "int", "value": 0}, "ultrawarm": {"type": "byte", "value": 0}, "has_ceiling": {"type": "byte", "value": 0}, "height": {"type": "int", "value": 256}}}}, {"name": {"type": "string", "value": "minecraft:the_nether"}, "id": {"type": "int", "value": 3}, "element": {"type": "compound", "value": {"piglin_safe": {"type": "byte", "value": 1}, "natural": {"type": "byte", "value": 0}, "ambient_light": {"type": "float", "value": 0.10000000149011612}, "monster_spawn_block_light_limit": {"type": "int", "value": 15}, "infiniburn": {"type": "string", "value": "#minecraft:infiniburn_nether"}, "respawn_anchor_works": {"type": "byte", "value": 1}, "has_skylight": {"type": "byte", "value": 0}, "bed_works": {"type": "byte", "value": 0}, "effects": {"type": "string", "value": "minecraft:the_nether"}, "fixed_time": {"type": "long", "value": [0, 18000]}, "has_raids": {"type": "byte", "value": 0}, "logical_height": {"type": "int", "value": 128}, "coordinate_scale": {"type": "double", "value": 8}, "monster_spawn_light_level": {"type": "int", "value": 7}, "min_y": {"type": "int", "value": 0}, "ultrawarm": {"type": "byte", "value": 1}, "has_ceiling": {"type": "byte", "value": 1}, "height": {"type": "int", "value": 256}}}}]}}}}, "minecraft:damage_type": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:damage_type"}, "value": {"type": "list", "value": {"type": "compound", "value": [{"name": {"type": "string", "value": "minecraft:arrow"}, "id": {"type": "int", "value": 0}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "arrow"}}}}, {"name": {"type": "string", "value": "minecraft:bad_respawn_point"}, "id": {"type": "int", "value": 1}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "always"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "badRespawnPoint"}, "death_message_type": {"type": "string", "value": "intentional_game_design"}}}}, {"name": {"type": "string", "value": "minecraft:cactus"}, "id": {"type": "int", "value": 2}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "cactus"}}}}, {"name": {"type": "string", "value": "minecraft:cramming"}, "id": {"type": "int", "value": 3}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0}, "message_id": {"type": "string", "value": "cramming"}}}}, {"name": {"type": "string", "value": "minecraft:dragon_breath"}, "id": {"type": "int", "value": 4}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0}, "message_id": {"type": "string", "value": "dragonBreath"}}}}, {"name": {"type": "string", "value": "minecraft:drown"}, "id": {"type": "int", "value": 5}, "element": {"type": "compound", "value": {"effects": {"type": "string", "value": "drowning"}, "scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0}, "message_id": {"type": "string", "value": "drown"}}}}, {"name": {"type": "string", "value": "minecraft:dry_out"}, "id": {"type": "int", "value": 6}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "dryout"}}}}, {"name": {"type": "string", "value": "minecraft:explosion"}, "id": {"type": "int", "value": 7}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "always"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "explosion"}}}}, {"name": {"type": "string", "value": "minecraft:fall"}, "id": {"type": "int", "value": 8}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0}, "message_id": {"type": "string", "value": "fall"}, "death_message_type": {"type": "string", "value": "fall_variants"}}}}, {"name": {"type": "string", "value": "minecraft:falling_anvil"}, "id": {"type": "int", "value": 9}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "anvil"}}}}, {"name": {"type": "string", "value": "minecraft:falling_block"}, "id": {"type": "int", "value": 10}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "fallingBlock"}}}}, {"name": {"type": "string", "value": "minecraft:falling_stalactite"}, "id": {"type": "int", "value": 11}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "fallingStalactite"}}}}, {"name": {"type": "string", "value": "minecraft:fireball"}, "id": {"type": "int", "value": 12}, "element": {"type": "compound", "value": {"effects": {"type": "string", "value": "burning"}, "scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "fireball"}}}}, {"name": {"type": "string", "value": "minecraft:fireworks"}, "id": {"type": "int", "value": 13}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "fireworks"}}}}, {"name": {"type": "string", "value": "minecraft:fly_into_wall"}, "id": {"type": "int", "value": 14}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0}, "message_id": {"type": "string", "value": "flyIntoWall"}}}}, {"name": {"type": "string", "value": "minecraft:freeze"}, "id": {"type": "int", "value": 15}, "element": {"type": "compound", "value": {"effects": {"type": "string", "value": "freezing"}, "scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0}, "message_id": {"type": "string", "value": "freeze"}}}}, {"name": {"type": "string", "value": "minecraft:generic"}, "id": {"type": "int", "value": 16}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0}, "message_id": {"type": "string", "value": "generic"}}}}, {"name": {"type": "string", "value": "minecraft:generic_kill"}, "id": {"type": "int", "value": 17}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0}, "message_id": {"type": "string", "value": "genericKill"}}}}, {"name": {"type": "string", "value": "minecraft:hot_floor"}, "id": {"type": "int", "value": 18}, "element": {"type": "compound", "value": {"effects": {"type": "string", "value": "burning"}, "scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "hotFloor"}}}}, {"name": {"type": "string", "value": "minecraft:in_fire"}, "id": {"type": "int", "value": 19}, "element": {"type": "compound", "value": {"effects": {"type": "string", "value": "burning"}, "scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "inFire"}}}}, {"name": {"type": "string", "value": "minecraft:in_wall"}, "id": {"type": "int", "value": 20}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0}, "message_id": {"type": "string", "value": "inWall"}}}}, {"name": {"type": "string", "value": "minecraft:indirect_magic"}, "id": {"type": "int", "value": 21}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0}, "message_id": {"type": "string", "value": "indirectMagic"}}}}, {"name": {"type": "string", "value": "minecraft:lava"}, "id": {"type": "int", "value": 22}, "element": {"type": "compound", "value": {"effects": {"type": "string", "value": "burning"}, "scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "lava"}}}}, {"name": {"type": "string", "value": "minecraft:lightning_bolt"}, "id": {"type": "int", "value": 23}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "lightningBolt"}}}}, {"name": {"type": "string", "value": "minecraft:magic"}, "id": {"type": "int", "value": 24}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0}, "message_id": {"type": "string", "value": "magic"}}}}, {"name": {"type": "string", "value": "minecraft:mob_attack"}, "id": {"type": "int", "value": 25}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "mob"}}}}, {"name": {"type": "string", "value": "minecraft:mob_attack_no_aggro"}, "id": {"type": "int", "value": 26}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "mob"}}}}, {"name": {"type": "string", "value": "minecraft:mob_projectile"}, "id": {"type": "int", "value": 27}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "mob"}}}}, {"name": {"type": "string", "value": "minecraft:on_fire"}, "id": {"type": "int", "value": 28}, "element": {"type": "compound", "value": {"effects": {"type": "string", "value": "burning"}, "scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0}, "message_id": {"type": "string", "value": "onFire"}}}}, {"name": {"type": "string", "value": "minecraft:out_of_world"}, "id": {"type": "int", "value": 29}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0}, "message_id": {"type": "string", "value": "outOfWorld"}}}}, {"name": {"type": "string", "value": "minecraft:outside_border"}, "id": {"type": "int", "value": 30}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0}, "message_id": {"type": "string", "value": "outsideBorder"}}}}, {"name": {"type": "string", "value": "minecraft:player_attack"}, "id": {"type": "int", "value": 31}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "player"}}}}, {"name": {"type": "string", "value": "minecraft:player_explosion"}, "id": {"type": "int", "value": 32}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "always"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "explosion.player"}}}}, {"name": {"type": "string", "value": "minecraft:sonic_boom"}, "id": {"type": "int", "value": 33}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "always"}, "exhaustion": {"type": "float", "value": 0}, "message_id": {"type": "string", "value": "sonic_boom"}}}}, {"name": {"type": "string", "value": "minecraft:stalagmite"}, "id": {"type": "int", "value": 34}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0}, "message_id": {"type": "string", "value": "stalagmite"}}}}, {"name": {"type": "string", "value": "minecraft:starve"}, "id": {"type": "int", "value": 35}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0}, "message_id": {"type": "string", "value": "starve"}}}}, {"name": {"type": "string", "value": "minecraft:sting"}, "id": {"type": "int", "value": 36}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "sting"}}}}, {"name": {"type": "string", "value": "minecraft:sweet_berry_bush"}, "id": {"type": "int", "value": 37}, "element": {"type": "compound", "value": {"effects": {"type": "string", "value": "poking"}, "scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "sweetBerryBush"}}}}, {"name": {"type": "string", "value": "minecraft:thorns"}, "id": {"type": "int", "value": 38}, "element": {"type": "compound", "value": {"effects": {"type": "string", "value": "thorns"}, "scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "thorns"}}}}, {"name": {"type": "string", "value": "minecraft:thrown"}, "id": {"type": "int", "value": 39}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "thrown"}}}}, {"name": {"type": "string", "value": "minecraft:trident"}, "id": {"type": "int", "value": 40}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "trident"}}}}, {"name": {"type": "string", "value": "minecraft:unattributed_fireball"}, "id": {"type": "int", "value": 41}, "element": {"type": "compound", "value": {"effects": {"type": "string", "value": "burning"}, "scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "onFire"}}}}, {"name": {"type": "string", "value": "minecraft:wither"}, "id": {"type": "int", "value": 42}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0}, "message_id": {"type": "string", "value": "wither"}}}}, {"name": {"type": "string", "value": "minecraft:wither_skull"}, "id": {"type": "int", "value": 43}, "element": {"type": "compound", "value": {"scaling": {"type": "string", "value": "when_caused_by_living_non_player"}, "exhaustion": {"type": "float", "value": 0.10000000149011612}, "message_id": {"type": "string", "value": "wither<PERSON><PERSON><PERSON>"}}}}]}}}}, "minecraft:worldgen/biome": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:worldgen/biome"}, "value": {"type": "list", "value": {"type": "compound", "value": [{"name": {"type": "string", "value": "minecraft:badlands"}, "id": {"type": "int", "value": 0}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.badlands"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7254527}, "grass_color": {"type": "int", "value": 9470285}, "foliage_color": {"type": "int", "value": 10387789}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 0}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:bamboo_jungle"}, "id": {"type": "int", "value": 1}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.bamboo_jungle"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7842047}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.949999988079071}, "downfall": {"type": "float", "value": 0.8999999761581421}}}}, {"name": {"type": "string", "value": "minecraft:basalt_deltas"}, "id": {"type": "int", "value": 2}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.nether.basalt_deltas"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7254527}, "ambient_sound": {"type": "string", "value": "minecraft:ambient.basalt_deltas.loop"}, "additions_sound": {"type": "compound", "value": {"sound": {"type": "string", "value": "minecraft:ambient.basalt_deltas.additions"}, "tick_chance": {"type": "double", "value": 0.0111}}}, "particle": {"type": "compound", "value": {"probability": {"type": "float", "value": 0.1180933341383934}, "options": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:white_ash"}}}}}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 6840176}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.basalt_deltas.mood"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 0}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:beach"}, "id": {"type": "int", "value": 3}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 7907327}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.800000011920929}, "downfall": {"type": "float", "value": 0.4000000059604645}}}}, {"name": {"type": "string", "value": "minecraft:birch_forest"}, "id": {"type": "int", "value": 4}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.forest"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 8037887}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.6000000238418579}, "downfall": {"type": "float", "value": 0.6000000238418579}}}}, {"name": {"type": "string", "value": "minecraft:cherry_grove"}, "id": {"type": "int", "value": 5}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.cherry_grove"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 8103167}, "grass_color": {"type": "int", "value": 11983713}, "foliage_color": {"type": "int", "value": 11983713}, "water_fog_color": {"type": "int", "value": 6141935}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 6141935}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.800000011920929}}}}, {"name": {"type": "string", "value": "minecraft:cold_ocean"}, "id": {"type": "int", "value": 6}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4020182}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:crimson_forest"}, "id": {"type": "int", "value": 7}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.nether.crimson_forest"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7254527}, "ambient_sound": {"type": "string", "value": "minecraft:ambient.crimson_forest.loop"}, "additions_sound": {"type": "compound", "value": {"sound": {"type": "string", "value": "minecraft:ambient.crimson_forest.additions"}, "tick_chance": {"type": "double", "value": 0.0111}}}, "particle": {"type": "compound", "value": {"probability": {"type": "float", "value": 0.02500000037252903}, "options": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:crimson_spore"}}}}}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 3343107}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.crimson_forest.mood"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 0}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:dark_forest"}, "id": {"type": "int", "value": 8}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"grass_color_modifier": {"type": "string", "value": "dark_forest"}, "music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.forest"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7972607}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.699999988079071}, "downfall": {"type": "float", "value": 0.800000011920929}}}}, {"name": {"type": "string", "value": "minecraft:deep_cold_ocean"}, "id": {"type": "int", "value": 9}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4020182}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:deep_dark"}, "id": {"type": "int", "value": 10}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.deep_dark"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7907327}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.800000011920929}, "downfall": {"type": "float", "value": 0.4000000059604645}}}}, {"name": {"type": "string", "value": "minecraft:deep_frozen_ocean"}, "id": {"type": "int", "value": 11}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 3750089}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}, "temperature_modifier": {"type": "string", "value": "frozen"}}}}, {"name": {"type": "string", "value": "minecraft:deep_lukewarm_ocean"}, "id": {"type": "int", "value": 12}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 267827}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4566514}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:deep_ocean"}, "id": {"type": "int", "value": 13}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:desert"}, "id": {"type": "int", "value": 14}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.desert"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7254527}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 0}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:dripstone_caves"}, "id": {"type": "int", "value": 15}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.dripstone_caves"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7907327}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.800000011920929}, "downfall": {"type": "float", "value": 0.4000000059604645}}}}, {"name": {"type": "string", "value": "minecraft:end_barrens"}, "id": {"type": "int", "value": 16}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 0}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 10518688}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 0}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:end_highlands"}, "id": {"type": "int", "value": 17}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 0}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 10518688}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 0}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:end_midlands"}, "id": {"type": "int", "value": 18}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 0}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 10518688}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 0}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:eroded_badlands"}, "id": {"type": "int", "value": 19}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.badlands"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7254527}, "grass_color": {"type": "int", "value": 9470285}, "foliage_color": {"type": "int", "value": 10387789}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 0}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:flower_forest"}, "id": {"type": "int", "value": 20}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.flower_forest"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7972607}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.699999988079071}, "downfall": {"type": "float", "value": 0.800000011920929}}}}, {"name": {"type": "string", "value": "minecraft:forest"}, "id": {"type": "int", "value": 21}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.forest"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7972607}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.699999988079071}, "downfall": {"type": "float", "value": 0.800000011920929}}}}, {"name": {"type": "string", "value": "minecraft:frozen_ocean"}, "id": {"type": "int", "value": 22}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8364543}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 3750089}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0}, "downfall": {"type": "float", "value": 0.5}, "temperature_modifier": {"type": "string", "value": "frozen"}}}}, {"name": {"type": "string", "value": "minecraft:frozen_peaks"}, "id": {"type": "int", "value": 23}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.frozen_peaks"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 8756735}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": -0.699999988079071}, "downfall": {"type": "float", "value": 0.8999999761581421}}}}, {"name": {"type": "string", "value": "minecraft:frozen_river"}, "id": {"type": "int", "value": 24}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8364543}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 3750089}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:grove"}, "id": {"type": "int", "value": 25}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.grove"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 8495359}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": -0.20000000298023224}, "downfall": {"type": "float", "value": 0.800000011920929}}}}, {"name": {"type": "string", "value": "minecraft:ice_spikes"}, "id": {"type": "int", "value": 26}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8364543}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:jagged_peaks"}, "id": {"type": "int", "value": 27}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.jagged_peaks"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 8756735}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": -0.699999988079071}, "downfall": {"type": "float", "value": 0.8999999761581421}}}}, {"name": {"type": "string", "value": "minecraft:jungle"}, "id": {"type": "int", "value": 28}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.jungle"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7842047}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.949999988079071}, "downfall": {"type": "float", "value": 0.8999999761581421}}}}, {"name": {"type": "string", "value": "minecraft:lukewarm_ocean"}, "id": {"type": "int", "value": 29}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 267827}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4566514}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:lush_caves"}, "id": {"type": "int", "value": 30}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.lush_caves"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:mangrove_swamp"}, "id": {"type": "int", "value": 31}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"grass_color_modifier": {"type": "string", "value": "swamp"}, "music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.swamp"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7907327}, "foliage_color": {"type": "int", "value": 9285927}, "water_fog_color": {"type": "int", "value": 5077600}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 3832426}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.800000011920929}, "downfall": {"type": "float", "value": 0.8999999761581421}}}}, {"name": {"type": "string", "value": "minecraft:meadow"}, "id": {"type": "int", "value": 32}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.meadow"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 937679}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.800000011920929}}}}, {"name": {"type": "string", "value": "minecraft:mushroom_fields"}, "id": {"type": "int", "value": 33}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 7842047}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.8999999761581421}, "downfall": {"type": "float", "value": 1}}}}, {"name": {"type": "string", "value": "minecraft:nether_wastes"}, "id": {"type": "int", "value": 34}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.nether.nether_wastes"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7254527}, "ambient_sound": {"type": "string", "value": "minecraft:ambient.nether_wastes.loop"}, "additions_sound": {"type": "compound", "value": {"sound": {"type": "string", "value": "minecraft:ambient.nether_wastes.additions"}, "tick_chance": {"type": "double", "value": 0.0111}}}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 3344392}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.nether_wastes.mood"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 0}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:ocean"}, "id": {"type": "int", "value": 35}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:old_growth_birch_forest"}, "id": {"type": "int", "value": 36}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.forest"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 8037887}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.6000000238418579}, "downfall": {"type": "float", "value": 0.6000000238418579}}}}, {"name": {"type": "string", "value": "minecraft:old_growth_pine_taiga"}, "id": {"type": "int", "value": 37}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.old_growth_taiga"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 8168447}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.30000001192092896}, "downfall": {"type": "float", "value": 0.800000011920929}}}}, {"name": {"type": "string", "value": "minecraft:old_growth_spruce_taiga"}, "id": {"type": "int", "value": 38}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.old_growth_taiga"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 8233983}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.25}, "downfall": {"type": "float", "value": 0.800000011920929}}}}, {"name": {"type": "string", "value": "minecraft:plains"}, "id": {"type": "int", "value": 39}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 7907327}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.800000011920929}, "downfall": {"type": "float", "value": 0.4000000059604645}}}}, {"name": {"type": "string", "value": "minecraft:river"}, "id": {"type": "int", "value": 40}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:savanna"}, "id": {"type": "int", "value": 41}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 7254527}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 0}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:savanna_plateau"}, "id": {"type": "int", "value": 42}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 7254527}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 0}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:small_end_islands"}, "id": {"type": "int", "value": 43}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 0}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 10518688}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 0}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:snowy_beach"}, "id": {"type": "int", "value": 44}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8364543}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4020182}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.05000000074505806}, "downfall": {"type": "float", "value": 0.30000001192092896}}}}, {"name": {"type": "string", "value": "minecraft:snowy_plains"}, "id": {"type": "int", "value": 45}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8364543}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:snowy_slopes"}, "id": {"type": "int", "value": 46}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.snowy_slopes"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 8560639}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": -0.30000001192092896}, "downfall": {"type": "float", "value": 0.8999999761581421}}}}, {"name": {"type": "string", "value": "minecraft:snowy_taiga"}, "id": {"type": "int", "value": 47}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8625919}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4020182}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": -0.5}, "downfall": {"type": "float", "value": 0.4000000059604645}}}}, {"name": {"type": "string", "value": "minecraft:soul_sand_valley"}, "id": {"type": "int", "value": 48}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.nether.soul_sand_valley"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7254527}, "ambient_sound": {"type": "string", "value": "minecraft:ambient.soul_sand_valley.loop"}, "additions_sound": {"type": "compound", "value": {"sound": {"type": "string", "value": "minecraft:ambient.soul_sand_valley.additions"}, "tick_chance": {"type": "double", "value": 0.0111}}}, "particle": {"type": "compound", "value": {"probability": {"type": "float", "value": 0.0062500000931322575}, "options": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:ash"}}}}}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 1787717}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.soul_sand_valley.mood"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 0}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:sparse_jungle"}, "id": {"type": "int", "value": 49}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.sparse_jungle"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7842047}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.949999988079071}, "downfall": {"type": "float", "value": 0.800000011920929}}}}, {"name": {"type": "string", "value": "minecraft:stony_peaks"}, "id": {"type": "int", "value": 50}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.stony_peaks"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7776511}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 1}, "downfall": {"type": "float", "value": 0.30000001192092896}}}}, {"name": {"type": "string", "value": "minecraft:stony_shore"}, "id": {"type": "int", "value": 51}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8233727}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.20000000298023224}, "downfall": {"type": "float", "value": 0.30000001192092896}}}}, {"name": {"type": "string", "value": "minecraft:sunflower_plains"}, "id": {"type": "int", "value": 52}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 7907327}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.800000011920929}, "downfall": {"type": "float", "value": 0.4000000059604645}}}}, {"name": {"type": "string", "value": "minecraft:swamp"}, "id": {"type": "int", "value": 53}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"grass_color_modifier": {"type": "string", "value": "swamp"}, "music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.swamp"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7907327}, "foliage_color": {"type": "int", "value": 6975545}, "water_fog_color": {"type": "int", "value": 2302743}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 6388580}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.800000011920929}, "downfall": {"type": "float", "value": 0.8999999761581421}}}}, {"name": {"type": "string", "value": "minecraft:taiga"}, "id": {"type": "int", "value": 54}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8233983}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.25}, "downfall": {"type": "float", "value": 0.800000011920929}}}}, {"name": {"type": "string", "value": "minecraft:the_end"}, "id": {"type": "int", "value": 55}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 0}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 10518688}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 0}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:the_void"}, "id": {"type": "int", "value": 56}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 0}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:warm_ocean"}, "id": {"type": "int", "value": 57}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8103167}, "water_fog_color": {"type": "int", "value": 270131}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4445678}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.5}, "downfall": {"type": "float", "value": 0.5}}}}, {"name": {"type": "string", "value": "minecraft:warped_forest"}, "id": {"type": "int", "value": 58}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.nether.warped_forest"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7254527}, "ambient_sound": {"type": "string", "value": "minecraft:ambient.warped_forest.loop"}, "additions_sound": {"type": "compound", "value": {"sound": {"type": "string", "value": "minecraft:ambient.warped_forest.additions"}, "tick_chance": {"type": "double", "value": 0.0111}}}, "particle": {"type": "compound", "value": {"probability": {"type": "float", "value": 0.014279999770224094}, "options": {"type": "compound", "value": {"type": {"type": "string", "value": "minecraft:warped_spore"}}}}}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 1705242}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.warped_forest.mood"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 0}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:windswept_forest"}, "id": {"type": "int", "value": 59}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8233727}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.20000000298023224}, "downfall": {"type": "float", "value": 0.30000001192092896}}}}, {"name": {"type": "string", "value": "minecraft:windswept_gravelly_hills"}, "id": {"type": "int", "value": 60}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8233727}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.20000000298023224}, "downfall": {"type": "float", "value": 0.30000001192092896}}}}, {"name": {"type": "string", "value": "minecraft:windswept_hills"}, "id": {"type": "int", "value": 61}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 8233727}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 1}, "temperature": {"type": "float", "value": 0.20000000298023224}, "downfall": {"type": "float", "value": 0.30000001192092896}}}}, {"name": {"type": "string", "value": "minecraft:windswept_savanna"}, "id": {"type": "int", "value": 62}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"sky_color": {"type": "int", "value": 7254527}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 0}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}, {"name": {"type": "string", "value": "minecraft:wooded_badlands"}, "id": {"type": "int", "value": 63}, "element": {"type": "compound", "value": {"effects": {"type": "compound", "value": {"music": {"type": "compound", "value": {"replace_current_music": {"type": "byte", "value": 0}, "max_delay": {"type": "int", "value": 24000}, "sound": {"type": "string", "value": "minecraft:music.overworld.badlands"}, "min_delay": {"type": "int", "value": 12000}}}, "sky_color": {"type": "int", "value": 7254527}, "grass_color": {"type": "int", "value": 9470285}, "foliage_color": {"type": "int", "value": 10387789}, "water_fog_color": {"type": "int", "value": 329011}, "fog_color": {"type": "int", "value": 12638463}, "water_color": {"type": "int", "value": 4159204}, "mood_sound": {"type": "compound", "value": {"tick_delay": {"type": "int", "value": 6000}, "offset": {"type": "double", "value": 2}, "sound": {"type": "string", "value": "minecraft:ambient.cave"}, "block_search_extent": {"type": "int", "value": 8}}}}}, "has_precipitation": {"type": "byte", "value": 0}, "temperature": {"type": "float", "value": 2}, "downfall": {"type": "float", "value": 0}}}}]}}}}}}, "worldType": "minecraft:overworld", "worldName": "minecraft:overworld", "hashedSeed": [-1362923270, 2103615173], "maxPlayers": 120, "viewDistance": 10, "simulationDistance": 10, "reducedDebugInfo": false, "enableRespawnScreen": true, "isDebug": false, "isFlat": false, "portalCooldown": 0}