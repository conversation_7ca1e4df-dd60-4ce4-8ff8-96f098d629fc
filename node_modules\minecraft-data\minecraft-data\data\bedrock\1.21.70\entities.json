[{"id": 0, "internalId": 10, "name": "chicken", "displayName": "Chicken", "height": 0.7, "width": 0.4, "length": null, "offset": null, "type": "animal", "category": "Passive mobs"}, {"id": 1, "internalId": 11, "name": "cow", "displayName": "Cow", "height": 1.4, "width": 0.9, "length": null, "offset": null, "type": "animal", "category": "Passive mobs"}, {"id": 2, "internalId": 12, "name": "pig", "displayName": "Pig", "height": 0.9, "width": null, "length": null, "offset": null, "type": "animal", "category": "Passive mobs"}, {"id": 3, "internalId": 13, "name": "sheep", "displayName": "Sheep", "height": 1.3, "width": 0.9, "length": null, "offset": null, "type": "animal", "category": "Passive mobs"}, {"id": 4, "internalId": 14, "name": "wolf", "displayName": "<PERSON>", "height": 0.85, "width": 0.6, "length": null, "offset": null, "type": "animal", "category": "Passive mobs"}, {"id": 5, "internalId": 15, "name": "villager_v2", "displayName": "Villager", "height": 1.8, "width": 0.6, "length": 0.6, "offset": 1.62, "type": "passive", "category": "Passive mobs"}, {"id": 6, "internalId": 16, "name": "mooshroom", "displayName": "Mooshroom", "height": 1.4, "width": 0.9, "length": null, "offset": null, "type": "animal", "category": "Passive mobs"}, {"id": 7, "internalId": 17, "name": "squid", "displayName": "Squid", "height": 0.8, "width": null, "length": null, "offset": null, "type": "passive", "category": "Passive mobs"}, {"id": 8, "internalId": 18, "name": "rabbit", "displayName": "Rabbit", "height": 0.5, "width": 0.4, "length": null, "offset": null, "type": "animal", "category": "Passive mobs"}, {"id": 9, "internalId": 19, "name": "bat", "displayName": "Bat", "height": 0.9, "width": 0.5, "length": null, "offset": null, "type": "ambient", "category": "Passive mobs"}, {"id": 10, "internalId": 20, "name": "iron_golem", "displayName": "Iron Golem", "height": 2.7, "width": 1.4, "length": null, "offset": null, "type": "mob", "category": "Passive mobs"}, {"id": 11, "internalId": 21, "name": "snow_golem", "displayName": "Snow Golem", "height": 1.9, "width": 0.7, "length": null, "offset": null, "type": "mob", "category": "Passive mobs"}, {"id": 12, "internalId": 22, "name": "ocelot", "displayName": "Ocelot", "height": 0.35, "width": 0.3, "length": null, "offset": null, "type": "animal", "category": "Passive mobs"}, {"id": 13, "internalId": 23, "name": "horse", "displayName": "Horse", "height": 1.6, "width": 1.3965, "length": null, "offset": null, "type": "animal", "category": "Passive mobs"}, {"id": 14, "internalId": 24, "name": "donkey", "displayName": "<PERSON><PERSON>", "height": 1.6, "width": 1.3965, "length": null, "offset": null, "type": "animal", "category": "Passive mobs"}, {"id": 15, "internalId": 25, "name": "mule", "displayName": "<PERSON><PERSON>", "height": 1.6, "width": 1.3965, "length": null, "offset": null, "type": "animal", "category": "Passive mobs"}, {"id": 16, "internalId": 26, "name": "skeleton_horse", "displayName": "Skeleton Horse", "height": 1.6, "width": 1.3965, "length": null, "offset": null, "type": "animal", "category": "Hostile mobs"}, {"id": 17, "internalId": 27, "name": "zombie_horse", "displayName": "Zombie Horse", "height": 1.6, "width": 1.3965, "length": null, "offset": null, "type": "animal", "category": "Hostile mobs"}, {"id": 18, "internalId": 28, "name": "polar_bear", "displayName": "Polar Bear", "height": 1.4, "width": 1.3, "length": null, "offset": null, "type": "animal", "category": "Passive mobs"}, {"id": 19, "internalId": 29, "name": "llama", "displayName": "Llama", "height": 1.87, "width": 0.9, "length": null, "offset": null, "type": "animal", "category": "Passive mobs"}, {"id": 20, "internalId": 29, "name": "llama", "displayName": "Trader <PERSON><PERSON><PERSON>", "height": 1.187, "width": 0.9, "length": 0, "offset": 0, "type": "animal", "category": "Passive mobs"}, {"id": 21, "internalId": 30, "name": "parrot", "displayName": "<PERSON><PERSON><PERSON>", "height": 0.9, "width": 0.5, "length": null, "offset": null, "type": "animal", "category": "Passive mobs"}, {"id": 22, "internalId": 31, "name": "dolphin", "displayName": "Dolphin", "height": 0.6, "width": 0.9, "length": null, "offset": null, "type": "passive", "category": "Passive mobs"}, {"id": 23, "internalId": 32, "name": "zombie", "displayName": "Zombie", "height": 1.8, "width": 0.6, "length": 0.6, "offset": 1.62, "type": "hostile", "category": "Hostile mobs"}, {"id": 24, "internalId": 32, "name": "zombie", "displayName": "Giant", "height": 1.8, "width": 0.6, "length": 0.6, "offset": 1.62, "type": "hostile", "category": "Hostile mobs"}, {"id": 25, "internalId": 33, "name": "creeper", "displayName": "C<PERSON>per", "height": 1.7, "width": 0.6, "length": 0.6, "offset": 1.62, "type": "hostile", "category": "Hostile mobs"}, {"id": 26, "internalId": 34, "name": "skeleton", "displayName": "Skeleton", "height": 1.8, "width": 0.6, "length": 0.6, "offset": 1.62, "type": "hostile", "category": "Hostile mobs"}, {"id": 27, "internalId": 35, "name": "spider", "displayName": "Spider", "height": 0.9, "width": 1.4, "length": 1.4, "offset": 1, "type": "hostile", "category": "Hostile mobs"}, {"id": 28, "internalId": 36, "name": "zombie_pigman", "displayName": "Zombified Piglin", "height": 1.95, "width": 0.6, "length": 0.6, "offset": 1.62, "type": "hostile", "category": "Hostile mobs"}, {"id": 29, "internalId": 37, "name": "slime", "displayName": "Slime", "height": 0.51, "width": null, "length": null, "offset": null, "type": "mob", "category": "Hostile mobs"}, {"id": 30, "internalId": 38, "name": "enderman", "displayName": "<PERSON><PERSON>", "height": 2.9, "width": 0.6, "length": null, "offset": null, "type": "hostile", "category": "Hostile mobs"}, {"id": 31, "internalId": 39, "name": "silverfish", "displayName": "Silverfish", "height": 0.3, "width": 0.4, "length": null, "offset": null, "type": "hostile", "category": "Hostile mobs"}, {"id": 32, "internalId": 40, "name": "cave_spider", "displayName": "<PERSON> Spider", "height": 0.5, "width": 0.7, "length": null, "offset": null, "type": "hostile", "category": "Hostile mobs"}, {"id": 33, "internalId": 41, "name": "ghast", "displayName": "<PERSON><PERSON><PERSON>", "height": 4, "width": null, "length": null, "offset": null, "type": "mob", "category": "Hostile mobs"}, {"id": 34, "internalId": 42, "name": "magma_cube", "displayName": "Magma Cube", "height": 0.51, "width": null, "length": null, "offset": null, "type": "mob", "category": "Hostile mobs"}, {"id": 35, "internalId": 43, "name": "blaze", "displayName": "Blaze", "height": 1.8, "width": 0.6, "length": null, "offset": null, "type": "hostile", "category": "Hostile mobs"}, {"id": 36, "internalId": 44, "name": "zombie_villager_v2", "displayName": "Zombie Villager", "height": 1.8, "width": 0.6, "length": 0.6, "offset": 1.62, "type": "hostile", "category": "Hostile mobs"}, {"id": 37, "internalId": 45, "name": "witch", "displayName": "Witch", "height": 1.8, "width": 0.6, "length": 0.6, "offset": 1.62, "type": "hostile", "category": "Hostile mobs"}, {"id": 38, "internalId": 46, "name": "stray", "displayName": "Stray", "height": 1.8, "width": 0.6, "length": 0.6, "offset": 1.62, "type": "hostile", "category": "Hostile mobs"}, {"id": 39, "internalId": 47, "name": "husk", "displayName": "Husk", "height": 1.8, "width": 0.6, "length": 0.6, "offset": 1.62, "type": "hostile", "category": "Hostile mobs"}, {"id": 40, "internalId": 48, "name": "wither_skeleton", "displayName": "<PERSON><PERSON>", "height": 2.4, "width": 0.7, "length": null, "offset": null, "type": "hostile", "category": "Hostile mobs"}, {"id": 41, "internalId": 49, "name": "guardian", "displayName": "Guardian", "height": 0.85, "width": null, "length": null, "offset": null, "type": "hostile", "category": "Hostile mobs"}, {"id": 42, "internalId": 50, "name": "elder_guardian", "displayName": "Elder Guardian", "height": 1.9975, "width": null, "length": null, "offset": null, "type": "hostile", "category": "Hostile mobs"}, {"id": 43, "internalId": 51, "name": "npc", "displayName": "npc", "height": 1.8, "width": 0.6, "length": 0.6, "offset": 1.62, "type": ""}, {"id": 44, "internalId": 52, "name": "wither", "displayName": "<PERSON>er", "height": 3.5, "width": 0.9, "length": null, "offset": null, "type": "hostile", "category": "Hostile mobs"}, {"id": 45, "internalId": 53, "name": "ender_dragon", "displayName": "<PERSON><PERSON>", "height": 0, "width": 0, "length": null, "offset": null, "type": "mob", "category": "Hostile mobs"}, {"id": 46, "internalId": 54, "name": "s<PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "height": 1, "width": 1, "length": null, "offset": null, "type": "mob", "category": "Hostile mobs"}, {"id": 47, "internalId": 55, "name": "endermite", "displayName": "Endermite", "height": 0.3, "width": 0.4, "length": null, "offset": null, "type": "hostile", "category": "Hostile mobs"}, {"id": 48, "internalId": 56, "name": "agent", "displayName": "agent", "height": 0, "width": null, "length": null, "offset": null, "type": ""}, {"id": 49, "internalId": 57, "name": "vindicator", "displayName": "Vindicator", "height": 1.8, "width": 0.6, "length": 0.6, "offset": 1.62, "type": "hostile", "category": "Hostile mobs"}, {"id": 50, "internalId": 114, "name": "pillager", "displayName": "Pillager", "height": 1.8, "width": 0.6, "length": 0.6, "offset": 1.62, "type": "hostile", "category": "Hostile mobs"}, {"id": 51, "internalId": 118, "name": "wandering_trader", "displayName": "Wandering Trader", "height": 1.8, "width": 0.6, "length": 0.6, "offset": 1.62, "type": "passive", "category": "Passive mobs"}, {"id": 52, "internalId": 58, "name": "phantom", "displayName": "Phantom", "height": 0.5, "width": 0.9, "length": 0.9, "offset": 0.6, "type": "mob", "category": "Hostile mobs"}, {"id": 53, "internalId": 59, "name": "ravager", "displayName": "<PERSON><PERSON><PERSON>", "height": 1.9, "width": 1.2, "length": null, "offset": null, "type": "hostile", "category": "Hostile mobs"}, {"id": 54, "internalId": 61, "name": "armor_stand", "displayName": "Armor Stand", "height": 1.975, "width": 0.5, "length": null, "offset": null, "type": "living", "category": "Immobile"}, {"id": 55, "internalId": 62, "name": "tripod_camera", "displayName": "tripod_camera", "height": 0, "width": null, "length": null, "offset": null, "type": ""}, {"id": 56, "internalId": 63, "name": "player", "displayName": "Player", "height": 1.8, "width": 0.6, "length": 0.6, "offset": 1.62, "type": "player", "category": "UNKNOWN"}, {"id": 57, "internalId": 64, "name": "item", "displayName": "<PERSON><PERSON>", "height": 0.25, "width": 0.25, "length": 0.25, "offset": 0.125, "type": "other", "category": "UNKNOWN"}, {"id": 58, "internalId": 65, "name": "tnt", "displayName": "tnt", "height": 0.98, "width": 0.98, "length": 0.98, "offset": 0, "type": ""}, {"id": 59, "internalId": 66, "name": "falling_block", "displayName": "Falling Block", "height": 0.98, "width": 0.98, "length": null, "offset": null, "type": "other", "category": "UNKNOWN"}, {"id": 60, "internalId": 67, "name": "moving_block", "displayName": "moving_block", "height": 0, "width": null, "length": null, "offset": null, "type": ""}, {"id": 61, "internalId": 68, "name": "xp_bottle", "displayName": "xp_bottle", "height": 0.25, "width": 0.25, "length": 0, "offset": 0, "type": ""}, {"id": 62, "internalId": 69, "name": "xp_orb", "displayName": "Experience Orb", "height": 0, "width": 0, "length": 0, "offset": 0, "type": "other", "category": "UNKNOWN"}, {"id": 63, "internalId": 70, "name": "eye_of_ender_signal", "displayName": "Eye of <PERSON>er", "height": 0.25, "width": 0.25, "length": 0, "offset": 0, "type": "other", "category": "UNKNOWN"}, {"id": 64, "internalId": 71, "name": "ender_crystal", "displayName": "End Crystal", "height": 2, "width": 2, "length": 2, "offset": 0, "type": "other", "category": "Immobile"}, {"id": 65, "internalId": 72, "name": "fireworks_rocket", "displayName": "Firework Rocket", "height": 0.25, "width": 0.25, "length": 0.25, "offset": 0, "type": "projectile", "category": "Projectiles"}, {"id": 66, "internalId": 73, "name": "thrown_trident", "displayName": "Trident", "height": 0, "width": 0, "length": 0, "offset": 0, "type": "projectile", "category": "Projectiles"}, {"id": 67, "internalId": 74, "name": "turtle", "displayName": "Turtle", "height": 0.4, "width": 1.2, "length": null, "offset": null, "type": "animal", "category": "Passive mobs"}, {"id": 68, "internalId": 75, "name": "cat", "displayName": "Cat", "height": 0.35, "width": 0.3, "length": null, "offset": null, "type": "animal", "category": "Passive mobs"}, {"id": 69, "internalId": 76, "name": "shulker_bullet", "displayName": "<PERSON><PERSON><PERSON> Bullet", "height": 0.3125, "width": null, "length": null, "offset": null, "type": "projectile", "category": "Projectiles"}, {"id": 70, "internalId": 77, "name": "fishing_hook", "displayName": "Fishing Bobber", "height": 0, "width": 0, "length": 0, "offset": 0, "type": "projectile", "category": "Projectiles"}, {"id": 71, "internalId": 78, "name": "chalkboard", "displayName": "chalkboard", "height": 0, "width": null, "length": null, "offset": null, "type": ""}, {"id": 72, "internalId": 79, "name": "dragon_fireball", "displayName": "Dragon Fireball", "height": 1, "width": null, "length": null, "offset": null, "type": "projectile", "category": "Projectiles"}, {"id": 73, "internalId": 80, "name": "arrow", "displayName": "Arrow", "height": 0.25, "width": 0.25, "length": null, "offset": null, "type": "projectile", "category": "Projectiles"}, {"id": 74, "internalId": 80, "name": "arrow", "displayName": "Spectral Arrow", "height": 0.25, "width": 0.25, "length": 0.25, "offset": 0, "type": "projectile", "category": "Projectiles"}, {"id": 75, "internalId": 81, "name": "snowball", "displayName": "Snowball", "height": 0.25, "width": null, "length": null, "offset": null, "type": "projectile", "category": "Projectiles"}, {"id": 76, "internalId": 82, "name": "egg", "displayName": "egg", "height": 0.25, "width": 0.25, "length": 0.25, "offset": 0, "type": ""}, {"id": 77, "internalId": 83, "name": "painting", "displayName": "Painting", "height": 0, "width": null, "length": null, "offset": null, "type": "other", "category": "Immobile"}, {"id": 78, "internalId": 84, "name": "minecart", "displayName": "Minecart", "height": 0.7, "width": 0.98, "length": 0.98, "offset": 0.35, "type": "other", "category": "Vehicles"}, {"id": 79, "internalId": 85, "name": "fireball", "displayName": "Fireball", "height": 1, "width": null, "length": null, "offset": null, "type": "projectile", "category": "Projectiles"}, {"id": 80, "internalId": 86, "name": "splash_potion", "displayName": "splash_potion", "height": 0.25, "width": 0.25, "length": 0.25, "offset": 0, "type": ""}, {"id": 81, "internalId": 87, "name": "ender_pearl", "displayName": "ender_pearl", "height": 0.25, "width": 0.25, "length": 0.25, "offset": 0, "type": ""}, {"id": 82, "internalId": 88, "name": "leash_knot", "displayName": "<PERSON><PERSON>", "height": 0.5, "width": 0.375, "length": null, "offset": null, "type": "other", "category": "Immobile"}, {"id": 83, "internalId": 89, "name": "wither_skull", "displayName": "<PERSON><PERSON>", "height": 0.3125, "width": null, "length": null, "offset": null, "type": "projectile", "category": "Projectiles"}, {"id": 84, "internalId": 90, "name": "boat", "displayName": "boat", "height": 0.6, "width": 1.6, "length": 1.6, "offset": 0.35, "type": ""}, {"id": 85, "internalId": 91, "name": "wither_skull_dangerous", "displayName": "wither_skull_dangerous", "height": 0, "width": null, "length": null, "offset": null, "type": ""}, {"id": 86, "internalId": 93, "name": "lightning_bolt", "displayName": "Lightning Bolt", "height": 0, "width": null, "length": null, "offset": null, "type": "other", "category": "UNKNOWN"}, {"id": 87, "internalId": 94, "name": "small_fireball", "displayName": "Small Fireball", "height": 0.3125, "width": null, "length": null, "offset": null, "type": "projectile", "category": "Projectiles"}, {"id": 88, "internalId": 95, "name": "area_effect_cloud", "displayName": "Area Effect Cloud", "height": 0.5, "width": 1, "length": null, "offset": null, "type": "other", "category": "UNKNOWN"}, {"id": 89, "internalId": 96, "name": "hopper_minecart", "displayName": "hopper_minecart", "height": 0.7, "width": 0.98, "length": 0.98, "offset": 0.35, "type": ""}, {"id": 90, "internalId": 97, "name": "tnt_minecart", "displayName": "tnt_minecart", "height": 0.7, "width": 0.98, "length": 0.98, "offset": 0.35, "type": ""}, {"id": 91, "internalId": 98, "name": "chest_minecart", "displayName": "chest_minecart", "height": 0.7, "width": 0.98, "length": 0.98, "offset": 0.35, "type": ""}, {"id": 92, "internalId": 98, "name": "minecart", "displayName": "minecart", "height": 0.7, "width": 0.98, "length": 0.98, "offset": 0.35, "type": ""}, {"id": 93, "internalId": 98, "name": "minecart", "displayName": "minecart", "height": 0.7, "width": 0.98, "length": 0.98, "offset": 0.35, "type": ""}, {"id": 94, "internalId": 100, "name": "command_block_minecart", "displayName": "command_block_minecart", "height": 0.7, "width": 0.98, "length": 0.98, "offset": 0.35, "type": ""}, {"id": 95, "internalId": 101, "name": "lingering_potion", "displayName": "Lingering Potion", "height": 0, "width": null, "length": null, "offset": null, "type": "projectile", "category": "Projectiles"}, {"id": 96, "internalId": 102, "name": "llama_spit", "displayName": "Llama <PERSON>", "height": 0.25, "width": null, "length": null, "offset": null, "type": "projectile", "category": "Projectiles"}, {"id": 97, "internalId": 103, "name": "evocation_fang", "displayName": "Evoker <PERSON>s", "height": 0.8, "width": 0.5, "length": 0.5, "offset": 0, "type": "other", "category": "Hostile mobs"}, {"id": 98, "internalId": 104, "name": "evocation_illager", "displayName": "Evoker", "height": 1.95, "width": 0.6, "length": 0.6, "offset": 0, "type": "hostile", "category": "Hostile mobs"}, {"id": 99, "internalId": 105, "name": "vex", "displayName": "Vex", "height": 0.8, "width": 0.4, "length": null, "offset": null, "type": "hostile", "category": "Hostile mobs"}, {"id": 100, "internalId": 106, "name": "ice_bomb", "displayName": "ice_bomb", "height": 0, "width": null, "length": null, "offset": null, "type": ""}, {"id": 101, "internalId": 107, "name": "balloon", "displayName": "balloon", "height": 0, "width": null, "length": null, "offset": null, "type": ""}, {"id": 102, "internalId": 108, "name": "pufferfish", "displayName": "Pufferfish", "height": 0.7, "width": 0.7, "length": null, "offset": null, "type": "water_creature", "category": "Passive mobs"}, {"id": 103, "internalId": 109, "name": "salmon", "displayName": "Salmon", "height": 0.5, "width": 0.7, "length": null, "offset": null, "type": "water_creature", "category": "Passive mobs"}, {"id": 104, "internalId": 110, "name": "drowned", "displayName": "Drowned", "height": 1.95, "width": 0.6, "length": null, "offset": null, "type": "hostile", "category": "Hostile mobs"}, {"id": 105, "internalId": 111, "name": "tropicalfish", "displayName": "Tropical Fish", "height": 0.6, "width": 0.6, "length": 0, "offset": 0, "type": "water_creature", "category": "Passive mobs"}, {"id": 106, "internalId": 112, "name": "cod", "displayName": "Cod", "height": 0.25, "width": 0.5, "length": null, "offset": null, "type": "water_creature", "category": "Passive mobs"}, {"id": 107, "internalId": 113, "name": "panda", "displayName": "Panda", "height": 1.25, "width": 1.125, "length": 1.825, "offset": null, "type": "animal", "category": "Passive mobs"}, {"id": 108, "internalId": 121, "name": "fox", "displayName": "Fox", "height": 0.5, "width": 1.25, "length": null, "offset": null, "type": "animal", "category": "Passive mobs"}, {"id": 109, "internalId": 122, "name": "bee", "displayName": "Bee", "height": 0.6, "width": 0.6, "length": null, "offset": null, "type": "animal", "category": "Passive mobs"}, {"id": 110, "internalId": 125, "name": "strider", "displayName": "Strider", "height": 1.7, "width": 0.9, "length": 0, "offset": 0, "type": "animal", "category": "Passive mobs"}, {"id": 111, "internalId": 124, "name": "hoglin", "displayName": "<PERSON><PERSON><PERSON>", "height": 1.4, "width": 1.3965, "length": 1.3965, "offset": 0, "type": "animal", "category": "Hostile mobs"}, {"id": 112, "internalId": 126, "name": "<PERSON>oglin", "displayName": "<PERSON><PERSON><PERSON>", "height": 1.4, "width": 1.3965, "length": 1.3965, "offset": 0, "type": "hostile", "category": "Hostile mobs"}, {"id": 113, "internalId": 123, "name": "piglin", "displayName": "<PERSON><PERSON>", "height": 1.95, "width": 0.6, "length": 0.6, "offset": 0, "type": "hostile", "category": "Hostile mobs"}, {"id": 114, "internalId": 127, "name": "piglin_brute", "displayName": "<PERSON><PERSON> B<PERSON>", "height": 1.95, "width": 0.6, "length": 0.6, "offset": 0, "type": "hostile", "category": "Hostile mobs"}, {"id": 115, "internalId": 0, "name": "axolotl", "displayName": "Axolotl", "height": 0.42, "width": 0.7, "length": 0.7, "offset": 0, "type": "animal", "category": "Passive mobs"}, {"id": 116, "internalId": 0, "name": "glow_squid", "displayName": "Glow Squid", "height": 0.8, "width": 0.8, "length": 0.8, "offset": 0, "type": "passive", "category": "Passive mobs"}, {"id": 117, "internalId": 0, "name": "goat", "displayName": "Goa<PERSON>", "height": 1.3, "width": 0.9, "length": 0.9, "offset": 0, "type": "animal", "category": "Passive mobs"}, {"id": 118, "internalId": 0, "name": "item_frame", "displayName": "<PERSON><PERSON>", "height": 0, "width": 0, "length": null, "offset": null, "type": "other", "category": "Immobile"}, {"id": 119, "internalId": 0, "name": "glow_item_frame", "displayName": "G<PERSON> Item <PERSON>", "height": 0, "width": 0, "length": null, "offset": null, "type": "other", "category": "Immobile"}, {"id": 120, "internalId": 104, "name": "evocation_illager", "displayName": "<PERSON><PERSON><PERSON>", "height": 1.8, "width": 0.6, "length": 0.6, "offset": 1.62, "type": "hostile", "category": "Hostile mobs"}, {"id": 121, "internalId": 32, "name": "armor_stand", "displayName": "armor_stand", "height": 0, "width": 0, "length": 0, "offset": 0, "type": ""}]