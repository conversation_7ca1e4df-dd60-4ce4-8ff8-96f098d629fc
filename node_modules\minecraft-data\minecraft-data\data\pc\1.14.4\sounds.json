[{"id": 0, "name": "ambient.cave"}, {"id": 1, "name": "ambient.underwater.enter"}, {"id": 2, "name": "ambient.underwater.exit"}, {"id": 3, "name": "ambient.underwater.loop"}, {"id": 4, "name": "ambient.underwater.loop.additions"}, {"id": 5, "name": "ambient.underwater.loop.additions.rare"}, {"id": 6, "name": "ambient.underwater.loop.additions.ultra_rare"}, {"id": 7, "name": "block.anvil.break"}, {"id": 8, "name": "block.anvil.destroy"}, {"id": 9, "name": "block.anvil.fall"}, {"id": 10, "name": "block.anvil.hit"}, {"id": 11, "name": "block.anvil.land"}, {"id": 12, "name": "block.anvil.place"}, {"id": 13, "name": "block.anvil.step"}, {"id": 14, "name": "block.anvil.use"}, {"id": 15, "name": "item.armor.equip_chain"}, {"id": 16, "name": "item.armor.equip_diamond"}, {"id": 17, "name": "item.armor.equip_elytra"}, {"id": 18, "name": "item.armor.equip_generic"}, {"id": 19, "name": "item.armor.equip_gold"}, {"id": 20, "name": "item.armor.equip_iron"}, {"id": 21, "name": "item.armor.equip_leather"}, {"id": 22, "name": "item.armor.equip_turtle"}, {"id": 23, "name": "entity.armor_stand.break"}, {"id": 24, "name": "entity.armor_stand.fall"}, {"id": 25, "name": "entity.armor_stand.hit"}, {"id": 26, "name": "entity.armor_stand.place"}, {"id": 27, "name": "entity.arrow.hit"}, {"id": 28, "name": "entity.arrow.hit_player"}, {"id": 29, "name": "entity.arrow.shoot"}, {"id": 30, "name": "item.axe.strip"}, {"id": 31, "name": "block.bamboo.break"}, {"id": 32, "name": "block.bamboo.fall"}, {"id": 33, "name": "block.bamboo.hit"}, {"id": 34, "name": "block.bamboo.place"}, {"id": 35, "name": "block.bamboo.step"}, {"id": 36, "name": "block.bamboo_sapling.break"}, {"id": 37, "name": "block.bamboo_sapling.hit"}, {"id": 38, "name": "block.bamboo_sapling.place"}, {"id": 39, "name": "block.barrel.close"}, {"id": 40, "name": "block.barrel.open"}, {"id": 41, "name": "entity.bat.ambient"}, {"id": 42, "name": "entity.bat.death"}, {"id": 43, "name": "entity.bat.hurt"}, {"id": 44, "name": "entity.bat.loop"}, {"id": 45, "name": "entity.bat.takeoff"}, {"id": 46, "name": "block.beacon.activate"}, {"id": 47, "name": "block.beacon.ambient"}, {"id": 48, "name": "block.beacon.deactivate"}, {"id": 49, "name": "block.beacon.power_select"}, {"id": 50, "name": "block.bell.use"}, {"id": 51, "name": "block.bell.resonate"}, {"id": 52, "name": "entity.blaze.ambient"}, {"id": 53, "name": "entity.blaze.burn"}, {"id": 54, "name": "entity.blaze.death"}, {"id": 55, "name": "entity.blaze.hurt"}, {"id": 56, "name": "entity.blaze.shoot"}, {"id": 57, "name": "entity.boat.paddle_land"}, {"id": 58, "name": "entity.boat.paddle_water"}, {"id": 59, "name": "item.book.page_turn"}, {"id": 60, "name": "item.book.put"}, {"id": 61, "name": "entity.fishing_bobber.retrieve"}, {"id": 62, "name": "entity.fishing_bobber.splash"}, {"id": 63, "name": "entity.fishing_bobber.throw"}, {"id": 64, "name": "block.blastfurnace.fire_crackle"}, {"id": 65, "name": "item.bottle.empty"}, {"id": 66, "name": "item.bottle.fill"}, {"id": 67, "name": "item.bottle.fill_dragonbreath"}, {"id": 68, "name": "block.brewing_stand.brew"}, {"id": 69, "name": "block.bubble_column.bubble_pop"}, {"id": 70, "name": "block.bubble_column.upwards_ambient"}, {"id": 71, "name": "block.bubble_column.upwards_inside"}, {"id": 72, "name": "block.bubble_column.whirlpool_ambient"}, {"id": 73, "name": "block.bubble_column.whirlpool_inside"}, {"id": 74, "name": "item.bucket.empty"}, {"id": 75, "name": "item.bucket.empty_fish"}, {"id": 76, "name": "item.bucket.empty_lava"}, {"id": 77, "name": "item.bucket.fill"}, {"id": 78, "name": "item.bucket.fill_fish"}, {"id": 79, "name": "item.bucket.fill_lava"}, {"id": 80, "name": "block.campfire.crackle"}, {"id": 81, "name": "entity.cat.ambient"}, {"id": 82, "name": "entity.cat.stray_ambient"}, {"id": 83, "name": "entity.cat.death"}, {"id": 84, "name": "entity.cat.eat"}, {"id": 85, "name": "entity.cat.hiss"}, {"id": 86, "name": "entity.cat.beg_for_food"}, {"id": 87, "name": "entity.cat.hurt"}, {"id": 88, "name": "entity.cat.purr"}, {"id": 89, "name": "entity.cat.purreow"}, {"id": 90, "name": "block.chest.close"}, {"id": 91, "name": "block.chest.locked"}, {"id": 92, "name": "block.chest.open"}, {"id": 93, "name": "entity.chicken.ambient"}, {"id": 94, "name": "entity.chicken.death"}, {"id": 95, "name": "entity.chicken.egg"}, {"id": 96, "name": "entity.chicken.hurt"}, {"id": 97, "name": "entity.chicken.step"}, {"id": 98, "name": "block.chorus_flower.death"}, {"id": 99, "name": "block.chorus_flower.grow"}, {"id": 100, "name": "item.chorus_fruit.teleport"}, {"id": 101, "name": "block.wool.break"}, {"id": 102, "name": "block.wool.fall"}, {"id": 103, "name": "block.wool.hit"}, {"id": 104, "name": "block.wool.place"}, {"id": 105, "name": "block.wool.step"}, {"id": 106, "name": "entity.cod.ambient"}, {"id": 107, "name": "entity.cod.death"}, {"id": 108, "name": "entity.cod.flop"}, {"id": 109, "name": "entity.cod.hurt"}, {"id": 110, "name": "block.comparator.click"}, {"id": 111, "name": "block.composter.empty"}, {"id": 112, "name": "block.composter.fill"}, {"id": 113, "name": "block.composter.fill_success"}, {"id": 114, "name": "block.composter.ready"}, {"id": 115, "name": "block.conduit.activate"}, {"id": 116, "name": "block.conduit.ambient"}, {"id": 117, "name": "block.conduit.ambient.short"}, {"id": 118, "name": "block.conduit.attack.target"}, {"id": 119, "name": "block.conduit.deactivate"}, {"id": 120, "name": "entity.cow.ambient"}, {"id": 121, "name": "entity.cow.death"}, {"id": 122, "name": "entity.cow.hurt"}, {"id": 123, "name": "entity.cow.milk"}, {"id": 124, "name": "entity.cow.step"}, {"id": 125, "name": "entity.creeper.death"}, {"id": 126, "name": "entity.creeper.hurt"}, {"id": 127, "name": "entity.creeper.primed"}, {"id": 128, "name": "block.crop.break"}, {"id": 129, "name": "item.crop.plant"}, {"id": 130, "name": "item.crossbow.hit"}, {"id": 131, "name": "item.crossbow.loading_end"}, {"id": 132, "name": "item.crossbow.loading_middle"}, {"id": 133, "name": "item.crossbow.loading_start"}, {"id": 134, "name": "item.crossbow.quick_charge_1"}, {"id": 135, "name": "item.crossbow.quick_charge_2"}, {"id": 136, "name": "item.crossbow.quick_charge_3"}, {"id": 137, "name": "item.crossbow.shoot"}, {"id": 138, "name": "block.dispenser.dispense"}, {"id": 139, "name": "block.dispenser.fail"}, {"id": 140, "name": "block.dispenser.launch"}, {"id": 141, "name": "entity.dolphin.ambient"}, {"id": 142, "name": "entity.dolphin.ambient_water"}, {"id": 143, "name": "entity.dolphin.attack"}, {"id": 144, "name": "entity.dolphin.death"}, {"id": 145, "name": "entity.dolphin.eat"}, {"id": 146, "name": "entity.dolphin.hurt"}, {"id": 147, "name": "entity.dolphin.jump"}, {"id": 148, "name": "entity.dolphin.play"}, {"id": 149, "name": "entity.dolphin.splash"}, {"id": 150, "name": "entity.dolphin.swim"}, {"id": 151, "name": "entity.donkey.ambient"}, {"id": 152, "name": "entity.donkey.angry"}, {"id": 153, "name": "entity.donkey.chest"}, {"id": 154, "name": "entity.donkey.death"}, {"id": 155, "name": "entity.donkey.hurt"}, {"id": 156, "name": "entity.drowned.ambient"}, {"id": 157, "name": "entity.drowned.ambient_water"}, {"id": 158, "name": "entity.drowned.death"}, {"id": 159, "name": "entity.drowned.death_water"}, {"id": 160, "name": "entity.drowned.hurt"}, {"id": 161, "name": "entity.drowned.hurt_water"}, {"id": 162, "name": "entity.drowned.shoot"}, {"id": 163, "name": "entity.drowned.step"}, {"id": 164, "name": "entity.drowned.swim"}, {"id": 165, "name": "entity.egg.throw"}, {"id": 166, "name": "entity.elder_guardian.ambient"}, {"id": 167, "name": "entity.elder_guardian.ambient_land"}, {"id": 168, "name": "entity.elder_guardian.curse"}, {"id": 169, "name": "entity.elder_guardian.death"}, {"id": 170, "name": "entity.elder_guardian.death_land"}, {"id": 171, "name": "entity.elder_guardian.flop"}, {"id": 172, "name": "entity.elder_guardian.hurt"}, {"id": 173, "name": "entity.elder_guardian.hurt_land"}, {"id": 174, "name": "item.elytra.flying"}, {"id": 175, "name": "block.enchantment_table.use"}, {"id": 176, "name": "block.ender_chest.close"}, {"id": 177, "name": "block.ender_chest.open"}, {"id": 178, "name": "entity.ender_dragon.ambient"}, {"id": 179, "name": "entity.ender_dragon.death"}, {"id": 180, "name": "entity.dragon_fireball.explode"}, {"id": 181, "name": "entity.ender_dragon.flap"}, {"id": 182, "name": "entity.ender_dragon.growl"}, {"id": 183, "name": "entity.ender_dragon.hurt"}, {"id": 184, "name": "entity.ender_dragon.shoot"}, {"id": 185, "name": "entity.ender_eye.death"}, {"id": 186, "name": "entity.ender_eye.launch"}, {"id": 187, "name": "entity.enderman.ambient"}, {"id": 188, "name": "entity.enderman.death"}, {"id": 189, "name": "entity.enderman.hurt"}, {"id": 190, "name": "entity.enderman.scream"}, {"id": 191, "name": "entity.enderman.stare"}, {"id": 192, "name": "entity.enderman.teleport"}, {"id": 193, "name": "entity.endermite.ambient"}, {"id": 194, "name": "entity.endermite.death"}, {"id": 195, "name": "entity.endermite.hurt"}, {"id": 196, "name": "entity.endermite.step"}, {"id": 197, "name": "entity.ender_pearl.throw"}, {"id": 198, "name": "block.end_gateway.spawn"}, {"id": 199, "name": "block.end_portal_frame.fill"}, {"id": 200, "name": "block.end_portal.spawn"}, {"id": 201, "name": "entity.evoker.ambient"}, {"id": 202, "name": "entity.evoker.cast_spell"}, {"id": 203, "name": "entity.evoker.celebrate"}, {"id": 204, "name": "entity.evoker.death"}, {"id": 205, "name": "entity.evoker_fangs.attack"}, {"id": 206, "name": "entity.evoker.hurt"}, {"id": 207, "name": "entity.evoker.prepare_attack"}, {"id": 208, "name": "entity.evoker.prepare_summon"}, {"id": 209, "name": "entity.evoker.prepare_wololo"}, {"id": 210, "name": "entity.experience_bottle.throw"}, {"id": 211, "name": "entity.experience_orb.pickup"}, {"id": 212, "name": "block.fence_gate.close"}, {"id": 213, "name": "block.fence_gate.open"}, {"id": 214, "name": "item.firecharge.use"}, {"id": 215, "name": "entity.firework_rocket.blast"}, {"id": 216, "name": "entity.firework_rocket.blast_far"}, {"id": 217, "name": "entity.firework_rocket.large_blast"}, {"id": 218, "name": "entity.firework_rocket.large_blast_far"}, {"id": 219, "name": "entity.firework_rocket.launch"}, {"id": 220, "name": "entity.firework_rocket.shoot"}, {"id": 221, "name": "entity.firework_rocket.twinkle"}, {"id": 222, "name": "entity.firework_rocket.twinkle_far"}, {"id": 223, "name": "block.fire.ambient"}, {"id": 224, "name": "block.fire.extinguish"}, {"id": 225, "name": "entity.fish.swim"}, {"id": 226, "name": "item.flintandsteel.use"}, {"id": 227, "name": "entity.fox.aggro"}, {"id": 228, "name": "entity.fox.ambient"}, {"id": 229, "name": "entity.fox.bite"}, {"id": 230, "name": "entity.fox.death"}, {"id": 231, "name": "entity.fox.eat"}, {"id": 232, "name": "entity.fox.hurt"}, {"id": 233, "name": "entity.fox.screech"}, {"id": 234, "name": "entity.fox.sleep"}, {"id": 235, "name": "entity.fox.sniff"}, {"id": 236, "name": "entity.fox.spit"}, {"id": 237, "name": "block.furnace.fire_crackle"}, {"id": 238, "name": "entity.generic.big_fall"}, {"id": 239, "name": "entity.generic.burn"}, {"id": 240, "name": "entity.generic.death"}, {"id": 241, "name": "entity.generic.drink"}, {"id": 242, "name": "entity.generic.eat"}, {"id": 243, "name": "entity.generic.explode"}, {"id": 244, "name": "entity.generic.extinguish_fire"}, {"id": 245, "name": "entity.generic.hurt"}, {"id": 246, "name": "entity.generic.small_fall"}, {"id": 247, "name": "entity.generic.splash"}, {"id": 248, "name": "entity.generic.swim"}, {"id": 249, "name": "entity.ghast.ambient"}, {"id": 250, "name": "entity.ghast.death"}, {"id": 251, "name": "entity.ghast.hurt"}, {"id": 252, "name": "entity.ghast.scream"}, {"id": 253, "name": "entity.ghast.shoot"}, {"id": 254, "name": "entity.ghast.warn"}, {"id": 255, "name": "block.glass.break"}, {"id": 256, "name": "block.glass.fall"}, {"id": 257, "name": "block.glass.hit"}, {"id": 258, "name": "block.glass.place"}, {"id": 259, "name": "block.glass.step"}, {"id": 260, "name": "block.grass.break"}, {"id": 261, "name": "block.grass.fall"}, {"id": 262, "name": "block.grass.hit"}, {"id": 263, "name": "block.grass.place"}, {"id": 264, "name": "block.grass.step"}, {"id": 265, "name": "block.wet_grass.break"}, {"id": 266, "name": "block.wet_grass.fall"}, {"id": 267, "name": "block.wet_grass.hit"}, {"id": 268, "name": "block.wet_grass.place"}, {"id": 269, "name": "block.wet_grass.step"}, {"id": 270, "name": "block.coral_block.break"}, {"id": 271, "name": "block.coral_block.fall"}, {"id": 272, "name": "block.coral_block.hit"}, {"id": 273, "name": "block.coral_block.place"}, {"id": 274, "name": "block.coral_block.step"}, {"id": 275, "name": "block.gravel.break"}, {"id": 276, "name": "block.gravel.fall"}, {"id": 277, "name": "block.gravel.hit"}, {"id": 278, "name": "block.gravel.place"}, {"id": 279, "name": "block.gravel.step"}, {"id": 280, "name": "block.grindstone.use"}, {"id": 281, "name": "entity.guardian.ambient"}, {"id": 282, "name": "entity.guardian.ambient_land"}, {"id": 283, "name": "entity.guardian.attack"}, {"id": 284, "name": "entity.guardian.death"}, {"id": 285, "name": "entity.guardian.death_land"}, {"id": 286, "name": "entity.guardian.flop"}, {"id": 287, "name": "entity.guardian.hurt"}, {"id": 288, "name": "entity.guardian.hurt_land"}, {"id": 289, "name": "item.hoe.till"}, {"id": 290, "name": "entity.horse.ambient"}, {"id": 291, "name": "entity.horse.angry"}, {"id": 292, "name": "entity.horse.armor"}, {"id": 293, "name": "entity.horse.breathe"}, {"id": 294, "name": "entity.horse.death"}, {"id": 295, "name": "entity.horse.eat"}, {"id": 296, "name": "entity.horse.gallop"}, {"id": 297, "name": "entity.horse.hurt"}, {"id": 298, "name": "entity.horse.jump"}, {"id": 299, "name": "entity.horse.land"}, {"id": 300, "name": "entity.horse.saddle"}, {"id": 301, "name": "entity.horse.step"}, {"id": 302, "name": "entity.horse.step_wood"}, {"id": 303, "name": "entity.hostile.big_fall"}, {"id": 304, "name": "entity.hostile.death"}, {"id": 305, "name": "entity.hostile.hurt"}, {"id": 306, "name": "entity.hostile.small_fall"}, {"id": 307, "name": "entity.hostile.splash"}, {"id": 308, "name": "entity.hostile.swim"}, {"id": 309, "name": "entity.husk.ambient"}, {"id": 310, "name": "entity.husk.converted_to_zombie"}, {"id": 311, "name": "entity.husk.death"}, {"id": 312, "name": "entity.husk.hurt"}, {"id": 313, "name": "entity.husk.step"}, {"id": 314, "name": "entity.ravager.ambient"}, {"id": 315, "name": "entity.ravager.attack"}, {"id": 316, "name": "entity.ravager.celebrate"}, {"id": 317, "name": "entity.ravager.death"}, {"id": 318, "name": "entity.ravager.hurt"}, {"id": 319, "name": "entity.ravager.step"}, {"id": 320, "name": "entity.ravager.stunned"}, {"id": 321, "name": "entity.ravager.roar"}, {"id": 322, "name": "entity.illusioner.ambient"}, {"id": 323, "name": "entity.illusioner.cast_spell"}, {"id": 324, "name": "entity.illusioner.death"}, {"id": 325, "name": "entity.illusioner.hurt"}, {"id": 326, "name": "entity.illusioner.mirror_move"}, {"id": 327, "name": "entity.illusioner.prepare_blindness"}, {"id": 328, "name": "entity.illusioner.prepare_mirror"}, {"id": 329, "name": "block.iron_door.close"}, {"id": 330, "name": "block.iron_door.open"}, {"id": 331, "name": "entity.iron_golem.attack"}, {"id": 332, "name": "entity.iron_golem.death"}, {"id": 333, "name": "entity.iron_golem.hurt"}, {"id": 334, "name": "entity.iron_golem.step"}, {"id": 335, "name": "block.iron_trapdoor.close"}, {"id": 336, "name": "block.iron_trapdoor.open"}, {"id": 337, "name": "entity.item_frame.add_item"}, {"id": 338, "name": "entity.item_frame.break"}, {"id": 339, "name": "entity.item_frame.place"}, {"id": 340, "name": "entity.item_frame.remove_item"}, {"id": 341, "name": "entity.item_frame.rotate_item"}, {"id": 342, "name": "entity.item.break"}, {"id": 343, "name": "entity.item.pickup"}, {"id": 344, "name": "block.ladder.break"}, {"id": 345, "name": "block.ladder.fall"}, {"id": 346, "name": "block.ladder.hit"}, {"id": 347, "name": "block.ladder.place"}, {"id": 348, "name": "block.ladder.step"}, {"id": 349, "name": "block.lantern.break"}, {"id": 350, "name": "block.lantern.fall"}, {"id": 351, "name": "block.lantern.hit"}, {"id": 352, "name": "block.lantern.place"}, {"id": 353, "name": "block.lantern.step"}, {"id": 354, "name": "block.lava.ambient"}, {"id": 355, "name": "block.lava.extinguish"}, {"id": 356, "name": "block.lava.pop"}, {"id": 357, "name": "entity.leash_knot.break"}, {"id": 358, "name": "entity.leash_knot.place"}, {"id": 359, "name": "block.lever.click"}, {"id": 360, "name": "entity.lightning_bolt.impact"}, {"id": 361, "name": "entity.lightning_bolt.thunder"}, {"id": 362, "name": "entity.lingering_potion.throw"}, {"id": 363, "name": "entity.llama.ambient"}, {"id": 364, "name": "entity.llama.angry"}, {"id": 365, "name": "entity.llama.chest"}, {"id": 366, "name": "entity.llama.death"}, {"id": 367, "name": "entity.llama.eat"}, {"id": 368, "name": "entity.llama.hurt"}, {"id": 369, "name": "entity.llama.spit"}, {"id": 370, "name": "entity.llama.step"}, {"id": 371, "name": "entity.llama.swag"}, {"id": 372, "name": "entity.magma_cube.death"}, {"id": 373, "name": "entity.magma_cube.hurt"}, {"id": 374, "name": "entity.magma_cube.jump"}, {"id": 375, "name": "entity.magma_cube.squish"}, {"id": 376, "name": "block.metal.break"}, {"id": 377, "name": "block.metal.fall"}, {"id": 378, "name": "block.metal.hit"}, {"id": 379, "name": "block.metal.place"}, {"id": 380, "name": "block.metal_pressure_plate.click_off"}, {"id": 381, "name": "block.metal_pressure_plate.click_on"}, {"id": 382, "name": "block.metal.step"}, {"id": 383, "name": "entity.minecart.inside"}, {"id": 384, "name": "entity.minecart.riding"}, {"id": 385, "name": "entity.mooshroom.convert"}, {"id": 386, "name": "entity.mooshroom.eat"}, {"id": 387, "name": "entity.mooshroom.milk"}, {"id": 388, "name": "entity.mooshroom.suspicious_milk"}, {"id": 389, "name": "entity.mooshroom.shear"}, {"id": 390, "name": "entity.mule.ambient"}, {"id": 391, "name": "entity.mule.chest"}, {"id": 392, "name": "entity.mule.death"}, {"id": 393, "name": "entity.mule.hurt"}, {"id": 394, "name": "music.creative"}, {"id": 395, "name": "music.credits"}, {"id": 396, "name": "music.dragon"}, {"id": 397, "name": "music.end"}, {"id": 398, "name": "music.game"}, {"id": 399, "name": "music.menu"}, {"id": 400, "name": "music.nether"}, {"id": 401, "name": "music.under_water"}, {"id": 402, "name": "block.nether_wart.break"}, {"id": 403, "name": "item.nether_wart.plant"}, {"id": 404, "name": "block.note_block.basedrum"}, {"id": 405, "name": "block.note_block.bass"}, {"id": 406, "name": "block.note_block.bell"}, {"id": 407, "name": "block.note_block.chime"}, {"id": 408, "name": "block.note_block.flute"}, {"id": 409, "name": "block.note_block.guitar"}, {"id": 410, "name": "block.note_block.harp"}, {"id": 411, "name": "block.note_block.hat"}, {"id": 412, "name": "block.note_block.pling"}, {"id": 413, "name": "block.note_block.snare"}, {"id": 414, "name": "block.note_block.xylophone"}, {"id": 415, "name": "block.note_block.iron_xylophone"}, {"id": 416, "name": "block.note_block.cow_bell"}, {"id": 417, "name": "block.note_block.didgeridoo"}, {"id": 418, "name": "block.note_block.bit"}, {"id": 419, "name": "block.note_block.banjo"}, {"id": 420, "name": "entity.ocelot.hurt"}, {"id": 421, "name": "entity.ocelot.ambient"}, {"id": 422, "name": "entity.ocelot.death"}, {"id": 423, "name": "entity.painting.break"}, {"id": 424, "name": "entity.painting.place"}, {"id": 425, "name": "entity.panda.pre_sneeze"}, {"id": 426, "name": "entity.panda.sneeze"}, {"id": 427, "name": "entity.panda.ambient"}, {"id": 428, "name": "entity.panda.death"}, {"id": 429, "name": "entity.panda.eat"}, {"id": 430, "name": "entity.panda.step"}, {"id": 431, "name": "entity.panda.cant_breed"}, {"id": 432, "name": "entity.panda.aggressive_ambient"}, {"id": 433, "name": "entity.panda.worried_ambient"}, {"id": 434, "name": "entity.panda.hurt"}, {"id": 435, "name": "entity.panda.bite"}, {"id": 436, "name": "entity.parrot.ambient"}, {"id": 437, "name": "entity.parrot.death"}, {"id": 438, "name": "entity.parrot.eat"}, {"id": 439, "name": "entity.parrot.fly"}, {"id": 440, "name": "entity.parrot.hurt"}, {"id": 441, "name": "entity.parrot.imitate.blaze"}, {"id": 442, "name": "entity.parrot.imitate.creeper"}, {"id": 443, "name": "entity.parrot.imitate.drowned"}, {"id": 444, "name": "entity.parrot.imitate.elder_guardian"}, {"id": 445, "name": "entity.parrot.imitate.ender_dragon"}, {"id": 446, "name": "entity.parrot.imitate.enderman"}, {"id": 447, "name": "entity.parrot.imitate.endermite"}, {"id": 448, "name": "entity.parrot.imitate.evoker"}, {"id": 449, "name": "entity.parrot.imitate.ghast"}, {"id": 450, "name": "entity.parrot.imitate.guardian"}, {"id": 451, "name": "entity.parrot.imitate.husk"}, {"id": 452, "name": "entity.parrot.imitate.illusioner"}, {"id": 453, "name": "entity.parrot.imitate.magma_cube"}, {"id": 454, "name": "entity.parrot.imitate.panda"}, {"id": 455, "name": "entity.parrot.imitate.phantom"}, {"id": 456, "name": "entity.parrot.imitate.pillager"}, {"id": 457, "name": "entity.parrot.imitate.polar_bear"}, {"id": 458, "name": "entity.parrot.imitate.ravager"}, {"id": 459, "name": "entity.parrot.imitate.shulker"}, {"id": 460, "name": "entity.parrot.imitate.silverfish"}, {"id": 461, "name": "entity.parrot.imitate.skeleton"}, {"id": 462, "name": "entity.parrot.imitate.slime"}, {"id": 463, "name": "entity.parrot.imitate.spider"}, {"id": 464, "name": "entity.parrot.imitate.stray"}, {"id": 465, "name": "entity.parrot.imitate.vex"}, {"id": 466, "name": "entity.parrot.imitate.vindicator"}, {"id": 467, "name": "entity.parrot.imitate.witch"}, {"id": 468, "name": "entity.parrot.imitate.wither"}, {"id": 469, "name": "entity.parrot.imitate.wither_skeleton"}, {"id": 470, "name": "entity.parrot.imitate.wolf"}, {"id": 471, "name": "entity.parrot.imitate.zombie"}, {"id": 472, "name": "entity.parrot.imitate.zombie_pigman"}, {"id": 473, "name": "entity.parrot.imitate.zombie_villager"}, {"id": 474, "name": "entity.parrot.step"}, {"id": 475, "name": "entity.phantom.ambient"}, {"id": 476, "name": "entity.phantom.bite"}, {"id": 477, "name": "entity.phantom.death"}, {"id": 478, "name": "entity.phantom.flap"}, {"id": 479, "name": "entity.phantom.hurt"}, {"id": 480, "name": "entity.phantom.swoop"}, {"id": 481, "name": "entity.pig.ambient"}, {"id": 482, "name": "entity.pig.death"}, {"id": 483, "name": "entity.pig.hurt"}, {"id": 484, "name": "entity.pig.saddle"}, {"id": 485, "name": "entity.pig.step"}, {"id": 486, "name": "entity.pillager.ambient"}, {"id": 487, "name": "entity.pillager.celebrate"}, {"id": 488, "name": "entity.pillager.death"}, {"id": 489, "name": "entity.pillager.hurt"}, {"id": 490, "name": "block.piston.contract"}, {"id": 491, "name": "block.piston.extend"}, {"id": 492, "name": "entity.player.attack.crit"}, {"id": 493, "name": "entity.player.attack.knockback"}, {"id": 494, "name": "entity.player.attack.nodamage"}, {"id": 495, "name": "entity.player.attack.strong"}, {"id": 496, "name": "entity.player.attack.sweep"}, {"id": 497, "name": "entity.player.attack.weak"}, {"id": 498, "name": "entity.player.big_fall"}, {"id": 499, "name": "entity.player.breath"}, {"id": 500, "name": "entity.player.burp"}, {"id": 501, "name": "entity.player.death"}, {"id": 502, "name": "entity.player.hurt"}, {"id": 503, "name": "entity.player.hurt_drown"}, {"id": 504, "name": "entity.player.hurt_on_fire"}, {"id": 505, "name": "entity.player.hurt_sweet_berry_bush"}, {"id": 506, "name": "entity.player.levelup"}, {"id": 507, "name": "entity.player.small_fall"}, {"id": 508, "name": "entity.player.splash"}, {"id": 509, "name": "entity.player.splash.high_speed"}, {"id": 510, "name": "entity.player.swim"}, {"id": 511, "name": "entity.polar_bear.ambient"}, {"id": 512, "name": "entity.polar_bear.ambient_baby"}, {"id": 513, "name": "entity.polar_bear.death"}, {"id": 514, "name": "entity.polar_bear.hurt"}, {"id": 515, "name": "entity.polar_bear.step"}, {"id": 516, "name": "entity.polar_bear.warning"}, {"id": 517, "name": "block.portal.ambient"}, {"id": 518, "name": "block.portal.travel"}, {"id": 519, "name": "block.portal.trigger"}, {"id": 520, "name": "entity.puffer_fish.ambient"}, {"id": 521, "name": "entity.puffer_fish.blow_out"}, {"id": 522, "name": "entity.puffer_fish.blow_up"}, {"id": 523, "name": "entity.puffer_fish.death"}, {"id": 524, "name": "entity.puffer_fish.flop"}, {"id": 525, "name": "entity.puffer_fish.hurt"}, {"id": 526, "name": "entity.puffer_fish.sting"}, {"id": 527, "name": "block.pumpkin.carve"}, {"id": 528, "name": "entity.rabbit.ambient"}, {"id": 529, "name": "entity.rabbit.attack"}, {"id": 530, "name": "entity.rabbit.death"}, {"id": 531, "name": "entity.rabbit.hurt"}, {"id": 532, "name": "entity.rabbit.jump"}, {"id": 533, "name": "event.raid.horn"}, {"id": 534, "name": "music_disc.11"}, {"id": 535, "name": "music_disc.13"}, {"id": 536, "name": "music_disc.blocks"}, {"id": 537, "name": "music_disc.cat"}, {"id": 538, "name": "music_disc.chirp"}, {"id": 539, "name": "music_disc.far"}, {"id": 540, "name": "music_disc.mall"}, {"id": 541, "name": "music_disc.mellohi"}, {"id": 542, "name": "music_disc.stal"}, {"id": 543, "name": "music_disc.strad"}, {"id": 544, "name": "music_disc.wait"}, {"id": 545, "name": "music_disc.ward"}, {"id": 546, "name": "block.redstone_torch.burnout"}, {"id": 547, "name": "entity.salmon.ambient"}, {"id": 548, "name": "entity.salmon.death"}, {"id": 549, "name": "entity.salmon.flop"}, {"id": 550, "name": "entity.salmon.hurt"}, {"id": 551, "name": "block.sand.break"}, {"id": 552, "name": "block.sand.fall"}, {"id": 553, "name": "block.sand.hit"}, {"id": 554, "name": "block.sand.place"}, {"id": 555, "name": "block.sand.step"}, {"id": 556, "name": "block.scaffolding.break"}, {"id": 557, "name": "block.scaffolding.fall"}, {"id": 558, "name": "block.scaffolding.hit"}, {"id": 559, "name": "block.scaffolding.place"}, {"id": 560, "name": "block.scaffolding.step"}, {"id": 561, "name": "entity.sheep.ambient"}, {"id": 562, "name": "entity.sheep.death"}, {"id": 563, "name": "entity.sheep.hurt"}, {"id": 564, "name": "entity.sheep.shear"}, {"id": 565, "name": "entity.sheep.step"}, {"id": 566, "name": "item.shield.block"}, {"id": 567, "name": "item.shield.break"}, {"id": 568, "name": "item.shovel.flatten"}, {"id": 569, "name": "entity.shulker.ambient"}, {"id": 570, "name": "block.shulker_box.close"}, {"id": 571, "name": "block.shulker_box.open"}, {"id": 572, "name": "entity.shulker_bullet.hit"}, {"id": 573, "name": "entity.shulker_bullet.hurt"}, {"id": 574, "name": "entity.shulker.close"}, {"id": 575, "name": "entity.shulker.death"}, {"id": 576, "name": "entity.shulker.hurt"}, {"id": 577, "name": "entity.shulker.hurt_closed"}, {"id": 578, "name": "entity.shulker.open"}, {"id": 579, "name": "entity.shulker.shoot"}, {"id": 580, "name": "entity.shulker.teleport"}, {"id": 581, "name": "entity.silverfish.ambient"}, {"id": 582, "name": "entity.silverfish.death"}, {"id": 583, "name": "entity.silverfish.hurt"}, {"id": 584, "name": "entity.silverfish.step"}, {"id": 585, "name": "entity.skeleton.ambient"}, {"id": 586, "name": "entity.skeleton.death"}, {"id": 587, "name": "entity.skeleton_horse.ambient"}, {"id": 588, "name": "entity.skeleton_horse.death"}, {"id": 589, "name": "entity.skeleton_horse.hurt"}, {"id": 590, "name": "entity.skeleton_horse.swim"}, {"id": 591, "name": "entity.skeleton_horse.ambient_water"}, {"id": 592, "name": "entity.skeleton_horse.gallop_water"}, {"id": 593, "name": "entity.skeleton_horse.jump_water"}, {"id": 594, "name": "entity.skeleton_horse.step_water"}, {"id": 595, "name": "entity.skeleton.hurt"}, {"id": 596, "name": "entity.skeleton.shoot"}, {"id": 597, "name": "entity.skeleton.step"}, {"id": 598, "name": "entity.slime.attack"}, {"id": 599, "name": "entity.slime.death"}, {"id": 600, "name": "entity.slime.hurt"}, {"id": 601, "name": "entity.slime.jump"}, {"id": 602, "name": "entity.slime.squish"}, {"id": 603, "name": "block.slime_block.break"}, {"id": 604, "name": "block.slime_block.fall"}, {"id": 605, "name": "block.slime_block.hit"}, {"id": 606, "name": "block.slime_block.place"}, {"id": 607, "name": "block.slime_block.step"}, {"id": 608, "name": "entity.magma_cube.death_small"}, {"id": 609, "name": "entity.magma_cube.hurt_small"}, {"id": 610, "name": "entity.magma_cube.squish_small"}, {"id": 611, "name": "entity.slime.death_small"}, {"id": 612, "name": "entity.slime.hurt_small"}, {"id": 613, "name": "entity.slime.jump_small"}, {"id": 614, "name": "entity.slime.squish_small"}, {"id": 615, "name": "block.smoker.smoke"}, {"id": 616, "name": "entity.snowball.throw"}, {"id": 617, "name": "block.snow.break"}, {"id": 618, "name": "block.snow.fall"}, {"id": 619, "name": "entity.snow_golem.ambient"}, {"id": 620, "name": "entity.snow_golem.death"}, {"id": 621, "name": "entity.snow_golem.hurt"}, {"id": 622, "name": "entity.snow_golem.shoot"}, {"id": 623, "name": "block.snow.hit"}, {"id": 624, "name": "block.snow.place"}, {"id": 625, "name": "block.snow.step"}, {"id": 626, "name": "entity.spider.ambient"}, {"id": 627, "name": "entity.spider.death"}, {"id": 628, "name": "entity.spider.hurt"}, {"id": 629, "name": "entity.spider.step"}, {"id": 630, "name": "entity.splash_potion.break"}, {"id": 631, "name": "entity.splash_potion.throw"}, {"id": 632, "name": "entity.squid.ambient"}, {"id": 633, "name": "entity.squid.death"}, {"id": 634, "name": "entity.squid.hurt"}, {"id": 635, "name": "entity.squid.squirt"}, {"id": 636, "name": "block.stone.break"}, {"id": 637, "name": "block.stone_button.click_off"}, {"id": 638, "name": "block.stone_button.click_on"}, {"id": 639, "name": "block.stone.fall"}, {"id": 640, "name": "block.stone.hit"}, {"id": 641, "name": "block.stone.place"}, {"id": 642, "name": "block.stone_pressure_plate.click_off"}, {"id": 643, "name": "block.stone_pressure_plate.click_on"}, {"id": 644, "name": "block.stone.step"}, {"id": 645, "name": "entity.stray.ambient"}, {"id": 646, "name": "entity.stray.death"}, {"id": 647, "name": "entity.stray.hurt"}, {"id": 648, "name": "entity.stray.step"}, {"id": 649, "name": "block.sweet_berry_bush.break"}, {"id": 650, "name": "block.sweet_berry_bush.place"}, {"id": 651, "name": "item.sweet_berries.pick_from_bush"}, {"id": 652, "name": "enchant.thorns.hit"}, {"id": 653, "name": "entity.tnt.primed"}, {"id": 654, "name": "item.totem.use"}, {"id": 655, "name": "item.trident.hit"}, {"id": 656, "name": "item.trident.hit_ground"}, {"id": 657, "name": "item.trident.return"}, {"id": 658, "name": "item.trident.riptide_1"}, {"id": 659, "name": "item.trident.riptide_2"}, {"id": 660, "name": "item.trident.riptide_3"}, {"id": 661, "name": "item.trident.throw"}, {"id": 662, "name": "item.trident.thunder"}, {"id": 663, "name": "block.tripwire.attach"}, {"id": 664, "name": "block.tripwire.click_off"}, {"id": 665, "name": "block.tripwire.click_on"}, {"id": 666, "name": "block.tripwire.detach"}, {"id": 667, "name": "entity.tropical_fish.ambient"}, {"id": 668, "name": "entity.tropical_fish.death"}, {"id": 669, "name": "entity.tropical_fish.flop"}, {"id": 670, "name": "entity.tropical_fish.hurt"}, {"id": 671, "name": "entity.turtle.ambient_land"}, {"id": 672, "name": "entity.turtle.death"}, {"id": 673, "name": "entity.turtle.death_baby"}, {"id": 674, "name": "entity.turtle.egg_break"}, {"id": 675, "name": "entity.turtle.egg_crack"}, {"id": 676, "name": "entity.turtle.egg_hatch"}, {"id": 677, "name": "entity.turtle.hurt"}, {"id": 678, "name": "entity.turtle.hurt_baby"}, {"id": 679, "name": "entity.turtle.lay_egg"}, {"id": 680, "name": "entity.turtle.shamble"}, {"id": 681, "name": "entity.turtle.shamble_baby"}, {"id": 682, "name": "entity.turtle.swim"}, {"id": 683, "name": "ui.button.click"}, {"id": 684, "name": "ui.loom.select_pattern"}, {"id": 685, "name": "ui.loom.take_result"}, {"id": 686, "name": "ui.cartography_table.take_result"}, {"id": 687, "name": "ui.stonecutter.take_result"}, {"id": 688, "name": "ui.stonecutter.select_recipe"}, {"id": 689, "name": "ui.toast.challenge_complete"}, {"id": 690, "name": "ui.toast.in"}, {"id": 691, "name": "ui.toast.out"}, {"id": 692, "name": "entity.vex.ambient"}, {"id": 693, "name": "entity.vex.charge"}, {"id": 694, "name": "entity.vex.death"}, {"id": 695, "name": "entity.vex.hurt"}, {"id": 696, "name": "entity.villager.ambient"}, {"id": 697, "name": "entity.villager.celebrate"}, {"id": 698, "name": "entity.villager.death"}, {"id": 699, "name": "entity.villager.hurt"}, {"id": 700, "name": "entity.villager.no"}, {"id": 701, "name": "entity.villager.trade"}, {"id": 702, "name": "entity.villager.yes"}, {"id": 703, "name": "entity.villager.work_armorer"}, {"id": 704, "name": "entity.villager.work_butcher"}, {"id": 705, "name": "entity.villager.work_cartographer"}, {"id": 706, "name": "entity.villager.work_cleric"}, {"id": 707, "name": "entity.villager.work_farmer"}, {"id": 708, "name": "entity.villager.work_fisherman"}, {"id": 709, "name": "entity.villager.work_fletcher"}, {"id": 710, "name": "entity.villager.work_leatherworker"}, {"id": 711, "name": "entity.villager.work_librarian"}, {"id": 712, "name": "entity.villager.work_mason"}, {"id": 713, "name": "entity.villager.work_shepherd"}, {"id": 714, "name": "entity.villager.work_toolsmith"}, {"id": 715, "name": "entity.villager.work_weaponsmith"}, {"id": 716, "name": "entity.vindicator.ambient"}, {"id": 717, "name": "entity.vindicator.celebrate"}, {"id": 718, "name": "entity.vindicator.death"}, {"id": 719, "name": "entity.vindicator.hurt"}, {"id": 720, "name": "block.lily_pad.place"}, {"id": 721, "name": "entity.wandering_trader.ambient"}, {"id": 722, "name": "entity.wandering_trader.death"}, {"id": 723, "name": "entity.wandering_trader.disappeared"}, {"id": 724, "name": "entity.wandering_trader.drink_milk"}, {"id": 725, "name": "entity.wandering_trader.drink_potion"}, {"id": 726, "name": "entity.wandering_trader.hurt"}, {"id": 727, "name": "entity.wandering_trader.no"}, {"id": 728, "name": "entity.wandering_trader.reappeared"}, {"id": 729, "name": "entity.wandering_trader.trade"}, {"id": 730, "name": "entity.wandering_trader.yes"}, {"id": 731, "name": "block.water.ambient"}, {"id": 732, "name": "weather.rain"}, {"id": 733, "name": "weather.rain.above"}, {"id": 734, "name": "entity.witch.ambient"}, {"id": 735, "name": "entity.witch.celebrate"}, {"id": 736, "name": "entity.witch.death"}, {"id": 737, "name": "entity.witch.drink"}, {"id": 738, "name": "entity.witch.hurt"}, {"id": 739, "name": "entity.witch.throw"}, {"id": 740, "name": "entity.wither.ambient"}, {"id": 741, "name": "entity.wither.break_block"}, {"id": 742, "name": "entity.wither.death"}, {"id": 743, "name": "entity.wither.hurt"}, {"id": 744, "name": "entity.wither.shoot"}, {"id": 745, "name": "entity.wither_skeleton.ambient"}, {"id": 746, "name": "entity.wither_skeleton.death"}, {"id": 747, "name": "entity.wither_skeleton.hurt"}, {"id": 748, "name": "entity.wither_skeleton.step"}, {"id": 749, "name": "entity.wither.spawn"}, {"id": 750, "name": "entity.wolf.ambient"}, {"id": 751, "name": "entity.wolf.death"}, {"id": 752, "name": "entity.wolf.growl"}, {"id": 753, "name": "entity.wolf.howl"}, {"id": 754, "name": "entity.wolf.hurt"}, {"id": 755, "name": "entity.wolf.pant"}, {"id": 756, "name": "entity.wolf.shake"}, {"id": 757, "name": "entity.wolf.step"}, {"id": 758, "name": "entity.wolf.whine"}, {"id": 759, "name": "block.wooden_door.close"}, {"id": 760, "name": "block.wooden_door.open"}, {"id": 761, "name": "block.wooden_trapdoor.close"}, {"id": 762, "name": "block.wooden_trapdoor.open"}, {"id": 763, "name": "block.wood.break"}, {"id": 764, "name": "block.wooden_button.click_off"}, {"id": 765, "name": "block.wooden_button.click_on"}, {"id": 766, "name": "block.wood.fall"}, {"id": 767, "name": "block.wood.hit"}, {"id": 768, "name": "block.wood.place"}, {"id": 769, "name": "block.wooden_pressure_plate.click_off"}, {"id": 770, "name": "block.wooden_pressure_plate.click_on"}, {"id": 771, "name": "block.wood.step"}, {"id": 772, "name": "entity.zombie.ambient"}, {"id": 773, "name": "entity.zombie.attack_wooden_door"}, {"id": 774, "name": "entity.zombie.attack_iron_door"}, {"id": 775, "name": "entity.zombie.break_wooden_door"}, {"id": 776, "name": "entity.zombie.converted_to_drowned"}, {"id": 777, "name": "entity.zombie.death"}, {"id": 778, "name": "entity.zombie.destroy_egg"}, {"id": 779, "name": "entity.zombie_horse.ambient"}, {"id": 780, "name": "entity.zombie_horse.death"}, {"id": 781, "name": "entity.zombie_horse.hurt"}, {"id": 782, "name": "entity.zombie.hurt"}, {"id": 783, "name": "entity.zombie.infect"}, {"id": 784, "name": "entity.zombie_pigman.ambient"}, {"id": 785, "name": "entity.zombie_pigman.angry"}, {"id": 786, "name": "entity.zombie_pigman.death"}, {"id": 787, "name": "entity.zombie_pigman.hurt"}, {"id": 788, "name": "entity.zombie.step"}, {"id": 789, "name": "entity.zombie_villager.ambient"}, {"id": 790, "name": "entity.zombie_villager.converted"}, {"id": 791, "name": "entity.zombie_villager.cure"}, {"id": 792, "name": "entity.zombie_villager.death"}, {"id": 793, "name": "entity.zombie_villager.hurt"}, {"id": 794, "name": "entity.zombie_villager.step"}]