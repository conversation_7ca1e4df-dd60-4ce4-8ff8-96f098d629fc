[{"id": 1, "name": "entity.allay.ambient_with_item"}, {"id": 2, "name": "entity.allay.ambient_without_item"}, {"id": 3, "name": "entity.allay.death"}, {"id": 4, "name": "entity.allay.hurt"}, {"id": 5, "name": "entity.allay.item_given"}, {"id": 6, "name": "entity.allay.item_taken"}, {"id": 7, "name": "entity.allay.item_thrown"}, {"id": 8, "name": "ambient.cave"}, {"id": 9, "name": "ambient.basalt_deltas.additions"}, {"id": 10, "name": "ambient.basalt_deltas.loop"}, {"id": 11, "name": "ambient.basalt_deltas.mood"}, {"id": 12, "name": "ambient.crimson_forest.additions"}, {"id": 13, "name": "ambient.crimson_forest.loop"}, {"id": 14, "name": "ambient.crimson_forest.mood"}, {"id": 15, "name": "ambient.nether_wastes.additions"}, {"id": 16, "name": "ambient.nether_wastes.loop"}, {"id": 17, "name": "ambient.nether_wastes.mood"}, {"id": 18, "name": "ambient.soul_sand_valley.additions"}, {"id": 19, "name": "ambient.soul_sand_valley.loop"}, {"id": 20, "name": "ambient.soul_sand_valley.mood"}, {"id": 21, "name": "ambient.warped_forest.additions"}, {"id": 22, "name": "ambient.warped_forest.loop"}, {"id": 23, "name": "ambient.warped_forest.mood"}, {"id": 24, "name": "ambient.underwater.enter"}, {"id": 25, "name": "ambient.underwater.exit"}, {"id": 26, "name": "ambient.underwater.loop"}, {"id": 27, "name": "ambient.underwater.loop.additions"}, {"id": 28, "name": "ambient.underwater.loop.additions.rare"}, {"id": 29, "name": "ambient.underwater.loop.additions.ultra_rare"}, {"id": 30, "name": "block.amethyst_block.break"}, {"id": 31, "name": "block.amethyst_block.chime"}, {"id": 32, "name": "block.amethyst_block.fall"}, {"id": 33, "name": "block.amethyst_block.hit"}, {"id": 34, "name": "block.amethyst_block.place"}, {"id": 35, "name": "block.amethyst_block.step"}, {"id": 36, "name": "block.amethyst_cluster.break"}, {"id": 37, "name": "block.amethyst_cluster.fall"}, {"id": 38, "name": "block.amethyst_cluster.hit"}, {"id": 39, "name": "block.amethyst_cluster.place"}, {"id": 40, "name": "block.amethyst_cluster.step"}, {"id": 41, "name": "block.ancient_debris.break"}, {"id": 42, "name": "block.ancient_debris.step"}, {"id": 43, "name": "block.ancient_debris.place"}, {"id": 44, "name": "block.ancient_debris.hit"}, {"id": 45, "name": "block.ancient_debris.fall"}, {"id": 46, "name": "block.anvil.break"}, {"id": 47, "name": "block.anvil.destroy"}, {"id": 48, "name": "block.anvil.fall"}, {"id": 49, "name": "block.anvil.hit"}, {"id": 50, "name": "block.anvil.land"}, {"id": 51, "name": "block.anvil.place"}, {"id": 52, "name": "block.anvil.step"}, {"id": 53, "name": "block.anvil.use"}, {"id": 54, "name": "item.armor.equip_chain"}, {"id": 55, "name": "item.armor.equip_diamond"}, {"id": 56, "name": "item.armor.equip_elytra"}, {"id": 57, "name": "item.armor.equip_generic"}, {"id": 58, "name": "item.armor.equip_gold"}, {"id": 59, "name": "item.armor.equip_iron"}, {"id": 60, "name": "item.armor.equip_leather"}, {"id": 61, "name": "item.armor.equip_netherite"}, {"id": 62, "name": "item.armor.equip_turtle"}, {"id": 63, "name": "entity.armor_stand.break"}, {"id": 64, "name": "entity.armor_stand.fall"}, {"id": 65, "name": "entity.armor_stand.hit"}, {"id": 66, "name": "entity.armor_stand.place"}, {"id": 67, "name": "entity.arrow.hit"}, {"id": 68, "name": "entity.arrow.hit_player"}, {"id": 69, "name": "entity.arrow.shoot"}, {"id": 70, "name": "item.axe.strip"}, {"id": 71, "name": "item.axe.scrape"}, {"id": 72, "name": "item.axe.wax_off"}, {"id": 73, "name": "entity.axolotl.attack"}, {"id": 74, "name": "entity.axolotl.death"}, {"id": 75, "name": "entity.axolotl.hurt"}, {"id": 76, "name": "entity.axolotl.idle_air"}, {"id": 77, "name": "entity.axolotl.idle_water"}, {"id": 78, "name": "entity.axolotl.splash"}, {"id": 79, "name": "entity.axolotl.swim"}, {"id": 80, "name": "block.azalea.break"}, {"id": 81, "name": "block.azalea.fall"}, {"id": 82, "name": "block.azalea.hit"}, {"id": 83, "name": "block.azalea.place"}, {"id": 84, "name": "block.azalea.step"}, {"id": 85, "name": "block.azalea_leaves.break"}, {"id": 86, "name": "block.azalea_leaves.fall"}, {"id": 87, "name": "block.azalea_leaves.hit"}, {"id": 88, "name": "block.azalea_leaves.place"}, {"id": 89, "name": "block.azalea_leaves.step"}, {"id": 90, "name": "block.bamboo.break"}, {"id": 91, "name": "block.bamboo.fall"}, {"id": 92, "name": "block.bamboo.hit"}, {"id": 93, "name": "block.bamboo.place"}, {"id": 94, "name": "block.bamboo.step"}, {"id": 95, "name": "block.bamboo_sapling.break"}, {"id": 96, "name": "block.bamboo_sapling.hit"}, {"id": 97, "name": "block.bamboo_sapling.place"}, {"id": 98, "name": "block.barrel.close"}, {"id": 99, "name": "block.barrel.open"}, {"id": 100, "name": "block.basalt.break"}, {"id": 101, "name": "block.basalt.step"}, {"id": 102, "name": "block.basalt.place"}, {"id": 103, "name": "block.basalt.hit"}, {"id": 104, "name": "block.basalt.fall"}, {"id": 105, "name": "entity.bat.ambient"}, {"id": 106, "name": "entity.bat.death"}, {"id": 107, "name": "entity.bat.hurt"}, {"id": 108, "name": "entity.bat.loop"}, {"id": 109, "name": "entity.bat.takeoff"}, {"id": 110, "name": "block.beacon.activate"}, {"id": 111, "name": "block.beacon.ambient"}, {"id": 112, "name": "block.beacon.deactivate"}, {"id": 113, "name": "block.beacon.power_select"}, {"id": 114, "name": "entity.bee.death"}, {"id": 115, "name": "entity.bee.hurt"}, {"id": 116, "name": "entity.bee.loop_aggressive"}, {"id": 117, "name": "entity.bee.loop"}, {"id": 118, "name": "entity.bee.sting"}, {"id": 119, "name": "entity.bee.pollinate"}, {"id": 120, "name": "block.beehive.drip"}, {"id": 121, "name": "block.beehive.enter"}, {"id": 122, "name": "block.beehive.exit"}, {"id": 123, "name": "block.beehive.shear"}, {"id": 124, "name": "block.beehive.work"}, {"id": 125, "name": "block.bell.use"}, {"id": 126, "name": "block.bell.resonate"}, {"id": 127, "name": "block.big_dripleaf.break"}, {"id": 128, "name": "block.big_dripleaf.fall"}, {"id": 129, "name": "block.big_dripleaf.hit"}, {"id": 130, "name": "block.big_dripleaf.place"}, {"id": 131, "name": "block.big_dripleaf.step"}, {"id": 132, "name": "entity.blaze.ambient"}, {"id": 133, "name": "entity.blaze.burn"}, {"id": 134, "name": "entity.blaze.death"}, {"id": 135, "name": "entity.blaze.hurt"}, {"id": 136, "name": "entity.blaze.shoot"}, {"id": 137, "name": "entity.boat.paddle_land"}, {"id": 138, "name": "entity.boat.paddle_water"}, {"id": 139, "name": "block.bone_block.break"}, {"id": 140, "name": "block.bone_block.fall"}, {"id": 141, "name": "block.bone_block.hit"}, {"id": 142, "name": "block.bone_block.place"}, {"id": 143, "name": "block.bone_block.step"}, {"id": 144, "name": "item.bone_meal.use"}, {"id": 145, "name": "item.book.page_turn"}, {"id": 146, "name": "item.book.put"}, {"id": 147, "name": "block.blastfurnace.fire_crackle"}, {"id": 148, "name": "item.bottle.empty"}, {"id": 149, "name": "item.bottle.fill"}, {"id": 150, "name": "item.bottle.fill_dragonbreath"}, {"id": 151, "name": "block.brewing_stand.brew"}, {"id": 152, "name": "block.bubble_column.bubble_pop"}, {"id": 153, "name": "block.bubble_column.upwards_ambient"}, {"id": 154, "name": "block.bubble_column.upwards_inside"}, {"id": 155, "name": "block.bubble_column.whirlpool_ambient"}, {"id": 156, "name": "block.bubble_column.whirlpool_inside"}, {"id": 157, "name": "item.bucket.empty"}, {"id": 158, "name": "item.bucket.empty_axolotl"}, {"id": 159, "name": "item.bucket.empty_fish"}, {"id": 160, "name": "item.bucket.empty_lava"}, {"id": 161, "name": "item.bucket.empty_powder_snow"}, {"id": 162, "name": "item.bucket.empty_tadpole"}, {"id": 163, "name": "item.bucket.fill"}, {"id": 164, "name": "item.bucket.fill_axolotl"}, {"id": 165, "name": "item.bucket.fill_fish"}, {"id": 166, "name": "item.bucket.fill_lava"}, {"id": 167, "name": "item.bucket.fill_powder_snow"}, {"id": 168, "name": "item.bucket.fill_tadpole"}, {"id": 169, "name": "item.bundle.drop_contents"}, {"id": 170, "name": "item.bundle.insert"}, {"id": 171, "name": "item.bundle.remove_one"}, {"id": 172, "name": "block.cake.add_candle"}, {"id": 173, "name": "block.calcite.break"}, {"id": 174, "name": "block.calcite.step"}, {"id": 175, "name": "block.calcite.place"}, {"id": 176, "name": "block.calcite.hit"}, {"id": 177, "name": "block.calcite.fall"}, {"id": 178, "name": "block.campfire.crackle"}, {"id": 179, "name": "block.candle.ambient"}, {"id": 180, "name": "block.candle.break"}, {"id": 181, "name": "block.candle.extinguish"}, {"id": 182, "name": "block.candle.fall"}, {"id": 183, "name": "block.candle.hit"}, {"id": 184, "name": "block.candle.place"}, {"id": 185, "name": "block.candle.step"}, {"id": 186, "name": "entity.cat.ambient"}, {"id": 187, "name": "entity.cat.stray_ambient"}, {"id": 188, "name": "entity.cat.death"}, {"id": 189, "name": "entity.cat.eat"}, {"id": 190, "name": "entity.cat.hiss"}, {"id": 191, "name": "entity.cat.beg_for_food"}, {"id": 192, "name": "entity.cat.hurt"}, {"id": 193, "name": "entity.cat.purr"}, {"id": 194, "name": "entity.cat.purreow"}, {"id": 195, "name": "block.cave_vines.break"}, {"id": 196, "name": "block.cave_vines.fall"}, {"id": 197, "name": "block.cave_vines.hit"}, {"id": 198, "name": "block.cave_vines.place"}, {"id": 199, "name": "block.cave_vines.step"}, {"id": 200, "name": "block.cave_vines.pick_berries"}, {"id": 201, "name": "block.chain.break"}, {"id": 202, "name": "block.chain.fall"}, {"id": 203, "name": "block.chain.hit"}, {"id": 204, "name": "block.chain.place"}, {"id": 205, "name": "block.chain.step"}, {"id": 206, "name": "block.chest.close"}, {"id": 207, "name": "block.chest.locked"}, {"id": 208, "name": "block.chest.open"}, {"id": 209, "name": "entity.chicken.ambient"}, {"id": 210, "name": "entity.chicken.death"}, {"id": 211, "name": "entity.chicken.egg"}, {"id": 212, "name": "entity.chicken.hurt"}, {"id": 213, "name": "entity.chicken.step"}, {"id": 214, "name": "block.chorus_flower.death"}, {"id": 215, "name": "block.chorus_flower.grow"}, {"id": 216, "name": "item.chorus_fruit.teleport"}, {"id": 217, "name": "entity.cod.ambient"}, {"id": 218, "name": "entity.cod.death"}, {"id": 219, "name": "entity.cod.flop"}, {"id": 220, "name": "entity.cod.hurt"}, {"id": 221, "name": "block.comparator.click"}, {"id": 222, "name": "block.composter.empty"}, {"id": 223, "name": "block.composter.fill"}, {"id": 224, "name": "block.composter.fill_success"}, {"id": 225, "name": "block.composter.ready"}, {"id": 226, "name": "block.conduit.activate"}, {"id": 227, "name": "block.conduit.ambient"}, {"id": 228, "name": "block.conduit.ambient.short"}, {"id": 229, "name": "block.conduit.attack.target"}, {"id": 230, "name": "block.conduit.deactivate"}, {"id": 231, "name": "block.copper.break"}, {"id": 232, "name": "block.copper.step"}, {"id": 233, "name": "block.copper.place"}, {"id": 234, "name": "block.copper.hit"}, {"id": 235, "name": "block.copper.fall"}, {"id": 236, "name": "block.coral_block.break"}, {"id": 237, "name": "block.coral_block.fall"}, {"id": 238, "name": "block.coral_block.hit"}, {"id": 239, "name": "block.coral_block.place"}, {"id": 240, "name": "block.coral_block.step"}, {"id": 241, "name": "entity.cow.ambient"}, {"id": 242, "name": "entity.cow.death"}, {"id": 243, "name": "entity.cow.hurt"}, {"id": 244, "name": "entity.cow.milk"}, {"id": 245, "name": "entity.cow.step"}, {"id": 246, "name": "entity.creeper.death"}, {"id": 247, "name": "entity.creeper.hurt"}, {"id": 248, "name": "entity.creeper.primed"}, {"id": 249, "name": "block.crop.break"}, {"id": 250, "name": "item.crop.plant"}, {"id": 251, "name": "item.crossbow.hit"}, {"id": 252, "name": "item.crossbow.loading_end"}, {"id": 253, "name": "item.crossbow.loading_middle"}, {"id": 254, "name": "item.crossbow.loading_start"}, {"id": 255, "name": "item.crossbow.quick_charge_1"}, {"id": 256, "name": "item.crossbow.quick_charge_2"}, {"id": 257, "name": "item.crossbow.quick_charge_3"}, {"id": 258, "name": "item.crossbow.shoot"}, {"id": 259, "name": "block.deepslate_bricks.break"}, {"id": 260, "name": "block.deepslate_bricks.fall"}, {"id": 261, "name": "block.deepslate_bricks.hit"}, {"id": 262, "name": "block.deepslate_bricks.place"}, {"id": 263, "name": "block.deepslate_bricks.step"}, {"id": 264, "name": "block.deepslate.break"}, {"id": 265, "name": "block.deepslate.fall"}, {"id": 266, "name": "block.deepslate.hit"}, {"id": 267, "name": "block.deepslate.place"}, {"id": 268, "name": "block.deepslate.step"}, {"id": 269, "name": "block.deepslate_tiles.break"}, {"id": 270, "name": "block.deepslate_tiles.fall"}, {"id": 271, "name": "block.deepslate_tiles.hit"}, {"id": 272, "name": "block.deepslate_tiles.place"}, {"id": 273, "name": "block.deepslate_tiles.step"}, {"id": 274, "name": "block.dispenser.dispense"}, {"id": 275, "name": "block.dispenser.fail"}, {"id": 276, "name": "block.dispenser.launch"}, {"id": 277, "name": "entity.dolphin.ambient"}, {"id": 278, "name": "entity.dolphin.ambient_water"}, {"id": 279, "name": "entity.dolphin.attack"}, {"id": 280, "name": "entity.dolphin.death"}, {"id": 281, "name": "entity.dolphin.eat"}, {"id": 282, "name": "entity.dolphin.hurt"}, {"id": 283, "name": "entity.dolphin.jump"}, {"id": 284, "name": "entity.dolphin.play"}, {"id": 285, "name": "entity.dolphin.splash"}, {"id": 286, "name": "entity.dolphin.swim"}, {"id": 287, "name": "entity.donkey.ambient"}, {"id": 288, "name": "entity.donkey.angry"}, {"id": 289, "name": "entity.donkey.chest"}, {"id": 290, "name": "entity.donkey.death"}, {"id": 291, "name": "entity.donkey.eat"}, {"id": 292, "name": "entity.donkey.hurt"}, {"id": 293, "name": "block.dripstone_block.break"}, {"id": 294, "name": "block.dripstone_block.step"}, {"id": 295, "name": "block.dripstone_block.place"}, {"id": 296, "name": "block.dripstone_block.hit"}, {"id": 297, "name": "block.dripstone_block.fall"}, {"id": 298, "name": "block.pointed_dripstone.break"}, {"id": 299, "name": "block.pointed_dripstone.step"}, {"id": 300, "name": "block.pointed_dripstone.place"}, {"id": 301, "name": "block.pointed_dripstone.hit"}, {"id": 302, "name": "block.pointed_dripstone.fall"}, {"id": 303, "name": "block.pointed_dripstone.land"}, {"id": 304, "name": "block.pointed_dripstone.drip_lava"}, {"id": 305, "name": "block.pointed_dripstone.drip_water"}, {"id": 306, "name": "block.pointed_dripstone.drip_lava_into_cauldron"}, {"id": 307, "name": "block.pointed_dripstone.drip_water_into_cauldron"}, {"id": 308, "name": "block.big_dripleaf.tilt_down"}, {"id": 309, "name": "block.big_dripleaf.tilt_up"}, {"id": 310, "name": "entity.drowned.ambient"}, {"id": 311, "name": "entity.drowned.ambient_water"}, {"id": 312, "name": "entity.drowned.death"}, {"id": 313, "name": "entity.drowned.death_water"}, {"id": 314, "name": "entity.drowned.hurt"}, {"id": 315, "name": "entity.drowned.hurt_water"}, {"id": 316, "name": "entity.drowned.shoot"}, {"id": 317, "name": "entity.drowned.step"}, {"id": 318, "name": "entity.drowned.swim"}, {"id": 319, "name": "item.dye.use"}, {"id": 320, "name": "entity.egg.throw"}, {"id": 321, "name": "entity.elder_guardian.ambient"}, {"id": 322, "name": "entity.elder_guardian.ambient_land"}, {"id": 323, "name": "entity.elder_guardian.curse"}, {"id": 324, "name": "entity.elder_guardian.death"}, {"id": 325, "name": "entity.elder_guardian.death_land"}, {"id": 326, "name": "entity.elder_guardian.flop"}, {"id": 327, "name": "entity.elder_guardian.hurt"}, {"id": 328, "name": "entity.elder_guardian.hurt_land"}, {"id": 329, "name": "item.elytra.flying"}, {"id": 330, "name": "block.enchantment_table.use"}, {"id": 331, "name": "block.ender_chest.close"}, {"id": 332, "name": "block.ender_chest.open"}, {"id": 333, "name": "entity.ender_dragon.ambient"}, {"id": 334, "name": "entity.ender_dragon.death"}, {"id": 335, "name": "entity.dragon_fireball.explode"}, {"id": 336, "name": "entity.ender_dragon.flap"}, {"id": 337, "name": "entity.ender_dragon.growl"}, {"id": 338, "name": "entity.ender_dragon.hurt"}, {"id": 339, "name": "entity.ender_dragon.shoot"}, {"id": 340, "name": "entity.ender_eye.death"}, {"id": 341, "name": "entity.ender_eye.launch"}, {"id": 342, "name": "entity.enderman.ambient"}, {"id": 343, "name": "entity.enderman.death"}, {"id": 344, "name": "entity.enderman.hurt"}, {"id": 345, "name": "entity.enderman.scream"}, {"id": 346, "name": "entity.enderman.stare"}, {"id": 347, "name": "entity.enderman.teleport"}, {"id": 348, "name": "entity.endermite.ambient"}, {"id": 349, "name": "entity.endermite.death"}, {"id": 350, "name": "entity.endermite.hurt"}, {"id": 351, "name": "entity.endermite.step"}, {"id": 352, "name": "entity.ender_pearl.throw"}, {"id": 353, "name": "block.end_gateway.spawn"}, {"id": 354, "name": "block.end_portal_frame.fill"}, {"id": 355, "name": "block.end_portal.spawn"}, {"id": 356, "name": "entity.evoker.ambient"}, {"id": 357, "name": "entity.evoker.cast_spell"}, {"id": 358, "name": "entity.evoker.celebrate"}, {"id": 359, "name": "entity.evoker.death"}, {"id": 360, "name": "entity.evoker_fangs.attack"}, {"id": 361, "name": "entity.evoker.hurt"}, {"id": 362, "name": "entity.evoker.prepare_attack"}, {"id": 363, "name": "entity.evoker.prepare_summon"}, {"id": 364, "name": "entity.evoker.prepare_wololo"}, {"id": 365, "name": "entity.experience_bottle.throw"}, {"id": 366, "name": "entity.experience_orb.pickup"}, {"id": 367, "name": "block.fence_gate.close"}, {"id": 368, "name": "block.fence_gate.open"}, {"id": 369, "name": "item.firecharge.use"}, {"id": 370, "name": "entity.firework_rocket.blast"}, {"id": 371, "name": "entity.firework_rocket.blast_far"}, {"id": 372, "name": "entity.firework_rocket.large_blast"}, {"id": 373, "name": "entity.firework_rocket.large_blast_far"}, {"id": 374, "name": "entity.firework_rocket.launch"}, {"id": 375, "name": "entity.firework_rocket.shoot"}, {"id": 376, "name": "entity.firework_rocket.twinkle"}, {"id": 377, "name": "entity.firework_rocket.twinkle_far"}, {"id": 378, "name": "block.fire.ambient"}, {"id": 379, "name": "block.fire.extinguish"}, {"id": 380, "name": "entity.fish.swim"}, {"id": 381, "name": "entity.fishing_bobber.retrieve"}, {"id": 382, "name": "entity.fishing_bobber.splash"}, {"id": 383, "name": "entity.fishing_bobber.throw"}, {"id": 384, "name": "item.flintandsteel.use"}, {"id": 385, "name": "block.flowering_azalea.break"}, {"id": 386, "name": "block.flowering_azalea.fall"}, {"id": 387, "name": "block.flowering_azalea.hit"}, {"id": 388, "name": "block.flowering_azalea.place"}, {"id": 389, "name": "block.flowering_azalea.step"}, {"id": 390, "name": "entity.fox.aggro"}, {"id": 391, "name": "entity.fox.ambient"}, {"id": 392, "name": "entity.fox.bite"}, {"id": 393, "name": "entity.fox.death"}, {"id": 394, "name": "entity.fox.eat"}, {"id": 395, "name": "entity.fox.hurt"}, {"id": 396, "name": "entity.fox.screech"}, {"id": 397, "name": "entity.fox.sleep"}, {"id": 398, "name": "entity.fox.sniff"}, {"id": 399, "name": "entity.fox.spit"}, {"id": 400, "name": "entity.fox.teleport"}, {"id": 401, "name": "block.froglight.break"}, {"id": 402, "name": "block.froglight.fall"}, {"id": 403, "name": "block.froglight.hit"}, {"id": 404, "name": "block.froglight.place"}, {"id": 405, "name": "block.froglight.step"}, {"id": 406, "name": "block.frogspawn.step"}, {"id": 407, "name": "block.frogspawn.break"}, {"id": 408, "name": "block.frogspawn.fall"}, {"id": 409, "name": "block.frogspawn.hatch"}, {"id": 410, "name": "block.frogspawn.hit"}, {"id": 411, "name": "block.frogspawn.place"}, {"id": 412, "name": "entity.frog.ambient"}, {"id": 413, "name": "entity.frog.death"}, {"id": 414, "name": "entity.frog.eat"}, {"id": 415, "name": "entity.frog.hurt"}, {"id": 416, "name": "entity.frog.lay_spawn"}, {"id": 417, "name": "entity.frog.long_jump"}, {"id": 418, "name": "entity.frog.step"}, {"id": 419, "name": "entity.frog.tongue"}, {"id": 420, "name": "block.roots.break"}, {"id": 421, "name": "block.roots.step"}, {"id": 422, "name": "block.roots.place"}, {"id": 423, "name": "block.roots.hit"}, {"id": 424, "name": "block.roots.fall"}, {"id": 425, "name": "block.furnace.fire_crackle"}, {"id": 426, "name": "entity.generic.big_fall"}, {"id": 427, "name": "entity.generic.burn"}, {"id": 428, "name": "entity.generic.death"}, {"id": 429, "name": "entity.generic.drink"}, {"id": 430, "name": "entity.generic.eat"}, {"id": 431, "name": "entity.generic.explode"}, {"id": 432, "name": "entity.generic.extinguish_fire"}, {"id": 433, "name": "entity.generic.hurt"}, {"id": 434, "name": "entity.generic.small_fall"}, {"id": 435, "name": "entity.generic.splash"}, {"id": 436, "name": "entity.generic.swim"}, {"id": 437, "name": "entity.ghast.ambient"}, {"id": 438, "name": "entity.ghast.death"}, {"id": 439, "name": "entity.ghast.hurt"}, {"id": 440, "name": "entity.ghast.scream"}, {"id": 441, "name": "entity.ghast.shoot"}, {"id": 442, "name": "entity.ghast.warn"}, {"id": 443, "name": "block.gilded_blackstone.break"}, {"id": 444, "name": "block.gilded_blackstone.fall"}, {"id": 445, "name": "block.gilded_blackstone.hit"}, {"id": 446, "name": "block.gilded_blackstone.place"}, {"id": 447, "name": "block.gilded_blackstone.step"}, {"id": 448, "name": "block.glass.break"}, {"id": 449, "name": "block.glass.fall"}, {"id": 450, "name": "block.glass.hit"}, {"id": 451, "name": "block.glass.place"}, {"id": 452, "name": "block.glass.step"}, {"id": 453, "name": "item.glow_ink_sac.use"}, {"id": 454, "name": "entity.glow_item_frame.add_item"}, {"id": 455, "name": "entity.glow_item_frame.break"}, {"id": 456, "name": "entity.glow_item_frame.place"}, {"id": 457, "name": "entity.glow_item_frame.remove_item"}, {"id": 458, "name": "entity.glow_item_frame.rotate_item"}, {"id": 459, "name": "entity.glow_squid.ambient"}, {"id": 460, "name": "entity.glow_squid.death"}, {"id": 461, "name": "entity.glow_squid.hurt"}, {"id": 462, "name": "entity.glow_squid.squirt"}, {"id": 463, "name": "entity.goat.ambient"}, {"id": 464, "name": "entity.goat.death"}, {"id": 465, "name": "entity.goat.eat"}, {"id": 466, "name": "entity.goat.hurt"}, {"id": 467, "name": "entity.goat.long_jump"}, {"id": 468, "name": "entity.goat.milk"}, {"id": 469, "name": "entity.goat.prepare_ram"}, {"id": 470, "name": "entity.goat.ram_impact"}, {"id": 471, "name": "entity.goat.horn_break"}, {"id": 472, "name": "item.goat_horn.play"}, {"id": 473, "name": "entity.goat.screaming.ambient"}, {"id": 474, "name": "entity.goat.screaming.death"}, {"id": 475, "name": "entity.goat.screaming.eat"}, {"id": 476, "name": "entity.goat.screaming.hurt"}, {"id": 477, "name": "entity.goat.screaming.long_jump"}, {"id": 478, "name": "entity.goat.screaming.milk"}, {"id": 479, "name": "entity.goat.screaming.prepare_ram"}, {"id": 480, "name": "entity.goat.screaming.ram_impact"}, {"id": 481, "name": "entity.goat.screaming.horn_break"}, {"id": 482, "name": "entity.goat.step"}, {"id": 483, "name": "block.grass.break"}, {"id": 484, "name": "block.grass.fall"}, {"id": 485, "name": "block.grass.hit"}, {"id": 486, "name": "block.grass.place"}, {"id": 487, "name": "block.grass.step"}, {"id": 488, "name": "block.gravel.break"}, {"id": 489, "name": "block.gravel.fall"}, {"id": 490, "name": "block.gravel.hit"}, {"id": 491, "name": "block.gravel.place"}, {"id": 492, "name": "block.gravel.step"}, {"id": 493, "name": "block.grindstone.use"}, {"id": 494, "name": "block.growing_plant.crop"}, {"id": 495, "name": "entity.guardian.ambient"}, {"id": 496, "name": "entity.guardian.ambient_land"}, {"id": 497, "name": "entity.guardian.attack"}, {"id": 498, "name": "entity.guardian.death"}, {"id": 499, "name": "entity.guardian.death_land"}, {"id": 500, "name": "entity.guardian.flop"}, {"id": 501, "name": "entity.guardian.hurt"}, {"id": 502, "name": "entity.guardian.hurt_land"}, {"id": 503, "name": "block.hanging_roots.break"}, {"id": 504, "name": "block.hanging_roots.fall"}, {"id": 505, "name": "block.hanging_roots.hit"}, {"id": 506, "name": "block.hanging_roots.place"}, {"id": 507, "name": "block.hanging_roots.step"}, {"id": 508, "name": "item.hoe.till"}, {"id": 509, "name": "entity.hoglin.ambient"}, {"id": 510, "name": "entity.hoglin.angry"}, {"id": 511, "name": "entity.hoglin.attack"}, {"id": 512, "name": "entity.hoglin.converted_to_zombified"}, {"id": 513, "name": "entity.hoglin.death"}, {"id": 514, "name": "entity.hoglin.hurt"}, {"id": 515, "name": "entity.hoglin.retreat"}, {"id": 516, "name": "entity.hoglin.step"}, {"id": 517, "name": "block.honey_block.break"}, {"id": 518, "name": "block.honey_block.fall"}, {"id": 519, "name": "block.honey_block.hit"}, {"id": 520, "name": "block.honey_block.place"}, {"id": 521, "name": "block.honey_block.slide"}, {"id": 522, "name": "block.honey_block.step"}, {"id": 523, "name": "item.honeycomb.wax_on"}, {"id": 524, "name": "item.honey_bottle.drink"}, {"id": 525, "name": "item.goat_horn.sound.0"}, {"id": 526, "name": "item.goat_horn.sound.1"}, {"id": 527, "name": "item.goat_horn.sound.2"}, {"id": 528, "name": "item.goat_horn.sound.3"}, {"id": 529, "name": "item.goat_horn.sound.4"}, {"id": 530, "name": "item.goat_horn.sound.5"}, {"id": 531, "name": "item.goat_horn.sound.6"}, {"id": 532, "name": "item.goat_horn.sound.7"}, {"id": 533, "name": "entity.horse.ambient"}, {"id": 534, "name": "entity.horse.angry"}, {"id": 535, "name": "entity.horse.armor"}, {"id": 536, "name": "entity.horse.breathe"}, {"id": 537, "name": "entity.horse.death"}, {"id": 538, "name": "entity.horse.eat"}, {"id": 539, "name": "entity.horse.gallop"}, {"id": 540, "name": "entity.horse.hurt"}, {"id": 541, "name": "entity.horse.jump"}, {"id": 542, "name": "entity.horse.land"}, {"id": 543, "name": "entity.horse.saddle"}, {"id": 544, "name": "entity.horse.step"}, {"id": 545, "name": "entity.horse.step_wood"}, {"id": 546, "name": "entity.hostile.big_fall"}, {"id": 547, "name": "entity.hostile.death"}, {"id": 548, "name": "entity.hostile.hurt"}, {"id": 549, "name": "entity.hostile.small_fall"}, {"id": 550, "name": "entity.hostile.splash"}, {"id": 551, "name": "entity.hostile.swim"}, {"id": 552, "name": "entity.husk.ambient"}, {"id": 553, "name": "entity.husk.converted_to_zombie"}, {"id": 554, "name": "entity.husk.death"}, {"id": 555, "name": "entity.husk.hurt"}, {"id": 556, "name": "entity.husk.step"}, {"id": 557, "name": "entity.illusioner.ambient"}, {"id": 558, "name": "entity.illusioner.cast_spell"}, {"id": 559, "name": "entity.illusioner.death"}, {"id": 560, "name": "entity.illusioner.hurt"}, {"id": 561, "name": "entity.illusioner.mirror_move"}, {"id": 562, "name": "entity.illusioner.prepare_blindness"}, {"id": 563, "name": "entity.illusioner.prepare_mirror"}, {"id": 564, "name": "item.ink_sac.use"}, {"id": 565, "name": "block.iron_door.close"}, {"id": 566, "name": "block.iron_door.open"}, {"id": 567, "name": "entity.iron_golem.attack"}, {"id": 568, "name": "entity.iron_golem.damage"}, {"id": 569, "name": "entity.iron_golem.death"}, {"id": 570, "name": "entity.iron_golem.hurt"}, {"id": 571, "name": "entity.iron_golem.repair"}, {"id": 572, "name": "entity.iron_golem.step"}, {"id": 573, "name": "block.iron_trapdoor.close"}, {"id": 574, "name": "block.iron_trapdoor.open"}, {"id": 575, "name": "entity.item_frame.add_item"}, {"id": 576, "name": "entity.item_frame.break"}, {"id": 577, "name": "entity.item_frame.place"}, {"id": 578, "name": "entity.item_frame.remove_item"}, {"id": 579, "name": "entity.item_frame.rotate_item"}, {"id": 580, "name": "entity.item.break"}, {"id": 581, "name": "entity.item.pickup"}, {"id": 582, "name": "block.ladder.break"}, {"id": 583, "name": "block.ladder.fall"}, {"id": 584, "name": "block.ladder.hit"}, {"id": 585, "name": "block.ladder.place"}, {"id": 586, "name": "block.ladder.step"}, {"id": 587, "name": "block.lantern.break"}, {"id": 588, "name": "block.lantern.fall"}, {"id": 589, "name": "block.lantern.hit"}, {"id": 590, "name": "block.lantern.place"}, {"id": 591, "name": "block.lantern.step"}, {"id": 592, "name": "block.large_amethyst_bud.break"}, {"id": 593, "name": "block.large_amethyst_bud.place"}, {"id": 594, "name": "block.lava.ambient"}, {"id": 595, "name": "block.lava.extinguish"}, {"id": 596, "name": "block.lava.pop"}, {"id": 597, "name": "entity.leash_knot.break"}, {"id": 598, "name": "entity.leash_knot.place"}, {"id": 599, "name": "block.lever.click"}, {"id": 600, "name": "entity.lightning_bolt.impact"}, {"id": 601, "name": "entity.lightning_bolt.thunder"}, {"id": 602, "name": "entity.lingering_potion.throw"}, {"id": 603, "name": "entity.llama.ambient"}, {"id": 604, "name": "entity.llama.angry"}, {"id": 605, "name": "entity.llama.chest"}, {"id": 606, "name": "entity.llama.death"}, {"id": 607, "name": "entity.llama.eat"}, {"id": 608, "name": "entity.llama.hurt"}, {"id": 609, "name": "entity.llama.spit"}, {"id": 610, "name": "entity.llama.step"}, {"id": 611, "name": "entity.llama.swag"}, {"id": 612, "name": "entity.magma_cube.death_small"}, {"id": 613, "name": "block.lodestone.break"}, {"id": 614, "name": "block.lodestone.step"}, {"id": 615, "name": "block.lodestone.place"}, {"id": 616, "name": "block.lodestone.hit"}, {"id": 617, "name": "block.lodestone.fall"}, {"id": 618, "name": "item.lodestone_compass.lock"}, {"id": 619, "name": "entity.magma_cube.death"}, {"id": 620, "name": "entity.magma_cube.hurt"}, {"id": 621, "name": "entity.magma_cube.hurt_small"}, {"id": 622, "name": "entity.magma_cube.jump"}, {"id": 623, "name": "entity.magma_cube.squish"}, {"id": 624, "name": "entity.magma_cube.squish_small"}, {"id": 625, "name": "block.mangrove_roots.break"}, {"id": 626, "name": "block.mangrove_roots.fall"}, {"id": 627, "name": "block.mangrove_roots.hit"}, {"id": 628, "name": "block.mangrove_roots.place"}, {"id": 629, "name": "block.mangrove_roots.step"}, {"id": 630, "name": "block.medium_amethyst_bud.break"}, {"id": 631, "name": "block.medium_amethyst_bud.place"}, {"id": 632, "name": "block.metal.break"}, {"id": 633, "name": "block.metal.fall"}, {"id": 634, "name": "block.metal.hit"}, {"id": 635, "name": "block.metal.place"}, {"id": 636, "name": "block.metal_pressure_plate.click_off"}, {"id": 637, "name": "block.metal_pressure_plate.click_on"}, {"id": 638, "name": "block.metal.step"}, {"id": 639, "name": "entity.minecart.inside.underwater"}, {"id": 640, "name": "entity.minecart.inside"}, {"id": 641, "name": "entity.minecart.riding"}, {"id": 642, "name": "entity.mooshroom.convert"}, {"id": 643, "name": "entity.mooshroom.eat"}, {"id": 644, "name": "entity.mooshroom.milk"}, {"id": 645, "name": "entity.mooshroom.suspicious_milk"}, {"id": 646, "name": "entity.mooshroom.shear"}, {"id": 647, "name": "block.moss_carpet.break"}, {"id": 648, "name": "block.moss_carpet.fall"}, {"id": 649, "name": "block.moss_carpet.hit"}, {"id": 650, "name": "block.moss_carpet.place"}, {"id": 651, "name": "block.moss_carpet.step"}, {"id": 652, "name": "block.moss.break"}, {"id": 653, "name": "block.moss.fall"}, {"id": 654, "name": "block.moss.hit"}, {"id": 655, "name": "block.moss.place"}, {"id": 656, "name": "block.moss.step"}, {"id": 657, "name": "block.mud.break"}, {"id": 658, "name": "block.mud.fall"}, {"id": 659, "name": "block.mud.hit"}, {"id": 660, "name": "block.mud.place"}, {"id": 661, "name": "block.mud.step"}, {"id": 662, "name": "block.mud_bricks.break"}, {"id": 663, "name": "block.mud_bricks.fall"}, {"id": 664, "name": "block.mud_bricks.hit"}, {"id": 665, "name": "block.mud_bricks.place"}, {"id": 666, "name": "block.mud_bricks.step"}, {"id": 667, "name": "block.muddy_mangrove_roots.break"}, {"id": 668, "name": "block.muddy_mangrove_roots.fall"}, {"id": 669, "name": "block.muddy_mangrove_roots.hit"}, {"id": 670, "name": "block.muddy_mangrove_roots.place"}, {"id": 671, "name": "block.muddy_mangrove_roots.step"}, {"id": 672, "name": "entity.mule.ambient"}, {"id": 673, "name": "entity.mule.angry"}, {"id": 674, "name": "entity.mule.chest"}, {"id": 675, "name": "entity.mule.death"}, {"id": 676, "name": "entity.mule.eat"}, {"id": 677, "name": "entity.mule.hurt"}, {"id": 678, "name": "music.creative"}, {"id": 679, "name": "music.credits"}, {"id": 680, "name": "music_disc.5"}, {"id": 681, "name": "music_disc.11"}, {"id": 682, "name": "music_disc.13"}, {"id": 683, "name": "music_disc.blocks"}, {"id": 684, "name": "music_disc.cat"}, {"id": 685, "name": "music_disc.chirp"}, {"id": 686, "name": "music_disc.far"}, {"id": 687, "name": "music_disc.mall"}, {"id": 688, "name": "music_disc.mellohi"}, {"id": 689, "name": "music_disc.pigstep"}, {"id": 690, "name": "music_disc.stal"}, {"id": 691, "name": "music_disc.strad"}, {"id": 692, "name": "music_disc.wait"}, {"id": 693, "name": "music_disc.ward"}, {"id": 694, "name": "music_disc.otherside"}, {"id": 695, "name": "music.dragon"}, {"id": 696, "name": "music.end"}, {"id": 697, "name": "music.game"}, {"id": 698, "name": "music.menu"}, {"id": 699, "name": "music.nether.basalt_deltas"}, {"id": 700, "name": "music.nether.crimson_forest"}, {"id": 701, "name": "music.overworld.deep_dark"}, {"id": 702, "name": "music.overworld.dripstone_caves"}, {"id": 703, "name": "music.overworld.grove"}, {"id": 704, "name": "music.overworld.jagged_peaks"}, {"id": 705, "name": "music.overworld.lush_caves"}, {"id": 706, "name": "music.overworld.swamp"}, {"id": 707, "name": "music.overworld.jungle_and_forest"}, {"id": 708, "name": "music.overworld.old_growth_taiga"}, {"id": 709, "name": "music.overworld.meadow"}, {"id": 710, "name": "music.nether.nether_wastes"}, {"id": 711, "name": "music.overworld.frozen_peaks"}, {"id": 712, "name": "music.overworld.snowy_slopes"}, {"id": 713, "name": "music.nether.soul_sand_valley"}, {"id": 714, "name": "music.overworld.stony_peaks"}, {"id": 715, "name": "music.nether.warped_forest"}, {"id": 716, "name": "music.under_water"}, {"id": 717, "name": "block.nether_bricks.break"}, {"id": 718, "name": "block.nether_bricks.step"}, {"id": 719, "name": "block.nether_bricks.place"}, {"id": 720, "name": "block.nether_bricks.hit"}, {"id": 721, "name": "block.nether_bricks.fall"}, {"id": 722, "name": "block.nether_wart.break"}, {"id": 723, "name": "item.nether_wart.plant"}, {"id": 724, "name": "block.packed_mud.break"}, {"id": 725, "name": "block.packed_mud.fall"}, {"id": 726, "name": "block.packed_mud.hit"}, {"id": 727, "name": "block.packed_mud.place"}, {"id": 728, "name": "block.packed_mud.step"}, {"id": 729, "name": "block.stem.break"}, {"id": 730, "name": "block.stem.step"}, {"id": 731, "name": "block.stem.place"}, {"id": 732, "name": "block.stem.hit"}, {"id": 733, "name": "block.stem.fall"}, {"id": 734, "name": "block.nylium.break"}, {"id": 735, "name": "block.nylium.step"}, {"id": 736, "name": "block.nylium.place"}, {"id": 737, "name": "block.nylium.hit"}, {"id": 738, "name": "block.nylium.fall"}, {"id": 739, "name": "block.nether_sprouts.break"}, {"id": 740, "name": "block.nether_sprouts.step"}, {"id": 741, "name": "block.nether_sprouts.place"}, {"id": 742, "name": "block.nether_sprouts.hit"}, {"id": 743, "name": "block.nether_sprouts.fall"}, {"id": 744, "name": "block.fungus.break"}, {"id": 745, "name": "block.fungus.step"}, {"id": 746, "name": "block.fungus.place"}, {"id": 747, "name": "block.fungus.hit"}, {"id": 748, "name": "block.fungus.fall"}, {"id": 749, "name": "block.weeping_vines.break"}, {"id": 750, "name": "block.weeping_vines.step"}, {"id": 751, "name": "block.weeping_vines.place"}, {"id": 752, "name": "block.weeping_vines.hit"}, {"id": 753, "name": "block.weeping_vines.fall"}, {"id": 754, "name": "block.wart_block.break"}, {"id": 755, "name": "block.wart_block.step"}, {"id": 756, "name": "block.wart_block.place"}, {"id": 757, "name": "block.wart_block.hit"}, {"id": 758, "name": "block.wart_block.fall"}, {"id": 759, "name": "block.netherite_block.break"}, {"id": 760, "name": "block.netherite_block.step"}, {"id": 761, "name": "block.netherite_block.place"}, {"id": 762, "name": "block.netherite_block.hit"}, {"id": 763, "name": "block.netherite_block.fall"}, {"id": 764, "name": "block.netherrack.break"}, {"id": 765, "name": "block.netherrack.step"}, {"id": 766, "name": "block.netherrack.place"}, {"id": 767, "name": "block.netherrack.hit"}, {"id": 768, "name": "block.netherrack.fall"}, {"id": 769, "name": "block.note_block.basedrum"}, {"id": 770, "name": "block.note_block.bass"}, {"id": 771, "name": "block.note_block.bell"}, {"id": 772, "name": "block.note_block.chime"}, {"id": 773, "name": "block.note_block.flute"}, {"id": 774, "name": "block.note_block.guitar"}, {"id": 775, "name": "block.note_block.harp"}, {"id": 776, "name": "block.note_block.hat"}, {"id": 777, "name": "block.note_block.pling"}, {"id": 778, "name": "block.note_block.snare"}, {"id": 779, "name": "block.note_block.xylophone"}, {"id": 780, "name": "block.note_block.iron_xylophone"}, {"id": 781, "name": "block.note_block.cow_bell"}, {"id": 782, "name": "block.note_block.didgeridoo"}, {"id": 783, "name": "block.note_block.bit"}, {"id": 784, "name": "block.note_block.banjo"}, {"id": 785, "name": "entity.ocelot.hurt"}, {"id": 786, "name": "entity.ocelot.ambient"}, {"id": 787, "name": "entity.ocelot.death"}, {"id": 788, "name": "entity.painting.break"}, {"id": 789, "name": "entity.painting.place"}, {"id": 790, "name": "entity.panda.pre_sneeze"}, {"id": 791, "name": "entity.panda.sneeze"}, {"id": 792, "name": "entity.panda.ambient"}, {"id": 793, "name": "entity.panda.death"}, {"id": 794, "name": "entity.panda.eat"}, {"id": 795, "name": "entity.panda.step"}, {"id": 796, "name": "entity.panda.cant_breed"}, {"id": 797, "name": "entity.panda.aggressive_ambient"}, {"id": 798, "name": "entity.panda.worried_ambient"}, {"id": 799, "name": "entity.panda.hurt"}, {"id": 800, "name": "entity.panda.bite"}, {"id": 801, "name": "entity.parrot.ambient"}, {"id": 802, "name": "entity.parrot.death"}, {"id": 803, "name": "entity.parrot.eat"}, {"id": 804, "name": "entity.parrot.fly"}, {"id": 805, "name": "entity.parrot.hurt"}, {"id": 806, "name": "entity.parrot.imitate.blaze"}, {"id": 807, "name": "entity.parrot.imitate.creeper"}, {"id": 808, "name": "entity.parrot.imitate.drowned"}, {"id": 809, "name": "entity.parrot.imitate.elder_guardian"}, {"id": 810, "name": "entity.parrot.imitate.ender_dragon"}, {"id": 811, "name": "entity.parrot.imitate.endermite"}, {"id": 812, "name": "entity.parrot.imitate.evoker"}, {"id": 813, "name": "entity.parrot.imitate.ghast"}, {"id": 814, "name": "entity.parrot.imitate.guardian"}, {"id": 815, "name": "entity.parrot.imitate.hoglin"}, {"id": 816, "name": "entity.parrot.imitate.husk"}, {"id": 817, "name": "entity.parrot.imitate.illusioner"}, {"id": 818, "name": "entity.parrot.imitate.magma_cube"}, {"id": 819, "name": "entity.parrot.imitate.phantom"}, {"id": 820, "name": "entity.parrot.imitate.piglin"}, {"id": 821, "name": "entity.parrot.imitate.piglin_brute"}, {"id": 822, "name": "entity.parrot.imitate.pillager"}, {"id": 823, "name": "entity.parrot.imitate.ravager"}, {"id": 824, "name": "entity.parrot.imitate.shulker"}, {"id": 825, "name": "entity.parrot.imitate.silverfish"}, {"id": 826, "name": "entity.parrot.imitate.skeleton"}, {"id": 827, "name": "entity.parrot.imitate.slime"}, {"id": 828, "name": "entity.parrot.imitate.spider"}, {"id": 829, "name": "entity.parrot.imitate.stray"}, {"id": 830, "name": "entity.parrot.imitate.vex"}, {"id": 831, "name": "entity.parrot.imitate.vindicator"}, {"id": 832, "name": "entity.parrot.imitate.warden"}, {"id": 833, "name": "entity.parrot.imitate.witch"}, {"id": 834, "name": "entity.parrot.imitate.wither"}, {"id": 835, "name": "entity.parrot.imitate.wither_skeleton"}, {"id": 836, "name": "entity.parrot.imitate.zoglin"}, {"id": 837, "name": "entity.parrot.imitate.zombie"}, {"id": 838, "name": "entity.parrot.imitate.zombie_villager"}, {"id": 839, "name": "entity.parrot.step"}, {"id": 840, "name": "entity.phantom.ambient"}, {"id": 841, "name": "entity.phantom.bite"}, {"id": 842, "name": "entity.phantom.death"}, {"id": 843, "name": "entity.phantom.flap"}, {"id": 844, "name": "entity.phantom.hurt"}, {"id": 845, "name": "entity.phantom.swoop"}, {"id": 846, "name": "entity.pig.ambient"}, {"id": 847, "name": "entity.pig.death"}, {"id": 848, "name": "entity.pig.hurt"}, {"id": 849, "name": "entity.pig.saddle"}, {"id": 850, "name": "entity.pig.step"}, {"id": 851, "name": "entity.piglin.admiring_item"}, {"id": 852, "name": "entity.piglin.ambient"}, {"id": 853, "name": "entity.piglin.angry"}, {"id": 854, "name": "entity.piglin.celebrate"}, {"id": 855, "name": "entity.piglin.death"}, {"id": 856, "name": "entity.piglin.jealous"}, {"id": 857, "name": "entity.piglin.hurt"}, {"id": 858, "name": "entity.piglin.retreat"}, {"id": 859, "name": "entity.piglin.step"}, {"id": 860, "name": "entity.piglin.converted_to_zombified"}, {"id": 861, "name": "entity.piglin_brute.ambient"}, {"id": 862, "name": "entity.piglin_brute.angry"}, {"id": 863, "name": "entity.piglin_brute.death"}, {"id": 864, "name": "entity.piglin_brute.hurt"}, {"id": 865, "name": "entity.piglin_brute.step"}, {"id": 866, "name": "entity.piglin_brute.converted_to_zombified"}, {"id": 867, "name": "entity.pillager.ambient"}, {"id": 868, "name": "entity.pillager.celebrate"}, {"id": 869, "name": "entity.pillager.death"}, {"id": 870, "name": "entity.pillager.hurt"}, {"id": 871, "name": "block.piston.contract"}, {"id": 872, "name": "block.piston.extend"}, {"id": 873, "name": "entity.player.attack.crit"}, {"id": 874, "name": "entity.player.attack.knockback"}, {"id": 875, "name": "entity.player.attack.nodamage"}, {"id": 876, "name": "entity.player.attack.strong"}, {"id": 877, "name": "entity.player.attack.sweep"}, {"id": 878, "name": "entity.player.attack.weak"}, {"id": 879, "name": "entity.player.big_fall"}, {"id": 880, "name": "entity.player.breath"}, {"id": 881, "name": "entity.player.burp"}, {"id": 882, "name": "entity.player.death"}, {"id": 883, "name": "entity.player.hurt"}, {"id": 884, "name": "entity.player.hurt_drown"}, {"id": 885, "name": "entity.player.hurt_freeze"}, {"id": 886, "name": "entity.player.hurt_on_fire"}, {"id": 887, "name": "entity.player.hurt_sweet_berry_bush"}, {"id": 888, "name": "entity.player.levelup"}, {"id": 889, "name": "entity.player.small_fall"}, {"id": 890, "name": "entity.player.splash"}, {"id": 891, "name": "entity.player.splash.high_speed"}, {"id": 892, "name": "entity.player.swim"}, {"id": 893, "name": "entity.polar_bear.ambient"}, {"id": 894, "name": "entity.polar_bear.ambient_baby"}, {"id": 895, "name": "entity.polar_bear.death"}, {"id": 896, "name": "entity.polar_bear.hurt"}, {"id": 897, "name": "entity.polar_bear.step"}, {"id": 898, "name": "entity.polar_bear.warning"}, {"id": 899, "name": "block.polished_deepslate.break"}, {"id": 900, "name": "block.polished_deepslate.fall"}, {"id": 901, "name": "block.polished_deepslate.hit"}, {"id": 902, "name": "block.polished_deepslate.place"}, {"id": 903, "name": "block.polished_deepslate.step"}, {"id": 904, "name": "block.portal.ambient"}, {"id": 905, "name": "block.portal.travel"}, {"id": 906, "name": "block.portal.trigger"}, {"id": 907, "name": "block.powder_snow.break"}, {"id": 908, "name": "block.powder_snow.fall"}, {"id": 909, "name": "block.powder_snow.hit"}, {"id": 910, "name": "block.powder_snow.place"}, {"id": 911, "name": "block.powder_snow.step"}, {"id": 912, "name": "entity.puffer_fish.ambient"}, {"id": 913, "name": "entity.puffer_fish.blow_out"}, {"id": 914, "name": "entity.puffer_fish.blow_up"}, {"id": 915, "name": "entity.puffer_fish.death"}, {"id": 916, "name": "entity.puffer_fish.flop"}, {"id": 917, "name": "entity.puffer_fish.hurt"}, {"id": 918, "name": "entity.puffer_fish.sting"}, {"id": 919, "name": "block.pumpkin.carve"}, {"id": 920, "name": "entity.rabbit.ambient"}, {"id": 921, "name": "entity.rabbit.attack"}, {"id": 922, "name": "entity.rabbit.death"}, {"id": 923, "name": "entity.rabbit.hurt"}, {"id": 924, "name": "entity.rabbit.jump"}, {"id": 925, "name": "event.raid.horn"}, {"id": 926, "name": "entity.ravager.ambient"}, {"id": 927, "name": "entity.ravager.attack"}, {"id": 928, "name": "entity.ravager.celebrate"}, {"id": 929, "name": "entity.ravager.death"}, {"id": 930, "name": "entity.ravager.hurt"}, {"id": 931, "name": "entity.ravager.step"}, {"id": 932, "name": "entity.ravager.stunned"}, {"id": 933, "name": "entity.ravager.roar"}, {"id": 934, "name": "block.nether_gold_ore.break"}, {"id": 935, "name": "block.nether_gold_ore.fall"}, {"id": 936, "name": "block.nether_gold_ore.hit"}, {"id": 937, "name": "block.nether_gold_ore.place"}, {"id": 938, "name": "block.nether_gold_ore.step"}, {"id": 939, "name": "block.nether_ore.break"}, {"id": 940, "name": "block.nether_ore.fall"}, {"id": 941, "name": "block.nether_ore.hit"}, {"id": 942, "name": "block.nether_ore.place"}, {"id": 943, "name": "block.nether_ore.step"}, {"id": 944, "name": "block.redstone_torch.burnout"}, {"id": 945, "name": "block.respawn_anchor.ambient"}, {"id": 946, "name": "block.respawn_anchor.charge"}, {"id": 947, "name": "block.respawn_anchor.deplete"}, {"id": 948, "name": "block.respawn_anchor.set_spawn"}, {"id": 949, "name": "block.rooted_dirt.break"}, {"id": 950, "name": "block.rooted_dirt.fall"}, {"id": 951, "name": "block.rooted_dirt.hit"}, {"id": 952, "name": "block.rooted_dirt.place"}, {"id": 953, "name": "block.rooted_dirt.step"}, {"id": 954, "name": "entity.salmon.ambient"}, {"id": 955, "name": "entity.salmon.death"}, {"id": 956, "name": "entity.salmon.flop"}, {"id": 957, "name": "entity.salmon.hurt"}, {"id": 958, "name": "block.sand.break"}, {"id": 959, "name": "block.sand.fall"}, {"id": 960, "name": "block.sand.hit"}, {"id": 961, "name": "block.sand.place"}, {"id": 962, "name": "block.sand.step"}, {"id": 963, "name": "block.scaffolding.break"}, {"id": 964, "name": "block.scaffolding.fall"}, {"id": 965, "name": "block.scaffolding.hit"}, {"id": 966, "name": "block.scaffolding.place"}, {"id": 967, "name": "block.scaffolding.step"}, {"id": 968, "name": "block.sculk.spread"}, {"id": 969, "name": "block.sculk.charge"}, {"id": 970, "name": "block.sculk.break"}, {"id": 971, "name": "block.sculk.fall"}, {"id": 972, "name": "block.sculk.hit"}, {"id": 973, "name": "block.sculk.place"}, {"id": 974, "name": "block.sculk.step"}, {"id": 975, "name": "block.sculk_catalyst.bloom"}, {"id": 976, "name": "block.sculk_catalyst.break"}, {"id": 977, "name": "block.sculk_catalyst.fall"}, {"id": 978, "name": "block.sculk_catalyst.hit"}, {"id": 979, "name": "block.sculk_catalyst.place"}, {"id": 980, "name": "block.sculk_catalyst.step"}, {"id": 981, "name": "block.sculk_sensor.clicking"}, {"id": 982, "name": "block.sculk_sensor.clicking_stop"}, {"id": 983, "name": "block.sculk_sensor.break"}, {"id": 984, "name": "block.sculk_sensor.fall"}, {"id": 985, "name": "block.sculk_sensor.hit"}, {"id": 986, "name": "block.sculk_sensor.place"}, {"id": 987, "name": "block.sculk_sensor.step"}, {"id": 988, "name": "block.sculk_shrieker.break"}, {"id": 989, "name": "block.sculk_shrieker.fall"}, {"id": 990, "name": "block.sculk_shrieker.hit"}, {"id": 991, "name": "block.sculk_shrieker.place"}, {"id": 992, "name": "block.sculk_shrieker.shriek"}, {"id": 993, "name": "block.sculk_shrieker.step"}, {"id": 994, "name": "block.sculk_vein.break"}, {"id": 995, "name": "block.sculk_vein.fall"}, {"id": 996, "name": "block.sculk_vein.hit"}, {"id": 997, "name": "block.sculk_vein.place"}, {"id": 998, "name": "block.sculk_vein.step"}, {"id": 999, "name": "entity.sheep.ambient"}, {"id": 1000, "name": "entity.sheep.death"}, {"id": 1001, "name": "entity.sheep.hurt"}, {"id": 1002, "name": "entity.sheep.shear"}, {"id": 1003, "name": "entity.sheep.step"}, {"id": 1004, "name": "item.shield.block"}, {"id": 1005, "name": "item.shield.break"}, {"id": 1006, "name": "block.shroomlight.break"}, {"id": 1007, "name": "block.shroomlight.step"}, {"id": 1008, "name": "block.shroomlight.place"}, {"id": 1009, "name": "block.shroomlight.hit"}, {"id": 1010, "name": "block.shroomlight.fall"}, {"id": 1011, "name": "item.shovel.flatten"}, {"id": 1012, "name": "entity.shulker.ambient"}, {"id": 1013, "name": "block.shulker_box.close"}, {"id": 1014, "name": "block.shulker_box.open"}, {"id": 1015, "name": "entity.shulker_bullet.hit"}, {"id": 1016, "name": "entity.shulker_bullet.hurt"}, {"id": 1017, "name": "entity.shulker.close"}, {"id": 1018, "name": "entity.shulker.death"}, {"id": 1019, "name": "entity.shulker.hurt"}, {"id": 1020, "name": "entity.shulker.hurt_closed"}, {"id": 1021, "name": "entity.shulker.open"}, {"id": 1022, "name": "entity.shulker.shoot"}, {"id": 1023, "name": "entity.shulker.teleport"}, {"id": 1024, "name": "entity.silverfish.ambient"}, {"id": 1025, "name": "entity.silverfish.death"}, {"id": 1026, "name": "entity.silverfish.hurt"}, {"id": 1027, "name": "entity.silverfish.step"}, {"id": 1028, "name": "entity.skeleton.ambient"}, {"id": 1029, "name": "entity.skeleton.converted_to_stray"}, {"id": 1030, "name": "entity.skeleton.death"}, {"id": 1031, "name": "entity.skeleton_horse.ambient"}, {"id": 1032, "name": "entity.skeleton_horse.death"}, {"id": 1033, "name": "entity.skeleton_horse.hurt"}, {"id": 1034, "name": "entity.skeleton_horse.swim"}, {"id": 1035, "name": "entity.skeleton_horse.ambient_water"}, {"id": 1036, "name": "entity.skeleton_horse.gallop_water"}, {"id": 1037, "name": "entity.skeleton_horse.jump_water"}, {"id": 1038, "name": "entity.skeleton_horse.step_water"}, {"id": 1039, "name": "entity.skeleton.hurt"}, {"id": 1040, "name": "entity.skeleton.shoot"}, {"id": 1041, "name": "entity.skeleton.step"}, {"id": 1042, "name": "entity.slime.attack"}, {"id": 1043, "name": "entity.slime.death"}, {"id": 1044, "name": "entity.slime.hurt"}, {"id": 1045, "name": "entity.slime.jump"}, {"id": 1046, "name": "entity.slime.squish"}, {"id": 1047, "name": "block.slime_block.break"}, {"id": 1048, "name": "block.slime_block.fall"}, {"id": 1049, "name": "block.slime_block.hit"}, {"id": 1050, "name": "block.slime_block.place"}, {"id": 1051, "name": "block.slime_block.step"}, {"id": 1052, "name": "block.small_amethyst_bud.break"}, {"id": 1053, "name": "block.small_amethyst_bud.place"}, {"id": 1054, "name": "block.small_dripleaf.break"}, {"id": 1055, "name": "block.small_dripleaf.fall"}, {"id": 1056, "name": "block.small_dripleaf.hit"}, {"id": 1057, "name": "block.small_dripleaf.place"}, {"id": 1058, "name": "block.small_dripleaf.step"}, {"id": 1059, "name": "block.soul_sand.break"}, {"id": 1060, "name": "block.soul_sand.step"}, {"id": 1061, "name": "block.soul_sand.place"}, {"id": 1062, "name": "block.soul_sand.hit"}, {"id": 1063, "name": "block.soul_sand.fall"}, {"id": 1064, "name": "block.soul_soil.break"}, {"id": 1065, "name": "block.soul_soil.step"}, {"id": 1066, "name": "block.soul_soil.place"}, {"id": 1067, "name": "block.soul_soil.hit"}, {"id": 1068, "name": "block.soul_soil.fall"}, {"id": 1069, "name": "particle.soul_escape"}, {"id": 1070, "name": "block.spore_blossom.break"}, {"id": 1071, "name": "block.spore_blossom.fall"}, {"id": 1072, "name": "block.spore_blossom.hit"}, {"id": 1073, "name": "block.spore_blossom.place"}, {"id": 1074, "name": "block.spore_blossom.step"}, {"id": 1075, "name": "entity.strider.ambient"}, {"id": 1076, "name": "entity.strider.happy"}, {"id": 1077, "name": "entity.strider.retreat"}, {"id": 1078, "name": "entity.strider.death"}, {"id": 1079, "name": "entity.strider.hurt"}, {"id": 1080, "name": "entity.strider.step"}, {"id": 1081, "name": "entity.strider.step_lava"}, {"id": 1082, "name": "entity.strider.eat"}, {"id": 1083, "name": "entity.strider.saddle"}, {"id": 1084, "name": "entity.slime.death_small"}, {"id": 1085, "name": "entity.slime.hurt_small"}, {"id": 1086, "name": "entity.slime.jump_small"}, {"id": 1087, "name": "entity.slime.squish_small"}, {"id": 1088, "name": "block.smithing_table.use"}, {"id": 1089, "name": "block.smoker.smoke"}, {"id": 1090, "name": "entity.snowball.throw"}, {"id": 1091, "name": "block.snow.break"}, {"id": 1092, "name": "block.snow.fall"}, {"id": 1093, "name": "entity.snow_golem.ambient"}, {"id": 1094, "name": "entity.snow_golem.death"}, {"id": 1095, "name": "entity.snow_golem.hurt"}, {"id": 1096, "name": "entity.snow_golem.shoot"}, {"id": 1097, "name": "entity.snow_golem.shear"}, {"id": 1098, "name": "block.snow.hit"}, {"id": 1099, "name": "block.snow.place"}, {"id": 1100, "name": "block.snow.step"}, {"id": 1101, "name": "entity.spider.ambient"}, {"id": 1102, "name": "entity.spider.death"}, {"id": 1103, "name": "entity.spider.hurt"}, {"id": 1104, "name": "entity.spider.step"}, {"id": 1105, "name": "entity.splash_potion.break"}, {"id": 1106, "name": "entity.splash_potion.throw"}, {"id": 1107, "name": "item.spyglass.use"}, {"id": 1108, "name": "item.spyglass.stop_using"}, {"id": 1109, "name": "entity.squid.ambient"}, {"id": 1110, "name": "entity.squid.death"}, {"id": 1111, "name": "entity.squid.hurt"}, {"id": 1112, "name": "entity.squid.squirt"}, {"id": 1113, "name": "block.stone.break"}, {"id": 1114, "name": "block.stone_button.click_off"}, {"id": 1115, "name": "block.stone_button.click_on"}, {"id": 1116, "name": "block.stone.fall"}, {"id": 1117, "name": "block.stone.hit"}, {"id": 1118, "name": "block.stone.place"}, {"id": 1119, "name": "block.stone_pressure_plate.click_off"}, {"id": 1120, "name": "block.stone_pressure_plate.click_on"}, {"id": 1121, "name": "block.stone.step"}, {"id": 1122, "name": "entity.stray.ambient"}, {"id": 1123, "name": "entity.stray.death"}, {"id": 1124, "name": "entity.stray.hurt"}, {"id": 1125, "name": "entity.stray.step"}, {"id": 1126, "name": "block.sweet_berry_bush.break"}, {"id": 1127, "name": "block.sweet_berry_bush.place"}, {"id": 1128, "name": "block.sweet_berry_bush.pick_berries"}, {"id": 1129, "name": "entity.tadpole.death"}, {"id": 1130, "name": "entity.tadpole.flop"}, {"id": 1131, "name": "entity.tadpole.grow_up"}, {"id": 1132, "name": "entity.tadpole.hurt"}, {"id": 1133, "name": "enchant.thorns.hit"}, {"id": 1134, "name": "entity.tnt.primed"}, {"id": 1135, "name": "item.totem.use"}, {"id": 1136, "name": "item.trident.hit"}, {"id": 1137, "name": "item.trident.hit_ground"}, {"id": 1138, "name": "item.trident.return"}, {"id": 1139, "name": "item.trident.riptide_1"}, {"id": 1140, "name": "item.trident.riptide_2"}, {"id": 1141, "name": "item.trident.riptide_3"}, {"id": 1142, "name": "item.trident.throw"}, {"id": 1143, "name": "item.trident.thunder"}, {"id": 1144, "name": "block.tripwire.attach"}, {"id": 1145, "name": "block.tripwire.click_off"}, {"id": 1146, "name": "block.tripwire.click_on"}, {"id": 1147, "name": "block.tripwire.detach"}, {"id": 1148, "name": "entity.tropical_fish.ambient"}, {"id": 1149, "name": "entity.tropical_fish.death"}, {"id": 1150, "name": "entity.tropical_fish.flop"}, {"id": 1151, "name": "entity.tropical_fish.hurt"}, {"id": 1152, "name": "block.tuff.break"}, {"id": 1153, "name": "block.tuff.step"}, {"id": 1154, "name": "block.tuff.place"}, {"id": 1155, "name": "block.tuff.hit"}, {"id": 1156, "name": "block.tuff.fall"}, {"id": 1157, "name": "entity.turtle.ambient_land"}, {"id": 1158, "name": "entity.turtle.death"}, {"id": 1159, "name": "entity.turtle.death_baby"}, {"id": 1160, "name": "entity.turtle.egg_break"}, {"id": 1161, "name": "entity.turtle.egg_crack"}, {"id": 1162, "name": "entity.turtle.egg_hatch"}, {"id": 1163, "name": "entity.turtle.hurt"}, {"id": 1164, "name": "entity.turtle.hurt_baby"}, {"id": 1165, "name": "entity.turtle.lay_egg"}, {"id": 1166, "name": "entity.turtle.shamble"}, {"id": 1167, "name": "entity.turtle.shamble_baby"}, {"id": 1168, "name": "entity.turtle.swim"}, {"id": 1169, "name": "ui.button.click"}, {"id": 1170, "name": "ui.loom.select_pattern"}, {"id": 1171, "name": "ui.loom.take_result"}, {"id": 1172, "name": "ui.cartography_table.take_result"}, {"id": 1173, "name": "ui.stonecutter.take_result"}, {"id": 1174, "name": "ui.stonecutter.select_recipe"}, {"id": 1175, "name": "ui.toast.challenge_complete"}, {"id": 1176, "name": "ui.toast.in"}, {"id": 1177, "name": "ui.toast.out"}, {"id": 1178, "name": "entity.vex.ambient"}, {"id": 1179, "name": "entity.vex.charge"}, {"id": 1180, "name": "entity.vex.death"}, {"id": 1181, "name": "entity.vex.hurt"}, {"id": 1182, "name": "entity.villager.ambient"}, {"id": 1183, "name": "entity.villager.celebrate"}, {"id": 1184, "name": "entity.villager.death"}, {"id": 1185, "name": "entity.villager.hurt"}, {"id": 1186, "name": "entity.villager.no"}, {"id": 1187, "name": "entity.villager.trade"}, {"id": 1188, "name": "entity.villager.yes"}, {"id": 1189, "name": "entity.villager.work_armorer"}, {"id": 1190, "name": "entity.villager.work_butcher"}, {"id": 1191, "name": "entity.villager.work_cartographer"}, {"id": 1192, "name": "entity.villager.work_cleric"}, {"id": 1193, "name": "entity.villager.work_farmer"}, {"id": 1194, "name": "entity.villager.work_fisherman"}, {"id": 1195, "name": "entity.villager.work_fletcher"}, {"id": 1196, "name": "entity.villager.work_leatherworker"}, {"id": 1197, "name": "entity.villager.work_librarian"}, {"id": 1198, "name": "entity.villager.work_mason"}, {"id": 1199, "name": "entity.villager.work_shepherd"}, {"id": 1200, "name": "entity.villager.work_toolsmith"}, {"id": 1201, "name": "entity.villager.work_weaponsmith"}, {"id": 1202, "name": "entity.vindicator.ambient"}, {"id": 1203, "name": "entity.vindicator.celebrate"}, {"id": 1204, "name": "entity.vindicator.death"}, {"id": 1205, "name": "entity.vindicator.hurt"}, {"id": 1206, "name": "block.vine.break"}, {"id": 1207, "name": "block.vine.fall"}, {"id": 1208, "name": "block.vine.hit"}, {"id": 1209, "name": "block.vine.place"}, {"id": 1210, "name": "block.vine.step"}, {"id": 1211, "name": "block.lily_pad.place"}, {"id": 1212, "name": "entity.wandering_trader.ambient"}, {"id": 1213, "name": "entity.wandering_trader.death"}, {"id": 1214, "name": "entity.wandering_trader.disappeared"}, {"id": 1215, "name": "entity.wandering_trader.drink_milk"}, {"id": 1216, "name": "entity.wandering_trader.drink_potion"}, {"id": 1217, "name": "entity.wandering_trader.hurt"}, {"id": 1218, "name": "entity.wandering_trader.no"}, {"id": 1219, "name": "entity.wandering_trader.reappeared"}, {"id": 1220, "name": "entity.wandering_trader.trade"}, {"id": 1221, "name": "entity.wandering_trader.yes"}, {"id": 1222, "name": "entity.warden.agitated"}, {"id": 1223, "name": "entity.warden.ambient"}, {"id": 1224, "name": "entity.warden.angry"}, {"id": 1225, "name": "entity.warden.attack_impact"}, {"id": 1226, "name": "entity.warden.death"}, {"id": 1227, "name": "entity.warden.dig"}, {"id": 1228, "name": "entity.warden.emerge"}, {"id": 1229, "name": "entity.warden.heartbeat"}, {"id": 1230, "name": "entity.warden.hurt"}, {"id": 1231, "name": "entity.warden.listening"}, {"id": 1232, "name": "entity.warden.listening_angry"}, {"id": 1233, "name": "entity.warden.nearby_close"}, {"id": 1234, "name": "entity.warden.nearby_closer"}, {"id": 1235, "name": "entity.warden.nearby_closest"}, {"id": 1236, "name": "entity.warden.roar"}, {"id": 1237, "name": "entity.warden.sniff"}, {"id": 1238, "name": "entity.warden.sonic_boom"}, {"id": 1239, "name": "entity.warden.sonic_charge"}, {"id": 1240, "name": "entity.warden.step"}, {"id": 1241, "name": "entity.warden.tendril_clicks"}, {"id": 1242, "name": "block.water.ambient"}, {"id": 1243, "name": "weather.rain"}, {"id": 1244, "name": "weather.rain.above"}, {"id": 1245, "name": "block.wet_grass.break"}, {"id": 1246, "name": "block.wet_grass.fall"}, {"id": 1247, "name": "block.wet_grass.hit"}, {"id": 1248, "name": "block.wet_grass.place"}, {"id": 1249, "name": "block.wet_grass.step"}, {"id": 1250, "name": "entity.witch.ambient"}, {"id": 1251, "name": "entity.witch.celebrate"}, {"id": 1252, "name": "entity.witch.death"}, {"id": 1253, "name": "entity.witch.drink"}, {"id": 1254, "name": "entity.witch.hurt"}, {"id": 1255, "name": "entity.witch.throw"}, {"id": 1256, "name": "entity.wither.ambient"}, {"id": 1257, "name": "entity.wither.break_block"}, {"id": 1258, "name": "entity.wither.death"}, {"id": 1259, "name": "entity.wither.hurt"}, {"id": 1260, "name": "entity.wither.shoot"}, {"id": 1261, "name": "entity.wither_skeleton.ambient"}, {"id": 1262, "name": "entity.wither_skeleton.death"}, {"id": 1263, "name": "entity.wither_skeleton.hurt"}, {"id": 1264, "name": "entity.wither_skeleton.step"}, {"id": 1265, "name": "entity.wither.spawn"}, {"id": 1266, "name": "entity.wolf.ambient"}, {"id": 1267, "name": "entity.wolf.death"}, {"id": 1268, "name": "entity.wolf.growl"}, {"id": 1269, "name": "entity.wolf.howl"}, {"id": 1270, "name": "entity.wolf.hurt"}, {"id": 1271, "name": "entity.wolf.pant"}, {"id": 1272, "name": "entity.wolf.shake"}, {"id": 1273, "name": "entity.wolf.step"}, {"id": 1274, "name": "entity.wolf.whine"}, {"id": 1275, "name": "block.wooden_door.close"}, {"id": 1276, "name": "block.wooden_door.open"}, {"id": 1277, "name": "block.wooden_trapdoor.close"}, {"id": 1278, "name": "block.wooden_trapdoor.open"}, {"id": 1279, "name": "block.wood.break"}, {"id": 1280, "name": "block.wooden_button.click_off"}, {"id": 1281, "name": "block.wooden_button.click_on"}, {"id": 1282, "name": "block.wood.fall"}, {"id": 1283, "name": "block.wood.hit"}, {"id": 1284, "name": "block.wood.place"}, {"id": 1285, "name": "block.wooden_pressure_plate.click_off"}, {"id": 1286, "name": "block.wooden_pressure_plate.click_on"}, {"id": 1287, "name": "block.wood.step"}, {"id": 1288, "name": "block.wool.break"}, {"id": 1289, "name": "block.wool.fall"}, {"id": 1290, "name": "block.wool.hit"}, {"id": 1291, "name": "block.wool.place"}, {"id": 1292, "name": "block.wool.step"}, {"id": 1293, "name": "entity.zoglin.ambient"}, {"id": 1294, "name": "entity.zoglin.angry"}, {"id": 1295, "name": "entity.zoglin.attack"}, {"id": 1296, "name": "entity.zoglin.death"}, {"id": 1297, "name": "entity.zoglin.hurt"}, {"id": 1298, "name": "entity.zoglin.step"}, {"id": 1299, "name": "entity.zombie.ambient"}, {"id": 1300, "name": "entity.zombie.attack_wooden_door"}, {"id": 1301, "name": "entity.zombie.attack_iron_door"}, {"id": 1302, "name": "entity.zombie.break_wooden_door"}, {"id": 1303, "name": "entity.zombie.converted_to_drowned"}, {"id": 1304, "name": "entity.zombie.death"}, {"id": 1305, "name": "entity.zombie.destroy_egg"}, {"id": 1306, "name": "entity.zombie_horse.ambient"}, {"id": 1307, "name": "entity.zombie_horse.death"}, {"id": 1308, "name": "entity.zombie_horse.hurt"}, {"id": 1309, "name": "entity.zombie.hurt"}, {"id": 1310, "name": "entity.zombie.infect"}, {"id": 1311, "name": "entity.zombified_piglin.ambient"}, {"id": 1312, "name": "entity.zombified_piglin.angry"}, {"id": 1313, "name": "entity.zombified_piglin.death"}, {"id": 1314, "name": "entity.zombified_piglin.hurt"}, {"id": 1315, "name": "entity.zombie.step"}, {"id": 1316, "name": "entity.zombie_villager.ambient"}, {"id": 1317, "name": "entity.zombie_villager.converted"}, {"id": 1318, "name": "entity.zombie_villager.cure"}, {"id": 1319, "name": "entity.zombie_villager.death"}, {"id": 1320, "name": "entity.zombie_villager.hurt"}, {"id": 1321, "name": "entity.zombie_villager.step"}]