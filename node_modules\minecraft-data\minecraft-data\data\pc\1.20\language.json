{"accessibility.onboarding.screen.narrator": "Press enter to enable the narrator", "accessibility.onboarding.screen.title": "Welcome to Minecraft!\n\nWould you like to enable the <PERSON>rrator or visit the Accessibility Settings?", "addServer.add": "Done", "addServer.enterIp": "Server Address", "addServer.enterName": "Server Name", "addServer.hideAddress": "Hide Address", "addServer.resourcePack": "Server Resource Packs", "addServer.resourcePack.disabled": "Disabled", "addServer.resourcePack.enabled": "Enabled", "addServer.resourcePack.prompt": "Prompt", "addServer.title": "Edit Server Info", "advancement.advancementNotFound": "Unknown advancement: %s", "advancements.adventure.adventuring_time.description": "Discover every biome", "advancements.adventure.adventuring_time.title": "Adventuring Time", "advancements.adventure.arbalistic.description": "Kill five unique mobs with one crossbow shot", "advancements.adventure.arbalistic.title": "Arbalistic", "advancements.adventure.avoid_vibration.description": "Sneak near a Sculk Sensor or Warden to prevent it from detecting you", "advancements.adventure.avoid_vibration.title": "Sneak 100", "advancements.adventure.bullseye.description": "Hit the bullseye of a Target block from at least 30 meters away", "advancements.adventure.bullseye.title": "Bullseye", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Make a Decorated Pot out of 4 Pottery Sherds", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Careful Restoration", "advancements.adventure.fall_from_world_height.description": "Free fall from the top of the world (build limit) to the bottom of the world and survive", "advancements.adventure.fall_from_world_height.title": "Caves & Cliffs", "advancements.adventure.hero_of_the_village.description": "Successfully defend a village from a raid", "advancements.adventure.hero_of_the_village.title": "Hero of the Village", "advancements.adventure.honey_block_slide.description": "Jump into a Honey Block to break your fall", "advancements.adventure.honey_block_slide.title": "Sticky Situation", "advancements.adventure.kill_a_mob.description": "Kill any hostile monster", "advancements.adventure.kill_a_mob.title": "Monster Hunter", "advancements.adventure.kill_all_mobs.description": "Kill one of every hostile monster", "advancements.adventure.kill_all_mobs.title": "Monsters Hunted", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Kill a mob near a Sculk Catalyst", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "It Spreads", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Protect a Villager from an undesired shock without starting a fire", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Surge Protector", "advancements.adventure.ol_betsy.description": "Shoot a Crossbow", "advancements.adventure.ol_betsy.title": "<PERSON><PERSON>' <PERSON>", "advancements.adventure.play_jukebox_in_meadows.description": "Make the Meadows come alive with the sound of music from a Jukebox", "advancements.adventure.play_jukebox_in_meadows.title": "Sound of Music", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Read the power signal of a Chiseled Bookshelf using a Comparator", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "The Power of Books", "advancements.adventure.root.description": "Adventure, exploration and combat", "advancements.adventure.root.title": "Adventure", "advancements.adventure.salvage_sherd.description": "Brush a Suspicious block to obtain a Pottery Sherd", "advancements.adventure.salvage_sherd.title": "Respecting the Remnants", "advancements.adventure.shoot_arrow.description": "Shoot something with an Arrow", "advancements.adventure.shoot_arrow.title": "Take Aim", "advancements.adventure.sleep_in_bed.description": "Sleep in a Bed to change your respawn point", "advancements.adventure.sleep_in_bed.title": "Sweet Dreams", "advancements.adventure.sniper_duel.description": "Kill a Skeleton from at least 50 meters away", "advancements.adventure.sniper_duel.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.spyglass_at_dragon.description": "Look at the Ender Dragon through a Spyglass", "advancements.adventure.spyglass_at_dragon.title": "Is It a Plane?", "advancements.adventure.spyglass_at_ghast.description": "Look at a Ghast through a Spyglass", "advancements.adventure.spyglass_at_ghast.title": "Is It a Balloon?", "advancements.adventure.spyglass_at_parrot.description": "Look at a Parrot through a Spyglass", "advancements.adventure.spyglass_at_parrot.title": "Is It a Bird?", "advancements.adventure.summon_iron_golem.description": "Summon an Iron Golem to help defend a village", "advancements.adventure.summon_iron_golem.title": "<PERSON><PERSON>", "advancements.adventure.throw_trident.description": "Throw a Trident at something.\nNote: Throwing away your only weapon is not a good idea.", "advancements.adventure.throw_trident.title": "A Throwaway Joke", "advancements.adventure.totem_of_undying.description": "Use a Totem of Undying to cheat death", "advancements.adventure.totem_of_undying.title": "Postmortal", "advancements.adventure.trade_at_world_height.description": "Trade with a Villager at the build height limit", "advancements.adventure.trade_at_world_height.title": "Star Trader", "advancements.adventure.trade.description": "Successfully trade with a Villager", "advancements.adventure.trade.title": "What a Deal!", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Apply these smithing templates at least once: <PERSON><PERSON>, <PERSON><PERSON>ut, R<PERSON>, Ward, Silence, Vex, Tide, Wayfinder", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Smithing with Style", "advancements.adventure.trim_with_any_armor_pattern.description": "Craft a trimmed armor at a Smithing Table", "advancements.adventure.trim_with_any_armor_pattern.title": "Crafting a New Look", "advancements.adventure.two_birds_one_arrow.description": "Kill two Phantoms with a piercing Arrow", "advancements.adventure.two_birds_one_arrow.title": "Two Birds, One Arrow", "advancements.adventure.very_very_frightening.description": "Strike a Villager with lightning", "advancements.adventure.very_very_frightening.title": "Very Very Frightening", "advancements.adventure.voluntary_exile.description": "Kill a raid captain.\nMaybe consider staying away from villages for the time being...", "advancements.adventure.voluntary_exile.title": "Voluntary Exile", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Walk on Powder Snow... without sinking in it", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Light as a Rabbit", "advancements.adventure.whos_the_pillager_now.description": "Give a Pillager a taste of their own medicine", "advancements.adventure.whos_the_pillager_now.title": "Who's the Pillager Now?", "advancements.empty": "There doesn't seem to be anything here...", "advancements.end.dragon_breath.description": "Collect Dragon's Breath in a Glass Bottle", "advancements.end.dragon_breath.title": "You Need a Mint", "advancements.end.dragon_egg.description": "Hold the Dragon Egg", "advancements.end.dragon_egg.title": "The Next Generation", "advancements.end.elytra.description": "Find Elytra", "advancements.end.elytra.title": "Sky's the Limit", "advancements.end.enter_end_gateway.description": "Escape the island", "advancements.end.enter_end_gateway.title": "Remote Getaway", "advancements.end.find_end_city.description": "Go on in, what could happen?", "advancements.end.find_end_city.title": "The City at the End of the Game", "advancements.end.kill_dragon.description": "Good luck", "advancements.end.kill_dragon.title": "Free the End", "advancements.end.levitate.description": "Levitate up 50 blocks from the attacks of a Shulker", "advancements.end.levitate.title": "Great View From Up Here", "advancements.end.respawn_dragon.description": "Respawn the Ender Dragon", "advancements.end.respawn_dragon.title": "The End... Again...", "advancements.end.root.description": "Or the beginning?", "advancements.end.root.title": "The End", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Have an Allay drop a Cake at a Note Block", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Birthday Song", "advancements.husbandry.allay_deliver_item_to_player.description": "Have an Allay deliver items to you", "advancements.husbandry.allay_deliver_item_to_player.title": "You've Got a Friend in Me", "advancements.husbandry.axolotl_in_a_bucket.description": "Catch an Axolotl in a Bucket", "advancements.husbandry.axolotl_in_a_bucket.title": "The Cutest Predator", "advancements.husbandry.balanced_diet.description": "Eat everything that is edible, even if it's not good for you", "advancements.husbandry.balanced_diet.title": "A Balanced Diet", "advancements.husbandry.breed_all_animals.description": "Breed all the animals!", "advancements.husbandry.breed_all_animals.title": "Two by Two", "advancements.husbandry.breed_an_animal.description": "Breed two animals together", "advancements.husbandry.breed_an_animal.title": "The Parrots and the Bats", "advancements.husbandry.complete_catalogue.description": "Tame all Cat variants!", "advancements.husbandry.complete_catalogue.title": "A Complete Catalogue", "advancements.husbandry.feed_snifflet.description": "Feed a Snifflet", "advancements.husbandry.feed_snifflet.title": "Little Sniffs", "advancements.husbandry.fishy_business.description": "Catch a fish", "advancements.husbandry.fishy_business.title": "Fishy Business", "advancements.husbandry.froglights.description": "Have all Froglights in your inventory", "advancements.husbandry.froglights.title": "With Our Powers Combined!", "advancements.husbandry.kill_axolotl_target.description": "Team up with an Axolotl and win a fight", "advancements.husbandry.kill_axolotl_target.title": "The Healing Power of Friendship!", "advancements.husbandry.leash_all_frog_variants.description": "Get each Frog variant on a Lead", "advancements.husbandry.leash_all_frog_variants.title": "When the Squad Hops into Town", "advancements.husbandry.make_a_sign_glow.description": "Make the text of any kind of sign glow", "advancements.husbandry.make_a_sign_glow.title": "Glow and Behold!", "advancements.husbandry.netherite_hoe.description": "Use a Netherite Ingot to upgrade a Hoe, and then reevaluate your life choices", "advancements.husbandry.netherite_hoe.title": "Serious Dedication", "advancements.husbandry.obtain_sniffer_egg.description": "Obtain a Sniffer Egg", "advancements.husbandry.obtain_sniffer_egg.title": "Smells Interesting", "advancements.husbandry.plant_any_sniffer_seed.description": "Plant any Sniffer seed", "advancements.husbandry.plant_any_sniffer_seed.title": "Planting the Past", "advancements.husbandry.plant_seed.description": "Plant a seed and watch it grow", "advancements.husbandry.plant_seed.title": "A Seedy Place", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Get in a Boat and float with a Goat", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Whatever Floats Your Goat!", "advancements.husbandry.root.description": "The world is full of friends and food", "advancements.husbandry.root.title": "Husbandry", "advancements.husbandry.safely_harvest_honey.description": "Use a Campfire to collect Honey from a Beehive using a Glass Bottle without aggravating the Bees", "advancements.husbandry.safely_harvest_honey.title": "Bee Our Guest", "advancements.husbandry.silk_touch_nest.description": "Move a Bee Nest, with 3 Bees inside, using Silk Touch", "advancements.husbandry.silk_touch_nest.title": "Total Beelocation", "advancements.husbandry.tactical_fishing.description": "Catch a Fish... without a Fishing Rod!", "advancements.husbandry.tactical_fishing.title": "Tactical Fishing", "advancements.husbandry.tadpole_in_a_bucket.description": "Catch a Tadpole in a Bucket", "advancements.husbandry.tadpole_in_a_bucket.title": "Bukkit Bukkit", "advancements.husbandry.tame_an_animal.description": "Tame an animal", "advancements.husbandry.tame_an_animal.title": "Best Friends Forever", "advancements.husbandry.wax_off.description": "Scrape Wax off of a Copper block!", "advancements.husbandry.wax_off.title": "Wax Off", "advancements.husbandry.wax_on.description": "Apply Honeycomb to a Copper block!", "advancements.husbandry.wax_on.title": "Wax On", "advancements.nether.all_effects.description": "Have every effect applied at the same time", "advancements.nether.all_effects.title": "How Did We Get Here?", "advancements.nether.all_potions.description": "Have every potion effect applied at the same time", "advancements.nether.all_potions.title": "A Furious Cocktail", "advancements.nether.brew_potion.description": "Brew a Potion", "advancements.nether.brew_potion.title": "Local Brewery", "advancements.nether.charge_respawn_anchor.description": "Charge a Respawn Anchor to the maximum", "advancements.nether.charge_respawn_anchor.title": "Not Quite \"Nine\" Lives", "advancements.nether.create_beacon.description": "Construct and place a Beacon", "advancements.nether.create_beacon.title": "Bring Home the Beacon", "advancements.nether.create_full_beacon.description": "Bring a Beacon to full power", "advancements.nether.create_full_beacon.title": "Beaconator", "advancements.nether.distract_piglin.description": "Distract <PERSON>lins with gold", "advancements.nether.distract_piglin.title": "Oh Shiny", "advancements.nether.explore_nether.description": "Explore all Nether biomes", "advancements.nether.explore_nether.title": "Hot Tourist Destinations", "advancements.nether.fast_travel.description": "Use the Nether to travel 7 km in the Overworld", "advancements.nether.fast_travel.title": "Subspace Bubble", "advancements.nether.find_bastion.description": "Enter a Bastion Remnant", "advancements.nether.find_bastion.title": "Those Were the Days", "advancements.nether.find_fortress.description": "Break your way into a Nether Fortress", "advancements.nether.find_fortress.title": "A Terrible Fortress", "advancements.nether.get_wither_skull.description": "Obtain a <PERSON>er <PERSON>'s skull", "advancements.nether.get_wither_skull.title": "Spooky Scary Skeleton", "advancements.nether.loot_bastion.description": "Loot a Chest in a Bastion Remnant", "advancements.nether.loot_bastion.title": "War Pigs", "advancements.nether.netherite_armor.description": "Get a full suit of Netherite armor", "advancements.nether.netherite_armor.title": "Cover Me in Debris", "advancements.nether.obtain_ancient_debris.description": "Obtain Ancient Debris", "advancements.nether.obtain_ancient_debris.title": "Hidden in the Depths", "advancements.nether.obtain_blaze_rod.description": "Relieve a Blaze of its rod", "advancements.nether.obtain_blaze_rod.title": "Into Fire", "advancements.nether.obtain_crying_obsidian.description": "Obtain Crying Obsidian", "advancements.nether.obtain_crying_obsidian.title": "Who is Cutting Onions?", "advancements.nether.return_to_sender.description": "<PERSON><PERSON>y a <PERSON><PERSON><PERSON> with a fireball", "advancements.nether.return_to_sender.title": "Return to Sender", "advancements.nether.ride_strider_in_overworld_lava.description": "Take a Strider for a loooong ride on a lava lake in the Overworld", "advancements.nether.ride_strider_in_overworld_lava.title": "Feels Like Home", "advancements.nether.ride_strider.description": "Ride a Strider with a Warped Fungus on a Stick", "advancements.nether.ride_strider.title": "This Boat Has Legs", "advancements.nether.root.description": "Bring summer clothes", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON><PERSON> the Wither", "advancements.nether.summon_wither.title": "Withering Heights", "advancements.nether.uneasy_alliance.description": "Rescue a Ghast from the Nether, bring it safely home to the Overworld... and then kill it", "advancements.nether.uneasy_alliance.title": "Uneasy Alliance", "advancements.nether.use_lodestone.description": "Use a Compass on a Lodestone", "advancements.nether.use_lodestone.title": "Country Lode, Take Me Home", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Weaken and then cure a Zombie Villager", "advancements.story.cure_zombie_villager.title": "Zombie Doctor", "advancements.story.deflect_arrow.description": "Deflect a projectile with a Shield", "advancements.story.deflect_arrow.title": "Not Today, Thank You", "advancements.story.enchant_item.description": "Enchant an item at an Enchanting Table", "advancements.story.enchant_item.title": "Enchanter", "advancements.story.enter_the_end.description": "Enter the End Portal", "advancements.story.enter_the_end.title": "The End?", "advancements.story.enter_the_nether.description": "Build, light and enter a Nether Portal", "advancements.story.enter_the_nether.title": "We Need to Go Deeper", "advancements.story.follow_ender_eye.description": "Follow an Eye of Ender", "advancements.story.follow_ender_eye.title": "Eye Spy", "advancements.story.form_obsidian.description": "Obtain a block of Obsidian", "advancements.story.form_obsidian.title": "Ice Bucket Challenge", "advancements.story.iron_tools.description": "Upgrade your Pickaxe", "advancements.story.iron_tools.title": "Isn't It Iron Pick", "advancements.story.lava_bucket.description": "Fill a Bucket with lava", "advancements.story.lava_bucket.title": "Hot Stuff", "advancements.story.mine_diamond.description": "Acquire diamonds", "advancements.story.mine_diamond.title": "Diamonds!", "advancements.story.mine_stone.description": "Mine Stone with your new Pickaxe", "advancements.story.mine_stone.title": "Stone Age", "advancements.story.obtain_armor.description": "Protect yourself with a piece of iron armor", "advancements.story.obtain_armor.title": "Suit Up", "advancements.story.root.description": "The heart and story of the game", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Diamond armor saves lives", "advancements.story.shiny_gear.title": "Cover Me with Diamonds", "advancements.story.smelt_iron.description": "Smelt an Iron Ingot", "advancements.story.smelt_iron.title": "Acquire Hardware", "advancements.story.upgrade_tools.description": "Construct a better Pickaxe", "advancements.story.upgrade_tools.title": "Getting an Upgrade", "advancements.toast.challenge": "Challenge Complete!", "advancements.toast.goal": "Goal Reached!", "advancements.toast.task": "Advancement Made!", "advMode.allEntities": "Use \"@e\" to target all entities", "advMode.allPlayers": "Use \"@a\" to target all players", "advMode.command": "Console Command", "advMode.mode": "Mode", "advMode.mode.auto": "Repeat", "advMode.mode.autoexec.bat": "Always Active", "advMode.mode.conditional": "Conditional", "advMode.mode.redstone": "Impulse", "advMode.mode.redstoneTriggered": "Needs <PERSON><PERSON>", "advMode.mode.sequence": "Chain", "advMode.mode.unconditional": "Unconditional", "advMode.nearestPlayer": "Use \"@p\" to target nearest player", "advMode.notAllowed": "Must be an opped player in creative mode", "advMode.notEnabled": "Command blocks are not enabled on this server", "advMode.previousOutput": "Previous Output", "advMode.randomPlayer": "Use \"@r\" to target random player", "advMode.self": "Use \"@s\" to target the executing entity", "advMode.setCommand": "Set Console Command for Block", "advMode.setCommand.success": "Command set: %s", "advMode.trackOutput": "Track output", "advMode.triggering": "Triggering", "advMode.type": "Type", "argument.anchor.invalid": "Invalid entity anchor position %s", "argument.angle.incomplete": "Incomplete (expected 1 angle)", "argument.angle.invalid": "Invalid angle", "argument.block.id.invalid": "Unknown block type '%s'", "argument.block.property.duplicate": "Property '%s' can only be set once for block %s", "argument.block.property.invalid": "Block %s does not accept '%s' for %s property", "argument.block.property.novalue": "Expected value for property '%s' on block %s", "argument.block.property.unclosed": "Expected closing ] for block state properties", "argument.block.property.unknown": "Block %s does not have property '%s'", "argument.block.tag.disallowed": "Tags aren't allowed here, only actual blocks", "argument.color.invalid": "Unknown color '%s'", "argument.component.invalid": "Invalid chat component: %s", "argument.criteria.invalid": "Unknown criterion '%s'", "argument.dimension.invalid": "Unknown dimension '%s'", "argument.double.big": "Double must not be more than %s, found %s", "argument.double.low": "Double must not be less than %s, found %s", "argument.entity.invalid": "Invalid name or UUID", "argument.entity.notfound.entity": "No entity was found", "argument.entity.notfound.player": "No player was found", "argument.entity.options.advancements.description": "Players with advancements", "argument.entity.options.distance.description": "Distance to entity", "argument.entity.options.distance.negative": "Distance cannot be negative", "argument.entity.options.dx.description": "Entities between x and x + dx", "argument.entity.options.dy.description": "Entities between y and y + dy", "argument.entity.options.dz.description": "Entities between z and z + dz", "argument.entity.options.gamemode.description": "Players with game mode", "argument.entity.options.inapplicable": "Option '%s' isn't applicable here", "argument.entity.options.level.description": "Experience level", "argument.entity.options.level.negative": "Level shouldn't be negative", "argument.entity.options.limit.description": "Maximum number of entities to return", "argument.entity.options.limit.toosmall": "Limit must be at least 1", "argument.entity.options.mode.invalid": "Invalid or unknown game mode '%s'", "argument.entity.options.name.description": "Entity name", "argument.entity.options.nbt.description": "Entities with NBT", "argument.entity.options.predicate.description": "Custom predicate", "argument.entity.options.scores.description": "Entities with scores", "argument.entity.options.sort.description": "Sort the entities", "argument.entity.options.sort.irreversible": "Invalid or unknown sort type '%s'", "argument.entity.options.tag.description": "Entities with tag", "argument.entity.options.team.description": "Entities on team", "argument.entity.options.type.description": "Entities of type", "argument.entity.options.type.invalid": "Invalid or unknown entity type '%s'", "argument.entity.options.unknown": "Unknown option '%s'", "argument.entity.options.unterminated": "Expected end of options", "argument.entity.options.valueless": "Expected value for option '%s'", "argument.entity.options.x_rotation.description": "<PERSON><PERSON><PERSON>'s x rotation", "argument.entity.options.x.description": "x position", "argument.entity.options.y_rotation.description": "<PERSON><PERSON><PERSON>'s y rotation", "argument.entity.options.y.description": "y position", "argument.entity.options.z.description": "z position", "argument.entity.selector.allEntities": "All entities", "argument.entity.selector.allPlayers": "All players", "argument.entity.selector.missing": "Missing selector type", "argument.entity.selector.nearestPlayer": "Nearest player", "argument.entity.selector.not_allowed": "Selector not allowed", "argument.entity.selector.randomPlayer": "Random player", "argument.entity.selector.self": "Current entity", "argument.entity.selector.unknown": "Unknown selector type '%s'", "argument.entity.toomany": "Only one entity is allowed, but the provided selector allows more than one", "argument.enum.invalid": "Invalid value \"%s\"", "argument.float.big": "Float must not be more than %s, found %s", "argument.float.low": "Float must not be less than %s, found %s", "argument.gamemode.invalid": "Unknown game mode: %s", "argument.id.invalid": "Invalid ID", "argument.id.unknown": "Unknown ID: %s", "argument.integer.big": "Integer must not be more than %s, found %s", "argument.integer.low": "Integer must not be less than %s, found %s", "argument.item.id.invalid": "Unknown item '%s'", "argument.item.tag.disallowed": "Tags aren't allowed here, only actual items", "argument.literal.incorrect": "Expected literal %s", "argument.long.big": "Long must not be more than %s, found %s", "argument.long.low": "Long must not be less than %s, found %s", "argument.nbt.array.invalid": "Invalid array type '%s'", "argument.nbt.array.mixed": "Can't insert %s into %s", "argument.nbt.expected.key": "Expected key", "argument.nbt.expected.value": "Expected value", "argument.nbt.list.mixed": "Can't insert %s into list of %s", "argument.nbt.trailing": "Unexpected trailing data", "argument.player.entities": "Only players may be affected by this command, but the provided selector includes entities", "argument.player.toomany": "Only one player is allowed, but the provided selector allows more than one", "argument.player.unknown": "That player does not exist", "argument.pos.missing.double": "Expected a coordinate", "argument.pos.missing.int": "Expected a block position", "argument.pos.mixed": "Cannot mix world & local coordinates (everything must either use ^ or not)", "argument.pos.outofbounds": "That position is outside the allowed boundaries.", "argument.pos.outofworld": "That position is out of this world!", "argument.pos.unloaded": "That position is not loaded", "argument.pos2d.incomplete": "Incomplete (expected 2 coordinates)", "argument.pos3d.incomplete": "Incomplete (expected 3 coordinates)", "argument.range.empty": "Expected value or range of values", "argument.range.ints": "Only whole numbers allowed, not decimals", "argument.range.swapped": "Min cannot be bigger than max", "argument.resource_tag.invalid_type": "Tag '%s' has wrong type '%s' (expected '%s')", "argument.resource_tag.not_found": "Can't find tag '%s' of type '%s'", "argument.resource.invalid_type": "Element '%s' has wrong type '%s' (expected '%s')", "argument.resource.not_found": "Can't find element '%s' of type '%s'", "argument.rotation.incomplete": "Incomplete (expected 2 coordinates)", "argument.scoreboardDisplaySlot.invalid": "Unknown display slot '%s'", "argument.scoreHolder.empty": "No relevant score holders could be found", "argument.time.invalid_tick_count": "Tick count must be non-negative", "argument.time.invalid_unit": "Invalid unit", "argument.time.tick_count_too_low": "Tick count must not be less than %s, found %s", "argument.uuid.invalid": "Invalid UUID", "arguments.block.tag.unknown": "Unknown block tag '%s'", "arguments.function.tag.unknown": "Unknown function tag '%s'", "arguments.function.unknown": "Unknown function %s", "arguments.item.overstacked": "%s can only stack up to %s", "arguments.item.tag.unknown": "Unknown item tag '%s'", "arguments.nbtpath.node.invalid": "Invalid NBT path element", "arguments.nbtpath.nothing_found": "Found no elements matching %s", "arguments.nbtpath.too_deep": "Resulting NBT too deeply nested", "arguments.nbtpath.too_large": "Resulting NBT too large", "arguments.objective.notFound": "Unknown scoreboard objective '%s'", "arguments.objective.readonly": "Scoreboard objective '%s' is read-only", "arguments.operation.div0": "Cannot divide by zero", "arguments.operation.invalid": "Invalid operation", "arguments.swizzle.invalid": "Invalid swizzle, expected combination of 'x', 'y' and 'z'", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.generic.armor": "Armor", "attribute.name.generic.armor_toughness": "<PERSON><PERSON>", "attribute.name.generic.attack_damage": "Attack Damage", "attribute.name.generic.attack_knockback": "Attack Knockback", "attribute.name.generic.attack_speed": "Attack Speed", "attribute.name.generic.flying_speed": "Flying Speed", "attribute.name.generic.follow_range": "<PERSON>b <PERSON> Range", "attribute.name.generic.knockback_resistance": "Knockback Resistance", "attribute.name.generic.luck": "Luck", "attribute.name.generic.max_health": "Max Health", "attribute.name.generic.movement_speed": "Speed", "attribute.name.horse.jump_strength": "Horse Jump Strength", "attribute.name.zombie.spawn_reinforcements": "Zombie Reinforcements", "biome.minecraft.badlands": "Badlands", "biome.minecraft.bamboo_jungle": "Bamboo Jungle", "biome.minecraft.basalt_deltas": "Basalt Deltas", "biome.minecraft.beach": "Beach", "biome.minecraft.birch_forest": "Birch Forest", "biome.minecraft.cherry_grove": "Cherry Grove", "biome.minecraft.cold_ocean": "Cold Ocean", "biome.minecraft.crimson_forest": "Crimson Forest", "biome.minecraft.dark_forest": "Dark Forest", "biome.minecraft.deep_cold_ocean": "Deep Cold Ocean", "biome.minecraft.deep_dark": "Deep Dark", "biome.minecraft.deep_frozen_ocean": "Deep Frozen Ocean", "biome.minecraft.deep_lukewarm_ocean": "Deep Lukewarm Ocean", "biome.minecraft.deep_ocean": "Deep Ocean", "biome.minecraft.desert": "Desert", "biome.minecraft.dripstone_caves": "Dripstone Caves", "biome.minecraft.end_barrens": "End Barrens", "biome.minecraft.end_highlands": "End Highlands", "biome.minecraft.end_midlands": "End Midlands", "biome.minecraft.eroded_badlands": "Eroded Badlands", "biome.minecraft.flower_forest": "Flower Forest", "biome.minecraft.forest": "Forest", "biome.minecraft.frozen_ocean": "Frozen Ocean", "biome.minecraft.frozen_peaks": "Frozen Peaks", "biome.minecraft.frozen_river": "Frozen River", "biome.minecraft.grove": "Grove", "biome.minecraft.ice_spikes": "Ice Spikes", "biome.minecraft.jagged_peaks": "Jagged Peaks", "biome.minecraft.jungle": "Jungle", "biome.minecraft.lukewarm_ocean": "Lukewarm Ocean", "biome.minecraft.lush_caves": "Lush Caves", "biome.minecraft.mangrove_swamp": "Mangrove Swamp", "biome.minecraft.meadow": "Meadow", "biome.minecraft.mushroom_fields": "Mushroom Fields", "biome.minecraft.nether_wastes": "Nether Wastes", "biome.minecraft.ocean": "Ocean", "biome.minecraft.old_growth_birch_forest": "Old Growth Birch Forest", "biome.minecraft.old_growth_pine_taiga": "Old Growth Pine Taiga", "biome.minecraft.old_growth_spruce_taiga": "Old Growth Spruce Taiga", "biome.minecraft.plains": "Plains", "biome.minecraft.river": "River", "biome.minecraft.savanna": "Savanna", "biome.minecraft.savanna_plateau": "Savanna Plateau", "biome.minecraft.small_end_islands": "Small End Islands", "biome.minecraft.snowy_beach": "Snowy Beach", "biome.minecraft.snowy_plains": "Snowy Plains", "biome.minecraft.snowy_slopes": "Snowy <PERSON>lopes", "biome.minecraft.snowy_taiga": "Snowy <PERSON>", "biome.minecraft.soul_sand_valley": "Soul Sand Valley", "biome.minecraft.sparse_jungle": "Sparse Jungle", "biome.minecraft.stony_peaks": "Stony Peaks", "biome.minecraft.stony_shore": "Stony Shore", "biome.minecraft.sunflower_plains": "Sunflower Plains", "biome.minecraft.swamp": "Swamp", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "The End", "biome.minecraft.the_void": "The Void", "biome.minecraft.warm_ocean": "Warm Ocean", "biome.minecraft.warped_forest": "Warped Forest", "biome.minecraft.windswept_forest": "Windswept Forest", "biome.minecraft.windswept_gravelly_hills": "Windswept Gravelly Hills", "biome.minecraft.windswept_hills": "Windswept Hills", "biome.minecraft.windswept_savanna": "Windswept Savanna", "biome.minecraft.wooded_badlands": "Wooded Badlands", "block.minecraft.acacia_button": "Acacia <PERSON>", "block.minecraft.acacia_door": "Acacia Door", "block.minecraft.acacia_fence": "Acacia Fence", "block.minecraft.acacia_fence_gate": "Acacia Fence Gate", "block.minecraft.acacia_hanging_sign": "Acacia Hanging Sign", "block.minecraft.acacia_leaves": "Acacia Leaves", "block.minecraft.acacia_log": "Acacia Log", "block.minecraft.acacia_planks": "Acacia Planks", "block.minecraft.acacia_pressure_plate": "Acacia Pressure Plate", "block.minecraft.acacia_sapling": "Acacia Sapling", "block.minecraft.acacia_sign": "Acacia Sign", "block.minecraft.acacia_slab": "Acacia <PERSON>b", "block.minecraft.acacia_stairs": "Acacia Stairs", "block.minecraft.acacia_trapdoor": "Acacia T<PERSON>door", "block.minecraft.acacia_wall_hanging_sign": "Acacia Wall Hanging Sign", "block.minecraft.acacia_wall_sign": "Acacia Wall Sign", "block.minecraft.acacia_wood": "Acacia Wood", "block.minecraft.activator_rail": "Activator Rail", "block.minecraft.air": "Air", "block.minecraft.allium": "Allium", "block.minecraft.amethyst_block": "Block of Amethyst", "block.minecraft.amethyst_cluster": "Amethyst Cluster", "block.minecraft.ancient_debris": "Ancient Debris", "block.minecraft.andesite": "Andesite", "block.minecraft.andesite_slab": "Andesite Slab", "block.minecraft.andesite_stairs": "Andesite Stairs", "block.minecraft.andesite_wall": "Andesite Wall", "block.minecraft.anvil": "An<PERSON>", "block.minecraft.attached_melon_stem": "Attached Melon Stem", "block.minecraft.attached_pumpkin_stem": "Attached P<PERSON><PERSON> Stem", "block.minecraft.azalea": "Azalea", "block.minecraft.azalea_leaves": "Azalea Leaves", "block.minecraft.azure_bluet": "Azure Bluet", "block.minecraft.bamboo": "Bamboo", "block.minecraft.bamboo_block": "Block of Bamboo", "block.minecraft.bamboo_button": "Bamboo Button", "block.minecraft.bamboo_door": "Bamboo Door", "block.minecraft.bamboo_fence": "Bamboo Fence", "block.minecraft.bamboo_fence_gate": "Bamboo Fence Gate", "block.minecraft.bamboo_hanging_sign": "Bamboo Hanging Sign", "block.minecraft.bamboo_mosaic": "Bamboo Mosaic", "block.minecraft.bamboo_mosaic_slab": "Bamboo Mosaic Slab", "block.minecraft.bamboo_mosaic_stairs": "Bamboo Mosaic Stairs", "block.minecraft.bamboo_planks": "Bamboo Planks", "block.minecraft.bamboo_pressure_plate": "Bamboo Pressure Plate", "block.minecraft.bamboo_sapling": "Bamboo Shoot", "block.minecraft.bamboo_sign": "Bamboo Sign", "block.minecraft.bamboo_slab": "Bamboo Slab", "block.minecraft.bamboo_stairs": "Bamboo Stairs", "block.minecraft.bamboo_trapdoor": "Bamboo Trapdoor", "block.minecraft.bamboo_wall_hanging_sign": "Bamboo Wall Hanging Sign", "block.minecraft.bamboo_wall_sign": "Bamboo Wall Sign", "block.minecraft.banner.base.black": "<PERSON>y Black <PERSON>", "block.minecraft.banner.base.blue": "Fully Blue Field", "block.minecraft.banner.base.brown": "<PERSON><PERSON>", "block.minecraft.banner.base.cyan": "<PERSON>y <PERSON><PERSON>", "block.minecraft.banner.base.gray": "<PERSON><PERSON>", "block.minecraft.banner.base.green": "Fully Green Field", "block.minecraft.banner.base.light_blue": "Fully Light Blue Field", "block.minecraft.banner.base.light_gray": "Fully Light Gray Field", "block.minecraft.banner.base.lime": "Fully Lime Field", "block.minecraft.banner.base.magenta": "Fully Magenta Field", "block.minecraft.banner.base.orange": "Fully Orange Field", "block.minecraft.banner.base.pink": "Fully Pink Field", "block.minecraft.banner.base.purple": "Fully <PERSON> Field", "block.minecraft.banner.base.red": "Fully Red Field", "block.minecraft.banner.base.white": "<PERSON>y White <PERSON>", "block.minecraft.banner.base.yellow": "Fully Yellow Field", "block.minecraft.banner.border.black": "Black Bordure", "block.minecraft.banner.border.blue": "Blue Bordure", "block.minecraft.banner.border.brown": "<PERSON>", "block.minecraft.banner.border.cyan": "<PERSON><PERSON>", "block.minecraft.banner.border.gray": "<PERSON>", "block.minecraft.banner.border.green": "Green Bordure", "block.minecraft.banner.border.light_blue": "Light Blue Bordure", "block.minecraft.banner.border.light_gray": "Light Gray <PERSON>", "block.minecraft.banner.border.lime": "Lime <PERSON>", "block.minecraft.banner.border.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.orange": "Orange Bordure", "block.minecraft.banner.border.pink": "Pink Bordure", "block.minecraft.banner.border.purple": "Purple Bordure", "block.minecraft.banner.border.red": "Red Bordure", "block.minecraft.banner.border.white": "White Bordure", "block.minecraft.banner.border.yellow": "Yellow Bordure", "block.minecraft.banner.bricks.black": "Black Field Masoned", "block.minecraft.banner.bricks.blue": "Blue Field Masoned", "block.minecraft.banner.bricks.brown": "<PERSON> Field Masoned", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON>", "block.minecraft.banner.bricks.gray": "<PERSON>ed", "block.minecraft.banner.bricks.green": "Green Field Masoned", "block.minecraft.banner.bricks.light_blue": "Light Blue Field Masoned", "block.minecraft.banner.bricks.light_gray": "<PERSON> <PERSON> Masoned", "block.minecraft.banner.bricks.lime": "Lime Field Masoned", "block.minecraft.banner.bricks.magenta": "Magenta Field Masoned", "block.minecraft.banner.bricks.orange": "Orange Field Masoned", "block.minecraft.banner.bricks.pink": "<PERSON> Masoned", "block.minecraft.banner.bricks.purple": "<PERSON> Masoned", "block.minecraft.banner.bricks.red": "<PERSON> Field Masoned", "block.minecraft.banner.bricks.white": "White Field Masoned", "block.minecraft.banner.bricks.yellow": "Yellow Field Masoned", "block.minecraft.banner.circle.black": "Black Roundel", "block.minecraft.banner.circle.blue": "Blue Roundel", "block.minecraft.banner.circle.brown": "<PERSON>", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON>", "block.minecraft.banner.circle.gray": "<PERSON>", "block.minecraft.banner.circle.green": "Green Roundel", "block.minecraft.banner.circle.light_blue": "Light Blue Roundel", "block.minecraft.banner.circle.light_gray": "<PERSON> <PERSON>", "block.minecraft.banner.circle.lime": "<PERSON><PERSON>", "block.minecraft.banner.circle.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.orange": "Orange Roundel", "block.minecraft.banner.circle.pink": "Pink Roundel", "block.minecraft.banner.circle.purple": "Purple Roundel", "block.minecraft.banner.circle.red": "Red Roundel", "block.minecraft.banner.circle.white": "White Roundel", "block.minecraft.banner.circle.yellow": "Yellow Roundel", "block.minecraft.banner.creeper.black": "Black Creeper Charge", "block.minecraft.banner.creeper.blue": "Blue Creeper Charge", "block.minecraft.banner.creeper.brown": "Brown Creeper Charge", "block.minecraft.banner.creeper.cyan": "<PERSON><PERSON>", "block.minecraft.banner.creeper.gray": "Gray <PERSON> Charge", "block.minecraft.banner.creeper.green": "Green Creeper Charge", "block.minecraft.banner.creeper.light_blue": "Light Blue Creeper Charge", "block.minecraft.banner.creeper.light_gray": "Light Gray Creeper Charge", "block.minecraft.banner.creeper.lime": "Lime Creeper Charge", "block.minecraft.banner.creeper.magenta": "Magenta Creeper Charge", "block.minecraft.banner.creeper.orange": "Orange Creeper Charge", "block.minecraft.banner.creeper.pink": "Pink Creeper Charge", "block.minecraft.banner.creeper.purple": "Purple Creeper Charge", "block.minecraft.banner.creeper.red": "Red Creeper Charge", "block.minecraft.banner.creeper.white": "White Creeper Charge", "block.minecraft.banner.creeper.yellow": "Yellow Creeper Charge", "block.minecraft.banner.cross.black": "Black Saltire", "block.minecraft.banner.cross.blue": "Blue Saltire", "block.minecraft.banner.cross.brown": "Brown Saltire", "block.minecraft.banner.cross.cyan": "<PERSON><PERSON>", "block.minecraft.banner.cross.gray": "Gray Saltire", "block.minecraft.banner.cross.green": "Green Saltire", "block.minecraft.banner.cross.light_blue": "Light Blue Saltire", "block.minecraft.banner.cross.light_gray": "Light Gray Saltire", "block.minecraft.banner.cross.lime": "Lime Saltire", "block.minecraft.banner.cross.magenta": "Magenta Saltire", "block.minecraft.banner.cross.orange": "Orange Saltire", "block.minecraft.banner.cross.pink": "Pink Saltire", "block.minecraft.banner.cross.purple": "Purple Saltire", "block.minecraft.banner.cross.red": "Red Saltire", "block.minecraft.banner.cross.white": "White Saltire", "block.minecraft.banner.cross.yellow": "Yellow Saltire", "block.minecraft.banner.curly_border.black": "Black Bordure Indented", "block.minecraft.banner.curly_border.blue": "Blue Bordure Indented", "block.minecraft.banner.curly_border.brown": "Brown Bordure Indented", "block.minecraft.banner.curly_border.cyan": "<PERSON><PERSON> Indented", "block.minecraft.banner.curly_border.gray": "Gray Bordure Indented", "block.minecraft.banner.curly_border.green": "Green Bordure Indented", "block.minecraft.banner.curly_border.light_blue": "Light Blue Bordure Indented", "block.minecraft.banner.curly_border.light_gray": "Light Gray Bordure Indented", "block.minecraft.banner.curly_border.lime": "Lime Bordure Indented", "block.minecraft.banner.curly_border.magenta": "Magenta Bordure Indented", "block.minecraft.banner.curly_border.orange": "Orange Bordure Indented", "block.minecraft.banner.curly_border.pink": "Pink Bordure Indented", "block.minecraft.banner.curly_border.purple": "Purple Bordure Indented", "block.minecraft.banner.curly_border.red": "Red Bordure Indented", "block.minecraft.banner.curly_border.white": "White Bordure Indented", "block.minecraft.banner.curly_border.yellow": "Yellow Bordure Indented", "block.minecraft.banner.diagonal_left.black": "Black Per Bend Sinister", "block.minecraft.banner.diagonal_left.blue": "Blue Per Bend Sinister", "block.minecraft.banner.diagonal_left.brown": "<PERSON> Sinister", "block.minecraft.banner.diagonal_left.cyan": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_left.gray": "<PERSON>ister", "block.minecraft.banner.diagonal_left.green": "Green Per Bend Sinister", "block.minecraft.banner.diagonal_left.light_blue": "Light Blue Per Bend Sinister", "block.minecraft.banner.diagonal_left.light_gray": "Light Gray Per Bend Sinister", "block.minecraft.banner.diagonal_left.lime": "Lime Per <PERSON>", "block.minecraft.banner.diagonal_left.magenta": "Ma<PERSON><PERSON> Per <PERSON>", "block.minecraft.banner.diagonal_left.orange": "Orange Per Bend Sinister", "block.minecraft.banner.diagonal_left.pink": "Pink Per Bend Sinister", "block.minecraft.banner.diagonal_left.purple": "Purple Per Bend Sinister", "block.minecraft.banner.diagonal_left.red": "Red Per Bend Sinister", "block.minecraft.banner.diagonal_left.white": "White Per Bend Sinister", "block.minecraft.banner.diagonal_left.yellow": "Yellow Per Bend Sinister", "block.minecraft.banner.diagonal_right.black": "Black Per Bend", "block.minecraft.banner.diagonal_right.blue": "Blue Per Bend", "block.minecraft.banner.diagonal_right.brown": "<PERSON>", "block.minecraft.banner.diagonal_right.cyan": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_right.gray": "<PERSON>", "block.minecraft.banner.diagonal_right.green": "Green Per Bend", "block.minecraft.banner.diagonal_right.light_blue": "Light Blue Per Bend", "block.minecraft.banner.diagonal_right.light_gray": "Light Gray Per Bend", "block.minecraft.banner.diagonal_right.lime": "Lime Per <PERSON>", "block.minecraft.banner.diagonal_right.magenta": "Magenta <PERSON>", "block.minecraft.banner.diagonal_right.orange": "Orange Per Bend", "block.minecraft.banner.diagonal_right.pink": "Pink Per Bend", "block.minecraft.banner.diagonal_right.purple": "Purple Per Bend", "block.minecraft.banner.diagonal_right.red": "Red Per Bend", "block.minecraft.banner.diagonal_right.white": "White Per Bend", "block.minecraft.banner.diagonal_right.yellow": "Yellow Per Bend", "block.minecraft.banner.diagonal_up_left.black": "Black Per Bend Inverted", "block.minecraft.banner.diagonal_up_left.blue": "Blue Per Bend Inverted", "block.minecraft.banner.diagonal_up_left.brown": "<PERSON> Per Bend Inverted", "block.minecraft.banner.diagonal_up_left.cyan": "<PERSON><PERSON> Inverted", "block.minecraft.banner.diagonal_up_left.gray": "<PERSON> Inverted", "block.minecraft.banner.diagonal_up_left.green": "Green Per Bend Inverted", "block.minecraft.banner.diagonal_up_left.light_blue": "Light Blue Per Bend Inverted", "block.minecraft.banner.diagonal_up_left.light_gray": "Light Gray Per Bend Inverted", "block.minecraft.banner.diagonal_up_left.lime": "Lime Per Bend Inverted", "block.minecraft.banner.diagonal_up_left.magenta": "Magenta Per Bend Inverted", "block.minecraft.banner.diagonal_up_left.orange": "Orange Per Bend Inverted", "block.minecraft.banner.diagonal_up_left.pink": "Pink Per Bend Inverted", "block.minecraft.banner.diagonal_up_left.purple": "Purple Per Bend Inverted", "block.minecraft.banner.diagonal_up_left.red": "Red Per Bend Inverted", "block.minecraft.banner.diagonal_up_left.white": "White Per Bend Inverted", "block.minecraft.banner.diagonal_up_left.yellow": "Yellow Per Bend Inverted", "block.minecraft.banner.diagonal_up_right.black": "Black Per Bend Sinister Inverted", "block.minecraft.banner.diagonal_up_right.blue": "Blue Per Bend Sinister Inverted", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON> Per Bend Sinister Inverted", "block.minecraft.banner.diagonal_up_right.cyan": "<PERSON><PERSON>ister Inverted", "block.minecraft.banner.diagonal_up_right.gray": "<PERSON> Per Bend Sinister Inverted", "block.minecraft.banner.diagonal_up_right.green": "Green Per Bend Sinister Inverted", "block.minecraft.banner.diagonal_up_right.light_blue": "Light Blue Per Bend Sinister Inverted", "block.minecraft.banner.diagonal_up_right.light_gray": "Light Gray Per Bend Sinister Inverted", "block.minecraft.banner.diagonal_up_right.lime": "Lime Per Bend Sinister Inverted", "block.minecraft.banner.diagonal_up_right.magenta": "Magenta Per Bend Sinister Inverted", "block.minecraft.banner.diagonal_up_right.orange": "Orange Per Bend Sinister Inverted", "block.minecraft.banner.diagonal_up_right.pink": "Pink Per Bend Sinister Inverted", "block.minecraft.banner.diagonal_up_right.purple": "Purple Per Bend Sinister Inverted", "block.minecraft.banner.diagonal_up_right.red": "Red Per Bend Sinister Inverted", "block.minecraft.banner.diagonal_up_right.white": "White Per Bend Sinister Inverted", "block.minecraft.banner.diagonal_up_right.yellow": "Yellow Per Bend Sinister Inverted", "block.minecraft.banner.flower.black": "Black Flower Charge", "block.minecraft.banner.flower.blue": "Blue Flower Charge", "block.minecraft.banner.flower.brown": "Brown Flower Charge", "block.minecraft.banner.flower.cyan": "<PERSON><PERSON>", "block.minecraft.banner.flower.gray": "Gray Flower Charge", "block.minecraft.banner.flower.green": "Green Flower Charge", "block.minecraft.banner.flower.light_blue": "Light Blue Flower Charge", "block.minecraft.banner.flower.light_gray": "Light Gray Flower Charge", "block.minecraft.banner.flower.lime": "Lime Flower Charge", "block.minecraft.banner.flower.magenta": "Magenta Flower Charge", "block.minecraft.banner.flower.orange": "Orange Flower Charge", "block.minecraft.banner.flower.pink": "Pink Flower Charge", "block.minecraft.banner.flower.purple": "Purple Flower Charge", "block.minecraft.banner.flower.red": "Red Flower Charge", "block.minecraft.banner.flower.white": "White Flower Charge", "block.minecraft.banner.flower.yellow": "Yellow Flower Charge", "block.minecraft.banner.globe.black": "Black Globe", "block.minecraft.banner.globe.blue": "Blue Globe", "block.minecraft.banner.globe.brown": "Brown Globe", "block.minecraft.banner.globe.cyan": "<PERSON><PERSON>", "block.minecraft.banner.globe.gray": "Gray Globe", "block.minecraft.banner.globe.green": "Green Globe", "block.minecraft.banner.globe.light_blue": "Light Blue Globe", "block.minecraft.banner.globe.light_gray": "Light Gray Globe", "block.minecraft.banner.globe.lime": "Lime Globe", "block.minecraft.banner.globe.magenta": "Magenta Globe", "block.minecraft.banner.globe.orange": "Orange Globe", "block.minecraft.banner.globe.pink": "Pink Globe", "block.minecraft.banner.globe.purple": "Purple Globe", "block.minecraft.banner.globe.red": "Red Globe", "block.minecraft.banner.globe.white": "White Globe", "block.minecraft.banner.globe.yellow": "Yellow Globe", "block.minecraft.banner.gradient_up.black": "Black Base Gradient", "block.minecraft.banner.gradient_up.blue": "Blue Base Gradient", "block.minecraft.banner.gradient_up.brown": "Brown Base Gradient", "block.minecraft.banner.gradient_up.cyan": "Cyan Base Gradient", "block.minecraft.banner.gradient_up.gray": "Gray Base Gradient", "block.minecraft.banner.gradient_up.green": "Green Base Gradient", "block.minecraft.banner.gradient_up.light_blue": "Light Blue Base Gradient", "block.minecraft.banner.gradient_up.light_gray": "Light Gray Base Gradient", "block.minecraft.banner.gradient_up.lime": "Lime Base Gradient", "block.minecraft.banner.gradient_up.magenta": "Magenta Base Gradient", "block.minecraft.banner.gradient_up.orange": "Orange Base Gradient", "block.minecraft.banner.gradient_up.pink": "Pink Base Gradient", "block.minecraft.banner.gradient_up.purple": "Purple Base Gradient", "block.minecraft.banner.gradient_up.red": "Red Base Gradient", "block.minecraft.banner.gradient_up.white": "White Base Gradient", "block.minecraft.banner.gradient_up.yellow": "Yellow Base Gradient", "block.minecraft.banner.gradient.black": "Black Gradient", "block.minecraft.banner.gradient.blue": "Blue Gradient", "block.minecraft.banner.gradient.brown": "<PERSON>", "block.minecraft.banner.gradient.cyan": "<PERSON><PERSON>", "block.minecraft.banner.gradient.gray": "<PERSON>", "block.minecraft.banner.gradient.green": "Green Gradient", "block.minecraft.banner.gradient.light_blue": "Light Blue Gradient", "block.minecraft.banner.gradient.light_gray": "<PERSON> <PERSON>", "block.minecraft.banner.gradient.lime": "Lime G<PERSON>ient", "block.minecraft.banner.gradient.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.orange": "Orange Gradient", "block.minecraft.banner.gradient.pink": "Pink Gradient", "block.minecraft.banner.gradient.purple": "Purple Gradient", "block.minecraft.banner.gradient.red": "<PERSON> Gradient", "block.minecraft.banner.gradient.white": "White Gradient", "block.minecraft.banner.gradient.yellow": "Yellow Gradient", "block.minecraft.banner.half_horizontal_bottom.black": "Black Per Fess Inverted", "block.minecraft.banner.half_horizontal_bottom.blue": "Blue Per Fess Inverted", "block.minecraft.banner.half_horizontal_bottom.brown": "<PERSON> Per Fess Inverted", "block.minecraft.banner.half_horizontal_bottom.cyan": "<PERSON><PERSON> Inverted", "block.minecraft.banner.half_horizontal_bottom.gray": "<PERSON>ss Inverted", "block.minecraft.banner.half_horizontal_bottom.green": "Green Per Fess Inverted", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Light Blue Per Fess Inverted", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Light Gray Per Fess Inverted", "block.minecraft.banner.half_horizontal_bottom.lime": "Lime Per Fess Inverted", "block.minecraft.banner.half_horizontal_bottom.magenta": "Magenta Per Fess Inverted", "block.minecraft.banner.half_horizontal_bottom.orange": "Orange Per Fess Inverted", "block.minecraft.banner.half_horizontal_bottom.pink": "Pink Per Fess Inverted", "block.minecraft.banner.half_horizontal_bottom.purple": "Purple Per Fess Inverted", "block.minecraft.banner.half_horizontal_bottom.red": "Red Per Fess Inverted", "block.minecraft.banner.half_horizontal_bottom.white": "White Per Fess Inverted", "block.minecraft.banner.half_horizontal_bottom.yellow": "Yellow Per Fess Inverted", "block.minecraft.banner.half_horizontal.black": "Black Per Fess", "block.minecraft.banner.half_horizontal.blue": "Blue Per Fess", "block.minecraft.banner.half_horizontal.brown": "<PERSON>", "block.minecraft.banner.half_horizontal.cyan": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.gray": "<PERSON>", "block.minecraft.banner.half_horizontal.green": "Green Per Fess", "block.minecraft.banner.half_horizontal.light_blue": "Light Blue Per Fess", "block.minecraft.banner.half_horizontal.light_gray": "Light Gray <PERSON>", "block.minecraft.banner.half_horizontal.lime": "Lime <PERSON>", "block.minecraft.banner.half_horizontal.magenta": "Ma<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.orange": "Orange Per Fess", "block.minecraft.banner.half_horizontal.pink": "Pink Per Fess", "block.minecraft.banner.half_horizontal.purple": "Purple Per Fess", "block.minecraft.banner.half_horizontal.red": "Red Per Fess", "block.minecraft.banner.half_horizontal.white": "White Per Fess", "block.minecraft.banner.half_horizontal.yellow": "Yellow Per Fess", "block.minecraft.banner.half_vertical_right.black": "Black Per Pale Inverted", "block.minecraft.banner.half_vertical_right.blue": "Blue Per Pale Inverted", "block.minecraft.banner.half_vertical_right.brown": "<PERSON> Inverted", "block.minecraft.banner.half_vertical_right.cyan": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.gray": "<PERSON> Inverted", "block.minecraft.banner.half_vertical_right.green": "Green Per Pale Inverted", "block.minecraft.banner.half_vertical_right.light_blue": "Light Blue Per Pale Inverted", "block.minecraft.banner.half_vertical_right.light_gray": "Light Gray Per <PERSON>le Inverted", "block.minecraft.banner.half_vertical_right.lime": "Lime Per <PERSON> Inverted", "block.minecraft.banner.half_vertical_right.magenta": "Magenta Per <PERSON> Inverted", "block.minecraft.banner.half_vertical_right.orange": "Orange Per Pale Inverted", "block.minecraft.banner.half_vertical_right.pink": "Pink Per Pale Inverted", "block.minecraft.banner.half_vertical_right.purple": "Purple Per <PERSON>le Inverted", "block.minecraft.banner.half_vertical_right.red": "Red Per Pale Inverted", "block.minecraft.banner.half_vertical_right.white": "White Per Pale Inverted", "block.minecraft.banner.half_vertical_right.yellow": "Yellow Per Pale Inverted", "block.minecraft.banner.half_vertical.black": "<PERSON> Per <PERSON>", "block.minecraft.banner.half_vertical.blue": "<PERSON> Per <PERSON>", "block.minecraft.banner.half_vertical.brown": "<PERSON>", "block.minecraft.banner.half_vertical.cyan": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.gray": "<PERSON>", "block.minecraft.banner.half_vertical.green": "<PERSON> Per <PERSON>", "block.minecraft.banner.half_vertical.light_blue": "Light Blue Per Pale", "block.minecraft.banner.half_vertical.light_gray": "<PERSON> <PERSON>", "block.minecraft.banner.half_vertical.lime": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.magenta": "Ma<PERSON><PERSON>", "block.minecraft.banner.half_vertical.orange": "Orange Per Pale", "block.minecraft.banner.half_vertical.pink": "Pink Per <PERSON>", "block.minecraft.banner.half_vertical.purple": "<PERSON> Per <PERSON>", "block.minecraft.banner.half_vertical.red": "<PERSON> Per <PERSON>le", "block.minecraft.banner.half_vertical.white": "<PERSON> <PERSON>", "block.minecraft.banner.half_vertical.yellow": "Yellow Per Pale", "block.minecraft.banner.mojang.black": "Black Thing", "block.minecraft.banner.mojang.blue": "Blue Thing", "block.minecraft.banner.mojang.brown": "<PERSON>", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON>", "block.minecraft.banner.mojang.gray": "<PERSON>", "block.minecraft.banner.mojang.green": "Green Thing", "block.minecraft.banner.mojang.light_blue": "Light Blue Thing", "block.minecraft.banner.mojang.light_gray": "Light Gray Thing", "block.minecraft.banner.mojang.lime": "Lime Thing", "block.minecraft.banner.mojang.magenta": "Magenta Thing", "block.minecraft.banner.mojang.orange": "Orange Thing", "block.minecraft.banner.mojang.pink": "Pink Thing", "block.minecraft.banner.mojang.purple": "Purple Thing", "block.minecraft.banner.mojang.red": "Red Thing", "block.minecraft.banner.mojang.white": "White Thing", "block.minecraft.banner.mojang.yellow": "Yellow Thing", "block.minecraft.banner.piglin.black": "Black Snout", "block.minecraft.banner.piglin.blue": "<PERSON> Snout", "block.minecraft.banner.piglin.brown": "<PERSON>", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON>", "block.minecraft.banner.piglin.gray": "<PERSON>", "block.minecraft.banner.piglin.green": "<PERSON> Snout", "block.minecraft.banner.piglin.light_blue": "Light Blue Snout", "block.minecraft.banner.piglin.light_gray": "<PERSON> <PERSON>ut", "block.minecraft.banner.piglin.lime": "Lime S<PERSON>ut", "block.minecraft.banner.piglin.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.orange": "Orange Snout", "block.minecraft.banner.piglin.pink": "<PERSON> Snout", "block.minecraft.banner.piglin.purple": "<PERSON> Snout", "block.minecraft.banner.piglin.red": "<PERSON> Snout", "block.minecraft.banner.piglin.white": "White Snout", "block.minecraft.banner.piglin.yellow": "Yellow Snout", "block.minecraft.banner.rhombus.black": "Black Lozenge", "block.minecraft.banner.rhombus.blue": "Blue Lozenge", "block.minecraft.banner.rhombus.brown": "Brown Lozenge", "block.minecraft.banner.rhombus.cyan": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.gray": "Gray Lozenge", "block.minecraft.banner.rhombus.green": "Green Lozenge", "block.minecraft.banner.rhombus.light_blue": "Light Blue Lozenge", "block.minecraft.banner.rhombus.light_gray": "Light Gray Lozenge", "block.minecraft.banner.rhombus.lime": "Lime Lozenge", "block.minecraft.banner.rhombus.magenta": "Magenta Lozenge", "block.minecraft.banner.rhombus.orange": "Orange Lozenge", "block.minecraft.banner.rhombus.pink": "Pink Lozenge", "block.minecraft.banner.rhombus.purple": "Purple Lozenge", "block.minecraft.banner.rhombus.red": "Red Lozenge", "block.minecraft.banner.rhombus.white": "White Lozenge", "block.minecraft.banner.rhombus.yellow": "Yellow Lozenge", "block.minecraft.banner.skull.black": "Black Skull Charge", "block.minecraft.banner.skull.blue": "Blue Skull Charge", "block.minecraft.banner.skull.brown": "Brown Skull Charge", "block.minecraft.banner.skull.cyan": "<PERSON>an <PERSON>", "block.minecraft.banner.skull.gray": "Gray Skull Charge", "block.minecraft.banner.skull.green": "Green Skull Charge", "block.minecraft.banner.skull.light_blue": "Light Blue Skull Charge", "block.minecraft.banner.skull.light_gray": "Light Gray Skull Charge", "block.minecraft.banner.skull.lime": "Lime Skull Charge", "block.minecraft.banner.skull.magenta": "Magenta Skull Charge", "block.minecraft.banner.skull.orange": "Orange Skull Charge", "block.minecraft.banner.skull.pink": "Pink Skull Charge", "block.minecraft.banner.skull.purple": "Purple Skull Charge", "block.minecraft.banner.skull.red": "Red Skull Charge", "block.minecraft.banner.skull.white": "White Skull Charge", "block.minecraft.banner.skull.yellow": "Yellow Skull Charge", "block.minecraft.banner.small_stripes.black": "<PERSON> Paly", "block.minecraft.banner.small_stripes.blue": "Blue Paly", "block.minecraft.banner.small_stripes.brown": "<PERSON>", "block.minecraft.banner.small_stripes.cyan": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.gray": "<PERSON>", "block.minecraft.banner.small_stripes.green": "<PERSON> Paly", "block.minecraft.banner.small_stripes.light_blue": "Light Blue Paly", "block.minecraft.banner.small_stripes.light_gray": "<PERSON> Gray <PERSON>", "block.minecraft.banner.small_stripes.lime": "<PERSON>e <PERSON>", "block.minecraft.banner.small_stripes.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.orange": "Orange Paly", "block.minecraft.banner.small_stripes.pink": "<PERSON> Paly", "block.minecraft.banner.small_stripes.purple": "<PERSON> Paly", "block.minecraft.banner.small_stripes.red": "<PERSON> Paly", "block.minecraft.banner.small_stripes.white": "<PERSON>ly", "block.minecraft.banner.small_stripes.yellow": "Yellow Paly", "block.minecraft.banner.square_bottom_left.black": "Black Base Dexter Canton", "block.minecraft.banner.square_bottom_left.blue": "Blue Base Dexter Canton", "block.minecraft.banner.square_bottom_left.brown": "Brown Base Dexter Canton", "block.minecraft.banner.square_bottom_left.cyan": "Cyan Base Dexter Canton", "block.minecraft.banner.square_bottom_left.gray": "Gray Base Dexter Canton", "block.minecraft.banner.square_bottom_left.green": "Green Base Dexter Canton", "block.minecraft.banner.square_bottom_left.light_blue": "Light Blue Base Dexter Canton", "block.minecraft.banner.square_bottom_left.light_gray": "Light Gray Base Dexter Canton", "block.minecraft.banner.square_bottom_left.lime": "Lime Base Dexter Canton", "block.minecraft.banner.square_bottom_left.magenta": "Magenta Base Dexter Canton", "block.minecraft.banner.square_bottom_left.orange": "Orange Base Dexter Canton", "block.minecraft.banner.square_bottom_left.pink": "Pink Base Dexter Canton", "block.minecraft.banner.square_bottom_left.purple": "Purple Base Dexter Canton", "block.minecraft.banner.square_bottom_left.red": "Red Base Dexter Canton", "block.minecraft.banner.square_bottom_left.white": "White Base Dexter Canton", "block.minecraft.banner.square_bottom_left.yellow": "Yellow Base Dexter Canton", "block.minecraft.banner.square_bottom_right.black": "Black Base Sinister Canton", "block.minecraft.banner.square_bottom_right.blue": "Blue Base Sinister Canton", "block.minecraft.banner.square_bottom_right.brown": "Brown Base Sinister Canton", "block.minecraft.banner.square_bottom_right.cyan": "Cyan Base Sinister Canton", "block.minecraft.banner.square_bottom_right.gray": "Gray Base Sinister Canton", "block.minecraft.banner.square_bottom_right.green": "Green Base Sinister Canton", "block.minecraft.banner.square_bottom_right.light_blue": "Light Blue Base Sinister Canton", "block.minecraft.banner.square_bottom_right.light_gray": "Light Gray Base Sinister Canton", "block.minecraft.banner.square_bottom_right.lime": "Lime Base Sinister Canton", "block.minecraft.banner.square_bottom_right.magenta": "Magenta Base Sinister Canton", "block.minecraft.banner.square_bottom_right.orange": "Orange Base Sinister Canton", "block.minecraft.banner.square_bottom_right.pink": "Pink Base Sinister Canton", "block.minecraft.banner.square_bottom_right.purple": "Purple Base Sinister Canton", "block.minecraft.banner.square_bottom_right.red": "Red Base Sinister Canton", "block.minecraft.banner.square_bottom_right.white": "White Base Sinister Canton", "block.minecraft.banner.square_bottom_right.yellow": "Yellow Base Sinister Canton", "block.minecraft.banner.square_top_left.black": "Black Chief <PERSON>", "block.minecraft.banner.square_top_left.blue": "Blue Chief <PERSON>", "block.minecraft.banner.square_top_left.brown": "Brown Chief <PERSON>", "block.minecraft.banner.square_top_left.cyan": "Cyan Chief <PERSON>", "block.minecraft.banner.square_top_left.gray": "Gray Chief <PERSON>", "block.minecraft.banner.square_top_left.green": "Green Chief <PERSON>", "block.minecraft.banner.square_top_left.light_blue": "Light Blue Chief <PERSON>", "block.minecraft.banner.square_top_left.light_gray": "Light Gray Chief <PERSON>", "block.minecraft.banner.square_top_left.lime": "Lime Chief <PERSON>", "block.minecraft.banner.square_top_left.magenta": "Magenta Chief <PERSON>", "block.minecraft.banner.square_top_left.orange": "Orange Chief <PERSON>", "block.minecraft.banner.square_top_left.pink": "Pink Chief <PERSON>", "block.minecraft.banner.square_top_left.purple": "Purple Chief <PERSON>", "block.minecraft.banner.square_top_left.red": "Red Chief <PERSON>", "block.minecraft.banner.square_top_left.white": "White Chief <PERSON>", "block.minecraft.banner.square_top_left.yellow": "Yellow Chief <PERSON>", "block.minecraft.banner.square_top_right.black": "Black Chief <PERSON>", "block.minecraft.banner.square_top_right.blue": "Blue Chief <PERSON>", "block.minecraft.banner.square_top_right.brown": "Brown Chief <PERSON><PERSON>", "block.minecraft.banner.square_top_right.cyan": "Cyan Chief <PERSON><PERSON>", "block.minecraft.banner.square_top_right.gray": "Gray Chief <PERSON>", "block.minecraft.banner.square_top_right.green": "Green Chief <PERSON>", "block.minecraft.banner.square_top_right.light_blue": "Light Blue Chief <PERSON>", "block.minecraft.banner.square_top_right.light_gray": "Light Gray Chief <PERSON>", "block.minecraft.banner.square_top_right.lime": "Lime Chief Sinister <PERSON>", "block.minecraft.banner.square_top_right.magenta": "Magenta Chief Sin<PERSON>", "block.minecraft.banner.square_top_right.orange": "Orange Chief <PERSON>", "block.minecraft.banner.square_top_right.pink": "Pink Chief <PERSON>", "block.minecraft.banner.square_top_right.purple": "Purple Chief <PERSON><PERSON>", "block.minecraft.banner.square_top_right.red": "Red Chief Sin<PERSON>", "block.minecraft.banner.square_top_right.white": "White Chief <PERSON>ister <PERSON>", "block.minecraft.banner.square_top_right.yellow": "Yellow Chief Sinister <PERSON>", "block.minecraft.banner.straight_cross.black": "Black Cross", "block.minecraft.banner.straight_cross.blue": "Blue Cross", "block.minecraft.banner.straight_cross.brown": "<PERSON>", "block.minecraft.banner.straight_cross.cyan": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.gray": "<PERSON>", "block.minecraft.banner.straight_cross.green": "Green Cross", "block.minecraft.banner.straight_cross.light_blue": "Light Blue Cross", "block.minecraft.banner.straight_cross.light_gray": "Light Gray Cross", "block.minecraft.banner.straight_cross.lime": "Lime Cross", "block.minecraft.banner.straight_cross.magenta": "Magenta Cross", "block.minecraft.banner.straight_cross.orange": "Orange Cross", "block.minecraft.banner.straight_cross.pink": "Pink Cross", "block.minecraft.banner.straight_cross.purple": "Purple Cross", "block.minecraft.banner.straight_cross.red": "Red Cross", "block.minecraft.banner.straight_cross.white": "White Cross", "block.minecraft.banner.straight_cross.yellow": "Yellow Cross", "block.minecraft.banner.stripe_bottom.black": "Black Base", "block.minecraft.banner.stripe_bottom.blue": "Blue Base", "block.minecraft.banner.stripe_bottom.brown": "Brown Base", "block.minecraft.banner.stripe_bottom.cyan": "Cyan Base", "block.minecraft.banner.stripe_bottom.gray": "Gray Base", "block.minecraft.banner.stripe_bottom.green": "Green Base", "block.minecraft.banner.stripe_bottom.light_blue": "Light Blue Base", "block.minecraft.banner.stripe_bottom.light_gray": "Light Gray Base", "block.minecraft.banner.stripe_bottom.lime": "Lime Base", "block.minecraft.banner.stripe_bottom.magenta": "Magenta Base", "block.minecraft.banner.stripe_bottom.orange": "Orange Base", "block.minecraft.banner.stripe_bottom.pink": "Pink Base", "block.minecraft.banner.stripe_bottom.purple": "Purple Base", "block.minecraft.banner.stripe_bottom.red": "Red Base", "block.minecraft.banner.stripe_bottom.white": "White Base", "block.minecraft.banner.stripe_bottom.yellow": "Yellow Base", "block.minecraft.banner.stripe_center.black": "Black Pale", "block.minecraft.banner.stripe_center.blue": "<PERSON> Pale", "block.minecraft.banner.stripe_center.brown": "<PERSON>", "block.minecraft.banner.stripe_center.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.gray": "<PERSON>", "block.minecraft.banner.stripe_center.green": "Green Pale", "block.minecraft.banner.stripe_center.light_blue": "Light Blue Pale", "block.minecraft.banner.stripe_center.light_gray": "<PERSON> <PERSON>", "block.minecraft.banner.stripe_center.lime": "<PERSON>e <PERSON>", "block.minecraft.banner.stripe_center.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.orange": "Orange Pale", "block.minecraft.banner.stripe_center.pink": "<PERSON>", "block.minecraft.banner.stripe_center.purple": "<PERSON> Pale", "block.minecraft.banner.stripe_center.red": "<PERSON>", "block.minecraft.banner.stripe_center.white": "White Pale", "block.minecraft.banner.stripe_center.yellow": "Yellow Pale", "block.minecraft.banner.stripe_downleft.black": "Black Bend Sinister", "block.minecraft.banner.stripe_downleft.blue": "Blue Bend Sinister", "block.minecraft.banner.stripe_downleft.brown": "Brown Bend Sinister", "block.minecraft.banner.stripe_downleft.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.gray": "Gray Bend Sinister", "block.minecraft.banner.stripe_downleft.green": "Green Bend Sinister", "block.minecraft.banner.stripe_downleft.light_blue": "Light Blue Bend Sinister", "block.minecraft.banner.stripe_downleft.light_gray": "Light Gray Bend Sinister", "block.minecraft.banner.stripe_downleft.lime": "Lime Bend Sinister", "block.minecraft.banner.stripe_downleft.magenta": "Magenta Bend Sinister", "block.minecraft.banner.stripe_downleft.orange": "Orange Bend Sinister", "block.minecraft.banner.stripe_downleft.pink": "Pink Bend Sinister", "block.minecraft.banner.stripe_downleft.purple": "Purple Bend Sinister", "block.minecraft.banner.stripe_downleft.red": "Red Bend Sinister", "block.minecraft.banner.stripe_downleft.white": "White Bend Sinister", "block.minecraft.banner.stripe_downleft.yellow": "Yellow Bend Sinister", "block.minecraft.banner.stripe_downright.black": "Black Bend", "block.minecraft.banner.stripe_downright.blue": "Blue Bend", "block.minecraft.banner.stripe_downright.brown": "Brown Bend", "block.minecraft.banner.stripe_downright.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.gray": "Gray Bend", "block.minecraft.banner.stripe_downright.green": "Green Bend", "block.minecraft.banner.stripe_downright.light_blue": "Light Blue Bend", "block.minecraft.banner.stripe_downright.light_gray": "Light Gray Bend", "block.minecraft.banner.stripe_downright.lime": "Lime Bend", "block.minecraft.banner.stripe_downright.magenta": "Magenta Bend", "block.minecraft.banner.stripe_downright.orange": "Orange Bend", "block.minecraft.banner.stripe_downright.pink": "Pink Bend", "block.minecraft.banner.stripe_downright.purple": "Purple Bend", "block.minecraft.banner.stripe_downright.red": "Red Bend", "block.minecraft.banner.stripe_downright.white": "White Bend", "block.minecraft.banner.stripe_downright.yellow": "Yellow Bend", "block.minecraft.banner.stripe_left.black": "<PERSON>", "block.minecraft.banner.stripe_left.blue": "<PERSON> Dexter", "block.minecraft.banner.stripe_left.brown": "<PERSON>", "block.minecraft.banner.stripe_left.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.gray": "<PERSON>", "block.minecraft.banner.stripe_left.green": "<PERSON>", "block.minecraft.banner.stripe_left.light_blue": "Light Blue Pale Dexter", "block.minecraft.banner.stripe_left.light_gray": "<PERSON> <PERSON>", "block.minecraft.banner.stripe_left.lime": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.orange": "Orange Pale Dexter", "block.minecraft.banner.stripe_left.pink": "<PERSON>", "block.minecraft.banner.stripe_left.purple": "<PERSON> Pale <PERSON>", "block.minecraft.banner.stripe_left.red": "<PERSON>", "block.minecraft.banner.stripe_left.white": "<PERSON>", "block.minecraft.banner.stripe_left.yellow": "Yellow Pale Dexter", "block.minecraft.banner.stripe_middle.black": "Black Fess", "block.minecraft.banner.stripe_middle.blue": "Blue Fess", "block.minecraft.banner.stripe_middle.brown": "Brown Fess", "block.minecraft.banner.stripe_middle.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.gray": "<PERSON>", "block.minecraft.banner.stripe_middle.green": "Green Fess", "block.minecraft.banner.stripe_middle.light_blue": "Light Blue Fess", "block.minecraft.banner.stripe_middle.light_gray": "Light Gray Fess", "block.minecraft.banner.stripe_middle.lime": "Lime Fess", "block.minecraft.banner.stripe_middle.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.orange": "Orange Fess", "block.minecraft.banner.stripe_middle.pink": "Pink Fess", "block.minecraft.banner.stripe_middle.purple": "Purple Fess", "block.minecraft.banner.stripe_middle.red": "Red Fess", "block.minecraft.banner.stripe_middle.white": "White Fess", "block.minecraft.banner.stripe_middle.yellow": "Yellow Fess", "block.minecraft.banner.stripe_right.black": "Black Pale Sinister", "block.minecraft.banner.stripe_right.blue": "Blue Pale Sinister", "block.minecraft.banner.stripe_right.brown": "<PERSON> Pale <PERSON>", "block.minecraft.banner.stripe_right.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.gray": "<PERSON>", "block.minecraft.banner.stripe_right.green": "Green Pale Sinister", "block.minecraft.banner.stripe_right.light_blue": "Light Blue Pale Sinister", "block.minecraft.banner.stripe_right.light_gray": "<PERSON> Gray Pale <PERSON>", "block.minecraft.banner.stripe_right.lime": "Lime Pale <PERSON>", "block.minecraft.banner.stripe_right.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.orange": "Orange Pale Sinister", "block.minecraft.banner.stripe_right.pink": "Pink Pale Sinister", "block.minecraft.banner.stripe_right.purple": "Purple Pale Sinister", "block.minecraft.banner.stripe_right.red": "Red Pale Sinister", "block.minecraft.banner.stripe_right.white": "White Pale Sinister", "block.minecraft.banner.stripe_right.yellow": "Yellow Pale Sinister", "block.minecraft.banner.stripe_top.black": "Black Chief", "block.minecraft.banner.stripe_top.blue": "Blue Chief", "block.minecraft.banner.stripe_top.brown": "<PERSON> Chief", "block.minecraft.banner.stripe_top.cyan": "<PERSON>an Chief", "block.minecraft.banner.stripe_top.gray": "<PERSON>", "block.minecraft.banner.stripe_top.green": "Green Chief", "block.minecraft.banner.stripe_top.light_blue": "Light Blue Chief", "block.minecraft.banner.stripe_top.light_gray": "<PERSON> Gray Chief", "block.minecraft.banner.stripe_top.lime": "Lime Chief", "block.minecraft.banner.stripe_top.magenta": "Magenta Chief", "block.minecraft.banner.stripe_top.orange": "Orange Chief", "block.minecraft.banner.stripe_top.pink": "Pink Chief", "block.minecraft.banner.stripe_top.purple": "Purple Chief", "block.minecraft.banner.stripe_top.red": "Red Chief", "block.minecraft.banner.stripe_top.white": "White Chief", "block.minecraft.banner.stripe_top.yellow": "Yellow Chief", "block.minecraft.banner.triangle_bottom.black": "Black Chevron", "block.minecraft.banner.triangle_bottom.blue": "Blue Chevron", "block.minecraft.banner.triangle_bottom.brown": "<PERSON> Chevron", "block.minecraft.banner.triangle_bottom.cyan": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.gray": "<PERSON>", "block.minecraft.banner.triangle_bottom.green": "Green Chevron", "block.minecraft.banner.triangle_bottom.light_blue": "Light Blue Chevron", "block.minecraft.banner.triangle_bottom.light_gray": "Light Gray Chevron", "block.minecraft.banner.triangle_bottom.lime": "Lime Chevron", "block.minecraft.banner.triangle_bottom.magenta": "<PERSON><PERSON><PERSON>ron", "block.minecraft.banner.triangle_bottom.orange": "Orange Chevron", "block.minecraft.banner.triangle_bottom.pink": "Pink Chevron", "block.minecraft.banner.triangle_bottom.purple": "Purple Chevron", "block.minecraft.banner.triangle_bottom.red": "Red Chevron", "block.minecraft.banner.triangle_bottom.white": "White Chevron", "block.minecraft.banner.triangle_bottom.yellow": "Yellow Chevron", "block.minecraft.banner.triangle_top.black": "Black Inverted Chevron", "block.minecraft.banner.triangle_top.blue": "Blue Inverted Chevron", "block.minecraft.banner.triangle_top.brown": "<PERSON> Inverted Chevron", "block.minecraft.banner.triangle_top.cyan": "<PERSON><PERSON> In<PERSON> Chevron", "block.minecraft.banner.triangle_top.gray": "Gray Inverted Chevron", "block.minecraft.banner.triangle_top.green": "Green Inverted Chevron", "block.minecraft.banner.triangle_top.light_blue": "Light Blue Inverted Chevron", "block.minecraft.banner.triangle_top.light_gray": "Light Gray Inverted Chevron", "block.minecraft.banner.triangle_top.lime": "Lime Inverted Chevron", "block.minecraft.banner.triangle_top.magenta": "Magenta Inverted Chevron", "block.minecraft.banner.triangle_top.orange": "Orange Inverted Chevron", "block.minecraft.banner.triangle_top.pink": "Pink Inverted Chevron", "block.minecraft.banner.triangle_top.purple": "Purple Inverted Chevron", "block.minecraft.banner.triangle_top.red": "Red Inverted Chevron", "block.minecraft.banner.triangle_top.white": "White Inverted Chevron", "block.minecraft.banner.triangle_top.yellow": "Yellow Inverted Chevron", "block.minecraft.banner.triangles_bottom.black": "Black Base Indented", "block.minecraft.banner.triangles_bottom.blue": "Blue Base Indented", "block.minecraft.banner.triangles_bottom.brown": "Brown Base Indented", "block.minecraft.banner.triangles_bottom.cyan": "Cyan Base Indented", "block.minecraft.banner.triangles_bottom.gray": "Gray Base Indented", "block.minecraft.banner.triangles_bottom.green": "Green Base Indented", "block.minecraft.banner.triangles_bottom.light_blue": "Light Blue Base Indented", "block.minecraft.banner.triangles_bottom.light_gray": "Light Gray Base Indented", "block.minecraft.banner.triangles_bottom.lime": "Lime Base Indented", "block.minecraft.banner.triangles_bottom.magenta": "Magenta Base Indented", "block.minecraft.banner.triangles_bottom.orange": "Orange Base Indented", "block.minecraft.banner.triangles_bottom.pink": "Pink Base Indented", "block.minecraft.banner.triangles_bottom.purple": "Purple Base Indented", "block.minecraft.banner.triangles_bottom.red": "Red Base Indented", "block.minecraft.banner.triangles_bottom.white": "White Base Indented", "block.minecraft.banner.triangles_bottom.yellow": "Yellow Base Indented", "block.minecraft.banner.triangles_top.black": "Black Chief Indented", "block.minecraft.banner.triangles_top.blue": "Blue Chief Indented", "block.minecraft.banner.triangles_top.brown": "<PERSON> Chief Indented", "block.minecraft.banner.triangles_top.cyan": "Cyan Chief Indented", "block.minecraft.banner.triangles_top.gray": "Gray Chief Indented", "block.minecraft.banner.triangles_top.green": "Green Chief Indented", "block.minecraft.banner.triangles_top.light_blue": "Light Blue Chief Indented", "block.minecraft.banner.triangles_top.light_gray": "Light Gray Chief Indented", "block.minecraft.banner.triangles_top.lime": "Lime Chief Indented", "block.minecraft.banner.triangles_top.magenta": "Magenta Chief Indented", "block.minecraft.banner.triangles_top.orange": "Orange Chief Indented", "block.minecraft.banner.triangles_top.pink": "Pink Chief Indented", "block.minecraft.banner.triangles_top.purple": "Purple Chief Indented", "block.minecraft.banner.triangles_top.red": "Red Chief Indented", "block.minecraft.banner.triangles_top.white": "White Chief Indented", "block.minecraft.banner.triangles_top.yellow": "Yellow Chief Indented", "block.minecraft.barrel": "Barrel", "block.minecraft.barrier": "Barrier", "block.minecraft.basalt": "Basalt", "block.minecraft.beacon": "Beacon", "block.minecraft.beacon.primary": "Primary Power", "block.minecraft.beacon.secondary": "Secondary Power", "block.minecraft.bed.no_sleep": "You can sleep only at night or during thunderstorms", "block.minecraft.bed.not_safe": "You may not rest now; there are monsters nearby", "block.minecraft.bed.obstructed": "This bed is obstructed", "block.minecraft.bed.occupied": "This bed is occupied", "block.minecraft.bed.too_far_away": "You may not rest now; the bed is too far away", "block.minecraft.bedrock": "Bedrock", "block.minecraft.bee_nest": "Bee Nest", "block.minecraft.beehive": "Beehive", "block.minecraft.beetroots": "Beetroots", "block.minecraft.bell": "Bell", "block.minecraft.big_dripleaf": "Big Dripleaf", "block.minecraft.big_dripleaf_stem": "Big Dripleaf Stem", "block.minecraft.birch_button": "<PERSON>", "block.minecraft.birch_door": "<PERSON>", "block.minecraft.birch_fence": "<PERSON>", "block.minecraft.birch_fence_gate": "Birch Fence Gate", "block.minecraft.birch_hanging_sign": "<PERSON> Hanging Sign", "block.minecraft.birch_leaves": "Birch Leaves", "block.minecraft.birch_log": "Birch Log", "block.minecraft.birch_planks": "Birch Planks", "block.minecraft.birch_pressure_plate": "Birch Pressure Plate", "block.minecraft.birch_sapling": "Birch Sapling", "block.minecraft.birch_sign": "Birch Sign", "block.minecraft.birch_slab": "<PERSON>", "block.minecraft.birch_stairs": "<PERSON> Stairs", "block.minecraft.birch_trapdoor": "<PERSON>", "block.minecraft.birch_wall_hanging_sign": "Birch Wall Hanging Sign", "block.minecraft.birch_wall_sign": "<PERSON> Sign", "block.minecraft.birch_wood": "Birch Wood", "block.minecraft.black_banner": "Black Banner", "block.minecraft.black_bed": "Black Bed", "block.minecraft.black_candle": "Black Candle", "block.minecraft.black_candle_cake": "Cake with Black Candle", "block.minecraft.black_carpet": "Black Carpet", "block.minecraft.black_concrete": "Black Concrete", "block.minecraft.black_concrete_powder": "Black Concrete Powder", "block.minecraft.black_glazed_terracotta": "Black Glazed Terracotta", "block.minecraft.black_shulker_box": "Black Shulker Box", "block.minecraft.black_stained_glass": "Black Stained Glass", "block.minecraft.black_stained_glass_pane": "Black Stained Glass Pane", "block.minecraft.black_terracotta": "Black Terracotta", "block.minecraft.black_wool": "Black Wool", "block.minecraft.blackstone": "Blackstone", "block.minecraft.blackstone_slab": "Blackstone Slab", "block.minecraft.blackstone_stairs": "Blackstone Stairs", "block.minecraft.blackstone_wall": "Blackstone Wall", "block.minecraft.blast_furnace": "Blast Furnace", "block.minecraft.blue_banner": "Blue Banner", "block.minecraft.blue_bed": "Blue Bed", "block.minecraft.blue_candle": "Blue Candle", "block.minecraft.blue_candle_cake": "Cake with Blue Candle", "block.minecraft.blue_carpet": "Blue Carpet", "block.minecraft.blue_concrete": "Blue Concrete", "block.minecraft.blue_concrete_powder": "Blue Concrete Powder", "block.minecraft.blue_glazed_terracotta": "Blue Glazed Terracotta", "block.minecraft.blue_ice": "Blue Ice", "block.minecraft.blue_orchid": "Blue Orchid", "block.minecraft.blue_shulker_box": "Blue Shulker Box", "block.minecraft.blue_stained_glass": "Blue Stained Glass", "block.minecraft.blue_stained_glass_pane": "Blue Stained Glass Pane", "block.minecraft.blue_terracotta": "Blue Terracotta", "block.minecraft.blue_wool": "Blue Wool", "block.minecraft.bone_block": "Bone Block", "block.minecraft.bookshelf": "Bookshelf", "block.minecraft.brain_coral": "Brain Coral", "block.minecraft.brain_coral_block": "Brain <PERSON>", "block.minecraft.brain_coral_fan": "Brain Coral Fan", "block.minecraft.brain_coral_wall_fan": "Brain <PERSON>", "block.minecraft.brewing_stand": "Brewing Stand", "block.minecraft.brick_slab": "Brick Slab", "block.minecraft.brick_stairs": "Brick Stairs", "block.minecraft.brick_wall": "Brick Wall", "block.minecraft.bricks": "Bricks", "block.minecraft.brown_banner": "<PERSON>", "block.minecraft.brown_bed": "Brown Bed", "block.minecraft.brown_candle": "<PERSON> Candle", "block.minecraft.brown_candle_cake": "Cake with <PERSON> Candle", "block.minecraft.brown_carpet": "Brown Carpet", "block.minecraft.brown_concrete": "<PERSON> Concrete", "block.minecraft.brown_concrete_powder": "<PERSON> Concrete <PERSON>", "block.minecraft.brown_glazed_terracotta": "Brown Glazed Terracotta", "block.minecraft.brown_mushroom": "Brown Mushroom", "block.minecraft.brown_mushroom_block": "Brown Mushroom Block", "block.minecraft.brown_shulker_box": "<PERSON> Shulker Box", "block.minecraft.brown_stained_glass": "<PERSON> Stained Glass", "block.minecraft.brown_stained_glass_pane": "<PERSON> Stained Glass Pane", "block.minecraft.brown_terracotta": "Brown Terracotta", "block.minecraft.brown_wool": "Brown Wool", "block.minecraft.bubble_column": "Bubble Column", "block.minecraft.bubble_coral": "Bubble Coral", "block.minecraft.bubble_coral_block": "Bubble Coral Block", "block.minecraft.bubble_coral_fan": "Bubble Coral Fan", "block.minecraft.bubble_coral_wall_fan": "Bubble Coral Wall Fan", "block.minecraft.budding_amethyst": "Budding Amethyst", "block.minecraft.cactus": "Cactus", "block.minecraft.cake": "Cake", "block.minecraft.calcite": "Calcite", "block.minecraft.calibrated_sculk_sensor": "Calibrated Sculk Sensor", "block.minecraft.campfire": "Campfire", "block.minecraft.candle": "Candle", "block.minecraft.candle_cake": "Cake with Candle", "block.minecraft.carrots": "Carrots", "block.minecraft.cartography_table": "Cartography Table", "block.minecraft.carved_pumpkin": "<PERSON><PERSON>", "block.minecraft.cauldron": "<PERSON><PERSON><PERSON>", "block.minecraft.cave_air": "Cave Air", "block.minecraft.cave_vines": "Cave Vines", "block.minecraft.cave_vines_plant": "Cave Vines Plant", "block.minecraft.chain": "Chain", "block.minecraft.chain_command_block": "Chain Command Block", "block.minecraft.cherry_button": "<PERSON>", "block.minecraft.cherry_door": "Cherry Door", "block.minecraft.cherry_fence": "<PERSON>", "block.minecraft.cherry_fence_gate": "Cherry Fence Gate", "block.minecraft.cherry_hanging_sign": "Cherry Hanging Sign", "block.minecraft.cherry_leaves": "Cherry Leaves", "block.minecraft.cherry_log": "Cherry Log", "block.minecraft.cherry_planks": "Cherry Planks", "block.minecraft.cherry_pressure_plate": "Cherry Pressure Plate", "block.minecraft.cherry_sapling": "Cherry Sapling", "block.minecraft.cherry_sign": "Cherry Sign", "block.minecraft.cherry_slab": "Cherry Slab", "block.minecraft.cherry_stairs": "<PERSON> Stairs", "block.minecraft.cherry_trapdoor": "Cherry Trapdoor", "block.minecraft.cherry_wall_hanging_sign": "Cherry Wall Hanging Sign", "block.minecraft.cherry_wall_sign": "Cherry Wall Sign", "block.minecraft.cherry_wood": "<PERSON>", "block.minecraft.chest": "Chest", "block.minecraft.chipped_anvil": "Chipped Anvil", "block.minecraft.chiseled_bookshelf": "Chiseled Bookshelf", "block.minecraft.chiseled_deepslate": "Chiseled Deepslate", "block.minecraft.chiseled_nether_bricks": "Chiseled Nether Bricks", "block.minecraft.chiseled_polished_blackstone": "Chiseled Polished Blackstone", "block.minecraft.chiseled_quartz_block": "Chiseled Quartz Block", "block.minecraft.chiseled_red_sandstone": "Chiseled Red Sandstone", "block.minecraft.chiseled_sandstone": "Chiseled Sandstone", "block.minecraft.chiseled_stone_bricks": "Chiseled Stone Bricks", "block.minecraft.chorus_flower": "Chorus Flower", "block.minecraft.chorus_plant": "Chorus Plant", "block.minecraft.clay": "<PERSON>", "block.minecraft.coal_block": "Block of Coal", "block.minecraft.coal_ore": "Coal Ore", "block.minecraft.coarse_dirt": "Coarse Dirt", "block.minecraft.cobbled_deepslate": "Cobbled Deepslate", "block.minecraft.cobbled_deepslate_slab": "Cobbled Deepslate Slab", "block.minecraft.cobbled_deepslate_stairs": "Cobbled Deepslate Stairs", "block.minecraft.cobbled_deepslate_wall": "Cobbled Deepslate Wall", "block.minecraft.cobblestone": "Cobblestone", "block.minecraft.cobblestone_slab": "Cobblestone Slab", "block.minecraft.cobblestone_stairs": "Cobblestone Stairs", "block.minecraft.cobblestone_wall": "Cobblestone Wall", "block.minecraft.cobweb": "Cobweb", "block.minecraft.cocoa": "Cocoa", "block.minecraft.command_block": "Command Block", "block.minecraft.comparator": "Redstone Comparator", "block.minecraft.composter": "Composter", "block.minecraft.conduit": "Conduit", "block.minecraft.copper_block": "Block of Copper", "block.minecraft.copper_ore": "Copper Ore", "block.minecraft.cornflower": "Corn<PERSON>", "block.minecraft.cracked_deepslate_bricks": "Cracked Deepslate Bricks", "block.minecraft.cracked_deepslate_tiles": "Cracked Deepslate Tiles", "block.minecraft.cracked_nether_bricks": "Cracked Nether Bricks", "block.minecraft.cracked_polished_blackstone_bricks": "Cracked Polished Blackstone Bricks", "block.minecraft.cracked_stone_bricks": "Cracked Stone Bricks", "block.minecraft.crafting_table": "Crafting Table", "block.minecraft.creeper_head": "Creeper Head", "block.minecraft.creeper_wall_head": "Creeper Wall Head", "block.minecraft.crimson_button": "<PERSON>", "block.minecraft.crimson_door": "Crimson Door", "block.minecraft.crimson_fence": "<PERSON> Fence", "block.minecraft.crimson_fence_gate": "Crimson Fence Gate", "block.minecraft.crimson_fungus": "Crimson Fungus", "block.minecraft.crimson_hanging_sign": "Crimson Hanging Sign", "block.minecraft.crimson_hyphae": "Crimson Hyphae", "block.minecraft.crimson_nylium": "Crimson Nylium", "block.minecraft.crimson_planks": "Crimson Planks", "block.minecraft.crimson_pressure_plate": "Crimson Pressure Plate", "block.minecraft.crimson_roots": "Crimson Roots", "block.minecraft.crimson_sign": "Crimson Sign", "block.minecraft.crimson_slab": "Crimson Slab", "block.minecraft.crimson_stairs": "Crimson Stairs", "block.minecraft.crimson_stem": "Crimson Stem", "block.minecraft.crimson_trapdoor": "Crimson Trapdoor", "block.minecraft.crimson_wall_hanging_sign": "Crimson Wall Hanging Sign", "block.minecraft.crimson_wall_sign": "Crimson Wall Sign", "block.minecraft.crying_obsidian": "Crying Obsidian", "block.minecraft.cut_copper": "Cut Copper", "block.minecraft.cut_copper_slab": "Cut Copper Slab", "block.minecraft.cut_copper_stairs": "Cut Copper Stairs", "block.minecraft.cut_red_sandstone": "Cut Red Sandstone", "block.minecraft.cut_red_sandstone_slab": "Cut Red Sandstone Slab", "block.minecraft.cut_sandstone": "Cut Sandstone", "block.minecraft.cut_sandstone_slab": "Cut Sandstone Slab", "block.minecraft.cyan_banner": "<PERSON><PERSON>", "block.minecraft.cyan_bed": "<PERSON><PERSON>", "block.minecraft.cyan_candle": "<PERSON><PERSON>", "block.minecraft.cyan_candle_cake": "Cake with <PERSON><PERSON>", "block.minecraft.cyan_carpet": "<PERSON><PERSON>", "block.minecraft.cyan_concrete": "<PERSON><PERSON>", "block.minecraft.cyan_concrete_powder": "<PERSON><PERSON>", "block.minecraft.cyan_glazed_terracotta": "<PERSON><PERSON>zed Terracotta", "block.minecraft.cyan_shulker_box": "<PERSON><PERSON>", "block.minecraft.cyan_stained_glass": "<PERSON><PERSON>", "block.minecraft.cyan_stained_glass_pane": "<PERSON><PERSON> Stained Glass Pane", "block.minecraft.cyan_terracotta": "<PERSON><PERSON>", "block.minecraft.cyan_wool": "<PERSON><PERSON>", "block.minecraft.damaged_anvil": "Damaged Anvil", "block.minecraft.dandelion": "Dandelion", "block.minecraft.dark_oak_button": "Dark Oak Button", "block.minecraft.dark_oak_door": "Dark Oak Door", "block.minecraft.dark_oak_fence": "Dark Oak Fence", "block.minecraft.dark_oak_fence_gate": "Dark Oak Fence Gate", "block.minecraft.dark_oak_hanging_sign": "Dark Oak Hanging Sign", "block.minecraft.dark_oak_leaves": "Dark Oak Leaves", "block.minecraft.dark_oak_log": "Dark Oak Log", "block.minecraft.dark_oak_planks": "Dark Oak Planks", "block.minecraft.dark_oak_pressure_plate": "Dark Oak Pressure Plate", "block.minecraft.dark_oak_sapling": "Dark Oak Sapling", "block.minecraft.dark_oak_sign": "Dark Oak Sign", "block.minecraft.dark_oak_slab": "Dark Oak Slab", "block.minecraft.dark_oak_stairs": "Dark Oak Stairs", "block.minecraft.dark_oak_trapdoor": "Dark Oak Trapdoor", "block.minecraft.dark_oak_wall_hanging_sign": "Dark Oak Wall Hanging Sign", "block.minecraft.dark_oak_wall_sign": "Dark Oak Wall Sign", "block.minecraft.dark_oak_wood": "Dark Oak Wood", "block.minecraft.dark_prismarine": "<PERSON>", "block.minecraft.dark_prismarine_slab": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine_stairs": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "block.minecraft.daylight_detector": "Daylight Detector", "block.minecraft.dead_brain_coral": "Dead Brain Coral", "block.minecraft.dead_brain_coral_block": "Dead Brain Coral Block", "block.minecraft.dead_brain_coral_fan": "Dead Brain Coral Fan", "block.minecraft.dead_brain_coral_wall_fan": "Dead Brain Coral Wall Fan", "block.minecraft.dead_bubble_coral": "Dead Bubble Coral", "block.minecraft.dead_bubble_coral_block": "Dead Bubble Coral Block", "block.minecraft.dead_bubble_coral_fan": "Dead Bubble Coral Fan", "block.minecraft.dead_bubble_coral_wall_fan": "Dead Bubble Coral Wall Fan", "block.minecraft.dead_bush": "Dead Bush", "block.minecraft.dead_fire_coral": "Dead Fire Coral", "block.minecraft.dead_fire_coral_block": "Dead Fire Coral Block", "block.minecraft.dead_fire_coral_fan": "Dead Fire Coral Fan", "block.minecraft.dead_fire_coral_wall_fan": "Dead Fire Coral Wall Fan", "block.minecraft.dead_horn_coral": "Dead Horn Coral", "block.minecraft.dead_horn_coral_block": "Dead Horn Coral Block", "block.minecraft.dead_horn_coral_fan": "Dead Horn Coral Fan", "block.minecraft.dead_horn_coral_wall_fan": "Dead Horn Coral Wall Fan", "block.minecraft.dead_tube_coral": "Dead Tube Coral", "block.minecraft.dead_tube_coral_block": "Dead Tube Coral Block", "block.minecraft.dead_tube_coral_fan": "Dead Tube Coral Fan", "block.minecraft.dead_tube_coral_wall_fan": "Dead Tube Coral Wall Fan", "block.minecraft.decorated_pot": "Decorated Pot", "block.minecraft.deepslate": "Deepslate", "block.minecraft.deepslate_brick_slab": "Deepslate Brick Slab", "block.minecraft.deepslate_brick_stairs": "Deepslate Brick Stairs", "block.minecraft.deepslate_brick_wall": "Deepslate Brick Wall", "block.minecraft.deepslate_bricks": "Deepslate Bricks", "block.minecraft.deepslate_coal_ore": "Deepslate Coal Ore", "block.minecraft.deepslate_copper_ore": "Deepslate Copper Ore", "block.minecraft.deepslate_diamond_ore": "Deepslate Diamond Ore", "block.minecraft.deepslate_emerald_ore": "Deepslate Emerald Ore", "block.minecraft.deepslate_gold_ore": "Deepslate Gold Ore", "block.minecraft.deepslate_iron_ore": "Deepslate Iron Ore", "block.minecraft.deepslate_lapis_ore": "Deepslate Lapis Lazuli Ore", "block.minecraft.deepslate_redstone_ore": "Deepslate Redstone Ore", "block.minecraft.deepslate_tile_slab": "Deepslate Tile Slab", "block.minecraft.deepslate_tile_stairs": "Deepslate Tile Stairs", "block.minecraft.deepslate_tile_wall": "Deepslate Tile Wall", "block.minecraft.deepslate_tiles": "Deepslate Tiles", "block.minecraft.detector_rail": "Detector Rail", "block.minecraft.diamond_block": "Block of Diamond", "block.minecraft.diamond_ore": "Diamond Ore", "block.minecraft.diorite": "Diorite", "block.minecraft.diorite_slab": "Diorite Slab", "block.minecraft.diorite_stairs": "Diorite Stairs", "block.minecraft.diorite_wall": "Diorite Wall", "block.minecraft.dirt": "Dirt", "block.minecraft.dirt_path": "Dirt Path", "block.minecraft.dispenser": "Dispenser", "block.minecraft.dragon_egg": "Dragon Egg", "block.minecraft.dragon_head": "Dragon Head", "block.minecraft.dragon_wall_head": "Dragon Wall Head", "block.minecraft.dried_kelp_block": "Dried Kelp Block", "block.minecraft.dripstone_block": "Dripstone Block", "block.minecraft.dropper": "Dropper", "block.minecraft.emerald_block": "Block of Emerald", "block.minecraft.emerald_ore": "Emerald Ore", "block.minecraft.enchanting_table": "Enchanting Table", "block.minecraft.end_gateway": "End Gateway", "block.minecraft.end_portal": "End Portal", "block.minecraft.end_portal_frame": "End Portal Frame", "block.minecraft.end_rod": "End Rod", "block.minecraft.end_stone": "End Stone", "block.minecraft.end_stone_brick_slab": "End Stone Brick Slab", "block.minecraft.end_stone_brick_stairs": "End Stone Brick Stairs", "block.minecraft.end_stone_brick_wall": "End Stone Brick Wall", "block.minecraft.end_stone_bricks": "End Stone Bricks", "block.minecraft.ender_chest": "<PERSON><PERSON> Chest", "block.minecraft.exposed_copper": "Exposed Copper", "block.minecraft.exposed_cut_copper": "Exposed Cut Copper", "block.minecraft.exposed_cut_copper_slab": "Exposed Cut Copper Slab", "block.minecraft.exposed_cut_copper_stairs": "Exposed Cut Copper Stairs", "block.minecraft.farmland": "Farmland", "block.minecraft.fern": "Fern", "block.minecraft.fire": "Fire", "block.minecraft.fire_coral": "Fire Coral", "block.minecraft.fire_coral_block": "Fire Coral Block", "block.minecraft.fire_coral_fan": "Fire Coral Fan", "block.minecraft.fire_coral_wall_fan": "Fire Coral Wall Fan", "block.minecraft.fletching_table": "Fletching Table", "block.minecraft.flower_pot": "Flower Pot", "block.minecraft.flowering_azalea": "Flowering Azalea", "block.minecraft.flowering_azalea_leaves": "Flowering Azalea Leaves", "block.minecraft.frogspawn": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.frosted_ice": "Frosted Ice", "block.minecraft.furnace": "Furnace", "block.minecraft.gilded_blackstone": "Gilded Blackstone", "block.minecraft.glass": "Glass", "block.minecraft.glass_pane": "Glass Pane", "block.minecraft.glow_lichen": "Glow Lichen", "block.minecraft.glowstone": "Glowstone", "block.minecraft.gold_block": "Block of Gold", "block.minecraft.gold_ore": "Gold Ore", "block.minecraft.granite": "Granite", "block.minecraft.granite_slab": "Granite Slab", "block.minecraft.granite_stairs": "Granite Stairs", "block.minecraft.granite_wall": "Granite Wall", "block.minecraft.grass": "Grass", "block.minecraft.grass_block": "Grass Block", "block.minecraft.gravel": "<PERSON>l", "block.minecraft.gray_banner": "<PERSON>", "block.minecraft.gray_bed": "Gray Bed", "block.minecraft.gray_candle": "<PERSON>", "block.minecraft.gray_candle_cake": "Cake with <PERSON>", "block.minecraft.gray_carpet": "<PERSON> Carpet", "block.minecraft.gray_concrete": "<PERSON>", "block.minecraft.gray_concrete_powder": "<PERSON> Concre<PERSON>", "block.minecraft.gray_glazed_terracotta": "Gray Glazed Terracotta", "block.minecraft.gray_shulker_box": "<PERSON>", "block.minecraft.gray_stained_glass": "<PERSON> Stained Glass", "block.minecraft.gray_stained_glass_pane": "Gray Stained Glass Pane", "block.minecraft.gray_terracotta": "Gray <PERSON>", "block.minecraft.gray_wool": "Gray <PERSON>", "block.minecraft.green_banner": "<PERSON> Banner", "block.minecraft.green_bed": "Green Bed", "block.minecraft.green_candle": "Green Candle", "block.minecraft.green_candle_cake": "Cake with Green Candle", "block.minecraft.green_carpet": "Green Carpet", "block.minecraft.green_concrete": "Green Concrete", "block.minecraft.green_concrete_powder": "Green Concrete Powder", "block.minecraft.green_glazed_terracotta": "Green Glazed Terracotta", "block.minecraft.green_shulker_box": "Green Shulker Box", "block.minecraft.green_stained_glass": "Green Stained Glass", "block.minecraft.green_stained_glass_pane": "Green Stained Glass Pane", "block.minecraft.green_terracotta": "Green Terracotta", "block.minecraft.green_wool": "Green Wool", "block.minecraft.grindstone": "Grindstone", "block.minecraft.hanging_roots": "Hanging Roots", "block.minecraft.hay_block": "<PERSON>", "block.minecraft.heavy_weighted_pressure_plate": "Heavy Weighted Pressure Plate", "block.minecraft.honey_block": "Honey Block", "block.minecraft.honeycomb_block": "Honeycomb Block", "block.minecraft.hopper": "<PERSON>", "block.minecraft.horn_coral": "Horn Coral", "block.minecraft.horn_coral_block": "Horn Coral Block", "block.minecraft.horn_coral_fan": "Horn Coral Fan", "block.minecraft.horn_coral_wall_fan": "Horn <PERSON> Wall Fan", "block.minecraft.ice": "Ice", "block.minecraft.infested_chiseled_stone_bricks": "Infested Chiseled Stone Bricks", "block.minecraft.infested_cobblestone": "Infested Cobblestone", "block.minecraft.infested_cracked_stone_bricks": "Infested Cracked Stone Bricks", "block.minecraft.infested_deepslate": "Infested Deepslate", "block.minecraft.infested_mossy_stone_bricks": "Infested Mossy Stone Bricks", "block.minecraft.infested_stone": "Infested Stone", "block.minecraft.infested_stone_bricks": "Infested Stone Bricks", "block.minecraft.iron_bars": "Iron Bars", "block.minecraft.iron_block": "Block of Iron", "block.minecraft.iron_door": "Iron Door", "block.minecraft.iron_ore": "Iron Ore", "block.minecraft.iron_trapdoor": "Iron Trapdoor", "block.minecraft.jack_o_lantern": "<PERSON>'<PERSON>", "block.minecraft.jigsaw": "Jigsaw Block", "block.minecraft.jukebox": "Jukebox", "block.minecraft.jungle_button": "<PERSON>ton", "block.minecraft.jungle_door": "Jungle Door", "block.minecraft.jungle_fence": "Jungle Fence", "block.minecraft.jungle_fence_gate": "Jungle Fence Gate", "block.minecraft.jungle_hanging_sign": "Jungle Hanging Sign", "block.minecraft.jungle_leaves": "Jungle Leaves", "block.minecraft.jungle_log": "Jungle Log", "block.minecraft.jungle_planks": "Jungle Planks", "block.minecraft.jungle_pressure_plate": "Jungle Pressure Plate", "block.minecraft.jungle_sapling": "Jungle Sapling", "block.minecraft.jungle_sign": "Jungle Sign", "block.minecraft.jungle_slab": "Jungle Slab", "block.minecraft.jungle_stairs": "Jungle Stairs", "block.minecraft.jungle_trapdoor": "Jungle Trapdoor", "block.minecraft.jungle_wall_hanging_sign": "Jungle Wall Hanging Sign", "block.minecraft.jungle_wall_sign": "Jungle Wall Sign", "block.minecraft.jungle_wood": "Jungle Wood", "block.minecraft.kelp": "<PERSON><PERSON><PERSON>", "block.minecraft.kelp_plant": "Kelp Plant", "block.minecraft.ladder": "Ladder", "block.minecraft.lantern": "Lantern", "block.minecraft.lapis_block": "Block of Lapis Lazuli", "block.minecraft.lapis_ore": "Lapis <PERSON> Ore", "block.minecraft.large_amethyst_bud": "Large Amethyst Bud", "block.minecraft.large_fern": "Large Fern", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "<PERSON><PERSON>", "block.minecraft.lectern": "Lectern", "block.minecraft.lever": "Lever", "block.minecraft.light": "Light", "block.minecraft.light_blue_banner": "Light Blue Banner", "block.minecraft.light_blue_bed": "Light Blue Bed", "block.minecraft.light_blue_candle": "Light Blue Candle", "block.minecraft.light_blue_candle_cake": "Cake with Light Blue Candle", "block.minecraft.light_blue_carpet": "Light Blue Carpet", "block.minecraft.light_blue_concrete": "Light Blue Concrete", "block.minecraft.light_blue_concrete_powder": "Light Blue Concrete Powder", "block.minecraft.light_blue_glazed_terracotta": "Light Blue Glazed Terracotta", "block.minecraft.light_blue_shulker_box": "Light Blue Shulker Box", "block.minecraft.light_blue_stained_glass": "Light Blue Stained Glass", "block.minecraft.light_blue_stained_glass_pane": "Light Blue Stained Glass Pane", "block.minecraft.light_blue_terracotta": "Light Blue Terracotta", "block.minecraft.light_blue_wool": "Light Blue Wool", "block.minecraft.light_gray_banner": "<PERSON> Gray Banner", "block.minecraft.light_gray_bed": "Light Gray Bed", "block.minecraft.light_gray_candle": "Light Gray Candle", "block.minecraft.light_gray_candle_cake": "Cake with Light Gray Candle", "block.minecraft.light_gray_carpet": "Light Gray Carpet", "block.minecraft.light_gray_concrete": "Light Gray Concrete", "block.minecraft.light_gray_concrete_powder": "Light Gray Concrete Powder", "block.minecraft.light_gray_glazed_terracotta": "Light Gray Glazed Terracotta", "block.minecraft.light_gray_shulker_box": "Light Gray Shulker Box", "block.minecraft.light_gray_stained_glass": "Light Gray Stained Glass", "block.minecraft.light_gray_stained_glass_pane": "Light Gray Stained Glass Pane", "block.minecraft.light_gray_terracotta": "Light Gray Terracotta", "block.minecraft.light_gray_wool": "Light Gray Wool", "block.minecraft.light_weighted_pressure_plate": "Light Weighted Pressure Plate", "block.minecraft.lightning_rod": "Lightning Rod", "block.minecraft.lilac": "Lilac", "block.minecraft.lily_of_the_valley": "Lily of the Valley", "block.minecraft.lily_pad": "<PERSON>", "block.minecraft.lime_banner": "Lime Banner", "block.minecraft.lime_bed": "Lime Bed", "block.minecraft.lime_candle": "<PERSON><PERSON>", "block.minecraft.lime_candle_cake": "Cake with <PERSON><PERSON> Candle", "block.minecraft.lime_carpet": "Lime Carpet", "block.minecraft.lime_concrete": "Lime Concrete", "block.minecraft.lime_concrete_powder": "Lime Concrete <PERSON>", "block.minecraft.lime_glazed_terracotta": "Lime Glazed Terracotta", "block.minecraft.lime_shulker_box": "<PERSON>e <PERSON>er Box", "block.minecraft.lime_stained_glass": "Lime Stained Glass", "block.minecraft.lime_stained_glass_pane": "Lime Stained Glass Pane", "block.minecraft.lime_terracotta": "Lime Terracotta", "block.minecraft.lime_wool": "Lime Wool", "block.minecraft.lodestone": "Lodestone", "block.minecraft.loom": "Loom", "block.minecraft.magenta_banner": "Magenta Banner", "block.minecraft.magenta_bed": "Magenta Bed", "block.minecraft.magenta_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_candle_cake": "Cake with <PERSON><PERSON><PERSON>dle", "block.minecraft.magenta_carpet": "Magenta Carpet", "block.minecraft.magenta_concrete": "Magenta Concrete", "block.minecraft.magenta_concrete_powder": "Magenta Concrete Powder", "block.minecraft.magenta_glazed_terracotta": "Magenta Glazed Terracotta", "block.minecraft.magenta_shulker_box": "<PERSON><PERSON>a <PERSON>er Box", "block.minecraft.magenta_stained_glass": "Magenta Stained Glass", "block.minecraft.magenta_stained_glass_pane": "Magenta Stained Glass Pane", "block.minecraft.magenta_terracotta": "Magenta Terracotta", "block.minecraft.magenta_wool": "Magenta Wool", "block.minecraft.magma_block": "Magma Block", "block.minecraft.mangrove_button": "Mangrove Button", "block.minecraft.mangrove_door": "Mangrove Door", "block.minecraft.mangrove_fence": "Mangrove Fence", "block.minecraft.mangrove_fence_gate": "Mangrove Fence Gate", "block.minecraft.mangrove_hanging_sign": "Mangrove Hanging Sign", "block.minecraft.mangrove_leaves": "Mangrove Leaves", "block.minecraft.mangrove_log": "Mangrove Log", "block.minecraft.mangrove_planks": "Mangrove Planks", "block.minecraft.mangrove_pressure_plate": "Mangrove Pressure Plate", "block.minecraft.mangrove_propagule": "Mangrove Propagule", "block.minecraft.mangrove_roots": "Mangrove Roots", "block.minecraft.mangrove_sign": "Mangrove Sign", "block.minecraft.mangrove_slab": "Mangrove Slab", "block.minecraft.mangrove_stairs": "Mangrove Stairs", "block.minecraft.mangrove_trapdoor": "Mangrove Trapdoor", "block.minecraft.mangrove_wall_hanging_sign": "Mangrove Wall Hanging Sign", "block.minecraft.mangrove_wall_sign": "Mangrove Wall Sign", "block.minecraft.mangrove_wood": "Mangrove Wood", "block.minecraft.medium_amethyst_bud": "Medium Amethyst Bud", "block.minecraft.melon": "Melon", "block.minecraft.melon_stem": "Melon Stem", "block.minecraft.moss_block": "Moss Block", "block.minecraft.moss_carpet": "Moss Carpet", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON>", "block.minecraft.mossy_cobblestone_slab": "<PERSON><PERSON> Slab", "block.minecraft.mossy_cobblestone_stairs": "Mossy Cobblestone Stairs", "block.minecraft.mossy_cobblestone_wall": "<PERSON><PERSON>", "block.minecraft.mossy_stone_brick_slab": "Mossy Stone Brick Slab", "block.minecraft.mossy_stone_brick_stairs": "Mossy Stone Brick Stairs", "block.minecraft.mossy_stone_brick_wall": "Mossy Stone Brick Wall", "block.minecraft.mossy_stone_bricks": "Mossy Stone Bricks", "block.minecraft.moving_piston": "Moving <PERSON>ston", "block.minecraft.mud": "Mud", "block.minecraft.mud_brick_slab": "Mud <PERSON> Slab", "block.minecraft.mud_brick_stairs": "Mud Brick Stairs", "block.minecraft.mud_brick_wall": "Mud Brick Wall", "block.minecraft.mud_bricks": "Mud Bricks", "block.minecraft.muddy_mangrove_roots": "Muddy Mangrove Roots", "block.minecraft.mushroom_stem": "Mushroom Stem", "block.minecraft.mycelium": "Mycelium", "block.minecraft.nether_brick_fence": "Nether Brick Fence", "block.minecraft.nether_brick_slab": "Nether Brick Slab", "block.minecraft.nether_brick_stairs": "Nether Brick Stairs", "block.minecraft.nether_brick_wall": "Nether Brick Wall", "block.minecraft.nether_bricks": "Nether Bricks", "block.minecraft.nether_gold_ore": "Nether Gold Ore", "block.minecraft.nether_portal": "Nether Portal", "block.minecraft.nether_quartz_ore": "<PERSON><PERSON>", "block.minecraft.nether_sprouts": "Nether Sprouts", "block.minecraft.nether_wart": "Nether Wart", "block.minecraft.nether_wart_block": "Nether Wart Block", "block.minecraft.netherite_block": "Block of Netherite", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Note Block", "block.minecraft.oak_button": "Oak Button", "block.minecraft.oak_door": "Oak Door", "block.minecraft.oak_fence": "Oak Fence", "block.minecraft.oak_fence_gate": "Oak Fence Gate", "block.minecraft.oak_hanging_sign": "Oak Hanging Sign", "block.minecraft.oak_leaves": "Oak Leaves", "block.minecraft.oak_log": "Oak Log", "block.minecraft.oak_planks": "Oak Planks", "block.minecraft.oak_pressure_plate": "Oak Pressure Plate", "block.minecraft.oak_sapling": "Oak Sapling", "block.minecraft.oak_sign": "Oak Sign", "block.minecraft.oak_slab": "Oak Slab", "block.minecraft.oak_stairs": "Oak Stairs", "block.minecraft.oak_trapdoor": "Oak Trapdoor", "block.minecraft.oak_wall_hanging_sign": "Oak Wall Hanging Sign", "block.minecraft.oak_wall_sign": "Oak Wall Sign", "block.minecraft.oak_wood": "Oak Wood", "block.minecraft.observer": "Observer", "block.minecraft.obsidian": "Obsidian", "block.minecraft.ochre_froglight": "Ochre Froglight", "block.minecraft.ominous_banner": "Ominous Banner", "block.minecraft.orange_banner": "Orange Banner", "block.minecraft.orange_bed": "Orange Bed", "block.minecraft.orange_candle": "Orange Candle", "block.minecraft.orange_candle_cake": "Cake with Orange Candle", "block.minecraft.orange_carpet": "Orange Carpet", "block.minecraft.orange_concrete": "Orange Concrete", "block.minecraft.orange_concrete_powder": "Orange Concrete Powder", "block.minecraft.orange_glazed_terracotta": "Orange Glazed Terracotta", "block.minecraft.orange_shulker_box": "Orange Shulker Box", "block.minecraft.orange_stained_glass": "Orange Stained Glass", "block.minecraft.orange_stained_glass_pane": "Orange Stained Glass Pane", "block.minecraft.orange_terracotta": "Orange Terracotta", "block.minecraft.orange_tulip": "Orange Tulip", "block.minecraft.orange_wool": "Orange Wool", "block.minecraft.oxeye_daisy": "Oxeye Daisy", "block.minecraft.oxidized_copper": "Oxidized Copper", "block.minecraft.oxidized_cut_copper": "Oxidized Cut Copper", "block.minecraft.oxidized_cut_copper_slab": "Oxidized Cut Copper Slab", "block.minecraft.oxidized_cut_copper_stairs": "Oxidized Cut Copper Stairs", "block.minecraft.packed_ice": "Packed Ice", "block.minecraft.packed_mud": "Packed Mud", "block.minecraft.pearlescent_froglight": "Pearlescent Froglight", "block.minecraft.peony": "Peony", "block.minecraft.petrified_oak_slab": "Petrified Oak Slab", "block.minecraft.piglin_head": "<PERSON><PERSON>", "block.minecraft.piglin_wall_head": "<PERSON><PERSON>", "block.minecraft.pink_banner": "Pink Banner", "block.minecraft.pink_bed": "Pink Bed", "block.minecraft.pink_candle": "Pink Candle", "block.minecraft.pink_candle_cake": "Cake with <PERSON> Candle", "block.minecraft.pink_carpet": "Pink Carpet", "block.minecraft.pink_concrete": "Pink Concrete", "block.minecraft.pink_concrete_powder": "Pink Concrete Powder", "block.minecraft.pink_glazed_terracotta": "Pink Glazed Terracotta", "block.minecraft.pink_petals": "Pink Petals", "block.minecraft.pink_shulker_box": "Pink Shulker Box", "block.minecraft.pink_stained_glass": "Pink Stained Glass", "block.minecraft.pink_stained_glass_pane": "Pink Stained Glass Pane", "block.minecraft.pink_terracotta": "Pink Terracotta", "block.minecraft.pink_tulip": "<PERSON> Tulip", "block.minecraft.pink_wool": "Pink Wool", "block.minecraft.piston": "<PERSON><PERSON>", "block.minecraft.piston_head": "Piston Head", "block.minecraft.pitcher_crop": "Pitcher C<PERSON>", "block.minecraft.pitcher_plant": "Pitcher Plant", "block.minecraft.player_head": "Player Head", "block.minecraft.player_head.named": "%s's Head", "block.minecraft.player_wall_head": "Player <PERSON>", "block.minecraft.podzol": "Podzol", "block.minecraft.pointed_dripstone": "Pointed Dripstone", "block.minecraft.polished_andesite": "Polished Andesite", "block.minecraft.polished_andesite_slab": "Polished Andesite Slab", "block.minecraft.polished_andesite_stairs": "Polished Andesite Stairs", "block.minecraft.polished_basalt": "Polished Ba<PERSON>t", "block.minecraft.polished_blackstone": "Polished Blackstone", "block.minecraft.polished_blackstone_brick_slab": "Polished Blackstone Brick Slab", "block.minecraft.polished_blackstone_brick_stairs": "Polished Blackstone Brick Stairs", "block.minecraft.polished_blackstone_brick_wall": "Polished Blackstone Brick Wall", "block.minecraft.polished_blackstone_bricks": "Polished Blackstone Bricks", "block.minecraft.polished_blackstone_button": "Polished Blackstone Button", "block.minecraft.polished_blackstone_pressure_plate": "Polished Blackstone Pressure Plate", "block.minecraft.polished_blackstone_slab": "Polished Blackstone Slab", "block.minecraft.polished_blackstone_stairs": "Polished Blackstone Stairs", "block.minecraft.polished_blackstone_wall": "Polished Blackstone Wall", "block.minecraft.polished_deepslate": "Polished Deepslate", "block.minecraft.polished_deepslate_slab": "Polished Deepslate Slab", "block.minecraft.polished_deepslate_stairs": "Polished Deepslate Stairs", "block.minecraft.polished_deepslate_wall": "Polished Deepslate Wall", "block.minecraft.polished_diorite": "Polished Diorite", "block.minecraft.polished_diorite_slab": "Polished Diorite S<PERSON>b", "block.minecraft.polished_diorite_stairs": "Polished Diorite Stairs", "block.minecraft.polished_granite": "Polished Granite", "block.minecraft.polished_granite_slab": "Polished Granite Slab", "block.minecraft.polished_granite_stairs": "Polished Granite Stairs", "block.minecraft.poppy": "<PERSON><PERSON>", "block.minecraft.potatoes": "Potatoes", "block.minecraft.potted_acacia_sapling": "Potted Acacia Sapling", "block.minecraft.potted_allium": "Potted Allium", "block.minecraft.potted_azalea_bush": "Potted Azalea", "block.minecraft.potted_azure_bluet": "Potted Azure Bluet", "block.minecraft.potted_bamboo": "Potted Bamboo", "block.minecraft.potted_birch_sapling": "Potted Birch Sapling", "block.minecraft.potted_blue_orchid": "Potted Blue Orchid", "block.minecraft.potted_brown_mushroom": "Potted Brown Mushroom", "block.minecraft.potted_cactus": "Potted Cactus", "block.minecraft.potted_cherry_sapling": "Potted Cherry Sapling", "block.minecraft.potted_cornflower": "Potted Cornflower", "block.minecraft.potted_crimson_fungus": "Potted Crimson Fungus", "block.minecraft.potted_crimson_roots": "Potted Crimson Roots", "block.minecraft.potted_dandelion": "Potted Dandelion", "block.minecraft.potted_dark_oak_sapling": "Potted Dark Oak Sapling", "block.minecraft.potted_dead_bush": "Potted Dead Bush", "block.minecraft.potted_fern": "Potted Fern", "block.minecraft.potted_flowering_azalea_bush": "Potted Flowering Azalea", "block.minecraft.potted_jungle_sapling": "Potted Jungle Sapling", "block.minecraft.potted_lily_of_the_valley": "Potted Lily of the Valley", "block.minecraft.potted_mangrove_propagule": "Potted Mangrove Propagule", "block.minecraft.potted_oak_sapling": "Potted Oak Sapling", "block.minecraft.potted_orange_tulip": "Potted Orange Tulip", "block.minecraft.potted_oxeye_daisy": "Potted Oxeye Daisy", "block.minecraft.potted_pink_tulip": "Potted Pink Tulip", "block.minecraft.potted_poppy": "Potted Poppy", "block.minecraft.potted_red_mushroom": "Potted Red Mushroom", "block.minecraft.potted_red_tulip": "Potted Red Tulip", "block.minecraft.potted_spruce_sapling": "Potted Spruce Sapling", "block.minecraft.potted_torchflower": "Potted Torchflower", "block.minecraft.potted_warped_fungus": "Potted Warped Fungus", "block.minecraft.potted_warped_roots": "Potted Warped Roots", "block.minecraft.potted_white_tulip": "Potted White Tulip", "block.minecraft.potted_wither_rose": "Potted Wither <PERSON>", "block.minecraft.powder_snow": "Powder Snow", "block.minecraft.powder_snow_cauldron": "Powder Snow Cauldron", "block.minecraft.powered_rail": "Powered Rail", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "Prismarine Brick Slab", "block.minecraft.prismarine_brick_stairs": "Prismarine Brick Stairs", "block.minecraft.prismarine_bricks": "Prismarine <PERSON>s", "block.minecraft.prismarine_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin": "<PERSON><PERSON><PERSON>", "block.minecraft.pumpkin_stem": "<PERSON><PERSON><PERSON>", "block.minecraft.purple_banner": "<PERSON> Banner", "block.minecraft.purple_bed": "Purple Bed", "block.minecraft.purple_candle": "Purple Candle", "block.minecraft.purple_candle_cake": "Cake with <PERSON> Candle", "block.minecraft.purple_carpet": "Purple Carpet", "block.minecraft.purple_concrete": "Purple Concrete", "block.minecraft.purple_concrete_powder": "Purple Concrete Powder", "block.minecraft.purple_glazed_terracotta": "Purple Glazed Terracotta", "block.minecraft.purple_shulker_box": "Purple Shulker Box", "block.minecraft.purple_stained_glass": "Purple Stained Glass", "block.minecraft.purple_stained_glass_pane": "Purple Stained Glass Pane", "block.minecraft.purple_terracotta": "Purple Terracotta", "block.minecraft.purple_wool": "Purple Wool", "block.minecraft.purpur_block": "Purpur Block", "block.minecraft.purpur_pillar": "Purpur Pillar", "block.minecraft.purpur_slab": "Purpur Slab", "block.minecraft.purpur_stairs": "Purpur Stairs", "block.minecraft.quartz_block": "Block of Quartz", "block.minecraft.quartz_bricks": "Quartz Bricks", "block.minecraft.quartz_pillar": "Quartz <PERSON>", "block.minecraft.quartz_slab": "Quartz Slab", "block.minecraft.quartz_stairs": "Quartz Stairs", "block.minecraft.rail": "Rail", "block.minecraft.raw_copper_block": "Block of Raw Copper", "block.minecraft.raw_gold_block": "Block of Raw Gold", "block.minecraft.raw_iron_block": "Block of Raw Iron", "block.minecraft.red_banner": "Red Banner", "block.minecraft.red_bed": "Red Bed", "block.minecraft.red_candle": "<PERSON> Candle", "block.minecraft.red_candle_cake": "Cake with <PERSON> Candle", "block.minecraft.red_carpet": "Red Carpet", "block.minecraft.red_concrete": "Red Concrete", "block.minecraft.red_concrete_powder": "Red Concrete Powder", "block.minecraft.red_glazed_terracotta": "Red Glazed Terracotta", "block.minecraft.red_mushroom": "Red Mushroom", "block.minecraft.red_mushroom_block": "Red Mushroom Block", "block.minecraft.red_nether_brick_slab": "Red Nether Brick Slab", "block.minecraft.red_nether_brick_stairs": "Red Nether Brick Stairs", "block.minecraft.red_nether_brick_wall": "Red Nether Brick Wall", "block.minecraft.red_nether_bricks": "Red Nether Bricks", "block.minecraft.red_sand": "Red Sand", "block.minecraft.red_sandstone": "Red Sandstone", "block.minecraft.red_sandstone_slab": "Red Sandstone Slab", "block.minecraft.red_sandstone_stairs": "Red Sandstone Stairs", "block.minecraft.red_sandstone_wall": "Red Sandstone Wall", "block.minecraft.red_shulker_box": "Red Shulker Box", "block.minecraft.red_stained_glass": "Red Stained Glass", "block.minecraft.red_stained_glass_pane": "Red Stained Glass Pane", "block.minecraft.red_terracotta": "Red Terracotta", "block.minecraft.red_tulip": "<PERSON>lip", "block.minecraft.red_wool": "Red Wool", "block.minecraft.redstone_block": "Block of Redstone", "block.minecraft.redstone_lamp": "Redstone Lamp", "block.minecraft.redstone_ore": "Redstone Ore", "block.minecraft.redstone_torch": "Redstone Torch", "block.minecraft.redstone_wall_torch": "Redstone Wall Torch", "block.minecraft.redstone_wire": "Redstone Wire", "block.minecraft.reinforced_deepslate": "Reinforced Deepslate", "block.minecraft.repeater": "Redstone Repeater", "block.minecraft.repeating_command_block": "Repeating Command Block", "block.minecraft.respawn_anchor": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.rooted_dirt": "Rooted Dirt", "block.minecraft.rose_bush": "<PERSON>", "block.minecraft.sand": "Sand", "block.minecraft.sandstone": "Sandstone", "block.minecraft.sandstone_slab": "Sandstone Slab", "block.minecraft.sandstone_stairs": "Sandstone Stairs", "block.minecraft.sandstone_wall": "Sandstone Wall", "block.minecraft.scaffolding": "Scaffolding", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Sculk Catalyst", "block.minecraft.sculk_sensor": "Sculk Sensor", "block.minecraft.sculk_shrieker": "<PERSON><PERSON><PERSON>", "block.minecraft.sculk_vein": "Sculk Vein", "block.minecraft.sea_lantern": "Sea Lantern", "block.minecraft.sea_pickle": "<PERSON>", "block.minecraft.seagrass": "Seagrass", "block.minecraft.set_spawn": "Respawn point set", "block.minecraft.shroomlight": "Shroomlight", "block.minecraft.shulker_box": "Shulker Box", "block.minecraft.skeleton_skull": "Skeleton Skull", "block.minecraft.skeleton_wall_skull": "Skeleton Wall Skull", "block.minecraft.slime_block": "Slime Block", "block.minecraft.small_amethyst_bud": "Small Amethyst Bud", "block.minecraft.small_dripleaf": "Small Dripleaf", "block.minecraft.smithing_table": "Smithing Table", "block.minecraft.smoker": "Smoker", "block.minecraft.smooth_basalt": "Smooth Basalt", "block.minecraft.smooth_quartz": "Smooth Quartz Block", "block.minecraft.smooth_quartz_slab": "Smooth Quartz Slab", "block.minecraft.smooth_quartz_stairs": "Smooth Quartz Stairs", "block.minecraft.smooth_red_sandstone": "Smooth Red Sandstone", "block.minecraft.smooth_red_sandstone_slab": "Smooth Red Sandstone Slab", "block.minecraft.smooth_red_sandstone_stairs": "Smooth Red Sandstone Stairs", "block.minecraft.smooth_sandstone": "Smooth Sandstone", "block.minecraft.smooth_sandstone_slab": "Smooth Sandstone Slab", "block.minecraft.smooth_sandstone_stairs": "Smooth Sandstone Stairs", "block.minecraft.smooth_stone": "Smooth Stone", "block.minecraft.smooth_stone_slab": "Smooth Stone Slab", "block.minecraft.sniffer_egg": "Sniffer Egg", "block.minecraft.snow": "Snow", "block.minecraft.snow_block": "Snow Block", "block.minecraft.soul_campfire": "Soul Campfire", "block.minecraft.soul_fire": "Soul Fire", "block.minecraft.soul_lantern": "Soul Lantern", "block.minecraft.soul_sand": "Soul Sand", "block.minecraft.soul_soil": "Soul Soil", "block.minecraft.soul_torch": "Soul Torch", "block.minecraft.soul_wall_torch": "Soul Wall Torch", "block.minecraft.spawn.not_valid": "You have no home bed or charged respawn anchor, or it was obstructed", "block.minecraft.spawner": "Monster Spawner", "block.minecraft.spawner.desc1": "Interact with Spawn Egg:", "block.minecraft.spawner.desc2": "Sets Mob Type", "block.minecraft.sponge": "Sponge", "block.minecraft.spore_blossom": "Spore Blossom", "block.minecraft.spruce_button": "Spruce Button", "block.minecraft.spruce_door": "Spruce Door", "block.minecraft.spruce_fence": "Spruce Fence", "block.minecraft.spruce_fence_gate": "Spruce Fence Gate", "block.minecraft.spruce_hanging_sign": "Spruce Hanging Sign", "block.minecraft.spruce_leaves": "Spruce Leaves", "block.minecraft.spruce_log": "Spruce Log", "block.minecraft.spruce_planks": "Spruce Planks", "block.minecraft.spruce_pressure_plate": "Spruce Pressure Plate", "block.minecraft.spruce_sapling": "Spruce Sapling", "block.minecraft.spruce_sign": "Spruce Sign", "block.minecraft.spruce_slab": "Spruce Slab", "block.minecraft.spruce_stairs": "Spruce Stairs", "block.minecraft.spruce_trapdoor": "Spruce Trapdoor", "block.minecraft.spruce_wall_hanging_sign": "Spruce Wall Hanging Sign", "block.minecraft.spruce_wall_sign": "Spruce Wall Sign", "block.minecraft.spruce_wood": "Spruce Wood", "block.minecraft.sticky_piston": "<PERSON><PERSON>", "block.minecraft.stone": "Stone", "block.minecraft.stone_brick_slab": "Stone Brick Slab", "block.minecraft.stone_brick_stairs": "Stone Brick Stairs", "block.minecraft.stone_brick_wall": "Stone Brick Wall", "block.minecraft.stone_bricks": "Stone Bricks", "block.minecraft.stone_button": "<PERSON>", "block.minecraft.stone_pressure_plate": "Stone Pressure Plate", "block.minecraft.stone_slab": "<PERSON> Slab", "block.minecraft.stone_stairs": "Stone Stairs", "block.minecraft.stonecutter": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_acacia_log": "Stripped Acacia Log", "block.minecraft.stripped_acacia_wood": "Stripped Acacia Wood", "block.minecraft.stripped_bamboo_block": "Block of Stripped Bamboo", "block.minecraft.stripped_birch_log": "Stripped Birch Log", "block.minecraft.stripped_birch_wood": "Stripped Birch Wood", "block.minecraft.stripped_cherry_log": "Stripped Cherry Log", "block.minecraft.stripped_cherry_wood": "Stripped Cherry Wood", "block.minecraft.stripped_crimson_hyphae": "Stripped Crimson Hyphae", "block.minecraft.stripped_crimson_stem": "Stripped Crimson Stem", "block.minecraft.stripped_dark_oak_log": "Stripped Dark Oak Log", "block.minecraft.stripped_dark_oak_wood": "Stripped Dark Oak Wood", "block.minecraft.stripped_jungle_log": "Stripped Jungle Log", "block.minecraft.stripped_jungle_wood": "Stripped Jungle Wood", "block.minecraft.stripped_mangrove_log": "Stripped Mangrove Log", "block.minecraft.stripped_mangrove_wood": "Stripped Mangrove Wood", "block.minecraft.stripped_oak_log": "Stripped Oak Log", "block.minecraft.stripped_oak_wood": "Stripped Oak Wood", "block.minecraft.stripped_spruce_log": "Stripped Spruce Log", "block.minecraft.stripped_spruce_wood": "Stripped Spruce Wood", "block.minecraft.stripped_warped_hyphae": "Stripped Warped Hyphae", "block.minecraft.stripped_warped_stem": "Stripped Warped Stem", "block.minecraft.structure_block": "Structure Block", "block.minecraft.structure_void": "Structure Void", "block.minecraft.sugar_cane": "Sugar Cane", "block.minecraft.sunflower": "Sunflower", "block.minecraft.suspicious_gravel": "Suspicious Gravel", "block.minecraft.suspicious_sand": "Suspicious Sand", "block.minecraft.sweet_berry_bush": "Sweet <PERSON>", "block.minecraft.tall_grass": "Tall Grass", "block.minecraft.tall_seagrass": "Tall Seagrass", "block.minecraft.target": "Target", "block.minecraft.terracotta": "Terracotta", "block.minecraft.tinted_glass": "Tinted Glass", "block.minecraft.tnt": "TNT", "block.minecraft.torch": "<PERSON>ch", "block.minecraft.torchflower": "Torch<PERSON>", "block.minecraft.torchflower_crop": "Torchflower <PERSON>", "block.minecraft.trapped_chest": "Trapped Chest", "block.minecraft.tripwire": "Tripwire", "block.minecraft.tripwire_hook": "Tripwire Hook", "block.minecraft.tube_coral": "Tube Coral", "block.minecraft.tube_coral_block": "Tube Coral Block", "block.minecraft.tube_coral_fan": "Tube Coral Fan", "block.minecraft.tube_coral_wall_fan": "Tube Coral Wall Fan", "block.minecraft.tuff": "<PERSON><PERSON>", "block.minecraft.turtle_egg": "Turtle Egg", "block.minecraft.twisting_vines": "Twisting Vines", "block.minecraft.twisting_vines_plant": "Twisting Vines Plant", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON>", "block.minecraft.vine": "Vines", "block.minecraft.void_air": "Void Air", "block.minecraft.wall_torch": "<PERSON>", "block.minecraft.warped_button": "Warped <PERSON>", "block.minecraft.warped_door": "Warped Door", "block.minecraft.warped_fence": "Warped <PERSON>", "block.minecraft.warped_fence_gate": "Warped Fence Gate", "block.minecraft.warped_fungus": "Warped Fungus", "block.minecraft.warped_hanging_sign": "Warped Hanging Sign", "block.minecraft.warped_hyphae": "Warped Hyphae", "block.minecraft.warped_nylium": "Warped Nylium", "block.minecraft.warped_planks": "Warped Planks", "block.minecraft.warped_pressure_plate": "Warped Pressure Plate", "block.minecraft.warped_roots": "Warped Roots", "block.minecraft.warped_sign": "Warped Sign", "block.minecraft.warped_slab": "Warped Slab", "block.minecraft.warped_stairs": "Warped Stairs", "block.minecraft.warped_stem": "Warped Stem", "block.minecraft.warped_trapdoor": "Warped Trapdoor", "block.minecraft.warped_wall_hanging_sign": "Warped Wall Hanging Sign", "block.minecraft.warped_wall_sign": "Warped Wall Sign", "block.minecraft.warped_wart_block": "Warped Wart Block", "block.minecraft.water": "Water", "block.minecraft.water_cauldron": "Water Cauldron", "block.minecraft.waxed_copper_block": "Waxed Block of Copper", "block.minecraft.waxed_cut_copper": "Waxed Cut Copper", "block.minecraft.waxed_cut_copper_slab": "Waxed Cut Copper Slab", "block.minecraft.waxed_cut_copper_stairs": "Waxed Cut Copper Stairs", "block.minecraft.waxed_exposed_copper": "Waxed Exposed Copper", "block.minecraft.waxed_exposed_cut_copper": "Waxed Exposed Cut Copper", "block.minecraft.waxed_exposed_cut_copper_slab": "Waxed Exposed Cut Copper Slab", "block.minecraft.waxed_exposed_cut_copper_stairs": "Waxed Exposed Cut Copper Stairs", "block.minecraft.waxed_oxidized_copper": "Waxed Oxidized Copper", "block.minecraft.waxed_oxidized_cut_copper": "Waxed Oxidized Cut Copper", "block.minecraft.waxed_oxidized_cut_copper_slab": "Waxed Oxidized Cut Copper Slab", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Waxed Oxidized Cut Copper Stairs", "block.minecraft.waxed_weathered_copper": "Waxed Weathered Copper", "block.minecraft.waxed_weathered_cut_copper": "Waxed Weathered Cut Copper", "block.minecraft.waxed_weathered_cut_copper_slab": "Waxed Weathered Cut Copper Slab", "block.minecraft.waxed_weathered_cut_copper_stairs": "Waxed Weathered Cut Copper Stairs", "block.minecraft.weathered_copper": "Weathered Copper", "block.minecraft.weathered_cut_copper": "Weathered Cut Copper", "block.minecraft.weathered_cut_copper_slab": "Weathered Cut Copper Slab", "block.minecraft.weathered_cut_copper_stairs": "Weathered Cut Copper Stairs", "block.minecraft.weeping_vines": "Weeping Vines", "block.minecraft.weeping_vines_plant": "Weeping Vines Plant", "block.minecraft.wet_sponge": "Wet Sponge", "block.minecraft.wheat": "Wheat Crops", "block.minecraft.white_banner": "White Banner", "block.minecraft.white_bed": "White Bed", "block.minecraft.white_candle": "White Candle", "block.minecraft.white_candle_cake": "Cake with White Candle", "block.minecraft.white_carpet": "White Carpet", "block.minecraft.white_concrete": "White Concrete", "block.minecraft.white_concrete_powder": "White Concrete Powder", "block.minecraft.white_glazed_terracotta": "White Glazed Terracotta", "block.minecraft.white_shulker_box": "White Shulker Box", "block.minecraft.white_stained_glass": "White Stained Glass", "block.minecraft.white_stained_glass_pane": "White Stained Glass Pane", "block.minecraft.white_terracotta": "White Terracotta", "block.minecraft.white_tulip": "White Tulip", "block.minecraft.white_wool": "White Wool", "block.minecraft.wither_rose": "<PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "Wither Skeleton Skull", "block.minecraft.wither_skeleton_wall_skull": "Wither Skeleton Wall Skull", "block.minecraft.yellow_banner": "Yellow Banner", "block.minecraft.yellow_bed": "Yellow Bed", "block.minecraft.yellow_candle": "Yellow Candle", "block.minecraft.yellow_candle_cake": "Cake with Yellow Candle", "block.minecraft.yellow_carpet": "Yellow Carpet", "block.minecraft.yellow_concrete": "Yellow Concrete", "block.minecraft.yellow_concrete_powder": "Yellow Concrete Powder", "block.minecraft.yellow_glazed_terracotta": "Yellow Glazed Terracotta", "block.minecraft.yellow_shulker_box": "Yellow Shulker Box", "block.minecraft.yellow_stained_glass": "Yellow Stained Glass", "block.minecraft.yellow_stained_glass_pane": "Yellow Stained Glass Pane", "block.minecraft.yellow_terracotta": "Yellow Terracotta", "block.minecraft.yellow_wool": "Yellow Wool", "block.minecraft.zombie_head": "Zombie Head", "block.minecraft.zombie_wall_head": "<PERSON> Head", "book.byAuthor": "by %1$s", "book.editTitle": "Enter Book Title:", "book.finalizeButton": "Sign and Close", "book.finalizeWarning": "Note! When you sign the book, it will no longer be editable.", "book.generation.0": "Original", "book.generation.1": "Copy of original", "book.generation.2": "Copy of a copy", "book.generation.3": "Tattered", "book.invalid.tag": "* Invalid book tag *", "book.pageIndicator": "Page %1$s of %2$s", "book.signButton": "Sign", "build.tooHigh": "Height limit for building is %s", "chat_screen.message": "Message to send: %s", "chat_screen.title": "Chat screen", "chat_screen.usage": "Input message and press Enter to send", "chat.cannotSend": "Cannot send chat message", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Click to teleport", "chat.copy": "Copy to Clipboard", "chat.copy.click": "Click to <PERSON><PERSON> to Clipboard", "chat.deleted_marker": "This chat message has been deleted by the server.", "chat.disabled.chain_broken": "<PERSON><PERSON> disabled due to broken chain. Please try reconnecting.", "chat.disabled.expiredProfileKey": "<PERSON><PERSON> disabled due to expired profile public key. Please try reconnecting.", "chat.disabled.launcher": "Chat disabled by launcher option. Cannot send message.", "chat.disabled.missingProfileKey": "<PERSON><PERSON> disabled due to missing profile public key. Please try reconnecting.", "chat.disabled.options": "Chat disabled in client options.", "chat.disabled.profile": "Chat not allowed by account settings. Press '%s' again for more information.", "chat.disabled.profile.moreInfo": "Chat not allowed by account settings. Cannot send or view messages.", "chat.editBox": "chat", "chat.filtered": "Filtered by the server.", "chat.filtered_full": "The server has hidden your message for some players.", "chat.link.confirm": "Are you sure you want to open the following website?", "chat.link.confirmTrusted": "Do you want to open this link or copy it to your clipboard?", "chat.link.open": "Open in Browser", "chat.link.warning": "Never open links from people that you don't trust!", "chat.queue": "[+%s pending lines]", "chat.square_brackets": "[%s]", "chat.tag.modified": "Message modified by the server. Original:", "chat.tag.not_secure": "Unverified message. Cannot be reported.", "chat.tag.system": "Server message. Cannot be reported.", "chat.tag.system_single_player": "Server message.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s has completed the challenge %s", "chat.type.advancement.goal": "%s has reached the goal %s", "chat.type.advancement.task": "%s has made the advancement %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Message Team", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s says %s", "clear.failed.multiple": "No items were found on %s players", "clear.failed.single": "No items were found on player %s", "color.minecraft.black": "Black", "color.minecraft.blue": "Blue", "color.minecraft.brown": "<PERSON>", "color.minecraft.cyan": "<PERSON><PERSON>", "color.minecraft.gray": "<PERSON>", "color.minecraft.green": "Green", "color.minecraft.light_blue": "Light Blue", "color.minecraft.light_gray": "Light Gray", "color.minecraft.lime": "Lime", "color.minecraft.magenta": "Ma<PERSON><PERSON>", "color.minecraft.orange": "Orange", "color.minecraft.pink": "Pink", "color.minecraft.purple": "Purple", "color.minecraft.red": "Red", "color.minecraft.white": "White", "color.minecraft.yellow": "Yellow", "command.context.here": "<--[HERE]", "command.context.parse_error": "%s at position %s: %s", "command.exception": "Could not parse command: %s", "command.expected.separator": "Expected whitespace to end one argument, but found trailing data", "command.failed": "An unexpected error occurred trying to execute that command", "command.unknown.argument": "Incorrect argument for command", "command.unknown.command": "Unknown or incomplete command, see below for error", "commands.advancement.advancementNotFound": "No advancement was found by the name '%1$s'", "commands.advancement.criterionNotFound": "The advancement %1$s does not contain the criterion '%2$s'", "commands.advancement.grant.criterion.to.many.failure": "Couldn't grant criterion '%s' of advancement %s to %s players as they already have it", "commands.advancement.grant.criterion.to.many.success": "Granted criterion '%s' of advancement %s to %s players", "commands.advancement.grant.criterion.to.one.failure": "Couldn't grant criterion '%s' of advancement %s to %s as they already have it", "commands.advancement.grant.criterion.to.one.success": "Granted criterion '%s' of advancement %s to %s", "commands.advancement.grant.many.to.many.failure": "Couldn't grant %s advancements to %s players as they already have them", "commands.advancement.grant.many.to.many.success": "Granted %s advancements to %s players", "commands.advancement.grant.many.to.one.failure": "Couldn't grant %s advancements to %s as they already have them", "commands.advancement.grant.many.to.one.success": "Granted %s advancements to %s", "commands.advancement.grant.one.to.many.failure": "Couldn't grant advancement %s to %s players as they already have it", "commands.advancement.grant.one.to.many.success": "Granted the advancement %s to %s players", "commands.advancement.grant.one.to.one.failure": "Couldn't grant advancement %s to %s as they already have it", "commands.advancement.grant.one.to.one.success": "Granted the advancement %s to %s", "commands.advancement.revoke.criterion.to.many.failure": "Couldn't revoke criterion '%s' of advancement %s from %s players as they don't have it", "commands.advancement.revoke.criterion.to.many.success": "Revoked criterion '%s' of advancement %s from %s players", "commands.advancement.revoke.criterion.to.one.failure": "Couldn't revoke criterion '%s' of advancement %s from %s as they don't have it", "commands.advancement.revoke.criterion.to.one.success": "Revoked criterion '%s' of advancement %s from %s", "commands.advancement.revoke.many.to.many.failure": "Couldn't revoke %s advancements from %s players as they don't have them", "commands.advancement.revoke.many.to.many.success": "Revoked %s advancements from %s players", "commands.advancement.revoke.many.to.one.failure": "Couldn't revoke %s advancements from %s as they don't have them", "commands.advancement.revoke.many.to.one.success": "Revoked %s advancements from %s", "commands.advancement.revoke.one.to.many.failure": "Couldn't revoke advancement %s from %s players as they don't have it", "commands.advancement.revoke.one.to.many.success": "Revoked the advancement %s from %s players", "commands.advancement.revoke.one.to.one.failure": "Couldn't revoke advancement %s from %s as they don't have it", "commands.advancement.revoke.one.to.one.success": "Revoked the advancement %s from %s", "commands.attribute.base_value.get.success": "Base value of attribute %s for entity %s is %s", "commands.attribute.base_value.set.success": "Base value for attribute %s for entity %s set to %s", "commands.attribute.failed.entity": "%s is not a valid entity for this command", "commands.attribute.failed.modifier_already_present": "Modifier %s is already present on attribute %s for entity %s", "commands.attribute.failed.no_attribute": "Entity %s has no attribute %s", "commands.attribute.failed.no_modifier": "Attribute %s for entity %s has no modifier %s", "commands.attribute.modifier.add.success": "Added modifier %s to attribute %s for entity %s", "commands.attribute.modifier.remove.success": "Removed modifier %s from attribute %s for entity %s", "commands.attribute.modifier.value.get.success": "Value of modifier %s on attribute %s for entity %s is %s", "commands.attribute.value.get.success": "Value of attribute %s for entity %s is %s", "commands.ban.failed": "Nothing changed. The player is already banned", "commands.ban.success": "Banned %s: %s", "commands.banip.failed": "Nothing changed. That IP is already banned", "commands.banip.info": "This ban affects %s player(s): %s", "commands.banip.invalid": "Invalid IP address or unknown player", "commands.banip.success": "Banned IP %s: %s", "commands.banlist.entry": "%s was banned by %s: %s", "commands.banlist.list": "There are %s ban(s):", "commands.banlist.none": "There are no bans", "commands.bossbar.create.failed": "A bossbar already exists with the ID '%s'", "commands.bossbar.create.success": "Created custom bossbar %s", "commands.bossbar.get.max": "Custom bossbar %s has a maximum of %s", "commands.bossbar.get.players.none": "Custom bossbar %s has no players currently online", "commands.bossbar.get.players.some": "Custom bossbar %s has %s player(s) currently online: %s", "commands.bossbar.get.value": "Custom bossbar %s has a value of %s", "commands.bossbar.get.visible.hidden": "Custom bossbar %s is currently hidden", "commands.bossbar.get.visible.visible": "Custom bossbar %s is currently shown", "commands.bossbar.list.bars.none": "There are no custom bossbars active", "commands.bossbar.list.bars.some": "There are %s custom bossbar(s) active: %s", "commands.bossbar.remove.success": "Removed custom bossbar %s", "commands.bossbar.set.color.success": "Custom bossbar %s has changed color", "commands.bossbar.set.color.unchanged": "Nothing changed. That's already the color of this bossbar", "commands.bossbar.set.max.success": "Custom bossbar %s has changed maximum to %s", "commands.bossbar.set.max.unchanged": "Nothing changed. That's already the max of this bossbar", "commands.bossbar.set.name.success": "Custom bossbar %s has been renamed", "commands.bossbar.set.name.unchanged": "Nothing changed. That's already the name of this bossbar", "commands.bossbar.set.players.success.none": "Custom bossbar %s no longer has any players", "commands.bossbar.set.players.success.some": "Custom bossbar %s now has %s player(s): %s", "commands.bossbar.set.players.unchanged": "Nothing changed. Those players are already on the bossbar with nobody to add or remove", "commands.bossbar.set.style.success": "Custom bossbar %s has changed style", "commands.bossbar.set.style.unchanged": "Nothing changed. That's already the style of this bossbar", "commands.bossbar.set.value.success": "Custom bossbar %s has changed value to %s", "commands.bossbar.set.value.unchanged": "Nothing changed. That's already the value of this bossbar", "commands.bossbar.set.visibility.unchanged.hidden": "Nothing changed. The bossbar is already hidden", "commands.bossbar.set.visibility.unchanged.visible": "Nothing changed. The bossbar is already visible", "commands.bossbar.set.visible.success.hidden": "Custom bossbar %s is now hidden", "commands.bossbar.set.visible.success.visible": "Custom bossbar %s is now visible", "commands.bossbar.unknown": "No bossbar exists with the ID '%s'", "commands.clear.success.multiple": "Removed %s item(s) from %s players", "commands.clear.success.single": "Removed %s item(s) from player %s", "commands.clear.test.multiple": "Found %s matching item(s) on %s players", "commands.clear.test.single": "Found %s matching item(s) on player %s", "commands.clone.failed": "No blocks were cloned", "commands.clone.overlap": "The source and destination areas cannot overlap", "commands.clone.success": "Successfully cloned %s block(s)", "commands.clone.toobig": "Too many blocks in the specified area (maximum %s, specified %s)", "commands.damage.invulnerable": "Target is invulnerable to the given damage type", "commands.damage.success": "Applied %s damage to %s", "commands.data.block.get": "%s on block %s, %s, %s after scale factor of %s is %s", "commands.data.block.invalid": "The target block is not a block entity", "commands.data.block.modified": "Modified block data of %s, %s, %s", "commands.data.block.query": "%s, %s, %s has the following block data: %s", "commands.data.entity.get": "%s on %s after scale factor of %s is %s", "commands.data.entity.invalid": "Unable to modify player data", "commands.data.entity.modified": "Modified entity data of %s", "commands.data.entity.query": "%s has the following entity data: %s", "commands.data.get.invalid": "Can't get %s; only numeric tags are allowed", "commands.data.get.multiple": "This argument accepts a single NBT value", "commands.data.get.unknown": "Can't get %s; tag doesn't exist", "commands.data.merge.failed": "Nothing changed. The specified properties already have these values", "commands.data.modify.expected_list": "Expected list, got: %s", "commands.data.modify.expected_object": "Expected object, got: %s", "commands.data.modify.expected_value": "Expected value, got: %s", "commands.data.modify.invalid_index": "Invalid list index: %s", "commands.data.modify.invalid_substring": "Invalid substring indices: %s to %s", "commands.data.storage.get": "%s in storage %s after scale factor of %s is %s", "commands.data.storage.modified": "Modified storage %s", "commands.data.storage.query": "Storage %s has the following contents: %s", "commands.datapack.disable.failed": "Pack '%s' is not enabled!", "commands.datapack.enable.failed": "Pack '%s' is already enabled!", "commands.datapack.enable.failed.no_flags": "Pack '%s' cannot be enabled, since required flags are not enabled in this world: %s!", "commands.datapack.list.available.none": "There are no more data packs available", "commands.datapack.list.available.success": "There are %s data pack(s) available: %s", "commands.datapack.list.enabled.none": "There are no data packs enabled", "commands.datapack.list.enabled.success": "There are %s data pack(s) enabled: %s", "commands.datapack.modify.disable": "Disabling data pack %s", "commands.datapack.modify.enable": "Enabling data pack %s", "commands.datapack.unknown": "Unknown data pack '%s'", "commands.debug.alreadyRunning": "The tick profiler is already started", "commands.debug.function.noRecursion": "Can't trace from inside of function", "commands.debug.function.success.multiple": "Traced %s command(s) from %s functions to output file %s", "commands.debug.function.success.single": "Traced %s command(s) from function '%s' to output file %s", "commands.debug.function.traceFailed": "Failed to trace function", "commands.debug.notRunning": "The tick profiler hasn't started", "commands.debug.started": "Started tick profiling", "commands.debug.stopped": "Stopped tick profiling after %s seconds and %s ticks (%s ticks per second)", "commands.defaultgamemode.success": "The default game mode is now %s", "commands.deop.failed": "Nothing changed. The player is not an operator", "commands.deop.success": "Made %s no longer a server operator", "commands.difficulty.failure": "The difficulty did not change; it is already set to %s", "commands.difficulty.query": "The difficulty is %s", "commands.difficulty.success": "The difficulty has been set to %s", "commands.drop.no_held_items": "Entity can't hold any items", "commands.drop.no_loot_table": "Entity %s has no loot table", "commands.drop.success.multiple": "Dropped %s items", "commands.drop.success.multiple_with_table": "Dropped %s items from loot table %s", "commands.drop.success.single": "Dropped %s %s", "commands.drop.success.single_with_table": "Dropped %s %s from loot table %s", "commands.effect.clear.everything.failed": "Target has no effects to remove", "commands.effect.clear.everything.success.multiple": "Removed every effect from %s targets", "commands.effect.clear.everything.success.single": "Removed every effect from %s", "commands.effect.clear.specific.failed": "Target doesn't have the requested effect", "commands.effect.clear.specific.success.multiple": "Removed effect %s from %s targets", "commands.effect.clear.specific.success.single": "Removed effect %s from %s", "commands.effect.give.failed": "Unable to apply this effect (target is either immune to effects, or has something stronger)", "commands.effect.give.success.multiple": "Applied effect %s to %s targets", "commands.effect.give.success.single": "Applied effect %s to %s", "commands.enchant.failed": "Nothing changed. Targets either have no item in their hands or the enchantment could not be applied", "commands.enchant.failed.entity": "%s is not a valid entity for this command", "commands.enchant.failed.incompatible": "%s cannot support that enchantment", "commands.enchant.failed.itemless": "%s is not holding any item", "commands.enchant.failed.level": "%s is higher than the maximum level of %s supported by that enchantment", "commands.enchant.success.multiple": "Applied enchantment %s to %s entities", "commands.enchant.success.single": "Applied enchantment %s to %s's item", "commands.execute.blocks.toobig": "Too many blocks in the specified area (maximum %s, specified %s)", "commands.execute.conditional.fail": "Test failed", "commands.execute.conditional.fail_count": "Test failed, count: %s", "commands.execute.conditional.pass": "Test passed", "commands.execute.conditional.pass_count": "Test passed, count: %s", "commands.experience.add.levels.success.multiple": "Gave %s experience levels to %s players", "commands.experience.add.levels.success.single": "Gave %s experience levels to %s", "commands.experience.add.points.success.multiple": "Gave %s experience points to %s players", "commands.experience.add.points.success.single": "Gave %s experience points to %s", "commands.experience.query.levels": "%s has %s experience levels", "commands.experience.query.points": "%s has %s experience points", "commands.experience.set.levels.success.multiple": "Set %s experience levels on %s players", "commands.experience.set.levels.success.single": "Set %s experience levels on %s", "commands.experience.set.points.invalid": "<PERSON><PERSON> set experience points above the maximum points for the player's current level", "commands.experience.set.points.success.multiple": "Set %s experience points on %s players", "commands.experience.set.points.success.single": "Set %s experience points on %s", "commands.fill.failed": "No blocks were filled", "commands.fill.success": "Successfully filled %s block(s)", "commands.fill.toobig": "Too many blocks in the specified area (maximum %s, specified %s)", "commands.fillbiome.success": "Biomes set between %s, %s, %s and %s, %s, %s", "commands.fillbiome.success.count": "%s biome entry/entries set between %s, %s, %s and %s, %s, %s", "commands.fillbiome.toobig": "Too many blocks in the specified volume (maximum %s, specified %s)", "commands.forceload.added.failure": "No chunks were marked for force loading", "commands.forceload.added.multiple": "Marked %s chunks in %s from %s to %s to be force loaded", "commands.forceload.added.none": "No force loaded chunks were found in %s", "commands.forceload.added.single": "Marked chunk %s in %s to be force loaded", "commands.forceload.list.multiple": "%s force loaded chunks were found in %s at: %s", "commands.forceload.list.single": "A force loaded chunk was found in %s at: %s", "commands.forceload.query.failure": "Chunk at %s in %s is not marked for force loading", "commands.forceload.query.success": "Chunk at %s in %s is marked for force loading", "commands.forceload.removed.all": "Unmarked all force loaded chunks in %s", "commands.forceload.removed.failure": "No chunks were removed from force loading", "commands.forceload.removed.multiple": "Unmarked %s chunks in %s from %s to %s for force loading", "commands.forceload.removed.single": "Unmarked chunk %s in %s for force loading", "commands.forceload.toobig": "Too many chunks in the specified area (maximum %s, specified %s)", "commands.function.success.multiple": "Executed %s command(s) from %s functions", "commands.function.success.multiple.result": "Executed %s functions", "commands.function.success.single": "Executed %s command(s) from function '%s'", "commands.function.success.single.result": "Function '%2$s' returned %1$s", "commands.gamemode.success.other": "Set %s's game mode to %s", "commands.gamemode.success.self": "Set own game mode to %s", "commands.gamerule.query": "Gamerule %s is currently set to: %s", "commands.gamerule.set": "Gamerule %s is now set to: %s", "commands.give.failed.toomanyitems": "Can't give more than %s of %s", "commands.give.success.multiple": "Gave %s %s to %s players", "commands.give.success.single": "Gave %s %s to %s", "commands.help.failed": "Unknown command or insufficient permissions", "commands.item.block.set.success": "Replaced a slot at %s, %s, %s with %s", "commands.item.entity.set.success.multiple": "Replaced a slot on %s entities with %s", "commands.item.entity.set.success.single": "Replaced a slot on %s with %s", "commands.item.source.no_such_slot": "The source does not have slot %s", "commands.item.source.not_a_container": "Source position %s, %s, %s is not a container", "commands.item.target.no_changed.known_item": "No targets accepted item %s into slot %s", "commands.item.target.no_changes": "No targets accepted item into slot %s", "commands.item.target.no_such_slot": "The target does not have slot %s", "commands.item.target.not_a_container": "Target position %s, %s, %s is not a container", "commands.jfr.dump.failed": "Failed to dump JFR recording: %s", "commands.jfr.start.failed": "Failed to start JFR profiling", "commands.jfr.started": "JFR profiling started", "commands.jfr.stopped": "JFR profiling stopped and dumped to %s", "commands.kick.success": "Kicked %s: %s", "commands.kill.success.multiple": "Killed %s entities", "commands.kill.success.single": "Killed %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "There are %s of a max of %s players online: %s", "commands.locate.biome.not_found": "Could not find a biome of type \"%s\" within reasonable distance", "commands.locate.biome.success": "The nearest %s is at %s (%s blocks away)", "commands.locate.poi.not_found": "Could not find a point of interest of type \"%s\" within reasonable distance", "commands.locate.poi.success": "The nearest %s is at %s (%s blocks away)", "commands.locate.structure.invalid": "There is no structure with type \"%s\"", "commands.locate.structure.not_found": "Could not find a structure of type \"%s\" nearby", "commands.locate.structure.success": "The nearest %s is at %s (%s blocks away)", "commands.message.display.incoming": "%s whispers to you: %s", "commands.message.display.outgoing": "You whisper to %s: %s", "commands.op.failed": "Nothing changed. The player already is an operator", "commands.op.success": "Made %s a server operator", "commands.pardon.failed": "Nothing changed. The player isn't banned", "commands.pardon.success": "Unbanned %s", "commands.pardonip.failed": "Nothing changed. That IP isn't banned", "commands.pardonip.invalid": "Invalid IP address", "commands.pardonip.success": "Unbanned IP %s", "commands.particle.failed": "The particle was not visible for anybody", "commands.particle.success": "Displaying particle %s", "commands.perf.alreadyRunning": "The performance profiler is already started", "commands.perf.notRunning": "The performance profiler hasn't started", "commands.perf.reportFailed": "Failed to create debug report", "commands.perf.reportSaved": "Created debug report in %s", "commands.perf.started": "Started 10 second performance profiling run (use '/perf stop' to stop early)", "commands.perf.stopped": "Stopped performance profiling after %s second(s) and %s tick(s) (%s tick(s) per second)", "commands.place.feature.failed": "Failed to place feature", "commands.place.feature.invalid": "There is no feature with type \"%s\"", "commands.place.feature.success": "Placed \"%s\" at %s, %s, %s", "commands.place.jigsaw.failed": "Failed to generate jigsaw", "commands.place.jigsaw.invalid": "There is no template pool with type \"%s\"", "commands.place.jigsaw.success": "Generated jigsaw at %s, %s, %s", "commands.place.structure.failed": "Failed to place structure", "commands.place.structure.invalid": "There is no structure with type \"%s\"", "commands.place.structure.success": "Generated structure \"%s\" at %s, %s, %s", "commands.place.template.failed": "Failed to place template", "commands.place.template.invalid": "There is no template with id \"%s\"", "commands.place.template.success": "Loaded template \"%s\" at %s, %s, %s", "commands.playsound.failed": "The sound is too far away to be heard", "commands.playsound.success.multiple": "Played sound %s to %s players", "commands.playsound.success.single": "Played sound %s to %s", "commands.publish.alreadyPublished": "Multiplayer game is already hosted on port %s", "commands.publish.failed": "Unable to host local game", "commands.publish.started": "Local game hosted on port %s", "commands.publish.success": "Multiplayer game is now hosted on port %s", "commands.recipe.give.failed": "No new recipes were learned", "commands.recipe.give.success.multiple": "Unlocked %s recipes for %s players", "commands.recipe.give.success.single": "Unlocked %s recipes for %s", "commands.recipe.take.failed": "No recipes could be forgotten", "commands.recipe.take.success.multiple": "Took %s recipes from %s players", "commands.recipe.take.success.single": "Took %s recipes from %s", "commands.reload.failure": "Reload failed; keeping old data", "commands.reload.success": "Reloading!", "commands.ride.already_riding": "%s is already riding %s", "commands.ride.dismount.success": "%s stopped riding %s", "commands.ride.mount.failure.cant_ride_players": "Players can't be ridden", "commands.ride.mount.failure.generic": "%s couldn't start riding %s", "commands.ride.mount.failure.loop": "Can't mount entity on itself or any of its passengers", "commands.ride.mount.failure.wrong_dimension": "Can't mount entity in different dimension", "commands.ride.mount.success": "%s started riding %s", "commands.ride.not_riding": "%s is not riding any vehicle", "commands.save.alreadyOff": "Saving is already turned off", "commands.save.alreadyOn": "Saving is already turned on", "commands.save.disabled": "Automatic saving is now disabled", "commands.save.enabled": "Automatic saving is now enabled", "commands.save.failed": "Unable to save the game (is there enough disk space?)", "commands.save.saving": "Saving the game (this may take a moment!)", "commands.save.success": "Saved the game", "commands.schedule.cleared.failure": "No schedules with id %s", "commands.schedule.cleared.success": "Removed %s schedule(s) with id %s", "commands.schedule.created.function": "Scheduled function '%s' in %s tick(s) at gametime %s", "commands.schedule.created.tag": "Scheduled tag '%s' in %s ticks at gametime %s", "commands.schedule.same_tick": "Can't schedule for current tick", "commands.scoreboard.objectives.add.duplicate": "An objective already exists by that name", "commands.scoreboard.objectives.add.success": "Created new objective %s", "commands.scoreboard.objectives.display.alreadyEmpty": "Nothing changed. That display slot is already empty", "commands.scoreboard.objectives.display.alreadySet": "Nothing changed. That display slot is already showing that objective", "commands.scoreboard.objectives.display.cleared": "Cleared any objectives in display slot %s", "commands.scoreboard.objectives.display.set": "Set display slot %s to show objective %s", "commands.scoreboard.objectives.list.empty": "There are no objectives", "commands.scoreboard.objectives.list.success": "There are %s objective(s): %s", "commands.scoreboard.objectives.modify.displayname": "Changed the display name of %s to %s", "commands.scoreboard.objectives.modify.rendertype": "Changed the render type of objective %s", "commands.scoreboard.objectives.remove.success": "Removed objective %s", "commands.scoreboard.players.add.success.multiple": "Added %s to %s for %s entities", "commands.scoreboard.players.add.success.single": "Added %s to %s for %s (now %s)", "commands.scoreboard.players.enable.failed": "Nothing changed. That trigger is already enabled", "commands.scoreboard.players.enable.invalid": "Enable only works on trigger-objectives", "commands.scoreboard.players.enable.success.multiple": "Enabled trigger %s for %s entities", "commands.scoreboard.players.enable.success.single": "Enabled trigger %s for %s", "commands.scoreboard.players.get.null": "Can't get value of %s for %s; none is set", "commands.scoreboard.players.get.success": "%s has %s %s", "commands.scoreboard.players.list.empty": "There are no tracked entities", "commands.scoreboard.players.list.entity.empty": "%s has no scores to show", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s has %s score(s):", "commands.scoreboard.players.list.success": "There are %s tracked entity/entities: %s", "commands.scoreboard.players.operation.success.multiple": "Updated %s for %s entities", "commands.scoreboard.players.operation.success.single": "Set %s for %s to %s", "commands.scoreboard.players.remove.success.multiple": "Removed %s from %s for %s entities", "commands.scoreboard.players.remove.success.single": "Removed %s from %s for %s (now %s)", "commands.scoreboard.players.reset.all.multiple": "Reset all scores for %s entities", "commands.scoreboard.players.reset.all.single": "Reset all scores for %s", "commands.scoreboard.players.reset.specific.multiple": "Reset %s for %s entities", "commands.scoreboard.players.reset.specific.single": "Reset %s for %s", "commands.scoreboard.players.set.success.multiple": "Set %s for %s entities to %s", "commands.scoreboard.players.set.success.single": "Set %s for %s to %s", "commands.seed.success": "Seed: %s", "commands.setblock.failed": "Could not set the block", "commands.setblock.success": "Changed the block at %s, %s, %s", "commands.setidletimeout.success": "The player idle timeout is now %s minute(s)", "commands.setworldspawn.success": "Set the world spawn point to %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Set spawn point to %s, %s, %s [%s] in %s for %s players", "commands.spawnpoint.success.single": "Set spawn point to %s, %s, %s [%s] in %s for %s", "commands.spectate.not_spectator": "%s is not in spectator mode", "commands.spectate.self": "Cannot spectate yourself", "commands.spectate.success.started": "Now spectating %s", "commands.spectate.success.stopped": "No longer spectating an entity", "commands.spreadplayers.failed.entities": "Could not spread %s entity/entities around %s, %s (too many entities for space - try using spread of at most %s)", "commands.spreadplayers.failed.invalid.height": "Invalid maxHeight %s; expected higher than world minimum %s", "commands.spreadplayers.failed.teams": "Could not spread %s team(s) around %s, %s (too many entities for space - try using spread of at most %s)", "commands.spreadplayers.success.entities": "Spread %s player(s) around %s, %s with an average distance of %s blocks apart", "commands.spreadplayers.success.teams": "Spread %s team(s) around %s, %s with an average distance of %s blocks apart", "commands.stop.stopping": "Stopping the server", "commands.stopsound.success.source.any": "Stopped all '%s' sounds", "commands.stopsound.success.source.sound": "Stopped sound '%s' on source '%s'", "commands.stopsound.success.sourceless.any": "Stopped all sounds", "commands.stopsound.success.sourceless.sound": "Stopped sound '%s'", "commands.summon.failed": "Unable to summon entity", "commands.summon.failed.uuid": "Unable to summon entity due to duplicate UUIDs", "commands.summon.invalidPosition": "Invalid position for summon", "commands.summon.success": "Summoned new %s", "commands.tag.add.failed": "Target either already has the tag or has too many tags", "commands.tag.add.success.multiple": "Added tag '%s' to %s entities", "commands.tag.add.success.single": "Added tag '%s' to %s", "commands.tag.list.multiple.empty": "There are no tags on the %s entities", "commands.tag.list.multiple.success": "The %s entities have %s total tags: %s", "commands.tag.list.single.empty": "%s has no tags", "commands.tag.list.single.success": "%s has %s tags: %s", "commands.tag.remove.failed": "Target does not have this tag", "commands.tag.remove.success.multiple": "Removed tag '%s' from %s entities", "commands.tag.remove.success.single": "Removed tag '%s' from %s", "commands.team.add.duplicate": "A team already exists by that name", "commands.team.add.success": "Created team %s", "commands.team.empty.success": "Removed %s member(s) from team %s", "commands.team.empty.unchanged": "Nothing changed. That team is already empty", "commands.team.join.success.multiple": "Added %s members to team %s", "commands.team.join.success.single": "Added %s to team %s", "commands.team.leave.success.multiple": "Removed %s members from any team", "commands.team.leave.success.single": "Removed %s from any team", "commands.team.list.members.empty": "There are no members on team %s", "commands.team.list.members.success": "Team %s has %s member(s): %s", "commands.team.list.teams.empty": "There are no teams", "commands.team.list.teams.success": "There are %s team(s): %s", "commands.team.option.collisionRule.success": "Collision rule for team %s is now \"%s\"", "commands.team.option.collisionRule.unchanged": "Nothing changed. Collision rule is already that value", "commands.team.option.color.success": "Updated the color for team %s to %s", "commands.team.option.color.unchanged": "Nothing changed. That team already has that color", "commands.team.option.deathMessageVisibility.success": "Death message visibility for team %s is now \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "Nothing changed. Death message visibility is already that value", "commands.team.option.friendlyfire.alreadyDisabled": "Nothing changed. Friendly fire is already disabled for that team", "commands.team.option.friendlyfire.alreadyEnabled": "Nothing changed. Friendly fire is already enabled for that team", "commands.team.option.friendlyfire.disabled": "Disabled friendly fire for team %s", "commands.team.option.friendlyfire.enabled": "Enabled friendly fire for team %s", "commands.team.option.name.success": "Updated the name of team %s", "commands.team.option.name.unchanged": "Nothing changed. That team already has that name", "commands.team.option.nametagVisibility.success": "Nametag visibility for team %s is now \"%s\"", "commands.team.option.nametagVisibility.unchanged": "Nothing changed. Nametag visibility is already that value", "commands.team.option.prefix.success": "Team prefix set to %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Nothing changed. That team already can't see invisible teammates", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Nothing changed. That team can already see invisible teammates", "commands.team.option.seeFriendlyInvisibles.disabled": "Team %s can no longer see invisible teammates", "commands.team.option.seeFriendlyInvisibles.enabled": "Team %s can now see invisible teammates", "commands.team.option.suffix.success": "Team suffix set to %s", "commands.team.remove.success": "Removed team %s", "commands.teammsg.failed.noteam": "You must be on a team to message your team", "commands.teleport.invalidPosition": "Invalid position for teleport", "commands.teleport.success.entity.multiple": "Teleported %s entities to %s", "commands.teleport.success.entity.single": "Teleported %s to %s", "commands.teleport.success.location.multiple": "Teleported %s entities to %s, %s, %s", "commands.teleport.success.location.single": "Teleported %s to %s, %s, %s", "commands.time.query": "The time is %s", "commands.time.set": "Set the time to %s", "commands.title.cleared.multiple": "Cleared titles for %s players", "commands.title.cleared.single": "Cleared titles for %s", "commands.title.reset.multiple": "Reset title options for %s players", "commands.title.reset.single": "Reset title options for %s", "commands.title.show.actionbar.multiple": "Showing new actionbar title for %s players", "commands.title.show.actionbar.single": "Showing new actionbar title for %s", "commands.title.show.subtitle.multiple": "Showing new subtitle for %s players", "commands.title.show.subtitle.single": "Showing new subtitle for %s", "commands.title.show.title.multiple": "Showing new title for %s players", "commands.title.show.title.single": "Showing new title for %s", "commands.title.times.multiple": "Changed title display times for %s players", "commands.title.times.single": "Changed title display times for %s", "commands.trigger.add.success": "Triggered %s (added %s to value)", "commands.trigger.failed.invalid": "You can only trigger objectives that are 'trigger' type", "commands.trigger.failed.unprimed": "You cannot trigger this objective yet", "commands.trigger.set.success": "Triggered %s (set value to %s)", "commands.trigger.simple.success": "Triggered %s", "commands.weather.set.clear": "Set the weather to clear", "commands.weather.set.rain": "Set the weather to rain", "commands.weather.set.thunder": "Set the weather to rain & thunder", "commands.whitelist.add.failed": "Player is already whitelisted", "commands.whitelist.add.success": "Added %s to the whitelist", "commands.whitelist.alreadyOff": "Whitelist is already turned off", "commands.whitelist.alreadyOn": "Whitelist is already turned on", "commands.whitelist.disabled": "Whitelist is now turned off", "commands.whitelist.enabled": "Whitelist is now turned on", "commands.whitelist.list": "There are %s whitelisted player(s): %s", "commands.whitelist.none": "There are no whitelisted players", "commands.whitelist.reloaded": "Reloaded the whitelist", "commands.whitelist.remove.failed": "Player is not whitelisted", "commands.whitelist.remove.success": "Removed %s from the whitelist", "commands.worldborder.center.failed": "Nothing changed. The world border is already centered there", "commands.worldborder.center.success": "Set the center of the world border to %s, %s", "commands.worldborder.damage.amount.failed": "Nothing changed. The world border damage is already that amount", "commands.worldborder.damage.amount.success": "Set the world border damage to %s per block each second", "commands.worldborder.damage.buffer.failed": "Nothing changed. The world border damage buffer is already that distance", "commands.worldborder.damage.buffer.success": "Set the world border damage buffer to %s block(s)", "commands.worldborder.get": "The world border is currently %s block(s) wide", "commands.worldborder.set.failed.big": "World border cannot be bigger than %s blocks wide", "commands.worldborder.set.failed.far": "World border cannot be further out than %s blocks", "commands.worldborder.set.failed.nochange": "Nothing changed. The world border is already that size", "commands.worldborder.set.failed.small": "World border cannot be smaller than 1 block wide", "commands.worldborder.set.grow": "Growing the world border to %s blocks wide over %s seconds", "commands.worldborder.set.immediate": "Set the world border to %s block(s) wide", "commands.worldborder.set.shrink": "Shrinking the world border to %s block(s) wide over %s second(s)", "commands.worldborder.warning.distance.failed": "Nothing changed. The world border warning is already that distance", "commands.worldborder.warning.distance.success": "Set the world border warning distance to %s block(s)", "commands.worldborder.warning.time.failed": "Nothing changed. The world border warning is already that amount of time", "commands.worldborder.warning.time.success": "Set the world border warning time to %s second(s)", "compliance.playtime.greaterThan24Hours": "You've been playing for greater than 24 hours", "compliance.playtime.hours": "You've been playing for %s hour(s)", "compliance.playtime.message": "Excessive gaming may interfere with normal daily life", "connect.aborted": "Aborted", "connect.authorizing": "Logging in...", "connect.connecting": "Connecting to the server...", "connect.encrypting": "Encrypting...", "connect.failed": "Failed to connect to the server", "connect.joining": "Joining world...", "connect.negotiating": "Negotiating...", "container.barrel": "Barrel", "container.beacon": "Beacon", "container.blast_furnace": "Blast Furnace", "container.brewing": "Brewing Stand", "container.cartography_table": "Cartography Table", "container.chest": "Chest", "container.chestDouble": "Large Chest", "container.crafting": "Crafting", "container.creative": "Item Selection", "container.dispenser": "Dispenser", "container.dropper": "Dropper", "container.enchant": "Enchant", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s Lapis <PERSON>", "container.enchant.lapis.one": "1 Lapis Lazuli", "container.enchant.level.many": "%s Enchantment Levels", "container.enchant.level.one": "1 Enchantment Level", "container.enchant.level.requirement": "Level Requirement: %s", "container.enderchest": "<PERSON><PERSON> Chest", "container.furnace": "Furnace", "container.grindstone_title": "Repair & Disenchant", "container.hopper": "<PERSON><PERSON>", "container.inventory": "Inventory", "container.isLocked": "%s is locked!", "container.lectern": "Lectern", "container.loom": "Loom", "container.repair": "Repair & Name", "container.repair.cost": "Enchantment Cost: %1$s", "container.repair.expensive": "Too Expensive!", "container.shulkerBox": "Shulker Box", "container.shulkerBox.more": "and %s more...", "container.smoker": "Smoker", "container.spectatorCantOpen": "Unable to open. Loot not generated yet.", "container.stonecutter": "<PERSON><PERSON><PERSON>", "container.upgrade": "Upgrade Gear", "container.upgrade.error_tooltip": "Item can't be upgraded this way", "container.upgrade.missing_template_tooltip": "Add <PERSON> Template", "controls.keybinds": "Key Binds...", "controls.keybinds.duplicateKeybinds": "This key is also used for:\n%s", "controls.keybinds.title": "Key Binds", "controls.reset": "Reset", "controls.resetAll": "Reset Keys", "controls.title": "Controls", "createWorld.customize.buffet.biome": "Please select a biome", "createWorld.customize.buffet.title": "Buffet world customization", "createWorld.customize.custom.baseSize": "Depth Base Size", "createWorld.customize.custom.biomeDepthOffset": "Biome Depth Offset", "createWorld.customize.custom.biomeDepthWeight": "Biome <PERSON>", "createWorld.customize.custom.biomeScaleOffset": "Biome Scale Offset", "createWorld.customize.custom.biomeScaleWeight": "Biome Scale Weight", "createWorld.customize.custom.biomeSize": "Biome <PERSON>", "createWorld.customize.custom.center": "Center Height", "createWorld.customize.custom.confirm1": "This will overwrite your current", "createWorld.customize.custom.confirm2": "settings and cannot be undone.", "createWorld.customize.custom.confirmTitle": "Warning!", "createWorld.customize.custom.coordinateScale": "Coordinate Scale", "createWorld.customize.custom.count": "Spawn Tries", "createWorld.customize.custom.defaults": "De<PERSON>ults", "createWorld.customize.custom.depthNoiseScaleExponent": "Depth Noise Exponent", "createWorld.customize.custom.depthNoiseScaleX": "Depth Noise Scale X", "createWorld.customize.custom.depthNoiseScaleZ": "Depth Noise Scale Z", "createWorld.customize.custom.dungeonChance": "Dungeon Count", "createWorld.customize.custom.fixedBiome": "Biome", "createWorld.customize.custom.heightScale": "Height Scale", "createWorld.customize.custom.lavaLakeChance": "Lava Lake Rarity", "createWorld.customize.custom.lowerLimitScale": "Lower Limit Scale", "createWorld.customize.custom.mainNoiseScaleX": "Main Noise Scale X", "createWorld.customize.custom.mainNoiseScaleY": "Main Noise Scale Y", "createWorld.customize.custom.mainNoiseScaleZ": "Main Noise Scale Z", "createWorld.customize.custom.maxHeight": "<PERSON>. Height", "createWorld.customize.custom.minHeight": "Min. Height", "createWorld.customize.custom.next": "Next Page", "createWorld.customize.custom.page0": "Basic Settings", "createWorld.customize.custom.page1": "<PERSON><PERSON>", "createWorld.customize.custom.page2": "Advanced Settings (Expert Users Only!)", "createWorld.customize.custom.page3": "Extra Advanced Settings (Expert Users Only!)", "createWorld.customize.custom.preset.caveChaos": "Caves of Chaos", "createWorld.customize.custom.preset.caveDelight": "Caver's Delight", "createWorld.customize.custom.preset.drought": "Drought", "createWorld.customize.custom.preset.goodLuck": "Good Luck", "createWorld.customize.custom.preset.isleLand": "Isle Land", "createWorld.customize.custom.preset.mountains": "Mountain Madness", "createWorld.customize.custom.preset.waterWorld": "Water World", "createWorld.customize.custom.presets": "Presets", "createWorld.customize.custom.presets.title": "Customize World Presets", "createWorld.customize.custom.prev": "Previous Page", "createWorld.customize.custom.randomize": "Randomize", "createWorld.customize.custom.riverSize": "River Size", "createWorld.customize.custom.seaLevel": "Sea Level", "createWorld.customize.custom.size": "Spawn Size", "createWorld.customize.custom.spread": "Spread Height", "createWorld.customize.custom.stretchY": "Height Stretch", "createWorld.customize.custom.upperLimitScale": "Upper Limit Scale", "createWorld.customize.custom.useCaves": "Caves", "createWorld.customize.custom.useDungeons": "Dungeons", "createWorld.customize.custom.useLavaLakes": "Lava Lakes", "createWorld.customize.custom.useLavaOceans": "Lava Oceans", "createWorld.customize.custom.useMansions": "Woodland Mansions", "createWorld.customize.custom.useMineShafts": "Mineshafts", "createWorld.customize.custom.useMonuments": "Ocean Monuments", "createWorld.customize.custom.useOceanRuins": "Ocean Ruins", "createWorld.customize.custom.useRavines": "<PERSON><PERSON>", "createWorld.customize.custom.useStrongholds": "Strongholds", "createWorld.customize.custom.useTemples": "Temples", "createWorld.customize.custom.useVillages": "Villages", "createWorld.customize.custom.useWaterLakes": "Water Lakes", "createWorld.customize.custom.waterLakeChance": "Water Lake Rarity", "createWorld.customize.flat.height": "Height", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Bottom - %s", "createWorld.customize.flat.layer.top": "Top - %s", "createWorld.customize.flat.removeLayer": "<PERSON><PERSON><PERSON>er", "createWorld.customize.flat.tile": "Layer Material", "createWorld.customize.flat.title": "Superflat Customization", "createWorld.customize.presets": "Presets", "createWorld.customize.presets.list": "Alternatively, here's some we made earlier!", "createWorld.customize.presets.select": "Use Preset", "createWorld.customize.presets.share": "Want to share your preset with someone? Use the box below!", "createWorld.customize.presets.title": "Select a Preset", "createWorld.preparing": "Preparing for world creation...", "createWorld.tab.game.title": "Game", "createWorld.tab.more.title": "More", "createWorld.tab.world.title": "World", "credits_and_attribution.button.attribution": "Attribution", "credits_and_attribution.button.credits": "Credits", "credits_and_attribution.button.licenses": "Licenses", "credits_and_attribution.screen.title": "Credits and Attribution", "dataPack.bundle.description": "Enables experimental Bundle item", "dataPack.bundle.name": "Bundles", "dataPack.title": "Select Data Packs", "dataPack.update_1_20.description": "New features and content for Minecraft 1.20", "dataPack.update_1_20.name": "Update 1.20", "dataPack.validation.back": "Go Back", "dataPack.validation.failed": "Data pack validation failed!", "dataPack.validation.reset": "Reset to De<PERSON>ult", "dataPack.validation.working": "Validating selected data packs...", "dataPack.vanilla.description": "The default data for Minecraft", "dataPack.vanilla.name": "<PERSON><PERSON><PERSON>", "datapackFailure.safeMode": "Safe Mode", "datapackFailure.safeMode.failed.description": "This world contains invalid or corrupted save data.", "datapackFailure.safeMode.failed.title": "Failed to load world in Safe Mode.", "datapackFailure.title": "Errors in currently selected data packs prevented the world from loading.\nYou can either try to load it with only the vanilla data pack (\"safe mode\"), or go back to the title screen and fix it manually.", "death.attack.anvil": "%1$s was squashed by a falling anvil", "death.attack.anvil.player": "%1$s was squashed by a falling anvil whilst fighting %2$s", "death.attack.arrow": "%1$s was shot by %2$s", "death.attack.arrow.item": "%1$s was shot by %2$s using %3$s", "death.attack.badRespawnPoint.link": "Intentional Game Design", "death.attack.badRespawnPoint.message": "%1$s was killed by %2$s", "death.attack.cactus": "%1$s was pricked to death", "death.attack.cactus.player": "%1$s walked into a cactus whilst trying to escape %2$s", "death.attack.cramming": "%1$s was squished too much", "death.attack.cramming.player": "%1$s was squashed by %2$s", "death.attack.dragonBreath": "%1$s was roasted in dragon's breath", "death.attack.dragonBreath.player": "%1$s was roasted in dragon's breath by %2$s", "death.attack.drown": "%1$s drowned", "death.attack.drown.player": "%1$s drowned whilst trying to escape %2$s", "death.attack.dryout": "%1$s died from dehydration", "death.attack.dryout.player": "%1$s died from dehydration whilst trying to escape %2$s", "death.attack.even_more_magic": "%1$s was killed by even more magic", "death.attack.explosion": "%1$s blew up", "death.attack.explosion.player": "%1$s was blown up by %2$s", "death.attack.explosion.player.item": "%1$s was blown up by %2$s using %3$s", "death.attack.fall": "%1$s hit the ground too hard", "death.attack.fall.player": "%1$s hit the ground too hard whilst trying to escape %2$s", "death.attack.fallingBlock": "%1$s was squashed by a falling block", "death.attack.fallingBlock.player": "%1$s was squashed by a falling block whilst fighting %2$s", "death.attack.fallingStalactite": "%1$s was skewered by a falling stalactite", "death.attack.fallingStalactite.player": "%1$s was skewered by a falling stalactite whilst fighting %2$s", "death.attack.fireball": "%1$s was fireballed by %2$s", "death.attack.fireball.item": "%1$s was fireballed by %2$s using %3$s", "death.attack.fireworks": "%1$s went off with a bang", "death.attack.fireworks.item": "%1$s went off with a bang due to a firework fired from %3$s by %2$s", "death.attack.fireworks.player": "%1$s went off with a bang whilst fighting %2$s", "death.attack.flyIntoWall": "%1$s experienced kinetic energy", "death.attack.flyIntoWall.player": "%1$s experienced kinetic energy whilst trying to escape %2$s", "death.attack.freeze": "%1$s froze to death", "death.attack.freeze.player": "%1$s was frozen to death by %2$s", "death.attack.generic": "%1$s died", "death.attack.genericKill": "%1$s was killed", "death.attack.genericKill.player": "%1$s was killed whilst fighting %2$s", "death.attack.generic.player": "%1$s died because of %2$s", "death.attack.hotFloor": "%1$s discovered the floor was lava", "death.attack.hotFloor.player": "%1$s walked into the danger zone due to %2$s", "death.attack.indirectMagic": "%1$s was killed by %2$s using magic", "death.attack.indirectMagic.item": "%1$s was killed by %2$s using %3$s", "death.attack.inFire": "%1$s went up in flames", "death.attack.inFire.player": "%1$s walked into fire whilst fighting %2$s", "death.attack.inWall": "%1$s suffocated in a wall", "death.attack.inWall.player": "%1$s suffocated in a wall whilst fighting %2$s", "death.attack.lava": "%1$s tried to swim in lava", "death.attack.lava.player": "%1$s tried to swim in lava to escape %2$s", "death.attack.lightningBolt": "%1$s was struck by lightning", "death.attack.lightningBolt.player": "%1$s was struck by lightning whilst fighting %2$s", "death.attack.magic": "%1$s was killed by magic", "death.attack.magic.player": "%1$s was killed by magic whilst trying to escape %2$s", "death.attack.message_too_long": "Actually, the message was too long to deliver fully. Sorry! Here's stripped version: %s", "death.attack.mob": "%1$s was slain by %2$s", "death.attack.mob.item": "%1$s was slain by %2$s using %3$s", "death.attack.onFire": "%1$s burned to death", "death.attack.onFire.item": "%1$s was burnt to a crisp whilst fighting %2$s wielding %3$s", "death.attack.onFire.player": "%1$s was burnt to a crisp whilst fighting %2$s", "death.attack.outsideBorder": "%1$s left the confines of this world", "death.attack.outsideBorder.player": "%1$s left the confines of this world whilst fighting %2$s", "death.attack.outOfWorld": "%1$s fell out of the world", "death.attack.outOfWorld.player": "%1$s didn't want to live in the same world as %2$s", "death.attack.player": "%1$s was slain by %2$s", "death.attack.player.item": "%1$s was slain by %2$s using %3$s", "death.attack.sonic_boom": "%1$s was obliterated by a sonically-charged shriek", "death.attack.sonic_boom.item": "%1$s was obliterated by a sonically-charged shriek whilst trying to escape %2$s wielding %3$s", "death.attack.sonic_boom.player": "%1$s was obliterated by a sonically-charged shriek whilst trying to escape %2$s", "death.attack.stalagmite": "%1$s was impaled on a stalagmite", "death.attack.stalagmite.player": "%1$s was impaled on a stalagmite whilst fighting %2$s", "death.attack.starve": "%1$s starved to death", "death.attack.starve.player": "%1$s starved to death whilst fighting %2$s", "death.attack.sting": "%1$s was stung to death", "death.attack.sting.item": "%1$s was stung to death by %2$s using %3$s", "death.attack.sting.player": "%1$s was stung to death by %2$s", "death.attack.sweetBerryBush": "%1$s was poked to death by a sweet berry bush", "death.attack.sweetBerryBush.player": "%1$s was poked to death by a sweet berry bush whilst trying to escape %2$s", "death.attack.thorns": "%1$s was killed trying to hurt %2$s", "death.attack.thorns.item": "%1$s was killed by %3$s trying to hurt %2$s", "death.attack.thrown": "%1$s was pummeled by %2$s", "death.attack.thrown.item": "%1$s was pummeled by %2$s using %3$s", "death.attack.trident": "%1$s was impaled by %2$s", "death.attack.trident.item": "%1$s was impaled by %2$s with %3$s", "death.attack.wither": "%1$s withered away", "death.attack.wither.player": "%1$s withered away whilst fighting %2$s", "death.attack.witherSkull": "%1$s was shot by a skull from %2$s", "death.attack.witherSkull.item": "%1$s was shot by a skull from %2$s using %3$s", "death.fell.accident.generic": "%1$s fell from a high place", "death.fell.accident.ladder": "%1$s fell off a ladder", "death.fell.accident.other_climbable": "%1$s fell while climbing", "death.fell.accident.scaffolding": "%1$s fell off scaffolding", "death.fell.accident.twisting_vines": "%1$s fell off some twisting vines", "death.fell.accident.vines": "%1$s fell off some vines", "death.fell.accident.weeping_vines": "%1$s fell off some weeping vines", "death.fell.assist": "%1$s was doomed to fall by %2$s", "death.fell.assist.item": "%1$s was doomed to fall by %2$s using %3$s", "death.fell.finish": "%1$s fell too far and was finished by %2$s", "death.fell.finish.item": "%1$s fell too far and was finished by %2$s using %3$s", "death.fell.killer": "%1$s was doomed to fall", "deathScreen.quit.confirm": "Are you sure you want to quit?", "deathScreen.respawn": "Respawn", "deathScreen.score": "Score", "deathScreen.spectate": "Spectate World", "deathScreen.title": "You Died!", "deathScreen.title.hardcore": "Game Over!", "deathScreen.titleScreen": "Title Screen", "debug.advanced_tooltips.help": "F3 + H = Advanced tooltips", "debug.advanced_tooltips.off": "Advanced tooltips: hidden", "debug.advanced_tooltips.on": "Advanced tooltips: shown", "debug.chunk_boundaries.help": "F3 + G = Show chunk boundaries", "debug.chunk_boundaries.off": "Chunk borders: hidden", "debug.chunk_boundaries.on": "Chunk borders: shown", "debug.clear_chat.help": "F3 + D = Clear chat", "debug.copy_location.help": "F3 + C = Copy location as /tp command, hold F3 + C to crash the game", "debug.copy_location.message": "Copied location to clipboard", "debug.crash.message": "F3 + C is held down. This will crash the game unless released.", "debug.crash.warning": "Crashing in %s...", "debug.creative_spectator.error": "Unable to switch game mode; no permission", "debug.creative_spectator.help": "F3 + N = Cycle previous game mode <-> spectator", "debug.dump_dynamic_textures": "Saved dynamic textures to %s", "debug.dump_dynamic_textures.help": "F3 + S = Dump dynamic textures", "debug.gamemodes.error": "Unable to open game mode switcher; no permission", "debug.gamemodes.help": "F3 + F4 = Open game mode switcher", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s Next", "debug.help.help": "F3 + Q = Show this list", "debug.help.message": "Key bindings:", "debug.inspect.client.block": "Copied client-side block data to clipboard", "debug.inspect.client.entity": "Copied client-side entity data to clipboard", "debug.inspect.help": "F3 + I = Copy entity or block data to clipboard", "debug.inspect.server.block": "Copied server-side block data to clipboard", "debug.inspect.server.entity": "Copied server-side entity data to clipboard", "debug.pause_focus.help": "F3 + P = Pause on lost focus", "debug.pause_focus.off": "Pause on lost focus: disabled", "debug.pause_focus.on": "Pause on lost focus: enabled", "debug.pause.help": "F3 + Esc = Pause without pause menu (if pausing is possible)", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = Start/stop profiling", "debug.profiling.start": "Profiling started for %s seconds. Use F3 + L to stop early", "debug.profiling.stop": "Profiling ended. Saved results to %s", "debug.reload_chunks.help": "F3 + A = Reload chunks", "debug.reload_chunks.message": "Reloading all chunks", "debug.reload_resourcepacks.help": "F3 + T = Reload resource packs", "debug.reload_resourcepacks.message": "Reloaded resource packs", "debug.show_hitboxes.help": "F3 + B = Show hitboxes", "debug.show_hitboxes.off": "Hitboxes: hidden", "debug.show_hitboxes.on": "Hitboxes: shown", "demo.day.1": "This demo will last five game days. Do your best!", "demo.day.2": "Day Two", "demo.day.3": "Day Three", "demo.day.4": "Day Four", "demo.day.5": "This is your last day!", "demo.day.6": "You have passed your fifth day. Use %s to save a screenshot of your creation.", "demo.day.warning": "Your time is almost up!", "demo.demoExpired": "Demo time's up!", "demo.help.buy": "Purchase Now!", "demo.help.fullWrapped": "This demo will last 5 in-game days (about 1 hour and 40 minutes of real time). Check the advancements for hints! Have fun!", "demo.help.inventory": "Use the %1$s key to open your inventory", "demo.help.jump": "Jump by pressing the %1$s key", "demo.help.later": "Continue Playing!", "demo.help.movement": "Use the %1$s, %2$s, %3$s, %4$s keys and the mouse to move around", "demo.help.movementMouse": "Look around using the mouse", "demo.help.movementShort": "Move by pressing the %1$s, %2$s, %3$s, %4$s keys", "demo.help.title": "Minecraft Demo Mode", "demo.remainingTime": "Remaining time: %s", "demo.reminder": "The demo time has expired. Buy the game to continue or start a new world!", "difficulty.lock.question": "Are you sure you want to lock the difficulty of this world? This will set this world to always be %1$s, and you will never be able to change that again.", "difficulty.lock.title": "Lock World Difficulty", "disconnect.closed": "Connection closed", "disconnect.disconnected": "Disconnected by <PERSON>", "disconnect.endOfStream": "End of stream", "disconnect.exceeded_packet_rate": "Kicked for exceeding packet rate limit", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Ignoring status request", "disconnect.kicked": "Was kicked from the game", "disconnect.loginFailed": "Failed to log in", "disconnect.loginFailedInfo": "Failed to log in: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Multiplayer is disabled. Please check your Microsoft account settings.", "disconnect.loginFailedInfo.invalidSession": "Invalid session (Try restarting your game and the launcher)", "disconnect.loginFailedInfo.serversUnavailable": "The authentication servers are currently not reachable. Please try again.", "disconnect.loginFailedInfo.userBanned": "You are banned from playing online", "disconnect.lost": "Connection Lost", "disconnect.overflow": "Buffer overflow", "disconnect.quitting": "Quitting", "disconnect.spam": "Kicked for spamming", "disconnect.timeout": "Timed out", "disconnect.unknownHost": "Unknown host", "editGamerule.default": "Default: %s", "editGamerule.title": "Edit Game Rules", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Absorption", "effect.minecraft.bad_omen": "Bad Omen", "effect.minecraft.blindness": "Blindness", "effect.minecraft.conduit_power": "Conduit Power", "effect.minecraft.darkness": "Darkness", "effect.minecraft.dolphins_grace": "Dolphin's Grace", "effect.minecraft.fire_resistance": "Fire Resistance", "effect.minecraft.glowing": "Glowing", "effect.minecraft.haste": "<PERSON><PERSON>", "effect.minecraft.health_boost": "Health Boost", "effect.minecraft.hero_of_the_village": "Hero of the Village", "effect.minecraft.hunger": "Hunger", "effect.minecraft.instant_damage": "Instant Damage", "effect.minecraft.instant_health": "Instant Health", "effect.minecraft.invisibility": "Invisibility", "effect.minecraft.jump_boost": "Jump Boost", "effect.minecraft.levitation": "Levitation", "effect.minecraft.luck": "Luck", "effect.minecraft.mining_fatigue": "Mining Fatigue", "effect.minecraft.nausea": "<PERSON><PERSON><PERSON>", "effect.minecraft.night_vision": "Night Vision", "effect.minecraft.poison": "Poison", "effect.minecraft.regeneration": "Regeneration", "effect.minecraft.resistance": "Resistance", "effect.minecraft.saturation": "Saturation", "effect.minecraft.slow_falling": "Slow Falling", "effect.minecraft.slowness": "Slowness", "effect.minecraft.speed": "Speed", "effect.minecraft.strength": "Strength", "effect.minecraft.unluck": "Bad Luck", "effect.minecraft.water_breathing": "Water Breathing", "effect.minecraft.weakness": "Weakness", "effect.minecraft.wither": "<PERSON>er", "effect.none": "No Effects", "enchantment.level.1": "I", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.level.10": "X", "enchantment.minecraft.aqua_affinity": "Aqua Affinity", "enchantment.minecraft.bane_of_arthropods": "Bane of Arthropods", "enchantment.minecraft.binding_curse": "Curse of Binding", "enchantment.minecraft.blast_protection": "Blast Protection", "enchantment.minecraft.channeling": "Channeling", "enchantment.minecraft.depth_strider": "Depth Strider", "enchantment.minecraft.efficiency": "Efficiency", "enchantment.minecraft.feather_falling": "Feather Falling", "enchantment.minecraft.fire_aspect": "Fire Aspect", "enchantment.minecraft.fire_protection": "Fire Protection", "enchantment.minecraft.flame": "Flame", "enchantment.minecraft.fortune": "Fortune", "enchantment.minecraft.frost_walker": "<PERSON>", "enchantment.minecraft.impaling": "Impaling", "enchantment.minecraft.infinity": "Infinity", "enchantment.minecraft.knockback": "K<PERSON><PERSON>", "enchantment.minecraft.looting": "Looting", "enchantment.minecraft.loyalty": "Loyalty", "enchantment.minecraft.luck_of_the_sea": "Luck of the Sea", "enchantment.minecraft.lure": "<PERSON><PERSON>", "enchantment.minecraft.mending": "Mending", "enchantment.minecraft.multishot": "Multishot", "enchantment.minecraft.piercing": "Piercing", "enchantment.minecraft.power": "Power", "enchantment.minecraft.projectile_protection": "Projectile Protection", "enchantment.minecraft.protection": "Protection", "enchantment.minecraft.punch": "Punch", "enchantment.minecraft.quick_charge": "Quick Charge", "enchantment.minecraft.respiration": "Respiration", "enchantment.minecraft.riptide": "Riptide", "enchantment.minecraft.sharpness": "Sharpness", "enchantment.minecraft.silk_touch": "Silk Touch", "enchantment.minecraft.smite": "Smite", "enchantment.minecraft.soul_speed": "Soul Speed", "enchantment.minecraft.sweeping": "Sweeping Edge", "enchantment.minecraft.swift_sneak": "Swift Sneak", "enchantment.minecraft.thorns": "Thorns", "enchantment.minecraft.unbreaking": "Unbreaking", "enchantment.minecraft.vanishing_curse": "Curse of Vanishing", "entity.minecraft.allay": "Allay", "entity.minecraft.area_effect_cloud": "Area Effect Cloud", "entity.minecraft.armor_stand": "Armor Stand", "entity.minecraft.arrow": "Arrow", "entity.minecraft.axolotl": "Axolotl", "entity.minecraft.bat": "Bat", "entity.minecraft.bee": "Bee", "entity.minecraft.blaze": "Blaze", "entity.minecraft.block_display": "Block Display", "entity.minecraft.boat": "Boat", "entity.minecraft.camel": "Camel", "entity.minecraft.cat": "Cat", "entity.minecraft.cave_spider": "<PERSON> Spider", "entity.minecraft.chest_boat": "Boat with Chest", "entity.minecraft.chest_minecart": "Minecart with Chest", "entity.minecraft.chicken": "Chicken", "entity.minecraft.cod": "Cod", "entity.minecraft.command_block_minecart": "Minecart with Command Block", "entity.minecraft.cow": "Cow", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dolphin": "Dolphin", "entity.minecraft.donkey": "<PERSON><PERSON>", "entity.minecraft.dragon_fireball": "Dragon Fireball", "entity.minecraft.drowned": "Drowned", "entity.minecraft.egg": "Thrown Egg", "entity.minecraft.elder_guardian": "Elder Guardian", "entity.minecraft.end_crystal": "End Crystal", "entity.minecraft.ender_dragon": "<PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON><PERSON> <PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Evoker", "entity.minecraft.evoker_fangs": "Evoker <PERSON>s", "entity.minecraft.experience_bottle": "T<PERSON><PERSON> Bottle o' Enchanting", "entity.minecraft.experience_orb": "Experience Orb", "entity.minecraft.eye_of_ender": "Eye of <PERSON>er", "entity.minecraft.falling_block": "Falling Block", "entity.minecraft.falling_block_type": "Falling %s", "entity.minecraft.fireball": "Fireball", "entity.minecraft.firework_rocket": "Firework Rocket", "entity.minecraft.fishing_bobber": "Fishing Bobber", "entity.minecraft.fox": "Fox", "entity.minecraft.frog": "<PERSON>", "entity.minecraft.furnace_minecart": "Minecart with Furnace", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Giant", "entity.minecraft.glow_item_frame": "G<PERSON> Item <PERSON>", "entity.minecraft.glow_squid": "Glow Squid", "entity.minecraft.goat": "Goa<PERSON>", "entity.minecraft.guardian": "Guardian", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Minecart with <PERSON>", "entity.minecraft.horse": "Horse", "entity.minecraft.husk": "Husk", "entity.minecraft.illusioner": "<PERSON><PERSON><PERSON>", "entity.minecraft.interaction": "Interaction", "entity.minecraft.iron_golem": "Iron Golem", "entity.minecraft.item": "<PERSON><PERSON>", "entity.minecraft.item_display": "<PERSON><PERSON>", "entity.minecraft.item_frame": "<PERSON><PERSON>", "entity.minecraft.killer_bunny": "The Killer Bunny", "entity.minecraft.leash_knot": "<PERSON><PERSON>", "entity.minecraft.lightning_bolt": "Lightning Bolt", "entity.minecraft.llama": "Llama", "entity.minecraft.llama_spit": "Llama <PERSON>", "entity.minecraft.magma_cube": "Magma Cube", "entity.minecraft.marker": "<PERSON><PERSON>", "entity.minecraft.minecart": "Minecart", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON>", "entity.minecraft.ocelot": "Ocelot", "entity.minecraft.painting": "Painting", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "<PERSON><PERSON><PERSON>", "entity.minecraft.phantom": "Phantom", "entity.minecraft.pig": "Pig", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON> B<PERSON>", "entity.minecraft.pillager": "Pillager", "entity.minecraft.player": "Player", "entity.minecraft.polar_bear": "Polar Bear", "entity.minecraft.potion": "Potion", "entity.minecraft.pufferfish": "Pufferfish", "entity.minecraft.rabbit": "Rabbit", "entity.minecraft.ravager": "<PERSON><PERSON><PERSON>", "entity.minecraft.salmon": "Salmon", "entity.minecraft.sheep": "Sheep", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON><PERSON> Bullet", "entity.minecraft.silverfish": "Silverfish", "entity.minecraft.skeleton": "Skeleton", "entity.minecraft.skeleton_horse": "Skeleton Horse", "entity.minecraft.slime": "Slime", "entity.minecraft.small_fireball": "Small Fireball", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "Snow Golem", "entity.minecraft.snowball": "Snowball", "entity.minecraft.spawner_minecart": "<PERSON><PERSON><PERSON> with <PERSON> Spawner", "entity.minecraft.spectral_arrow": "Spectral Arrow", "entity.minecraft.spider": "Spider", "entity.minecraft.squid": "Squid", "entity.minecraft.stray": "Stray", "entity.minecraft.strider": "Strider", "entity.minecraft.tadpole": "Tadpole", "entity.minecraft.text_display": "Text Display", "entity.minecraft.tnt": "Primed TNT", "entity.minecraft.tnt_minecart": "Minecart with TNT", "entity.minecraft.trader_llama": "Trader <PERSON><PERSON><PERSON>", "entity.minecraft.trident": "Trident", "entity.minecraft.tropical_fish": "Tropical Fish", "entity.minecraft.tropical_fish.predefined.0": "Anemone", "entity.minecraft.tropical_fish.predefined.1": "Black Tang", "entity.minecraft.tropical_fish.predefined.2": "Blue Tang", "entity.minecraft.tropical_fish.predefined.3": "Butterflyfish", "entity.minecraft.tropical_fish.predefined.4": "Cichlid", "entity.minecraft.tropical_fish.predefined.5": "Clownfish", "entity.minecraft.tropical_fish.predefined.6": "Cotton Candy <PERSON>", "entity.minecraft.tropical_fish.predefined.7": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.8": "Emperor <PERSON>", "entity.minecraft.tropical_fish.predefined.9": "Goatfish", "entity.minecraft.tropical_fish.predefined.10": "Moorish Idol", "entity.minecraft.tropical_fish.predefined.11": "Ornate Butterflyfish", "entity.minecraft.tropical_fish.predefined.12": "Parrotfish", "entity.minecraft.tropical_fish.predefined.13": "Queen Angelfish", "entity.minecraft.tropical_fish.predefined.14": "Red Cichlid", "entity.minecraft.tropical_fish.predefined.15": "Red Lipped Blenny", "entity.minecraft.tropical_fish.predefined.16": "Red Snapper", "entity.minecraft.tropical_fish.predefined.17": "Threadfin", "entity.minecraft.tropical_fish.predefined.18": "Tomato Clownfish", "entity.minecraft.tropical_fish.predefined.19": "Triggerfish", "entity.minecraft.tropical_fish.predefined.20": "Yellowtail Parrotfish", "entity.minecraft.tropical_fish.predefined.21": "Yellow Tang", "entity.minecraft.tropical_fish.type.betty": "<PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "Blockfish", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "Clayfish", "entity.minecraft.tropical_fish.type.dasher": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.flopper": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.glitter": "Glitter", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "Snooper", "entity.minecraft.tropical_fish.type.spotty": "Spotty", "entity.minecraft.tropical_fish.type.stripey": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "Sunstreak", "entity.minecraft.turtle": "Turtle", "entity.minecraft.vex": "Vex", "entity.minecraft.villager": "Villager", "entity.minecraft.villager.armorer": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.butcher": "<PERSON>", "entity.minecraft.villager.cartographer": "Cartographer", "entity.minecraft.villager.cleric": "Cleric", "entity.minecraft.villager.farmer": "<PERSON>", "entity.minecraft.villager.fisherman": "Fisherman", "entity.minecraft.villager.fletcher": "<PERSON>", "entity.minecraft.villager.leatherworker": "Leatherworker", "entity.minecraft.villager.librarian": "Librarian", "entity.minecraft.villager.mason": "<PERSON>", "entity.minecraft.villager.nitwit": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.none": "Villager", "entity.minecraft.villager.shepherd": "<PERSON>", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.weaponsmith": "Weaponsmith", "entity.minecraft.vindicator": "Vindicator", "entity.minecraft.wandering_trader": "Wandering Trader", "entity.minecraft.warden": "Warden", "entity.minecraft.witch": "Witch", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "<PERSON><PERSON>", "entity.minecraft.wither_skull": "<PERSON><PERSON>", "entity.minecraft.wolf": "<PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "Zombie", "entity.minecraft.zombie_horse": "Zombie Horse", "entity.minecraft.zombie_villager": "Zombie Villager", "entity.minecraft.zombified_piglin": "Zombified Piglin", "entity.not_summonable": "Can't summon entity of type %s", "event.minecraft.raid": "Raid", "event.minecraft.raid.defeat": "Defeat", "event.minecraft.raid.raiders_remaining": "Raiders Remaining: %s", "event.minecraft.raid.victory": "Victory", "filled_map.buried_treasure": "Buried Treasure Map", "filled_map.id": "Id #%s", "filled_map.level": "(Level %s/%s)", "filled_map.locked": "Locked", "filled_map.mansion": "Woodland Explorer Map", "filled_map.monument": "Ocean Explorer Map", "filled_map.scale": "Scaling at 1:%s", "filled_map.unknown": "Unknown Map", "flat_world_preset.minecraft.bottomless_pit": "Bottomless Pit", "flat_world_preset.minecraft.classic_flat": "Classic Flat", "flat_world_preset.minecraft.desert": "Desert", "flat_world_preset.minecraft.overworld": "Overworld", "flat_world_preset.minecraft.redstone_ready": "Redstone Ready", "flat_world_preset.minecraft.snowy_kingdom": "Snowy Kingdom", "flat_world_preset.minecraft.the_void": "The Void", "flat_world_preset.minecraft.tunnelers_dream": "Tunnelers' Dream", "flat_world_preset.minecraft.water_world": "Water World", "flat_world_preset.unknown": "???", "gameMode.adventure": "Adventure Mode", "gameMode.changed": "Your game mode has been updated to %s", "gameMode.creative": "Creative Mode", "gameMode.hardcore": "Hardcore Mode!", "gameMode.spectator": "Spectator Mode", "gameMode.survival": "Survival Mode", "gamerule.announceAdvancements": "Announce advancements", "gamerule.blockExplosionDropDecay": "In block interaction explosions, some blocks won't drop their loot", "gamerule.blockExplosionDropDecay.description": "Some of the drops from blocks destroyed by explosions caused by block interactions are lost in the explosion.", "gamerule.category.chat": "Cha<PERSON>", "gamerule.category.drops": "Drops", "gamerule.category.misc": "Miscellaneous", "gamerule.category.mobs": "<PERSON><PERSON>", "gamerule.category.player": "Player", "gamerule.category.spawning": "Spawning", "gamerule.category.updates": "World Updates", "gamerule.commandBlockOutput": "Broadcast command block output", "gamerule.commandModificationBlockLimit": "Command Modification Block Limit", "gamerule.commandModificationBlockLimit.description": "Number of blocks that can be changed at once by one command, such as fill or clone.", "gamerule.disableElytraMovementCheck": "Disable elytra movement check", "gamerule.disableRaids": "Disable raids", "gamerule.doDaylightCycle": "Advance time of day", "gamerule.doEntityDrops": "Drop entity equipment", "gamerule.doEntityDrops.description": "Controls drops from minecarts (including inventories), item frames, boats, etc.", "gamerule.doFireTick": "Update fire", "gamerule.doImmediateRespawn": "Respawn immediately", "gamerule.doInsomnia": "Spawn phantoms", "gamerule.doLimitedCrafting": "Require recipe for crafting", "gamerule.doLimitedCrafting.description": "If enabled, players will be able to craft only unlocked recipes.", "gamerule.doMobLoot": "Drop mob loot", "gamerule.doMobLoot.description": "Controls resource drops from mobs, including experience orbs.", "gamerule.doMobSpawning": "Spawn mobs", "gamerule.doMobSpawning.description": "Some entities might have separate rules.", "gamerule.doPatrolSpawning": "Spawn pillager patrols", "gamerule.doTileDrops": "Drop blocks", "gamerule.doTileDrops.description": "Controls resource drops from blocks, including experience orbs.", "gamerule.doTraderSpawning": "Spawn Wandering Traders", "gamerule.doVinesSpread": "Vines spread", "gamerule.doVinesSpread.description": "Controls whether or not the Vines block spreads randomly to adjacent blocks. Does not affect other type of vine blocks such as Weeping Vines, Twisting Vines, etc.", "gamerule.doWardenSpawning": "Spawn Wardens", "gamerule.doWeatherCycle": "Update weather", "gamerule.drowningDamage": "Deal drowning damage", "gamerule.fallDamage": "Deal fall damage", "gamerule.fireDamage": "Deal fire damage", "gamerule.forgiveDeadPlayers": "Forgive dead players", "gamerule.forgiveDeadPlayers.description": "Angered neutral mobs stop being angry when the targeted player dies nearby.", "gamerule.freezeDamage": "Deal freeze damage", "gamerule.globalSoundEvents": "Global sound events", "gamerule.globalSoundEvents.description": "When certain game events happen, like a boss spawning, the sound is heard everywhere.", "gamerule.keepInventory": "Keep inventory after death", "gamerule.lavaSourceConversion": "Lava converts to source", "gamerule.lavaSourceConversion.description": "When flowing lava is surrounded on two sides by lava sources it converts into a source.", "gamerule.logAdminCommands": "Broadcast admin commands", "gamerule.maxCommandChainLength": "Command chain size limit", "gamerule.maxCommandChainLength.description": "Applies to command block chains and functions.", "gamerule.maxEntityCramming": "Entity cramming threshold", "gamerule.mobExplosionDropDecay": "In mob explosions, some blocks won't drop their loot", "gamerule.mobExplosionDropDecay.description": "Some of the drops from blocks destroyed by explosions caused by mobs are lost in the explosion.", "gamerule.mobGriefing": "Allow destructive mob actions", "gamerule.naturalRegeneration": "Regenerate health", "gamerule.playersSleepingPercentage": "Sleep percentage", "gamerule.playersSleepingPercentage.description": "The percentage of players who must be sleeping to skip the night.", "gamerule.randomTickSpeed": "Random tick speed rate", "gamerule.reducedDebugInfo": "Reduce debug info", "gamerule.reducedDebugInfo.description": "Limits contents of debug screen.", "gamerule.sendCommandFeedback": "Send command feedback", "gamerule.showDeathMessages": "Show death messages", "gamerule.snowAccumulationHeight": "Snow accumulation height", "gamerule.snowAccumulationHeight.description": "When it snows, layers of snow form on the ground up to at most this number of layers.", "gamerule.spawnRadius": "Respawn location radius", "gamerule.spectatorsGenerateChunks": "Allow spectators to generate terrain", "gamerule.tntExplosionDropDecay": "In TNT explosions, some blocks won't drop their loot", "gamerule.tntExplosionDropDecay.description": "Some of the drops from blocks destroyed by explosions caused by TNT are lost in the explosion.", "gamerule.universalAnger": "Universal anger", "gamerule.universalAnger.description": "Angered neutral mobs attack any nearby player, not just the player that angered them. Works best if forgiveDead<PERSON><PERSON><PERSON> is disabled.", "gamerule.waterSourceConversion": "Water converts to source", "gamerule.waterSourceConversion.description": "When flowing water is surrounded on two sides by water sources it converts into a source.", "generator.custom": "Custom", "generator.customized": "Old Customized", "generator.minecraft.amplified": "AMPLIFIED", "generator.minecraft.amplified.info": "Notice: Just for fun! Requires a beefy computer.", "generator.minecraft.debug_all_block_states": "Debug Mode", "generator.minecraft.flat": "Superflat", "generator.minecraft.large_biomes": "Large Biomes", "generator.minecraft.normal": "<PERSON><PERSON><PERSON>", "generator.minecraft.single_biome_surface": "Single Biome", "generator.single_biome_caves": "Caves", "generator.single_biome_floating_islands": "Floating Islands", "gui.abuseReport.error.title": "Problem sending your report", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Drugs or alcohol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Someone is encouraging others to partake in illegal drug related activities or encouraging underage drinking.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Child sexual exploitation or abuse", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Someone is talking about or otherwise promoting indecent behavior involving children.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Defamation, impersonation, or false information", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Someone is damaging someone else's reputation, pretending to be someone they're not, or sharing false information with the aim to exploit or mislead others.", "gui.abuseReport.reason.description": "Description:", "gui.abuseReport.reason.false_reporting": "False Reporting", "gui.abuseReport.reason.harassment_or_bullying": "Harassment or bullying", "gui.abuseReport.reason.harassment_or_bullying.description": "Someone is shaming, attacking, or bullying you or someone else. This includes when someone is repeatedly trying to contact you or someone else without consent or posting private personal information about you or someone else without consent (\"doxing\").", "gui.abuseReport.reason.hate_speech": "Hate speech", "gui.abuseReport.reason.hate_speech.description": "Someone is attacking you or another player based on characteristics of their identity, like religion, race, or sexuality.", "gui.abuseReport.reason.imminent_harm": "Imminent harm - Threat to harm others", "gui.abuseReport.reason.imminent_harm.description": "Someone is threatening to harm you or someone else in real life.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Non-consensual intimate imagery", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "Someone is talking about, sharing, or otherwise promoting private and intimate images.", "gui.abuseReport.reason.self_harm_or_suicide": "Imminent harm - Self-harm or suicide", "gui.abuseReport.reason.self_harm_or_suicide.description": "Someone is threatening to harm themselves in real life or talking about harming themselves in real life.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorism or violent extremism", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "Someone is talking about, promoting, or threatening to commit acts of terrorism or violent extremism for political, religious, ideological, or other reasons.", "gui.abuseReport.reason.title": "Select Report Category", "gui.abuseReport.send.error_message": "An error was returned while sending your report:\n'%s'", "gui.abuseReport.send.generic_error": "Encountered an unexpected error while sending your report.", "gui.abuseReport.send.http_error": "An unexpected HTTP error occurred while sending your report.", "gui.abuseReport.send.json_error": "Encountered malformed payload while sending your report.", "gui.abuseReport.send.service_unavailable": "Unable to reach the Abuse Reporting service. Please make sure you are connected to the internet and try again.", "gui.abuseReport.sending.title": "Sending your report...", "gui.abuseReport.sent.title": "Report sent", "gui.acknowledge": "Acknowledge", "gui.advancements": "Advancements", "gui.all": "All", "gui.back": "Back", "gui.copy_link_to_clipboard": "Copy Link to Clipboard", "gui.banned.description": "%s\n\n%s\n\nLearn more at the following link: %s", "gui.banned.description.permanent": "Your account is permanently banned, which means you can’t play online or join Realms.", "gui.banned.description.reason": "We recently received a report for bad behavior by your account. Our moderators have now reviewed your case and identified it as %s, which goes against the Minecraft Community Standards.", "gui.banned.description.reason_id": "Code: %s", "gui.banned.description.reason_id_message": "Code: %s - %s", "gui.banned.description.temporary": "%s Until then, you can’t play online or join Realms.", "gui.banned.description.temporary.duration": "Your account is temporarily suspended and will be reactivated in %s.", "gui.banned.description.unknownreason": "We recently received a report for bad behavior by your account. Our moderators have now reviewed your case and identified that it goes against the Minecraft Community Standards.", "gui.banned.reason.defamation_impersonation_false_information": "Impersonation or sharing information to exploit or mislead others", "gui.banned.reason.drugs": "References to illegal drugs", "gui.banned.reason.extreme_violence_or_gore": "Depictions of real-life excessive violence or gore", "gui.banned.reason.false_reporting": "Excessive false or inaccurate reports", "gui.banned.reason.fraud": "Fraudulent acquisition or use of content", "gui.banned.reason.generic_violation": "Violating Community Standards", "gui.banned.reason.harassment_or_bullying": "Abusive language used in a directed, harmful manner", "gui.banned.reason.hate_speech": "Hate speech or discrimination", "gui.banned.reason.hate_terrorism_notorious_figure": "References to hate groups, terrorist organizations, or notorious figures", "gui.banned.reason.imminent_harm_to_person_or_property": "Intent to cause real-life harm to persons or property", "gui.banned.reason.nudity_or_pornography": "Displaying lewd or pornographic material", "gui.banned.reason.sexually_inappropriate": "Topics or content of a sexual nature", "gui.banned.reason.spam_or_advertising": "Spam or advertising", "gui.banned.title.permanent": "Account permanently banned", "gui.banned.title.temporary": "Account temporarily suspended", "gui.cancel": "Cancel", "gui.chatReport.comments": "Comments", "gui.chatReport.describe": "Sharing details will help us make a well-informed decision.", "gui.chatReport.discard.content": "If you leave, you'll lose this report and your comments.\nAre you sure you want to leave?", "gui.chatReport.discard.discard": "Leave and Discard Report", "gui.chatReport.discard.draft": "Save as Draft", "gui.chatReport.discard.return": "Continue Editing", "gui.chatReport.discard.title": "Discard report and comments?", "gui.chatReport.draft.content": "Would you like to continue editing the existing report or discard it and create a new one?", "gui.chatReport.draft.discard": "Discard", "gui.chatReport.draft.edit": "Continue Editing", "gui.chatReport.draft.quittotitle.content": "Would you like to continue editing it or discard it?", "gui.chatReport.draft.quittotitle.title": "You have a draft chat report that will be lost if you quit", "gui.chatReport.draft.title": "Edit draft chat report?", "gui.chatReport.more_comments": "Please describe what happened:", "gui.chatReport.observed_what": "Why are you reporting this?", "gui.chatReport.read_info": "Learn About Reporting", "gui.chatReport.report_sent_msg": "We’ve successfully received your report. Thank you!\n\nOur team will review it as soon as possible.", "gui.chatReport.select_chat": "Select Chat Messages to Report", "gui.chatReport.select_reason": "Select Report Category", "gui.chatReport.selected_chat": "%s Chat Message(s) Selected to Report", "gui.chatReport.send": "Send Report", "gui.chatReport.send.comments_too_long": "Please shorten the comment", "gui.chatReport.send.no_reason": "Please select a report category", "gui.chatReport.send.no_reported_messages": "Please select at least one chat message to report", "gui.chatReport.send.too_many_messages": "Trying to include too many messages in the report", "gui.chatReport.title": "Report Player", "gui.chatSelection.context": "Messages surrounding this selection will be included to provide additional context", "gui.chatSelection.fold": "%s message(s) hidden", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s joined the chat", "gui.chatSelection.message.narrate": "%s said: %s at %s", "gui.chatSelection.selected": "%s/%s message(s) selected", "gui.chatSelection.title": "Select Chat Messages to Report", "gui.continue": "Continue", "gui.days": "%s day(s)", "gui.done": "Done", "gui.down": "Down", "gui.entity_tooltip.type": "Type: %s", "gui.hours": "%s hour(s)", "gui.minutes": "%s minute(s)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s button", "gui.narrate.editBox": "%s edit box: %s", "gui.narrate.slider": "%s slider", "gui.narrate.tab": "%s tab", "gui.no": "No", "gui.none": "None", "gui.ok": "Ok", "gui.proceed": "Proceed", "gui.recipebook.moreRecipes": "Right Click for More", "gui.recipebook.search_hint": "Search...", "gui.recipebook.toggleRecipes.all": "Showing All", "gui.recipebook.toggleRecipes.blastable": "Showing Blastable", "gui.recipebook.toggleRecipes.craftable": "Showing Craftable", "gui.recipebook.toggleRecipes.smeltable": "Showing Smeltable", "gui.recipebook.toggleRecipes.smokable": "Showing Smokable", "gui.socialInteractions.blocking_hint": "Manage with Microsoft account", "gui.socialInteractions.empty_blocked": "No blocked players in chat", "gui.socialInteractions.empty_hidden": "No players hidden in chat", "gui.socialInteractions.hidden_in_chat": "Chat messages from %s will be hidden", "gui.socialInteractions.hide": "Hide in Chat", "gui.socialInteractions.narration.hide": "Hide messages from %s", "gui.socialInteractions.narration.report": "Report player %s", "gui.socialInteractions.narration.show": "Show messages from %s", "gui.socialInteractions.report": "Report", "gui.socialInteractions.search_empty": "Couldn't find any players with that name", "gui.socialInteractions.search_hint": "Search...", "gui.socialInteractions.server_label.multiple": "%s - %s players", "gui.socialInteractions.server_label.single": "%s - %s player", "gui.socialInteractions.show": "Show in Chat", "gui.socialInteractions.shown_in_chat": "Chat messages from %s will be shown", "gui.socialInteractions.status_blocked": "Blocked", "gui.socialInteractions.status_blocked_offline": "Blocked - Offline", "gui.socialInteractions.status_hidden": "Hidden", "gui.socialInteractions.status_hidden_offline": "Hidden - Offline", "gui.socialInteractions.status_offline": "Offline", "gui.socialInteractions.tab_all": "All", "gui.socialInteractions.tab_blocked": "Blocked", "gui.socialInteractions.tab_hidden": "Hidden", "gui.socialInteractions.title": "Social Interactions", "gui.socialInteractions.tooltip.hide": "Hide messages", "gui.socialInteractions.tooltip.report": "Report player", "gui.socialInteractions.tooltip.report.disabled": "The reporting service is unavailable", "gui.socialInteractions.tooltip.report.no_messages": "No reportable messages from player %s", "gui.socialInteractions.tooltip.report.not_reportable": "This player can't be reported, because their chat messages can't be verified on this server", "gui.socialInteractions.tooltip.show": "Show messages", "gui.stats": "Statistics", "gui.toMenu": "Back to Server List", "gui.toRealms": "Back to Realms List", "gui.toTitle": "Back to Title Screen", "gui.toWorld": "Back to World List", "gui.up": "Up", "gui.yes": "Yes", "hanging_sign.edit": "Edit Hanging Sign Message", "instrument.minecraft.admire_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.call_goat_horn": "Call", "instrument.minecraft.dream_goat_horn": "Dream", "instrument.minecraft.feel_goat_horn": "Feel", "instrument.minecraft.ponder_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.seek_goat_horn": "Seek", "instrument.minecraft.sing_goat_horn": "Sing", "instrument.minecraft.yearn_goat_horn": "Yearn", "inventory.binSlot": "<PERSON><PERSON><PERSON>em", "inventory.hotbarInfo": "Save hotbar with %1$s+%2$s", "inventory.hotbarSaved": "Item hotbar saved (restore with %1$s+%2$s)", "item_modifier.unknown": "Unknown item modifier: %s", "item.canBreak": "Can break:", "item.canPlace": "Can be placed on:", "item.color": "Color: %s", "item.disabled": "Disabled item", "item.durability": "Durability: %s / %s", "item.dyed": "Dyed", "item.minecraft.acacia_boat": "Acacia Boat", "item.minecraft.acacia_chest_boat": "Acacia Boat with Chest", "item.minecraft.allay_spawn_egg": "Allay Spawn Egg", "item.minecraft.amethyst_shard": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.angler_pottery_shard": "Angler Pottery Shard", "item.minecraft.angler_pottery_sherd": "Angler Pottery Sherd", "item.minecraft.apple": "Apple", "item.minecraft.archer_pottery_shard": "Archer Pottery Shard", "item.minecraft.archer_pottery_sherd": "Archer <PERSON>y Sherd", "item.minecraft.armor_stand": "Armor Stand", "item.minecraft.arms_up_pottery_shard": "Arms Up Pottery Shard", "item.minecraft.arms_up_pottery_sherd": "Arms Up Pottery Sherd", "item.minecraft.arrow": "Arrow", "item.minecraft.axolotl_bucket": "Bucket of Axolotl", "item.minecraft.axolotl_spawn_egg": "Axolotl Spawn Egg", "item.minecraft.baked_potato": "Baked Potato", "item.minecraft.bamboo_chest_raft": "Bamboo Raft with Chest", "item.minecraft.bamboo_raft": "Bamboo Raft", "item.minecraft.bat_spawn_egg": "Bat Spawn Egg", "item.minecraft.bee_spawn_egg": "Bee Spawn Egg", "item.minecraft.beef": "Raw Beef", "item.minecraft.beetroot": "Beetroot", "item.minecraft.beetroot_seeds": "Beetroot Seeds", "item.minecraft.beetroot_soup": "Beetroot Soup", "item.minecraft.birch_boat": "<PERSON> Boat", "item.minecraft.birch_chest_boat": "<PERSON> Boat with Chest", "item.minecraft.black_dye": "Black Dye", "item.minecraft.blade_pottery_shard": "Blade Pottery Shard", "item.minecraft.blade_pottery_sherd": "Blade Pottery Sherd", "item.minecraft.blaze_powder": "<PERSON>", "item.minecraft.blaze_rod": "<PERSON>", "item.minecraft.blaze_spawn_egg": "Blaze Spawn Egg", "item.minecraft.blue_dye": "Blue Dye", "item.minecraft.bone": "Bone", "item.minecraft.bone_meal": "<PERSON>", "item.minecraft.book": "Book", "item.minecraft.bow": "Bow", "item.minecraft.bowl": "Bowl", "item.minecraft.bread": "Bread", "item.minecraft.brewer_pottery_shard": "Brewer Pottery Shard", "item.minecraft.brewer_pottery_sherd": "Brewer Pottery Sherd", "item.minecraft.brewing_stand": "Brewing Stand", "item.minecraft.brick": "Brick", "item.minecraft.brown_dye": "<PERSON>", "item.minecraft.brush": "Brush", "item.minecraft.bucket": "Bucket", "item.minecraft.bundle": "Bundle", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Burn Pottery Shard", "item.minecraft.burn_pottery_sherd": "Burn Pottery Sherd", "item.minecraft.camel_spawn_egg": "Camel Spawn Egg", "item.minecraft.carrot": "Carrot", "item.minecraft.carrot_on_a_stick": "Carrot on a Stick", "item.minecraft.cat_spawn_egg": "Cat Spawn Egg", "item.minecraft.cauldron": "<PERSON><PERSON><PERSON>", "item.minecraft.cave_spider_spawn_egg": "Cave Spider Spawn Egg", "item.minecraft.chainmail_boots": "Chainmail Boots", "item.minecraft.chainmail_chestplate": "Chainmail Chestplate", "item.minecraft.chainmail_helmet": "Chainmail Helmet", "item.minecraft.chainmail_leggings": "Chainmail Leggings", "item.minecraft.charcoal": "Charc<PERSON>l", "item.minecraft.cherry_boat": "Cherry Boat", "item.minecraft.cherry_chest_boat": "Cherry Boat with Chest", "item.minecraft.chest_minecart": "Minecart with Chest", "item.minecraft.chicken": "Raw Chicken", "item.minecraft.chicken_spawn_egg": "Chicken Spawn Egg", "item.minecraft.chorus_fruit": "Chorus Fruit", "item.minecraft.clay_ball": "<PERSON>", "item.minecraft.clock": "Clock", "item.minecraft.coal": "Coal", "item.minecraft.cocoa_beans": "Cocoa Beans", "item.minecraft.cod": "Raw Cod", "item.minecraft.cod_bucket": "Bucket of Cod", "item.minecraft.cod_spawn_egg": "Cod Spawn Egg", "item.minecraft.command_block_minecart": "Minecart with Command Block", "item.minecraft.compass": "<PERSON>mp<PERSON>", "item.minecraft.cooked_beef": "Steak", "item.minecraft.cooked_chicken": "Cooked Chicken", "item.minecraft.cooked_cod": "Cooked Cod", "item.minecraft.cooked_mutton": "Cooked <PERSON>tton", "item.minecraft.cooked_porkchop": "Cooked Porkchop", "item.minecraft.cooked_rabbit": "Cooked Rabbit", "item.minecraft.cooked_salmon": "Cooked Salmon", "item.minecraft.cookie": "<PERSON><PERSON>", "item.minecraft.copper_ingot": "Copper Ingot", "item.minecraft.cow_spawn_egg": "Cow Spawn Egg", "item.minecraft.creeper_banner_pattern": "<PERSON>", "item.minecraft.creeper_banner_pattern.desc": "Creeper Charge", "item.minecraft.creeper_spawn_egg": "Creeper Spawn Egg", "item.minecraft.crossbow": "Crossbow", "item.minecraft.crossbow.projectile": "Projectile:", "item.minecraft.cyan_dye": "<PERSON><PERSON>", "item.minecraft.danger_pottery_shard": "Danger Pottery Shard", "item.minecraft.danger_pottery_sherd": "Danger Pottery Sherd", "item.minecraft.dark_oak_boat": "Dark Oak Boat", "item.minecraft.dark_oak_chest_boat": "Dark Oak Boat with Chest", "item.minecraft.debug_stick": "Debug Stick", "item.minecraft.debug_stick.empty": "%s has no properties", "item.minecraft.debug_stick.select": "selected \"%s\" (%s)", "item.minecraft.debug_stick.update": "\"%s\" to %s", "item.minecraft.diamond": "Diamond", "item.minecraft.diamond_axe": "Diamond Axe", "item.minecraft.diamond_boots": "Diamond Boots", "item.minecraft.diamond_chestplate": "Diamond Chestplate", "item.minecraft.diamond_helmet": "Diamond Helmet", "item.minecraft.diamond_hoe": "Diamond Hoe", "item.minecraft.diamond_horse_armor": "Diamond Horse Armor", "item.minecraft.diamond_leggings": "Diamond Leggings", "item.minecraft.diamond_pickaxe": "Diamond Pickaxe", "item.minecraft.diamond_shovel": "Diamond Shovel", "item.minecraft.diamond_sword": "Diamond Sword", "item.minecraft.disc_fragment_5": "Disc Fragment", "item.minecraft.disc_fragment_5.desc": "Music Disc - 5", "item.minecraft.dolphin_spawn_egg": "Dolphin Spawn Egg", "item.minecraft.donkey_spawn_egg": "Donkey Spawn Egg", "item.minecraft.dragon_breath": "Dragon's Breath", "item.minecraft.dried_kelp": "<PERSON><PERSON>", "item.minecraft.drowned_spawn_egg": "Drowned Spawn Egg", "item.minecraft.echo_shard": "Echo Shard", "item.minecraft.egg": "Egg", "item.minecraft.elder_guardian_spawn_egg": "Elder Guardian Spawn Egg", "item.minecraft.elytra": "Elytra", "item.minecraft.emerald": "Emerald", "item.minecraft.enchanted_book": "Enchanted Book", "item.minecraft.enchanted_golden_apple": "Enchanted Golden Apple", "item.minecraft.end_crystal": "End Crystal", "item.minecraft.ender_dragon_spawn_egg": "Ender Dragon Spawn Egg", "item.minecraft.ender_eye": "Eye of <PERSON>er", "item.minecraft.ender_pearl": "<PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "Enderman Spawn Egg", "item.minecraft.endermite_spawn_egg": "Endermite Spawn Egg", "item.minecraft.evoker_spawn_egg": "Evoker Spawn Egg", "item.minecraft.experience_bottle": "Bottle o' Enchanting", "item.minecraft.explorer_pottery_shard": "Explorer <PERSON><PERSON> Shard", "item.minecraft.explorer_pottery_sherd": "Explorer <PERSON><PERSON> She<PERSON>", "item.minecraft.feather": "<PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "Fermented Spider Eye", "item.minecraft.filled_map": "Map", "item.minecraft.fire_charge": "Fire Charge", "item.minecraft.firework_rocket": "Firework Rocket", "item.minecraft.firework_rocket.flight": "Flight Duration:", "item.minecraft.firework_star": "Firework Star", "item.minecraft.firework_star.black": "Black", "item.minecraft.firework_star.blue": "Blue", "item.minecraft.firework_star.brown": "<PERSON>", "item.minecraft.firework_star.custom_color": "Custom", "item.minecraft.firework_star.cyan": "<PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "Fade to", "item.minecraft.firework_star.flicker": "Twinkle", "item.minecraft.firework_star.gray": "<PERSON>", "item.minecraft.firework_star.green": "Green", "item.minecraft.firework_star.light_blue": "Light Blue", "item.minecraft.firework_star.light_gray": "Light Gray", "item.minecraft.firework_star.lime": "Lime", "item.minecraft.firework_star.magenta": "Ma<PERSON><PERSON>", "item.minecraft.firework_star.orange": "Orange", "item.minecraft.firework_star.pink": "Pink", "item.minecraft.firework_star.purple": "Purple", "item.minecraft.firework_star.red": "Red", "item.minecraft.firework_star.shape": "Unknown <PERSON><PERSON>pe", "item.minecraft.firework_star.shape.burst": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape.creeper": "Creeper-shaped", "item.minecraft.firework_star.shape.large_ball": "Large Ball", "item.minecraft.firework_star.shape.small_ball": "Small Ball", "item.minecraft.firework_star.shape.star": "Star-shaped", "item.minecraft.firework_star.trail": "Trail", "item.minecraft.firework_star.white": "White", "item.minecraft.firework_star.yellow": "Yellow", "item.minecraft.fishing_rod": "Fishing Rod", "item.minecraft.flint": "Flint", "item.minecraft.flint_and_steel": "Flint and Steel", "item.minecraft.flower_banner_pattern": "<PERSON>", "item.minecraft.flower_banner_pattern.desc": "Flower Charge", "item.minecraft.flower_pot": "Flower Pot", "item.minecraft.fox_spawn_egg": "Fox Spawn Egg", "item.minecraft.friend_pottery_shard": "<PERSON> <PERSON><PERSON>", "item.minecraft.friend_pottery_sherd": "<PERSON> <PERSON>", "item.minecraft.frog_spawn_egg": "Frog Spawn Egg", "item.minecraft.furnace_minecart": "Minecart with Furnace", "item.minecraft.ghast_spawn_egg": "Ghast Spawn Egg", "item.minecraft.ghast_tear": "Ghast Tear", "item.minecraft.glass_bottle": "Glass Bottle", "item.minecraft.glistering_melon_slice": "Glistering <PERSON><PERSON>", "item.minecraft.globe_banner_pattern": "<PERSON>", "item.minecraft.globe_banner_pattern.desc": "Globe", "item.minecraft.glow_berries": "Glow Berries", "item.minecraft.glow_ink_sac": "Glow Ink Sac", "item.minecraft.glow_item_frame": "G<PERSON> Item <PERSON>", "item.minecraft.glow_squid_spawn_egg": "Glow Squid Spawn Egg", "item.minecraft.glowstone_dust": "Glowstone Dust", "item.minecraft.goat_horn": "<PERSON><PERSON>", "item.minecraft.goat_spawn_egg": "Goat Spawn Egg", "item.minecraft.gold_ingot": "Gold Ingot", "item.minecraft.gold_nugget": "Gold Nugget", "item.minecraft.golden_apple": "Golden Apple", "item.minecraft.golden_axe": "Golden Axe", "item.minecraft.golden_boots": "Golden Boots", "item.minecraft.golden_carrot": "Golden Carrot", "item.minecraft.golden_chestplate": "Golden Chestplate", "item.minecraft.golden_helmet": "Golden Helmet", "item.minecraft.golden_hoe": "Golden Hoe", "item.minecraft.golden_horse_armor": "Golden Horse Armor", "item.minecraft.golden_leggings": "Golden Leggings", "item.minecraft.golden_pickaxe": "Golden Pickaxe", "item.minecraft.golden_shovel": "Golden Shovel", "item.minecraft.golden_sword": "Golden Sword", "item.minecraft.gray_dye": "<PERSON>", "item.minecraft.green_dye": "Green Dye", "item.minecraft.guardian_spawn_egg": "Guardian Spawn Egg", "item.minecraft.gunpowder": "Gunpowder", "item.minecraft.heart_of_the_sea": "Heart of the Sea", "item.minecraft.heart_pottery_shard": "Heart Pottery Shard", "item.minecraft.heart_pottery_sherd": "Heart Pottery Sherd", "item.minecraft.heartbreak_pottery_shard": "Heartbreak Pottery Shard", "item.minecraft.heartbreak_pottery_sherd": "Heartbreak Pottery Sherd", "item.minecraft.hoglin_spawn_egg": "Hoglin Spawn Egg", "item.minecraft.honey_bottle": "<PERSON>", "item.minecraft.honeycomb": "Honeycomb", "item.minecraft.hopper_minecart": "Minecart with <PERSON>", "item.minecraft.horse_spawn_egg": "Horse Spawn Egg", "item.minecraft.howl_pottery_shard": "Howl Pottery Shard", "item.minecraft.howl_pottery_sherd": "Howl Pottery Sherd", "item.minecraft.husk_spawn_egg": "Husk Spawn Egg", "item.minecraft.ink_sac": "Ink Sac", "item.minecraft.iron_axe": "Iron Axe", "item.minecraft.iron_boots": "Iron Boots", "item.minecraft.iron_chestplate": "Iron Chestplate", "item.minecraft.iron_golem_spawn_egg": "Iron Golem Spawn Egg", "item.minecraft.iron_helmet": "Iron Helmet", "item.minecraft.iron_hoe": "Iron Hoe", "item.minecraft.iron_horse_armor": "Iron Horse Armor", "item.minecraft.iron_ingot": "Iron Ingot", "item.minecraft.iron_leggings": "Iron Leggings", "item.minecraft.iron_nugget": "Iron Nugget", "item.minecraft.iron_pickaxe": "Iron Pickaxe", "item.minecraft.iron_shovel": "Iron Shovel", "item.minecraft.iron_sword": "Iron Sword", "item.minecraft.item_frame": "<PERSON><PERSON>", "item.minecraft.jungle_boat": "Jungle Boat", "item.minecraft.jungle_chest_boat": "Jungle Boat with Chest", "item.minecraft.knowledge_book": "Knowledge Book", "item.minecraft.lapis_lazuli": "<PERSON><PERSON>", "item.minecraft.lava_bucket": "<PERSON><PERSON>et", "item.minecraft.lead": "Lead", "item.minecraft.leather": "Leather", "item.minecraft.leather_boots": "<PERSON><PERSON>", "item.minecraft.leather_chestplate": "<PERSON><PERSON>", "item.minecraft.leather_helmet": "Leather Cap", "item.minecraft.leather_horse_armor": "Leather Horse Armor", "item.minecraft.leather_leggings": "<PERSON><PERSON>", "item.minecraft.light_blue_dye": "Light Blue Dye", "item.minecraft.light_gray_dye": "Light Gray D<PERSON>", "item.minecraft.lime_dye": "Lime Dye", "item.minecraft.lingering_potion": "Lingering Potion", "item.minecraft.lingering_potion.effect.awkward": "Awkward Lingering Potion", "item.minecraft.lingering_potion.effect.empty": "Lingering Uncraftable Potion", "item.minecraft.lingering_potion.effect.fire_resistance": "Lingering Potion of Fire Resistance", "item.minecraft.lingering_potion.effect.harming": "Lingering Potion of Harming", "item.minecraft.lingering_potion.effect.healing": "Lingering Potion of Healing", "item.minecraft.lingering_potion.effect.invisibility": "Lingering Potion of Invisibility", "item.minecraft.lingering_potion.effect.leaping": "Lingering Potion of Leaping", "item.minecraft.lingering_potion.effect.levitation": "Lingering Potion of Levitation", "item.minecraft.lingering_potion.effect.luck": "Lingering Potion of Luck", "item.minecraft.lingering_potion.effect.mundane": "Mu<PERSON>ne Lingering Potion", "item.minecraft.lingering_potion.effect.night_vision": "Lingering Potion of Night Vision", "item.minecraft.lingering_potion.effect.poison": "Lingering Potion of Poison", "item.minecraft.lingering_potion.effect.regeneration": "Lingering Potion of Regeneration", "item.minecraft.lingering_potion.effect.slow_falling": "Lingering Potion of Slow Falling", "item.minecraft.lingering_potion.effect.slowness": "Lingering Potion of Slowness", "item.minecraft.lingering_potion.effect.strength": "Lingering Potion of Strength", "item.minecraft.lingering_potion.effect.swiftness": "Lingering Potion of Swiftness", "item.minecraft.lingering_potion.effect.thick": "T<PERSON>k Lingering Potion", "item.minecraft.lingering_potion.effect.turtle_master": "Lingering Potion of the Turtle Master", "item.minecraft.lingering_potion.effect.water": "Lingering Water Bottle", "item.minecraft.lingering_potion.effect.water_breathing": "Lingering Potion of Water Breathing", "item.minecraft.lingering_potion.effect.weakness": "Lingering Potion of Weakness", "item.minecraft.llama_spawn_egg": "Llama Spawn Egg", "item.minecraft.lodestone_compass": "Lodestone Compass", "item.minecraft.magenta_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.magma_cream": "Magma Cream", "item.minecraft.magma_cube_spawn_egg": "Magma Cube Spawn Egg", "item.minecraft.mangrove_boat": "Mangrove Boat", "item.minecraft.mangrove_chest_boat": "Mangrove Boat with Chest", "item.minecraft.map": "Empty Map", "item.minecraft.melon_seeds": "<PERSON>on Seeds", "item.minecraft.melon_slice": "<PERSON><PERSON>", "item.minecraft.milk_bucket": "Milk Bucket", "item.minecraft.minecart": "Minecart", "item.minecraft.miner_pottery_shard": "Miner <PERSON><PERSON>", "item.minecraft.miner_pottery_sherd": "Miner <PERSON>", "item.minecraft.mojang_banner_pattern": "<PERSON>", "item.minecraft.mojang_banner_pattern.desc": "Thing", "item.minecraft.mooshroom_spawn_egg": "Mooshroom Spawn Egg", "item.minecraft.mourner_pottery_shard": "Mourner Pottery Shard", "item.minecraft.mourner_pottery_sherd": "Mourner Pottery Sherd", "item.minecraft.mule_spawn_egg": "Mule Spawn Egg", "item.minecraft.mushroom_stew": "Mushroom Stew", "item.minecraft.music_disc_5": "Music Disc", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_11": "Music Disc", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Music Disc", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_blocks": "Music Disc", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Music Disc", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Music Disc", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_far": "Music Disc", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_mall": "Music Disc", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Music Disc", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Music Disc", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Music Disc", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_relic": "Music Disc", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Music Disc", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Music Disc", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_wait": "Music Disc", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Music Disc", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "<PERSON>", "item.minecraft.name_tag": "Name Tag", "item.minecraft.nautilus_shell": "Nautilus Shell", "item.minecraft.nether_brick": "Nether Brick", "item.minecraft.nether_star": "Nether Star", "item.minecraft.nether_wart": "Nether Wart", "item.minecraft.netherite_axe": "Netherite Axe", "item.minecraft.netherite_boots": "Netherite Boots", "item.minecraft.netherite_chestplate": "Netherite Chestplate", "item.minecraft.netherite_helmet": "Netherite Helmet", "item.minecraft.netherite_hoe": "Netherite Hoe", "item.minecraft.netherite_ingot": "Netherite Ingot", "item.minecraft.netherite_leggings": "Netherite Leggings", "item.minecraft.netherite_pickaxe": "Netherite Pickaxe", "item.minecraft.netherite_scrap": "Netherite Scrap", "item.minecraft.netherite_shovel": "<PERSON><PERSON><PERSON>", "item.minecraft.netherite_sword": "Netherite Sword", "item.minecraft.oak_boat": "Oak Boat", "item.minecraft.oak_chest_boat": "Oak Boat with Chest", "item.minecraft.ocelot_spawn_egg": "Ocelot Spawn Egg", "item.minecraft.orange_dye": "Orange Dye", "item.minecraft.painting": "Painting", "item.minecraft.panda_spawn_egg": "Panda Spawn Egg", "item.minecraft.paper": "Paper", "item.minecraft.parrot_spawn_egg": "Parrot Spawn Egg", "item.minecraft.phantom_membrane": "Phantom Membrane", "item.minecraft.phantom_spawn_egg": "Phantom Spawn Egg", "item.minecraft.pig_spawn_egg": "Pig Spawn Egg", "item.minecraft.piglin_banner_pattern": "<PERSON>", "item.minecraft.piglin_banner_pattern.desc": "<PERSON>nout", "item.minecraft.piglin_brute_spawn_egg": "Piglin Brute Spawn Egg", "item.minecraft.piglin_spawn_egg": "Piglin Spawn Egg", "item.minecraft.pillager_spawn_egg": "Pillager Spawn Egg", "item.minecraft.pink_dye": "Pink Dye", "item.minecraft.pitcher_plant": "Pitcher Plant", "item.minecraft.pitcher_pod": "Pitcher Pod", "item.minecraft.plenty_pottery_shard": "Plenty Pottery Shard", "item.minecraft.plenty_pottery_sherd": "Plenty Pottery Sherd", "item.minecraft.poisonous_potato": "Poisonous Potato", "item.minecraft.polar_bear_spawn_egg": "Polar Bear Spawn Egg", "item.minecraft.popped_chorus_fruit": "Popped Chorus Fruit", "item.minecraft.porkchop": "Raw Porkchop", "item.minecraft.potato": "Potato", "item.minecraft.potion": "Potion", "item.minecraft.potion.effect.awkward": "Awkward Potion", "item.minecraft.potion.effect.empty": "Uncraftable Potion", "item.minecraft.potion.effect.fire_resistance": "Potion of Fire Resistance", "item.minecraft.potion.effect.harming": "Potion of Harming", "item.minecraft.potion.effect.healing": "Potion of Healing", "item.minecraft.potion.effect.invisibility": "Potion of Invisibility", "item.minecraft.potion.effect.leaping": "Potion of Leaping", "item.minecraft.potion.effect.levitation": "Potion of Levitation", "item.minecraft.potion.effect.luck": "Potion of Luck", "item.minecraft.potion.effect.mundane": "<PERSON><PERSON>ne <PERSON>", "item.minecraft.potion.effect.night_vision": "Potion of Night Vision", "item.minecraft.potion.effect.poison": "Potion of Poison", "item.minecraft.potion.effect.regeneration": "Potion of Regeneration", "item.minecraft.potion.effect.slow_falling": "Potion of Slow Falling", "item.minecraft.potion.effect.slowness": "Potion of Slowness", "item.minecraft.potion.effect.strength": "Potion of Strength", "item.minecraft.potion.effect.swiftness": "Potion of Swiftness", "item.minecraft.potion.effect.thick": "Thick Potion", "item.minecraft.potion.effect.turtle_master": "Potion of the Turtle Master", "item.minecraft.potion.effect.water": "Water Bottle", "item.minecraft.potion.effect.water_breathing": "Potion of Water Breathing", "item.minecraft.potion.effect.weakness": "Potion of Weakness", "item.minecraft.pottery_shard_archer": "Archer Pottery Shard", "item.minecraft.pottery_shard_arms_up": "Arms Up Pottery Shard", "item.minecraft.pottery_shard_prize": "Prize <PERSON><PERSON> Shard", "item.minecraft.pottery_shard_skull": "Skull Pottery Shard", "item.minecraft.powder_snow_bucket": "Powder Snow Bucket", "item.minecraft.prismarine_crystals": "Prismarine Crystals", "item.minecraft.prismarine_shard": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.prize_pottery_shard": "Prize <PERSON><PERSON> Shard", "item.minecraft.prize_pottery_sherd": "Prize <PERSON><PERSON> Sherd", "item.minecraft.pufferfish": "Pufferfish", "item.minecraft.pufferfish_bucket": "Bucket of Pufferfish", "item.minecraft.pufferfish_spawn_egg": "Pufferfish Spawn Egg", "item.minecraft.pumpkin_pie": "Pumpkin Pie", "item.minecraft.pumpkin_seeds": "<PERSON><PERSON><PERSON> Seeds", "item.minecraft.purple_dye": "Purple Dye", "item.minecraft.quartz": "<PERSON><PERSON>", "item.minecraft.rabbit": "Raw Rabbit", "item.minecraft.rabbit_foot": "<PERSON>'s Foot", "item.minecraft.rabbit_hide": "<PERSON>", "item.minecraft.rabbit_spawn_egg": "Rabbit Spawn Egg", "item.minecraft.rabbit_stew": "Rabbit Stew", "item.minecraft.ravager_spawn_egg": "Ravager Spawn Egg", "item.minecraft.raw_copper": "Raw Copper", "item.minecraft.raw_gold": "Raw Gold", "item.minecraft.raw_iron": "Raw Iron", "item.minecraft.recovery_compass": "Recovery Compass", "item.minecraft.red_dye": "Red Dye", "item.minecraft.redstone": "Redstone Dust", "item.minecraft.rotten_flesh": "Rotten Flesh", "item.minecraft.saddle": "Saddle", "item.minecraft.salmon": "Raw Salmon", "item.minecraft.salmon_bucket": "Bucket of Salmon", "item.minecraft.salmon_spawn_egg": "Salmon Spawn Egg", "item.minecraft.scute": "<PERSON><PERSON>", "item.minecraft.sheaf_pottery_shard": "<PERSON><PERSON>", "item.minecraft.sheaf_pottery_sherd": "<PERSON><PERSON>", "item.minecraft.shears": "Shears", "item.minecraft.sheep_spawn_egg": "Sheep Spawn Egg", "item.minecraft.shelter_pottery_shard": "Shelter Pottery Shard", "item.minecraft.shelter_pottery_sherd": "<PERSON>lter Pottery Sherd", "item.minecraft.shield": "Shield", "item.minecraft.shield.black": "Black Shield", "item.minecraft.shield.blue": "Blue Shield", "item.minecraft.shield.brown": "Brown Shield", "item.minecraft.shield.cyan": "Cyan <PERSON>", "item.minecraft.shield.gray": "Gray Shield", "item.minecraft.shield.green": "Green Shield", "item.minecraft.shield.light_blue": "Light Blue Shield", "item.minecraft.shield.light_gray": "Light Gray Shield", "item.minecraft.shield.lime": "Lime Shield", "item.minecraft.shield.magenta": "Magenta Shield", "item.minecraft.shield.orange": "Orange Shield", "item.minecraft.shield.pink": "Pink Shield", "item.minecraft.shield.purple": "Purple Shield", "item.minecraft.shield.red": "Red Shield", "item.minecraft.shield.white": "White Shield", "item.minecraft.shield.yellow": "Yellow Shield", "item.minecraft.shulker_shell": "Shulker Shell", "item.minecraft.shulker_spawn_egg": "Shulker Spawn Egg", "item.minecraft.sign": "Sign", "item.minecraft.silverfish_spawn_egg": "Silverfish Spawn Egg", "item.minecraft.skeleton_horse_spawn_egg": "Skeleton Horse Spawn Egg", "item.minecraft.skeleton_spawn_egg": "Skeleton Spawn Egg", "item.minecraft.skull_banner_pattern": "<PERSON>", "item.minecraft.skull_banner_pattern.desc": "Skull Charge", "item.minecraft.skull_pottery_shard": "Skull Pottery Shard", "item.minecraft.skull_pottery_sherd": "Skull Pottery Sherd", "item.minecraft.slime_ball": "Slimeball", "item.minecraft.slime_spawn_egg": "Slime Spawn Egg", "item.minecraft.smithing_template": "<PERSON><PERSON> Template", "item.minecraft.smithing_template.applies_to": "Applies to:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Add ingot or crystal", "item.minecraft.smithing_template.armor_trim.applies_to": "Armor", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Add a piece of armor", "item.minecraft.smithing_template.armor_trim.ingredients": "Ingots & Crystals", "item.minecraft.smithing_template.ingredients": "Ingredients:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Add Netherite Ingot", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Diamond Equipment", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Add diamond armor, weapon, or tool", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Netherite Ingot", "item.minecraft.smithing_template.upgrade": "Upgrade: ", "item.minecraft.sniffer_spawn_egg": "Sniffer Spawn Egg", "item.minecraft.snort_pottery_shard": "Snort Pottery Shard", "item.minecraft.snort_pottery_sherd": "<PERSON><PERSON><PERSON> Pottery Sherd", "item.minecraft.snow_golem_spawn_egg": "Snow Golem Spawn Egg", "item.minecraft.snowball": "Snowball", "item.minecraft.spectral_arrow": "Spectral Arrow", "item.minecraft.spider_eye": "Spider Eye", "item.minecraft.spider_spawn_egg": "Spider Spawn Egg", "item.minecraft.splash_potion": "Splash Potion", "item.minecraft.splash_potion.effect.awkward": "Awkward Splash Potion", "item.minecraft.splash_potion.effect.empty": "Splash Uncraftable Potion", "item.minecraft.splash_potion.effect.fire_resistance": "Splash Potion of Fire Resistance", "item.minecraft.splash_potion.effect.harming": "Splash Potion of Harming", "item.minecraft.splash_potion.effect.healing": "Splash Potion of Healing", "item.minecraft.splash_potion.effect.invisibility": "Splash Potion of Invisibility", "item.minecraft.splash_potion.effect.leaping": "Splash Potion of Leaping", "item.minecraft.splash_potion.effect.levitation": "Splash Potion of Levitation", "item.minecraft.splash_potion.effect.luck": "Splash Potion of Luck", "item.minecraft.splash_potion.effect.mundane": "<PERSON><PERSON>ne Splash Potion", "item.minecraft.splash_potion.effect.night_vision": "Splash Potion of Night Vision", "item.minecraft.splash_potion.effect.poison": "Splash Potion of Poison", "item.minecraft.splash_potion.effect.regeneration": "Splash Potion of Regeneration", "item.minecraft.splash_potion.effect.slow_falling": "Splash Potion of Slow Falling", "item.minecraft.splash_potion.effect.slowness": "Splash Potion of Slowness", "item.minecraft.splash_potion.effect.strength": "Splash Potion of Strength", "item.minecraft.splash_potion.effect.swiftness": "Splash Potion of Swiftness", "item.minecraft.splash_potion.effect.thick": "T<PERSON>k Splash Potion", "item.minecraft.splash_potion.effect.turtle_master": "Splash Potion of the Turtle Master", "item.minecraft.splash_potion.effect.water": "Splash Water Bottle", "item.minecraft.splash_potion.effect.water_breathing": "Splash Potion of Water Breathing", "item.minecraft.splash_potion.effect.weakness": "Splash Potion of Weakness", "item.minecraft.spruce_boat": "Spruce Boat", "item.minecraft.spruce_chest_boat": "Spruce Boat with Chest", "item.minecraft.spyglass": "Spyglass", "item.minecraft.squid_spawn_egg": "Squid Spawn Egg", "item.minecraft.stick": "Stick", "item.minecraft.stone_axe": "Stone Axe", "item.minecraft.stone_hoe": "Stone Hoe", "item.minecraft.stone_pickaxe": "<PERSON>", "item.minecraft.stone_shovel": "<PERSON>el", "item.minecraft.stone_sword": "Stone Sword", "item.minecraft.stray_spawn_egg": "Stray Spawn Egg", "item.minecraft.strider_spawn_egg": "Strider Spawn Egg", "item.minecraft.string": "String", "item.minecraft.sugar": "Sugar", "item.minecraft.suspicious_stew": "Suspicious Stew", "item.minecraft.sweet_berries": "Sweet Berries", "item.minecraft.tadpole_bucket": "Bucket of Tadpole", "item.minecraft.tadpole_spawn_egg": "Tadpole Spawn Egg", "item.minecraft.tipped_arrow": "Tipped Arrow", "item.minecraft.tipped_arrow.effect.awkward": "Tipped Arrow", "item.minecraft.tipped_arrow.effect.empty": "Uncraftable Tipped Arrow", "item.minecraft.tipped_arrow.effect.fire_resistance": "Arrow of Fire Resistance", "item.minecraft.tipped_arrow.effect.harming": "Arrow of Harming", "item.minecraft.tipped_arrow.effect.healing": "Arrow of Healing", "item.minecraft.tipped_arrow.effect.invisibility": "Arrow of Invisibility", "item.minecraft.tipped_arrow.effect.leaping": "Arrow of Leaping", "item.minecraft.tipped_arrow.effect.levitation": "Arrow of Levitation", "item.minecraft.tipped_arrow.effect.luck": "Arrow of Luck", "item.minecraft.tipped_arrow.effect.mundane": "Tipped Arrow", "item.minecraft.tipped_arrow.effect.night_vision": "Arrow of Night Vision", "item.minecraft.tipped_arrow.effect.poison": "Arrow of Poison", "item.minecraft.tipped_arrow.effect.regeneration": "Arrow of Regeneration", "item.minecraft.tipped_arrow.effect.slow_falling": "Arrow of Slow Falling", "item.minecraft.tipped_arrow.effect.slowness": "Arrow of Slowness", "item.minecraft.tipped_arrow.effect.strength": "Arrow of Strength", "item.minecraft.tipped_arrow.effect.swiftness": "Arrow of Swiftness", "item.minecraft.tipped_arrow.effect.thick": "Tipped Arrow", "item.minecraft.tipped_arrow.effect.turtle_master": "Arrow of the Turtle Master", "item.minecraft.tipped_arrow.effect.water": "Arrow of Splashing", "item.minecraft.tipped_arrow.effect.water_breathing": "Arrow of Water Breathing", "item.minecraft.tipped_arrow.effect.weakness": "Arrow of Weakness", "item.minecraft.tnt_minecart": "Minecart with TNT", "item.minecraft.torchflower_seeds": "Torchflower Seeds", "item.minecraft.totem_of_undying": "Totem of Undying", "item.minecraft.trader_llama_spawn_egg": "Trader <PERSON>lama Spawn Egg", "item.minecraft.trident": "Trident", "item.minecraft.tropical_fish": "Tropical Fish", "item.minecraft.tropical_fish_bucket": "Bucket of Tropical Fish", "item.minecraft.tropical_fish_spawn_egg": "Tropical Fish Spawn Egg", "item.minecraft.turtle_helmet": "Turtle Shell", "item.minecraft.turtle_spawn_egg": "Turtle Spawn Egg", "item.minecraft.vex_spawn_egg": "Vex Spawn Egg", "item.minecraft.villager_spawn_egg": "Villager Spawn Egg", "item.minecraft.vindicator_spawn_egg": "Vindicator Spawn Egg", "item.minecraft.wandering_trader_spawn_egg": "Wandering Trader Spawn Egg", "item.minecraft.warden_spawn_egg": "Warden Spawn Egg", "item.minecraft.warped_fungus_on_a_stick": "Warped Fungus on a Stick", "item.minecraft.water_bucket": "Water Bucket", "item.minecraft.wheat": "Wheat", "item.minecraft.wheat_seeds": "Wheat Seeds", "item.minecraft.white_dye": "White Dye", "item.minecraft.witch_spawn_egg": "Witch Spawn Egg", "item.minecraft.wither_skeleton_spawn_egg": "Wither Skeleton Spawn Egg", "item.minecraft.wither_spawn_egg": "Wither Spawn Egg", "item.minecraft.wolf_spawn_egg": "Wolf Spawn Egg", "item.minecraft.wooden_axe": "Wooden Axe", "item.minecraft.wooden_hoe": "<PERSON><PERSON>e", "item.minecraft.wooden_pickaxe": "<PERSON><PERSON> Pick<PERSON>e", "item.minecraft.wooden_shovel": "<PERSON><PERSON>", "item.minecraft.wooden_sword": "Wooden Sword", "item.minecraft.writable_book": "Book and Quill", "item.minecraft.written_book": "Written Book", "item.minecraft.yellow_dye": "Yellow Dye", "item.minecraft.zoglin_spawn_egg": "Zoglin Spawn Egg", "item.minecraft.zombie_horse_spawn_egg": "Zombie Horse Spawn Egg", "item.minecraft.zombie_spawn_egg": "Zombie Spawn Egg", "item.minecraft.zombie_villager_spawn_egg": "Zombie Villager Spawn Egg", "item.minecraft.zombified_piglin_spawn_egg": "Zombified Piglin Spawn Egg", "item.modifiers.chest": "When on Body:", "item.modifiers.feet": "When on Feet:", "item.modifiers.head": "When on Head:", "item.modifiers.legs": "When on Legs:", "item.modifiers.mainhand": "When in Main Hand:", "item.modifiers.offhand": "When in Off Hand:", "item.nbt_tags": "NBT: %s tag(s)", "item.unbreakable": "Unbreakable", "itemGroup.buildingBlocks": "Building Blocks", "itemGroup.coloredBlocks": "Colored Blocks", "itemGroup.combat": "Combat", "itemGroup.consumables": "Consumables", "itemGroup.crafting": "Crafting", "itemGroup.foodAndDrink": "Food & Drinks", "itemGroup.functional": "Functional Blocks", "itemGroup.hotbar": "Saved Hotbars", "itemGroup.ingredients": "Ingredients", "itemGroup.inventory": "Survival Inventory", "itemGroup.natural": "Natural Blocks", "itemGroup.op": "Operator Utilities", "itemGroup.redstone": "Redstone Blocks", "itemGroup.search": "Search Items", "itemGroup.spawnEggs": "Spawn Eggs", "itemGroup.tools": "Tools & Utilities", "jigsaw_block.final_state": "Turns into:", "jigsaw_block.generate": "Generate", "jigsaw_block.joint_label": "Joint Type:", "jigsaw_block.joint.aligned": "Aligned", "jigsaw_block.joint.rollable": "Rollable", "jigsaw_block.keep_jigsaws": "Keep Jigsaws", "jigsaw_block.levels": "Levels: %s", "jigsaw_block.name": "Name:", "jigsaw_block.pool": "Target Pool:", "jigsaw_block.target": "Target Name:", "key.advancements": "Advancements", "key.attack": "Attack/Destroy", "key.back": "Walk Backwards", "key.categories.creative": "Creative Mode", "key.categories.gameplay": "Gameplay", "key.categories.inventory": "Inventory", "key.categories.misc": "Miscellaneous", "key.categories.movement": "Movement", "key.categories.multiplayer": "Multiplayer", "key.categories.ui": "Game Interface", "key.chat": "Open Chat", "key.command": "Open Command", "key.drop": "Drop Selected Item", "key.forward": "Walk Forwards", "key.fullscreen": "Toggle Fullscreen", "key.hotbar.1": "Hotbar Slot 1", "key.hotbar.2": "Hotbar Slot 2", "key.hotbar.3": "Hotbar Slot 3", "key.hotbar.4": "Hotbar Slot 4", "key.hotbar.5": "Hotbar Slot 5", "key.hotbar.6": "Hotbar Slot 6", "key.hotbar.7": "Hotbar Slot 7", "key.hotbar.8": "Hotbar Slot 8", "key.hotbar.9": "Hotbar Slot 9", "key.inventory": "Open/Close Inventory", "key.jump": "Jump", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Backspace", "key.keyboard.caps.lock": "Caps Lock", "key.keyboard.comma": ",", "key.keyboard.delete": "Delete", "key.keyboard.down": "Down Arrow", "key.keyboard.end": "End", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Escape", "key.keyboard.f1": "F1", "key.keyboard.f2": "F2", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Home", "key.keyboard.insert": "Insert", "key.keyboard.keypad.0": "Keypad 0", "key.keyboard.keypad.1": "Keypad 1", "key.keyboard.keypad.2": "Keypad 2", "key.keyboard.keypad.3": "Keypad 3", "key.keyboard.keypad.4": "Keypad 4", "key.keyboard.keypad.5": "Keypad 5", "key.keyboard.keypad.6": "Keypad 6", "key.keyboard.keypad.7": "Keypad 7", "key.keyboard.keypad.8": "Keypad 8", "key.keyboard.keypad.9": "Keypad 9", "key.keyboard.keypad.add": "Keypad +", "key.keyboard.keypad.decimal": "Keypad Decimal", "key.keyboard.keypad.divide": "Keypad /", "key.keyboard.keypad.enter": "Keypad Enter", "key.keyboard.keypad.equal": "Keypad =", "key.keyboard.keypad.multiply": "Keypad *", "key.keyboard.keypad.subtract": "Keypad -", "key.keyboard.left": "Left Arrow", "key.keyboard.left.alt": "Left Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Left Control", "key.keyboard.left.shift": "Left Shift", "key.keyboard.left.win": "Left Win", "key.keyboard.menu": "<PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num Lock", "key.keyboard.page.down": "Page Down", "key.keyboard.page.up": "Page Up", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "Print Screen", "key.keyboard.right": "Right Arrow", "key.keyboard.right.alt": "Right Alt", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Right Control", "key.keyboard.right.shift": "Right Shift", "key.keyboard.right.win": "Right Win", "key.keyboard.scroll.lock": "<PERSON><PERSON> Lock", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Space", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "Not Bound", "key.keyboard.up": "Up Arrow", "key.keyboard.world.1": "World 1", "key.keyboard.world.2": "World 2", "key.left": "Strafe Left", "key.loadToolbarActivator": "Load Hotbar Activator", "key.mouse": "Button %1$s", "key.mouse.left": "Left Button", "key.mouse.middle": "Middle Button", "key.mouse.right": "Right Button", "key.pickItem": "Pick Block", "key.playerlist": "List Players", "key.right": "Strafe Right", "key.saveToolbarActivator": "Save Hotbar Activator", "key.screenshot": "Take Screenshot", "key.smoothCamera": "Toggle Cinematic Camera", "key.sneak": "<PERSON><PERSON><PERSON>", "key.socialInteractions": "Social Interactions Screen", "key.spectatorOutlines": "Highlight Players (Spectators)", "key.sprint": "Sprint", "key.swapOffhand": "Swap Item With Offhand", "key.togglePerspective": "Toggle Perspective", "key.use": "Use Item/Place Block", "language.code": "en_us", "language.name": "English", "language.region": "United States", "lanServer.otherPlayers": "Settings for Other Players", "lanServer.port": "Port Number", "lanServer.port.invalid": "Not a valid port.\nLeave the edit box empty or enter a number between 1024 and 65535.", "lanServer.port.invalid.new": "Not a valid port.\nLeave the edit box empty or enter a number between %s and %s.", "lanServer.port.unavailable": "Port not available.\nLeave the edit box empty or enter a different number between 1024 and 65535.", "lanServer.port.unavailable.new": "Port not available.\nLeave the edit box empty or enter a different number between %s and %s.", "lanServer.scanning": "Scanning for games on your local network", "lanServer.start": "Start LAN World", "lanServer.title": "LAN World", "lectern.take_book": "Take Book", "mco.account.privacy.info": "Read more about Mojang and privacy laws", "mco.account.privacyinfo": "Mojang implements certain procedures to help protect children and their privacy including complying with the Children’s Online Privacy Protection Act (COPPA) and General Data Protection Regulation (GDPR).\n\nYou may need to obtain parental consent before accessing your Realms account.\n\nIf you have an older Minecraft account (you log in with your username), you need to migrate the account to a Mojang account in order to access Realms.", "mco.account.update": "Update account", "mco.activity.noactivity": "No activity for the past %s day(s)", "mco.activity.title": "Player activity", "mco.backup.button.download": "Download latest", "mco.backup.button.reset": "Reset world", "mco.backup.button.restore": "Rest<PERSON>", "mco.backup.button.upload": "Upload world", "mco.backup.changes.tooltip": "Changes", "mco.backup.entry": "Backup (%s)", "mco.backup.entry.description": "Description", "mco.backup.entry.enabledPack": "Enabled Pack", "mco.backup.entry.gameDifficulty": "Game Difficulty", "mco.backup.entry.gameMode": "Game Mode", "mco.backup.entry.gameServerVersion": "Game Server Version", "mco.backup.entry.name": "Name", "mco.backup.entry.seed": "Seed", "mco.backup.entry.templateName": "Template Name", "mco.backup.entry.undefined": "Undefined Change", "mco.backup.entry.uploaded": "Uploaded", "mco.backup.entry.worldType": "World Type", "mco.backup.generate.world": "Generate world", "mco.backup.info.title": "Changes from last backup", "mco.backup.nobackups": "This realm doesn't have any backups currently.", "mco.backup.restoring": "Restoring your realm", "mco.backup.unknown": "UNKNOWN", "mco.brokenworld.download": "Download", "mco.brokenworld.downloaded": "Downloaded", "mco.brokenworld.message.line1": "Please reset or select another world.", "mco.brokenworld.message.line2": "You can also choose to download the world to singleplayer.", "mco.brokenworld.minigame.title": "This minigame is no longer supported", "mco.brokenworld.nonowner.error": "Please wait for the realm owner to reset the world", "mco.brokenworld.nonowner.title": "World is out of date", "mco.brokenworld.play": "Play", "mco.brokenworld.reset": "Reset", "mco.brokenworld.title": "Your current world is no longer supported", "mco.client.incompatible.msg.line1": "Your client is not compatible with Realms.", "mco.client.incompatible.msg.line2": "Please use the most recent version of Minecraft.", "mco.client.incompatible.msg.line3": "Realms is not compatible with snapshot versions.", "mco.client.incompatible.title": "Client incompatible!", "mco.configure.current.minigame": "Current", "mco.configure.world.activityfeed.disabled": "Player feed temporarily disabled", "mco.configure.world.backup": "World backups", "mco.configure.world.buttons.activity": "Player activity", "mco.configure.world.buttons.close": "Close realm", "mco.configure.world.buttons.delete": "Delete", "mco.configure.world.buttons.done": "Done", "mco.configure.world.buttons.edit": "Settings", "mco.configure.world.buttons.invite": "Invite player", "mco.configure.world.buttons.moreoptions": "More options", "mco.configure.world.buttons.open": "Open realm", "mco.configure.world.buttons.options": "World options", "mco.configure.world.buttons.players": "Players", "mco.configure.world.buttons.resetworld": "Reset world", "mco.configure.world.buttons.settings": "Settings", "mco.configure.world.buttons.subscription": "Subscription", "mco.configure.world.buttons.switchminigame": "Switch minigame", "mco.configure.world.close.question.line1": "Your realm will become unavailable.", "mco.configure.world.close.question.line2": "Are you sure you want to continue?", "mco.configure.world.closing": "Closing the realm...", "mco.configure.world.commandBlocks": "Command blocks", "mco.configure.world.delete.button": "Delete realm", "mco.configure.world.delete.question.line1": "Your realm will be permanently deleted", "mco.configure.world.delete.question.line2": "Are you sure you want to continue?", "mco.configure.world.description": "Realm description", "mco.configure.world.edit.slot.name": "World name", "mco.configure.world.edit.subscreen.adventuremap": "Some settings are disabled since your current world is an adventure", "mco.configure.world.edit.subscreen.experience": "Some settings are disabled since your current world is an experience", "mco.configure.world.edit.subscreen.inspiration": "Some settings are disabled since your current world is an inspiration", "mco.configure.world.forceGameMode": "Force game mode", "mco.configure.world.invite.narration": "You have %s new invite(s)", "mco.configure.world.invite.profile.name": "Name", "mco.configure.world.invited": "Invited", "mco.configure.world.invited.number": "Invited (%s)", "mco.configure.world.invites.normal.tooltip": "Normal user", "mco.configure.world.invites.ops.tooltip": "Operator", "mco.configure.world.invites.remove.tooltip": "Remove", "mco.configure.world.leave.question.line1": "If you leave this realm you won't see it unless you are invited again", "mco.configure.world.leave.question.line2": "Are you sure you want to continue?", "mco.configure.world.location": "Location", "mco.configure.world.minigame": "Current: %s", "mco.configure.world.name": "Realm name", "mco.configure.world.opening": "Opening the realm...", "mco.configure.world.players.error": "A player with the provided name does not exist", "mco.configure.world.players.inviting": "Inviting player...", "mco.configure.world.players.title": "Players", "mco.configure.world.pvp": "PVP", "mco.configure.world.reset.question.line1": "Your world will be regenerated and your current world will be lost", "mco.configure.world.reset.question.line2": "Are you sure you want to continue?", "mco.configure.world.resourcepack.question.line1": "You need a custom resource pack to play on this realm", "mco.configure.world.resourcepack.question.line2": "Do you want to download it and play?", "mco.configure.world.restore.download.question.line1": "The world will be downloaded and added to your single player worlds.", "mco.configure.world.restore.download.question.line2": "Do you want to continue?", "mco.configure.world.restore.question.line1": "Your world will be restored to date '%s' (%s)", "mco.configure.world.restore.question.line2": "Are you sure you want to continue?", "mco.configure.world.settings.title": "Settings", "mco.configure.world.slot": "World %s", "mco.configure.world.slot.empty": "Empty", "mco.configure.world.slot.switch.question.line1": "Your realm will be switched to another world", "mco.configure.world.slot.switch.question.line2": "Are you sure you want to continue?", "mco.configure.world.slot.tooltip": "Switch to world", "mco.configure.world.slot.tooltip.active": "Join", "mco.configure.world.slot.tooltip.minigame": "Switch to minigame", "mco.configure.world.spawn_toggle.message": "Turning this option off will REMOVE ALL existing entities of that type", "mco.configure.world.spawn_toggle.message.npc": "Turning this option off will REMOVE ALL existing entities of that type, like Villagers", "mco.configure.world.spawn_toggle.title": "Warning!", "mco.configure.world.spawnAnimals": "Spawn animals", "mco.configure.world.spawnMonsters": "Spawn monsters", "mco.configure.world.spawnNPCs": "Spawn NPCs", "mco.configure.world.spawnProtection": "Spawn protection", "mco.configure.world.status": "Status", "mco.configure.world.subscription.remaining.months.days": "%1$s month(s), %2$s day(s)", "mco.configure.world.subscription.remaining.months": "%1$s month(s)", "mco.configure.world.subscription.remaining.days": "%1$s day(s)", "mco.configure.world.subscription.day": "day", "mco.configure.world.subscription.days": "days", "mco.configure.world.subscription.expired": "Expired", "mco.configure.world.subscription.extend": "Extend subscription", "mco.configure.world.subscription.less_than_a_day": "Less than a day", "mco.configure.world.subscription.month": "month", "mco.configure.world.subscription.months": "months", "mco.configure.world.subscription.recurring.daysleft": "Renewed automatically in", "mco.configure.world.subscription.recurring.info": "Changes made to your Realms subscription such as stacking time or turning off recurring billing will not be reflected until your next bill date.", "mco.configure.world.subscription.start": "Start date", "mco.configure.world.subscription.timeleft": "Time left", "mco.configure.world.subscription.title": "Your subscription", "mco.configure.world.subscription.unknown": "Unknown", "mco.configure.world.switch.slot": "Create world", "mco.configure.world.switch.slot.subtitle": "This world is empty, choose how to create your world", "mco.configure.world.title": "Configure realm:", "mco.configure.world.uninvite.question": "Are you sure that you want to uninvite", "mco.configure.world.uninvite.player": "Are you sure that you want to uninvite '%s'?", "mco.configure.worlds.title": "Worlds", "mco.connect.authorizing": "Logging in...", "mco.connect.connecting": "Connecting to the realm...", "mco.connect.failed": "Failed to connect to the realm", "mco.connect.success": "Done", "mco.create.world": "Create", "mco.create.world.error": "You must enter a name!", "mco.create.world.reset.title": "Creating world...", "mco.create.world.skip": "<PERSON><PERSON>", "mco.create.world.subtitle": "Optionally, select what world to put on your new realm", "mco.create.world.wait": "Creating the realm...", "mco.download.cancelled": "Download cancelled", "mco.download.confirmation.line1": "The world you are going to download is larger than %s", "mco.download.confirmation.line2": "You won't be able to upload this world to your realm again", "mco.download.done": "Download done", "mco.download.downloading": "Downloading", "mco.download.extracting": "Extracting", "mco.download.failed": "Download failed", "mco.download.percent": "%s %%", "mco.download.preparing": "Preparing download", "mco.download.resourcePack.fail": "Failed to download resource pack!", "mco.download.speed": "(%s/s)", "mco.download.title": "Downloading latest world", "mco.error.invalid.session.message": "Please try restarting Minecraft", "mco.error.invalid.session.title": "Invalid session", "mco.errorMessage.6001": "Client outdated", "mco.errorMessage.6002": "Terms of service not accepted", "mco.errorMessage.6003": "Download limit reached", "mco.errorMessage.6004": "Upload limit reached", "mco.errorMessage.6005": "World locked", "mco.errorMessage.6006": "World is out of date", "mco.errorMessage.6007": "User in too many Realms", "mco.errorMessage.6008": "Invalid Realm name", "mco.errorMessage.6009": "Invalid Realm description", "mco.errorMessage.connectionFailure": "An error occurred, please try again later.", "mco.errorMessage.generic": "An error occurred: ", "mco.errorMessage.realmsService": "An error occurred (%s):", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.serviceBusy": "Realms is busy at the moment.\nPlease try connecting to your Realm again in a couple of minutes.", "mco.gui.button": "<PERSON><PERSON>", "mco.gui.ok": "Ok", "mco.info": "Info!", "mco.invites.button.accept": "Accept", "mco.invites.button.reject": "Reject", "mco.invites.nopending": "No pending invites!", "mco.invites.pending": "New invite(s)!", "mco.invites.title": "Pending Invites", "mco.minigame.world.changeButton": "Select another minigame", "mco.minigame.world.info.line1": "This will temporarily replace your world with a minigame!", "mco.minigame.world.info.line2": "You can later return to your original world without losing anything.", "mco.minigame.world.noSelection": "Please make a selection", "mco.minigame.world.restore": "Ending minigame...", "mco.minigame.world.restore.question.line1": "The minigame will end and your realm will be restored.", "mco.minigame.world.restore.question.line2": "Are you sure you want to continue?", "mco.minigame.world.selected": "Selected minigame:", "mco.minigame.world.slot.screen.title": "Switching world...", "mco.minigame.world.startButton": "Switch", "mco.minigame.world.starting.screen.title": "Starting minigame...", "mco.minigame.world.stopButton": "End minigame", "mco.minigame.world.switch.new": "Select another minigame?", "mco.minigame.world.switch.title": "Switch minigame", "mco.minigame.world.title": "Switch realm to minigame", "mco.news": "Realms news", "mco.notification.dismiss": "<PERSON><PERSON><PERSON>", "mco.notification.visitUrl.buttonText.default": "Open link", "mco.notification.visitUrl.message.default": "Please visit the link below", "mco.question": "Question", "mco.reset.world.adventure": "Adventures", "mco.reset.world.experience": "Experiences", "mco.reset.world.generate": "New world", "mco.reset.world.inspiration": "Inspiration", "mco.reset.world.resetting.screen.title": "Resetting world...", "mco.reset.world.seed": "Seed (Optional)", "mco.reset.world.template": "World templates", "mco.reset.world.title": "Reset world", "mco.reset.world.upload": "Upload world", "mco.reset.world.warning": "This will replace the current world of your realm", "mco.selectServer.buy": "Buy a realm!", "mco.selectServer.close": "Close", "mco.selectServer.closed": "Closed realm", "mco.selectServer.closeserver": "Close realm", "mco.selectServer.configure": "Configure", "mco.selectServer.configureRealm": "Configure realm", "mco.selectServer.create": "Create realm", "mco.selectServer.expired": "Expired realm", "mco.selectServer.expiredList": "Your subscription has expired", "mco.selectServer.expiredRenew": "<PERSON>w", "mco.selectServer.expiredSubscribe": "Subscribe", "mco.selectServer.expiredTrial": "Your trial has ended", "mco.selectServer.expires.day": "Expires in a day", "mco.selectServer.expires.days": "Expires in %s days", "mco.selectServer.expires.soon": "Expires soon", "mco.selectServer.leave": "Leave realm", "mco.selectServer.mapOnlySupportedForVersion": "This map is unsupported in %s", "mco.selectServer.minigame": "Minigame:", "mco.selectServer.minigameNotSupportedInVersion": "Can't play this minigame in %s", "mco.selectServer.note": "Note:", "mco.selectServer.open": "Open realm", "mco.selectServer.openserver": "Open realm", "mco.selectServer.play": "Play", "mco.selectServer.popup": "Realms is a safe, simple way to enjoy an online Minecraft world with up to ten friends at a time.   It supports loads of minigames and plenty of custom worlds! Only the owner of the realm needs to pay.", "mco.selectServer.purchase": "Add Realm", "mco.selectServer.trial": "Get a trial!", "mco.selectServer.uninitialized": "Click to start your new realm!", "mco.template.button.publisher": "Publisher", "mco.template.button.select": "Select", "mco.template.button.trailer": "Trailer", "mco.template.default.name": "World template", "mco.template.info.tooltip": "Publisher website", "mco.template.name": "Template", "mco.template.select.failure": "We couldn't retrieve the list of content for this category.\nPlease check your internet connection, or try again later.", "mco.template.select.narrate.authors": "Authors: <AUTHORS>", "mco.template.select.narrate.version": "version %s", "mco.template.select.none": "Oops, it looks like this content category is currently empty.\nPlease check back later for new content, or if you're a creator,\n%s.", "mco.template.select.none.linkTitle": "consider submitting something yourself", "mco.template.title": "World templates", "mco.template.title.minigame": "Minigames", "mco.template.trailer.tooltip": "Map trailer", "mco.terms.buttons.agree": "Agree", "mco.terms.buttons.disagree": "Don't agree", "mco.terms.sentence.1": "I agree to the Minecraft Realms", "mco.terms.sentence.2": "Terms of Service", "mco.terms.title": "Realms Terms of Service", "mco.trial.message.line1": "Want to get your own realm?", "mco.trial.message.line2": "Click here for more info!", "mco.upload.button.name": "Upload", "mco.upload.cancelled": "Upload cancelled", "mco.upload.close.failure": "Could not close your realm, please try again later", "mco.upload.done": "Upload done", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.failed": "Upload failed! (%s)", "mco.upload.hardcore": "Hardcore worlds can't be uploaded!", "mco.upload.preparing": "Preparing your world", "mco.upload.select.world.none": "No singleplayer worlds found!", "mco.upload.select.world.subtitle": "Please select a singleplayer world to upload", "mco.upload.select.world.title": "Upload world", "mco.upload.size.failure.line1": "'%s' is too big!", "mco.upload.size.failure.line2": "It is %s. The maximum allowed size is %s.", "mco.upload.uploading": "Uploading '%s'", "mco.upload.verifying": "Verifying your world", "mco.time.now": "right now", "mco.time.secondsAgo": "%1$s second(s) ago", "mco.time.minutesAgo": "%1$s minute(s) ago", "mco.time.hoursAgo": "%1$s hour(s) ago", "mco.time.daysAgo": "%1$s day(s) ago", "mco.warning": "Warning!", "mco.worldSlot.minigame": "Minigame", "menu.convertingLevel": "Converting world", "menu.disconnect": "Disconnect", "menu.game": "Game Menu", "menu.generatingLevel": "Generating world", "menu.generatingTerrain": "Building terrain", "menu.loadingForcedChunks": "Loading forced chunks for dimension %s", "menu.loadingLevel": "Loading world", "menu.modded": " (Modded)", "menu.multiplayer": "Multiplayer", "menu.online": "Minecraft Realms", "menu.options": "Options...", "menu.paused": "Game Paused", "menu.playdemo": "Play Demo World", "menu.playerReporting": "Player Reporting", "menu.preparingSpawn": "Preparing spawn area: %s%%", "menu.quit": "Quit Game", "menu.reportBugs": "Report Bugs", "menu.resetdemo": "Reset Demo World", "menu.respawning": "Respawning", "menu.returnToGame": "Back to Game", "menu.returnToMenu": "Save and Quit to Title", "menu.savingChunks": "Saving chunks", "menu.savingLevel": "Saving world", "menu.sendFeedback": "<PERSON>", "menu.shareToLan": "Open to LAN", "menu.singleplayer": "Singleplayer", "menu.working": "Working...", "merchant.current_level": "Trader's current level", "merchant.deprecated": "Villagers restock up to two times per day.", "merchant.level.1": "Novice", "merchant.level.2": "Apprentice", "merchant.level.3": "Journeyman", "merchant.level.4": "Expert", "merchant.level.5": "Master", "merchant.next_level": "Trader's next level", "merchant.trades": "Trades", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Press %1$s to Dismount", "multiplayer.applyingPack": "Applying resource pack", "multiplayer.disconnect.authservers_down": "Authentication servers are down. Please try again later, sorry!", "multiplayer.disconnect.banned": "You are banned from this server", "multiplayer.disconnect.banned_ip.expiration": "\nYour ban will be removed on %s", "multiplayer.disconnect.banned_ip.reason": "Your IP address is banned from this server.\nReason: %s", "multiplayer.disconnect.banned.expiration": "\nYour ban will be removed on %s", "multiplayer.disconnect.banned.reason": "You are banned from this server.\nReason: %s", "multiplayer.disconnect.chat_validation_failed": "Chat message validation failure", "multiplayer.disconnect.duplicate_login": "You logged in from another location", "multiplayer.disconnect.expired_public_key": "Expired profile public key. Check that your system time is synchronized, and try restarting your game.", "multiplayer.disconnect.flying": "Flying is not enabled on this server", "multiplayer.disconnect.generic": "Disconnected", "multiplayer.disconnect.idling": "You have been idle for too long!", "multiplayer.disconnect.illegal_characters": "Illegal characters in chat", "multiplayer.disconnect.incompatible": "Incompatible client! Please use %s", "multiplayer.disconnect.invalid_entity_attacked": "Attempting to attack an invalid entity", "multiplayer.disconnect.invalid_packet": "Server sent an invalid packet", "multiplayer.disconnect.invalid_player_data": "Invalid player data", "multiplayer.disconnect.invalid_player_movement": "Invalid move player packet received", "multiplayer.disconnect.invalid_public_key_signature": "Invalid signature for profile public key.\nTry restarting your game.", "multiplayer.disconnect.invalid_public_key_signature.new": "Invalid signature for profile public key.\nTry restarting your game.", "multiplayer.disconnect.invalid_vehicle_movement": "Invalid move vehicle packet received", "multiplayer.disconnect.ip_banned": "You have been IP banned from this server", "multiplayer.disconnect.kicked": "Kicked by an operator", "multiplayer.disconnect.missing_tags": "Incomplete set of tags received from server.\nPlease contact server operator.", "multiplayer.disconnect.name_taken": "That name is already taken", "multiplayer.disconnect.not_whitelisted": "You are not white-listed on this server!", "multiplayer.disconnect.out_of_order_chat": "Out-of-order chat packet received. Did your system time change?", "multiplayer.disconnect.outdated_client": "Incompatible client! Please use %s", "multiplayer.disconnect.outdated_server": "Incompatible client! Please use %s", "multiplayer.disconnect.server_full": "The server is full!", "multiplayer.disconnect.server_shutdown": "Server closed", "multiplayer.disconnect.slow_login": "Took too long to log in", "multiplayer.disconnect.too_many_pending_chats": "Too many unacknowledged chat messages", "multiplayer.disconnect.unexpected_query_response": "Unexpected custom data from client", "multiplayer.disconnect.unsigned_chat": "Received chat packet with missing or invalid signature.", "multiplayer.disconnect.unverified_username": "Failed to verify username!", "multiplayer.downloadingStats": "Retrieving statistics...", "multiplayer.downloadingTerrain": "Loading terrain...", "multiplayer.lan.server_found": "New server found: %s", "multiplayer.message_not_delivered": "Can't deliver chat message, check server logs: %s", "multiplayer.player.joined": "%s joined the game", "multiplayer.player.joined.renamed": "%s (formerly known as %s) joined the game", "multiplayer.player.left": "%s left the game", "multiplayer.player.list.narration": "Online players: %s", "multiplayer.requiredTexturePrompt.disconnect": "Server requires a custom resource pack", "multiplayer.requiredTexturePrompt.line1": "This server requires the use of a custom resource pack.", "multiplayer.requiredTexturePrompt.line2": "Rejecting this custom resource pack will disconnect you from this server.", "multiplayer.socialInteractions.not_available": "Social Interactions are only available in Multiplayer worlds", "multiplayer.status.and_more": "... and %s more ...", "multiplayer.status.cancelled": "Cancelled", "multiplayer.status.cannot_connect": "Can't connect to server", "multiplayer.status.cannot_resolve": "Can't resolve hostname", "multiplayer.status.finished": "Finished", "multiplayer.status.incompatible": "Incompatible version!", "multiplayer.status.motd.narration": "Message of the day: %s", "multiplayer.status.no_connection": "(no connection)", "multiplayer.status.old": "Old", "multiplayer.status.online": "Online", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping %s milliseconds", "multiplayer.status.pinging": "Pinging...", "multiplayer.status.player_count.narration": "%s out of %s players online", "multiplayer.status.quitting": "Quitting", "multiplayer.status.request_handled": "Status request has been handled", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Received unrequested status", "multiplayer.status.version.narration": "Server version: %s", "multiplayer.stopSleeping": "Leave Bed", "multiplayer.texturePrompt.failure.line1": "Server resource pack couldn't be applied", "multiplayer.texturePrompt.failure.line2": "Any functionality that requires custom resources might not work as expected", "multiplayer.texturePrompt.line1": "This server recommends the use of a custom resource pack.", "multiplayer.texturePrompt.line2": "Would you like to download and install it automagically?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nMessage from server:\n%s", "multiplayer.title": "Play Multiplayer", "multiplayer.unsecureserver.toast": "Messages sent on this server may be modified and might not reflect the original message", "multiplayer.unsecureserver.toast.title": "Chat messages can't be verified", "multiplayerWarning.check": "Do not show this screen again", "multiplayerWarning.header": "Caution: Third-Party Online Play", "multiplayerWarning.message": "Caution: Online play is offered by third-party servers that are not owned, operated, or supervised by Mojang Studios or Microsoft. During online play, you may be exposed to unmoderated chat messages or other types of user-generated content that may not be suitable for everyone.", "narration.button": "Button: %s", "narration.button.usage.focused": "Press Enter to activate", "narration.button.usage.hovered": "Left click to activate", "narration.checkbox": "Checkbox: %s", "narration.checkbox.usage.focused": "Press Enter to toggle", "narration.checkbox.usage.hovered": "Left click to toggle", "narration.component_list.usage": "Press Tab to navigate to next element", "narration.cycle_button.usage.focused": "Press Enter to switch to %s", "narration.cycle_button.usage.hovered": "Left click to switch to %s", "narration.edit_box": "Edit box: %s", "narration.recipe": "Recipe for %s", "narration.recipe.usage": "Left click to select", "narration.recipe.usage.more": "Right click to show more recipes", "narration.selection.usage": "Press up and down buttons to move to another entry", "narration.slider.usage.focused": "Press left or right keyboard buttons to change value", "narration.slider.usage.hovered": "Drag slider to change value", "narration.suggestion": "Selected suggestion %d out of %d: %s", "narration.suggestion.tooltip": "Selected suggestion %d out of %d: %s (%s)", "narration.tab_navigation.usage": "Press Ctrl and Tab to switch between tabs", "narrator.button.accessibility": "Accessibility", "narrator.button.difficulty_lock": "Difficulty lock", "narrator.button.difficulty_lock.locked": "Locked", "narrator.button.difficulty_lock.unlocked": "Unlocked", "narrator.button.language": "Language", "narrator.controls.bound": "%s is bound to %s", "narrator.controls.reset": "Reset %s button", "narrator.controls.unbound": "%s is not bound", "narrator.joining": "Joining", "narrator.loading": "Loading: %s", "narrator.loading.done": "Done", "narrator.position.list": "Selected list row %s out of %s", "narrator.position.object_list": "Selected row element %s out of %s", "narrator.position.screen": "Screen element %s out of %s", "narrator.position.tab": "Selected tab %s out of %s", "narrator.ready_to_play": "Ready to play", "narrator.screen.title": "Title Screen", "narrator.screen.usage": "Use mouse cursor or Tab button to select element", "narrator.select": "Selected: %s", "narrator.select.world": "Selected %s, last played: %s, %s, %s, version: %s", "narrator.select.world_info": "Selected %s, last played: %s, %s", "narrator.toast.disabled": "Narrator Disabled", "narrator.toast.enabled": "Narrator Enabled", "optimizeWorld.confirm.description": "This will attempt to optimize your world by making sure all data is stored in the most recent game format. This can take a very long time, depending on your world. Once done, your world may play faster but will no longer be compatible with older versions of the game. Are you sure you wish to proceed?", "optimizeWorld.confirm.title": "Optimize World", "optimizeWorld.info.converted": "Upgraded chunks: %s", "optimizeWorld.info.skipped": "Skipped chunks: %s", "optimizeWorld.info.total": "Total chunks: %s", "optimizeWorld.stage.counting": "Counting chunks...", "optimizeWorld.stage.failed": "Failed! :(", "optimizeWorld.stage.finished": "Finishing up...", "optimizeWorld.stage.upgrading": "Upgrading all chunks...", "optimizeWorld.title": "Optimizing World '%s'", "options.accessibility.high_contrast": "High Contrast", "options.accessibility.high_contrast.error.tooltip": "High Contrast resource pack is not available", "options.accessibility.high_contrast.tooltip": "Enhances the contrast of UI elements", "options.accessibility.link": "Accessibility Guide", "options.accessibility.panorama_speed": "Panorama Scroll Speed", "options.accessibility.text_background": "Text Background", "options.accessibility.text_background_opacity": "Text Background Opacity", "options.accessibility.text_background.chat": "Cha<PERSON>", "options.accessibility.text_background.everywhere": "Everywhere", "options.accessibility.title": "Accessibility Settings...", "options.allowServerListing": "Allow Server Listings", "options.allowServerListing.tooltip": "Servers may list online players as part of their public status.\nWith this option off your name will not show up in such lists.", "options.ao": "Smooth Lighting", "options.ao.max": "Maximum", "options.ao.min": "Minimum", "options.ao.off": "OFF", "options.attack.crosshair": "<PERSON><PERSON><PERSON>", "options.attack.hotbar": "Hotbar", "options.attackIndicator": "Attack Indicator", "options.audioDevice": "<PERSON><PERSON>", "options.audioDevice.default": "System Default", "options.autoJump": "Auto-Jump", "options.autosaveIndicator": "Autosave Indicator", "options.autoSuggestCommands": "Command Suggestions", "options.biomeBlendRadius": "Biome Blend", "options.biomeBlendRadius.1": "OFF (Fastest)", "options.biomeBlendRadius.3": "3x3 (Fast)", "options.biomeBlendRadius.5": "5x5 (Normal)", "options.biomeBlendRadius.7": "7x7 (High)", "options.biomeBlendRadius.9": "9x9 (Very High)", "options.biomeBlendRadius.11": "11x11 (Extreme)", "options.biomeBlendRadius.13": "13x13 (Showoff)", "options.biomeBlendRadius.15": "15x15 (Maximum)", "options.chat.color": "Colors", "options.chat.delay": "Chat <PERSON>ay: %s seconds", "options.chat.delay_none": "<PERSON><PERSON>: None", "options.chat.height.focused": "Focused Height", "options.chat.height.unfocused": "Unfocused Height", "options.chat.line_spacing": "Line Spacing", "options.chat.links": "Web Links", "options.chat.links.prompt": "Prompt on Links", "options.chat.opacity": "Chat Text Opacity", "options.chat.scale": "Chat Text Size", "options.chat.title": "Cha<PERSON>s...", "options.chat.visibility": "Cha<PERSON>", "options.chat.visibility.full": "Shown", "options.chat.visibility.hidden": "Hidden", "options.chat.visibility.system": "Commands Only", "options.chat.width": "<PERSON><PERSON><PERSON>", "options.chunks": "%s chunks", "options.clouds.fancy": "Fancy", "options.clouds.fast": "Fast", "options.controls": "Controls...", "options.credits_and_attribution": "Credits & Attribution...", "options.customizeTitle": "Customize World Settings", "options.damageTiltStrength": "Damage Tilt", "options.damageTiltStrength.tooltip": "The amount of camera shake caused by being hurt.", "options.darkMojangStudiosBackgroundColor": "Monochrome Logo", "options.darkMojangStudiosBackgroundColor.tooltip": "Changes the Mojang Studios loading screen background color to black.", "options.darknessEffectScale": "Darkness Pulsing", "options.darknessEffectScale.tooltip": "Controls how much the Darkness effect pulses when a Warden or Sculk Shrieker gives it to you.", "options.difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.difficulty.easy": "Easy", "options.difficulty.easy.info": "Hostile mobs spawn but deal less damage. Hunger bar depletes and drains health down to 5 hearts.", "options.difficulty.hard": "Hard", "options.difficulty.hard.info": "Hostile mobs spawn and deal more damage. Hunger bar depletes and drains all health.", "options.difficulty.hardcore": "Hardcore", "options.difficulty.normal": "Normal", "options.difficulty.normal.info": "Hostile mobs spawn and deal standard damage. Hunger bar depletes and drains health down to half a heart.", "options.difficulty.online": "Server Difficulty", "options.difficulty.peaceful": "Peaceful", "options.difficulty.peaceful.info": "No hostile mobs and only some neutral mobs spawn. Hunger bar doesn't deplete and health replenishes over time.", "options.directionalAudio": "Directional Audio", "options.directionalAudio.off.tooltip": "Classic Stereo sound", "options.directionalAudio.on.tooltip": "Uses HRTF-based directional audio to improve the simulation of 3D sound. Requires HRTF compatible audio hardware, and is best experienced with headphones.", "options.discrete_mouse_scroll": "Discret<PERSON>ing", "options.entityDistanceScaling": "Entity Distance", "options.entityShadows": "Entity Shadows", "options.forceUnicodeFont": "Force Unicode Font", "options.fov": "FOV", "options.fov.max": "Quake Pro", "options.fov.min": "Normal", "options.fovEffectScale": "FOV Effects", "options.fovEffectScale.tooltip": "Controls how much the field of view can change with gameplay effects.", "options.framerate": "%s fps", "options.framerateLimit": "<PERSON>", "options.framerateLimit.max": "Unlimited", "options.fullscreen": "Fullscreen", "options.fullscreen.current": "Current", "options.fullscreen.resolution": "Fullscreen Resolution", "options.fullscreen.unavailable": "Setting unavailable", "options.gamma": "Brightness", "options.gamma.default": "<PERSON><PERSON><PERSON>", "options.gamma.max": "<PERSON>", "options.gamma.min": "<PERSON>", "options.generic_value": "%s: %s", "options.glintSpeed": "<PERSON><PERSON>", "options.glintSpeed.tooltip": "Controls how fast the visual glint shimmers across enchanted items.", "options.glintStrength": "<PERSON><PERSON>", "options.glintStrength.tooltip": "Controls how transparent the visual glint is on enchanted items.", "options.graphics": "Graphics", "options.graphics.fabulous": "Fabulous!", "options.graphics.fabulous.tooltip": "%s graphics uses screen shaders for drawing weather, clouds, and particles behind translucent blocks and water.\nThis may severely impact performance for portable devices and 4K displays.", "options.graphics.fancy": "Fancy", "options.graphics.fancy.tooltip": "Fancy graphics balances performance and quality for the majority of machines.\nWeather, clouds, and particles may not appear behind translucent blocks or water.", "options.graphics.fast": "Fast", "options.graphics.fast.tooltip": "Fast graphics reduces the amount of visible rain and snow.\nTransparency effects are disabled for various blocks such as leaves.", "options.graphics.warning.accept": "Continue Without Support", "options.graphics.warning.cancel": "Take Me Back", "options.graphics.warning.message": "Your graphics device is detected as unsupported for the %s graphics option.\n\nYou may ignore this and continue, however support will not be provided for your device if you choose to use %s graphics.", "options.graphics.warning.renderer": "Renderer detected: [%s]", "options.graphics.warning.title": "Graphics Device Unsupported", "options.graphics.warning.vendor": "Vendor detected: [%s]", "options.graphics.warning.version": "OpenGL Version detected: [%s]", "options.guiScale": "GUI Scale", "options.guiScale.auto": "Auto", "options.hidden": "Hidden", "options.hideLightningFlashes": "Hide Lightning Flashes", "options.hideLightningFlashes.tooltip": "Prevents Lightning Bolts from making the sky flash. The bolts themselves will still be visible.", "options.hideMatchedNames": "Hide Matched Names", "options.hideMatchedNames.tooltip": "3rd-party Servers may send chat messages in non-standard formats.\nWith this option on, hidden players will be matched based on chat sender names.", "options.invertMouse": "Invert Mouse", "options.key.hold": "Hold", "options.key.toggle": "Toggle", "options.language": "Language...", "options.languageWarning": "Language translations may not be 100%% accurate", "options.mainHand": "Main Hand", "options.mainHand.left": "Left", "options.mainHand.right": "Right", "options.mipmapLevels": "Mipmap Levels", "options.modelPart.cape": "Cape", "options.modelPart.hat": "Hat", "options.modelPart.jacket": "Jacket", "options.modelPart.left_pants_leg": "Left Pants Leg", "options.modelPart.left_sleeve": "Left Sleeve", "options.modelPart.right_pants_leg": "Right Pants Leg", "options.modelPart.right_sleeve": "Right Sleeve", "options.mouse_settings": "Mouse Settings...", "options.mouse_settings.title": "Mouse Settings", "options.mouseWheelSensitivity": "Scroll Sensitivity", "options.multiplayer.title": "Multiplayer Settings...", "options.multiplier": "%sx", "options.narrator": "Narrator", "options.narrator.all": "Narrates All", "options.narrator.chat": "<PERSON>rra<PERSON>", "options.narrator.notavailable": "Not Available", "options.narrator.off": "OFF", "options.narrator.system": "Narrates System", "options.notifications.display_time": "Notification Time", "options.notifications.display_time.tooltip": "Affects the length of time that all notifications stay visible on the screen.", "options.off": "OFF", "options.off.composed": "%s: OFF", "options.on": "ON", "options.on.composed": "%s: ON", "options.online": "Online...", "options.online.title": "Online Options", "options.onlyShowSecureChat": "Only Show Secure Chat", "options.onlyShowSecureChat.tooltip": "Only display messages from other players that can be verified to have been sent by that player, and have not been modified.", "options.operatorItemsTab": "Operator Items Tab", "options.particles": "Particles", "options.particles.all": "All", "options.particles.decreased": "Decreased", "options.particles.minimal": "Minimal", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "Chunk Builder", "options.prioritizeChunkUpdates.byPlayer": "Semi Blocking", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Some actions within a chunk will recompile the chunk immediately. This includes block placing & destroying.", "options.prioritizeChunkUpdates.nearby": "Fully Blocking", "options.prioritizeChunkUpdates.nearby.tooltip": "Nearby chunks are always compiled immediately. This may impact game performance when blocks are placed or destroyed.", "options.prioritizeChunkUpdates.none": "Threaded", "options.prioritizeChunkUpdates.none.tooltip": "Nearby chunks are compiled in parallel threads. This may result in brief visual holes when blocks are destroyed.", "options.rawMouseInput": "Raw Input", "options.realmsNotifications": "Realms News & Invites", "options.reducedDebugInfo": "Reduced Debug Info", "options.renderClouds": "Clouds", "options.renderDistance": "Render Distance", "options.resourcepack": "Resource Packs...", "options.screenEffectScale": "Distortion Effects", "options.screenEffectScale.tooltip": "Strength of nausea and Nether portal screen distortion effects.\nAt lower values, the nausea effect is replaced with a green overlay.", "options.sensitivity": "Sensitivity", "options.sensitivity.max": "HYPERSPEED!!!", "options.sensitivity.min": "*yawn*", "options.showSubtitles": "Show Subtitles", "options.simulationDistance": "Simulation Distance", "options.skinCustomisation": "Skin Customization...", "options.skinCustomisation.title": "Skin Customization", "options.sounds": "Music & Sounds...", "options.sounds.title": "Music & Sound Options", "options.telemetry": "Telemetry Data...", "options.telemetry.button": "Data Collection", "options.telemetry.button.tooltip": "\"%s\" includes only the required data.\n\"%s\" includes optional, as well as the required data.", "options.telemetry.state.all": "All", "options.telemetry.state.minimal": "Minimal", "options.telemetry.state.none": "None", "options.title": "Options", "options.touchscreen": "Touchscreen Mode", "options.video": "Video Settings...", "options.videoTitle": "Video Settings", "options.viewBobbing": "View Bobbing", "options.visible": "Shown", "options.vsync": "VSync", "outOfMemory.message": "Minecraft has run out of memory.\n\nThis could be caused by a bug in the game or by the Java Virtual Machine not being allocated enough memory.\n\nTo prevent level corruption, the current game has quit. We've tried to free up enough memory to let you go back to the main menu and back to playing, but this may not have worked.\n\nPlease restart the game if you see this message again.", "outOfMemory.title": "Out of memory!", "pack.available.title": "Available", "pack.copyFailure": "Failed to copy packs", "pack.dropConfirm": "Do you want to add the following packs to Minecraft?", "pack.dropInfo": "Drag and drop files into this window to add packs", "pack.folderInfo": "(Place pack files here)", "pack.incompatible": "Incompatible", "pack.incompatible.confirm.new": "This pack was made for a newer version of Minecraft and may not work correctly.", "pack.incompatible.confirm.old": "This pack was made for an older version of Minecraft and may no longer work correctly.", "pack.incompatible.confirm.title": "Are you sure you want to load this pack?", "pack.incompatible.new": "(Made for a newer version of Minecraft)", "pack.incompatible.old": "(Made for an older version of Minecraft)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "Open Pack Folder", "pack.selected.title": "Selected", "pack.source.builtin": "built-in", "pack.source.feature": "feature", "pack.source.local": "local", "pack.source.server": "server", "pack.source.world": "world", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanian", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Target Successfully Bombed", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Skull On Fire", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Earth", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Fighters", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Fire", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Match", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Pigscene", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pointer", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "The Pool", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Seaside", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Mortal Coil", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Skull and Roses", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "The Stage Is Set", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "The void", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Wasteland", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Water", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Wind", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Random variant", "parsing.bool.expected": "Expected boolean", "parsing.bool.invalid": "Invalid boolean, expected 'true' or 'false' but found '%s'", "parsing.double.expected": "Expected double", "parsing.double.invalid": "Invalid double '%s'", "parsing.expected": "Expected '%s'", "parsing.float.expected": "Expected float", "parsing.float.invalid": "Invalid float '%s'", "parsing.int.expected": "Expected integer", "parsing.int.invalid": "Invalid integer '%s'", "parsing.long.expected": "Expected long", "parsing.long.invalid": "Invalid long '%s'", "parsing.quote.escape": "Invalid escape sequence '\\%s' in quoted string", "parsing.quote.expected.end": "Unclosed quoted string", "parsing.quote.expected.start": "Expected quote to start a string", "particle.notFound": "Unknown particle: %s", "permissions.requires.entity": "An entity is required to run this command here", "permissions.requires.player": "A player is required to run this command here", "potion.potency.0": "", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "When Applied:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Unknown predicate: %s", "quickplay.error.invalid_identifier": "Could not find world with the provided identifier", "quickplay.error.realm_connect": "Could not connect to Realm", "quickplay.error.realm_permission": "Lacking permission to connect to this Realm", "quickplay.error.title": "Failed to Quick Play", "realms.missing.module.error.text": "Realms could not be opened right now, please try again later", "realms.missing.snapshot.error.text": "Realms is currently not supported in snapshots", "recipe.notFound": "Unknown recipe: %s", "recipe.toast.description": "Check your recipe book", "recipe.toast.title": "New Recipes Unlocked!", "record.nowPlaying": "Now Playing: %s", "resourcePack.broken_assets": "BROKEN ASSETS DETECTED", "resourcepack.downloading": "Downloading Resource Pack", "resourcePack.high_contrast.name": "High Contrast", "resourcePack.load_fail": "Resource reload failed", "resourcePack.programmer_art.name": "Programmer Art", "resourcepack.progress": "Downloading file (%s MB)...", "resourcepack.requesting": "Making Request...", "resourcePack.server.name": "World Specific Resources", "resourcePack.title": "Select Resource Packs", "resourcePack.vanilla.description": "The default look and feel of Minecraft", "resourcePack.vanilla.name": "<PERSON><PERSON><PERSON>", "screenshot.failure": "Couldn't save screenshot: %s", "screenshot.success": "Saved screenshot as %s", "selectServer.add": "Add Server", "selectServer.defaultName": "Minecraft Server", "selectServer.delete": "Delete", "selectServer.deleteButton": "Delete", "selectServer.deleteQuestion": "Are you sure you want to remove this server?", "selectServer.deleteWarning": "'%s' will be lost forever! (A long time!)", "selectServer.direct": "Direct Connection", "selectServer.edit": "Edit", "selectServer.hiddenAddress": "(Hidden)", "selectServer.refresh": "Refresh", "selectServer.select": "Join <PERSON>", "selectServer.title": "Select Server", "selectWorld.access_failure": "Failed to access world", "selectWorld.allowCommands": "Allow Cheats", "selectWorld.allowCommands.info": "Commands like /gamemode, /experience", "selectWorld.backupEraseCache": "<PERSON>se Cached Data", "selectWorld.backupJoinConfirmButton": "Create Backup and Load", "selectWorld.backupJoinSkipButton": "I know what I'm doing!", "selectWorld.backupQuestion.customized": "Customized worlds are no longer supported", "selectWorld.backupQuestion.downgrade": "Downgrading a world is not supported", "selectWorld.backupQuestion.experimental": "Worlds using Experimental Settings are not supported", "selectWorld.backupQuestion.snapshot": "Do you really want to load this world?", "selectWorld.backupWarning.customized": "Unfortunately, we do not support customized worlds in this version of Minecraft. We can still load this world and keep everything the way it was, but any newly generated terrain will no longer be customized. We're sorry for the inconvenience!", "selectWorld.backupWarning.downgrade": "This world was last played in version %s; you are on version %s. Downgrading a world could cause corruption - we cannot guarantee that it will load or work. If you still want to continue, please make a backup!", "selectWorld.backupWarning.experimental": "This world uses experimental settings that could stop working at any time. We cannot guarantee it will load or work. Here be dragons!", "selectWorld.backupWarning.snapshot": "This world was last played in version %s; you are on version %s. Please make a backup in case you experience world corruptions!", "selectWorld.bonusItems": "Bonus Chest", "selectWorld.cheats": "Cheats", "selectWorld.conversion": "Must be converted!", "selectWorld.conversion.tooltip": "This world must be opened in an older version (like 1.6.4) to be safely converted", "selectWorld.create": "Create New World", "selectWorld.createDemo": "Play New Demo World", "selectWorld.customizeType": "Customize", "selectWorld.data_read": "Reading world data...", "selectWorld.dataPacks": "Data Packs", "selectWorld.delete": "Delete", "selectWorld.delete_failure": "Failed to delete world", "selectWorld.deleteButton": "Delete", "selectWorld.deleteQuestion": "Are you sure you want to delete this world?", "selectWorld.deleteWarning": "'%s' will be lost forever! (A long time!)", "selectWorld.edit": "Edit", "selectWorld.edit.backup": "Make Backup", "selectWorld.edit.backupCreated": "Backed up: %s", "selectWorld.edit.backupFailed": "Backup failed", "selectWorld.edit.backupFolder": "Open Backups Folder", "selectWorld.edit.backupSize": "size: %s MB", "selectWorld.edit.export_worldgen_settings": "Export World Generation Settings", "selectWorld.edit.export_worldgen_settings.failure": "Export failed", "selectWorld.edit.export_worldgen_settings.success": "Exported", "selectWorld.edit.openFolder": "Open World Folder", "selectWorld.edit.optimize": "Optimize World", "selectWorld.edit.resetIcon": "Reset Icon", "selectWorld.edit.save": "Save", "selectWorld.edit.title": "Edit World", "selectWorld.enterName": "World Name", "selectWorld.enterSeed": "Seed for the world generator", "selectWorld.experimental": "Experimental", "selectWorld.experimental.details": "Details", "selectWorld.experimental.details.entry": "Required experimental features: %s", "selectWorld.experimental.details.title": "Experimental feature requirements", "selectWorld.experimental.message": "Be careful!\nThis configuration requires features that are still under development. Your world might crash, break, or not work with future updates.", "selectWorld.experimental.title": "Experimental Features Warning", "selectWorld.experiments": "Experiments", "selectWorld.experiments.info": "Experiments are potential new features. Be careful as things might break. Experiments can't be turned off after world creation.", "selectWorld.futureworld.error.text": "Something went wrong while trying to load a world from a future version. This was a risky operation to begin with; sorry it didn't work.", "selectWorld.futureworld.error.title": "An error occurred!", "selectWorld.gameMode": "Game Mode", "selectWorld.gameMode.adventure": "Adventure", "selectWorld.gameMode.adventure.info": "Same as Survival Mode, but blocks can't be added or removed.", "selectWorld.gameMode.adventure.line1": "Same as Survival Mode, but blocks can't", "selectWorld.gameMode.adventure.line2": "be added or removed", "selectWorld.gameMode.creative": "Creative", "selectWorld.gameMode.creative.info": "Create, build, and explore without limits. You can fly, have endless materials, and can't be hurt by monsters.", "selectWorld.gameMode.creative.line1": "Unlimited resources, free flying and", "selectWorld.gameMode.creative.line2": "destroy blocks instantly", "selectWorld.gameMode.hardcore": "Hardcore", "selectWorld.gameMode.hardcore.info": "Survival Mode locked to 'Hard' difficulty. You can't respawn if you die.", "selectWorld.gameMode.hardcore.line1": "Same as Survival Mode, locked at hardest", "selectWorld.gameMode.hardcore.line2": "difficulty, and one life only", "selectWorld.gameMode.spectator": "Spectator", "selectWorld.gameMode.spectator.info": "You can look but don't touch.", "selectWorld.gameMode.spectator.line1": "You can look but don't touch", "selectWorld.gameMode.spectator.line2": "", "selectWorld.gameMode.survival": "Survival", "selectWorld.gameMode.survival.info": "Explore a mysterious world where you build, collect, craft, and fight monsters.", "selectWorld.gameMode.survival.line1": "Search for resources, craft, gain", "selectWorld.gameMode.survival.line2": "levels, health and hunger", "selectWorld.gameRules": "Game Rules", "selectWorld.import_worldgen_settings": "Import Settings", "selectWorld.import_worldgen_settings.failure": "Error importing settings", "selectWorld.import_worldgen_settings.select_file": "Select settings file (.json)", "selectWorld.incompatible_series": "Created by an incompatible version", "selectWorld.load_folder_access": "Unable to read or access folder where game worlds are saved!", "selectWorld.loading_list": "Loading world list", "selectWorld.locked": "Locked by another running instance of <PERSON><PERSON>", "selectWorld.mapFeatures": "Generate Structures", "selectWorld.mapFeatures.info": "Villages, Shipwrecks, etc.", "selectWorld.mapType": "World Type", "selectWorld.mapType.normal": "Normal", "selectWorld.moreWorldOptions": "More World Options...", "selectWorld.newWorld": "New World", "selectWorld.recreate": "Re-Create", "selectWorld.recreate.customized.text": "Customized worlds are no longer supported in this version of Minecraft. We can try to recreate it with the same seed and properties, but any terrain customizations will be lost. We're sorry for the inconvenience!", "selectWorld.recreate.customized.title": "Customized worlds are no longer supported", "selectWorld.recreate.error.text": "Something went wrong while trying to recreate a world.", "selectWorld.recreate.error.title": "An error occurred!", "selectWorld.resultFolder": "Will be saved in:", "selectWorld.search": "search for worlds", "selectWorld.seedInfo": "Leave blank for a random seed", "selectWorld.select": "Play Selected World", "selectWorld.targetFolder": "Save folder: %s", "selectWorld.title": "Select World", "selectWorld.tooltip.fromNewerVersion1": "World was saved in a newer version,", "selectWorld.tooltip.fromNewerVersion2": "loading this world could cause problems!", "selectWorld.tooltip.snapshot1": "Don't forget to back up this world", "selectWorld.tooltip.snapshot2": "before you load it in this snapshot.", "selectWorld.unable_to_load": "Unable to load worlds", "selectWorld.version": "Version:", "selectWorld.versionJoinButton": "Load Anyway", "selectWorld.versionQuestion": "Do you really want to load this world?", "selectWorld.versionUnknown": "unknown", "selectWorld.versionWarning": "This world was last played in version %s and loading it in this version could cause corruption!", "selectWorld.warning.deprecated.question": "Some features used are deprecated and will stop working in the future. Do you wish to proceed?", "selectWorld.warning.deprecated.title": "Warning! These settings are using deprecated features", "selectWorld.warning.experimental.question": "These settings are experimental and could one day stop working. Do you wish to proceed?", "selectWorld.warning.experimental.title": "Warning! These settings are using experimental features", "selectWorld.world": "World", "sign.edit": "Edit Sign Message", "sleep.not_possible": "No amount of rest can pass this night", "sleep.players_sleeping": "%s/%s players sleeping", "sleep.skipping_night": "Sleeping through this night", "slot.unknown": "Unknown slot '%s'", "soundCategory.ambient": "Ambient/Environment", "soundCategory.block": "Blocks", "soundCategory.hostile": "Hostile Creatures", "soundCategory.master": "Master Volume", "soundCategory.music": "Music", "soundCategory.neutral": "Friendly Creatures", "soundCategory.player": "Players", "soundCategory.record": "Jukebox/Note Blocks", "soundCategory.voice": "Voice/Speech", "soundCategory.weather": "Weather", "spectatorMenu.close": "Close Menu", "spectatorMenu.next_page": "Next Page", "spectatorMenu.previous_page": "Previous Page", "spectatorMenu.root.prompt": "Press a key to select a command, and again to use it.", "spectatorMenu.team_teleport": "Teleport to Team Member", "spectatorMenu.team_teleport.prompt": "Select a team to teleport to", "spectatorMenu.teleport": "Teleport to Player", "spectatorMenu.teleport.prompt": "Select a player to teleport to", "stat_type.minecraft.broken": "Times Broken", "stat_type.minecraft.crafted": "Times Crafted", "stat_type.minecraft.dropped": "Dropped", "stat_type.minecraft.killed": "You killed %s %s", "stat_type.minecraft.killed_by": "%s killed you %s time(s)", "stat_type.minecraft.killed_by.none": "You have never been killed by %s", "stat_type.minecraft.killed.none": "You have never killed %s", "stat_type.minecraft.mined": "Times Mined", "stat_type.minecraft.picked_up": "Picked Up", "stat_type.minecraft.used": "Times Used", "stat.generalButton": "General", "stat.itemsButton": "Items", "stat.minecraft.animals_bred": "Animals Bred", "stat.minecraft.aviate_one_cm": "Distance by Elytra", "stat.minecraft.bell_ring": "Bells Rung", "stat.minecraft.boat_one_cm": "Distance by Boat", "stat.minecraft.clean_armor": "Armor Pieces Cleaned", "stat.minecraft.clean_banner": "Banners Cleaned", "stat.minecraft.clean_shulker_box": "Shulker Boxes Cleaned", "stat.minecraft.climb_one_cm": "Distance Climbed", "stat.minecraft.crouch_one_cm": "Distance Crouched", "stat.minecraft.damage_absorbed": "Damage Absorbed", "stat.minecraft.damage_blocked_by_shield": "Damage Blocked by Shield", "stat.minecraft.damage_dealt": "Damage Dealt", "stat.minecraft.damage_dealt_absorbed": "Damage Dealt (Absorbed)", "stat.minecraft.damage_dealt_resisted": "Damage Dealt (Resisted)", "stat.minecraft.damage_resisted": "Damage Resisted", "stat.minecraft.damage_taken": "Damage Taken", "stat.minecraft.deaths": "Number of Deaths", "stat.minecraft.drop": "Items Dropped", "stat.minecraft.eat_cake_slice": "Cake Slices Eaten", "stat.minecraft.enchant_item": "Items Enchanted", "stat.minecraft.fall_one_cm": "Distance Fallen", "stat.minecraft.fill_cauldron": "Cauldrons Filled", "stat.minecraft.fish_caught": "<PERSON> Caught", "stat.minecraft.fly_one_cm": "Distance Flown", "stat.minecraft.horse_one_cm": "Distance by Horse", "stat.minecraft.inspect_dispenser": "Dispensers Searched", "stat.minecraft.inspect_dropper": "Droppers Searched", "stat.minecraft.inspect_hopper": "Hoppers Searched", "stat.minecraft.interact_with_anvil": "Interactions with Anvil", "stat.minecraft.interact_with_beacon": "Interactions with Beacon", "stat.minecraft.interact_with_blast_furnace": "Interactions with Blast Furnace", "stat.minecraft.interact_with_brewingstand": "Interactions with Brewing Stand", "stat.minecraft.interact_with_campfire": "Interactions with Campfire", "stat.minecraft.interact_with_cartography_table": "Interactions with Cartography Table", "stat.minecraft.interact_with_crafting_table": "Interactions with Crafting Table", "stat.minecraft.interact_with_furnace": "Interactions with Furnace", "stat.minecraft.interact_with_grindstone": "Interactions with Grindstone", "stat.minecraft.interact_with_lectern": "Interactions with Lectern", "stat.minecraft.interact_with_loom": "Interactions with Loom", "stat.minecraft.interact_with_smithing_table": "Interactions with Smithing Table", "stat.minecraft.interact_with_smoker": "Interactions with Smoker", "stat.minecraft.interact_with_stonecutter": "Interactions with Stone<PERSON>ter", "stat.minecraft.jump": "Jumps", "stat.minecraft.junk_fished": "Jun<PERSON> Fished", "stat.minecraft.leave_game": "Games Quit", "stat.minecraft.minecart_one_cm": "Distance by Minecart", "stat.minecraft.mob_kills": "<PERSON><PERSON>", "stat.minecraft.open_barrel": "Barrels Opened", "stat.minecraft.open_chest": "Chests Opened", "stat.minecraft.open_enderchest": "<PERSON><PERSON> Chests Opened", "stat.minecraft.open_shulker_box": "Shulker Boxes Opened", "stat.minecraft.pig_one_cm": "Distance by Pig", "stat.minecraft.play_noteblock": "Note Blocks Played", "stat.minecraft.play_record": "Music Discs Played", "stat.minecraft.play_time": "Time Played", "stat.minecraft.player_kills": "Player Kills", "stat.minecraft.pot_flower": "Plants Potted", "stat.minecraft.raid_trigger": "Raids Triggered", "stat.minecraft.raid_win": "Raids Won", "stat.minecraft.ring_bell": "Bells Rung", "stat.minecraft.sleep_in_bed": "Times Slept in a Bed", "stat.minecraft.sneak_time": "Sneak Time", "stat.minecraft.sprint_one_cm": "Distance Sprinted", "stat.minecraft.strider_one_cm": "Distance by Strider", "stat.minecraft.swim_one_cm": "Distance Swum", "stat.minecraft.talked_to_villager": "Talked to Villagers", "stat.minecraft.target_hit": "Targets Hit", "stat.minecraft.time_since_death": "Time Since Last Death", "stat.minecraft.time_since_rest": "Time Since Last Rest", "stat.minecraft.total_world_time": "Time with World Open", "stat.minecraft.traded_with_villager": "Traded with Villagers", "stat.minecraft.treasure_fished": "Treasure Fished", "stat.minecraft.trigger_trapped_chest": "Trapped Chests Triggered", "stat.minecraft.tune_noteblock": "Note Blocks Tuned", "stat.minecraft.use_cauldron": "Water Taken from Cauldron", "stat.minecraft.walk_on_water_one_cm": "Distance Walked on Water", "stat.minecraft.walk_one_cm": "Distance Walked", "stat.minecraft.walk_under_water_one_cm": "Distance Walked under Water", "stat.mobsButton": "<PERSON><PERSON>", "stats.tooltip.type.statistic": "Statistic", "structure_block.button.detect_size": "DETECT", "structure_block.button.load": "LOAD", "structure_block.button.save": "SAVE", "structure_block.custom_data": "Custom Data Tag Name", "structure_block.detect_size": "Detect Structure Size and Position:", "structure_block.hover.corner": "Corner: %s", "structure_block.hover.data": "Data: %s", "structure_block.hover.load": "Load: %s", "structure_block.hover.save": "Save: %s", "structure_block.include_entities": "Include Entities:", "structure_block.integrity": "Structure Integrity and Seed", "structure_block.integrity.integrity": "Structure Integrity", "structure_block.integrity.seed": "Structure Seed", "structure_block.invalid_structure_name": "Invalid structure name '%s'", "structure_block.load_not_found": "Structure '%s' is not available", "structure_block.load_prepare": "Structure '%s' position prepared", "structure_block.load_success": "Structure loaded from '%s'", "structure_block.mode_info.corner": "Corner Mode - Place<PERSON> and <PERSON><PERSON>", "structure_block.mode_info.data": "Data Mode - Game Logic Marker", "structure_block.mode_info.load": "Load Mode - Load from File", "structure_block.mode_info.save": "Save Mode - Write to File", "structure_block.mode.corner": "Corner", "structure_block.mode.data": "Data", "structure_block.mode.load": "Load", "structure_block.mode.save": "Save", "structure_block.position": "Relative Position", "structure_block.position.x": "relative Position x", "structure_block.position.y": "relative position y", "structure_block.position.z": "relative position z", "structure_block.save_failure": "Unable to save structure '%s'", "structure_block.save_success": "Structure saved as '%s'", "structure_block.show_air": "Show Invisible Blocks:", "structure_block.show_boundingbox": "Show Bounding Box:", "structure_block.size": "Structure Size", "structure_block.size_failure": "Unable to detect structure size. Add corners with matching structure names", "structure_block.size_success": "<PERSON><PERSON> successfully detected for '%s'", "structure_block.size.x": "structure size x", "structure_block.size.y": "structure size y", "structure_block.size.z": "structure size z", "structure_block.structure_name": "Structure Name", "subtitles.ambient.cave": "Eerie noise", "subtitles.block.amethyst_block.chime": "Amethyst chimes", "subtitles.block.amethyst_block.resonate": "Amethyst resonates", "subtitles.block.anvil.destroy": "Anvil destroyed", "subtitles.block.anvil.land": "Anvil landed", "subtitles.block.anvil.use": "Anvil used", "subtitles.block.barrel.close": "Barrel closes", "subtitles.block.barrel.open": "Barrel opens", "subtitles.block.beacon.activate": "Beacon activates", "subtitles.block.beacon.ambient": "Beacon hums", "subtitles.block.beacon.deactivate": "Beacon deactivates", "subtitles.block.beacon.power_select": "Beacon power selected", "subtitles.block.beehive.drip": "Honey drips", "subtitles.block.beehive.enter": "<PERSON> enters hive", "subtitles.block.beehive.exit": "Bee leaves hive", "subtitles.block.beehive.shear": "Shears scrape", "subtitles.block.beehive.work": "Bees work", "subtitles.block.bell.resonate": "Bell resonates", "subtitles.block.bell.use": "Bell rings", "subtitles.block.big_dripleaf.tilt_down": "Dripleaf tilts down", "subtitles.block.big_dripleaf.tilt_up": "Dripleaf tilts up", "subtitles.block.blastfurnace.fire_crackle": "Blast Furnace crackles", "subtitles.block.brewing_stand.brew": "Brewing Stand bubbles", "subtitles.block.bubble_column.bubble_pop": "Bubbles pop", "subtitles.block.bubble_column.upwards_ambient": "Bubbles flow", "subtitles.block.bubble_column.upwards_inside": "Bubbles woosh", "subtitles.block.bubble_column.whirlpool_ambient": "Bubbles whirl", "subtitles.block.bubble_column.whirlpool_inside": "Bubbles zoom", "subtitles.block.button.click": "<PERSON><PERSON> clicks", "subtitles.block.cake.add_candle": "Cake squishes", "subtitles.block.campfire.crackle": "Campfire crackles", "subtitles.block.candle.crackle": "Candle crackles", "subtitles.block.chest.close": "Chest closes", "subtitles.block.chest.locked": "Chest locked", "subtitles.block.chest.open": "Chest opens", "subtitles.block.chorus_flower.death": "Chorus Flower withers", "subtitles.block.chorus_flower.grow": "Chorus Flower grows", "subtitles.block.comparator.click": "Comparator clicks", "subtitles.block.composter.empty": "Composter emptied", "subtitles.block.composter.fill": "Composter filled", "subtitles.block.composter.ready": "Composter composts", "subtitles.block.conduit.activate": "Conduit activates", "subtitles.block.conduit.ambient": "Conduit pulses", "subtitles.block.conduit.attack.target": "Conduit attacks", "subtitles.block.conduit.deactivate": "Conduit deactivates", "subtitles.block.decorated_pot.shatter": "Pot shatters", "subtitles.block.dispenser.dispense": "Dispensed item", "subtitles.block.dispenser.fail": "Dispenser failed", "subtitles.block.door.toggle": "Door creaks", "subtitles.block.enchantment_table.use": "Enchanting Table used", "subtitles.block.end_portal_frame.fill": "Eye of <PERSON><PERSON> attaches", "subtitles.block.end_portal.spawn": "End Portal opens", "subtitles.block.fence_gate.toggle": "Fence Gate creaks", "subtitles.block.fire.ambient": "Fire crackles", "subtitles.block.fire.extinguish": "Fire extinguished", "subtitles.block.frogspawn.hatch": "Tadpole hatches", "subtitles.block.furnace.fire_crackle": "Furnace crackles", "subtitles.block.generic.break": "Block broken", "subtitles.block.generic.footsteps": "Footsteps", "subtitles.block.generic.hit": "Block breaking", "subtitles.block.generic.place": "Block placed", "subtitles.block.grindstone.use": "Grindstone used", "subtitles.block.growing_plant.crop": "Plant cropped", "subtitles.block.honey_block.slide": "Sliding down a honey block", "subtitles.block.iron_trapdoor.close": "Trapdoor closes", "subtitles.block.iron_trapdoor.open": "Trapdoor opens", "subtitles.block.lava.ambient": "Lava pops", "subtitles.block.lava.extinguish": "<PERSON><PERSON> hisses", "subtitles.block.lever.click": "Lever clicks", "subtitles.block.note_block.note": "Note Block plays", "subtitles.block.piston.move": "<PERSON><PERSON> moves", "subtitles.block.pointed_dripstone.drip_lava": "Lava drips", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Lava drips into Cauldron", "subtitles.block.pointed_dripstone.drip_water": "Water drips", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Water drips into Cauldron", "subtitles.block.pointed_dripstone.land": "Stalactite crashes down", "subtitles.block.portal.ambient": "Portal whooshes", "subtitles.block.portal.travel": "Portal noise fades", "subtitles.block.portal.trigger": "Portal noise intensifies", "subtitles.block.pressure_plate.click": "Pressure Plate clicks", "subtitles.block.pumpkin.carve": "Shears carve", "subtitles.block.redstone_torch.burnout": "Torch fizzes", "subtitles.block.respawn_anchor.ambient": "Portal whooshes", "subtitles.block.respawn_anchor.charge": "<PERSON><PERSON><PERSON><PERSON> is charged", "subtitles.block.respawn_anchor.deplete": "<PERSON><PERSON><PERSON><PERSON> depletes", "subtitles.block.respawn_anchor.set_spawn": "<PERSON><PERSON><PERSON><PERSON> sets spawn", "subtitles.block.sculk_catalyst.bloom": "Sculk Catalyst blooms", "subtitles.block.sculk_sensor.clicking": "<PERSON><PERSON><PERSON> starts clicking", "subtitles.block.sculk_sensor.clicking_stop": "<PERSON><PERSON><PERSON> stops clicking", "subtitles.block.sculk_shrieker.shriek": "<PERSON><PERSON><PERSON> shrieks", "subtitles.block.sculk.charge": "Sculk bubbles", "subtitles.block.sculk.spread": "Sculk spreads", "subtitles.block.shulker_box.close": "<PERSON><PERSON><PERSON> closes", "subtitles.block.shulker_box.open": "<PERSON><PERSON><PERSON> opens", "subtitles.block.sign.waxed_interact_fail": "Sign wobbles", "subtitles.block.smithing_table.use": "Smithing Table used", "subtitles.block.smoker.smoke": "Smoker smokes", "subtitles.block.sniffer_egg.crack": "Sniffer Egg cracks", "subtitles.block.sniffer_egg.hatch": "Sniffer Egg hatches", "subtitles.block.sniffer_egg.plop": "Sniffer plops", "subtitles.block.sweet_berry_bush.pick_berries": "Berries pop", "subtitles.block.trapdoor.toggle": "Trapdoor creaks", "subtitles.block.tripwire.attach": "Tripwire attaches", "subtitles.block.tripwire.click": "Tripwire clicks", "subtitles.block.tripwire.detach": "Tripwire detaches", "subtitles.block.water.ambient": "Water flows", "subtitles.chiseled_bookshelf.insert": "Book placed", "subtitles.chiseled_bookshelf.insert_enchanted": "Enchanted Book placed", "subtitles.chiseled_bookshelf.take": "Book taken", "subtitles.chiseled_bookshelf.take_enchanted": "Enchanted Book taken", "subtitles.enchant.thorns.hit": "<PERSON><PERSON> prick", "subtitles.entity.allay.ambient_with_item": "Allay seeks", "subtitles.entity.allay.ambient_without_item": "Allay yearns", "subtitles.entity.allay.death": "<PERSON><PERSON> dies", "subtitles.entity.allay.hurt": "Allay hurts", "subtitles.entity.allay.item_given": "<PERSON>ay chortles", "subtitles.entity.allay.item_taken": "Allay allays", "subtitles.entity.allay.item_thrown": "Allay tosses", "subtitles.entity.armor_stand.fall": "Something fell", "subtitles.entity.arrow.hit": "Arrow hits", "subtitles.entity.arrow.hit_player": "Player hit", "subtitles.entity.arrow.shoot": "Arrow fired", "subtitles.entity.axolotl.attack": "Axolotl attacks", "subtitles.entity.axolotl.death": "<PERSON><PERSON><PERSON><PERSON> dies", "subtitles.entity.axolotl.hurt": "<PERSON><PERSON><PERSON><PERSON> hurts", "subtitles.entity.axolotl.idle_air": "Axolotl chirps", "subtitles.entity.axolotl.idle_water": "Axolotl chirps", "subtitles.entity.axolotl.splash": "Axolotl splashes", "subtitles.entity.axolotl.swim": "<PERSON><PERSON><PERSON><PERSON> swims", "subtitles.entity.bat.ambient": "<PERSON> screeches", "subtitles.entity.bat.death": "<PERSON> dies", "subtitles.entity.bat.hurt": "Bat hurts", "subtitles.entity.bat.takeoff": "<PERSON> takes off", "subtitles.entity.bee.ambient": "Bee buzzes", "subtitles.entity.bee.death": "<PERSON> dies", "subtitles.entity.bee.hurt": "<PERSON> hurts", "subtitles.entity.bee.loop": "Bee buzzes", "subtitles.entity.bee.loop_aggressive": "<PERSON> buzzes angrily", "subtitles.entity.bee.pollinate": "Bee buzzes happily", "subtitles.entity.bee.sting": "Bee stings", "subtitles.entity.blaze.ambient": "<PERSON> breathes", "subtitles.entity.blaze.burn": "<PERSON> crackles", "subtitles.entity.blaze.death": "<PERSON> dies", "subtitles.entity.blaze.hurt": "Blaze hurts", "subtitles.entity.blaze.shoot": "Blaze shoots", "subtitles.entity.boat.paddle_land": "Rowing", "subtitles.entity.boat.paddle_water": "Rowing", "subtitles.entity.camel.ambient": "Camel grunts", "subtitles.entity.camel.dash": "Camel yeets", "subtitles.entity.camel.dash_ready": "Camel recovers", "subtitles.entity.camel.death": "Camel dies", "subtitles.entity.camel.eat": "Camel eats", "subtitles.entity.camel.hurt": "Camel hurts", "subtitles.entity.camel.saddle": "Saddle equips", "subtitles.entity.camel.sit": "Camel sits down", "subtitles.entity.camel.stand": "Camel stands up", "subtitles.entity.camel.step": "Camel steps", "subtitles.entity.camel.step_sand": "Camel sands", "subtitles.entity.cat.ambient": "<PERSON> meows", "subtitles.entity.cat.beg_for_food": "Cat begs", "subtitles.entity.cat.death": "<PERSON> dies", "subtitles.entity.cat.eat": "Cat eats", "subtitles.entity.cat.hiss": "Cat hisses", "subtitles.entity.cat.hurt": "Cat hurts", "subtitles.entity.cat.purr": "Cat purrs", "subtitles.entity.chicken.ambient": "Chicken clucks", "subtitles.entity.chicken.death": "<PERSON> dies", "subtitles.entity.chicken.egg": "Chicken plops", "subtitles.entity.chicken.hurt": "Chicken hurts", "subtitles.entity.cod.death": "Cod dies", "subtitles.entity.cod.flop": "Cod flops", "subtitles.entity.cod.hurt": "Cod hurts", "subtitles.entity.cow.ambient": "Cow moos", "subtitles.entity.cow.death": "<PERSON>w dies", "subtitles.entity.cow.hurt": "Cow hurts", "subtitles.entity.cow.milk": "Cow gets milked", "subtitles.entity.creeper.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.creeper.hurt": "<PERSON><PERSON><PERSON> hurts", "subtitles.entity.creeper.primed": "<PERSON><PERSON><PERSON> hisses", "subtitles.entity.dolphin.ambient": "Dolphin chirps", "subtitles.entity.dolphin.ambient_water": "Dolphin whistles", "subtitles.entity.dolphin.attack": "Dolphin attacks", "subtitles.entity.dolphin.death": "Dolphin dies", "subtitles.entity.dolphin.eat": "Dolphin eats", "subtitles.entity.dolphin.hurt": "Dolphin hurts", "subtitles.entity.dolphin.jump": "Dolphin jumps", "subtitles.entity.dolphin.play": "Dolphin plays", "subtitles.entity.dolphin.splash": "Dolphin splashes", "subtitles.entity.dolphin.swim": "Dolphin swims", "subtitles.entity.donkey.ambient": "Donkey hee-haws", "subtitles.entity.donkey.angry": "Donkey neighs", "subtitles.entity.donkey.chest": "Donkey Chest equips", "subtitles.entity.donkey.death": "<PERSON><PERSON> dies", "subtitles.entity.donkey.eat": "Donkey eats", "subtitles.entity.donkey.hurt": "Donkey hurts", "subtitles.entity.drowned.ambient": "Drowned gurgles", "subtitles.entity.drowned.ambient_water": "Drowned gurgles", "subtitles.entity.drowned.death": "Drowned dies", "subtitles.entity.drowned.hurt": "Drowned hurts", "subtitles.entity.drowned.shoot": "Drowned throws Trident", "subtitles.entity.drowned.step": "Drowned steps", "subtitles.entity.drowned.swim": "Drowned swims", "subtitles.entity.egg.throw": "Egg flies", "subtitles.entity.elder_guardian.ambient": "Elder Guardian moans", "subtitles.entity.elder_guardian.ambient_land": "Elder Guardian flaps", "subtitles.entity.elder_guardian.curse": "Elder Guardian curses", "subtitles.entity.elder_guardian.death": "Elder Guardian dies", "subtitles.entity.elder_guardian.flop": "Elder Guardian flops", "subtitles.entity.elder_guardian.hurt": "Elder Guardian hurts", "subtitles.entity.ender_dragon.ambient": "Dragon roars", "subtitles.entity.ender_dragon.death": "<PERSON> dies", "subtitles.entity.ender_dragon.flap": "Dragon flaps", "subtitles.entity.ender_dragon.growl": "Dragon growls", "subtitles.entity.ender_dragon.hurt": "Dragon hurts", "subtitles.entity.ender_dragon.shoot": "Dragon shoots", "subtitles.entity.ender_eye.death": "Eye of <PERSON><PERSON> falls", "subtitles.entity.ender_eye.launch": "Eye of <PERSON><PERSON> shoots", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON> flies", "subtitles.entity.enderman.ambient": "<PERSON><PERSON> vwoops", "subtitles.entity.enderman.death": "<PERSON><PERSON> dies", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> hurts", "subtitles.entity.enderman.scream": "<PERSON><PERSON> screams", "subtitles.entity.enderman.stare": "<PERSON><PERSON> cries out", "subtitles.entity.enderman.teleport": "Enderman teleports", "subtitles.entity.endermite.ambient": "Endermite scuttles", "subtitles.entity.endermite.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.endermite.hurt": "Endermite hurts", "subtitles.entity.evoker_fangs.attack": "Fangs snap", "subtitles.entity.evoker.ambient": "Evoker murmurs", "subtitles.entity.evoker.cast_spell": "Evoker casts spell", "subtitles.entity.evoker.celebrate": "Evoker cheers", "subtitles.entity.evoker.death": "Evoker dies", "subtitles.entity.evoker.hurt": "Evoker hurts", "subtitles.entity.evoker.prepare_attack": "Evoker prepares attack", "subtitles.entity.evoker.prepare_summon": "Evoker prepares summoning", "subtitles.entity.evoker.prepare_wololo": "Evoker prepares charming", "subtitles.entity.experience_orb.pickup": "Experience gained", "subtitles.entity.firework_rocket.blast": "Firework blasts", "subtitles.entity.firework_rocket.launch": "Firework launches", "subtitles.entity.firework_rocket.twinkle": "Firework twinkles", "subtitles.entity.fishing_bobber.retrieve": "<PERSON><PERSON> retrieved", "subtitles.entity.fishing_bobber.splash": "Fishing Bobber splashes", "subtitles.entity.fishing_bobber.throw": "<PERSON><PERSON> thrown", "subtitles.entity.fox.aggro": "<PERSON> angers", "subtitles.entity.fox.ambient": "Fox squeaks", "subtitles.entity.fox.bite": "Fox bites", "subtitles.entity.fox.death": "<PERSON> dies", "subtitles.entity.fox.eat": "Fox eats", "subtitles.entity.fox.hurt": "<PERSON> hurts", "subtitles.entity.fox.screech": "<PERSON> screeches", "subtitles.entity.fox.sleep": "<PERSON> snores", "subtitles.entity.fox.sniff": "Fox sniffs", "subtitles.entity.fox.spit": "Fox spits", "subtitles.entity.fox.teleport": "Fox teleports", "subtitles.entity.frog.ambient": "Frog croaks", "subtitles.entity.frog.death": "<PERSON> dies", "subtitles.entity.frog.eat": "Frog eats", "subtitles.entity.frog.hurt": "Frog hurts", "subtitles.entity.frog.lay_spawn": "Frog lays spawn", "subtitles.entity.frog.long_jump": "Frog jumps", "subtitles.entity.generic.big_fall": "Something fell", "subtitles.entity.generic.burn": "Burning", "subtitles.entity.generic.death": "Dying", "subtitles.entity.generic.drink": "Sipping", "subtitles.entity.generic.eat": "Eating", "subtitles.entity.generic.explode": "Explosion", "subtitles.entity.generic.extinguish_fire": "Fire extinguishes", "subtitles.entity.generic.hurt": "Something hurts", "subtitles.entity.generic.small_fall": "Something trips", "subtitles.entity.generic.splash": "Splashing", "subtitles.entity.generic.swim": "Swimming", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON> cries", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> hurts", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> shoots", "subtitles.entity.glow_item_frame.add_item": "Glow Item <PERSON> fills", "subtitles.entity.glow_item_frame.break": "<PERSON><PERSON> <PERSON>em <PERSON> breaks", "subtitles.entity.glow_item_frame.place": "<PERSON><PERSON> <PERSON><PERSON> placed", "subtitles.entity.glow_item_frame.remove_item": "Glow Item Frame empties", "subtitles.entity.glow_item_frame.rotate_item": "Glow Item Frame clicks", "subtitles.entity.glow_squid.ambient": "Glow Squid swims", "subtitles.entity.glow_squid.death": "Glow Squid dies", "subtitles.entity.glow_squid.hurt": "Glow Squid hurts", "subtitles.entity.glow_squid.squirt": "Glow Squid shoots ink", "subtitles.entity.goat.ambient": "<PERSON><PERSON> bleats", "subtitles.entity.goat.death": "<PERSON><PERSON> dies", "subtitles.entity.goat.eat": "Goat eats", "subtitles.entity.goat.horn_break": "<PERSON><PERSON> breaks off", "subtitles.entity.goat.hurt": "<PERSON><PERSON> hurts", "subtitles.entity.goat.long_jump": "<PERSON><PERSON> leaps", "subtitles.entity.goat.milk": "<PERSON><PERSON> gets milked", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON> stomps", "subtitles.entity.goat.ram_impact": "Goat rams", "subtitles.entity.goat.screaming.ambient": "Goat bellows", "subtitles.entity.goat.step": "Goat steps", "subtitles.entity.guardian.ambient": "Guardian moans", "subtitles.entity.guardian.ambient_land": "Guardian flaps", "subtitles.entity.guardian.attack": "Guardian shoots", "subtitles.entity.guardian.death": "Guardian dies", "subtitles.entity.guardian.flop": "Guardian flops", "subtitles.entity.guardian.hurt": "Guardian hurts", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> growls", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON> growls angrily", "subtitles.entity.hoglin.attack": "Hoglin attacks", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON> converts to <PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON> hurts", "subtitles.entity.hoglin.retreat": "Hoglin retreats", "subtitles.entity.hoglin.step": "Hoglin steps", "subtitles.entity.horse.ambient": "Horse neighs", "subtitles.entity.horse.angry": "Horse neighs", "subtitles.entity.horse.armor": "Horse armor equips", "subtitles.entity.horse.breathe": "Horse breathes", "subtitles.entity.horse.death": "Horse dies", "subtitles.entity.horse.eat": "Horse eats", "subtitles.entity.horse.gallop": "Horse gallops", "subtitles.entity.horse.hurt": "Horse hurts", "subtitles.entity.horse.jump": "Horse jumps", "subtitles.entity.horse.saddle": "Saddle equips", "subtitles.entity.husk.ambient": "<PERSON><PERSON> groans", "subtitles.entity.husk.converted_to_zombie": "Husk converts to Zombie", "subtitles.entity.husk.death": "<PERSON><PERSON> dies", "subtitles.entity.husk.hurt": "Husk hurts", "subtitles.entity.illusioner.ambient": "Illusioner murmurs", "subtitles.entity.illusioner.cast_spell": "<PERSON><PERSON><PERSON> casts spell", "subtitles.entity.illusioner.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.illusioner.hurt": "Il<PERSON><PERSON> hurts", "subtitles.entity.illusioner.mirror_move": "<PERSON><PERSON><PERSON> displaces", "subtitles.entity.illusioner.prepare_blindness": "Il<PERSON><PERSON> prepares blindness", "subtitles.entity.illusioner.prepare_mirror": "Il<PERSON><PERSON> prepares mirror image", "subtitles.entity.iron_golem.attack": "Iron Golem attacks", "subtitles.entity.iron_golem.damage": "Iron Golem breaks", "subtitles.entity.iron_golem.death": "Iron Golem dies", "subtitles.entity.iron_golem.hurt": "Iron Golem hurts", "subtitles.entity.iron_golem.repair": "Iron Golem repaired", "subtitles.entity.item_frame.add_item": "<PERSON><PERSON>", "subtitles.entity.item_frame.break": "<PERSON><PERSON>", "subtitles.entity.item_frame.place": "<PERSON><PERSON> placed", "subtitles.entity.item_frame.remove_item": "Item Frame empties", "subtitles.entity.item_frame.rotate_item": "<PERSON><PERSON> clicks", "subtitles.entity.item.break": "<PERSON>em breaks", "subtitles.entity.item.pickup": "Item plops", "subtitles.entity.leash_knot.break": "<PERSON><PERSON> breaks", "subtitles.entity.leash_knot.place": "<PERSON><PERSON> tied", "subtitles.entity.lightning_bolt.impact": "Lightning strikes", "subtitles.entity.lightning_bolt.thunder": "Thunder roars", "subtitles.entity.llama.ambient": "<PERSON><PERSON><PERSON> bleats", "subtitles.entity.llama.angry": "<PERSON><PERSON><PERSON> bleats angrily", "subtitles.entity.llama.chest": "Llama Chest equips", "subtitles.entity.llama.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.llama.eat": "<PERSON><PERSON><PERSON> eats", "subtitles.entity.llama.hurt": "<PERSON><PERSON><PERSON> hurts", "subtitles.entity.llama.spit": "Llama spits", "subtitles.entity.llama.step": "L<PERSON>a steps", "subtitles.entity.llama.swag": "<PERSON><PERSON><PERSON> is decorated", "subtitles.entity.magma_cube.death": "Magma Cube dies", "subtitles.entity.magma_cube.hurt": "Magma Cube hurts", "subtitles.entity.magma_cube.squish": "Magma Cube squishes", "subtitles.entity.minecart.riding": "Minecart rolls", "subtitles.entity.mooshroom.convert": "Mooshroom transforms", "subtitles.entity.mooshroom.eat": "Mooshroom eats", "subtitles.entity.mooshroom.milk": "Mooshroom gets milked", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom gets milked suspiciously", "subtitles.entity.mule.ambient": "Mule hee-haws", "subtitles.entity.mule.angry": "<PERSON><PERSON> neighs", "subtitles.entity.mule.chest": "Mule Chest equips", "subtitles.entity.mule.death": "<PERSON><PERSON> dies", "subtitles.entity.mule.eat": "<PERSON><PERSON> eats", "subtitles.entity.mule.hurt": "<PERSON><PERSON> hurts", "subtitles.entity.painting.break": "Painting breaks", "subtitles.entity.painting.place": "Painting placed", "subtitles.entity.panda.aggressive_ambient": "Panda huffs", "subtitles.entity.panda.ambient": "Panda pants", "subtitles.entity.panda.bite": "Panda bites", "subtitles.entity.panda.cant_breed": "Panda bleats", "subtitles.entity.panda.death": "<PERSON><PERSON> dies", "subtitles.entity.panda.eat": "Panda eats", "subtitles.entity.panda.hurt": "<PERSON><PERSON> hurts", "subtitles.entity.panda.pre_sneeze": "<PERSON><PERSON>'s nose tickles", "subtitles.entity.panda.sneeze": "<PERSON><PERSON> sneezes", "subtitles.entity.panda.step": "Panda steps", "subtitles.entity.panda.worried_ambient": "Panda whimpers", "subtitles.entity.parrot.ambient": "Parrot talks", "subtitles.entity.parrot.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.parrot.eats": "<PERSON><PERSON><PERSON> eats", "subtitles.entity.parrot.fly": "Parrot flutters", "subtitles.entity.parrot.hurts": "<PERSON><PERSON><PERSON> hurts", "subtitles.entity.parrot.imitate.blaze": "<PERSON><PERSON><PERSON> breathes", "subtitles.entity.parrot.imitate.creeper": "Parrot hisses", "subtitles.entity.parrot.imitate.drowned": "<PERSON><PERSON><PERSON> gurgles", "subtitles.entity.parrot.imitate.elder_guardian": "<PERSON><PERSON><PERSON> moans", "subtitles.entity.parrot.imitate.ender_dragon": "Parrot roars", "subtitles.entity.parrot.imitate.endermite": "Parrot scuttles", "subtitles.entity.parrot.imitate.evoker": "Parrot murmurs", "subtitles.entity.parrot.imitate.ghast": "<PERSON><PERSON><PERSON> cries", "subtitles.entity.parrot.imitate.guardian": "<PERSON><PERSON><PERSON> moans", "subtitles.entity.parrot.imitate.hoglin": "<PERSON>rro<PERSON> growls", "subtitles.entity.parrot.imitate.husk": "<PERSON><PERSON><PERSON> groans", "subtitles.entity.parrot.imitate.illusioner": "Parrot murmurs", "subtitles.entity.parrot.imitate.magma_cube": "Parrot squishes", "subtitles.entity.parrot.imitate.phantom": "Parrot screeches", "subtitles.entity.parrot.imitate.piglin": "<PERSON><PERSON><PERSON> snorts", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON><PERSON><PERSON> snorts", "subtitles.entity.parrot.imitate.pillager": "Parrot murmurs", "subtitles.entity.parrot.imitate.ravager": "Parrot grunts", "subtitles.entity.parrot.imitate.shulker": "Parrot lurks", "subtitles.entity.parrot.imitate.silverfish": "Parrot hisses", "subtitles.entity.parrot.imitate.skeleton": "Parrot rattles", "subtitles.entity.parrot.imitate.slime": "Parrot squishes", "subtitles.entity.parrot.imitate.spider": "Parrot hisses", "subtitles.entity.parrot.imitate.stray": "Parrot rattles", "subtitles.entity.parrot.imitate.vex": "Parrot vexes", "subtitles.entity.parrot.imitate.vindicator": "<PERSON><PERSON><PERSON> mutters", "subtitles.entity.parrot.imitate.warden": "Parrot whines", "subtitles.entity.parrot.imitate.witch": "<PERSON><PERSON><PERSON> giggles", "subtitles.entity.parrot.imitate.wither": "<PERSON><PERSON><PERSON> angers", "subtitles.entity.parrot.imitate.wither_skeleton": "Parrot rattles", "subtitles.entity.parrot.imitate.zoglin": "<PERSON>rro<PERSON> growls", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON><PERSON> groans", "subtitles.entity.parrot.imitate.zombie_villager": "<PERSON><PERSON><PERSON> groans", "subtitles.entity.phantom.ambient": "Phantom screeches", "subtitles.entity.phantom.bite": "Phantom bites", "subtitles.entity.phantom.death": "Phantom dies", "subtitles.entity.phantom.flap": "Phantom flaps", "subtitles.entity.phantom.hurt": "Phantom hurts", "subtitles.entity.phantom.swoop": "Phantom swoops", "subtitles.entity.pig.ambient": "Pig oinks", "subtitles.entity.pig.death": "Pig dies", "subtitles.entity.pig.hurt": "Pig hurts", "subtitles.entity.pig.saddle": "Saddle equips", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON> snorts", "subtitles.entity.piglin_brute.angry": "<PERSON><PERSON> snorts angrily", "subtitles.entity.piglin_brute.converted_to_zombified": "<PERSON>lin Brute converts to Zombified Piglin", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON> dies", "subtitles.entity.piglin_brute.hurt": "<PERSON><PERSON> hurts", "subtitles.entity.piglin_brute.step": "<PERSON><PERSON> steps", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> admires item", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> snorts", "subtitles.entity.piglin.angry": "<PERSON><PERSON> snorts angrily", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> celebrates", "subtitles.entity.piglin.converted_to_zombified": "<PERSON><PERSON> converts to Zombified Piglin", "subtitles.entity.piglin.death": "<PERSON><PERSON> dies", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> hurts", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> snorts enviously", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> retreats", "subtitles.entity.piglin.step": "<PERSON><PERSON> steps", "subtitles.entity.pillager.ambient": "Pillager murmurs", "subtitles.entity.pillager.celebrate": "Pillager cheers", "subtitles.entity.pillager.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.pillager.hurt": "Pillager hurts", "subtitles.entity.player.attack.crit": "Critical attack", "subtitles.entity.player.attack.knockback": "Knockback attack", "subtitles.entity.player.attack.strong": "Strong attack", "subtitles.entity.player.attack.sweep": "Sweeping attack", "subtitles.entity.player.attack.weak": "Weak attack", "subtitles.entity.player.burp": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.death": "Player dies", "subtitles.entity.player.freeze_hurt": "Player freezes", "subtitles.entity.player.hurt": "Player hurts", "subtitles.entity.player.hurt_drown": "Player drowning", "subtitles.entity.player.hurt_on_fire": "Player burns", "subtitles.entity.player.levelup": "Player dings", "subtitles.entity.polar_bear.ambient": "Polar Bear groans", "subtitles.entity.polar_bear.ambient_baby": "Polar Bear hums", "subtitles.entity.polar_bear.death": "Polar Bear dies", "subtitles.entity.polar_bear.hurt": "Polar Bear hurts", "subtitles.entity.polar_bear.warning": "Polar Bear roars", "subtitles.entity.potion.splash": "Bottle smashes", "subtitles.entity.potion.throw": "Bottle thrown", "subtitles.entity.puffer_fish.blow_out": "Pufferfish deflates", "subtitles.entity.puffer_fish.blow_up": "Pufferfish inflates", "subtitles.entity.puffer_fish.death": "Pufferfish dies", "subtitles.entity.puffer_fish.flop": "Pufferfish flops", "subtitles.entity.puffer_fish.hurt": "Pufferfish hurts", "subtitles.entity.puffer_fish.sting": "Pufferfish stings", "subtitles.entity.rabbit.ambient": "Rabbit squeaks", "subtitles.entity.rabbit.attack": "Rabbit attacks", "subtitles.entity.rabbit.death": "<PERSON> dies", "subtitles.entity.rabbit.hurt": "Rabbit hurts", "subtitles.entity.rabbit.jump": "Rabbit hops", "subtitles.entity.ravager.ambient": "Ravager grunts", "subtitles.entity.ravager.attack": "Ravager bites", "subtitles.entity.ravager.celebrate": "<PERSON><PERSON><PERSON> cheers", "subtitles.entity.ravager.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.ravager.hurt": "Ravager hurts", "subtitles.entity.ravager.roar": "Ravager roars", "subtitles.entity.ravager.step": "Ravager steps", "subtitles.entity.ravager.stunned": "<PERSON><PERSON><PERSON> stunned", "subtitles.entity.salmon.death": "<PERSON> dies", "subtitles.entity.salmon.flop": "Salmon flops", "subtitles.entity.salmon.hurt": "Salmon hurts", "subtitles.entity.sheep.ambient": "Sheep baahs", "subtitles.entity.sheep.death": "Sheep dies", "subtitles.entity.sheep.hurt": "Sheep hurts", "subtitles.entity.shulker_bullet.hit": "<PERSON><PERSON><PERSON> Bullet explodes", "subtitles.entity.shulker_bullet.hurt": "<PERSON><PERSON><PERSON> Bullet breaks", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON> lurks", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> closes", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> hurts", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> opens", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> shoots", "subtitles.entity.shulker.teleport": "Shulker teleports", "subtitles.entity.silverfish.ambient": "Silverfish hisses", "subtitles.entity.silverfish.death": "Silverfish dies", "subtitles.entity.silverfish.hurt": "Silverfish hurts", "subtitles.entity.skeleton_horse.ambient": "Skeleton Horse cries", "subtitles.entity.skeleton_horse.death": "Skeleton Horse dies", "subtitles.entity.skeleton_horse.hurt": "Skeleton Horse hurts", "subtitles.entity.skeleton_horse.swim": "Skeleton Horse swims", "subtitles.entity.skeleton.ambient": "Skeleton rattles", "subtitles.entity.skeleton.converted_to_stray": "Skeleton converts to Stray", "subtitles.entity.skeleton.death": "Skel<PERSON> dies", "subtitles.entity.skeleton.hurt": "Skeleton hurts", "subtitles.entity.skeleton.shoot": "Skeleton shoots", "subtitles.entity.slime.attack": "Slime attacks", "subtitles.entity.slime.death": "<PERSON><PERSON> dies", "subtitles.entity.slime.hurt": "Slime hurts", "subtitles.entity.slime.squish": "Slime squishes", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.sniffer.digging": "Sniffer digs", "subtitles.entity.sniffer.digging_stop": "<PERSON><PERSON><PERSON> stands up", "subtitles.entity.sniffer.drop_seed": "Sniffer drops seed", "subtitles.entity.sniffer.eat": "Sniffer eats", "subtitles.entity.sniffer.egg_crack": "Sniffer Egg cracks", "subtitles.entity.sniffer.egg_hatch": "Sniffer Egg hatches", "subtitles.entity.sniffer.happy": "Sniffer delights", "subtitles.entity.sniffer.hurt": "Sniffer hurts", "subtitles.entity.sniffer.idle": "Sniffer grunts", "subtitles.entity.sniffer.scenting": "Sniffer scents", "subtitles.entity.sniffer.searching": "Sniffer searches", "subtitles.entity.sniffer.sniffing": "Sniffer sniffs", "subtitles.entity.sniffer.step": "Sniffer steps", "subtitles.entity.snow_golem.death": "<PERSON><PERSON> dies", "subtitles.entity.snow_golem.hurt": "Snow Golem hurts", "subtitles.entity.snowball.throw": "Snowball flies", "subtitles.entity.spider.ambient": "Spider hisses", "subtitles.entity.spider.death": "<PERSON> dies", "subtitles.entity.spider.hurt": "<PERSON> hurts", "subtitles.entity.squid.ambient": "Squid swims", "subtitles.entity.squid.death": "<PERSON><PERSON> dies", "subtitles.entity.squid.hurt": "Squid hurts", "subtitles.entity.squid.squirt": "Squid shoots ink", "subtitles.entity.stray.ambient": "Stray rattles", "subtitles.entity.stray.death": "<PERSON><PERSON> dies", "subtitles.entity.stray.hurt": "Stray hurts", "subtitles.entity.strider.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.strider.eat": "<PERSON><PERSON><PERSON> eats", "subtitles.entity.strider.happy": "Strider warbles", "subtitles.entity.strider.hurt": "Strider hurts", "subtitles.entity.strider.idle": "Strider chirps", "subtitles.entity.strider.retreat": "Strider retreats", "subtitles.entity.tadpole.death": "<PERSON><PERSON> dies", "subtitles.entity.tadpole.flop": "Tadpole flops", "subtitles.entity.tadpole.grow_up": "Tadpole grows up", "subtitles.entity.tadpole.hurt": "Tadpole hurts", "subtitles.entity.tnt.primed": "TNT fizzes", "subtitles.entity.tropical_fish.death": "Tropical Fish dies", "subtitles.entity.tropical_fish.flop": "Tropical Fish flops", "subtitles.entity.tropical_fish.hurt": "Tropical Fish hurts", "subtitles.entity.turtle.ambient_land": "Turtle chirps", "subtitles.entity.turtle.death": "<PERSON> dies", "subtitles.entity.turtle.death_baby": "<PERSON> baby dies", "subtitles.entity.turtle.egg_break": "<PERSON> breaks", "subtitles.entity.turtle.egg_crack": "Turtle Egg cracks", "subtitles.entity.turtle.egg_hatch": "Turtle Egg hatches", "subtitles.entity.turtle.hurt": "Turtle hurts", "subtitles.entity.turtle.hurt_baby": "<PERSON> baby hurts", "subtitles.entity.turtle.lay_egg": "Turtle lays egg", "subtitles.entity.turtle.shamble": "Turtle shambles", "subtitles.entity.turtle.shamble_baby": "Turtle baby shambles", "subtitles.entity.turtle.swim": "Turtle swims", "subtitles.entity.vex.ambient": "Vex vexes", "subtitles.entity.vex.charge": "Vex shrieks", "subtitles.entity.vex.death": "Vex dies", "subtitles.entity.vex.hurt": "Vex hurts", "subtitles.entity.villager.ambient": "Villager mumbles", "subtitles.entity.villager.celebrate": "Villager cheers", "subtitles.entity.villager.death": "Villager dies", "subtitles.entity.villager.hurt": "Villager hurts", "subtitles.entity.villager.no": "Villager disagrees", "subtitles.entity.villager.trade": "Villager trades", "subtitles.entity.villager.work_armorer": "Armorer works", "subtitles.entity.villager.work_butcher": "Butcher works", "subtitles.entity.villager.work_cartographer": "Cartographer works", "subtitles.entity.villager.work_cleric": "Cleric works", "subtitles.entity.villager.work_farmer": "Farmer works", "subtitles.entity.villager.work_fisherman": "Fisherman works", "subtitles.entity.villager.work_fletcher": "Fletcher works", "subtitles.entity.villager.work_leatherworker": "Leatherworker works", "subtitles.entity.villager.work_librarian": "Librarian works", "subtitles.entity.villager.work_mason": "Mason works", "subtitles.entity.villager.work_shepherd": "Shepherd works", "subtitles.entity.villager.work_toolsmith": "Toolsmith works", "subtitles.entity.villager.work_weaponsmith": "Weaponsmith works", "subtitles.entity.villager.yes": "Villager agrees", "subtitles.entity.vindicator.ambient": "Vindicator mutters", "subtitles.entity.vindicator.celebrate": "Vindicator cheers", "subtitles.entity.vindicator.death": "Vindicator dies", "subtitles.entity.vindicator.hurt": "Vindicator hurts", "subtitles.entity.wandering_trader.ambient": "Wandering Trader mumbles", "subtitles.entity.wandering_trader.death": "Wandering Trader dies", "subtitles.entity.wandering_trader.disappeared": "Wandering Trader disappears", "subtitles.entity.wandering_trader.drink_milk": "Wandering Trader drinks milk", "subtitles.entity.wandering_trader.drink_potion": "Wandering Trader drinks potion", "subtitles.entity.wandering_trader.hurt": "Wandering Trader hurts", "subtitles.entity.wandering_trader.no": "Wandering Trader disagrees", "subtitles.entity.wandering_trader.reappeared": "Wandering Trader appears", "subtitles.entity.wandering_trader.trade": "Wandering Trader trades", "subtitles.entity.wandering_trader.yes": "Wandering Trader agrees", "subtitles.entity.warden.agitated": "Warden groans angrily", "subtitles.entity.warden.ambient": "Warden whines", "subtitles.entity.warden.angry": "Warden rages", "subtitles.entity.warden.attack_impact": "Warden lands hit", "subtitles.entity.warden.death": "Warden dies", "subtitles.entity.warden.dig": "Warden digs", "subtitles.entity.warden.emerge": "Warden emerges", "subtitles.entity.warden.heartbeat": "Warden's heart beats", "subtitles.entity.warden.hurt": "Warden hurts", "subtitles.entity.warden.listening": "Warden takes notice", "subtitles.entity.warden.listening_angry": "Warden takes notice angrily", "subtitles.entity.warden.nearby_close": "Warden approaches", "subtitles.entity.warden.nearby_closer": "Warden advances", "subtitles.entity.warden.nearby_closest": "<PERSON> draws close", "subtitles.entity.warden.roar": "Warden roars", "subtitles.entity.warden.sniff": "Warden sniffs", "subtitles.entity.warden.sonic_boom": "Warden booms", "subtitles.entity.warden.sonic_charge": "Warden charges", "subtitles.entity.warden.step": "Warden steps", "subtitles.entity.warden.tendril_clicks": "Warden's tendrils click", "subtitles.entity.witch.ambient": "Witch giggles", "subtitles.entity.witch.celebrate": "Witch cheers", "subtitles.entity.witch.death": "Witch dies", "subtitles.entity.witch.drink": "Witch drinks", "subtitles.entity.witch.hurt": "Witch hurts", "subtitles.entity.witch.throw": "Witch throws", "subtitles.entity.wither_skeleton.ambient": "Wither Skeleton rattles", "subtitles.entity.wither_skeleton.death": "<PERSON><PERSON> dies", "subtitles.entity.wither_skeleton.hurt": "Wither <PERSON> hurts", "subtitles.entity.wither.ambient": "Wither angers", "subtitles.entity.wither.death": "<PERSON><PERSON> dies", "subtitles.entity.wither.hurt": "Wither hurts", "subtitles.entity.wither.shoot": "Wither attacks", "subtitles.entity.wither.spawn": "<PERSON><PERSON> released", "subtitles.entity.wolf.ambient": "Wolf pants", "subtitles.entity.wolf.death": "<PERSON> dies", "subtitles.entity.wolf.growl": "Wolf growls", "subtitles.entity.wolf.hurt": "<PERSON> hurts", "subtitles.entity.wolf.shake": "Wolf shakes", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON> growls", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> growls angrily", "subtitles.entity.zoglin.attack": "Zoglin attacks", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> hurts", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> steps", "subtitles.entity.zombie_horse.ambient": "Zombie Horse cries", "subtitles.entity.zombie_horse.death": "<PERSON> Horse dies", "subtitles.entity.zombie_horse.hurt": "Zombie Horse hurts", "subtitles.entity.zombie_villager.ambient": "Zombie Villager groans", "subtitles.entity.zombie_villager.converted": "Zombie Villager vociferates", "subtitles.entity.zombie_villager.cure": "Zombie Villager snuffles", "subtitles.entity.zombie_villager.death": "Zombie Villager dies", "subtitles.entity.zombie_villager.hurt": "Zombie Villager hurts", "subtitles.entity.zombie.ambient": "Zombie groans", "subtitles.entity.zombie.attack_wooden_door": "Door shakes", "subtitles.entity.zombie.break_wooden_door": "Door breaks", "subtitles.entity.zombie.converted_to_drowned": "Zombie converts to Drowned", "subtitles.entity.zombie.death": "<PERSON> dies", "subtitles.entity.zombie.destroy_egg": "<PERSON> stomped", "subtitles.entity.zombie.hurt": "Zombie hurts", "subtitles.entity.zombie.infect": "Zombie infects", "subtitles.entity.zombified_piglin.ambient": "Zombified <PERSON><PERSON> grunts", "subtitles.entity.zombified_piglin.angry": "Zombified <PERSON><PERSON> grunts angrily", "subtitles.entity.zombified_piglin.death": "Zombified <PERSON><PERSON> dies", "subtitles.entity.zombified_piglin.hurt": "Zombified <PERSON><PERSON> hurts", "subtitles.event.raid.horn": "Ominous horn blares", "subtitles.item.armor.equip": "Gear equips", "subtitles.item.armor.equip_chain": "Chain armor jingles", "subtitles.item.armor.equip_diamond": "Diamond armor clangs", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON> rustle", "subtitles.item.armor.equip_gold": "Gold armor clinks", "subtitles.item.armor.equip_iron": "Iron armor clanks", "subtitles.item.armor.equip_leather": "Leather armor rustles", "subtitles.item.armor.equip_netherite": "Netherite armor clanks", "subtitles.item.armor.equip_turtle": "Turtle Shell thunks", "subtitles.item.axe.scrape": "Axe scrapes", "subtitles.item.axe.strip": "Axe strips", "subtitles.item.axe.wax_off": "Wax off", "subtitles.item.bone_meal.use": "Bone Meal crinkles", "subtitles.item.book.page_turn": "Page rustles", "subtitles.item.book.put": "Book thumps", "subtitles.item.bottle.empty": "Bottle empties", "subtitles.item.bottle.fill": "Bottle fills", "subtitles.item.brush.brushing.generic": "Brushing", "subtitles.item.brush.brushing.gravel": "Brushing Gravel", "subtitles.item.brush.brushing.gravel.complete": "Brushing Gravel completed", "subtitles.item.brush.brushing.sand": "Brushing Sand", "subtitles.item.brush.brushing.sand.complete": "Brushing Sand completed", "subtitles.item.bucket.empty": "Bucket empties", "subtitles.item.bucket.fill": "Bucket fills", "subtitles.item.bucket.fill_axolotl": "<PERSON><PERSON><PERSON><PERSON> scooped", "subtitles.item.bucket.fill_fish": "Fish captured", "subtitles.item.bucket.fill_tadpole": "Tadpole captured", "subtitles.item.bundle.drop_contents": "Bundle empties", "subtitles.item.bundle.insert": "Item packed", "subtitles.item.bundle.remove_one": "Item unpacked", "subtitles.item.chorus_fruit.teleport": "Player teleports", "subtitles.item.crop.plant": "Crop planted", "subtitles.item.crossbow.charge": "Crossbow charges up", "subtitles.item.crossbow.hit": "Arrow hits", "subtitles.item.crossbow.load": "Crossbow loads", "subtitles.item.crossbow.shoot": "Crossbow fires", "subtitles.item.dye.use": "Dye stains", "subtitles.item.firecharge.use": "Fireball whooshes", "subtitles.item.flintandsteel.use": "Flint and Steel click", "subtitles.item.glow_ink_sac.use": "Glow Ink Sac splotches", "subtitles.item.goat_horn.play": "<PERSON><PERSON> plays", "subtitles.item.hoe.till": "Hoe tills", "subtitles.item.honey_bottle.drink": "Gulping", "subtitles.item.honeycomb.wax_on": "Wax on", "subtitles.item.ink_sac.use": "Ink Sac splotches", "subtitles.item.lodestone_compass.lock": "Lodestone Compass locks onto Lodestone", "subtitles.item.nether_wart.plant": "Crop planted", "subtitles.item.shears.shear": "Shears click", "subtitles.item.shield.block": "Shield blocks", "subtitles.item.shovel.flatten": "<PERSON><PERSON><PERSON> flattens", "subtitles.item.spyglass.stop_using": "Spyglass retracts", "subtitles.item.spyglass.use": "Spyglass expands", "subtitles.item.totem.use": "Totem activates", "subtitles.item.trident.hit": "Trident stabs", "subtitles.item.trident.hit_ground": "Trident vibrates", "subtitles.item.trident.return": "Trident returns", "subtitles.item.trident.riptide": "Trident zooms", "subtitles.item.trident.throw": "Trident clangs", "subtitles.item.trident.thunder": "Trident thunder cracks", "subtitles.particle.soul_escape": "Soul escapes", "subtitles.ui.cartography_table.take_result": "Map drawn", "subtitles.ui.loom.take_result": "Loom used", "subtitles.ui.stonecutter.take_result": "Stonecutter used", "subtitles.weather.rain": "Rain falls", "symlink_warning.title": "World folder contains symbolic links", "symlink_warning.message": "Loading worlds from folders with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "team.collision.always": "Always", "team.collision.never": "Never", "team.collision.pushOtherTeams": "Push other teams", "team.collision.pushOwnTeam": "<PERSON><PERSON> own team", "team.notFound": "Unknown team '%s'", "team.visibility.always": "Always", "team.visibility.hideForOtherTeams": "Hide for other teams", "team.visibility.hideForOwnTeam": "Hide for own team", "team.visibility.never": "Never", "telemetry_info.button.give_feedback": "<PERSON>", "telemetry_info.button.show_data": "Open My Data", "telemetry_info.property_title": "Included Data", "telemetry_info.screen.description": "Collecting this data helps us improve Minecraft by guiding us in directions that are relevant to our players.\nYou can also send in additional feedback to help us keep improving Minecraft.", "telemetry_info.screen.title": "Telemetry Data Collection", "telemetry.event.advancement_made.description": "Understanding the context behind receiving an advancement can help us better understand and improve the progression of the game.", "telemetry.event.advancement_made.title": "Advancement Made", "telemetry.event.game_load_times.description": "This event can help us figure out where startup performance improvements are needed by measuring the execution times of the startup phases.", "telemetry.event.game_load_times.title": "Game Load Times", "telemetry.event.optional": "%s (Optional)", "telemetry.event.performance_metrics.description": "Knowing the overall performance profile of Minecraft helps us tune and optimize the game for a wide range of machine specifications and operating systems. \nGame version is included to help us compare the performance profile for new versions of Minecraft.", "telemetry.event.performance_metrics.title": "Performance Metrics", "telemetry.event.required": "%s (Required)", "telemetry.event.world_load_times.description": "It’s important for us to understand how long it takes to join a world, and how that changes over time. For example, when we add new features or do larger technical changes, we need to see what impact that had on load times.", "telemetry.event.world_load_times.title": "World Load Times", "telemetry.event.world_loaded.description": "Knowing how players play Minecraft (such as Game Mode, client or server modded, and game version) allows us to focus game updates to improve the areas that players care about most.\nThe World Loaded event is paired with the World Unloaded event to calculate how long the play session has lasted.", "telemetry.event.world_loaded.title": "World Loaded", "telemetry.event.world_unloaded.description": "This event is paired with the World Loaded event to calculate how long the world session has lasted.\nThe duration (in seconds and ticks) is measured when a world session has ended (quitting to title, disconnecting from a server).", "telemetry.event.world_unloaded.title": "World Unloaded", "telemetry.property.advancement_game_time.title": "Game Time (Ticks)", "telemetry.property.advancement_id.title": "Advancement ID", "telemetry.property.client_id.title": "Client ID", "telemetry.property.client_modded.title": "<PERSON><PERSON>", "telemetry.property.dedicated_memory_kb.title": "Dedicated Memory (kB)", "telemetry.property.event_timestamp_utc.title": "Event Timestamp (UTC)", "telemetry.property.frame_rate_samples.title": "Frame Rate Samples (FPS)", "telemetry.property.game_mode.title": "Game Mode", "telemetry.property.game_version.title": "Game Version", "telemetry.property.launcher_name.title": "Launcher Name", "telemetry.property.load_time_bootstrap_ms.title": "Bootstrap Time (Milliseconds)", "telemetry.property.load_time_loading_overlay_ms.title": "Time in Loading Screen (Milliseconds)", "telemetry.property.load_time_pre_window_ms.title": "Time Before Window Opens (Milliseconds)", "telemetry.property.load_time_total_time_ms.title": "Total Load Time (Milliseconds)", "telemetry.property.minecraft_session_id.title": "Minecraft Session ID", "telemetry.property.new_world.title": "New World", "telemetry.property.number_of_samples.title": "Sample Count", "telemetry.property.operating_system.title": "Operating System", "telemetry.property.opt_in.title": "Opt-In", "telemetry.property.platform.title": "Platform", "telemetry.property.realms_map_content.title": "Realms Map Content (Minigame Name)", "telemetry.property.render_distance.title": "Render Distance", "telemetry.property.render_time_samples.title": "Render Time Samples", "telemetry.property.seconds_since_load.title": "Time Since Load (Seconds)", "telemetry.property.server_modded.title": "Server Modded", "telemetry.property.server_type.title": "Server Type", "telemetry.property.ticks_since_load.title": "Time Since Load (Ticks)", "telemetry.property.used_memory_samples.title": "Used Random Access Memory", "telemetry.property.user_id.title": "User ID", "telemetry.property.world_load_time_ms.title": "World Load Time (Milliseconds)", "telemetry.property.world_session_id.title": "World Session ID", "title.32bit.deprecation": "32-bit system detected: this may prevent you from playing in the future as a 64-bit system will be required!", "title.32bit.deprecation.realms": "Minecraft will soon require a 64-bit system, which will prevent you from playing or using Realms on this device. You will need to manually cancel any Realms subscription.", "title.32bit.deprecation.realms.check": "Do not show this screen again", "title.32bit.deprecation.realms.header": "32-bit system detected", "title.multiplayer.disabled": "Multiplayer is disabled. Please check your Microsoft account settings.", "title.multiplayer.disabled.banned.permanent": "Your account is permanently suspended from online play", "title.multiplayer.disabled.banned.temporary": "Your account is temporarily suspended from online play", "title.multiplayer.lan": "Multiplayer (LAN)", "title.multiplayer.other": "Multiplayer (3rd-party Server)", "title.multiplayer.realms": "Multiplayer (Realms)", "title.singleplayer": "Singleplayer", "translation.test.args": "%s %s", "translation.test.complex": "Prefix, %s%2$s again %s and %1$s lastly %s and also %1$s again!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "hi %", "translation.test.invalid2": "hi %  s", "translation.test.none": "Hello, world!", "translation.test.world": "world", "trim_material.minecraft.amethyst": "Amethyst Material", "trim_material.minecraft.copper": "Copper Material", "trim_material.minecraft.diamond": "Diamond Material", "trim_material.minecraft.emerald": "Emerald Material", "trim_material.minecraft.gold": "Gold Material", "trim_material.minecraft.iron": "Iron Material", "trim_material.minecraft.lapis": "Lapis Material", "trim_material.minecraft.netherite": "Netherite Material", "trim_material.minecraft.quartz": "Quartz Material", "trim_material.minecraft.redstone": "Redstone Material", "trim_pattern.minecraft.coast": "Coast Armor Trim", "trim_pattern.minecraft.dune": "<PERSON>ne Armor <PERSON>", "trim_pattern.minecraft.eye": "Eye Armor Trim", "trim_pattern.minecraft.host": "Host <PERSON><PERSON>", "trim_pattern.minecraft.raiser": "Raiser Armor Trim", "trim_pattern.minecraft.rib": "<PERSON><PERSON>", "trim_pattern.minecraft.sentry": "Sentry Armor Trim", "trim_pattern.minecraft.shaper": "<PERSON><PERSON><PERSON> Armor <PERSON>", "trim_pattern.minecraft.silence": "Silence Armor Trim", "trim_pattern.minecraft.snout": "Snout Armor Trim", "trim_pattern.minecraft.spire": "Spire Arm<PERSON>", "trim_pattern.minecraft.tide": "Tide Armor Trim", "trim_pattern.minecraft.vex": "Vex Armor Trim", "trim_pattern.minecraft.ward": "<PERSON>", "trim_pattern.minecraft.wayfinder": "Wayfinder Armor Trim", "trim_pattern.minecraft.wild": "Wild Armor Trim", "tutorial.bundleInsert.description": "Right Click to add items", "tutorial.bundleInsert.title": "Use a Bundle", "tutorial.craft_planks.description": "The recipe book can help", "tutorial.craft_planks.title": "Craft wooden planks", "tutorial.find_tree.description": "Punch it to collect wood", "tutorial.find_tree.title": "Find a tree", "tutorial.look.description": "Use your mouse to turn", "tutorial.look.title": "Look around", "tutorial.move.description": "Jump with %s", "tutorial.move.title": "Move with %s, %s, %s and %s", "tutorial.open_inventory.description": "Press %s", "tutorial.open_inventory.title": "Open your inventory", "tutorial.punch_tree.description": "Hold down %s", "tutorial.punch_tree.title": "Destroy the tree", "tutorial.socialInteractions.description": "Press %s to open", "tutorial.socialInteractions.title": "Social Interactions", "upgrade.minecraft.netherite_upgrade": "Netherite Upgrade"}