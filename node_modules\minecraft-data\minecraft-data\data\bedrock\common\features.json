[{"name": "smallWorld", "description": "world height is 128 blocks", "version": "0.14.3"}, {"name": "usesPalettedChunks", "description": "the chunk format uses local palettes", "versions": ["1.16_major", "latest"]}, {"name": "newRecipeSchema", "description": "New recipe schema", "versions": ["1.16_major", "latest"]}, {"name": "usesBlockStates", "versions": ["1.16_major", "latest"]}, {"name": "tallWorld", "description": "world y defaults to starts at -64 and ends at 384", "versions": ["1.18.0", "latest"]}, {"name": "itemSerializeUsesAuxValue", "description": "in newer versions, item metadata and count are separate fields, but before 1.16.220, they're stored in one field called 'auxiliary_value'", "versions": ["1.16.201", "1.16.220"]}, {"name": "spawnEggsUseInternalIdInNbt", "description": "in older versions, spawn eggs have a field in their nbt called 'internalId' which tells what entity they will spawn", "versions": ["1.16_major", "latest"]}, {"name": "spawnEggsHaveSpawnedEntityInName", "description": "in newer versions, spawn eggs have the entity they spawn in their name, ex: 'squid_spawn_egg'", "versions": ["1.16.201", "latest"]}, {"name": "whereDurabilityIsSerialized", "description": "where the durability is saved in nbt", "values": [{"value": "Damage", "versions": ["1.16_major", "latest"]}]}, {"name": "typeOfValueForEnchantLevel", "description": "type of value that stores enchant lvl in the nbt", "values": [{"value": "short", "versions": ["1.16_major", "latest"]}]}, {"name": "nbtNameForEnchant", "description": "what the nbt key for enchants is", "values": [{"value": "ench", "versions": ["1.16_major", "latest"]}]}, {"name": "explicitMaxDurability", "description": "Items with maximum durability have explicit NBT data Damage:0", "versions": ["1.16_major", "latest"]}, {"name": "compressorInPacketHeader", "description": "Packet header includes compressor", "versions": ["1.20.61", "latest"]}, {"name": "blockHashes", "description": "Block runtime IDs are generated from a hash function", "versions": ["1.19.80", "latest"]}, {"name": "itemRegistryPacket", "description": "The item registry is sent in a dedicated packet as opposed to StartGame", "versions": ["1.21.60", "latest"]}]