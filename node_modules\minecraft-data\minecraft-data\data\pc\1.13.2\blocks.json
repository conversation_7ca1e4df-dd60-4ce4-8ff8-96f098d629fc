[{"id": 0, "displayName": "Air", "name": "air", "hardness": 0, "minStateId": 0, "maxStateId": 0, "states": [], "drops": [0], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 0, "defaultState": 0, "resistance": 0}, {"id": 1, "displayName": "Stone", "name": "stone", "hardness": 1.5, "minStateId": 1, "maxStateId": 1, "states": [], "drops": [1], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 1, "resistance": 6}, {"id": 2, "displayName": "Granite", "name": "granite", "hardness": 1.5, "minStateId": 2, "maxStateId": 2, "states": [], "drops": [2], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 2, "resistance": 6}, {"id": 3, "displayName": "Polished Granite", "name": "polished_granite", "hardness": 1.5, "minStateId": 3, "maxStateId": 3, "states": [], "drops": [3], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 3, "resistance": 6}, {"id": 4, "displayName": "Diorite", "name": "diorite", "hardness": 1.5, "minStateId": 4, "maxStateId": 4, "states": [], "drops": [4], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 4, "resistance": 6}, {"id": 5, "displayName": "Polished Diorite", "name": "polished_diorite", "hardness": 1.5, "minStateId": 5, "maxStateId": 5, "states": [], "drops": [5], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5, "resistance": 6}, {"id": 6, "displayName": "Andesite", "name": "andesite", "hardness": 1.5, "minStateId": 6, "maxStateId": 6, "states": [], "drops": [6], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 6, "resistance": 6}, {"id": 7, "displayName": "Polished Andesite", "name": "polished_andesite", "hardness": 1.5, "minStateId": 7, "maxStateId": 7, "states": [], "drops": [7], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7, "resistance": 6}, {"id": 8, "displayName": "Grass Block", "name": "grass_block", "hardness": 0.6, "minStateId": 8, "maxStateId": 9, "states": [{"name": "snowy", "type": "bool", "num_values": 2}], "drops": [8], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 9, "resistance": 0.6}, {"id": 9, "displayName": "Dirt", "name": "dirt", "hardness": 0.5, "minStateId": 10, "maxStateId": 10, "states": [], "drops": [9], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 10, "resistance": 0.5}, {"id": 10, "displayName": "Coarse Dirt", "name": "coarse_dirt", "hardness": 0.5, "minStateId": 11, "maxStateId": 11, "states": [], "drops": [10], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 11, "resistance": 0.5}, {"id": 11, "displayName": "Podzol", "name": "podzol", "hardness": 0.5, "minStateId": 12, "maxStateId": 13, "states": [{"name": "snowy", "type": "bool", "num_values": 2}], "drops": [11], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 13, "resistance": 0.5}, {"id": 12, "displayName": "Cobblestone", "name": "cobblestone", "hardness": 2, "minStateId": 14, "maxStateId": 14, "states": [], "drops": [12], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 14, "resistance": 6}, {"id": 13, "displayName": "Oak Planks", "name": "oak_planks", "hardness": 2, "minStateId": 15, "maxStateId": 15, "states": [], "drops": [13], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 15, "resistance": 3}, {"id": 14, "displayName": "Spruce Planks", "name": "spruce_planks", "hardness": 2, "minStateId": 16, "maxStateId": 16, "states": [], "drops": [14], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 16, "resistance": 3}, {"id": 15, "displayName": "Birch Planks", "name": "birch_planks", "hardness": 2, "minStateId": 17, "maxStateId": 17, "states": [], "drops": [15], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 17, "resistance": 3}, {"id": 16, "displayName": "Jungle Planks", "name": "jungle_planks", "hardness": 2, "minStateId": 18, "maxStateId": 18, "states": [], "drops": [16], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 18, "resistance": 3}, {"id": 17, "displayName": "Acacia Planks", "name": "acacia_planks", "hardness": 2, "minStateId": 19, "maxStateId": 19, "states": [], "drops": [17], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 19, "resistance": 3}, {"id": 18, "displayName": "Dark Oak Planks", "name": "dark_oak_planks", "hardness": 2, "minStateId": 20, "maxStateId": 20, "states": [], "drops": [18], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 20, "resistance": 3}, {"id": 19, "displayName": "Oak Sapling", "name": "oak_sapling", "hardness": 0, "minStateId": 21, "maxStateId": 22, "states": [{"name": "stage", "type": "int", "num_values": 2}], "drops": [19], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 21, "resistance": 0}, {"id": 20, "displayName": "Spruce Sapling", "name": "spruce_sapling", "hardness": 0, "minStateId": 23, "maxStateId": 24, "states": [{"name": "stage", "type": "int", "num_values": 2}], "drops": [20], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 23, "resistance": 0}, {"id": 21, "displayName": "Birch Sapling", "name": "birch_sapling", "hardness": 0, "minStateId": 25, "maxStateId": 26, "states": [{"name": "stage", "type": "int", "num_values": 2}], "drops": [21], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 25, "resistance": 0}, {"id": 22, "displayName": "Jungle Sapling", "name": "jungle_sapling", "hardness": 0, "minStateId": 27, "maxStateId": 28, "states": [{"name": "stage", "type": "int", "num_values": 2}], "drops": [22], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 27, "resistance": 0}, {"id": 23, "displayName": "Acacia Sapling", "name": "acacia_sapling", "hardness": 0, "minStateId": 29, "maxStateId": 30, "states": [{"name": "stage", "type": "int", "num_values": 2}], "drops": [23], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 29, "resistance": 0}, {"id": 24, "displayName": "Dark Oak Sapling", "name": "dark_oak_sapling", "hardness": 0, "minStateId": 31, "maxStateId": 32, "states": [{"name": "stage", "type": "int", "num_values": 2}], "drops": [24], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 31, "resistance": 0}, {"id": 25, "displayName": "Bedrock", "name": "bedrock", "hardness": null, "minStateId": 33, "maxStateId": 33, "states": [], "drops": [25], "diggable": false, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 33, "resistance": 3600000}, {"id": 26, "displayName": "Water", "name": "water", "hardness": 100, "minStateId": 34, "maxStateId": 49, "states": [{"name": "level", "type": "int", "num_values": 16}], "drops": [], "diggable": false, "transparent": true, "filterLight": 2, "emitLight": 0, "boundingBox": "empty", "stackSize": 0, "defaultState": 34, "resistance": 100}, {"id": 27, "displayName": "<PERSON><PERSON>", "name": "lava", "hardness": 100, "minStateId": 50, "maxStateId": 65, "states": [{"name": "level", "type": "int", "num_values": 16}], "drops": [], "diggable": false, "transparent": true, "filterLight": 0, "emitLight": 15, "boundingBox": "empty", "stackSize": 0, "defaultState": 50, "resistance": 100}, {"id": 28, "displayName": "Sand", "name": "sand", "hardness": 0.5, "minStateId": 66, "maxStateId": 66, "states": [], "drops": [26], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 0, "material": "dirt", "defaultState": 66, "resistance": 0.5}, {"id": 29, "displayName": "Red Sand", "name": "red_sand", "hardness": 0.5, "minStateId": 67, "maxStateId": 67, "states": [], "drops": [27], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 0, "material": "dirt", "defaultState": 67, "resistance": 0.5}, {"id": 30, "displayName": "<PERSON>l", "name": "gravel", "hardness": 0.6, "minStateId": 68, "maxStateId": 68, "states": [], "drops": [28], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 68, "resistance": 0.6}, {"id": 31, "displayName": "Gold Ore", "name": "gold_ore", "hardness": 3, "minStateId": 69, "maxStateId": 69, "states": [], "drops": [29], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "495": true}, "defaultState": 69, "resistance": 3}, {"id": 32, "displayName": "Iron Ore", "name": "iron_ore", "hardness": 3, "minStateId": 70, "maxStateId": 70, "states": [], "drops": [30], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "491": true, "495": true}, "defaultState": 70, "resistance": 3}, {"id": 33, "displayName": "Coal Ore", "name": "coal_ore", "hardness": 3, "minStateId": 71, "maxStateId": 71, "states": [], "drops": [31], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 71, "resistance": 3}, {"id": 34, "displayName": "Oak Log", "name": "oak_log", "hardness": 2, "minStateId": 72, "maxStateId": 74, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [32], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 73, "resistance": 2}, {"id": 35, "displayName": "Spruce Log", "name": "spruce_log", "hardness": 2, "minStateId": 75, "maxStateId": 77, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [33], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 76, "resistance": 2}, {"id": 36, "displayName": "Birch Log", "name": "birch_log", "hardness": 2, "minStateId": 78, "maxStateId": 80, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [34], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 79, "resistance": 2}, {"id": 37, "displayName": "Jungle Log", "name": "jungle_log", "hardness": 2, "minStateId": 81, "maxStateId": 83, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [35], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 82, "resistance": 2}, {"id": 38, "displayName": "Acacia Log", "name": "acacia_log", "hardness": 2, "minStateId": 84, "maxStateId": 86, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [36], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 85, "resistance": 2}, {"id": 39, "displayName": "Dark Oak Log", "name": "dark_oak_log", "hardness": 2, "minStateId": 87, "maxStateId": 89, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [37], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 88, "resistance": 2}, {"id": 40, "displayName": "Stripped Spruce Log", "name": "stripped_spruce_log", "hardness": 2, "minStateId": 90, "maxStateId": 92, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [39], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 91, "resistance": 2}, {"id": 41, "displayName": "Stripped Birch Log", "name": "stripped_birch_log", "hardness": 2, "minStateId": 93, "maxStateId": 95, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [40], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 94, "resistance": 2}, {"id": 42, "displayName": "Stripped Jungle Log", "name": "stripped_jungle_log", "hardness": 2, "minStateId": 96, "maxStateId": 98, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [41], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 97, "resistance": 2}, {"id": 43, "displayName": "Stripped Acacia Log", "name": "stripped_acacia_log", "hardness": 2, "minStateId": 99, "maxStateId": 101, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [42], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 100, "resistance": 2}, {"id": 44, "displayName": "Stripped Dark Oak Log", "name": "stripped_dark_oak_log", "hardness": 2, "minStateId": 102, "maxStateId": 104, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [43], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 103, "resistance": 2}, {"id": 45, "displayName": "Stripped Oak Log", "name": "stripped_oak_log", "hardness": 2, "minStateId": 105, "maxStateId": 107, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [38], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 106, "resistance": 2}, {"id": 46, "displayName": "Oak Wood", "name": "oak_wood", "hardness": 2, "minStateId": 108, "maxStateId": 110, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [50], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 109, "resistance": 2}, {"id": 47, "displayName": "Spruce Wood", "name": "spruce_wood", "hardness": 2, "minStateId": 111, "maxStateId": 113, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [51], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 112, "resistance": 2}, {"id": 48, "displayName": "Birch Wood", "name": "birch_wood", "hardness": 2, "minStateId": 114, "maxStateId": 116, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [52], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 115, "resistance": 2}, {"id": 49, "displayName": "Jungle Wood", "name": "jungle_wood", "hardness": 2, "minStateId": 117, "maxStateId": 119, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [53], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 118, "resistance": 2}, {"id": 50, "displayName": "Acacia Wood", "name": "acacia_wood", "hardness": 2, "minStateId": 120, "maxStateId": 122, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [54], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 121, "resistance": 2}, {"id": 51, "displayName": "Dark Oak Wood", "name": "dark_oak_wood", "hardness": 2, "minStateId": 123, "maxStateId": 125, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [55], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 124, "resistance": 2}, {"id": 52, "displayName": "Stripped Oak Wood", "name": "stripped_oak_wood", "hardness": 2, "minStateId": 126, "maxStateId": 128, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [44], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 127, "resistance": 2}, {"id": 53, "displayName": "Stripped Spruce Wood", "name": "stripped_spruce_wood", "hardness": 2, "minStateId": 129, "maxStateId": 131, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [45], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 130, "resistance": 2}, {"id": 54, "displayName": "Stripped Birch Wood", "name": "stripped_birch_wood", "hardness": 2, "minStateId": 132, "maxStateId": 134, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [46], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 133, "resistance": 2}, {"id": 55, "displayName": "Stripped Jungle Wood", "name": "stripped_jungle_wood", "hardness": 2, "minStateId": 135, "maxStateId": 137, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [47], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 136, "resistance": 2}, {"id": 56, "displayName": "Stripped Acacia Wood", "name": "stripped_acacia_wood", "hardness": 2, "minStateId": 138, "maxStateId": 140, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [48], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 139, "resistance": 2}, {"id": 57, "displayName": "Stripped Dark Oak Wood", "name": "stripped_dark_oak_wood", "hardness": 2, "minStateId": 141, "maxStateId": 143, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [49], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 142, "resistance": 2}, {"id": 58, "displayName": "Oak Leaves", "name": "oak_leaves", "hardness": 0.2, "minStateId": 144, "maxStateId": 157, "states": [{"name": "distance", "type": "enum", "num_values": 7, "values": ["1", "2", "3", "4", "5", "6", "7"]}, {"name": "persistent", "type": "bool", "num_values": 2}], "drops": [56], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 157, "resistance": 0.2}, {"id": 59, "displayName": "Spruce Leaves", "name": "spruce_leaves", "hardness": 0.2, "minStateId": 158, "maxStateId": 171, "states": [{"name": "distance", "type": "enum", "num_values": 7, "values": ["1", "2", "3", "4", "5", "6", "7"]}, {"name": "persistent", "type": "bool", "num_values": 2}], "drops": [57], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 171, "resistance": 0.2}, {"id": 60, "displayName": "Birch Leaves", "name": "birch_leaves", "hardness": 0.2, "minStateId": 172, "maxStateId": 185, "states": [{"name": "distance", "type": "enum", "num_values": 7, "values": ["1", "2", "3", "4", "5", "6", "7"]}, {"name": "persistent", "type": "bool", "num_values": 2}], "drops": [58], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 185, "resistance": 0.2}, {"id": 61, "displayName": "Jungle Leaves", "name": "jungle_leaves", "hardness": 0.2, "minStateId": 186, "maxStateId": 199, "states": [{"name": "distance", "type": "enum", "num_values": 7, "values": ["1", "2", "3", "4", "5", "6", "7"]}, {"name": "persistent", "type": "bool", "num_values": 2}], "drops": [59], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 199, "resistance": 0.2}, {"id": 62, "displayName": "Acacia Leaves", "name": "acacia_leaves", "hardness": 0.2, "minStateId": 200, "maxStateId": 213, "states": [{"name": "distance", "type": "enum", "num_values": 7, "values": ["1", "2", "3", "4", "5", "6", "7"]}, {"name": "persistent", "type": "bool", "num_values": 2}], "drops": [60], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 213, "resistance": 0.2}, {"id": 63, "displayName": "Dark Oak Leaves", "name": "dark_oak_leaves", "hardness": 0.2, "minStateId": 214, "maxStateId": 227, "states": [{"name": "distance", "type": "enum", "num_values": 7, "values": ["1", "2", "3", "4", "5", "6", "7"]}, {"name": "persistent", "type": "bool", "num_values": 2}], "drops": [61], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 227, "resistance": 0.2}, {"id": 64, "displayName": "Sponge", "name": "sponge", "hardness": 0.6, "minStateId": 228, "maxStateId": 228, "states": [], "drops": [62], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 228, "resistance": 0.6}, {"id": 65, "displayName": "Wet Sponge", "name": "wet_sponge", "hardness": 0.6, "minStateId": 229, "maxStateId": 229, "states": [], "drops": [63], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 229, "resistance": 0.6}, {"id": 66, "displayName": "Glass", "name": "glass", "hardness": 0.3, "minStateId": 230, "maxStateId": 230, "states": [], "drops": [64], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 230, "resistance": 0.3}, {"id": 67, "displayName": "Lapis <PERSON> Ore", "name": "lapis_ore", "hardness": 3, "minStateId": 231, "maxStateId": 231, "states": [], "drops": [65], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "491": true, "495": true}, "defaultState": 231, "resistance": 3}, {"id": 68, "displayName": "Lapis <PERSON>", "name": "lapis_block", "hardness": 3, "minStateId": 232, "maxStateId": 232, "states": [], "drops": [66], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "491": true, "495": true}, "defaultState": 232, "resistance": 3}, {"id": 69, "displayName": "Dispenser", "name": "dispenser", "hardness": 3.5, "minStateId": 233, "maxStateId": 244, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}, {"name": "triggered", "type": "bool", "num_values": 2}], "drops": [67], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 234, "resistance": 3.5}, {"id": 70, "displayName": "Sandstone", "name": "sandstone", "hardness": 0.8, "minStateId": 245, "maxStateId": 245, "states": [], "drops": [68], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 245, "resistance": 0.8}, {"id": 71, "displayName": "Chiseled Sandstone", "name": "chiseled_sandstone", "hardness": 0.8, "minStateId": 246, "maxStateId": 246, "states": [], "drops": [69], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 246, "resistance": 0.8}, {"id": 72, "displayName": "Cut Sandstone", "name": "cut_sandstone", "hardness": 0.8, "minStateId": 247, "maxStateId": 247, "states": [], "drops": [70], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 247, "resistance": 0.8}, {"id": 73, "displayName": "Note Block", "name": "note_block", "hardness": 0.8, "minStateId": 248, "maxStateId": 747, "states": [{"name": "instrument", "type": "enum", "num_values": 10, "values": ["harp", "basedrum", "snare", "hat", "bass", "flute", "bell", "guitar", "chime", "xylophone"]}, {"name": "note", "type": "int", "num_values": 25}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [71], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 249, "resistance": 0.8}, {"id": 74, "displayName": "White Bed", "name": "white_bed", "hardness": 0.2, "minStateId": 748, "maxStateId": 763, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [596], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 751, "resistance": 0.2}, {"id": 75, "displayName": "Orange Bed", "name": "orange_bed", "hardness": 0.2, "minStateId": 764, "maxStateId": 779, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [597], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 767, "resistance": 0.2}, {"id": 76, "displayName": "Magenta Bed", "name": "magenta_bed", "hardness": 0.2, "minStateId": 780, "maxStateId": 795, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [598], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 783, "resistance": 0.2}, {"id": 77, "displayName": "Light Blue Bed", "name": "light_blue_bed", "hardness": 0.2, "minStateId": 796, "maxStateId": 811, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [599], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 799, "resistance": 0.2}, {"id": 78, "displayName": "Yellow Bed", "name": "yellow_bed", "hardness": 0.2, "minStateId": 812, "maxStateId": 827, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [600], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 815, "resistance": 0.2}, {"id": 79, "displayName": "Lime Bed", "name": "lime_bed", "hardness": 0.2, "minStateId": 828, "maxStateId": 843, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [601], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 831, "resistance": 0.2}, {"id": 80, "displayName": "Pink Bed", "name": "pink_bed", "hardness": 0.2, "minStateId": 844, "maxStateId": 859, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [602], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 847, "resistance": 0.2}, {"id": 81, "displayName": "Gray Bed", "name": "gray_bed", "hardness": 0.2, "minStateId": 860, "maxStateId": 875, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [603], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 863, "resistance": 0.2}, {"id": 82, "displayName": "Light Gray Bed", "name": "light_gray_bed", "hardness": 0.2, "minStateId": 876, "maxStateId": 891, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [604], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 879, "resistance": 0.2}, {"id": 83, "displayName": "<PERSON><PERSON>", "name": "cyan_bed", "hardness": 0.2, "minStateId": 892, "maxStateId": 907, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [605], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 895, "resistance": 0.2}, {"id": 84, "displayName": "Purple Bed", "name": "purple_bed", "hardness": 0.2, "minStateId": 908, "maxStateId": 923, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [606], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 911, "resistance": 0.2}, {"id": 85, "displayName": "Blue Bed", "name": "blue_bed", "hardness": 0.2, "minStateId": 924, "maxStateId": 939, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [607], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 927, "resistance": 0.2}, {"id": 86, "displayName": "Brown Bed", "name": "brown_bed", "hardness": 0.2, "minStateId": 940, "maxStateId": 955, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [608], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 943, "resistance": 0.2}, {"id": 87, "displayName": "Green Bed", "name": "green_bed", "hardness": 0.2, "minStateId": 956, "maxStateId": 971, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [609], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 959, "resistance": 0.2}, {"id": 88, "displayName": "Red Bed", "name": "red_bed", "hardness": 0.2, "minStateId": 972, "maxStateId": 987, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [610], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 975, "resistance": 0.2}, {"id": 89, "displayName": "Black Bed", "name": "black_bed", "hardness": 0.2, "minStateId": 988, "maxStateId": 1003, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [611], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 991, "resistance": 0.2}, {"id": 90, "displayName": "Powered Rail", "name": "powered_rail", "hardness": 0.7, "minStateId": 1004, "maxStateId": 1015, "states": [{"name": "powered", "type": "bool", "num_values": 2}, {"name": "shape", "type": "enum", "num_values": 6, "values": ["north_south", "east_west", "ascending_east", "ascending_west", "ascending_north", "ascending_south"]}], "drops": [72], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "rock", "defaultState": 1010, "resistance": 0.7}, {"id": 91, "displayName": "Detector Rail", "name": "detector_rail", "hardness": 0.7, "minStateId": 1016, "maxStateId": 1027, "states": [{"name": "powered", "type": "bool", "num_values": 2}, {"name": "shape", "type": "enum", "num_values": 6, "values": ["north_south", "east_west", "ascending_east", "ascending_west", "ascending_north", "ascending_south"]}], "drops": [73], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "rock", "defaultState": 1022, "resistance": 0.7}, {"id": 92, "displayName": "<PERSON><PERSON>", "name": "sticky_piston", "hardness": 0.5, "minStateId": 1028, "maxStateId": 1039, "states": [{"name": "extended", "type": "bool", "num_values": 2}, {"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [74], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 1034, "resistance": 0.5}, {"id": 93, "displayName": "Cobweb", "name": "cobweb", "hardness": 4, "minStateId": 1040, "maxStateId": 1040, "states": [], "drops": [75], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "web", "harvestTools": {"484": true, "485": true, "489": true, "493": true, "500": true, "614": true}, "defaultState": 1040, "resistance": 4}, {"id": 94, "displayName": "Grass", "name": "grass", "hardness": 0, "minStateId": 1041, "maxStateId": 1041, "states": [], "drops": [76], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1041, "resistance": 0}, {"id": 95, "displayName": "Fern", "name": "fern", "hardness": 0, "minStateId": 1042, "maxStateId": 1042, "states": [], "drops": [77], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1042, "resistance": 0}, {"id": 96, "displayName": "Dead Bush", "name": "dead_bush", "hardness": 0, "minStateId": 1043, "maxStateId": 1043, "states": [], "drops": [78], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1043, "resistance": 0}, {"id": 97, "displayName": "Seagrass", "name": "seagrass", "hardness": 0, "minStateId": 1044, "maxStateId": 1044, "states": [], "drops": [79], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1044, "resistance": 0}, {"id": 98, "displayName": "Tall Seagrass", "name": "tall_seagrass", "hardness": 0, "minStateId": 1045, "maxStateId": 1046, "states": [{"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}], "drops": [79], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1046, "resistance": 0}, {"id": 99, "displayName": "<PERSON><PERSON>", "name": "piston", "hardness": 0.5, "minStateId": 1047, "maxStateId": 1058, "states": [{"name": "extended", "type": "bool", "num_values": 2}, {"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [81], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 1053, "resistance": 0.5}, {"id": 100, "displayName": "Piston Head", "name": "piston_head", "hardness": 0.5, "minStateId": 1059, "maxStateId": 1082, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}, {"name": "short", "type": "bool", "num_values": 2}, {"name": "type", "type": "enum", "num_values": 2, "values": ["normal", "sticky"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 1061, "resistance": 0.5}, {"id": 101, "displayName": "White Wool", "name": "white_wool", "hardness": 0.8, "minStateId": 1083, "maxStateId": 1083, "states": [], "drops": [82], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1083, "resistance": 0.8}, {"id": 102, "displayName": "Orange Wool", "name": "orange_wool", "hardness": 0.8, "minStateId": 1084, "maxStateId": 1084, "states": [], "drops": [83], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1084, "resistance": 0.8}, {"id": 103, "displayName": "Magenta Wool", "name": "magenta_wool", "hardness": 0.8, "minStateId": 1085, "maxStateId": 1085, "states": [], "drops": [84], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1085, "resistance": 0.8}, {"id": 104, "displayName": "Light Blue Wool", "name": "light_blue_wool", "hardness": 0.8, "minStateId": 1086, "maxStateId": 1086, "states": [], "drops": [85], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1086, "resistance": 0.8}, {"id": 105, "displayName": "Yellow Wool", "name": "yellow_wool", "hardness": 0.8, "minStateId": 1087, "maxStateId": 1087, "states": [], "drops": [86], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1087, "resistance": 0.8}, {"id": 106, "displayName": "Lime Wool", "name": "lime_wool", "hardness": 0.8, "minStateId": 1088, "maxStateId": 1088, "states": [], "drops": [87], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1088, "resistance": 0.8}, {"id": 107, "displayName": "Pink Wool", "name": "pink_wool", "hardness": 0.8, "minStateId": 1089, "maxStateId": 1089, "states": [], "drops": [88], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1089, "resistance": 0.8}, {"id": 108, "displayName": "Gray <PERSON>", "name": "gray_wool", "hardness": 0.8, "minStateId": 1090, "maxStateId": 1090, "states": [], "drops": [89], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1090, "resistance": 0.8}, {"id": 109, "displayName": "Light Gray Wool", "name": "light_gray_wool", "hardness": 0.8, "minStateId": 1091, "maxStateId": 1091, "states": [], "drops": [90], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1091, "resistance": 0.8}, {"id": 110, "displayName": "<PERSON><PERSON>", "name": "cyan_wool", "hardness": 0.8, "minStateId": 1092, "maxStateId": 1092, "states": [], "drops": [91], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1092, "resistance": 0.8}, {"id": 111, "displayName": "Purple Wool", "name": "purple_wool", "hardness": 0.8, "minStateId": 1093, "maxStateId": 1093, "states": [], "drops": [92], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1093, "resistance": 0.8}, {"id": 112, "displayName": "Blue Wool", "name": "blue_wool", "hardness": 0.8, "minStateId": 1094, "maxStateId": 1094, "states": [], "drops": [93], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1094, "resistance": 0.8}, {"id": 113, "displayName": "Brown Wool", "name": "brown_wool", "hardness": 0.8, "minStateId": 1095, "maxStateId": 1095, "states": [], "drops": [94], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1095, "resistance": 0.8}, {"id": 114, "displayName": "Green Wool", "name": "green_wool", "hardness": 0.8, "minStateId": 1096, "maxStateId": 1096, "states": [], "drops": [95], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1096, "resistance": 0.8}, {"id": 115, "displayName": "Red Wool", "name": "red_wool", "hardness": 0.8, "minStateId": 1097, "maxStateId": 1097, "states": [], "drops": [96], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1097, "resistance": 0.8}, {"id": 116, "displayName": "Black Wool", "name": "black_wool", "hardness": 0.8, "minStateId": 1098, "maxStateId": 1098, "states": [], "drops": [97], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1098, "resistance": 0.8}, {"id": 117, "displayName": "Moving <PERSON>ston", "name": "moving_piston", "hardness": null, "minStateId": 1099, "maxStateId": 1110, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}, {"name": "type", "type": "enum", "num_values": 2, "values": ["normal", "sticky"]}], "drops": [], "diggable": false, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 1099, "resistance": -1}, {"id": 118, "displayName": "Dandelion", "name": "dandelion", "hardness": 0, "minStateId": 1111, "maxStateId": 1111, "states": [], "drops": [98], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 1111, "resistance": 0}, {"id": 119, "displayName": "<PERSON><PERSON>", "name": "poppy", "hardness": 0, "minStateId": 1112, "maxStateId": 1112, "states": [], "drops": [99], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 1112, "resistance": 0}, {"id": 120, "displayName": "Blue Orchid", "name": "blue_orchid", "hardness": 0, "minStateId": 1113, "maxStateId": 1113, "states": [], "drops": [100], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1113, "resistance": 0}, {"id": 121, "displayName": "Allium", "name": "allium", "hardness": 0, "minStateId": 1114, "maxStateId": 1114, "states": [], "drops": [101], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 1114, "resistance": 0}, {"id": 122, "displayName": "Azure Bluet", "name": "azure_bluet", "hardness": 0, "minStateId": 1115, "maxStateId": 1115, "states": [], "drops": [102], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 1115, "resistance": 0}, {"id": 123, "displayName": "<PERSON>lip", "name": "red_tulip", "hardness": 0, "minStateId": 1116, "maxStateId": 1116, "states": [], "drops": [103], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1116, "resistance": 0}, {"id": 124, "displayName": "Orange Tulip", "name": "orange_tulip", "hardness": 0, "minStateId": 1117, "maxStateId": 1117, "states": [], "drops": [104], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1117, "resistance": 0}, {"id": 125, "displayName": "White Tulip", "name": "white_tulip", "hardness": 0, "minStateId": 1118, "maxStateId": 1118, "states": [], "drops": [105], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1118, "resistance": 0}, {"id": 126, "displayName": "<PERSON> Tulip", "name": "pink_tulip", "hardness": 0, "minStateId": 1119, "maxStateId": 1119, "states": [], "drops": [106], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1119, "resistance": 0}, {"id": 127, "displayName": "Oxeye Daisy", "name": "oxeye_daisy", "hardness": 0, "minStateId": 1120, "maxStateId": 1120, "states": [], "drops": [107], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 1120, "resistance": 0}, {"id": 128, "displayName": "Brown Mushroom", "name": "brown_mushroom", "hardness": 0, "minStateId": 1121, "maxStateId": 1121, "states": [], "drops": [108], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 1, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1121, "resistance": 0}, {"id": 129, "displayName": "Red Mushroom", "name": "red_mushroom", "hardness": 0, "minStateId": 1122, "maxStateId": 1122, "states": [], "drops": [109], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 1, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1122, "resistance": 0}, {"id": 130, "displayName": "Block of Gold", "name": "gold_block", "hardness": 3, "minStateId": 1123, "maxStateId": 1123, "states": [], "drops": [110], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "495": true}, "defaultState": 1123, "resistance": 6}, {"id": 131, "displayName": "Block of Iron", "name": "iron_block", "hardness": 5, "minStateId": 1124, "maxStateId": 1124, "states": [], "drops": [111], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "491": true, "495": true}, "defaultState": 1124, "resistance": 6}, {"id": 132, "displayName": "Bricks", "name": "bricks", "hardness": 2, "minStateId": 1125, "maxStateId": 1125, "states": [], "drops": [135], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 1125, "resistance": 6}, {"id": 133, "displayName": "TNT", "name": "tnt", "hardness": 0, "minStateId": 1126, "maxStateId": 1127, "states": [{"name": "unstable", "type": "bool", "num_values": 2}], "drops": [136], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 1127, "resistance": 0}, {"id": 134, "displayName": "Bookshelf", "name": "bookshelf", "hardness": 1.5, "minStateId": 1128, "maxStateId": 1128, "states": [], "drops": [137], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 1128, "resistance": 1.5}, {"id": 135, "displayName": "<PERSON><PERSON>", "name": "mossy_cobblestone", "hardness": 2, "minStateId": 1129, "maxStateId": 1129, "states": [], "drops": [138], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 1129, "resistance": 6}, {"id": 136, "displayName": "Obsidian", "name": "obsidian", "hardness": 50, "minStateId": 1130, "maxStateId": 1130, "states": [], "drops": [139], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"495": true}, "defaultState": 1130, "resistance": 1200}, {"id": 137, "displayName": "<PERSON>ch", "name": "torch", "hardness": 0, "minStateId": 1131, "maxStateId": 1131, "states": [], "drops": [140], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 14, "boundingBox": "empty", "stackSize": 64, "defaultState": 1131, "resistance": 0}, {"id": 138, "displayName": "<PERSON>", "name": "wall_torch", "hardness": 0, "minStateId": 1132, "maxStateId": 1135, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [140], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 14, "boundingBox": "empty", "stackSize": 64, "defaultState": 1132, "resistance": 0}, {"id": 139, "displayName": "Fire", "name": "fire", "hardness": 0, "minStateId": 1136, "maxStateId": 1647, "states": [{"name": "age", "type": "int", "num_values": 16}, {"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 15, "boundingBox": "empty", "stackSize": 0, "defaultState": 1167, "resistance": 0}, {"id": 140, "displayName": "Spawner", "name": "spawner", "hardness": 5, "minStateId": 1648, "maxStateId": 1648, "states": [], "drops": [147], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 1648, "resistance": 5}, {"id": 141, "displayName": "Oak Stairs", "name": "oak_stairs", "hardness": 2, "minStateId": 1649, "maxStateId": 1728, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [148], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 1660, "resistance": 3}, {"id": 142, "displayName": "Chest", "name": "chest", "hardness": 2.5, "minStateId": 1729, "maxStateId": 1752, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "type", "type": "enum", "num_values": 3, "values": ["single", "left", "right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [149], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 1730, "resistance": 2.5}, {"id": 143, "displayName": "Redstone Dust", "name": "redstone_wire", "hardness": 0, "minStateId": 1753, "maxStateId": 3048, "states": [{"name": "east", "type": "enum", "num_values": 3, "values": ["up", "side", "none"]}, {"name": "north", "type": "enum", "num_values": 3, "values": ["up", "side", "none"]}, {"name": "power", "type": "int", "num_values": 16}, {"name": "south", "type": "enum", "num_values": 3, "values": ["up", "side", "none"]}, {"name": "west", "type": "enum", "num_values": 3, "values": ["up", "side", "none"]}], "drops": [547], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 2913, "resistance": 0}, {"id": 144, "displayName": "Diamond Ore", "name": "diamond_ore", "hardness": 3, "minStateId": 3049, "maxStateId": 3049, "states": [], "drops": [150], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "495": true}, "defaultState": 3049, "resistance": 3}, {"id": 145, "displayName": "Block of Diamond", "name": "diamond_block", "hardness": 5, "minStateId": 3050, "maxStateId": 3050, "states": [], "drops": [151], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "495": true}, "defaultState": 3050, "resistance": 6}, {"id": 146, "displayName": "Crafting Table", "name": "crafting_table", "hardness": 2.5, "minStateId": 3051, "maxStateId": 3051, "states": [], "drops": [152], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 3051, "resistance": 2.5}, {"id": 147, "displayName": "Wheat Crops", "name": "wheat", "hardness": 0, "minStateId": 3052, "maxStateId": 3059, "states": [{"name": "age", "type": "int", "num_values": 8}], "drops": [513], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 3052, "resistance": 0}, {"id": 148, "displayName": "Farmland", "name": "farmland", "hardness": 0.6, "minStateId": 3060, "maxStateId": 3067, "states": [{"name": "moisture", "type": "int", "num_values": 8}], "drops": [153], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 3060, "resistance": 0.6}, {"id": 149, "displayName": "Furnace", "name": "furnace", "hardness": 3.5, "minStateId": 3068, "maxStateId": 3075, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "lit", "type": "bool", "num_values": 2}], "drops": [154], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 13, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 3069, "resistance": 3.5}, {"id": 150, "displayName": "Sign", "name": "sign", "hardness": 1, "minStateId": 3076, "maxStateId": 3107, "states": [{"name": "rotation", "type": "int", "num_values": 16}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [541], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 3077, "resistance": 1}, {"id": 151, "displayName": "Oak Door", "name": "oak_door", "hardness": 3, "minStateId": 3108, "maxStateId": 3171, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}, {"name": "hinge", "type": "enum", "num_values": 2, "values": ["left", "right"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [461], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 3119, "resistance": 3}, {"id": 152, "displayName": "Ladder", "name": "ladder", "hardness": 0.4, "minStateId": 3172, "maxStateId": 3179, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [155], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 3173, "resistance": 0.4}, {"id": 153, "displayName": "Rail", "name": "rail", "hardness": 0.7, "minStateId": 3180, "maxStateId": 3189, "states": [{"name": "shape", "type": "enum", "num_values": 10, "values": ["north_south", "east_west", "ascending_east", "ascending_west", "ascending_north", "ascending_south", "south_east", "south_west", "north_west", "north_east"]}], "drops": [156], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "rock", "defaultState": 3180, "resistance": 0.7}, {"id": 154, "displayName": "Cobblestone Stairs", "name": "cobblestone_stairs", "hardness": 2, "minStateId": 3190, "maxStateId": 3269, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [157], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 3201, "resistance": 6}, {"id": 155, "displayName": "Wall Sign", "name": "wall_sign", "hardness": 1, "minStateId": 3270, "maxStateId": 3277, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [541], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 3271, "resistance": 1}, {"id": 156, "displayName": "Lever", "name": "lever", "hardness": 0.5, "minStateId": 3278, "maxStateId": 3301, "states": [{"name": "face", "type": "enum", "num_values": 3, "values": ["floor", "wall", "ceiling"]}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [158], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 3287, "resistance": 0.5}, {"id": 157, "displayName": "Stone Pressure Plate", "name": "stone_pressure_plate", "hardness": 0.5, "minStateId": 3302, "maxStateId": 3303, "states": [{"name": "powered", "type": "bool", "num_values": 2}], "drops": [159], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 3303, "resistance": 0.5}, {"id": 158, "displayName": "Iron Door", "name": "iron_door", "hardness": 5, "minStateId": 3304, "maxStateId": 3367, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}, {"name": "hinge", "type": "enum", "num_values": 2, "values": ["left", "right"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [460], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 3315, "resistance": 5}, {"id": 159, "displayName": "Oak Pressure Plate", "name": "oak_pressure_plate", "hardness": 0.5, "minStateId": 3368, "maxStateId": 3369, "states": [{"name": "powered", "type": "bool", "num_values": 2}], "drops": [160], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 3369, "resistance": 0.5}, {"id": 160, "displayName": "Spruce Pressure Plate", "name": "spruce_pressure_plate", "hardness": 0.5, "minStateId": 3370, "maxStateId": 3371, "states": [{"name": "powered", "type": "bool", "num_values": 2}], "drops": [161], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 3371, "resistance": 0.5}, {"id": 161, "displayName": "Birch Pressure Plate", "name": "birch_pressure_plate", "hardness": 0.5, "minStateId": 3372, "maxStateId": 3373, "states": [{"name": "powered", "type": "bool", "num_values": 2}], "drops": [162], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 3373, "resistance": 0.5}, {"id": 162, "displayName": "Jungle Pressure Plate", "name": "jungle_pressure_plate", "hardness": 0.5, "minStateId": 3374, "maxStateId": 3375, "states": [{"name": "powered", "type": "bool", "num_values": 2}], "drops": [163], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 3375, "resistance": 0.5}, {"id": 163, "displayName": "Acacia Pressure Plate", "name": "acacia_pressure_plate", "hardness": 0.5, "minStateId": 3376, "maxStateId": 3377, "states": [{"name": "powered", "type": "bool", "num_values": 2}], "drops": [164], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 3377, "resistance": 0.5}, {"id": 164, "displayName": "Dark Oak Pressure Plate", "name": "dark_oak_pressure_plate", "hardness": 0.5, "minStateId": 3378, "maxStateId": 3379, "states": [{"name": "powered", "type": "bool", "num_values": 2}], "drops": [165], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 3379, "resistance": 0.5}, {"id": 165, "displayName": "Redstone Ore", "name": "redstone_ore", "hardness": 3, "minStateId": 3380, "maxStateId": 3381, "states": [{"name": "lit", "type": "bool", "num_values": 2}], "drops": [166], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 9, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "495": true}, "defaultState": 3381, "resistance": 3}, {"id": 166, "displayName": "Redstone Torch", "name": "redstone_torch", "hardness": 0, "minStateId": 3382, "maxStateId": 3383, "states": [{"name": "lit", "type": "bool", "num_values": 2}], "drops": [167], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 7, "boundingBox": "empty", "stackSize": 64, "defaultState": 3382, "resistance": 0}, {"id": 167, "displayName": "Redstone Wall Torch", "name": "redstone_wall_torch", "hardness": 0, "minStateId": 3384, "maxStateId": 3391, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "lit", "type": "bool", "num_values": 2}], "drops": [167], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 7, "boundingBox": "empty", "stackSize": 64, "defaultState": 3384, "resistance": 0}, {"id": 168, "displayName": "<PERSON>", "name": "stone_button", "hardness": 0.5, "minStateId": 3392, "maxStateId": 3415, "states": [{"name": "face", "type": "enum", "num_values": 3, "values": ["floor", "wall", "ceiling"]}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [168], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "rock", "defaultState": 3401, "resistance": 0.5}, {"id": 169, "displayName": "Snow", "name": "snow", "hardness": 0.1, "minStateId": 3416, "maxStateId": 3423, "states": [{"name": "layers", "type": "enum", "num_values": 8, "values": ["1", "2", "3", "4", "5", "6", "7", "8"]}], "drops": [169], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "harvestTools": {"472": true, "486": true, "490": true, "494": true, "501": true}, "defaultState": 3416, "resistance": 0.1}, {"id": 170, "displayName": "Ice", "name": "ice", "hardness": 0.5, "minStateId": 3424, "maxStateId": 3424, "states": [], "drops": [170], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "defaultState": 3424, "resistance": 0.5}, {"id": 171, "displayName": "Snow Block", "name": "snow_block", "hardness": 0.2, "minStateId": 3425, "maxStateId": 3425, "states": [], "drops": [171], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "harvestTools": {"472": true, "486": true, "490": true, "494": true, "501": true}, "defaultState": 3425, "resistance": 0.2}, {"id": 172, "displayName": "Cactus", "name": "cactus", "hardness": 0.4, "minStateId": 3426, "maxStateId": 3441, "states": [{"name": "age", "type": "int", "num_values": 16}], "drops": [172], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 3426, "resistance": 0.4}, {"id": 173, "displayName": "<PERSON>", "name": "clay", "hardness": 0.6, "minStateId": 3442, "maxStateId": 3442, "states": [], "drops": [173], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 3442, "resistance": 0.6}, {"id": 174, "displayName": "Sugar Cane", "name": "sugar_cane", "hardness": 0, "minStateId": 3443, "maxStateId": 3458, "states": [{"name": "age", "type": "int", "num_values": 16}], "drops": [558], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 3443, "resistance": 0}, {"id": 175, "displayName": "Jukebox", "name": "jukebox", "hardness": 2, "minStateId": 3459, "maxStateId": 3460, "states": [{"name": "has_record", "type": "bool", "num_values": 2}], "drops": [174], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 3460, "resistance": 6}, {"id": 176, "displayName": "Oak Fence", "name": "oak_fence", "hardness": 2, "minStateId": 3461, "maxStateId": 3492, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [175], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 3492, "resistance": 3}, {"id": 177, "displayName": "<PERSON><PERSON><PERSON>", "name": "pumpkin", "hardness": 1, "minStateId": 3493, "maxStateId": 3493, "states": [], "drops": [181], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 3493, "resistance": 1}, {"id": 178, "displayName": "Netherrack", "name": "netherrack", "hardness": 0.4, "minStateId": 3494, "maxStateId": 3494, "states": [], "drops": [183], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 3494, "resistance": 0.4}, {"id": 179, "displayName": "Soul Sand", "name": "soul_sand", "hardness": 0.5, "minStateId": 3495, "maxStateId": 3495, "states": [], "drops": [184], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 3495, "resistance": 0.5}, {"id": 180, "displayName": "Glowstone", "name": "glowstone", "hardness": 0.3, "minStateId": 3496, "maxStateId": 3496, "states": [], "drops": [185], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 15, "boundingBox": "block", "stackSize": 64, "defaultState": 3496, "resistance": 0.3}, {"id": 181, "displayName": "Nether Portal", "name": "nether_portal", "hardness": null, "minStateId": 3497, "maxStateId": 3498, "states": [{"name": "axis", "type": "enum", "num_values": 2, "values": ["x", "z"]}], "drops": [], "diggable": false, "transparent": true, "filterLight": 0, "emitLight": 11, "boundingBox": "empty", "stackSize": 0, "defaultState": 3497, "resistance": -1}, {"id": 182, "displayName": "<PERSON><PERSON>", "name": "carved_pumpkin", "hardness": 1, "minStateId": 3499, "maxStateId": 3502, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [182], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 3499, "resistance": 1}, {"id": 183, "displayName": "<PERSON>'<PERSON>", "name": "jack_o_lantern", "hardness": 1, "minStateId": 3503, "maxStateId": 3506, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [186], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 15, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 3503, "resistance": 1}, {"id": 184, "displayName": "Cake", "name": "cake", "hardness": 0.5, "minStateId": 3507, "maxStateId": 3513, "states": [{"name": "bites", "type": "int", "num_values": 7}], "drops": [595], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 3507, "resistance": 0.5}, {"id": 185, "displayName": "Redstone Repeater", "name": "repeater", "hardness": 0, "minStateId": 3514, "maxStateId": 3577, "states": [{"name": "delay", "type": "enum", "num_values": 4, "values": ["1", "2", "3", "4"]}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "locked", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [467], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 3517, "resistance": 0}, {"id": 186, "displayName": "White Stained Glass", "name": "white_stained_glass", "hardness": 0.3, "minStateId": 3578, "maxStateId": 3578, "states": [], "drops": [311], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 3578, "resistance": 0.3}, {"id": 187, "displayName": "Orange Stained Glass", "name": "orange_stained_glass", "hardness": 0.3, "minStateId": 3579, "maxStateId": 3579, "states": [], "drops": [312], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 3579, "resistance": 0.3}, {"id": 188, "displayName": "Magenta Stained Glass", "name": "magenta_stained_glass", "hardness": 0.3, "minStateId": 3580, "maxStateId": 3580, "states": [], "drops": [313], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 3580, "resistance": 0.3}, {"id": 189, "displayName": "Light Blue Stained Glass", "name": "light_blue_stained_glass", "hardness": 0.3, "minStateId": 3581, "maxStateId": 3581, "states": [], "drops": [314], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 3581, "resistance": 0.3}, {"id": 190, "displayName": "Yellow Stained Glass", "name": "yellow_stained_glass", "hardness": 0.3, "minStateId": 3582, "maxStateId": 3582, "states": [], "drops": [315], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 3582, "resistance": 0.3}, {"id": 191, "displayName": "Lime Stained Glass", "name": "lime_stained_glass", "hardness": 0.3, "minStateId": 3583, "maxStateId": 3583, "states": [], "drops": [316], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 3583, "resistance": 0.3}, {"id": 192, "displayName": "Pink Stained Glass", "name": "pink_stained_glass", "hardness": 0.3, "minStateId": 3584, "maxStateId": 3584, "states": [], "drops": [317], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 3584, "resistance": 0.3}, {"id": 193, "displayName": "<PERSON> Stained Glass", "name": "gray_stained_glass", "hardness": 0.3, "minStateId": 3585, "maxStateId": 3585, "states": [], "drops": [318], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 3585, "resistance": 0.3}, {"id": 194, "displayName": "Light Gray Stained Glass", "name": "light_gray_stained_glass", "hardness": 0.3, "minStateId": 3586, "maxStateId": 3586, "states": [], "drops": [319], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 3586, "resistance": 0.3}, {"id": 195, "displayName": "<PERSON><PERSON>", "name": "cyan_stained_glass", "hardness": 0.3, "minStateId": 3587, "maxStateId": 3587, "states": [], "drops": [320], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 3587, "resistance": 0.3}, {"id": 196, "displayName": "Purple Stained Glass", "name": "purple_stained_glass", "hardness": 0.3, "minStateId": 3588, "maxStateId": 3588, "states": [], "drops": [321], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 3588, "resistance": 0.3}, {"id": 197, "displayName": "Blue Stained Glass", "name": "blue_stained_glass", "hardness": 0.3, "minStateId": 3589, "maxStateId": 3589, "states": [], "drops": [322], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 3589, "resistance": 0.3}, {"id": 198, "displayName": "<PERSON> Stained Glass", "name": "brown_stained_glass", "hardness": 0.3, "minStateId": 3590, "maxStateId": 3590, "states": [], "drops": [323], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 3590, "resistance": 0.3}, {"id": 199, "displayName": "Green Stained Glass", "name": "green_stained_glass", "hardness": 0.3, "minStateId": 3591, "maxStateId": 3591, "states": [], "drops": [324], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 3591, "resistance": 0.3}, {"id": 200, "displayName": "Red Stained Glass", "name": "red_stained_glass", "hardness": 0.3, "minStateId": 3592, "maxStateId": 3592, "states": [], "drops": [325], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 3592, "resistance": 0.3}, {"id": 201, "displayName": "Black Stained Glass", "name": "black_stained_glass", "hardness": 0.3, "minStateId": 3593, "maxStateId": 3593, "states": [], "drops": [326], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 3593, "resistance": 0.3}, {"id": 202, "displayName": "Oak Trapdoor", "name": "oak_trapdoor", "hardness": 3, "minStateId": 3594, "maxStateId": 3657, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [187], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 3609, "resistance": 3}, {"id": 203, "displayName": "Spruce Trapdoor", "name": "spruce_trapdoor", "hardness": 3, "minStateId": 3658, "maxStateId": 3721, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [188], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 3673, "resistance": 3}, {"id": 204, "displayName": "<PERSON>", "name": "birch_trapdoor", "hardness": 3, "minStateId": 3722, "maxStateId": 3785, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [189], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 3737, "resistance": 3}, {"id": 205, "displayName": "Jungle Trapdoor", "name": "jungle_trapdoor", "hardness": 3, "minStateId": 3786, "maxStateId": 3849, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [190], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 3801, "resistance": 3}, {"id": 206, "displayName": "Acacia T<PERSON>door", "name": "acacia_trapdoor", "hardness": 3, "minStateId": 3850, "maxStateId": 3913, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [191], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 3865, "resistance": 3}, {"id": 207, "displayName": "Dark Oak Trapdoor", "name": "dark_oak_trapdoor", "hardness": 3, "minStateId": 3914, "maxStateId": 3977, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [192], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 3929, "resistance": 3}, {"id": 208, "displayName": "Infested Stone", "name": "infested_stone", "hardness": 0, "minStateId": 3978, "maxStateId": 3978, "states": [], "drops": [193], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 3978, "resistance": 0.75}, {"id": 209, "displayName": "Infested Cobblestone", "name": "infested_cobblestone", "hardness": 0, "minStateId": 3979, "maxStateId": 3979, "states": [], "drops": [194], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 3979, "resistance": 0.75}, {"id": 210, "displayName": "Infested Stone Bricks", "name": "infested_stone_bricks", "hardness": 0, "minStateId": 3980, "maxStateId": 3980, "states": [], "drops": [195], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 3980, "resistance": 0.75}, {"id": 211, "displayName": "Infested Mossy Stone Bricks", "name": "infested_mossy_stone_bricks", "hardness": 0, "minStateId": 3981, "maxStateId": 3981, "states": [], "drops": [196], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 3981, "resistance": 0.75}, {"id": 212, "displayName": "Infested Cracked Stone Bricks", "name": "infested_cracked_stone_bricks", "hardness": 0, "minStateId": 3982, "maxStateId": 3982, "states": [], "drops": [197], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 3982, "resistance": 0.75}, {"id": 213, "displayName": "Infested Chiseled Stone Bricks", "name": "infested_chiseled_stone_bricks", "hardness": 0, "minStateId": 3983, "maxStateId": 3983, "states": [], "drops": [198], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 3983, "resistance": 0.75}, {"id": 214, "displayName": "Stone Bricks", "name": "stone_bricks", "hardness": 1.5, "minStateId": 3984, "maxStateId": 3984, "states": [], "drops": [199], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 3984, "resistance": 6}, {"id": 215, "displayName": "Mossy Stone Bricks", "name": "mossy_stone_bricks", "hardness": 1.5, "minStateId": 3985, "maxStateId": 3985, "states": [], "drops": [200], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 3985, "resistance": 6}, {"id": 216, "displayName": "Cracked Stone Bricks", "name": "cracked_stone_bricks", "hardness": 1.5, "minStateId": 3986, "maxStateId": 3986, "states": [], "drops": [201], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 3986, "resistance": 6}, {"id": 217, "displayName": "Chiseled Stone Bricks", "name": "chiseled_stone_bricks", "hardness": 1.5, "minStateId": 3987, "maxStateId": 3987, "states": [], "drops": [202], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 3987, "resistance": 6}, {"id": 218, "displayName": "Brown Mushroom Block", "name": "brown_mushroom_block", "hardness": 0.2, "minStateId": 3988, "maxStateId": 4051, "states": [{"name": "down", "type": "bool", "num_values": 2}, {"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [203], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 3988, "resistance": 0.2}, {"id": 219, "displayName": "Red Mushroom Block", "name": "red_mushroom_block", "hardness": 0.2, "minStateId": 4052, "maxStateId": 4115, "states": [{"name": "down", "type": "bool", "num_values": 2}, {"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [204], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 4052, "resistance": 0.2}, {"id": 220, "displayName": "Mushroom Stem", "name": "mushroom_stem", "hardness": 0.2, "minStateId": 4116, "maxStateId": 4179, "states": [{"name": "down", "type": "bool", "num_values": 2}, {"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [205], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 4116, "resistance": 0.2}, {"id": 221, "displayName": "Iron Bars", "name": "iron_bars", "hardness": 5, "minStateId": 4180, "maxStateId": 4211, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [206], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 4211, "resistance": 6}, {"id": 222, "displayName": "Glass Pane", "name": "glass_pane", "hardness": 0.3, "minStateId": 4212, "maxStateId": 4243, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [207], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 4243, "resistance": 0.3}, {"id": 223, "displayName": "Melon", "name": "melon", "hardness": 1, "minStateId": 4244, "maxStateId": 4244, "states": [], "drops": [208], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 4244, "resistance": 1}, {"id": 224, "displayName": "Attached P<PERSON><PERSON> Stem", "name": "attached_pumpkin_stem", "hardness": 0, "minStateId": 4245, "maxStateId": 4248, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 4245, "resistance": 0}, {"id": 225, "displayName": "Attached Melon Stem", "name": "attached_melon_stem", "hardness": 0, "minStateId": 4249, "maxStateId": 4252, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 4249, "resistance": 0}, {"id": 226, "displayName": "<PERSON><PERSON><PERSON>", "name": "pumpkin_stem", "hardness": 0, "minStateId": 4253, "maxStateId": 4260, "states": [{"name": "age", "type": "int", "num_values": 8}], "drops": [617], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 4253, "resistance": 0}, {"id": 227, "displayName": "Melon Stem", "name": "melon_stem", "hardness": 0, "minStateId": 4261, "maxStateId": 4268, "states": [{"name": "age", "type": "int", "num_values": 8}], "drops": [618], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 4261, "resistance": 0}, {"id": 228, "displayName": "Vines", "name": "vine", "hardness": 0.2, "minStateId": 4269, "maxStateId": 4300, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [209], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 4300, "resistance": 0.2}, {"id": 229, "displayName": "Oak Fence Gate", "name": "oak_fence_gate", "hardness": 2, "minStateId": 4301, "maxStateId": 4332, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "in_wall", "type": "bool", "num_values": 2}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [210], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 4308, "resistance": 3}, {"id": 230, "displayName": "Brick Stairs", "name": "brick_stairs", "hardness": 2, "minStateId": 4333, "maxStateId": 4412, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [216], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 4344, "resistance": 6}, {"id": 231, "displayName": "Stone Brick Stairs", "name": "stone_brick_stairs", "hardness": 1.5, "minStateId": 4413, "maxStateId": 4492, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [217], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 4424, "resistance": 6}, {"id": 232, "displayName": "Mycelium", "name": "mycelium", "hardness": 0.6, "minStateId": 4493, "maxStateId": 4494, "states": [{"name": "snowy", "type": "bool", "num_values": 2}], "drops": [218], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 4494, "resistance": 0.6}, {"id": 233, "displayName": "<PERSON>", "name": "lily_pad", "hardness": 0, "minStateId": 4495, "maxStateId": 4495, "states": [], "drops": [219], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 4495, "resistance": 0}, {"id": 234, "displayName": "Nether Bricks", "name": "nether_bricks", "hardness": 2, "minStateId": 4496, "maxStateId": 4496, "states": [], "drops": [220], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 4496, "resistance": 6}, {"id": 235, "displayName": "Nether Brick Fence", "name": "nether_brick_fence", "hardness": 2, "minStateId": 4497, "maxStateId": 4528, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [221], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 4528, "resistance": 6}, {"id": 236, "displayName": "Nether Brick Stairs", "name": "nether_brick_stairs", "hardness": 2, "minStateId": 4529, "maxStateId": 4608, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [222], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 4540, "resistance": 6}, {"id": 237, "displayName": "Nether Wart", "name": "nether_wart", "hardness": 0, "minStateId": 4609, "maxStateId": 4612, "states": [{"name": "age", "type": "int", "num_values": 4}], "drops": [628], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 4609, "resistance": 0}, {"id": 238, "displayName": "Enchanting Table", "name": "enchanting_table", "hardness": 5, "minStateId": 4613, "maxStateId": 4613, "states": [], "drops": [223], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 4613, "resistance": 1200}, {"id": 239, "displayName": "Brewing Stand", "name": "brewing_stand", "hardness": 0.5, "minStateId": 4614, "maxStateId": 4621, "states": [{"name": "has_bottle_0", "type": "bool", "num_values": 2}, {"name": "has_bottle_1", "type": "bool", "num_values": 2}, {"name": "has_bottle_2", "type": "bool", "num_values": 2}], "drops": [635], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 1, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 4621, "resistance": 0.5}, {"id": 240, "displayName": "<PERSON><PERSON><PERSON>", "name": "cauldron", "hardness": 2, "minStateId": 4622, "maxStateId": 4625, "states": [{"name": "level", "type": "int", "num_values": 4}], "drops": [636], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 4622, "resistance": 2}, {"id": 241, "displayName": "End Portal", "name": "end_portal", "hardness": null, "minStateId": 4626, "maxStateId": 4626, "states": [], "drops": [], "diggable": false, "transparent": true, "filterLight": 0, "emitLight": 15, "boundingBox": "empty", "stackSize": 0, "defaultState": 4626, "resistance": 3600000}, {"id": 242, "displayName": "End Portal Frame", "name": "end_portal_frame", "hardness": null, "minStateId": 4627, "maxStateId": 4634, "states": [{"name": "eye", "type": "bool", "num_values": 2}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [224], "diggable": false, "transparent": true, "filterLight": 0, "emitLight": 1, "boundingBox": "block", "stackSize": 64, "defaultState": 4631, "resistance": 3600000}, {"id": 243, "displayName": "End Stone", "name": "end_stone", "hardness": 3, "minStateId": 4635, "maxStateId": 4635, "states": [], "drops": [225], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 4635, "resistance": 9}, {"id": 244, "displayName": "Dragon Egg", "name": "dragon_egg", "hardness": 3, "minStateId": 4636, "maxStateId": 4636, "states": [], "drops": [227], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 4636, "resistance": 9}, {"id": 245, "displayName": "Redstone Lamp", "name": "redstone_lamp", "hardness": 0.3, "minStateId": 4637, "maxStateId": 4638, "states": [{"name": "lit", "type": "bool", "num_values": 2}], "drops": [228], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 15, "boundingBox": "block", "stackSize": 64, "defaultState": 4638, "resistance": 0.3}, {"id": 246, "displayName": "Cocoa", "name": "cocoa", "hardness": 0.2, "minStateId": 4639, "maxStateId": 4650, "states": [{"name": "age", "type": "int", "num_values": 3}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [580], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 4639, "resistance": 3}, {"id": 247, "displayName": "Sandstone Stairs", "name": "sandstone_stairs", "hardness": 0.8, "minStateId": 4651, "maxStateId": 4730, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [229], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 4662, "resistance": 0.8}, {"id": 248, "displayName": "Emerald Ore", "name": "emerald_ore", "hardness": 3, "minStateId": 4731, "maxStateId": 4731, "states": [], "drops": [230], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "495": true}, "defaultState": 4731, "resistance": 3}, {"id": 249, "displayName": "<PERSON><PERSON> Chest", "name": "ender_chest", "hardness": 22.5, "minStateId": 4732, "maxStateId": 4739, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [231], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 4733, "resistance": 600}, {"id": 250, "displayName": "Tripwire Hook", "name": "tripwire_hook", "hardness": 0, "minStateId": 4740, "maxStateId": 4755, "states": [{"name": "attached", "type": "bool", "num_values": 2}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [232], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 4749, "resistance": 0}, {"id": 251, "displayName": "Tripwire", "name": "tripwire", "hardness": 0, "minStateId": 4756, "maxStateId": 4883, "states": [{"name": "attached", "type": "bool", "num_values": 2}, {"name": "disarmed", "type": "bool", "num_values": 2}, {"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [504], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 4883, "resistance": 0}, {"id": 252, "displayName": "Block of Emerald", "name": "emerald_block", "hardness": 5, "minStateId": 4884, "maxStateId": 4884, "states": [], "drops": [233], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "495": true}, "defaultState": 4884, "resistance": 6}, {"id": 253, "displayName": "Spruce Stairs", "name": "spruce_stairs", "hardness": 2, "minStateId": 4885, "maxStateId": 4964, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [234], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 4896, "resistance": 3}, {"id": 254, "displayName": "<PERSON> Stairs", "name": "birch_stairs", "hardness": 2, "minStateId": 4965, "maxStateId": 5044, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [235], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 4976, "resistance": 3}, {"id": 255, "displayName": "Jungle Stairs", "name": "jungle_stairs", "hardness": 2, "minStateId": 5045, "maxStateId": 5124, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [236], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 5056, "resistance": 3}, {"id": 256, "displayName": "Command Block", "name": "command_block", "hardness": null, "minStateId": 5125, "maxStateId": 5136, "states": [{"name": "conditional", "type": "bool", "num_values": 2}, {"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [237], "diggable": false, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5131, "resistance": 3600000}, {"id": 257, "displayName": "Beacon", "name": "beacon", "hardness": 3, "minStateId": 5137, "maxStateId": 5137, "states": [], "drops": [238], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 15, "boundingBox": "block", "stackSize": 64, "defaultState": 5137, "resistance": 3}, {"id": 258, "displayName": "Cobblestone Wall", "name": "cobblestone_wall", "hardness": 2, "minStateId": 5138, "maxStateId": 5201, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [239], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5197, "resistance": 6}, {"id": 259, "displayName": "<PERSON><PERSON>", "name": "mossy_cobblestone_wall", "hardness": 2, "minStateId": 5202, "maxStateId": 5265, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [240], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5261, "resistance": 6}, {"id": 260, "displayName": "Flower Pot", "name": "flower_pot", "hardness": 0, "minStateId": 5266, "maxStateId": 5266, "states": [], "drops": [696], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5266, "resistance": 0}, {"id": 261, "displayName": "Potted Oak Sapling", "name": "potted_oak_sapling", "hardness": 0, "minStateId": 5267, "maxStateId": 5267, "states": [], "drops": [696, 19], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5267, "resistance": 0}, {"id": 262, "displayName": "Potted Spruce Sapling", "name": "potted_spruce_sapling", "hardness": 0, "minStateId": 5268, "maxStateId": 5268, "states": [], "drops": [696, 20], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5268, "resistance": 0}, {"id": 263, "displayName": "Potted Birch Sapling", "name": "potted_birch_sapling", "hardness": 0, "minStateId": 5269, "maxStateId": 5269, "states": [], "drops": [696, 21], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5269, "resistance": 0}, {"id": 264, "displayName": "Potted Jungle Sapling", "name": "potted_jungle_sapling", "hardness": 0, "minStateId": 5270, "maxStateId": 5270, "states": [], "drops": [696, 22], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5270, "resistance": 0}, {"id": 265, "displayName": "Potted Acacia Sapling", "name": "potted_acacia_sapling", "hardness": 0, "minStateId": 5271, "maxStateId": 5271, "states": [], "drops": [696, 23], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5271, "resistance": 0}, {"id": 266, "displayName": "Potted Dark Oak Sapling", "name": "potted_dark_oak_sapling", "hardness": 0, "minStateId": 5272, "maxStateId": 5272, "states": [], "drops": [696, 24], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5272, "resistance": 0}, {"id": 267, "displayName": "Potted Fern", "name": "potted_fern", "hardness": 0, "minStateId": 5273, "maxStateId": 5273, "states": [], "drops": [696, 77], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5273, "resistance": 0}, {"id": 268, "displayName": "Potted Dandelion", "name": "potted_dandelion", "hardness": 0, "minStateId": 5274, "maxStateId": 5274, "states": [], "drops": [696, 98], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5274, "resistance": 0}, {"id": 269, "displayName": "Potted Poppy", "name": "potted_poppy", "hardness": 0, "minStateId": 5275, "maxStateId": 5275, "states": [], "drops": [696, 99], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5275, "resistance": 0}, {"id": 270, "displayName": "Potted Blue Orchid", "name": "potted_blue_orchid", "hardness": 0, "minStateId": 5276, "maxStateId": 5276, "states": [], "drops": [696, 100], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5276, "resistance": 0}, {"id": 271, "displayName": "Potted Allium", "name": "potted_allium", "hardness": 0, "minStateId": 5277, "maxStateId": 5277, "states": [], "drops": [696, 101], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5277, "resistance": 0}, {"id": 272, "displayName": "Potted Azure Bluet", "name": "potted_azure_bluet", "hardness": 0, "minStateId": 5278, "maxStateId": 5278, "states": [], "drops": [696, 102], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5278, "resistance": 0}, {"id": 273, "displayName": "Potted Red Tulip", "name": "potted_red_tulip", "hardness": 0, "minStateId": 5279, "maxStateId": 5279, "states": [], "drops": [696, 103], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5279, "resistance": 0}, {"id": 274, "displayName": "Potted Orange Tulip", "name": "potted_orange_tulip", "hardness": 0, "minStateId": 5280, "maxStateId": 5280, "states": [], "drops": [696, 104], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5280, "resistance": 0}, {"id": 275, "displayName": "Potted White Tulip", "name": "potted_white_tulip", "hardness": 0, "minStateId": 5281, "maxStateId": 5281, "states": [], "drops": [696, 105], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5281, "resistance": 0}, {"id": 276, "displayName": "Potted Pink Tulip", "name": "potted_pink_tulip", "hardness": 0, "minStateId": 5282, "maxStateId": 5282, "states": [], "drops": [696, 106], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5282, "resistance": 0}, {"id": 277, "displayName": "Potted Oxeye Daisy", "name": "potted_oxeye_daisy", "hardness": 0, "minStateId": 5283, "maxStateId": 5283, "states": [], "drops": [696, 107], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5283, "resistance": 0}, {"id": 278, "displayName": "Potted Red Mushroom", "name": "potted_red_mushroom", "hardness": 0, "minStateId": 5284, "maxStateId": 5284, "states": [], "drops": [696, 109], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5284, "resistance": 0}, {"id": 279, "displayName": "Potted Brown Mushroom", "name": "potted_brown_mushroom", "hardness": 0, "minStateId": 5285, "maxStateId": 5285, "states": [], "drops": [696, 108], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5285, "resistance": 0}, {"id": 280, "displayName": "Potted Dead Bush", "name": "potted_dead_bush", "hardness": 0, "minStateId": 5286, "maxStateId": 5286, "states": [], "drops": [696, 78], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5286, "resistance": 0}, {"id": 281, "displayName": "Potted Cactus", "name": "potted_cactus", "hardness": 0, "minStateId": 5287, "maxStateId": 5287, "states": [], "drops": [696, 172], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5287, "resistance": 0}, {"id": 282, "displayName": "Carrots", "name": "carrots", "hardness": 0, "minStateId": 5288, "maxStateId": 5295, "states": [{"name": "age", "type": "int", "num_values": 8}], "drops": [697], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 5288, "resistance": 0}, {"id": 283, "displayName": "Potatoes", "name": "potatoes", "hardness": 0, "minStateId": 5296, "maxStateId": 5303, "states": [{"name": "age", "type": "int", "num_values": 8}], "drops": [698], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 5296, "resistance": 0}, {"id": 284, "displayName": "Oak Button", "name": "oak_button", "hardness": 0.5, "minStateId": 5304, "maxStateId": 5327, "states": [{"name": "face", "type": "enum", "num_values": 3, "values": ["floor", "wall", "ceiling"]}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [241], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 5313, "resistance": 0.5}, {"id": 285, "displayName": "Spruce Button", "name": "spruce_button", "hardness": 0.5, "minStateId": 5328, "maxStateId": 5351, "states": [{"name": "face", "type": "enum", "num_values": 3, "values": ["floor", "wall", "ceiling"]}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [242], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 5337, "resistance": 0.5}, {"id": 286, "displayName": "<PERSON>", "name": "birch_button", "hardness": 0.5, "minStateId": 5352, "maxStateId": 5375, "states": [{"name": "face", "type": "enum", "num_values": 3, "values": ["floor", "wall", "ceiling"]}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [243], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 5361, "resistance": 0.5}, {"id": 287, "displayName": "<PERSON>ton", "name": "jungle_button", "hardness": 0.5, "minStateId": 5376, "maxStateId": 5399, "states": [{"name": "face", "type": "enum", "num_values": 3, "values": ["floor", "wall", "ceiling"]}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [244], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 5385, "resistance": 0.5}, {"id": 288, "displayName": "Acacia <PERSON>", "name": "acacia_button", "hardness": 0.5, "minStateId": 5400, "maxStateId": 5423, "states": [{"name": "face", "type": "enum", "num_values": 3, "values": ["floor", "wall", "ceiling"]}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [245], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 5409, "resistance": 0.5}, {"id": 289, "displayName": "Dark Oak Button", "name": "dark_oak_button", "hardness": 0.5, "minStateId": 5424, "maxStateId": 5447, "states": [{"name": "face", "type": "enum", "num_values": 3, "values": ["floor", "wall", "ceiling"]}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [246], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 5433, "resistance": 0.5}, {"id": 290, "displayName": "Skeleton Wall Skull", "name": "skeleton_wall_skull", "hardness": 1, "minStateId": 5448, "maxStateId": 5451, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [703], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5448, "resistance": 1}, {"id": 291, "displayName": "Skeleton Skull", "name": "skeleton_skull", "hardness": 1, "minStateId": 5452, "maxStateId": 5467, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [703], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5452, "resistance": 1}, {"id": 292, "displayName": "Wither Skeleton Wall Skull", "name": "wither_skeleton_wall_skull", "hardness": 1, "minStateId": 5468, "maxStateId": 5471, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [704], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5468, "resistance": 1}, {"id": 293, "displayName": "Wither Skeleton Skull", "name": "wither_skeleton_skull", "hardness": 1, "minStateId": 5472, "maxStateId": 5487, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [704], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5472, "resistance": 1}, {"id": 294, "displayName": "<PERSON> Head", "name": "zombie_wall_head", "hardness": 1, "minStateId": 5488, "maxStateId": 5491, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [706], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5488, "resistance": 1}, {"id": 295, "displayName": "Zombie Head", "name": "zombie_head", "hardness": 1, "minStateId": 5492, "maxStateId": 5507, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [706], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5492, "resistance": 1}, {"id": 296, "displayName": "Player <PERSON>", "name": "player_wall_head", "hardness": 1, "minStateId": 5508, "maxStateId": 5511, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [705], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5508, "resistance": 1}, {"id": 297, "displayName": "Player Head", "name": "player_head", "hardness": 1, "minStateId": 5512, "maxStateId": 5527, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [705], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5512, "resistance": 1}, {"id": 298, "displayName": "Creeper Wall Head", "name": "creeper_wall_head", "hardness": 1, "minStateId": 5528, "maxStateId": 5531, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [707], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5528, "resistance": 1}, {"id": 299, "displayName": "Creeper Head", "name": "creeper_head", "hardness": 1, "minStateId": 5532, "maxStateId": 5547, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [707], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5532, "resistance": 1}, {"id": 300, "displayName": "Dragon Wall Head", "name": "dragon_wall_head", "hardness": 1, "minStateId": 5548, "maxStateId": 5551, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [708], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5548, "resistance": 1}, {"id": 301, "displayName": "Dragon Head", "name": "dragon_head", "hardness": 1, "minStateId": 5552, "maxStateId": 5567, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [708], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5552, "resistance": 1}, {"id": 302, "displayName": "An<PERSON>", "name": "anvil", "hardness": 5, "minStateId": 5568, "maxStateId": 5571, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [247], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5568, "resistance": 1200}, {"id": 303, "displayName": "Chipped Anvil", "name": "chipped_anvil", "hardness": 5, "minStateId": 5572, "maxStateId": 5575, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [248], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5572, "resistance": 1200}, {"id": 304, "displayName": "Damaged Anvil", "name": "damaged_anvil", "hardness": 5, "minStateId": 5576, "maxStateId": 5579, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [249], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5576, "resistance": 1200}, {"id": 305, "displayName": "Trapped Chest", "name": "trapped_chest", "hardness": 2.5, "minStateId": 5580, "maxStateId": 5603, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "type", "type": "enum", "num_values": 3, "values": ["single", "left", "right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [250], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 5581, "resistance": 2.5}, {"id": 306, "displayName": "Light Weighted Pressure Plate", "name": "light_weighted_pressure_plate", "hardness": 0.5, "minStateId": 5604, "maxStateId": 5619, "states": [{"name": "power", "type": "int", "num_values": 16}], "drops": [251], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5604, "resistance": 0.5}, {"id": 307, "displayName": "Heavy Weighted Pressure Plate", "name": "heavy_weighted_pressure_plate", "hardness": 0.5, "minStateId": 5620, "maxStateId": 5635, "states": [{"name": "power", "type": "int", "num_values": 16}], "drops": [252], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5620, "resistance": 0.5}, {"id": 308, "displayName": "Redstone Comparator", "name": "comparator", "hardness": 0, "minStateId": 5636, "maxStateId": 5651, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "mode", "type": "enum", "num_values": 2, "values": ["compare", "subtract"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [468], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5637, "resistance": 0}, {"id": 309, "displayName": "Daylight Detector", "name": "daylight_detector", "hardness": 0.2, "minStateId": 5652, "maxStateId": 5683, "states": [{"name": "inverted", "type": "bool", "num_values": 2}, {"name": "power", "type": "int", "num_values": 16}], "drops": [253], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 5668, "resistance": 0.2}, {"id": 310, "displayName": "Block of Redstone", "name": "redstone_block", "hardness": 5, "minStateId": 5684, "maxStateId": 5684, "states": [], "drops": [254], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5684, "resistance": 6}, {"id": 311, "displayName": "<PERSON><PERSON>", "name": "nether_quartz_ore", "hardness": 3, "minStateId": 5685, "maxStateId": 5685, "states": [], "drops": [255], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5685, "resistance": 3}, {"id": 312, "displayName": "<PERSON>", "name": "hopper", "hardness": 3, "minStateId": 5686, "maxStateId": 5695, "states": [{"name": "enabled", "type": "bool", "num_values": 2}, {"name": "facing", "type": "enum", "num_values": 5, "values": ["down", "north", "south", "west", "east"]}], "drops": [256], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5686, "resistance": 4.8}, {"id": 313, "displayName": "Block of Quartz", "name": "quartz_block", "hardness": 0.8, "minStateId": 5696, "maxStateId": 5696, "states": [], "drops": [258], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5696, "resistance": 0.8}, {"id": 314, "displayName": "Chiseled Quartz Block", "name": "chiseled_quartz_block", "hardness": 0.8, "minStateId": 5697, "maxStateId": 5697, "states": [], "drops": [257], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5697, "resistance": 0.8}, {"id": 315, "displayName": "Quartz <PERSON>", "name": "quartz_pillar", "hardness": 0.8, "minStateId": 5698, "maxStateId": 5700, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [259], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5699, "resistance": 0.8}, {"id": 316, "displayName": "Quartz Stairs", "name": "quartz_stairs", "hardness": 0.8, "minStateId": 5701, "maxStateId": 5780, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [260], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5712, "resistance": 0.8}, {"id": 317, "displayName": "Activator Rail", "name": "activator_rail", "hardness": 0.7, "minStateId": 5781, "maxStateId": 5792, "states": [{"name": "powered", "type": "bool", "num_values": 2}, {"name": "shape", "type": "enum", "num_values": 6, "values": ["north_south", "east_west", "ascending_east", "ascending_west", "ascending_north", "ascending_south"]}], "drops": [261], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "rock", "defaultState": 5787, "resistance": 0.7}, {"id": 318, "displayName": "Dropper", "name": "dropper", "hardness": 3.5, "minStateId": 5793, "maxStateId": 5804, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}, {"name": "triggered", "type": "bool", "num_values": 2}], "drops": [262], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5794, "resistance": 3.5}, {"id": 319, "displayName": "White Terracotta", "name": "white_terracotta", "hardness": 1.25, "minStateId": 5805, "maxStateId": 5805, "states": [], "drops": [263], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5805, "resistance": 4.2}, {"id": 320, "displayName": "Orange Terracotta", "name": "orange_terracotta", "hardness": 1.25, "minStateId": 5806, "maxStateId": 5806, "states": [], "drops": [264], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5806, "resistance": 4.2}, {"id": 321, "displayName": "Magenta Terracotta", "name": "magenta_terracotta", "hardness": 1.25, "minStateId": 5807, "maxStateId": 5807, "states": [], "drops": [265], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5807, "resistance": 4.2}, {"id": 322, "displayName": "Light Blue Terracotta", "name": "light_blue_terracotta", "hardness": 1.25, "minStateId": 5808, "maxStateId": 5808, "states": [], "drops": [266], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5808, "resistance": 4.2}, {"id": 323, "displayName": "Yellow Terracotta", "name": "yellow_terracotta", "hardness": 1.25, "minStateId": 5809, "maxStateId": 5809, "states": [], "drops": [267], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5809, "resistance": 4.2}, {"id": 324, "displayName": "Lime Terracotta", "name": "lime_terracotta", "hardness": 1.25, "minStateId": 5810, "maxStateId": 5810, "states": [], "drops": [268], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5810, "resistance": 4.2}, {"id": 325, "displayName": "Pink Terracotta", "name": "pink_terracotta", "hardness": 1.25, "minStateId": 5811, "maxStateId": 5811, "states": [], "drops": [269], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5811, "resistance": 4.2}, {"id": 326, "displayName": "Gray <PERSON>", "name": "gray_terracotta", "hardness": 1.25, "minStateId": 5812, "maxStateId": 5812, "states": [], "drops": [270], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5812, "resistance": 4.2}, {"id": 327, "displayName": "Light Gray Terracotta", "name": "light_gray_terracotta", "hardness": 1.25, "minStateId": 5813, "maxStateId": 5813, "states": [], "drops": [271], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5813, "resistance": 4.2}, {"id": 328, "displayName": "<PERSON><PERSON>", "name": "cyan_terracotta", "hardness": 1.25, "minStateId": 5814, "maxStateId": 5814, "states": [], "drops": [272], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5814, "resistance": 4.2}, {"id": 329, "displayName": "Purple Terracotta", "name": "purple_terracotta", "hardness": 1.25, "minStateId": 5815, "maxStateId": 5815, "states": [], "drops": [273], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5815, "resistance": 4.2}, {"id": 330, "displayName": "Blue Terracotta", "name": "blue_terracotta", "hardness": 1.25, "minStateId": 5816, "maxStateId": 5816, "states": [], "drops": [274], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5816, "resistance": 4.2}, {"id": 331, "displayName": "Brown Terracotta", "name": "brown_terracotta", "hardness": 1.25, "minStateId": 5817, "maxStateId": 5817, "states": [], "drops": [275], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5817, "resistance": 4.2}, {"id": 332, "displayName": "Green Terracotta", "name": "green_terracotta", "hardness": 1.25, "minStateId": 5818, "maxStateId": 5818, "states": [], "drops": [276], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5818, "resistance": 4.2}, {"id": 333, "displayName": "Red Terracotta", "name": "red_terracotta", "hardness": 1.25, "minStateId": 5819, "maxStateId": 5819, "states": [], "drops": [277], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5819, "resistance": 4.2}, {"id": 334, "displayName": "Black Terracotta", "name": "black_terracotta", "hardness": 1.25, "minStateId": 5820, "maxStateId": 5820, "states": [], "drops": [278], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 5820, "resistance": 4.2}, {"id": 335, "displayName": "White Stained Glass Pane", "name": "white_stained_glass_pane", "hardness": 0.3, "minStateId": 5821, "maxStateId": 5852, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [327], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5852, "resistance": 0.3}, {"id": 336, "displayName": "Orange Stained Glass Pane", "name": "orange_stained_glass_pane", "hardness": 0.3, "minStateId": 5853, "maxStateId": 5884, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [328], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5884, "resistance": 0.3}, {"id": 337, "displayName": "Magenta Stained Glass Pane", "name": "magenta_stained_glass_pane", "hardness": 0.3, "minStateId": 5885, "maxStateId": 5916, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [329], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5916, "resistance": 0.3}, {"id": 338, "displayName": "Light Blue Stained Glass Pane", "name": "light_blue_stained_glass_pane", "hardness": 0.3, "minStateId": 5917, "maxStateId": 5948, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [330], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5948, "resistance": 0.3}, {"id": 339, "displayName": "Yellow Stained Glass Pane", "name": "yellow_stained_glass_pane", "hardness": 0.3, "minStateId": 5949, "maxStateId": 5980, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [331], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5980, "resistance": 0.3}, {"id": 340, "displayName": "Lime Stained Glass Pane", "name": "lime_stained_glass_pane", "hardness": 0.3, "minStateId": 5981, "maxStateId": 6012, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [332], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6012, "resistance": 0.3}, {"id": 341, "displayName": "Pink Stained Glass Pane", "name": "pink_stained_glass_pane", "hardness": 0.3, "minStateId": 6013, "maxStateId": 6044, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [333], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6044, "resistance": 0.3}, {"id": 342, "displayName": "Gray Stained Glass Pane", "name": "gray_stained_glass_pane", "hardness": 0.3, "minStateId": 6045, "maxStateId": 6076, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [334], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6076, "resistance": 0.3}, {"id": 343, "displayName": "Light Gray Stained Glass Pane", "name": "light_gray_stained_glass_pane", "hardness": 0.3, "minStateId": 6077, "maxStateId": 6108, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [335], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6108, "resistance": 0.3}, {"id": 344, "displayName": "<PERSON><PERSON> Stained Glass Pane", "name": "cyan_stained_glass_pane", "hardness": 0.3, "minStateId": 6109, "maxStateId": 6140, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [336], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6140, "resistance": 0.3}, {"id": 345, "displayName": "Purple Stained Glass Pane", "name": "purple_stained_glass_pane", "hardness": 0.3, "minStateId": 6141, "maxStateId": 6172, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [337], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6172, "resistance": 0.3}, {"id": 346, "displayName": "Blue Stained Glass Pane", "name": "blue_stained_glass_pane", "hardness": 0.3, "minStateId": 6173, "maxStateId": 6204, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [338], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6204, "resistance": 0.3}, {"id": 347, "displayName": "<PERSON> Stained Glass Pane", "name": "brown_stained_glass_pane", "hardness": 0.3, "minStateId": 6205, "maxStateId": 6236, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [339], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6236, "resistance": 0.3}, {"id": 348, "displayName": "Green Stained Glass Pane", "name": "green_stained_glass_pane", "hardness": 0.3, "minStateId": 6237, "maxStateId": 6268, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [340], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6268, "resistance": 0.3}, {"id": 349, "displayName": "Red Stained Glass Pane", "name": "red_stained_glass_pane", "hardness": 0.3, "minStateId": 6269, "maxStateId": 6300, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [341], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6300, "resistance": 0.3}, {"id": 350, "displayName": "Black Stained Glass Pane", "name": "black_stained_glass_pane", "hardness": 0.3, "minStateId": 6301, "maxStateId": 6332, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [342], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6332, "resistance": 0.3}, {"id": 351, "displayName": "Acacia Stairs", "name": "acacia_stairs", "hardness": 2, "minStateId": 6333, "maxStateId": 6412, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [301], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 6344, "resistance": 3}, {"id": 352, "displayName": "Dark Oak Stairs", "name": "dark_oak_stairs", "hardness": 2, "minStateId": 6413, "maxStateId": 6492, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [302], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 6424, "resistance": 3}, {"id": 353, "displayName": "Slime Block", "name": "slime_block", "hardness": 0, "minStateId": 6493, "maxStateId": 6493, "states": [], "drops": [303], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6493, "resistance": 0}, {"id": 354, "displayName": "Barrier", "name": "barrier", "hardness": null, "minStateId": 6494, "maxStateId": 6494, "states": [], "drops": [279], "diggable": false, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6494, "resistance": 3600000.75}, {"id": 355, "displayName": "Iron Trapdoor", "name": "iron_trapdoor", "hardness": 5, "minStateId": 6495, "maxStateId": 6558, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [280], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 6510, "resistance": 5}, {"id": 356, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "prismarine", "hardness": 1.5, "minStateId": 6559, "maxStateId": 6559, "states": [], "drops": [343], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 6559, "resistance": 6}, {"id": 357, "displayName": "Prismarine <PERSON>s", "name": "prismarine_bricks", "hardness": 1.5, "minStateId": 6560, "maxStateId": 6560, "states": [], "drops": [344], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 6560, "resistance": 6}, {"id": 358, "displayName": "<PERSON>", "name": "dark_prismarine", "hardness": 1.5, "minStateId": 6561, "maxStateId": 6561, "states": [], "drops": [345], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 6561, "resistance": 6}, {"id": 359, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "prismarine_stairs", "hardness": 1.5, "minStateId": 6562, "maxStateId": 6641, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [346], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 6573, "resistance": 6}, {"id": 360, "displayName": "Prismarine Brick Stairs", "name": "prismarine_brick_stairs", "hardness": 1.5, "minStateId": 6642, "maxStateId": 6721, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [347], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 6653, "resistance": 6}, {"id": 361, "displayName": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "name": "dark_prismarine_stairs", "hardness": 1.5, "minStateId": 6722, "maxStateId": 6801, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [348], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 6733, "resistance": 6}, {"id": 362, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "prismarine_slab", "hardness": 1.5, "minStateId": 6802, "maxStateId": 6807, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [128], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 6805, "resistance": 6}, {"id": 363, "displayName": "Prismarine Brick Slab", "name": "prismarine_brick_slab", "hardness": 1.5, "minStateId": 6808, "maxStateId": 6813, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [129], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 6811, "resistance": 6}, {"id": 364, "displayName": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "name": "dark_prismarine_slab", "hardness": 1.5, "minStateId": 6814, "maxStateId": 6819, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [130], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 6817, "resistance": 6}, {"id": 365, "displayName": "Sea Lantern", "name": "sea_lantern", "hardness": 0.3, "minStateId": 6820, "maxStateId": 6820, "states": [], "drops": [349], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 15, "boundingBox": "block", "stackSize": 64, "defaultState": 6820, "resistance": 0.3}, {"id": 366, "displayName": "<PERSON>", "name": "hay_block", "hardness": 0.5, "minStateId": 6821, "maxStateId": 6823, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [281], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6822, "resistance": 0.5}, {"id": 367, "displayName": "White Carpet", "name": "white_carpet", "hardness": 0.1, "minStateId": 6824, "maxStateId": 6824, "states": [], "drops": [282], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6824, "resistance": 0.1}, {"id": 368, "displayName": "Orange Carpet", "name": "orange_carpet", "hardness": 0.1, "minStateId": 6825, "maxStateId": 6825, "states": [], "drops": [283], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6825, "resistance": 0.1}, {"id": 369, "displayName": "Magenta Carpet", "name": "magenta_carpet", "hardness": 0.1, "minStateId": 6826, "maxStateId": 6826, "states": [], "drops": [284], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6826, "resistance": 0.1}, {"id": 370, "displayName": "Light Blue Carpet", "name": "light_blue_carpet", "hardness": 0.1, "minStateId": 6827, "maxStateId": 6827, "states": [], "drops": [285], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6827, "resistance": 0.1}, {"id": 371, "displayName": "Yellow Carpet", "name": "yellow_carpet", "hardness": 0.1, "minStateId": 6828, "maxStateId": 6828, "states": [], "drops": [286], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6828, "resistance": 0.1}, {"id": 372, "displayName": "Lime Carpet", "name": "lime_carpet", "hardness": 0.1, "minStateId": 6829, "maxStateId": 6829, "states": [], "drops": [287], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6829, "resistance": 0.1}, {"id": 373, "displayName": "Pink Carpet", "name": "pink_carpet", "hardness": 0.1, "minStateId": 6830, "maxStateId": 6830, "states": [], "drops": [288], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6830, "resistance": 0.1}, {"id": 374, "displayName": "<PERSON> Carpet", "name": "gray_carpet", "hardness": 0.1, "minStateId": 6831, "maxStateId": 6831, "states": [], "drops": [289], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6831, "resistance": 0.1}, {"id": 375, "displayName": "Light Gray Carpet", "name": "light_gray_carpet", "hardness": 0.1, "minStateId": 6832, "maxStateId": 6832, "states": [], "drops": [290], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6832, "resistance": 0.1}, {"id": 376, "displayName": "<PERSON><PERSON>", "name": "cyan_carpet", "hardness": 0.1, "minStateId": 6833, "maxStateId": 6833, "states": [], "drops": [291], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6833, "resistance": 0.1}, {"id": 377, "displayName": "Purple Carpet", "name": "purple_carpet", "hardness": 0.1, "minStateId": 6834, "maxStateId": 6834, "states": [], "drops": [292], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6834, "resistance": 0.1}, {"id": 378, "displayName": "Blue Carpet", "name": "blue_carpet", "hardness": 0.1, "minStateId": 6835, "maxStateId": 6835, "states": [], "drops": [293], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6835, "resistance": 0.1}, {"id": 379, "displayName": "Brown Carpet", "name": "brown_carpet", "hardness": 0.1, "minStateId": 6836, "maxStateId": 6836, "states": [], "drops": [294], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6836, "resistance": 0.1}, {"id": 380, "displayName": "Green Carpet", "name": "green_carpet", "hardness": 0.1, "minStateId": 6837, "maxStateId": 6837, "states": [], "drops": [295], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6837, "resistance": 0.1}, {"id": 381, "displayName": "Red Carpet", "name": "red_carpet", "hardness": 0.1, "minStateId": 6838, "maxStateId": 6838, "states": [], "drops": [296], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6838, "resistance": 0.1}, {"id": 382, "displayName": "Black Carpet", "name": "black_carpet", "hardness": 0.1, "minStateId": 6839, "maxStateId": 6839, "states": [], "drops": [297], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6839, "resistance": 0.1}, {"id": 383, "displayName": "Terracotta", "name": "terracotta", "hardness": 1.25, "minStateId": 6840, "maxStateId": 6840, "states": [], "drops": [298], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 6840, "resistance": 4.2}, {"id": 384, "displayName": "Block of Coal", "name": "coal_block", "hardness": 5, "minStateId": 6841, "maxStateId": 6841, "states": [], "drops": [299], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 6841, "resistance": 6}, {"id": 385, "displayName": "Packed Ice", "name": "packed_ice", "hardness": 0.5, "minStateId": 6842, "maxStateId": 6842, "states": [], "drops": [300], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "defaultState": 6842, "resistance": 0.5}, {"id": 386, "displayName": "Sunflower", "name": "sunflower", "hardness": 0, "minStateId": 6843, "maxStateId": 6844, "states": [{"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}], "drops": [305], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 6844, "resistance": 0}, {"id": 387, "displayName": "Lilac", "name": "lilac", "hardness": 0, "minStateId": 6845, "maxStateId": 6846, "states": [{"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}], "drops": [306], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 6846, "resistance": 0}, {"id": 388, "displayName": "<PERSON>", "name": "rose_bush", "hardness": 0, "minStateId": 6847, "maxStateId": 6848, "states": [{"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}], "drops": [307], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 6848, "resistance": 0}, {"id": 389, "displayName": "Peony", "name": "peony", "hardness": 0, "minStateId": 6849, "maxStateId": 6850, "states": [{"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}], "drops": [308], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 6850, "resistance": 0}, {"id": 390, "displayName": "Tall Grass", "name": "tall_grass", "hardness": 0, "minStateId": 6851, "maxStateId": 6852, "states": [{"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}], "drops": [309], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 6852, "resistance": 0}, {"id": 391, "displayName": "Large Fern", "name": "large_fern", "hardness": 0, "minStateId": 6853, "maxStateId": 6854, "states": [{"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}], "drops": [310], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 6854, "resistance": 0}, {"id": 392, "displayName": "White Banner", "name": "white_banner", "hardness": 1, "minStateId": 6855, "maxStateId": 6870, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [735], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 6855, "resistance": 1}, {"id": 393, "displayName": "Orange Banner", "name": "orange_banner", "hardness": 1, "minStateId": 6871, "maxStateId": 6886, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [736], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 6871, "resistance": 1}, {"id": 394, "displayName": "Magenta Banner", "name": "magenta_banner", "hardness": 1, "minStateId": 6887, "maxStateId": 6902, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [737], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 6887, "resistance": 1}, {"id": 395, "displayName": "Light Blue Banner", "name": "light_blue_banner", "hardness": 1, "minStateId": 6903, "maxStateId": 6918, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [738], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 6903, "resistance": 1}, {"id": 396, "displayName": "Yellow Banner", "name": "yellow_banner", "hardness": 1, "minStateId": 6919, "maxStateId": 6934, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [739], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 6919, "resistance": 1}, {"id": 397, "displayName": "Lime Banner", "name": "lime_banner", "hardness": 1, "minStateId": 6935, "maxStateId": 6950, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [740], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 6935, "resistance": 1}, {"id": 398, "displayName": "Pink Banner", "name": "pink_banner", "hardness": 1, "minStateId": 6951, "maxStateId": 6966, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [741], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 6951, "resistance": 1}, {"id": 399, "displayName": "<PERSON>", "name": "gray_banner", "hardness": 1, "minStateId": 6967, "maxStateId": 6982, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [742], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 6967, "resistance": 1}, {"id": 400, "displayName": "<PERSON> Gray Banner", "name": "light_gray_banner", "hardness": 1, "minStateId": 6983, "maxStateId": 6998, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [743], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 6983, "resistance": 1}, {"id": 401, "displayName": "<PERSON><PERSON>", "name": "cyan_banner", "hardness": 1, "minStateId": 6999, "maxStateId": 7014, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [744], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 6999, "resistance": 1}, {"id": 402, "displayName": "<PERSON> Banner", "name": "purple_banner", "hardness": 1, "minStateId": 7015, "maxStateId": 7030, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [745], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7015, "resistance": 1}, {"id": 403, "displayName": "Blue Banner", "name": "blue_banner", "hardness": 1, "minStateId": 7031, "maxStateId": 7046, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [746], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7031, "resistance": 1}, {"id": 404, "displayName": "<PERSON>", "name": "brown_banner", "hardness": 1, "minStateId": 7047, "maxStateId": 7062, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [747], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7047, "resistance": 1}, {"id": 405, "displayName": "<PERSON> Banner", "name": "green_banner", "hardness": 1, "minStateId": 7063, "maxStateId": 7078, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [748], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7063, "resistance": 1}, {"id": 406, "displayName": "Red Banner", "name": "red_banner", "hardness": 1, "minStateId": 7079, "maxStateId": 7094, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [749], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7079, "resistance": 1}, {"id": 407, "displayName": "Black Banner", "name": "black_banner", "hardness": 1, "minStateId": 7095, "maxStateId": 7110, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [750], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7095, "resistance": 1}, {"id": 408, "displayName": "White wall banner", "name": "white_wall_banner", "hardness": 1, "minStateId": 7111, "maxStateId": 7114, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [735], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7111, "resistance": 1}, {"id": 409, "displayName": "Orange wall banner", "name": "orange_wall_banner", "hardness": 1, "minStateId": 7115, "maxStateId": 7118, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [736], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7115, "resistance": 1}, {"id": 410, "displayName": "Magenta wall banner", "name": "magenta_wall_banner", "hardness": 1, "minStateId": 7119, "maxStateId": 7122, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [737], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7119, "resistance": 1}, {"id": 411, "displayName": "Light blue wall banner", "name": "light_blue_wall_banner", "hardness": 1, "minStateId": 7123, "maxStateId": 7126, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [738], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7123, "resistance": 1}, {"id": 412, "displayName": "Yellow wall banner", "name": "yellow_wall_banner", "hardness": 1, "minStateId": 7127, "maxStateId": 7130, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [739], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7127, "resistance": 1}, {"id": 413, "displayName": "Lime wall banner", "name": "lime_wall_banner", "hardness": 1, "minStateId": 7131, "maxStateId": 7134, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [740], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7131, "resistance": 1}, {"id": 414, "displayName": "Pink wall banner", "name": "pink_wall_banner", "hardness": 1, "minStateId": 7135, "maxStateId": 7138, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [741], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7135, "resistance": 1}, {"id": 415, "displayName": "Gray wall banner", "name": "gray_wall_banner", "hardness": 1, "minStateId": 7139, "maxStateId": 7142, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [742], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7139, "resistance": 1}, {"id": 416, "displayName": "Light gray wall banner", "name": "light_gray_wall_banner", "hardness": 1, "minStateId": 7143, "maxStateId": 7146, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [743], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7143, "resistance": 1}, {"id": 417, "displayName": "<PERSON>an wall banner", "name": "cyan_wall_banner", "hardness": 1, "minStateId": 7147, "maxStateId": 7150, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [744], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7147, "resistance": 1}, {"id": 418, "displayName": "Purple wall banner", "name": "purple_wall_banner", "hardness": 1, "minStateId": 7151, "maxStateId": 7154, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [745], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7151, "resistance": 1}, {"id": 419, "displayName": "Blue wall banner", "name": "blue_wall_banner", "hardness": 1, "minStateId": 7155, "maxStateId": 7158, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [746], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7155, "resistance": 1}, {"id": 420, "displayName": "<PERSON> wall banner", "name": "brown_wall_banner", "hardness": 1, "minStateId": 7159, "maxStateId": 7162, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [747], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7159, "resistance": 1}, {"id": 421, "displayName": "Green wall banner", "name": "green_wall_banner", "hardness": 1, "minStateId": 7163, "maxStateId": 7166, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [748], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7163, "resistance": 1}, {"id": 422, "displayName": "Red wall banner", "name": "red_wall_banner", "hardness": 1, "minStateId": 7167, "maxStateId": 7170, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [749], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7167, "resistance": 1}, {"id": 423, "displayName": "Black wall banner", "name": "black_wall_banner", "hardness": 1, "minStateId": 7171, "maxStateId": 7174, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [750], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7171, "resistance": 1}, {"id": 424, "displayName": "Red Sandstone", "name": "red_sandstone", "hardness": 0.8, "minStateId": 7175, "maxStateId": 7175, "states": [], "drops": [350], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7175, "resistance": 0.8}, {"id": 425, "displayName": "Chiseled Red Sandstone", "name": "chiseled_red_sandstone", "hardness": 0.8, "minStateId": 7176, "maxStateId": 7176, "states": [], "drops": [351], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7176, "resistance": 0.8}, {"id": 426, "displayName": "Cut Red Sandstone", "name": "cut_red_sandstone", "hardness": 0.8, "minStateId": 7177, "maxStateId": 7177, "states": [], "drops": [352], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7177, "resistance": 0.8}, {"id": 427, "displayName": "Red Sandstone Stairs", "name": "red_sandstone_stairs", "hardness": 0.8, "minStateId": 7178, "maxStateId": 7257, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [353], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7189, "resistance": 0.8}, {"id": 428, "displayName": "Oak Slab", "name": "oak_slab", "hardness": 2, "minStateId": 7258, "maxStateId": 7263, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [112], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7261, "resistance": 3}, {"id": 429, "displayName": "Spruce Slab", "name": "spruce_slab", "hardness": 2, "minStateId": 7264, "maxStateId": 7269, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [113], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7267, "resistance": 3}, {"id": 430, "displayName": "<PERSON>", "name": "birch_slab", "hardness": 2, "minStateId": 7270, "maxStateId": 7275, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [114], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7273, "resistance": 3}, {"id": 431, "displayName": "Jungle Slab", "name": "jungle_slab", "hardness": 2, "minStateId": 7276, "maxStateId": 7281, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [115], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7279, "resistance": 3}, {"id": 432, "displayName": "Acacia <PERSON>b", "name": "acacia_slab", "hardness": 2, "minStateId": 7282, "maxStateId": 7287, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [116], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7285, "resistance": 3}, {"id": 433, "displayName": "Dark Oak Slab", "name": "dark_oak_slab", "hardness": 2, "minStateId": 7288, "maxStateId": 7293, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [117], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7291, "resistance": 3}, {"id": 434, "displayName": "<PERSON> Slab", "name": "stone_slab", "hardness": 2, "minStateId": 7294, "maxStateId": 7299, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [118], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7297, "resistance": 6}, {"id": 435, "displayName": "Sandstone Slab", "name": "sandstone_slab", "hardness": 2, "minStateId": 7300, "maxStateId": 7305, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [119], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7303, "resistance": 6}, {"id": 436, "displayName": "Petrified Oak Slab", "name": "petrified_oak_slab", "hardness": 2, "minStateId": 7306, "maxStateId": 7311, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [120], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7309, "resistance": 6}, {"id": 437, "displayName": "Cobblestone Slab", "name": "cobblestone_slab", "hardness": 2, "minStateId": 7312, "maxStateId": 7317, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [121], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7315, "resistance": 6}, {"id": 438, "displayName": "Brick Slab", "name": "brick_slab", "hardness": 2, "minStateId": 7318, "maxStateId": 7323, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [122], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7321, "resistance": 6}, {"id": 439, "displayName": "Stone Brick Slab", "name": "stone_brick_slab", "hardness": 2, "minStateId": 7324, "maxStateId": 7329, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [123], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7327, "resistance": 6}, {"id": 440, "displayName": "Nether Brick Slab", "name": "nether_brick_slab", "hardness": 2, "minStateId": 7330, "maxStateId": 7335, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [124], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7333, "resistance": 6}, {"id": 441, "displayName": "Quartz Slab", "name": "quartz_slab", "hardness": 2, "minStateId": 7336, "maxStateId": 7341, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [125], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7339, "resistance": 6}, {"id": 442, "displayName": "Red Sandstone Slab", "name": "red_sandstone_slab", "hardness": 2, "minStateId": 7342, "maxStateId": 7347, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [126], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7345, "resistance": 6}, {"id": 443, "displayName": "Purpur Slab", "name": "purpur_slab", "hardness": 2, "minStateId": 7348, "maxStateId": 7353, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [127], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7351, "resistance": 6}, {"id": 444, "displayName": "Smooth Stone", "name": "smooth_stone", "hardness": 2, "minStateId": 7354, "maxStateId": 7354, "states": [], "drops": [134], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7354, "resistance": 6}, {"id": 445, "displayName": "Smooth Sandstone", "name": "smooth_sandstone", "hardness": 2, "minStateId": 7355, "maxStateId": 7355, "states": [], "drops": [133], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7355, "resistance": 6}, {"id": 446, "displayName": "Smooth Quartz", "name": "smooth_quartz", "hardness": 2, "minStateId": 7356, "maxStateId": 7356, "states": [], "drops": [131], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7356, "resistance": 6}, {"id": 447, "displayName": "Smooth Red Sandstone", "name": "smooth_red_sandstone", "hardness": 2, "minStateId": 7357, "maxStateId": 7357, "states": [], "drops": [132], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 7357, "resistance": 6}, {"id": 448, "displayName": "Spruce Fence Gate", "name": "spruce_fence_gate", "hardness": 2, "minStateId": 7358, "maxStateId": 7389, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "in_wall", "type": "bool", "num_values": 2}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [211], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 7365, "resistance": 3}, {"id": 449, "displayName": "Birch Fence Gate", "name": "birch_fence_gate", "hardness": 2, "minStateId": 7390, "maxStateId": 7421, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "in_wall", "type": "bool", "num_values": 2}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [212], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 7397, "resistance": 3}, {"id": 450, "displayName": "Jungle Fence Gate", "name": "jungle_fence_gate", "hardness": 2, "minStateId": 7422, "maxStateId": 7453, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "in_wall", "type": "bool", "num_values": 2}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [213], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 7429, "resistance": 3}, {"id": 451, "displayName": "Acacia Fence Gate", "name": "acacia_fence_gate", "hardness": 2, "minStateId": 7454, "maxStateId": 7485, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "in_wall", "type": "bool", "num_values": 2}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [214], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 7461, "resistance": 3}, {"id": 452, "displayName": "Dark Oak Fence Gate", "name": "dark_oak_fence_gate", "hardness": 2, "minStateId": 7486, "maxStateId": 7517, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "in_wall", "type": "bool", "num_values": 2}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [215], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 7493, "resistance": 3}, {"id": 453, "displayName": "Spruce Fence", "name": "spruce_fence", "hardness": 2, "minStateId": 7518, "maxStateId": 7549, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [176], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 7549, "resistance": 3}, {"id": 454, "displayName": "<PERSON>", "name": "birch_fence", "hardness": 2, "minStateId": 7550, "maxStateId": 7581, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [177], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 7581, "resistance": 3}, {"id": 455, "displayName": "Jungle Fence", "name": "jungle_fence", "hardness": 2, "minStateId": 7582, "maxStateId": 7613, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [178], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 7613, "resistance": 3}, {"id": 456, "displayName": "Acacia Fence", "name": "acacia_fence", "hardness": 2, "minStateId": 7614, "maxStateId": 7645, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [179], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 7645, "resistance": 3}, {"id": 457, "displayName": "Dark Oak Fence", "name": "dark_oak_fence", "hardness": 2, "minStateId": 7646, "maxStateId": 7677, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [180], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 7677, "resistance": 3}, {"id": 458, "displayName": "Spruce Door", "name": "spruce_door", "hardness": 3, "minStateId": 7678, "maxStateId": 7741, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}, {"name": "hinge", "type": "enum", "num_values": 2, "values": ["left", "right"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [462], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 7689, "resistance": 3}, {"id": 459, "displayName": "<PERSON>", "name": "birch_door", "hardness": 3, "minStateId": 7742, "maxStateId": 7805, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}, {"name": "hinge", "type": "enum", "num_values": 2, "values": ["left", "right"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [463], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 7753, "resistance": 3}, {"id": 460, "displayName": "Jungle Door", "name": "jungle_door", "hardness": 3, "minStateId": 7806, "maxStateId": 7869, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}, {"name": "hinge", "type": "enum", "num_values": 2, "values": ["left", "right"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [464], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 7817, "resistance": 3}, {"id": 461, "displayName": "Acacia Door", "name": "acacia_door", "hardness": 3, "minStateId": 7870, "maxStateId": 7933, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}, {"name": "hinge", "type": "enum", "num_values": 2, "values": ["left", "right"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [465], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 7881, "resistance": 3}, {"id": 462, "displayName": "Dark Oak Door", "name": "dark_oak_door", "hardness": 3, "minStateId": 7934, "maxStateId": 7997, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}, {"name": "hinge", "type": "enum", "num_values": 2, "values": ["left", "right"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [466], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 7945, "resistance": 3}, {"id": 463, "displayName": "End Rod", "name": "end_rod", "hardness": 0, "minStateId": 7998, "maxStateId": 8003, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [141], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 14, "boundingBox": "block", "stackSize": 64, "defaultState": 8002, "resistance": 0}, {"id": 464, "displayName": "Chorus Plant", "name": "chorus_plant", "hardness": 0.4, "minStateId": 8004, "maxStateId": 8067, "states": [{"name": "down", "type": "bool", "num_values": 2}, {"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [142], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 8067, "resistance": 0.4}, {"id": 465, "displayName": "Chorus Flower", "name": "chorus_flower", "hardness": 0.4, "minStateId": 8068, "maxStateId": 8073, "states": [{"name": "age", "type": "int", "num_values": 6}], "drops": [143], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 8068, "resistance": 0.4}, {"id": 466, "displayName": "Purpur Block", "name": "purpur_block", "hardness": 1.5, "minStateId": 8074, "maxStateId": 8074, "states": [], "drops": [144], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8074, "resistance": 6}, {"id": 467, "displayName": "Purpur Pillar", "name": "purpur_pillar", "hardness": 1.5, "minStateId": 8075, "maxStateId": 8077, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [145], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8076, "resistance": 6}, {"id": 468, "displayName": "Purpur Stairs", "name": "purpur_stairs", "hardness": 1.5, "minStateId": 8078, "maxStateId": 8157, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [146], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8089, "resistance": 6}, {"id": 469, "displayName": "End Stone Bricks", "name": "end_stone_bricks", "hardness": 0.8, "minStateId": 8158, "maxStateId": 8158, "states": [], "drops": [226], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8158, "resistance": 0.8}, {"id": 470, "displayName": "Beetroots", "name": "beetroots", "hardness": 0, "minStateId": 8159, "maxStateId": 8162, "states": [{"name": "age", "type": "int", "num_values": 4}], "drops": [754], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8159, "resistance": 0}, {"id": 471, "displayName": "Grass Path", "name": "grass_path", "hardness": 0.65, "minStateId": 8163, "maxStateId": 8163, "states": [], "drops": [304], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8163, "resistance": 0.65}, {"id": 472, "displayName": "End Gateway", "name": "end_gateway", "hardness": null, "minStateId": 8164, "maxStateId": 8164, "states": [], "drops": [], "diggable": false, "transparent": false, "filterLight": 15, "emitLight": 15, "boundingBox": "empty", "stackSize": 0, "defaultState": 8164, "resistance": 3600000}, {"id": 473, "displayName": "Repeating Command Block", "name": "repeating_command_block", "hardness": null, "minStateId": 8165, "maxStateId": 8176, "states": [{"name": "conditional", "type": "bool", "num_values": 2}, {"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [354], "diggable": false, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 8171, "resistance": 3600000}, {"id": 474, "displayName": "Chain Command Block", "name": "chain_command_block", "hardness": null, "minStateId": 8177, "maxStateId": 8188, "states": [{"name": "conditional", "type": "bool", "num_values": 2}, {"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [355], "diggable": false, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 8183, "resistance": 3600000}, {"id": 475, "displayName": "Frosted Ice", "name": "frosted_ice", "hardness": 0.5, "minStateId": 8189, "maxStateId": 8192, "states": [{"name": "age", "type": "int", "num_values": 4}], "drops": [], "diggable": true, "transparent": true, "filterLight": 2, "emitLight": 0, "boundingBox": "block", "stackSize": 0, "defaultState": 8189, "resistance": 0.5}, {"id": 476, "displayName": "Magma Block", "name": "magma_block", "hardness": 0.5, "minStateId": 8193, "maxStateId": 8193, "states": [], "drops": [356], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8193, "resistance": 0.5}, {"id": 477, "displayName": "Nether Wart Block", "name": "nether_wart_block", "hardness": 1, "minStateId": 8194, "maxStateId": 8194, "states": [], "drops": [357], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 8194, "resistance": 1}, {"id": 478, "displayName": "Red Nether Bricks", "name": "red_nether_bricks", "hardness": 2, "minStateId": 8195, "maxStateId": 8195, "states": [], "drops": [358], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8195, "resistance": 6}, {"id": 479, "displayName": "Bone Block", "name": "bone_block", "hardness": 2, "minStateId": 8196, "maxStateId": 8198, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [359], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8197, "resistance": 2}, {"id": 480, "displayName": "Structure Void", "name": "structure_void", "hardness": 0, "minStateId": 8199, "maxStateId": 8199, "states": [], "drops": [360], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8199, "resistance": 0}, {"id": 481, "displayName": "Observer", "name": "observer", "hardness": 3, "minStateId": 8200, "maxStateId": 8211, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [361], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8205, "resistance": 3}, {"id": 482, "displayName": "Shulker Box", "name": "shulker_box", "hardness": 2, "minStateId": 8212, "maxStateId": 8217, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [362], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8216, "resistance": 2}, {"id": 483, "displayName": "White Shulker Box", "name": "white_shulker_box", "hardness": 2, "minStateId": 8218, "maxStateId": 8223, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [363], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8222, "resistance": 2}, {"id": 484, "displayName": "Orange Shulker Box", "name": "orange_shulker_box", "hardness": 2, "minStateId": 8224, "maxStateId": 8229, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [364], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8228, "resistance": 2}, {"id": 485, "displayName": "<PERSON><PERSON>a <PERSON>er Box", "name": "magenta_shulker_box", "hardness": 2, "minStateId": 8230, "maxStateId": 8235, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [365], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8234, "resistance": 2}, {"id": 486, "displayName": "Light Blue Shulker Box", "name": "light_blue_shulker_box", "hardness": 2, "minStateId": 8236, "maxStateId": 8241, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [366], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8240, "resistance": 2}, {"id": 487, "displayName": "Yellow Shulker Box", "name": "yellow_shulker_box", "hardness": 2, "minStateId": 8242, "maxStateId": 8247, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [367], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8246, "resistance": 2}, {"id": 488, "displayName": "<PERSON>e <PERSON>er Box", "name": "lime_shulker_box", "hardness": 2, "minStateId": 8248, "maxStateId": 8253, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [368], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8252, "resistance": 2}, {"id": 489, "displayName": "Pink Shulker Box", "name": "pink_shulker_box", "hardness": 2, "minStateId": 8254, "maxStateId": 8259, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [369], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8258, "resistance": 2}, {"id": 490, "displayName": "<PERSON>", "name": "gray_shulker_box", "hardness": 2, "minStateId": 8260, "maxStateId": 8265, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [370], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8264, "resistance": 2}, {"id": 491, "displayName": "Light Gray Shulker Box", "name": "light_gray_shulker_box", "hardness": 2, "minStateId": 8266, "maxStateId": 8271, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [371], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8270, "resistance": 2}, {"id": 492, "displayName": "<PERSON><PERSON>", "name": "cyan_shulker_box", "hardness": 2, "minStateId": 8272, "maxStateId": 8277, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [372], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8276, "resistance": 2}, {"id": 493, "displayName": "Purple Shulker Box", "name": "purple_shulker_box", "hardness": 2, "minStateId": 8278, "maxStateId": 8283, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [373], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8282, "resistance": 2}, {"id": 494, "displayName": "Blue Shulker Box", "name": "blue_shulker_box", "hardness": 2, "minStateId": 8284, "maxStateId": 8289, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [374], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8288, "resistance": 2}, {"id": 495, "displayName": "<PERSON> Shulker Box", "name": "brown_shulker_box", "hardness": 2, "minStateId": 8290, "maxStateId": 8295, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [375], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8294, "resistance": 2}, {"id": 496, "displayName": "Green Shulker Box", "name": "green_shulker_box", "hardness": 2, "minStateId": 8296, "maxStateId": 8301, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [376], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8300, "resistance": 2}, {"id": 497, "displayName": "Red Shulker Box", "name": "red_shulker_box", "hardness": 2, "minStateId": 8302, "maxStateId": 8307, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [377], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8306, "resistance": 2}, {"id": 498, "displayName": "Black Shulker Box", "name": "black_shulker_box", "hardness": 2, "minStateId": 8308, "maxStateId": 8313, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [378], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8312, "resistance": 2}, {"id": 499, "displayName": "White Glazed Terracotta", "name": "white_glazed_terracotta", "hardness": 1.4, "minStateId": 8314, "maxStateId": 8317, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [379], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8314, "resistance": 1.4}, {"id": 500, "displayName": "Orange Glazed Terracotta", "name": "orange_glazed_terracotta", "hardness": 1.4, "minStateId": 8318, "maxStateId": 8321, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [380], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8318, "resistance": 1.4}, {"id": 501, "displayName": "Magenta Glazed Terracotta", "name": "magenta_glazed_terracotta", "hardness": 1.4, "minStateId": 8322, "maxStateId": 8325, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [381], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8322, "resistance": 1.4}, {"id": 502, "displayName": "Light Blue Glazed Terracotta", "name": "light_blue_glazed_terracotta", "hardness": 1.4, "minStateId": 8326, "maxStateId": 8329, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [382], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8326, "resistance": 1.4}, {"id": 503, "displayName": "Yellow Glazed Terracotta", "name": "yellow_glazed_terracotta", "hardness": 1.4, "minStateId": 8330, "maxStateId": 8333, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [383], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8330, "resistance": 1.4}, {"id": 504, "displayName": "Lime Glazed Terracotta", "name": "lime_glazed_terracotta", "hardness": 1.4, "minStateId": 8334, "maxStateId": 8337, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [384], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8334, "resistance": 1.4}, {"id": 505, "displayName": "Pink Glazed Terracotta", "name": "pink_glazed_terracotta", "hardness": 1.4, "minStateId": 8338, "maxStateId": 8341, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [385], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8338, "resistance": 1.4}, {"id": 506, "displayName": "Gray Glazed Terracotta", "name": "gray_glazed_terracotta", "hardness": 1.4, "minStateId": 8342, "maxStateId": 8345, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [386], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8342, "resistance": 1.4}, {"id": 507, "displayName": "Light Gray Glazed Terracotta", "name": "light_gray_glazed_terracotta", "hardness": 1.4, "minStateId": 8346, "maxStateId": 8349, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [387], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8346, "resistance": 1.4}, {"id": 508, "displayName": "<PERSON><PERSON>zed Terracotta", "name": "cyan_glazed_terracotta", "hardness": 1.4, "minStateId": 8350, "maxStateId": 8353, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [388], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8350, "resistance": 1.4}, {"id": 509, "displayName": "Purple Glazed Terracotta", "name": "purple_glazed_terracotta", "hardness": 1.4, "minStateId": 8354, "maxStateId": 8357, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [389], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8354, "resistance": 1.4}, {"id": 510, "displayName": "Blue Glazed Terracotta", "name": "blue_glazed_terracotta", "hardness": 1.4, "minStateId": 8358, "maxStateId": 8361, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [390], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8358, "resistance": 1.4}, {"id": 511, "displayName": "Brown Glazed Terracotta", "name": "brown_glazed_terracotta", "hardness": 1.4, "minStateId": 8362, "maxStateId": 8365, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [391], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8362, "resistance": 1.4}, {"id": 512, "displayName": "Green Glazed Terracotta", "name": "green_glazed_terracotta", "hardness": 1.4, "minStateId": 8366, "maxStateId": 8369, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [392], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8366, "resistance": 1.4}, {"id": 513, "displayName": "Red Glazed Terracotta", "name": "red_glazed_terracotta", "hardness": 1.4, "minStateId": 8370, "maxStateId": 8373, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [393], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8370, "resistance": 1.4}, {"id": 514, "displayName": "Black Glazed Terracotta", "name": "black_glazed_terracotta", "hardness": 1.4, "minStateId": 8374, "maxStateId": 8377, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [394], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8374, "resistance": 1.4}, {"id": 515, "displayName": "White Concrete", "name": "white_concrete", "hardness": 1.8, "minStateId": 8378, "maxStateId": 8378, "states": [], "drops": [395], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8378, "resistance": 1.8}, {"id": 516, "displayName": "Orange Concrete", "name": "orange_concrete", "hardness": 1.8, "minStateId": 8379, "maxStateId": 8379, "states": [], "drops": [396], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8379, "resistance": 1.8}, {"id": 517, "displayName": "Magenta Concrete", "name": "magenta_concrete", "hardness": 1.8, "minStateId": 8380, "maxStateId": 8380, "states": [], "drops": [397], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8380, "resistance": 1.8}, {"id": 518, "displayName": "Light Blue Concrete", "name": "light_blue_concrete", "hardness": 1.8, "minStateId": 8381, "maxStateId": 8381, "states": [], "drops": [398], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8381, "resistance": 1.8}, {"id": 519, "displayName": "Yellow Concrete", "name": "yellow_concrete", "hardness": 1.8, "minStateId": 8382, "maxStateId": 8382, "states": [], "drops": [399], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8382, "resistance": 1.8}, {"id": 520, "displayName": "Lime Concrete", "name": "lime_concrete", "hardness": 1.8, "minStateId": 8383, "maxStateId": 8383, "states": [], "drops": [400], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8383, "resistance": 1.8}, {"id": 521, "displayName": "Pink Concrete", "name": "pink_concrete", "hardness": 1.8, "minStateId": 8384, "maxStateId": 8384, "states": [], "drops": [401], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8384, "resistance": 1.8}, {"id": 522, "displayName": "<PERSON>", "name": "gray_concrete", "hardness": 1.8, "minStateId": 8385, "maxStateId": 8385, "states": [], "drops": [402], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8385, "resistance": 1.8}, {"id": 523, "displayName": "Light Gray Concrete", "name": "light_gray_concrete", "hardness": 1.8, "minStateId": 8386, "maxStateId": 8386, "states": [], "drops": [403], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8386, "resistance": 1.8}, {"id": 524, "displayName": "<PERSON><PERSON>", "name": "cyan_concrete", "hardness": 1.8, "minStateId": 8387, "maxStateId": 8387, "states": [], "drops": [404], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8387, "resistance": 1.8}, {"id": 525, "displayName": "Purple Concrete", "name": "purple_concrete", "hardness": 1.8, "minStateId": 8388, "maxStateId": 8388, "states": [], "drops": [405], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8388, "resistance": 1.8}, {"id": 526, "displayName": "Blue Concrete", "name": "blue_concrete", "hardness": 1.8, "minStateId": 8389, "maxStateId": 8389, "states": [], "drops": [406], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8389, "resistance": 1.8}, {"id": 527, "displayName": "<PERSON> Concrete", "name": "brown_concrete", "hardness": 1.8, "minStateId": 8390, "maxStateId": 8390, "states": [], "drops": [407], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8390, "resistance": 1.8}, {"id": 528, "displayName": "Green Concrete", "name": "green_concrete", "hardness": 1.8, "minStateId": 8391, "maxStateId": 8391, "states": [], "drops": [408], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8391, "resistance": 1.8}, {"id": 529, "displayName": "Red Concrete", "name": "red_concrete", "hardness": 1.8, "minStateId": 8392, "maxStateId": 8392, "states": [], "drops": [409], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8392, "resistance": 1.8}, {"id": 530, "displayName": "Black Concrete", "name": "black_concrete", "hardness": 1.8, "minStateId": 8393, "maxStateId": 8393, "states": [], "drops": [410], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8393, "resistance": 1.8}, {"id": 531, "displayName": "White Concrete Powder", "name": "white_concrete_powder", "hardness": 0.5, "minStateId": 8394, "maxStateId": 8394, "states": [], "drops": [411], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8394, "resistance": 0.5}, {"id": 532, "displayName": "Orange Concrete Powder", "name": "orange_concrete_powder", "hardness": 0.5, "minStateId": 8395, "maxStateId": 8395, "states": [], "drops": [412], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8395, "resistance": 0.5}, {"id": 533, "displayName": "Magenta Concrete Powder", "name": "magenta_concrete_powder", "hardness": 0.5, "minStateId": 8396, "maxStateId": 8396, "states": [], "drops": [413], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8396, "resistance": 0.5}, {"id": 534, "displayName": "Light Blue Concrete Powder", "name": "light_blue_concrete_powder", "hardness": 0.5, "minStateId": 8397, "maxStateId": 8397, "states": [], "drops": [414], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8397, "resistance": 0.5}, {"id": 535, "displayName": "Yellow Concrete Powder", "name": "yellow_concrete_powder", "hardness": 0.5, "minStateId": 8398, "maxStateId": 8398, "states": [], "drops": [415], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8398, "resistance": 0.5}, {"id": 536, "displayName": "Lime Concrete <PERSON>", "name": "lime_concrete_powder", "hardness": 0.5, "minStateId": 8399, "maxStateId": 8399, "states": [], "drops": [416], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8399, "resistance": 0.5}, {"id": 537, "displayName": "Pink Concrete Powder", "name": "pink_concrete_powder", "hardness": 0.5, "minStateId": 8400, "maxStateId": 8400, "states": [], "drops": [417], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8400, "resistance": 0.5}, {"id": 538, "displayName": "<PERSON> Concre<PERSON>", "name": "gray_concrete_powder", "hardness": 0.5, "minStateId": 8401, "maxStateId": 8401, "states": [], "drops": [418], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8401, "resistance": 0.5}, {"id": 539, "displayName": "Light Gray Concrete Powder", "name": "light_gray_concrete_powder", "hardness": 0.5, "minStateId": 8402, "maxStateId": 8402, "states": [], "drops": [419], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8402, "resistance": 0.5}, {"id": 540, "displayName": "<PERSON><PERSON>", "name": "cyan_concrete_powder", "hardness": 0.5, "minStateId": 8403, "maxStateId": 8403, "states": [], "drops": [420], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8403, "resistance": 0.5}, {"id": 541, "displayName": "Purple Concrete Powder", "name": "purple_concrete_powder", "hardness": 0.5, "minStateId": 8404, "maxStateId": 8404, "states": [], "drops": [421], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8404, "resistance": 0.5}, {"id": 542, "displayName": "Blue Concrete Powder", "name": "blue_concrete_powder", "hardness": 0.5, "minStateId": 8405, "maxStateId": 8405, "states": [], "drops": [422], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8405, "resistance": 0.5}, {"id": 543, "displayName": "<PERSON> Concrete <PERSON>", "name": "brown_concrete_powder", "hardness": 0.5, "minStateId": 8406, "maxStateId": 8406, "states": [], "drops": [423], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8406, "resistance": 0.5}, {"id": 544, "displayName": "Green Concrete Powder", "name": "green_concrete_powder", "hardness": 0.5, "minStateId": 8407, "maxStateId": 8407, "states": [], "drops": [424], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8407, "resistance": 0.5}, {"id": 545, "displayName": "Red Concrete Powder", "name": "red_concrete_powder", "hardness": 0.5, "minStateId": 8408, "maxStateId": 8408, "states": [], "drops": [425], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8408, "resistance": 0.5}, {"id": 546, "displayName": "Black Concrete Powder", "name": "black_concrete_powder", "hardness": 0.5, "minStateId": 8409, "maxStateId": 8409, "states": [], "drops": [426], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8409, "resistance": 0.5}, {"id": 547, "displayName": "<PERSON><PERSON><PERSON>", "name": "kelp", "hardness": 0, "minStateId": 8410, "maxStateId": 8435, "states": [{"name": "age", "type": "int", "num_values": 26}], "drops": [559], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8410, "resistance": 0}, {"id": 548, "displayName": "Kelp Plant", "name": "kelp_plant", "hardness": 0, "minStateId": 8436, "maxStateId": 8436, "states": [], "drops": [559], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8436, "resistance": 0}, {"id": 549, "displayName": "Dried Kelp Block", "name": "dried_kelp_block", "hardness": 0.5, "minStateId": 8437, "maxStateId": 8437, "states": [], "drops": [560], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 8437, "resistance": 2.5}, {"id": 550, "displayName": "Turtle Egg", "name": "turtle_egg", "hardness": 0.5, "minStateId": 8438, "maxStateId": 8449, "states": [{"name": "eggs", "type": "enum", "num_values": 4, "values": ["1", "2", "3", "4"]}, {"name": "hatch", "type": "int", "num_values": 3}], "drops": [427], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 8438, "resistance": 0.5}, {"id": 551, "displayName": "Dead Tube Coral Block", "name": "dead_tube_coral_block", "hardness": 1.5, "minStateId": 8450, "maxStateId": 8450, "states": [], "drops": [428], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8450, "resistance": 6}, {"id": 552, "displayName": "Dead Brain Coral Block", "name": "dead_brain_coral_block", "hardness": 1.5, "minStateId": 8451, "maxStateId": 8451, "states": [], "drops": [429], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8451, "resistance": 6}, {"id": 553, "displayName": "Dead Bubble Coral Block", "name": "dead_bubble_coral_block", "hardness": 1.5, "minStateId": 8452, "maxStateId": 8452, "states": [], "drops": [430], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8452, "resistance": 6}, {"id": 554, "displayName": "Dead Fire Coral Block", "name": "dead_fire_coral_block", "hardness": 1.5, "minStateId": 8453, "maxStateId": 8453, "states": [], "drops": [431], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8453, "resistance": 6}, {"id": 555, "displayName": "Dead Horn Coral Block", "name": "dead_horn_coral_block", "hardness": 1.5, "minStateId": 8454, "maxStateId": 8454, "states": [], "drops": [432], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8454, "resistance": 6}, {"id": 556, "displayName": "Tube Coral Block", "name": "tube_coral_block", "hardness": 1.5, "minStateId": 8455, "maxStateId": 8455, "states": [], "drops": [433], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8455, "resistance": 6}, {"id": 557, "displayName": "Brain <PERSON>", "name": "brain_coral_block", "hardness": 1.5, "minStateId": 8456, "maxStateId": 8456, "states": [], "drops": [434], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8456, "resistance": 6}, {"id": 558, "displayName": "Bubble Coral Block", "name": "bubble_coral_block", "hardness": 1.5, "minStateId": 8457, "maxStateId": 8457, "states": [], "drops": [435], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8457, "resistance": 6}, {"id": 559, "displayName": "Fire Coral Block", "name": "fire_coral_block", "hardness": 1.5, "minStateId": 8458, "maxStateId": 8458, "states": [], "drops": [436], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8458, "resistance": 6}, {"id": 560, "displayName": "Horn Coral Block", "name": "horn_coral_block", "hardness": 1.5, "minStateId": 8459, "maxStateId": 8459, "states": [], "drops": [437], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"473": true, "487": true, "491": true, "495": true, "502": true}, "defaultState": 8459, "resistance": 6}, {"id": 561, "displayName": "Dead Tube Coral", "name": "dead_tube_coral", "hardness": 0, "minStateId": 8460, "maxStateId": 8461, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [447], "diggable": true, "transparent": true, "stackSize": 64, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "defaultState": 8460, "resistance": 0}, {"id": 562, "displayName": "Dead Brain Coral", "name": "dead_brain_coral", "hardness": 0, "minStateId": 8462, "maxStateId": 8463, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [443], "diggable": true, "transparent": true, "stackSize": 64, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "defaultState": 8462, "resistance": 0}, {"id": 563, "displayName": "Dead Bubble Coral", "name": "dead_bubble_coral", "hardness": 0, "minStateId": 8464, "maxStateId": 8465, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [444], "diggable": true, "transparent": true, "stackSize": 64, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "defaultState": 8464, "resistance": 0}, {"id": 564, "displayName": "Dead Fire Coral", "name": "dead_fire_coral", "hardness": 0, "minStateId": 8466, "maxStateId": 8467, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [445], "diggable": true, "transparent": true, "stackSize": 64, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "defaultState": 8466, "resistance": 0}, {"id": 565, "displayName": "Dead Horn Coral", "name": "dead_horn_coral", "hardness": 0, "minStateId": 8468, "maxStateId": 8469, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [446], "diggable": true, "transparent": true, "stackSize": 64, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "defaultState": 8468, "resistance": 0}, {"id": 566, "displayName": "Tube Coral", "name": "tube_coral", "hardness": 0, "minStateId": 8470, "maxStateId": 8471, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [438], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8470, "resistance": 0}, {"id": 567, "displayName": "Brain Coral", "name": "brain_coral", "hardness": 0, "minStateId": 8472, "maxStateId": 8473, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [439], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8472, "resistance": 0}, {"id": 568, "displayName": "Bubble Coral", "name": "bubble_coral", "hardness": 0, "minStateId": 8474, "maxStateId": 8475, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [440], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8474, "resistance": 0}, {"id": 569, "displayName": "Fire Coral", "name": "fire_coral", "hardness": 0, "minStateId": 8476, "maxStateId": 8477, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [441], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8476, "resistance": 0}, {"id": 570, "displayName": "Horn Coral", "name": "horn_coral", "hardness": 0, "minStateId": 8478, "maxStateId": 8479, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [442], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8478, "resistance": 0}, {"id": 571, "displayName": "Dead Tube Coral Wall Fan", "name": "dead_tube_coral_wall_fan", "hardness": 0, "minStateId": 8480, "maxStateId": 8487, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [453], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8480, "resistance": 0}, {"id": 572, "displayName": "Dead Brain Coral Wall Fan", "name": "dead_brain_coral_wall_fan", "hardness": 0, "minStateId": 8488, "maxStateId": 8495, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [454], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8488, "resistance": 0}, {"id": 573, "displayName": "Dead Bubble Coral Wall Fan", "name": "dead_bubble_coral_wall_fan", "hardness": 0, "minStateId": 8496, "maxStateId": 8503, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [455], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8496, "resistance": 0}, {"id": 574, "displayName": "Dead Fire Coral Wall Fan", "name": "dead_fire_coral_wall_fan", "hardness": 0, "minStateId": 8504, "maxStateId": 8511, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [456], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8504, "resistance": 0}, {"id": 575, "displayName": "Dead Horn Coral Wall Fan", "name": "dead_horn_coral_wall_fan", "hardness": 0, "minStateId": 8512, "maxStateId": 8519, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [457], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8512, "resistance": 0}, {"id": 576, "displayName": "Tube Coral Wall Fan", "name": "tube_coral_wall_fan", "hardness": 0, "minStateId": 8520, "maxStateId": 8527, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [448], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8520, "resistance": 0}, {"id": 577, "displayName": "Brain <PERSON>", "name": "brain_coral_wall_fan", "hardness": 0, "minStateId": 8528, "maxStateId": 8535, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [449], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8528, "resistance": 0}, {"id": 578, "displayName": "Bubble Coral Wall Fan", "name": "bubble_coral_wall_fan", "hardness": 0, "minStateId": 8536, "maxStateId": 8543, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [450], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8536, "resistance": 0}, {"id": 579, "displayName": "Fire Coral Wall Fan", "name": "fire_coral_wall_fan", "hardness": 0, "minStateId": 8544, "maxStateId": 8551, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [451], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8544, "resistance": 0}, {"id": 580, "displayName": "Horn <PERSON> Wall Fan", "name": "horn_coral_wall_fan", "hardness": 0, "minStateId": 8552, "maxStateId": 8559, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [452], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8552, "resistance": 0}, {"id": 581, "displayName": "Dead Tube Coral Fan", "name": "dead_tube_coral_fan", "hardness": 0, "minStateId": 8560, "maxStateId": 8561, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [453], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8560, "resistance": 0}, {"id": 582, "displayName": "Dead Brain Coral Fan", "name": "dead_brain_coral_fan", "hardness": 0, "minStateId": 8562, "maxStateId": 8563, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [454], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8562, "resistance": 0}, {"id": 583, "displayName": "Dead Bubble Coral Fan", "name": "dead_bubble_coral_fan", "hardness": 0, "minStateId": 8564, "maxStateId": 8565, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [455], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8564, "resistance": 0}, {"id": 584, "displayName": "Dead Fire Coral Fan", "name": "dead_fire_coral_fan", "hardness": 0, "minStateId": 8566, "maxStateId": 8567, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [456], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8566, "resistance": 0}, {"id": 585, "displayName": "Dead Horn Coral Fan", "name": "dead_horn_coral_fan", "hardness": 0, "minStateId": 8568, "maxStateId": 8569, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [457], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8568, "resistance": 0}, {"id": 586, "displayName": "Tube Coral Fan", "name": "tube_coral_fan", "hardness": 0, "minStateId": 8570, "maxStateId": 8571, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [448], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8570, "resistance": 0}, {"id": 587, "displayName": "Brain Coral Fan", "name": "brain_coral_fan", "hardness": 0, "minStateId": 8572, "maxStateId": 8573, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [449], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8572, "resistance": 0}, {"id": 588, "displayName": "Bubble Coral Fan", "name": "bubble_coral_fan", "hardness": 0, "minStateId": 8574, "maxStateId": 8575, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [450], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8574, "resistance": 0}, {"id": 589, "displayName": "Fire Coral Fan", "name": "fire_coral_fan", "hardness": 0, "minStateId": 8576, "maxStateId": 8577, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [451], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8576, "resistance": 0}, {"id": 590, "displayName": "Horn Coral Fan", "name": "horn_coral_fan", "hardness": 0, "minStateId": 8578, "maxStateId": 8579, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [452], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8578, "resistance": 0}, {"id": 591, "displayName": "<PERSON>", "name": "sea_pickle", "hardness": 0, "minStateId": 8580, "maxStateId": 8587, "states": [{"name": "pickles", "type": "enum", "num_values": 4, "values": ["1", "2", "3", "4"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [80], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 8580, "resistance": 0}, {"id": 592, "displayName": "Blue Ice", "name": "blue_ice", "hardness": 2.8, "minStateId": 8588, "maxStateId": 8588, "states": [], "drops": [458], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 8588, "resistance": 2.8}, {"id": 593, "displayName": "Conduit", "name": "conduit", "hardness": 3, "minStateId": 8589, "maxStateId": 8590, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [459], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "defaultState": 8589, "resistance": 3}, {"id": 594, "displayName": "Void Air", "name": "void_air", "hardness": 0, "minStateId": 8591, "maxStateId": 8591, "states": [], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 0, "defaultState": 8591, "resistance": 0}, {"id": 595, "displayName": "Cave Air", "name": "cave_air", "hardness": 0, "minStateId": 8592, "maxStateId": 8592, "states": [], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 0, "defaultState": 8592, "resistance": 0}, {"id": 596, "displayName": "Bubble Column", "name": "bubble_column", "hardness": 0, "minStateId": 8593, "maxStateId": 8594, "states": [{"name": "drag", "type": "bool", "num_values": 2}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 0, "defaultState": 8593, "resistance": 0}, {"id": 597, "displayName": "Structure Block", "name": "structure_block", "hardness": null, "minStateId": 8595, "maxStateId": 8598, "states": [{"name": "mode", "type": "enum", "num_values": 4, "values": ["save", "load", "corner", "data"]}], "drops": [469], "diggable": false, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 8595, "resistance": 3600000}]