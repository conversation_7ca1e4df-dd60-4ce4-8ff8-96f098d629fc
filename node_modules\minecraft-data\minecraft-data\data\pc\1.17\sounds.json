[{"id": 0, "name": "ambient.cave"}, {"id": 1, "name": "ambient.basalt_deltas.additions"}, {"id": 2, "name": "ambient.basalt_deltas.loop"}, {"id": 3, "name": "ambient.basalt_deltas.mood"}, {"id": 4, "name": "ambient.crimson_forest.additions"}, {"id": 5, "name": "ambient.crimson_forest.loop"}, {"id": 6, "name": "ambient.crimson_forest.mood"}, {"id": 7, "name": "ambient.nether_wastes.additions"}, {"id": 8, "name": "ambient.nether_wastes.loop"}, {"id": 9, "name": "ambient.nether_wastes.mood"}, {"id": 10, "name": "ambient.soul_sand_valley.additions"}, {"id": 11, "name": "ambient.soul_sand_valley.loop"}, {"id": 12, "name": "ambient.soul_sand_valley.mood"}, {"id": 13, "name": "ambient.warped_forest.additions"}, {"id": 14, "name": "ambient.warped_forest.loop"}, {"id": 15, "name": "ambient.warped_forest.mood"}, {"id": 16, "name": "ambient.underwater.enter"}, {"id": 17, "name": "ambient.underwater.exit"}, {"id": 18, "name": "ambient.underwater.loop"}, {"id": 19, "name": "ambient.underwater.loop.additions"}, {"id": 20, "name": "ambient.underwater.loop.additions.rare"}, {"id": 21, "name": "ambient.underwater.loop.additions.ultra_rare"}, {"id": 22, "name": "block.amethyst_block.break"}, {"id": 23, "name": "block.amethyst_block.chime"}, {"id": 24, "name": "block.amethyst_block.fall"}, {"id": 25, "name": "block.amethyst_block.hit"}, {"id": 26, "name": "block.amethyst_block.place"}, {"id": 27, "name": "block.amethyst_block.step"}, {"id": 28, "name": "block.amethyst_cluster.break"}, {"id": 29, "name": "block.amethyst_cluster.fall"}, {"id": 30, "name": "block.amethyst_cluster.hit"}, {"id": 31, "name": "block.amethyst_cluster.place"}, {"id": 32, "name": "block.amethyst_cluster.step"}, {"id": 33, "name": "block.ancient_debris.break"}, {"id": 34, "name": "block.ancient_debris.step"}, {"id": 35, "name": "block.ancient_debris.place"}, {"id": 36, "name": "block.ancient_debris.hit"}, {"id": 37, "name": "block.ancient_debris.fall"}, {"id": 38, "name": "block.anvil.break"}, {"id": 39, "name": "block.anvil.destroy"}, {"id": 40, "name": "block.anvil.fall"}, {"id": 41, "name": "block.anvil.hit"}, {"id": 42, "name": "block.anvil.land"}, {"id": 43, "name": "block.anvil.place"}, {"id": 44, "name": "block.anvil.step"}, {"id": 45, "name": "block.anvil.use"}, {"id": 46, "name": "item.armor.equip_chain"}, {"id": 47, "name": "item.armor.equip_diamond"}, {"id": 48, "name": "item.armor.equip_elytra"}, {"id": 49, "name": "item.armor.equip_generic"}, {"id": 50, "name": "item.armor.equip_gold"}, {"id": 51, "name": "item.armor.equip_iron"}, {"id": 52, "name": "item.armor.equip_leather"}, {"id": 53, "name": "item.armor.equip_netherite"}, {"id": 54, "name": "item.armor.equip_turtle"}, {"id": 55, "name": "entity.armor_stand.break"}, {"id": 56, "name": "entity.armor_stand.fall"}, {"id": 57, "name": "entity.armor_stand.hit"}, {"id": 58, "name": "entity.armor_stand.place"}, {"id": 59, "name": "entity.arrow.hit"}, {"id": 60, "name": "entity.arrow.hit_player"}, {"id": 61, "name": "entity.arrow.shoot"}, {"id": 62, "name": "item.axe.strip"}, {"id": 63, "name": "item.axe.scrape"}, {"id": 64, "name": "item.axe.wax_off"}, {"id": 65, "name": "entity.axolotl.attack"}, {"id": 66, "name": "entity.axolotl.death"}, {"id": 67, "name": "entity.axolotl.hurt"}, {"id": 68, "name": "entity.axolotl.idle_air"}, {"id": 69, "name": "entity.axolotl.idle_water"}, {"id": 70, "name": "entity.axolotl.splash"}, {"id": 71, "name": "entity.axolotl.swim"}, {"id": 72, "name": "block.azalea.break"}, {"id": 73, "name": "block.azalea.fall"}, {"id": 74, "name": "block.azalea.hit"}, {"id": 75, "name": "block.azalea.place"}, {"id": 76, "name": "block.azalea.step"}, {"id": 77, "name": "block.azalea_leaves.break"}, {"id": 78, "name": "block.azalea_leaves.fall"}, {"id": 79, "name": "block.azalea_leaves.hit"}, {"id": 80, "name": "block.azalea_leaves.place"}, {"id": 81, "name": "block.azalea_leaves.step"}, {"id": 82, "name": "block.bamboo.break"}, {"id": 83, "name": "block.bamboo.fall"}, {"id": 84, "name": "block.bamboo.hit"}, {"id": 85, "name": "block.bamboo.place"}, {"id": 86, "name": "block.bamboo.step"}, {"id": 87, "name": "block.bamboo_sapling.break"}, {"id": 88, "name": "block.bamboo_sapling.hit"}, {"id": 89, "name": "block.bamboo_sapling.place"}, {"id": 90, "name": "block.barrel.close"}, {"id": 91, "name": "block.barrel.open"}, {"id": 92, "name": "block.basalt.break"}, {"id": 93, "name": "block.basalt.step"}, {"id": 94, "name": "block.basalt.place"}, {"id": 95, "name": "block.basalt.hit"}, {"id": 96, "name": "block.basalt.fall"}, {"id": 97, "name": "entity.bat.ambient"}, {"id": 98, "name": "entity.bat.death"}, {"id": 99, "name": "entity.bat.hurt"}, {"id": 100, "name": "entity.bat.loop"}, {"id": 101, "name": "entity.bat.takeoff"}, {"id": 102, "name": "block.beacon.activate"}, {"id": 103, "name": "block.beacon.ambient"}, {"id": 104, "name": "block.beacon.deactivate"}, {"id": 105, "name": "block.beacon.power_select"}, {"id": 106, "name": "entity.bee.death"}, {"id": 107, "name": "entity.bee.hurt"}, {"id": 108, "name": "entity.bee.loop_aggressive"}, {"id": 109, "name": "entity.bee.loop"}, {"id": 110, "name": "entity.bee.sting"}, {"id": 111, "name": "entity.bee.pollinate"}, {"id": 112, "name": "block.beehive.drip"}, {"id": 113, "name": "block.beehive.enter"}, {"id": 114, "name": "block.beehive.exit"}, {"id": 115, "name": "block.beehive.shear"}, {"id": 116, "name": "block.beehive.work"}, {"id": 117, "name": "block.bell.use"}, {"id": 118, "name": "block.bell.resonate"}, {"id": 119, "name": "block.big_dripleaf.break"}, {"id": 120, "name": "block.big_dripleaf.fall"}, {"id": 121, "name": "block.big_dripleaf.hit"}, {"id": 122, "name": "block.big_dripleaf.place"}, {"id": 123, "name": "block.big_dripleaf.step"}, {"id": 124, "name": "entity.blaze.ambient"}, {"id": 125, "name": "entity.blaze.burn"}, {"id": 126, "name": "entity.blaze.death"}, {"id": 127, "name": "entity.blaze.hurt"}, {"id": 128, "name": "entity.blaze.shoot"}, {"id": 129, "name": "entity.boat.paddle_land"}, {"id": 130, "name": "entity.boat.paddle_water"}, {"id": 131, "name": "block.bone_block.break"}, {"id": 132, "name": "block.bone_block.fall"}, {"id": 133, "name": "block.bone_block.hit"}, {"id": 134, "name": "block.bone_block.place"}, {"id": 135, "name": "block.bone_block.step"}, {"id": 136, "name": "item.bone_meal.use"}, {"id": 137, "name": "item.book.page_turn"}, {"id": 138, "name": "item.book.put"}, {"id": 139, "name": "block.blastfurnace.fire_crackle"}, {"id": 140, "name": "item.bottle.empty"}, {"id": 141, "name": "item.bottle.fill"}, {"id": 142, "name": "item.bottle.fill_dragonbreath"}, {"id": 143, "name": "block.brewing_stand.brew"}, {"id": 144, "name": "block.bubble_column.bubble_pop"}, {"id": 145, "name": "block.bubble_column.upwards_ambient"}, {"id": 146, "name": "block.bubble_column.upwards_inside"}, {"id": 147, "name": "block.bubble_column.whirlpool_ambient"}, {"id": 148, "name": "block.bubble_column.whirlpool_inside"}, {"id": 149, "name": "item.bucket.empty"}, {"id": 150, "name": "item.bucket.empty_axolotl"}, {"id": 151, "name": "item.bucket.empty_fish"}, {"id": 152, "name": "item.bucket.empty_lava"}, {"id": 153, "name": "item.bucket.empty_powder_snow"}, {"id": 154, "name": "item.bucket.fill"}, {"id": 155, "name": "item.bucket.fill_axolotl"}, {"id": 156, "name": "item.bucket.fill_fish"}, {"id": 157, "name": "item.bucket.fill_lava"}, {"id": 158, "name": "item.bucket.fill_powder_snow"}, {"id": 159, "name": "block.cake.add_candle"}, {"id": 160, "name": "block.calcite.break"}, {"id": 161, "name": "block.calcite.step"}, {"id": 162, "name": "block.calcite.place"}, {"id": 163, "name": "block.calcite.hit"}, {"id": 164, "name": "block.calcite.fall"}, {"id": 165, "name": "block.campfire.crackle"}, {"id": 166, "name": "block.candle.ambient"}, {"id": 167, "name": "block.candle.break"}, {"id": 168, "name": "block.candle.extinguish"}, {"id": 169, "name": "block.candle.fall"}, {"id": 170, "name": "block.candle.hit"}, {"id": 171, "name": "block.candle.place"}, {"id": 172, "name": "block.candle.step"}, {"id": 173, "name": "entity.cat.ambient"}, {"id": 174, "name": "entity.cat.stray_ambient"}, {"id": 175, "name": "entity.cat.death"}, {"id": 176, "name": "entity.cat.eat"}, {"id": 177, "name": "entity.cat.hiss"}, {"id": 178, "name": "entity.cat.beg_for_food"}, {"id": 179, "name": "entity.cat.hurt"}, {"id": 180, "name": "entity.cat.purr"}, {"id": 181, "name": "entity.cat.purreow"}, {"id": 182, "name": "block.cave_vines.break"}, {"id": 183, "name": "block.cave_vines.fall"}, {"id": 184, "name": "block.cave_vines.hit"}, {"id": 185, "name": "block.cave_vines.place"}, {"id": 186, "name": "block.cave_vines.step"}, {"id": 187, "name": "block.cave_vines.pick_berries"}, {"id": 188, "name": "block.chain.break"}, {"id": 189, "name": "block.chain.fall"}, {"id": 190, "name": "block.chain.hit"}, {"id": 191, "name": "block.chain.place"}, {"id": 192, "name": "block.chain.step"}, {"id": 193, "name": "block.chest.close"}, {"id": 194, "name": "block.chest.locked"}, {"id": 195, "name": "block.chest.open"}, {"id": 196, "name": "entity.chicken.ambient"}, {"id": 197, "name": "entity.chicken.death"}, {"id": 198, "name": "entity.chicken.egg"}, {"id": 199, "name": "entity.chicken.hurt"}, {"id": 200, "name": "entity.chicken.step"}, {"id": 201, "name": "block.chorus_flower.death"}, {"id": 202, "name": "block.chorus_flower.grow"}, {"id": 203, "name": "item.chorus_fruit.teleport"}, {"id": 204, "name": "entity.cod.ambient"}, {"id": 205, "name": "entity.cod.death"}, {"id": 206, "name": "entity.cod.flop"}, {"id": 207, "name": "entity.cod.hurt"}, {"id": 208, "name": "block.comparator.click"}, {"id": 209, "name": "block.composter.empty"}, {"id": 210, "name": "block.composter.fill"}, {"id": 211, "name": "block.composter.fill_success"}, {"id": 212, "name": "block.composter.ready"}, {"id": 213, "name": "block.conduit.activate"}, {"id": 214, "name": "block.conduit.ambient"}, {"id": 215, "name": "block.conduit.ambient.short"}, {"id": 216, "name": "block.conduit.attack.target"}, {"id": 217, "name": "block.conduit.deactivate"}, {"id": 218, "name": "block.copper.break"}, {"id": 219, "name": "block.copper.step"}, {"id": 220, "name": "block.copper.place"}, {"id": 221, "name": "block.copper.hit"}, {"id": 222, "name": "block.copper.fall"}, {"id": 223, "name": "block.coral_block.break"}, {"id": 224, "name": "block.coral_block.fall"}, {"id": 225, "name": "block.coral_block.hit"}, {"id": 226, "name": "block.coral_block.place"}, {"id": 227, "name": "block.coral_block.step"}, {"id": 228, "name": "entity.cow.ambient"}, {"id": 229, "name": "entity.cow.death"}, {"id": 230, "name": "entity.cow.hurt"}, {"id": 231, "name": "entity.cow.milk"}, {"id": 232, "name": "entity.cow.step"}, {"id": 233, "name": "entity.creeper.death"}, {"id": 234, "name": "entity.creeper.hurt"}, {"id": 235, "name": "entity.creeper.primed"}, {"id": 236, "name": "block.crop.break"}, {"id": 237, "name": "item.crop.plant"}, {"id": 238, "name": "item.crossbow.hit"}, {"id": 239, "name": "item.crossbow.loading_end"}, {"id": 240, "name": "item.crossbow.loading_middle"}, {"id": 241, "name": "item.crossbow.loading_start"}, {"id": 242, "name": "item.crossbow.quick_charge_1"}, {"id": 243, "name": "item.crossbow.quick_charge_2"}, {"id": 244, "name": "item.crossbow.quick_charge_3"}, {"id": 245, "name": "item.crossbow.shoot"}, {"id": 246, "name": "block.deepslate_bricks.break"}, {"id": 247, "name": "block.deepslate_bricks.fall"}, {"id": 248, "name": "block.deepslate_bricks.hit"}, {"id": 249, "name": "block.deepslate_bricks.place"}, {"id": 250, "name": "block.deepslate_bricks.step"}, {"id": 251, "name": "block.deepslate.break"}, {"id": 252, "name": "block.deepslate.fall"}, {"id": 253, "name": "block.deepslate.hit"}, {"id": 254, "name": "block.deepslate.place"}, {"id": 255, "name": "block.deepslate.step"}, {"id": 256, "name": "block.deepslate_tiles.break"}, {"id": 257, "name": "block.deepslate_tiles.fall"}, {"id": 258, "name": "block.deepslate_tiles.hit"}, {"id": 259, "name": "block.deepslate_tiles.place"}, {"id": 260, "name": "block.deepslate_tiles.step"}, {"id": 261, "name": "block.dispenser.dispense"}, {"id": 262, "name": "block.dispenser.fail"}, {"id": 263, "name": "block.dispenser.launch"}, {"id": 264, "name": "entity.dolphin.ambient"}, {"id": 265, "name": "entity.dolphin.ambient_water"}, {"id": 266, "name": "entity.dolphin.attack"}, {"id": 267, "name": "entity.dolphin.death"}, {"id": 268, "name": "entity.dolphin.eat"}, {"id": 269, "name": "entity.dolphin.hurt"}, {"id": 270, "name": "entity.dolphin.jump"}, {"id": 271, "name": "entity.dolphin.play"}, {"id": 272, "name": "entity.dolphin.splash"}, {"id": 273, "name": "entity.dolphin.swim"}, {"id": 274, "name": "entity.donkey.ambient"}, {"id": 275, "name": "entity.donkey.angry"}, {"id": 276, "name": "entity.donkey.chest"}, {"id": 277, "name": "entity.donkey.death"}, {"id": 278, "name": "entity.donkey.eat"}, {"id": 279, "name": "entity.donkey.hurt"}, {"id": 280, "name": "block.dripstone_block.break"}, {"id": 281, "name": "block.dripstone_block.step"}, {"id": 282, "name": "block.dripstone_block.place"}, {"id": 283, "name": "block.dripstone_block.hit"}, {"id": 284, "name": "block.dripstone_block.fall"}, {"id": 285, "name": "block.pointed_dripstone.break"}, {"id": 286, "name": "block.pointed_dripstone.step"}, {"id": 287, "name": "block.pointed_dripstone.place"}, {"id": 288, "name": "block.pointed_dripstone.hit"}, {"id": 289, "name": "block.pointed_dripstone.fall"}, {"id": 290, "name": "block.pointed_dripstone.land"}, {"id": 291, "name": "block.pointed_dripstone.drip_lava"}, {"id": 292, "name": "block.pointed_dripstone.drip_water"}, {"id": 293, "name": "block.pointed_dripstone.drip_lava_into_cauldron"}, {"id": 294, "name": "block.pointed_dripstone.drip_water_into_cauldron"}, {"id": 295, "name": "block.big_dripleaf.tilt_down"}, {"id": 296, "name": "block.big_dripleaf.tilt_up"}, {"id": 297, "name": "entity.drowned.ambient"}, {"id": 298, "name": "entity.drowned.ambient_water"}, {"id": 299, "name": "entity.drowned.death"}, {"id": 300, "name": "entity.drowned.death_water"}, {"id": 301, "name": "entity.drowned.hurt"}, {"id": 302, "name": "entity.drowned.hurt_water"}, {"id": 303, "name": "entity.drowned.shoot"}, {"id": 304, "name": "entity.drowned.step"}, {"id": 305, "name": "entity.drowned.swim"}, {"id": 306, "name": "item.dye.use"}, {"id": 307, "name": "entity.egg.throw"}, {"id": 308, "name": "entity.elder_guardian.ambient"}, {"id": 309, "name": "entity.elder_guardian.ambient_land"}, {"id": 310, "name": "entity.elder_guardian.curse"}, {"id": 311, "name": "entity.elder_guardian.death"}, {"id": 312, "name": "entity.elder_guardian.death_land"}, {"id": 313, "name": "entity.elder_guardian.flop"}, {"id": 314, "name": "entity.elder_guardian.hurt"}, {"id": 315, "name": "entity.elder_guardian.hurt_land"}, {"id": 316, "name": "item.elytra.flying"}, {"id": 317, "name": "block.enchantment_table.use"}, {"id": 318, "name": "block.ender_chest.close"}, {"id": 319, "name": "block.ender_chest.open"}, {"id": 320, "name": "entity.ender_dragon.ambient"}, {"id": 321, "name": "entity.ender_dragon.death"}, {"id": 322, "name": "entity.dragon_fireball.explode"}, {"id": 323, "name": "entity.ender_dragon.flap"}, {"id": 324, "name": "entity.ender_dragon.growl"}, {"id": 325, "name": "entity.ender_dragon.hurt"}, {"id": 326, "name": "entity.ender_dragon.shoot"}, {"id": 327, "name": "entity.ender_eye.death"}, {"id": 328, "name": "entity.ender_eye.launch"}, {"id": 329, "name": "entity.enderman.ambient"}, {"id": 330, "name": "entity.enderman.death"}, {"id": 331, "name": "entity.enderman.hurt"}, {"id": 332, "name": "entity.enderman.scream"}, {"id": 333, "name": "entity.enderman.stare"}, {"id": 334, "name": "entity.enderman.teleport"}, {"id": 335, "name": "entity.endermite.ambient"}, {"id": 336, "name": "entity.endermite.death"}, {"id": 337, "name": "entity.endermite.hurt"}, {"id": 338, "name": "entity.endermite.step"}, {"id": 339, "name": "entity.ender_pearl.throw"}, {"id": 340, "name": "block.end_gateway.spawn"}, {"id": 341, "name": "block.end_portal_frame.fill"}, {"id": 342, "name": "block.end_portal.spawn"}, {"id": 343, "name": "entity.evoker.ambient"}, {"id": 344, "name": "entity.evoker.cast_spell"}, {"id": 345, "name": "entity.evoker.celebrate"}, {"id": 346, "name": "entity.evoker.death"}, {"id": 347, "name": "entity.evoker_fangs.attack"}, {"id": 348, "name": "entity.evoker.hurt"}, {"id": 349, "name": "entity.evoker.prepare_attack"}, {"id": 350, "name": "entity.evoker.prepare_summon"}, {"id": 351, "name": "entity.evoker.prepare_wololo"}, {"id": 352, "name": "entity.experience_bottle.throw"}, {"id": 353, "name": "entity.experience_orb.pickup"}, {"id": 354, "name": "block.fence_gate.close"}, {"id": 355, "name": "block.fence_gate.open"}, {"id": 356, "name": "item.firecharge.use"}, {"id": 357, "name": "entity.firework_rocket.blast"}, {"id": 358, "name": "entity.firework_rocket.blast_far"}, {"id": 359, "name": "entity.firework_rocket.large_blast"}, {"id": 360, "name": "entity.firework_rocket.large_blast_far"}, {"id": 361, "name": "entity.firework_rocket.launch"}, {"id": 362, "name": "entity.firework_rocket.shoot"}, {"id": 363, "name": "entity.firework_rocket.twinkle"}, {"id": 364, "name": "entity.firework_rocket.twinkle_far"}, {"id": 365, "name": "block.fire.ambient"}, {"id": 366, "name": "block.fire.extinguish"}, {"id": 367, "name": "entity.fish.swim"}, {"id": 368, "name": "entity.fishing_bobber.retrieve"}, {"id": 369, "name": "entity.fishing_bobber.splash"}, {"id": 370, "name": "entity.fishing_bobber.throw"}, {"id": 371, "name": "item.flintandsteel.use"}, {"id": 372, "name": "block.flowering_azalea.break"}, {"id": 373, "name": "block.flowering_azalea.fall"}, {"id": 374, "name": "block.flowering_azalea.hit"}, {"id": 375, "name": "block.flowering_azalea.place"}, {"id": 376, "name": "block.flowering_azalea.step"}, {"id": 377, "name": "entity.fox.aggro"}, {"id": 378, "name": "entity.fox.ambient"}, {"id": 379, "name": "entity.fox.bite"}, {"id": 380, "name": "entity.fox.death"}, {"id": 381, "name": "entity.fox.eat"}, {"id": 382, "name": "entity.fox.hurt"}, {"id": 383, "name": "entity.fox.screech"}, {"id": 384, "name": "entity.fox.sleep"}, {"id": 385, "name": "entity.fox.sniff"}, {"id": 386, "name": "entity.fox.spit"}, {"id": 387, "name": "entity.fox.teleport"}, {"id": 388, "name": "block.roots.break"}, {"id": 389, "name": "block.roots.step"}, {"id": 390, "name": "block.roots.place"}, {"id": 391, "name": "block.roots.hit"}, {"id": 392, "name": "block.roots.fall"}, {"id": 393, "name": "block.furnace.fire_crackle"}, {"id": 394, "name": "entity.generic.big_fall"}, {"id": 395, "name": "entity.generic.burn"}, {"id": 396, "name": "entity.generic.death"}, {"id": 397, "name": "entity.generic.drink"}, {"id": 398, "name": "entity.generic.eat"}, {"id": 399, "name": "entity.generic.explode"}, {"id": 400, "name": "entity.generic.extinguish_fire"}, {"id": 401, "name": "entity.generic.hurt"}, {"id": 402, "name": "entity.generic.small_fall"}, {"id": 403, "name": "entity.generic.splash"}, {"id": 404, "name": "entity.generic.swim"}, {"id": 405, "name": "entity.ghast.ambient"}, {"id": 406, "name": "entity.ghast.death"}, {"id": 407, "name": "entity.ghast.hurt"}, {"id": 408, "name": "entity.ghast.scream"}, {"id": 409, "name": "entity.ghast.shoot"}, {"id": 410, "name": "entity.ghast.warn"}, {"id": 411, "name": "block.gilded_blackstone.break"}, {"id": 412, "name": "block.gilded_blackstone.fall"}, {"id": 413, "name": "block.gilded_blackstone.hit"}, {"id": 414, "name": "block.gilded_blackstone.place"}, {"id": 415, "name": "block.gilded_blackstone.step"}, {"id": 416, "name": "block.glass.break"}, {"id": 417, "name": "block.glass.fall"}, {"id": 418, "name": "block.glass.hit"}, {"id": 419, "name": "block.glass.place"}, {"id": 420, "name": "block.glass.step"}, {"id": 421, "name": "item.glow_ink_sac.use"}, {"id": 422, "name": "entity.glow_item_frame.add_item"}, {"id": 423, "name": "entity.glow_item_frame.break"}, {"id": 424, "name": "entity.glow_item_frame.place"}, {"id": 425, "name": "entity.glow_item_frame.remove_item"}, {"id": 426, "name": "entity.glow_item_frame.rotate_item"}, {"id": 427, "name": "entity.glow_squid.ambient"}, {"id": 428, "name": "entity.glow_squid.death"}, {"id": 429, "name": "entity.glow_squid.hurt"}, {"id": 430, "name": "entity.glow_squid.squirt"}, {"id": 431, "name": "entity.goat.ambient"}, {"id": 432, "name": "entity.goat.death"}, {"id": 433, "name": "entity.goat.eat"}, {"id": 434, "name": "entity.goat.hurt"}, {"id": 435, "name": "entity.goat.long_jump"}, {"id": 436, "name": "entity.goat.milk"}, {"id": 437, "name": "entity.goat.prepare_ram"}, {"id": 438, "name": "entity.goat.ram_impact"}, {"id": 439, "name": "entity.goat.screaming.ambient"}, {"id": 440, "name": "entity.goat.screaming.death"}, {"id": 441, "name": "entity.goat.screaming.eat"}, {"id": 442, "name": "entity.goat.screaming.hurt"}, {"id": 443, "name": "entity.goat.screaming.long_jump"}, {"id": 444, "name": "entity.goat.screaming.milk"}, {"id": 445, "name": "entity.goat.screaming.prepare_ram"}, {"id": 446, "name": "entity.goat.screaming.ram_impact"}, {"id": 447, "name": "entity.goat.step"}, {"id": 448, "name": "block.grass.break"}, {"id": 449, "name": "block.grass.fall"}, {"id": 450, "name": "block.grass.hit"}, {"id": 451, "name": "block.grass.place"}, {"id": 452, "name": "block.grass.step"}, {"id": 453, "name": "block.gravel.break"}, {"id": 454, "name": "block.gravel.fall"}, {"id": 455, "name": "block.gravel.hit"}, {"id": 456, "name": "block.gravel.place"}, {"id": 457, "name": "block.gravel.step"}, {"id": 458, "name": "block.grindstone.use"}, {"id": 459, "name": "entity.guardian.ambient"}, {"id": 460, "name": "entity.guardian.ambient_land"}, {"id": 461, "name": "entity.guardian.attack"}, {"id": 462, "name": "entity.guardian.death"}, {"id": 463, "name": "entity.guardian.death_land"}, {"id": 464, "name": "entity.guardian.flop"}, {"id": 465, "name": "entity.guardian.hurt"}, {"id": 466, "name": "entity.guardian.hurt_land"}, {"id": 467, "name": "block.hanging_roots.break"}, {"id": 468, "name": "block.hanging_roots.fall"}, {"id": 469, "name": "block.hanging_roots.hit"}, {"id": 470, "name": "block.hanging_roots.place"}, {"id": 471, "name": "block.hanging_roots.step"}, {"id": 472, "name": "item.hoe.till"}, {"id": 473, "name": "entity.hoglin.ambient"}, {"id": 474, "name": "entity.hoglin.angry"}, {"id": 475, "name": "entity.hoglin.attack"}, {"id": 476, "name": "entity.hoglin.converted_to_zombified"}, {"id": 477, "name": "entity.hoglin.death"}, {"id": 478, "name": "entity.hoglin.hurt"}, {"id": 479, "name": "entity.hoglin.retreat"}, {"id": 480, "name": "entity.hoglin.step"}, {"id": 481, "name": "block.honey_block.break"}, {"id": 482, "name": "block.honey_block.fall"}, {"id": 483, "name": "block.honey_block.hit"}, {"id": 484, "name": "block.honey_block.place"}, {"id": 485, "name": "block.honey_block.slide"}, {"id": 486, "name": "block.honey_block.step"}, {"id": 487, "name": "item.honeycomb.wax_on"}, {"id": 488, "name": "item.honey_bottle.drink"}, {"id": 489, "name": "entity.horse.ambient"}, {"id": 490, "name": "entity.horse.angry"}, {"id": 491, "name": "entity.horse.armor"}, {"id": 492, "name": "entity.horse.breathe"}, {"id": 493, "name": "entity.horse.death"}, {"id": 494, "name": "entity.horse.eat"}, {"id": 495, "name": "entity.horse.gallop"}, {"id": 496, "name": "entity.horse.hurt"}, {"id": 497, "name": "entity.horse.jump"}, {"id": 498, "name": "entity.horse.land"}, {"id": 499, "name": "entity.horse.saddle"}, {"id": 500, "name": "entity.horse.step"}, {"id": 501, "name": "entity.horse.step_wood"}, {"id": 502, "name": "entity.hostile.big_fall"}, {"id": 503, "name": "entity.hostile.death"}, {"id": 504, "name": "entity.hostile.hurt"}, {"id": 505, "name": "entity.hostile.small_fall"}, {"id": 506, "name": "entity.hostile.splash"}, {"id": 507, "name": "entity.hostile.swim"}, {"id": 508, "name": "entity.husk.ambient"}, {"id": 509, "name": "entity.husk.converted_to_zombie"}, {"id": 510, "name": "entity.husk.death"}, {"id": 511, "name": "entity.husk.hurt"}, {"id": 512, "name": "entity.husk.step"}, {"id": 513, "name": "entity.illusioner.ambient"}, {"id": 514, "name": "entity.illusioner.cast_spell"}, {"id": 515, "name": "entity.illusioner.death"}, {"id": 516, "name": "entity.illusioner.hurt"}, {"id": 517, "name": "entity.illusioner.mirror_move"}, {"id": 518, "name": "entity.illusioner.prepare_blindness"}, {"id": 519, "name": "entity.illusioner.prepare_mirror"}, {"id": 520, "name": "item.ink_sac.use"}, {"id": 521, "name": "block.iron_door.close"}, {"id": 522, "name": "block.iron_door.open"}, {"id": 523, "name": "entity.iron_golem.attack"}, {"id": 524, "name": "entity.iron_golem.damage"}, {"id": 525, "name": "entity.iron_golem.death"}, {"id": 526, "name": "entity.iron_golem.hurt"}, {"id": 527, "name": "entity.iron_golem.repair"}, {"id": 528, "name": "entity.iron_golem.step"}, {"id": 529, "name": "block.iron_trapdoor.close"}, {"id": 530, "name": "block.iron_trapdoor.open"}, {"id": 531, "name": "entity.item_frame.add_item"}, {"id": 532, "name": "entity.item_frame.break"}, {"id": 533, "name": "entity.item_frame.place"}, {"id": 534, "name": "entity.item_frame.remove_item"}, {"id": 535, "name": "entity.item_frame.rotate_item"}, {"id": 536, "name": "entity.item.break"}, {"id": 537, "name": "entity.item.pickup"}, {"id": 538, "name": "block.ladder.break"}, {"id": 539, "name": "block.ladder.fall"}, {"id": 540, "name": "block.ladder.hit"}, {"id": 541, "name": "block.ladder.place"}, {"id": 542, "name": "block.ladder.step"}, {"id": 543, "name": "block.lantern.break"}, {"id": 544, "name": "block.lantern.fall"}, {"id": 545, "name": "block.lantern.hit"}, {"id": 546, "name": "block.lantern.place"}, {"id": 547, "name": "block.lantern.step"}, {"id": 548, "name": "block.large_amethyst_bud.break"}, {"id": 549, "name": "block.large_amethyst_bud.place"}, {"id": 550, "name": "block.lava.ambient"}, {"id": 551, "name": "block.lava.extinguish"}, {"id": 552, "name": "block.lava.pop"}, {"id": 553, "name": "entity.leash_knot.break"}, {"id": 554, "name": "entity.leash_knot.place"}, {"id": 555, "name": "block.lever.click"}, {"id": 556, "name": "entity.lightning_bolt.impact"}, {"id": 557, "name": "entity.lightning_bolt.thunder"}, {"id": 558, "name": "entity.lingering_potion.throw"}, {"id": 559, "name": "entity.llama.ambient"}, {"id": 560, "name": "entity.llama.angry"}, {"id": 561, "name": "entity.llama.chest"}, {"id": 562, "name": "entity.llama.death"}, {"id": 563, "name": "entity.llama.eat"}, {"id": 564, "name": "entity.llama.hurt"}, {"id": 565, "name": "entity.llama.spit"}, {"id": 566, "name": "entity.llama.step"}, {"id": 567, "name": "entity.llama.swag"}, {"id": 568, "name": "entity.magma_cube.death_small"}, {"id": 569, "name": "block.lodestone.break"}, {"id": 570, "name": "block.lodestone.step"}, {"id": 571, "name": "block.lodestone.place"}, {"id": 572, "name": "block.lodestone.hit"}, {"id": 573, "name": "block.lodestone.fall"}, {"id": 574, "name": "item.lodestone_compass.lock"}, {"id": 575, "name": "entity.magma_cube.death"}, {"id": 576, "name": "entity.magma_cube.hurt"}, {"id": 577, "name": "entity.magma_cube.hurt_small"}, {"id": 578, "name": "entity.magma_cube.jump"}, {"id": 579, "name": "entity.magma_cube.squish"}, {"id": 580, "name": "entity.magma_cube.squish_small"}, {"id": 581, "name": "block.medium_amethyst_bud.break"}, {"id": 582, "name": "block.medium_amethyst_bud.place"}, {"id": 583, "name": "block.metal.break"}, {"id": 584, "name": "block.metal.fall"}, {"id": 585, "name": "block.metal.hit"}, {"id": 586, "name": "block.metal.place"}, {"id": 587, "name": "block.metal_pressure_plate.click_off"}, {"id": 588, "name": "block.metal_pressure_plate.click_on"}, {"id": 589, "name": "block.metal.step"}, {"id": 590, "name": "entity.minecart.inside.underwater"}, {"id": 591, "name": "entity.minecart.inside"}, {"id": 592, "name": "entity.minecart.riding"}, {"id": 593, "name": "entity.mooshroom.convert"}, {"id": 594, "name": "entity.mooshroom.eat"}, {"id": 595, "name": "entity.mooshroom.milk"}, {"id": 596, "name": "entity.mooshroom.suspicious_milk"}, {"id": 597, "name": "entity.mooshroom.shear"}, {"id": 598, "name": "block.moss_carpet.break"}, {"id": 599, "name": "block.moss_carpet.fall"}, {"id": 600, "name": "block.moss_carpet.hit"}, {"id": 601, "name": "block.moss_carpet.place"}, {"id": 602, "name": "block.moss_carpet.step"}, {"id": 603, "name": "block.moss.break"}, {"id": 604, "name": "block.moss.fall"}, {"id": 605, "name": "block.moss.hit"}, {"id": 606, "name": "block.moss.place"}, {"id": 607, "name": "block.moss.step"}, {"id": 608, "name": "entity.mule.ambient"}, {"id": 609, "name": "entity.mule.angry"}, {"id": 610, "name": "entity.mule.chest"}, {"id": 611, "name": "entity.mule.death"}, {"id": 612, "name": "entity.mule.eat"}, {"id": 613, "name": "entity.mule.hurt"}, {"id": 614, "name": "music.creative"}, {"id": 615, "name": "music.credits"}, {"id": 616, "name": "music_disc.11"}, {"id": 617, "name": "music_disc.13"}, {"id": 618, "name": "music_disc.blocks"}, {"id": 619, "name": "music_disc.cat"}, {"id": 620, "name": "music_disc.chirp"}, {"id": 621, "name": "music_disc.far"}, {"id": 622, "name": "music_disc.mall"}, {"id": 623, "name": "music_disc.mellohi"}, {"id": 624, "name": "music_disc.pigstep"}, {"id": 625, "name": "music_disc.stal"}, {"id": 626, "name": "music_disc.strad"}, {"id": 627, "name": "music_disc.wait"}, {"id": 628, "name": "music_disc.ward"}, {"id": 629, "name": "music.dragon"}, {"id": 630, "name": "music.end"}, {"id": 631, "name": "music.game"}, {"id": 632, "name": "music.menu"}, {"id": 633, "name": "music.nether.basalt_deltas"}, {"id": 634, "name": "music.nether.nether_wastes"}, {"id": 635, "name": "music.nether.soul_sand_valley"}, {"id": 636, "name": "music.nether.crimson_forest"}, {"id": 637, "name": "music.nether.warped_forest"}, {"id": 638, "name": "music.under_water"}, {"id": 639, "name": "block.nether_bricks.break"}, {"id": 640, "name": "block.nether_bricks.step"}, {"id": 641, "name": "block.nether_bricks.place"}, {"id": 642, "name": "block.nether_bricks.hit"}, {"id": 643, "name": "block.nether_bricks.fall"}, {"id": 644, "name": "block.nether_wart.break"}, {"id": 645, "name": "item.nether_wart.plant"}, {"id": 646, "name": "block.stem.break"}, {"id": 647, "name": "block.stem.step"}, {"id": 648, "name": "block.stem.place"}, {"id": 649, "name": "block.stem.hit"}, {"id": 650, "name": "block.stem.fall"}, {"id": 651, "name": "block.nylium.break"}, {"id": 652, "name": "block.nylium.step"}, {"id": 653, "name": "block.nylium.place"}, {"id": 654, "name": "block.nylium.hit"}, {"id": 655, "name": "block.nylium.fall"}, {"id": 656, "name": "block.nether_sprouts.break"}, {"id": 657, "name": "block.nether_sprouts.step"}, {"id": 658, "name": "block.nether_sprouts.place"}, {"id": 659, "name": "block.nether_sprouts.hit"}, {"id": 660, "name": "block.nether_sprouts.fall"}, {"id": 661, "name": "block.fungus.break"}, {"id": 662, "name": "block.fungus.step"}, {"id": 663, "name": "block.fungus.place"}, {"id": 664, "name": "block.fungus.hit"}, {"id": 665, "name": "block.fungus.fall"}, {"id": 666, "name": "block.weeping_vines.break"}, {"id": 667, "name": "block.weeping_vines.step"}, {"id": 668, "name": "block.weeping_vines.place"}, {"id": 669, "name": "block.weeping_vines.hit"}, {"id": 670, "name": "block.weeping_vines.fall"}, {"id": 671, "name": "block.wart_block.break"}, {"id": 672, "name": "block.wart_block.step"}, {"id": 673, "name": "block.wart_block.place"}, {"id": 674, "name": "block.wart_block.hit"}, {"id": 675, "name": "block.wart_block.fall"}, {"id": 676, "name": "block.netherite_block.break"}, {"id": 677, "name": "block.netherite_block.step"}, {"id": 678, "name": "block.netherite_block.place"}, {"id": 679, "name": "block.netherite_block.hit"}, {"id": 680, "name": "block.netherite_block.fall"}, {"id": 681, "name": "block.netherrack.break"}, {"id": 682, "name": "block.netherrack.step"}, {"id": 683, "name": "block.netherrack.place"}, {"id": 684, "name": "block.netherrack.hit"}, {"id": 685, "name": "block.netherrack.fall"}, {"id": 686, "name": "block.note_block.basedrum"}, {"id": 687, "name": "block.note_block.bass"}, {"id": 688, "name": "block.note_block.bell"}, {"id": 689, "name": "block.note_block.chime"}, {"id": 690, "name": "block.note_block.flute"}, {"id": 691, "name": "block.note_block.guitar"}, {"id": 692, "name": "block.note_block.harp"}, {"id": 693, "name": "block.note_block.hat"}, {"id": 694, "name": "block.note_block.pling"}, {"id": 695, "name": "block.note_block.snare"}, {"id": 696, "name": "block.note_block.xylophone"}, {"id": 697, "name": "block.note_block.iron_xylophone"}, {"id": 698, "name": "block.note_block.cow_bell"}, {"id": 699, "name": "block.note_block.didgeridoo"}, {"id": 700, "name": "block.note_block.bit"}, {"id": 701, "name": "block.note_block.banjo"}, {"id": 702, "name": "entity.ocelot.hurt"}, {"id": 703, "name": "entity.ocelot.ambient"}, {"id": 704, "name": "entity.ocelot.death"}, {"id": 705, "name": "entity.painting.break"}, {"id": 706, "name": "entity.painting.place"}, {"id": 707, "name": "entity.panda.pre_sneeze"}, {"id": 708, "name": "entity.panda.sneeze"}, {"id": 709, "name": "entity.panda.ambient"}, {"id": 710, "name": "entity.panda.death"}, {"id": 711, "name": "entity.panda.eat"}, {"id": 712, "name": "entity.panda.step"}, {"id": 713, "name": "entity.panda.cant_breed"}, {"id": 714, "name": "entity.panda.aggressive_ambient"}, {"id": 715, "name": "entity.panda.worried_ambient"}, {"id": 716, "name": "entity.panda.hurt"}, {"id": 717, "name": "entity.panda.bite"}, {"id": 718, "name": "entity.parrot.ambient"}, {"id": 719, "name": "entity.parrot.death"}, {"id": 720, "name": "entity.parrot.eat"}, {"id": 721, "name": "entity.parrot.fly"}, {"id": 722, "name": "entity.parrot.hurt"}, {"id": 723, "name": "entity.parrot.imitate.blaze"}, {"id": 724, "name": "entity.parrot.imitate.creeper"}, {"id": 725, "name": "entity.parrot.imitate.drowned"}, {"id": 726, "name": "entity.parrot.imitate.elder_guardian"}, {"id": 727, "name": "entity.parrot.imitate.ender_dragon"}, {"id": 728, "name": "entity.parrot.imitate.endermite"}, {"id": 729, "name": "entity.parrot.imitate.evoker"}, {"id": 730, "name": "entity.parrot.imitate.ghast"}, {"id": 731, "name": "entity.parrot.imitate.guardian"}, {"id": 732, "name": "entity.parrot.imitate.hoglin"}, {"id": 733, "name": "entity.parrot.imitate.husk"}, {"id": 734, "name": "entity.parrot.imitate.illusioner"}, {"id": 735, "name": "entity.parrot.imitate.magma_cube"}, {"id": 736, "name": "entity.parrot.imitate.phantom"}, {"id": 737, "name": "entity.parrot.imitate.piglin"}, {"id": 738, "name": "entity.parrot.imitate.piglin_brute"}, {"id": 739, "name": "entity.parrot.imitate.pillager"}, {"id": 740, "name": "entity.parrot.imitate.ravager"}, {"id": 741, "name": "entity.parrot.imitate.shulker"}, {"id": 742, "name": "entity.parrot.imitate.silverfish"}, {"id": 743, "name": "entity.parrot.imitate.skeleton"}, {"id": 744, "name": "entity.parrot.imitate.slime"}, {"id": 745, "name": "entity.parrot.imitate.spider"}, {"id": 746, "name": "entity.parrot.imitate.stray"}, {"id": 747, "name": "entity.parrot.imitate.vex"}, {"id": 748, "name": "entity.parrot.imitate.vindicator"}, {"id": 749, "name": "entity.parrot.imitate.witch"}, {"id": 750, "name": "entity.parrot.imitate.wither"}, {"id": 751, "name": "entity.parrot.imitate.wither_skeleton"}, {"id": 752, "name": "entity.parrot.imitate.zoglin"}, {"id": 753, "name": "entity.parrot.imitate.zombie"}, {"id": 754, "name": "entity.parrot.imitate.zombie_villager"}, {"id": 755, "name": "entity.parrot.step"}, {"id": 756, "name": "entity.phantom.ambient"}, {"id": 757, "name": "entity.phantom.bite"}, {"id": 758, "name": "entity.phantom.death"}, {"id": 759, "name": "entity.phantom.flap"}, {"id": 760, "name": "entity.phantom.hurt"}, {"id": 761, "name": "entity.phantom.swoop"}, {"id": 762, "name": "entity.pig.ambient"}, {"id": 763, "name": "entity.pig.death"}, {"id": 764, "name": "entity.pig.hurt"}, {"id": 765, "name": "entity.pig.saddle"}, {"id": 766, "name": "entity.pig.step"}, {"id": 767, "name": "entity.piglin.admiring_item"}, {"id": 768, "name": "entity.piglin.ambient"}, {"id": 769, "name": "entity.piglin.angry"}, {"id": 770, "name": "entity.piglin.celebrate"}, {"id": 771, "name": "entity.piglin.death"}, {"id": 772, "name": "entity.piglin.jealous"}, {"id": 773, "name": "entity.piglin.hurt"}, {"id": 774, "name": "entity.piglin.retreat"}, {"id": 775, "name": "entity.piglin.step"}, {"id": 776, "name": "entity.piglin.converted_to_zombified"}, {"id": 777, "name": "entity.piglin_brute.ambient"}, {"id": 778, "name": "entity.piglin_brute.angry"}, {"id": 779, "name": "entity.piglin_brute.death"}, {"id": 780, "name": "entity.piglin_brute.hurt"}, {"id": 781, "name": "entity.piglin_brute.step"}, {"id": 782, "name": "entity.piglin_brute.converted_to_zombified"}, {"id": 783, "name": "entity.pillager.ambient"}, {"id": 784, "name": "entity.pillager.celebrate"}, {"id": 785, "name": "entity.pillager.death"}, {"id": 786, "name": "entity.pillager.hurt"}, {"id": 787, "name": "block.piston.contract"}, {"id": 788, "name": "block.piston.extend"}, {"id": 789, "name": "entity.player.attack.crit"}, {"id": 790, "name": "entity.player.attack.knockback"}, {"id": 791, "name": "entity.player.attack.nodamage"}, {"id": 792, "name": "entity.player.attack.strong"}, {"id": 793, "name": "entity.player.attack.sweep"}, {"id": 794, "name": "entity.player.attack.weak"}, {"id": 795, "name": "entity.player.big_fall"}, {"id": 796, "name": "entity.player.breath"}, {"id": 797, "name": "entity.player.burp"}, {"id": 798, "name": "entity.player.death"}, {"id": 799, "name": "entity.player.hurt"}, {"id": 800, "name": "entity.player.hurt_drown"}, {"id": 801, "name": "entity.player.hurt_freeze"}, {"id": 802, "name": "entity.player.hurt_on_fire"}, {"id": 803, "name": "entity.player.hurt_sweet_berry_bush"}, {"id": 804, "name": "entity.player.levelup"}, {"id": 805, "name": "entity.player.small_fall"}, {"id": 806, "name": "entity.player.splash"}, {"id": 807, "name": "entity.player.splash.high_speed"}, {"id": 808, "name": "entity.player.swim"}, {"id": 809, "name": "entity.polar_bear.ambient"}, {"id": 810, "name": "entity.polar_bear.ambient_baby"}, {"id": 811, "name": "entity.polar_bear.death"}, {"id": 812, "name": "entity.polar_bear.hurt"}, {"id": 813, "name": "entity.polar_bear.step"}, {"id": 814, "name": "entity.polar_bear.warning"}, {"id": 815, "name": "block.polished_deepslate.break"}, {"id": 816, "name": "block.polished_deepslate.fall"}, {"id": 817, "name": "block.polished_deepslate.hit"}, {"id": 818, "name": "block.polished_deepslate.place"}, {"id": 819, "name": "block.polished_deepslate.step"}, {"id": 820, "name": "block.portal.ambient"}, {"id": 821, "name": "block.portal.travel"}, {"id": 822, "name": "block.portal.trigger"}, {"id": 823, "name": "block.powder_snow.break"}, {"id": 824, "name": "block.powder_snow.fall"}, {"id": 825, "name": "block.powder_snow.hit"}, {"id": 826, "name": "block.powder_snow.place"}, {"id": 827, "name": "block.powder_snow.step"}, {"id": 828, "name": "entity.puffer_fish.ambient"}, {"id": 829, "name": "entity.puffer_fish.blow_out"}, {"id": 830, "name": "entity.puffer_fish.blow_up"}, {"id": 831, "name": "entity.puffer_fish.death"}, {"id": 832, "name": "entity.puffer_fish.flop"}, {"id": 833, "name": "entity.puffer_fish.hurt"}, {"id": 834, "name": "entity.puffer_fish.sting"}, {"id": 835, "name": "block.pumpkin.carve"}, {"id": 836, "name": "entity.rabbit.ambient"}, {"id": 837, "name": "entity.rabbit.attack"}, {"id": 838, "name": "entity.rabbit.death"}, {"id": 839, "name": "entity.rabbit.hurt"}, {"id": 840, "name": "entity.rabbit.jump"}, {"id": 841, "name": "event.raid.horn"}, {"id": 842, "name": "entity.ravager.ambient"}, {"id": 843, "name": "entity.ravager.attack"}, {"id": 844, "name": "entity.ravager.celebrate"}, {"id": 845, "name": "entity.ravager.death"}, {"id": 846, "name": "entity.ravager.hurt"}, {"id": 847, "name": "entity.ravager.step"}, {"id": 848, "name": "entity.ravager.stunned"}, {"id": 849, "name": "entity.ravager.roar"}, {"id": 850, "name": "block.nether_gold_ore.break"}, {"id": 851, "name": "block.nether_gold_ore.fall"}, {"id": 852, "name": "block.nether_gold_ore.hit"}, {"id": 853, "name": "block.nether_gold_ore.place"}, {"id": 854, "name": "block.nether_gold_ore.step"}, {"id": 855, "name": "block.nether_ore.break"}, {"id": 856, "name": "block.nether_ore.fall"}, {"id": 857, "name": "block.nether_ore.hit"}, {"id": 858, "name": "block.nether_ore.place"}, {"id": 859, "name": "block.nether_ore.step"}, {"id": 860, "name": "block.redstone_torch.burnout"}, {"id": 861, "name": "block.respawn_anchor.ambient"}, {"id": 862, "name": "block.respawn_anchor.charge"}, {"id": 863, "name": "block.respawn_anchor.deplete"}, {"id": 864, "name": "block.respawn_anchor.set_spawn"}, {"id": 865, "name": "block.rooted_dirt.break"}, {"id": 866, "name": "block.rooted_dirt.fall"}, {"id": 867, "name": "block.rooted_dirt.hit"}, {"id": 868, "name": "block.rooted_dirt.place"}, {"id": 869, "name": "block.rooted_dirt.step"}, {"id": 870, "name": "entity.salmon.ambient"}, {"id": 871, "name": "entity.salmon.death"}, {"id": 872, "name": "entity.salmon.flop"}, {"id": 873, "name": "entity.salmon.hurt"}, {"id": 874, "name": "block.sand.break"}, {"id": 875, "name": "block.sand.fall"}, {"id": 876, "name": "block.sand.hit"}, {"id": 877, "name": "block.sand.place"}, {"id": 878, "name": "block.sand.step"}, {"id": 879, "name": "block.scaffolding.break"}, {"id": 880, "name": "block.scaffolding.fall"}, {"id": 881, "name": "block.scaffolding.hit"}, {"id": 882, "name": "block.scaffolding.place"}, {"id": 883, "name": "block.scaffolding.step"}, {"id": 884, "name": "block.sculk_sensor.clicking"}, {"id": 885, "name": "block.sculk_sensor.clicking_stop"}, {"id": 886, "name": "block.sculk_sensor.break"}, {"id": 887, "name": "block.sculk_sensor.fall"}, {"id": 888, "name": "block.sculk_sensor.hit"}, {"id": 889, "name": "block.sculk_sensor.place"}, {"id": 890, "name": "block.sculk_sensor.step"}, {"id": 891, "name": "entity.sheep.ambient"}, {"id": 892, "name": "entity.sheep.death"}, {"id": 893, "name": "entity.sheep.hurt"}, {"id": 894, "name": "entity.sheep.shear"}, {"id": 895, "name": "entity.sheep.step"}, {"id": 896, "name": "item.shield.block"}, {"id": 897, "name": "item.shield.break"}, {"id": 898, "name": "block.shroomlight.break"}, {"id": 899, "name": "block.shroomlight.step"}, {"id": 900, "name": "block.shroomlight.place"}, {"id": 901, "name": "block.shroomlight.hit"}, {"id": 902, "name": "block.shroomlight.fall"}, {"id": 903, "name": "item.shovel.flatten"}, {"id": 904, "name": "entity.shulker.ambient"}, {"id": 905, "name": "block.shulker_box.close"}, {"id": 906, "name": "block.shulker_box.open"}, {"id": 907, "name": "entity.shulker_bullet.hit"}, {"id": 908, "name": "entity.shulker_bullet.hurt"}, {"id": 909, "name": "entity.shulker.close"}, {"id": 910, "name": "entity.shulker.death"}, {"id": 911, "name": "entity.shulker.hurt"}, {"id": 912, "name": "entity.shulker.hurt_closed"}, {"id": 913, "name": "entity.shulker.open"}, {"id": 914, "name": "entity.shulker.shoot"}, {"id": 915, "name": "entity.shulker.teleport"}, {"id": 916, "name": "entity.silverfish.ambient"}, {"id": 917, "name": "entity.silverfish.death"}, {"id": 918, "name": "entity.silverfish.hurt"}, {"id": 919, "name": "entity.silverfish.step"}, {"id": 920, "name": "entity.skeleton.ambient"}, {"id": 921, "name": "entity.skeleton.converted_to_stray"}, {"id": 922, "name": "entity.skeleton.death"}, {"id": 923, "name": "entity.skeleton_horse.ambient"}, {"id": 924, "name": "entity.skeleton_horse.death"}, {"id": 925, "name": "entity.skeleton_horse.hurt"}, {"id": 926, "name": "entity.skeleton_horse.swim"}, {"id": 927, "name": "entity.skeleton_horse.ambient_water"}, {"id": 928, "name": "entity.skeleton_horse.gallop_water"}, {"id": 929, "name": "entity.skeleton_horse.jump_water"}, {"id": 930, "name": "entity.skeleton_horse.step_water"}, {"id": 931, "name": "entity.skeleton.hurt"}, {"id": 932, "name": "entity.skeleton.shoot"}, {"id": 933, "name": "entity.skeleton.step"}, {"id": 934, "name": "entity.slime.attack"}, {"id": 935, "name": "entity.slime.death"}, {"id": 936, "name": "entity.slime.hurt"}, {"id": 937, "name": "entity.slime.jump"}, {"id": 938, "name": "entity.slime.squish"}, {"id": 939, "name": "block.slime_block.break"}, {"id": 940, "name": "block.slime_block.fall"}, {"id": 941, "name": "block.slime_block.hit"}, {"id": 942, "name": "block.slime_block.place"}, {"id": 943, "name": "block.slime_block.step"}, {"id": 944, "name": "block.small_amethyst_bud.break"}, {"id": 945, "name": "block.small_amethyst_bud.place"}, {"id": 946, "name": "block.small_dripleaf.break"}, {"id": 947, "name": "block.small_dripleaf.fall"}, {"id": 948, "name": "block.small_dripleaf.hit"}, {"id": 949, "name": "block.small_dripleaf.place"}, {"id": 950, "name": "block.small_dripleaf.step"}, {"id": 951, "name": "block.soul_sand.break"}, {"id": 952, "name": "block.soul_sand.step"}, {"id": 953, "name": "block.soul_sand.place"}, {"id": 954, "name": "block.soul_sand.hit"}, {"id": 955, "name": "block.soul_sand.fall"}, {"id": 956, "name": "block.soul_soil.break"}, {"id": 957, "name": "block.soul_soil.step"}, {"id": 958, "name": "block.soul_soil.place"}, {"id": 959, "name": "block.soul_soil.hit"}, {"id": 960, "name": "block.soul_soil.fall"}, {"id": 961, "name": "particle.soul_escape"}, {"id": 962, "name": "block.spore_blossom.break"}, {"id": 963, "name": "block.spore_blossom.fall"}, {"id": 964, "name": "block.spore_blossom.hit"}, {"id": 965, "name": "block.spore_blossom.place"}, {"id": 966, "name": "block.spore_blossom.step"}, {"id": 967, "name": "entity.strider.ambient"}, {"id": 968, "name": "entity.strider.happy"}, {"id": 969, "name": "entity.strider.retreat"}, {"id": 970, "name": "entity.strider.death"}, {"id": 971, "name": "entity.strider.hurt"}, {"id": 972, "name": "entity.strider.step"}, {"id": 973, "name": "entity.strider.step_lava"}, {"id": 974, "name": "entity.strider.eat"}, {"id": 975, "name": "entity.strider.saddle"}, {"id": 976, "name": "entity.slime.death_small"}, {"id": 977, "name": "entity.slime.hurt_small"}, {"id": 978, "name": "entity.slime.jump_small"}, {"id": 979, "name": "entity.slime.squish_small"}, {"id": 980, "name": "block.smithing_table.use"}, {"id": 981, "name": "block.smoker.smoke"}, {"id": 982, "name": "entity.snowball.throw"}, {"id": 983, "name": "block.snow.break"}, {"id": 984, "name": "block.snow.fall"}, {"id": 985, "name": "entity.snow_golem.ambient"}, {"id": 986, "name": "entity.snow_golem.death"}, {"id": 987, "name": "entity.snow_golem.hurt"}, {"id": 988, "name": "entity.snow_golem.shoot"}, {"id": 989, "name": "entity.snow_golem.shear"}, {"id": 990, "name": "block.snow.hit"}, {"id": 991, "name": "block.snow.place"}, {"id": 992, "name": "block.snow.step"}, {"id": 993, "name": "entity.spider.ambient"}, {"id": 994, "name": "entity.spider.death"}, {"id": 995, "name": "entity.spider.hurt"}, {"id": 996, "name": "entity.spider.step"}, {"id": 997, "name": "entity.splash_potion.break"}, {"id": 998, "name": "entity.splash_potion.throw"}, {"id": 999, "name": "item.spyglass.use"}, {"id": 1000, "name": "item.spyglass.stop_using"}, {"id": 1001, "name": "entity.squid.ambient"}, {"id": 1002, "name": "entity.squid.death"}, {"id": 1003, "name": "entity.squid.hurt"}, {"id": 1004, "name": "entity.squid.squirt"}, {"id": 1005, "name": "block.stone.break"}, {"id": 1006, "name": "block.stone_button.click_off"}, {"id": 1007, "name": "block.stone_button.click_on"}, {"id": 1008, "name": "block.stone.fall"}, {"id": 1009, "name": "block.stone.hit"}, {"id": 1010, "name": "block.stone.place"}, {"id": 1011, "name": "block.stone_pressure_plate.click_off"}, {"id": 1012, "name": "block.stone_pressure_plate.click_on"}, {"id": 1013, "name": "block.stone.step"}, {"id": 1014, "name": "entity.stray.ambient"}, {"id": 1015, "name": "entity.stray.death"}, {"id": 1016, "name": "entity.stray.hurt"}, {"id": 1017, "name": "entity.stray.step"}, {"id": 1018, "name": "block.sweet_berry_bush.break"}, {"id": 1019, "name": "block.sweet_berry_bush.place"}, {"id": 1020, "name": "block.sweet_berry_bush.pick_berries"}, {"id": 1021, "name": "enchant.thorns.hit"}, {"id": 1022, "name": "entity.tnt.primed"}, {"id": 1023, "name": "item.totem.use"}, {"id": 1024, "name": "item.trident.hit"}, {"id": 1025, "name": "item.trident.hit_ground"}, {"id": 1026, "name": "item.trident.return"}, {"id": 1027, "name": "item.trident.riptide_1"}, {"id": 1028, "name": "item.trident.riptide_2"}, {"id": 1029, "name": "item.trident.riptide_3"}, {"id": 1030, "name": "item.trident.throw"}, {"id": 1031, "name": "item.trident.thunder"}, {"id": 1032, "name": "block.tripwire.attach"}, {"id": 1033, "name": "block.tripwire.click_off"}, {"id": 1034, "name": "block.tripwire.click_on"}, {"id": 1035, "name": "block.tripwire.detach"}, {"id": 1036, "name": "entity.tropical_fish.ambient"}, {"id": 1037, "name": "entity.tropical_fish.death"}, {"id": 1038, "name": "entity.tropical_fish.flop"}, {"id": 1039, "name": "entity.tropical_fish.hurt"}, {"id": 1040, "name": "block.tuff.break"}, {"id": 1041, "name": "block.tuff.step"}, {"id": 1042, "name": "block.tuff.place"}, {"id": 1043, "name": "block.tuff.hit"}, {"id": 1044, "name": "block.tuff.fall"}, {"id": 1045, "name": "entity.turtle.ambient_land"}, {"id": 1046, "name": "entity.turtle.death"}, {"id": 1047, "name": "entity.turtle.death_baby"}, {"id": 1048, "name": "entity.turtle.egg_break"}, {"id": 1049, "name": "entity.turtle.egg_crack"}, {"id": 1050, "name": "entity.turtle.egg_hatch"}, {"id": 1051, "name": "entity.turtle.hurt"}, {"id": 1052, "name": "entity.turtle.hurt_baby"}, {"id": 1053, "name": "entity.turtle.lay_egg"}, {"id": 1054, "name": "entity.turtle.shamble"}, {"id": 1055, "name": "entity.turtle.shamble_baby"}, {"id": 1056, "name": "entity.turtle.swim"}, {"id": 1057, "name": "ui.button.click"}, {"id": 1058, "name": "ui.loom.select_pattern"}, {"id": 1059, "name": "ui.loom.take_result"}, {"id": 1060, "name": "ui.cartography_table.take_result"}, {"id": 1061, "name": "ui.stonecutter.take_result"}, {"id": 1062, "name": "ui.stonecutter.select_recipe"}, {"id": 1063, "name": "ui.toast.challenge_complete"}, {"id": 1064, "name": "ui.toast.in"}, {"id": 1065, "name": "ui.toast.out"}, {"id": 1066, "name": "entity.vex.ambient"}, {"id": 1067, "name": "entity.vex.charge"}, {"id": 1068, "name": "entity.vex.death"}, {"id": 1069, "name": "entity.vex.hurt"}, {"id": 1070, "name": "entity.villager.ambient"}, {"id": 1071, "name": "entity.villager.celebrate"}, {"id": 1072, "name": "entity.villager.death"}, {"id": 1073, "name": "entity.villager.hurt"}, {"id": 1074, "name": "entity.villager.no"}, {"id": 1075, "name": "entity.villager.trade"}, {"id": 1076, "name": "entity.villager.yes"}, {"id": 1077, "name": "entity.villager.work_armorer"}, {"id": 1078, "name": "entity.villager.work_butcher"}, {"id": 1079, "name": "entity.villager.work_cartographer"}, {"id": 1080, "name": "entity.villager.work_cleric"}, {"id": 1081, "name": "entity.villager.work_farmer"}, {"id": 1082, "name": "entity.villager.work_fisherman"}, {"id": 1083, "name": "entity.villager.work_fletcher"}, {"id": 1084, "name": "entity.villager.work_leatherworker"}, {"id": 1085, "name": "entity.villager.work_librarian"}, {"id": 1086, "name": "entity.villager.work_mason"}, {"id": 1087, "name": "entity.villager.work_shepherd"}, {"id": 1088, "name": "entity.villager.work_toolsmith"}, {"id": 1089, "name": "entity.villager.work_weaponsmith"}, {"id": 1090, "name": "entity.vindicator.ambient"}, {"id": 1091, "name": "entity.vindicator.celebrate"}, {"id": 1092, "name": "entity.vindicator.death"}, {"id": 1093, "name": "entity.vindicator.hurt"}, {"id": 1094, "name": "block.vine.break"}, {"id": 1095, "name": "block.vine.fall"}, {"id": 1096, "name": "block.vine.hit"}, {"id": 1097, "name": "block.vine.place"}, {"id": 1098, "name": "block.vine.step"}, {"id": 1099, "name": "block.lily_pad.place"}, {"id": 1100, "name": "entity.wandering_trader.ambient"}, {"id": 1101, "name": "entity.wandering_trader.death"}, {"id": 1102, "name": "entity.wandering_trader.disappeared"}, {"id": 1103, "name": "entity.wandering_trader.drink_milk"}, {"id": 1104, "name": "entity.wandering_trader.drink_potion"}, {"id": 1105, "name": "entity.wandering_trader.hurt"}, {"id": 1106, "name": "entity.wandering_trader.no"}, {"id": 1107, "name": "entity.wandering_trader.reappeared"}, {"id": 1108, "name": "entity.wandering_trader.trade"}, {"id": 1109, "name": "entity.wandering_trader.yes"}, {"id": 1110, "name": "block.water.ambient"}, {"id": 1111, "name": "weather.rain"}, {"id": 1112, "name": "weather.rain.above"}, {"id": 1113, "name": "block.wet_grass.break"}, {"id": 1114, "name": "block.wet_grass.fall"}, {"id": 1115, "name": "block.wet_grass.hit"}, {"id": 1116, "name": "block.wet_grass.place"}, {"id": 1117, "name": "block.wet_grass.step"}, {"id": 1118, "name": "entity.witch.ambient"}, {"id": 1119, "name": "entity.witch.celebrate"}, {"id": 1120, "name": "entity.witch.death"}, {"id": 1121, "name": "entity.witch.drink"}, {"id": 1122, "name": "entity.witch.hurt"}, {"id": 1123, "name": "entity.witch.throw"}, {"id": 1124, "name": "entity.wither.ambient"}, {"id": 1125, "name": "entity.wither.break_block"}, {"id": 1126, "name": "entity.wither.death"}, {"id": 1127, "name": "entity.wither.hurt"}, {"id": 1128, "name": "entity.wither.shoot"}, {"id": 1129, "name": "entity.wither_skeleton.ambient"}, {"id": 1130, "name": "entity.wither_skeleton.death"}, {"id": 1131, "name": "entity.wither_skeleton.hurt"}, {"id": 1132, "name": "entity.wither_skeleton.step"}, {"id": 1133, "name": "entity.wither.spawn"}, {"id": 1134, "name": "entity.wolf.ambient"}, {"id": 1135, "name": "entity.wolf.death"}, {"id": 1136, "name": "entity.wolf.growl"}, {"id": 1137, "name": "entity.wolf.howl"}, {"id": 1138, "name": "entity.wolf.hurt"}, {"id": 1139, "name": "entity.wolf.pant"}, {"id": 1140, "name": "entity.wolf.shake"}, {"id": 1141, "name": "entity.wolf.step"}, {"id": 1142, "name": "entity.wolf.whine"}, {"id": 1143, "name": "block.wooden_door.close"}, {"id": 1144, "name": "block.wooden_door.open"}, {"id": 1145, "name": "block.wooden_trapdoor.close"}, {"id": 1146, "name": "block.wooden_trapdoor.open"}, {"id": 1147, "name": "block.wood.break"}, {"id": 1148, "name": "block.wooden_button.click_off"}, {"id": 1149, "name": "block.wooden_button.click_on"}, {"id": 1150, "name": "block.wood.fall"}, {"id": 1151, "name": "block.wood.hit"}, {"id": 1152, "name": "block.wood.place"}, {"id": 1153, "name": "block.wooden_pressure_plate.click_off"}, {"id": 1154, "name": "block.wooden_pressure_plate.click_on"}, {"id": 1155, "name": "block.wood.step"}, {"id": 1156, "name": "block.wool.break"}, {"id": 1157, "name": "block.wool.fall"}, {"id": 1158, "name": "block.wool.hit"}, {"id": 1159, "name": "block.wool.place"}, {"id": 1160, "name": "block.wool.step"}, {"id": 1161, "name": "entity.zoglin.ambient"}, {"id": 1162, "name": "entity.zoglin.angry"}, {"id": 1163, "name": "entity.zoglin.attack"}, {"id": 1164, "name": "entity.zoglin.death"}, {"id": 1165, "name": "entity.zoglin.hurt"}, {"id": 1166, "name": "entity.zoglin.step"}, {"id": 1167, "name": "entity.zombie.ambient"}, {"id": 1168, "name": "entity.zombie.attack_wooden_door"}, {"id": 1169, "name": "entity.zombie.attack_iron_door"}, {"id": 1170, "name": "entity.zombie.break_wooden_door"}, {"id": 1171, "name": "entity.zombie.converted_to_drowned"}, {"id": 1172, "name": "entity.zombie.death"}, {"id": 1173, "name": "entity.zombie.destroy_egg"}, {"id": 1174, "name": "entity.zombie_horse.ambient"}, {"id": 1175, "name": "entity.zombie_horse.death"}, {"id": 1176, "name": "entity.zombie_horse.hurt"}, {"id": 1177, "name": "entity.zombie.hurt"}, {"id": 1178, "name": "entity.zombie.infect"}, {"id": 1179, "name": "entity.zombified_piglin.ambient"}, {"id": 1180, "name": "entity.zombified_piglin.angry"}, {"id": 1181, "name": "entity.zombified_piglin.death"}, {"id": 1182, "name": "entity.zombified_piglin.hurt"}, {"id": 1183, "name": "entity.zombie.step"}, {"id": 1184, "name": "entity.zombie_villager.ambient"}, {"id": 1185, "name": "entity.zombie_villager.converted"}, {"id": 1186, "name": "entity.zombie_villager.cure"}, {"id": 1187, "name": "entity.zombie_villager.death"}, {"id": 1188, "name": "entity.zombie_villager.hurt"}, {"id": 1189, "name": "entity.zombie_villager.step"}]