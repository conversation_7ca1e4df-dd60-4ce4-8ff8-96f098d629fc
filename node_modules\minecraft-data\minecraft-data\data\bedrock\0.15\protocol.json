{"types": {"uuid": "native", "shapelessRecipe": "native", "shapedRecipe": "native", "furnaceRecipe": "native", "furnaceRecipeData": "native", "enchantList": "native", "restBuffer": "native", "ipAddress": "native", "string": ["pstring", {"countType": "i16"}], "lstring": ["pstring", {"countType": "li16"}], "shapeless_recipe": ["container", [{"name": "ingredientList", "type": ["array", {"countType": "i32", "type": "slot"}]}, {"name": "resultCount", "type": "i32"}, {"name": "slot", "type": "slot"}, {"name": "id", "type": "uuid"}]], "shaped_recipe": ["container", [{"name": "width", "type": ["count", {"type": "i32", "countFor": "shape"}]}, {"name": "height", "type": ["count", {"type": "i32", "countFor": "shape/0"}]}, {"name": "shape", "type": ["array", {"count": "width", "type": ["array", {"count": "height", "type": "slot"}]}]}, {"name": "resultCount", "type": "i32"}, {"name": "slot", "type": "slot"}, {"name": "id", "type": "uuid"}]], "furnace_recipe": ["container", [{"name": "meta", "type": "i16"}, {"name": "id", "type": "i16"}, {"name": "result", "type": "slot"}]], "furnace_recipe_data": ["container", [{"name": "id", "type": "i16"}, {"name": "meta", "type": "i16"}, {"name": "result", "type": "slot"}]], "enchant_list": ["array", {"countType": "i8", "type": ["container", [{"name": "cost", "type": "i32"}, {"name": "enchantments", "type": ["array", {"countType": "i8", "type": ["container", [{"name": "id", "type": "i32"}, {"name": "level", "type": "i32"}]]}]}, {"name": "name", "type": "string"}]]}], "entityMetadataItem": ["switch", {"compareTo": "$compareTo", "fields": {"0": "li8", "1": "li16", "2": "li32", "3": "lf32", "4": "lstring", "5": ["container", [{"name": "blockId", "type": "li16"}, {"name": "itemCount", "type": "li8"}, {"name": "itemDamage", "type": "li16"}]], "6": ["container", [{"name": "x", "type": "li32"}, {"name": "y", "type": "li32"}, {"name": "z", "type": "li32"}]], "7": ["container", [{"name": "pitch", "type": "lf32"}, {"name": "yaw", "type": "lf32"}, {"name": "roll", "type": "lf32"}]], "8": "li64"}}], "metadatadictionary": ["entityMetadataLoop", {"endVal": 127, "type": ["container", [{"anon": true, "type": ["bitfield", [{"name": "type", "size": 3, "signed": false}, {"name": "key", "size": 5, "signed": false}]]}, {"name": "value", "type": ["entityMetadataItem", {"compareTo": "type"}]}]]}], "slot": ["container", [{"name": "blockId", "type": "i16"}, {"anon": true, "type": ["switch", {"compareTo": "blockId", "fields": {"0": "void"}, "default": ["container", [{"name": "itemCount", "type": "i8"}, {"name": "itemDamage", "type": "i16"}, {"name": "nbtData", "type": ["buffer", {"countType": "li16"}]}]]}]}]], "itemstacks": ["array", {"countType": "i16", "type": ["container", [{"name": "slot", "type": "itemstacks"}]]}], "skin": ["container", [{"name": "skin_type", "type": "string"}, {"name": "texture", "type": ["buffer", {"countType": "i16"}]}]], "blockrecords": ["array", {"countType": "i32", "type": ["container", [{"name": "x", "type": "i32"}, {"name": "z", "type": "i32"}, {"name": "y", "type": "i8"}, {"name": "blockId", "type": "i8"}, {"anon": true, "type": ["bitfield", [{"name": "blockData", "size": 4, "signed": false}, {"name": "flags", "size": 4, "signed": false}]]}]]}], "records": ["array", {"countType": "i16", "type": ["container", [{"name": "x", "type": "i8"}, {"name": "y", "type": "i8"}, {"name": "z", "type": "i8"}]]}], "playerattributes": ["array", {"countType": "i16", "type": ["container", [{"name": "minValue", "type": "f32"}, {"name": "maxValue", "type": "f32"}, {"name": "id", "type": "f32"}, {"name": "name", "type": "string"}]]}], "entitymotions": ["array", {"countType": "i16", "type": ["container", [{"name": "eid", "type": "i64"}, {"name": "motX", "type": "f32"}, {"name": "motY", "type": "f32"}, {"name": "motZ", "type": "f32"}]]}], "vector3": ["container", [{"name": "x", "type": "f32"}, {"name": "y", "type": "f32"}, {"name": "z", "type": "f32"}]], "blockcoordinates": ["container", [{"name": "x", "type": "i32"}, {"name": "y", "type": "i32"}, {"name": "z", "type": "i32"}]], "entitylocations": ["array", {"countType": "i16", "type": ["container", [{"name": "eid", "type": "i64"}, {"name": "x", "type": "f32"}, {"name": "y", "type": "f32"}, {"name": "z", "type": "f32"}, {"name": "yaw", "type": "f32"}, {"name": "headYaw", "type": "f32"}, {"name": "pitch", "type": "f32"}]]}], "encapsulated_packet": ["container", [{"name": "name", "type": ["mapper", {"type": "u8", "mappings": {"0xfe": "mcpe"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"mcpe": "mcpe_packet"}}]}]], "mcpe_packet": ["container", [{"name": "name", "type": ["mapper", {"type": "u8", "mappings": {"0x03": "server_to_client_handshake", "0x04": "client_to_server_handshake", "0x01": "game_login", "0x02": "player_status", "0x05": "disconnect", "0x06": "batch", "0x07": "text", "0x08": "set_time", "0x09": "start_game", "0x0a": "add_player", "0x0b": "remove_player", "0x0c": "add_entity", "0x0d": "remove_entity", "0x0e": "add_item_entity", "0x0f": "take_item_entity", "0x10": "move_entity", "0x11": "move_player", "0x12": "remove_block", "0x13": "update_block", "0x14": "add_painting", "0x15": "explode", "0x16": "level_event", "0x17": "tile_event", "0x18": "entity_event", "0x19": "mob_effect", "0x1a": "update_attributes", "0x1b": "player_equipment", "0x1c": "player_armor_equipment", "0x1d": "interact", "0x1e": "use_item", "0x1f": "player_action", "0x20": "hurt_armor", "0x21": "set_entity_data", "0x22": "set_entity_motion", "0x23": "set_entity_link", "0x24": "set_health", "0x25": "set_spawn_position", "0x26": "animate", "0x27": "respawn", "0x28": "drop_item", "0x29": "container_open", "0x2a": "container_close", "0x2b": "container_set_slot", "0x2c": "container_set_data", "0x2d": "container_set_content", "0x2e": "crafting_data", "0x2f": "crafting_event", "0x30": "adventure_settings", "0x31": "tile_entity_data", "0x32": "player_input", "0x33": "full_chunk_data", "0x34": "set_difficulty", "0x37": "player_list", "0x3c": "request_chunk_radius", "0x3d": "chunk_radius_update", "0x3a": "spawn_experience_orb", "0x3f": "replace_selected_item"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"server_to_client_handshake": "packet_server_to_client_handshake", "client_to_server_handshake": "packet_client_to_server_handshake", "game_login": "packet_game_login", "player_status": "packet_player_status", "disconnect": "packet_disconnect", "batch": "packet_batch", "text": "packet_text", "set_time": "packet_set_time", "start_game": "packet_start_game", "add_player": "packet_add_player", "remove_player": "packet_remove_player", "add_entity": "packet_add_entity", "remove_entity": "packet_remove_entity", "add_item_entity": "packet_add_item_entity", "take_item_entity": "packet_take_item_entity", "move_entity": "packet_move_entity", "move_player": "packet_move_player", "remove_block": "packet_remove_block", "update_block": "packet_update_block", "add_painting": "packet_add_painting", "explode": "packet_explode", "level_event": "packet_level_event", "tile_event": "packet_tile_event", "entity_event": "packet_entity_event", "mob_effect": "packet_mob_effect", "update_attributes": "packet_update_attributes", "player_equipment": "packet_player_equipment", "player_armor_equipment": "packet_player_armor_equipment", "interact": "packet_interact", "use_item": "packet_use_item", "player_action": "packet_player_action", "hurt_armor": "packet_hurt_armor", "set_entity_data": "packet_set_entity_data", "set_entity_motion": "packet_set_entity_motion", "set_entity_link": "packet_set_entity_link", "set_health": "packet_set_health", "set_spawn_position": "packet_set_spawn_position", "animate": "packet_animate", "respawn": "packet_respawn", "drop_item": "packet_drop_item", "container_open": "packet_container_open", "container_close": "packet_container_close", "container_set_slot": "packet_container_set_slot", "container_set_data": "packet_container_set_data", "container_set_content": "packet_container_set_content", "crafting_data": "packet_crafting_data", "crafting_event": "packet_crafting_event", "adventure_settings": "packet_adventure_settings", "tile_entity_data": "packet_tile_entity_data", "full_chunk_data": "packet_full_chunk_data", "set_difficulty": "packet_set_difficulty", "player_list": "packet_player_list", "request_chunk_radius": "packet_request_chunk_radius", "chunk_radius_update": "packet_chunk_radius_update", "transfer": "packet_transfer", "spawn_experience_orb": "packet_spawn_experience_orb", "replace_selected_item": "packet_replace_selected_item"}}]}]], "packet_id_detect_lost_connections": ["container", []], "packet_id_no_free_incoming_connections": ["container", []], "packet_id_connection_banned": ["container", []], "packet_id_ip_recently_connected": ["container", []], "packet_server_to_client_handshake": ["container", [{"name": "public<PERSON>ey", "type": "string"}, {"name": "serverToken", "type": "string"}]], "packet_client_to_server_handshake": ["container", []], "packet_game_login": ["container", [{"name": "protocol", "type": "i32"}, {"name": "body", "type": ["buffer", {"countType": "i32"}]}]], "packet_player_status": ["container", [{"name": "status", "type": "i32"}]], "packet_disconnect": ["container", [{"name": "message", "type": "string"}]], "packet_batch": ["container", [{"name": "payload", "type": ["buffer", {"countType": "i32"}]}]], "packet_text": ["container", [{"name": "type", "type": "i8"}, {"name": "name", "type": ["switch", {"compareTo": "type", "fields": {"1": "string", "3": "string"}, "default": "void"}]}, {"name": "message", "type": ["switch", {"compareTo": "type", "fields": {"0": "string", "1": "string", "2": "string", "3": "string", "4": "string", "5": "string"}}]}, {"name": "parameters", "type": ["switch", {"compareTo": "type", "fields": {"2": ["array", {"countType": "i8", "type": "string"}]}, "default": "void"}]}]], "packet_set_time": ["container", [{"name": "time", "type": "i32"}, {"name": "started", "type": "i8"}]], "packet_start_game": ["container", [{"name": "seed", "type": "i32"}, {"name": "dimension", "type": "i8"}, {"name": "generator", "type": "i32"}, {"name": "gamemode", "type": "i8"}, {"name": "entityId", "type": "i64"}, {"name": "spawnX", "type": "i32"}, {"name": "spawnY", "type": "i32"}, {"name": "spawnZ", "type": "i32"}, {"name": "x", "type": "f32"}, {"name": "y", "type": "f32"}, {"name": "z", "type": "f32"}, {"name": "isLoadedInCreative", "type": "bool"}, {"name": "dayCycleStopTime", "type": "i8"}, {"name": "eduMode", "type": "bool"}, {"name": "worldName", "type": "string"}]], "packet_add_player": ["container", [{"name": "uuid", "type": "uuid"}, {"name": "username", "type": "string"}, {"name": "entityId", "type": "i64"}, {"name": "x", "type": "f32"}, {"name": "y", "type": "f32"}, {"name": "z", "type": "f32"}, {"name": "speedX", "type": "f32"}, {"name": "speedY", "type": "f32"}, {"name": "speedZ", "type": "f32"}, {"name": "yaw", "type": "f32"}, {"name": "headYaw", "type": "f32"}, {"name": "pitch", "type": "f32"}, {"name": "item", "type": "slot"}, {"name": "metadata", "type": "metadatadictionary"}]], "packet_remove_player": ["container", [{"name": "entityId", "type": "i64"}, {"name": "clientUuid", "type": "uuid"}]], "packet_add_entity": ["container", [{"name": "entityId", "type": "i64"}, {"name": "entityType", "type": "i32"}, {"name": "x", "type": "f32"}, {"name": "y", "type": "f32"}, {"name": "z", "type": "f32"}, {"name": "speedX", "type": "f32"}, {"name": "speedY", "type": "f32"}, {"name": "speedZ", "type": "f32"}, {"name": "yaw", "type": "f32"}, {"name": "pitch", "type": "f32"}, {"name": "metadata", "type": "metadatadictionary"}, {"name": "links", "type": "i16"}]], "packet_remove_entity": ["container", [{"name": "entityId", "type": "i64"}]], "packet_add_item_entity": ["container", [{"name": "entityId", "type": "i64"}, {"name": "item", "type": "slot"}, {"name": "x", "type": "f32"}, {"name": "y", "type": "f32"}, {"name": "z", "type": "f32"}, {"name": "speedX", "type": "f32"}, {"name": "speedY", "type": "f32"}, {"name": "speedZ", "type": "f32"}]], "packet_take_item_entity": ["container", [{"name": "target", "type": "i64"}, {"name": "entityId", "type": "i64"}]], "packet_move_entity": ["container", [{"name": "entities", "type": "entitylocations"}]], "packet_move_player": ["container", [{"name": "entityId", "type": "i64"}, {"name": "x", "type": "f32"}, {"name": "y", "type": "f32"}, {"name": "z", "type": "f32"}, {"name": "yaw", "type": "f32"}, {"name": "headYaw", "type": "f32"}, {"name": "pitch", "type": "f32"}, {"name": "mode", "type": "i8"}, {"name": "onGround", "type": "i8"}]], "packet_remove_block": ["container", [{"name": "entityId", "type": "i64"}, {"name": "x", "type": "i32"}, {"name": "z", "type": "i32"}, {"name": "y", "type": "i8"}]], "packet_update_block": ["container", [{"name": "blocks", "type": "blockrecords"}]], "packet_add_painting": ["container", [{"name": "entityId", "type": "i64"}, {"name": "x", "type": "i32"}, {"name": "y", "type": "i32"}, {"name": "z", "type": "i32"}, {"name": "direction", "type": "i32"}, {"name": "title", "type": "string"}]], "packet_explode": ["container", [{"name": "x", "type": "f32"}, {"name": "y", "type": "f32"}, {"name": "z", "type": "f32"}, {"name": "radius", "type": "f32"}, {"name": "records", "type": "records"}]], "packet_level_event": ["container", [{"name": "eventId", "type": "i16"}, {"name": "x", "type": "f32"}, {"name": "y", "type": "f32"}, {"name": "z", "type": "f32"}, {"name": "data", "type": "i32"}]], "packet_tile_event": ["container", [{"name": "x", "type": "i32"}, {"name": "y", "type": "i32"}, {"name": "z", "type": "i32"}, {"name": "case1", "type": "i32"}, {"name": "case2", "type": "i32"}]], "packet_entity_event": ["container", [{"name": "entityId", "type": "i64"}, {"name": "eventId", "type": "i8"}]], "packet_mob_effect": ["container", [{"name": "entityId", "type": "i64"}, {"name": "eventId", "type": "i8"}, {"name": "effectId", "type": "i8"}, {"name": "amplifier", "type": "i8"}, {"name": "particles", "type": "i8"}, {"name": "duration", "type": "i32"}]], "packet_update_attributes": ["container", [{"name": "entityId", "type": "i64"}, {"name": "attributes", "type": "playerattributes"}]], "packet_player_equipment": ["container", [{"name": "entityId", "type": "i64"}, {"name": "item", "type": "slot"}, {"name": "slot", "type": "i8"}, {"name": "selectedSlot", "type": "i8"}]], "packet_player_armor_equipment": ["container", [{"name": "entityId", "type": "i64"}, {"name": "helmet", "type": "slot"}, {"name": "chestplate", "type": "slot"}, {"name": "leggings", "type": "slot"}, {"name": "boots", "type": "slot"}]], "packet_interact": ["container", [{"name": "actionId", "type": "i8"}, {"name": "targetEntityId", "type": "i64"}]], "packet_use_item": ["container", [{"name": "blockcoordinates", "type": "blockcoordinates"}, {"name": "face", "type": "i8"}, {"name": "facecoordinates", "type": "vector3"}, {"name": "playerposition", "type": "vector3"}, {"name": "slot", "type": "i32"}, {"name": "item", "type": "slot"}]], "packet_player_action": ["container", [{"name": "entityId", "type": "i64"}, {"name": "actionId", "type": "i32"}, {"name": "x", "type": "i32"}, {"name": "y", "type": "i32"}, {"name": "z", "type": "i32"}, {"name": "face", "type": "i32"}]], "packet_hurt_armor": ["container", [{"name": "health", "type": "i8"}]], "packet_set_entity_data": ["container", [{"name": "entity_id", "type": "i64"}, {"name": "metadata", "type": "metadatadictionary"}]], "packet_set_entity_motion": ["container", [{"name": "entities", "type": "entitymotions"}]], "packet_set_entity_link": ["container", [{"name": "riderId", "type": "i64"}, {"name": "riddenId", "type": "i64"}, {"name": "linkType", "type": "i8"}]], "packet_set_health": ["container", [{"name": "health", "type": "i32"}]], "packet_set_spawn_position": ["container", [{"name": "x", "type": "i32"}, {"name": "z", "type": "i32"}, {"name": "y", "type": "i32"}]], "packet_animate": ["container", [{"name": "actionId", "type": "i8"}, {"name": "entityId", "type": "i64"}]], "packet_respawn": ["container", [{"name": "x", "type": "f32"}, {"name": "y", "type": "f32"}, {"name": "z", "type": "f32"}]], "packet_drop_item": ["container", [{"name": "itemtype", "type": "i8"}, {"name": "item", "type": "slot"}]], "packet_container_open": ["container", [{"name": "windowId", "type": "i8"}, {"name": "type", "type": "i8"}, {"name": "slotCount", "type": "i16"}, {"name": "x", "type": "i32"}, {"name": "y", "type": "i32"}, {"name": "z", "type": "i32"}]], "packet_container_close": ["container", [{"name": "windowId", "type": "i8"}]], "packet_container_set_slot": ["container", [{"name": "windowId", "type": "i8"}, {"name": "slot", "type": "i16"}, {"name": "unknown", "type": "i16"}, {"name": "item", "type": "slot"}]], "packet_container_set_data": ["container", [{"name": "windowId", "type": "i8"}, {"name": "property", "type": "i16"}, {"name": "value", "type": "i16"}]], "packet_container_set_content": ["container", [{"name": "windowId", "type": "i8"}, {"name": "slotData", "type": "itemstacks"}, {"name": "hotbarData", "type": ["switch", {"compareTo": "windowId", "fields": {"0": ["array", {"countType": "i16", "type": ["container", [{"name": "slot", "type": "i32"}]]}]}, "default": "i16"}]}]], "packet_crafting_data": ["container", [{"name": "recipes", "type": ["container", [{"name": "entryType", "type": "i32"}, {"name": "recipe", "type": ["array", {"countType": "i32", "type": ["switch", {"compareTo": "entryType", "fields": {"0": "shapelessRecipe", "1": "shapedRecipe", "2": "furnaceRecipe", "3": "furnaceRecipeData", "4": "enchantList"}, "default": "void"}]}]}]]}, {"name": "cleanRecipes", "type": "i8"}]], "packet_crafting_event": ["container", [{"name": "windowId", "type": "i8"}, {"name": "recipeType", "type": "i32"}, {"name": "recipeId", "type": "uuid"}, {"name": "input", "type": "itemstacks"}, {"name": "result", "type": "itemstacks"}]], "packet_adventure_settings": ["container", [{"name": "flags", "type": "i32"}, {"name": "userPermission", "type": "i32"}, {"name": "globalPermission", "type": "i32"}]], "packet_tile_entity_data": ["container", [{"name": "x", "type": "i32"}, {"name": "y", "type": "i32"}, {"name": "z", "type": "i32"}, {"name": "namedtag", "type": "restBuffer"}]], "packet_player_input": ["container", [{"name": "motionX", "type": "f32"}, {"name": "motionZ", "type": "f32"}, {"name": "flags", "type": "i16"}]], "packet_full_chunk_data": ["container", [{"name": "chunkX", "type": "i32"}, {"name": "chunkZ", "type": "i32"}, {"name": "order", "type": "i8"}, {"name": "chunkData", "type": ["buffer", {"countType": "i32"}]}]], "packet_set_difficulty": ["container", [{"name": "difficulty", "type": "i32"}]], "packet_player_list": ["container", [{"name": "type", "type": "i8"}, {"name": "entries", "type": ["array", {"countType": "i32", "type": ["switch", {"compareTo": "type", "fields": {"0": ["container", [{"name": "clientUuid", "type": "uuid"}, {"name": "entityId", "type": "i64"}, {"name": "displayName", "type": "string"}, {"name": "skin", "type": "skin"}]], "1": ["container", [{"name": "clientUuid", "type": "uuid"}]]}}]}]}]], "packet_request_chunk_radius": ["container", [{"name": "chunkRadius", "type": "i32"}]], "packet_chunk_radius_update": ["container", [{"name": "chunkRadius", "type": "i32"}]], "packet_transfer": ["container", [{"name": "endpoint", "type": "ip<PERSON><PERSON><PERSON>"}]], "packet_spawn_experience_orb": ["container", [{"name": "entityId", "type": "i64"}, {"name": "x", "type": "i32"}, {"name": "y", "type": "i32"}, {"name": "z", "type": "i32"}, {"name": "count", "type": "i32"}]], "packet_replace_selected_item": ["container", [{"name": "slot", "type": "slot"}]]}}