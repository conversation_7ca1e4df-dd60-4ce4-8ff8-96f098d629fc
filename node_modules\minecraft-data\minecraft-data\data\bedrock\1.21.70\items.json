[{"id": 0, "stackSize": 64, "name": "air", "displayName": "Air"}, {"id": 1, "stackSize": 64, "name": "stone", "displayName": "Stone"}, {"id": 2, "stackSize": 64, "name": "granite", "displayName": "Granite"}, {"id": 3, "stackSize": 64, "name": "polished_granite", "displayName": "Polished Granite"}, {"id": 4, "stackSize": 64, "name": "diorite", "displayName": "Diorite"}, {"id": 5, "stackSize": 64, "name": "polished_diorite", "displayName": "Polished Diorite"}, {"id": 6, "stackSize": 64, "name": "andesite", "displayName": "Andesite"}, {"id": 7, "stackSize": 64, "name": "polished_andesite", "displayName": "Polished Andesite"}, {"id": 8, "stackSize": 64, "name": "deepslate", "displayName": "Deepslate"}, {"id": 9, "stackSize": 64, "name": "cobbled_deepslate", "displayName": "Cobbled Deepslate"}, {"id": 10, "stackSize": 64, "name": "polished_deepslate", "displayName": "Polished Deepslate"}, {"id": 11, "stackSize": 64, "name": "calcite", "displayName": "Calcite"}, {"id": 12, "stackSize": 64, "name": "tuff", "displayName": "<PERSON><PERSON>"}, {"id": 13, "stackSize": 64, "name": "tuff_slab", "displayName": "<PERSON><PERSON>"}, {"id": 14, "stackSize": 64, "name": "tuff_stairs", "displayName": "<PERSON><PERSON> St<PERSON>s"}, {"id": 15, "stackSize": 64, "name": "tuff_wall", "displayName": "<PERSON><PERSON>"}, {"id": 16, "stackSize": 64, "name": "chiseled_tuff", "displayName": "Chiseled <PERSON>"}, {"id": 17, "stackSize": 64, "name": "polished_tuff", "displayName": "Polished <PERSON>"}, {"id": 18, "stackSize": 64, "name": "polished_tuff_slab", "displayName": "Polished <PERSON><PERSON>"}, {"id": 19, "stackSize": 64, "name": "polished_tuff_stairs", "displayName": "Polished <PERSON><PERSON>"}, {"id": 20, "stackSize": 64, "name": "polished_tuff_wall", "displayName": "Polished <PERSON><PERSON>"}, {"id": 21, "stackSize": 64, "name": "tuff_bricks", "displayName": "<PERSON>ff Bricks"}, {"id": 22, "stackSize": 64, "name": "tuff_brick_slab", "displayName": "Tuff Brick Slab"}, {"id": 23, "stackSize": 64, "name": "tuff_brick_stairs", "displayName": "Tuff Brick Stairs"}, {"id": 24, "stackSize": 64, "name": "tuff_brick_wall", "displayName": "Tuff Brick Wall"}, {"id": 25, "stackSize": 64, "name": "chiseled_tuff_bricks", "displayName": "Chiseled Tuff Bricks"}, {"id": 26, "stackSize": 64, "name": "dripstone_block", "displayName": "Dripstone Block"}, {"id": 27, "stackSize": 64, "name": "grass_block", "displayName": "Grass Block"}, {"id": 28, "stackSize": 64, "name": "dirt", "displayName": "Dirt"}, {"id": 29, "stackSize": 64, "name": "coarse_dirt", "displayName": "Coarse Dirt"}, {"id": 30, "stackSize": 64, "name": "podzol", "displayName": "Podzol"}, {"id": 31, "stackSize": 64, "name": "dirt_with_roots", "displayName": "Rooted Dirt"}, {"id": 32, "stackSize": 64, "name": "mud", "displayName": "Mud"}, {"id": 33, "stackSize": 64, "name": "crimson_nylium", "displayName": "Crimson Nylium"}, {"id": 34, "stackSize": 64, "name": "warped_nylium", "displayName": "Warped Nylium"}, {"id": 35, "stackSize": 64, "name": "cobblestone", "displayName": "Cobblestone"}, {"id": 36, "stackSize": 64, "name": "oak_planks", "displayName": "Oak Planks"}, {"id": 37, "stackSize": 64, "name": "spruce_planks", "displayName": "Spruce Planks"}, {"id": 38, "stackSize": 64, "name": "birch_planks", "displayName": "Birch Planks"}, {"id": 39, "stackSize": 64, "name": "jungle_planks", "displayName": "Jungle Planks"}, {"id": 40, "stackSize": 64, "name": "acacia_planks", "displayName": "Acacia Planks"}, {"id": 41, "stackSize": 64, "name": "cherry_planks", "displayName": "Cherry Planks"}, {"id": 42, "stackSize": 64, "name": "dark_oak_planks", "displayName": "Dark Oak Planks"}, {"id": 43, "stackSize": 64, "name": "pale_oak_planks", "displayName": "Pale Oak Planks"}, {"id": 44, "stackSize": 64, "name": "mangrove_planks", "displayName": "Mangrove Planks"}, {"id": 45, "stackSize": 64, "name": "bamboo_planks", "displayName": "Bamboo Planks"}, {"id": 46, "stackSize": 64, "name": "crimson_planks", "displayName": "Crimson Planks"}, {"id": 47, "stackSize": 64, "name": "warped_planks", "displayName": "Warped Planks"}, {"id": 48, "stackSize": 64, "name": "bamboo_mosaic", "displayName": "Bamboo Mosaic"}, {"id": 49, "stackSize": 64, "name": "oak_sapling", "displayName": "Oak Sapling"}, {"id": 50, "stackSize": 64, "name": "spruce_sapling", "displayName": "Spruce Sapling"}, {"id": 51, "stackSize": 64, "name": "birch_sapling", "displayName": "Birch Sapling"}, {"id": 52, "stackSize": 64, "name": "jungle_sapling", "displayName": "Jungle Sapling"}, {"id": 53, "stackSize": 64, "name": "acacia_sapling", "displayName": "Acacia Sapling"}, {"id": 54, "stackSize": 64, "name": "cherry_sapling", "displayName": "Cherry Sapling"}, {"id": 55, "stackSize": 64, "name": "dark_oak_sapling", "displayName": "Dark Oak Sapling"}, {"id": 56, "stackSize": 64, "name": "pale_oak_sapling", "displayName": "Pale Oak Sapling"}, {"id": 57, "stackSize": 64, "name": "mangrove_propagule", "displayName": "Mangrove Propagule"}, {"id": 58, "stackSize": 64, "name": "bedrock", "displayName": "Bedrock"}, {"id": 59, "stackSize": 64, "name": "sand", "displayName": "Sand"}, {"id": 60, "stackSize": 64, "name": "suspicious_sand", "displayName": "Suspicious Sand"}, {"id": 61, "stackSize": 64, "name": "suspicious_gravel", "displayName": "Suspicious Gravel"}, {"id": 62, "stackSize": 64, "name": "red_sand", "displayName": "Red Sand"}, {"id": 63, "stackSize": 64, "name": "gravel", "displayName": "<PERSON>l"}, {"id": 64, "stackSize": 64, "name": "coal_ore", "displayName": "Coal Ore"}, {"id": 65, "stackSize": 64, "name": "deepslate_coal_ore", "displayName": "Deepslate Coal Ore"}, {"id": 66, "stackSize": 64, "name": "iron_ore", "displayName": "Iron Ore"}, {"id": 67, "stackSize": 64, "name": "deepslate_iron_ore", "displayName": "Deepslate Iron Ore"}, {"id": 68, "stackSize": 64, "name": "copper_ore", "displayName": "Copper Ore"}, {"id": 69, "stackSize": 64, "name": "deepslate_copper_ore", "displayName": "Deepslate Copper Ore"}, {"id": 70, "stackSize": 64, "name": "gold_ore", "displayName": "Gold Ore"}, {"id": 71, "stackSize": 64, "name": "deepslate_gold_ore", "displayName": "Deepslate Gold Ore"}, {"id": 72, "stackSize": 64, "name": "redstone_ore", "displayName": "Redstone Ore"}, {"id": 73, "stackSize": 64, "name": "deepslate_redstone_ore", "displayName": "Deepslate Redstone Ore"}, {"id": 74, "stackSize": 64, "name": "emerald_ore", "displayName": "Emerald Ore"}, {"id": 75, "stackSize": 64, "name": "deepslate_emerald_ore", "displayName": "Deepslate Emerald Ore"}, {"id": 76, "stackSize": 64, "name": "lapis_ore", "displayName": "Lapis <PERSON> Ore"}, {"id": 77, "stackSize": 64, "name": "deepslate_lapis_ore", "displayName": "Deepslate Lapis Lazuli Ore"}, {"id": 78, "stackSize": 64, "name": "diamond_ore", "displayName": "Diamond Ore"}, {"id": 79, "stackSize": 64, "name": "deepslate_diamond_ore", "displayName": "Deepslate Diamond Ore"}, {"id": 80, "stackSize": 64, "name": "nether_gold_ore", "displayName": "Nether Gold Ore"}, {"id": 81, "stackSize": 64, "name": "quartz_ore", "displayName": "<PERSON><PERSON>"}, {"id": 82, "stackSize": 64, "name": "ancient_debris", "displayName": "Ancient Debris"}, {"id": 83, "stackSize": 64, "name": "coal_block", "displayName": "Block of Coal"}, {"id": 84, "stackSize": 64, "name": "raw_iron_block", "displayName": "Block of Raw Iron"}, {"id": 85, "stackSize": 64, "name": "raw_copper_block", "displayName": "Block of Raw Copper"}, {"id": 86, "stackSize": 64, "name": "raw_gold_block", "displayName": "Block of Raw Gold"}, {"id": 87, "stackSize": 64, "name": "heavy_core", "displayName": "Heavy Core"}, {"id": 88, "stackSize": 64, "name": "amethyst_block", "displayName": "Block of Amethyst"}, {"id": 89, "stackSize": 64, "name": "budding_amethyst", "displayName": "Budding Amethyst"}, {"id": 90, "stackSize": 64, "name": "iron_block", "displayName": "Block of Iron"}, {"id": 91, "stackSize": 64, "name": "copper_block", "displayName": "Block of Copper"}, {"id": 92, "stackSize": 64, "name": "gold_block", "displayName": "Block of Gold"}, {"id": 93, "stackSize": 64, "name": "diamond_block", "displayName": "Block of Diamond"}, {"id": 94, "stackSize": 64, "name": "netherite_block", "displayName": "Block of Netherite"}, {"id": 95, "stackSize": 64, "name": "exposed_copper", "displayName": "Exposed Copper"}, {"id": 96, "stackSize": 64, "name": "weathered_copper", "displayName": "Weathered Copper"}, {"id": 97, "stackSize": 64, "name": "oxidized_copper", "displayName": "Oxidized Copper"}, {"id": 98, "stackSize": 64, "name": "chiseled_copper", "displayName": "Chiseled Copper"}, {"id": 99, "stackSize": 64, "name": "exposed_chiseled_copper", "displayName": "Exposed Chiseled Copper"}, {"id": 100, "stackSize": 64, "name": "weathered_chiseled_copper", "displayName": "Weathered Chiseled Copper"}, {"id": 101, "stackSize": 64, "name": "oxidized_chiseled_copper", "displayName": "Oxidized Chiseled Copper"}, {"id": 102, "stackSize": 64, "name": "cut_copper", "displayName": "Cut Copper"}, {"id": 103, "stackSize": 64, "name": "exposed_cut_copper", "displayName": "Exposed Cut Copper"}, {"id": 104, "stackSize": 64, "name": "weathered_cut_copper", "displayName": "Weathered Cut Copper"}, {"id": 105, "stackSize": 64, "name": "oxidized_cut_copper", "displayName": "Oxidized Cut Copper"}, {"id": 106, "stackSize": 64, "name": "cut_copper_stairs", "displayName": "Cut Copper Stairs"}, {"id": 107, "stackSize": 64, "name": "exposed_cut_copper_stairs", "displayName": "Exposed Cut Copper Stairs"}, {"id": 108, "stackSize": 64, "name": "weathered_cut_copper_stairs", "displayName": "Weathered Cut Copper Stairs"}, {"id": 109, "stackSize": 64, "name": "oxidized_cut_copper_stairs", "displayName": "Oxidized Cut Copper Stairs"}, {"id": 110, "stackSize": 64, "name": "cut_copper_slab", "displayName": "Cut Copper Slab"}, {"id": 111, "stackSize": 64, "name": "exposed_cut_copper_slab", "displayName": "Exposed Cut Copper Slab"}, {"id": 112, "stackSize": 64, "name": "weathered_cut_copper_slab", "displayName": "Weathered Cut Copper Slab"}, {"id": 113, "stackSize": 64, "name": "oxidized_cut_copper_slab", "displayName": "Oxidized Cut Copper Slab"}, {"id": 114, "stackSize": 64, "name": "waxed_copper", "displayName": "Waxed Block of Copper"}, {"id": 115, "stackSize": 64, "name": "waxed_exposed_copper", "displayName": "Waxed Exposed Copper"}, {"id": 116, "stackSize": 64, "name": "waxed_weathered_copper", "displayName": "Waxed Weathered Copper"}, {"id": 117, "stackSize": 64, "name": "waxed_oxidized_copper", "displayName": "Waxed Oxidized Copper"}, {"id": 118, "stackSize": 64, "name": "waxed_chiseled_copper", "displayName": "Waxed Chiseled Copper"}, {"id": 119, "stackSize": 64, "name": "waxed_exposed_chiseled_copper", "displayName": "Waxed Exposed Chiseled Copper"}, {"id": 120, "stackSize": 64, "name": "waxed_weathered_chiseled_copper", "displayName": "Waxed Weathered Chiseled Copper"}, {"id": 121, "stackSize": 64, "name": "waxed_oxidized_chiseled_copper", "displayName": "Waxed Oxidized Chiseled Copper"}, {"id": 122, "stackSize": 64, "name": "waxed_cut_copper", "displayName": "Waxed Cut Copper"}, {"id": 123, "stackSize": 64, "name": "waxed_exposed_cut_copper", "displayName": "Waxed Exposed Cut Copper"}, {"id": 124, "stackSize": 64, "name": "waxed_weathered_cut_copper", "displayName": "Waxed Weathered Cut Copper"}, {"id": 125, "stackSize": 64, "name": "waxed_oxidized_cut_copper", "displayName": "Waxed Oxidized Cut Copper"}, {"id": 126, "stackSize": 64, "name": "waxed_cut_copper_stairs", "displayName": "Waxed Cut Copper Stairs"}, {"id": 127, "stackSize": 64, "name": "waxed_exposed_cut_copper_stairs", "displayName": "Waxed Exposed Cut Copper Stairs"}, {"id": 128, "stackSize": 64, "name": "waxed_weathered_cut_copper_stairs", "displayName": "Waxed Weathered Cut Copper Stairs"}, {"id": 129, "stackSize": 64, "name": "waxed_oxidized_cut_copper_stairs", "displayName": "Waxed Oxidized Cut Copper Stairs"}, {"id": 130, "stackSize": 64, "name": "waxed_cut_copper_slab", "displayName": "Waxed Cut Copper Slab"}, {"id": 131, "stackSize": 64, "name": "waxed_exposed_cut_copper_slab", "displayName": "Waxed Exposed Cut Copper Slab"}, {"id": 132, "stackSize": 64, "name": "waxed_weathered_cut_copper_slab", "displayName": "Waxed Weathered Cut Copper Slab"}, {"id": 133, "stackSize": 64, "name": "waxed_oxidized_cut_copper_slab", "displayName": "Waxed Oxidized Cut Copper Slab"}, {"id": 134, "stackSize": 64, "name": "oak_log", "displayName": "Oak Log"}, {"id": 135, "stackSize": 64, "name": "spruce_log", "displayName": "Spruce Log"}, {"id": 136, "stackSize": 64, "name": "birch_log", "displayName": "Birch Log"}, {"id": 137, "stackSize": 64, "name": "jungle_log", "displayName": "Jungle Log"}, {"id": 138, "stackSize": 64, "name": "acacia_log", "displayName": "Acacia Log"}, {"id": 139, "stackSize": 64, "name": "cherry_log", "displayName": "Cherry Log"}, {"id": 140, "stackSize": 64, "name": "pale_oak_log", "displayName": "Pale Oak Log"}, {"id": 141, "stackSize": 64, "name": "dark_oak_log", "displayName": "Dark Oak Log"}, {"id": 142, "stackSize": 64, "name": "mangrove_log", "displayName": "Mangrove Log"}, {"id": 143, "stackSize": 64, "name": "mangrove_roots", "displayName": "Mangrove Roots"}, {"id": 144, "stackSize": 64, "name": "muddy_mangrove_roots", "displayName": "Muddy Mangrove Roots"}, {"id": 145, "stackSize": 64, "name": "crimson_stem", "displayName": "Crimson Stem"}, {"id": 146, "stackSize": 64, "name": "warped_stem", "displayName": "Warped Stem"}, {"id": 147, "stackSize": 64, "name": "bamboo_block", "displayName": "Block of Bamboo"}, {"id": 148, "stackSize": 64, "name": "stripped_oak_log", "displayName": "Stripped Oak Log"}, {"id": 149, "stackSize": 64, "name": "stripped_spruce_log", "displayName": "Stripped Spruce Log"}, {"id": 150, "stackSize": 64, "name": "stripped_birch_log", "displayName": "Stripped Birch Log"}, {"id": 151, "stackSize": 64, "name": "stripped_jungle_log", "displayName": "Stripped Jungle Log"}, {"id": 152, "stackSize": 64, "name": "stripped_acacia_log", "displayName": "Stripped Acacia Log"}, {"id": 153, "stackSize": 64, "name": "stripped_cherry_log", "displayName": "Stripped Cherry Log"}, {"id": 154, "stackSize": 64, "name": "stripped_dark_oak_log", "displayName": "Stripped Dark Oak Log"}, {"id": 155, "stackSize": 64, "name": "stripped_pale_oak_log", "displayName": "Stripped Pale Oak Log"}, {"id": 156, "stackSize": 64, "name": "stripped_mangrove_log", "displayName": "Stripped Mangrove Log"}, {"id": 157, "stackSize": 64, "name": "stripped_crimson_stem", "displayName": "Stripped Crimson Stem"}, {"id": 158, "stackSize": 64, "name": "stripped_warped_stem", "displayName": "Stripped Warped Stem"}, {"id": 159, "stackSize": 64, "name": "stripped_oak_wood", "displayName": "Stripped Oak Wood"}, {"id": 160, "stackSize": 64, "name": "stripped_spruce_wood", "displayName": "Stripped Spruce Wood"}, {"id": 161, "stackSize": 64, "name": "stripped_birch_wood", "displayName": "Stripped Birch Wood"}, {"id": 162, "stackSize": 64, "name": "stripped_jungle_wood", "displayName": "Stripped Jungle Wood"}, {"id": 163, "stackSize": 64, "name": "stripped_acacia_wood", "displayName": "Stripped Acacia Wood"}, {"id": 164, "stackSize": 64, "name": "stripped_cherry_wood", "displayName": "Stripped Cherry Wood"}, {"id": 165, "stackSize": 64, "name": "stripped_dark_oak_wood", "displayName": "Stripped Dark Oak Wood"}, {"id": 166, "stackSize": 64, "name": "stripped_pale_oak_wood", "displayName": "Stripped Pale Oak Wood"}, {"id": 167, "stackSize": 64, "name": "stripped_mangrove_wood", "displayName": "Stripped Mangrove Wood"}, {"id": 168, "stackSize": 64, "name": "stripped_crimson_hyphae", "displayName": "Stripped Crimson Hyphae"}, {"id": 169, "stackSize": 64, "name": "stripped_warped_hyphae", "displayName": "Stripped Warped Hyphae"}, {"id": 170, "stackSize": 64, "name": "stripped_bamboo_block", "displayName": "Block of Stripped Bamboo"}, {"id": 171, "stackSize": 64, "name": "oak_wood", "displayName": "Oak Wood"}, {"id": 172, "stackSize": 64, "name": "spruce_wood", "displayName": "Spruce Wood"}, {"id": 173, "stackSize": 64, "name": "birch_wood", "displayName": "Birch Wood"}, {"id": 174, "stackSize": 64, "name": "jungle_wood", "displayName": "Jungle Wood"}, {"id": 175, "stackSize": 64, "name": "acacia_wood", "displayName": "Acacia Wood"}, {"id": 176, "stackSize": 64, "name": "cherry_wood", "displayName": "<PERSON>"}, {"id": 177, "stackSize": 64, "name": "pale_oak_wood", "displayName": "Pale Oak Wood"}, {"id": 178, "stackSize": 64, "name": "dark_oak_wood", "displayName": "Dark Oak Wood"}, {"id": 179, "stackSize": 64, "name": "mangrove_wood", "displayName": "Mangrove Wood"}, {"id": 180, "stackSize": 64, "name": "crimson_hyphae", "displayName": "Crimson Hyphae"}, {"id": 181, "stackSize": 64, "name": "warped_hyphae", "displayName": "Warped Hyphae"}, {"id": 182, "stackSize": 64, "name": "oak_leaves", "displayName": "Oak Leaves"}, {"id": 183, "stackSize": 64, "name": "spruce_leaves", "displayName": "Spruce Leaves"}, {"id": 184, "stackSize": 64, "name": "birch_leaves", "displayName": "Birch Leaves"}, {"id": 185, "stackSize": 64, "name": "jungle_leaves", "displayName": "Jungle Leaves"}, {"id": 186, "stackSize": 64, "name": "acacia_leaves", "displayName": "Acacia Leaves"}, {"id": 187, "stackSize": 64, "name": "cherry_leaves", "displayName": "Cherry Leaves"}, {"id": 188, "stackSize": 64, "name": "dark_oak_leaves", "displayName": "Dark Oak Leaves"}, {"id": 189, "stackSize": 64, "name": "pale_oak_leaves", "displayName": "Pale Oak Leaves"}, {"id": 190, "stackSize": 64, "name": "mangrove_leaves", "displayName": "Mangrove Leaves"}, {"id": 191, "stackSize": 64, "name": "azalea_leaves", "displayName": "Azalea Leaves"}, {"id": 192, "stackSize": 64, "name": "azalea_leaves_flowered", "displayName": "Flowering Azalea Leaves"}, {"id": 193, "stackSize": 64, "name": "sponge", "displayName": "Sponge"}, {"id": 194, "stackSize": 64, "name": "wet_sponge", "displayName": "Wet Sponge"}, {"id": 195, "stackSize": 64, "name": "glass", "displayName": "Glass"}, {"id": 196, "stackSize": 64, "name": "tinted_glass", "displayName": "Tinted Glass"}, {"id": 197, "stackSize": 64, "name": "lapis_block", "displayName": "Block of Lapis Lazuli"}, {"id": 198, "stackSize": 64, "name": "sandstone", "displayName": "Sandstone"}, {"id": 199, "stackSize": 64, "name": "chiseled_sandstone", "displayName": "Chiseled Sandstone"}, {"id": 200, "stackSize": 64, "name": "cut_sandstone", "displayName": "Cut Sandstone"}, {"id": 201, "stackSize": 64, "name": "web", "displayName": "Cobweb"}, {"id": 202, "stackSize": 64, "name": "short_grass", "displayName": "Short Grass"}, {"id": 203, "stackSize": 64, "name": "fern", "displayName": "Fern"}, {"id": 204, "stackSize": 64, "name": "azalea", "displayName": "Azalea"}, {"id": 205, "stackSize": 64, "name": "flowering_azalea", "displayName": "Flowering Azalea"}, {"id": 206, "stackSize": 64, "name": "deadbush", "displayName": "Dead Bush"}, {"id": 207, "stackSize": 64, "name": "seagrass", "displayName": "Seagrass"}, {"id": 208, "stackSize": 64, "name": "sea_pickle", "displayName": "<PERSON>"}, {"id": 209, "stackSize": 64, "name": "white_wool", "displayName": "White Wool"}, {"id": 210, "stackSize": 64, "name": "orange_wool", "displayName": "Orange Wool"}, {"id": 211, "stackSize": 64, "name": "magenta_wool", "displayName": "Magenta Wool"}, {"id": 212, "stackSize": 64, "name": "light_blue_wool", "displayName": "Light Blue Wool"}, {"id": 213, "stackSize": 64, "name": "yellow_wool", "displayName": "Yellow Wool"}, {"id": 214, "stackSize": 64, "name": "lime_wool", "displayName": "Lime Wool"}, {"id": 215, "stackSize": 64, "name": "pink_wool", "displayName": "Pink Wool"}, {"id": 216, "stackSize": 64, "name": "gray_wool", "displayName": "Gray <PERSON>"}, {"id": 217, "stackSize": 64, "name": "light_gray_wool", "displayName": "Light Gray Wool"}, {"id": 218, "stackSize": 64, "name": "cyan_wool", "displayName": "<PERSON><PERSON>"}, {"id": 219, "stackSize": 64, "name": "purple_wool", "displayName": "Purple Wool"}, {"id": 220, "stackSize": 64, "name": "blue_wool", "displayName": "Blue Wool"}, {"id": 221, "stackSize": 64, "name": "brown_wool", "displayName": "Brown Wool"}, {"id": 222, "stackSize": 64, "name": "green_wool", "displayName": "Green Wool"}, {"id": 223, "stackSize": 64, "name": "red_wool", "displayName": "Red Wool"}, {"id": 224, "stackSize": 64, "name": "black_wool", "displayName": "Black Wool"}, {"id": 225, "stackSize": 64, "name": "dandelion", "displayName": "Dandelion"}, {"id": 226, "stackSize": 64, "name": "open_eyeblossom", "displayName": "Open Eyeblossom"}, {"id": 227, "stackSize": 64, "name": "closed_eyeblossom", "displayName": "Closed Eyeblossom"}, {"id": 228, "stackSize": 64, "name": "poppy", "displayName": "<PERSON><PERSON>"}, {"id": 229, "stackSize": 64, "name": "blue_orchid", "displayName": "Blue Orchid"}, {"id": 230, "stackSize": 64, "name": "allium", "displayName": "Allium"}, {"id": 231, "stackSize": 64, "name": "azure_bluet", "displayName": "Azure Bluet"}, {"id": 232, "stackSize": 64, "name": "red_tulip", "displayName": "<PERSON>lip"}, {"id": 233, "stackSize": 64, "name": "orange_tulip", "displayName": "Orange Tulip"}, {"id": 234, "stackSize": 64, "name": "white_tulip", "displayName": "White Tulip"}, {"id": 235, "stackSize": 64, "name": "pink_tulip", "displayName": "<PERSON> Tulip"}, {"id": 236, "stackSize": 64, "name": "oxeye_daisy", "displayName": "Oxeye Daisy"}, {"id": 237, "stackSize": 64, "name": "cornflower", "displayName": "Corn<PERSON>"}, {"id": 238, "stackSize": 64, "name": "lily_of_the_valley", "displayName": "Lily of the Valley"}, {"id": 239, "stackSize": 64, "name": "wither_rose", "displayName": "<PERSON><PERSON>"}, {"id": 240, "stackSize": 64, "name": "torchflower", "displayName": "Torch<PERSON>"}, {"id": 241, "stackSize": 64, "name": "pitcher_plant", "displayName": "Pitcher Plant"}, {"id": 242, "stackSize": 64, "name": "spore_blossom", "displayName": "Spore Blossom"}, {"id": 243, "stackSize": 64, "name": "brown_mushroom", "displayName": "Brown Mushroom"}, {"id": 244, "stackSize": 64, "name": "red_mushroom", "displayName": "Red Mushroom"}, {"id": 245, "stackSize": 64, "name": "crimson_fungus", "displayName": "Crimson Fungus"}, {"id": 246, "stackSize": 64, "name": "warped_fungus", "displayName": "Warped Fungus"}, {"id": 247, "stackSize": 64, "name": "crimson_roots", "displayName": "Crimson Roots"}, {"id": 248, "stackSize": 64, "name": "warped_roots", "displayName": "Warped Roots"}, {"id": 249, "stackSize": 64, "name": "nether_sprouts", "displayName": "Nether Sprouts"}, {"id": 250, "stackSize": 64, "name": "weeping_vines", "displayName": "Weeping Vines"}, {"id": 251, "stackSize": 64, "name": "twisting_vines", "displayName": "Twisting Vines"}, {"id": 252, "stackSize": 64, "name": "sugar_cane", "displayName": "Sugar Cane"}, {"id": 253, "stackSize": 64, "name": "kelp", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 254, "stackSize": 64, "name": "pink_petals", "displayName": "Pink Petals"}, {"id": 255, "stackSize": 64, "name": "moss_carpet", "displayName": "Moss Carpet"}, {"id": 256, "stackSize": 64, "name": "moss_block", "displayName": "Moss Block"}, {"id": 257, "stackSize": 64, "name": "pale_moss_carpet", "displayName": "<PERSON><PERSON> Moss Carpet"}, {"id": 258, "stackSize": 64, "name": "pale_hanging_moss", "displayName": "<PERSON><PERSON>"}, {"id": 259, "stackSize": 64, "name": "pale_moss_block", "displayName": "Pale Moss Block"}, {"id": 260, "stackSize": 64, "name": "hanging_roots", "displayName": "Hanging Roots"}, {"id": 261, "stackSize": 64, "name": "big_dripleaf", "displayName": "Big Dripleaf"}, {"id": 262, "stackSize": 64, "name": "small_dripleaf_block", "displayName": "Small Dripleaf"}, {"id": 263, "stackSize": 64, "name": "bamboo", "displayName": "Bamboo"}, {"id": 264, "stackSize": 64, "name": "oak_slab", "displayName": "Oak Slab"}, {"id": 265, "stackSize": 64, "name": "spruce_slab", "displayName": "Spruce Slab"}, {"id": 266, "stackSize": 64, "name": "birch_slab", "displayName": "<PERSON>"}, {"id": 267, "stackSize": 64, "name": "jungle_slab", "displayName": "Jungle Slab"}, {"id": 268, "stackSize": 64, "name": "acacia_slab", "displayName": "Acacia <PERSON>b"}, {"id": 269, "stackSize": 64, "name": "cherry_slab", "displayName": "Cherry Slab"}, {"id": 270, "stackSize": 64, "name": "dark_oak_slab", "displayName": "Dark Oak Slab"}, {"id": 271, "stackSize": 64, "name": "pale_oak_slab", "displayName": "Pale Oak Slab"}, {"id": 272, "stackSize": 64, "name": "mangrove_slab", "displayName": "Mangrove Slab"}, {"id": 273, "stackSize": 64, "name": "bamboo_slab", "displayName": "Bamboo Slab"}, {"id": 274, "stackSize": 64, "name": "bamboo_mosaic_slab", "displayName": "Bamboo Mosaic Slab"}, {"id": 275, "stackSize": 64, "name": "crimson_slab", "displayName": "Crimson Slab"}, {"id": 276, "stackSize": 64, "name": "warped_slab", "displayName": "Warped Slab"}, {"id": 277, "stackSize": 64, "name": "normal_stone_slab", "displayName": "<PERSON> Slab"}, {"id": 278, "stackSize": 64, "name": "smooth_stone_slab", "displayName": "Smooth Stone Slab"}, {"id": 279, "stackSize": 64, "name": "sandstone_slab", "displayName": "Sandstone Slab"}, {"id": 280, "stackSize": 64, "name": "cut_sandstone_slab", "displayName": "Cut Sandstone Slab"}, {"id": 281, "stackSize": 64, "name": "petrified_oak_slab", "displayName": "Petrified Oak Slab"}, {"id": 282, "stackSize": 64, "name": "cobblestone_slab", "displayName": "Cobblestone Slab"}, {"id": 283, "stackSize": 64, "name": "brick_slab", "displayName": "Brick Slab"}, {"id": 284, "stackSize": 64, "name": "stone_brick_slab", "displayName": "Stone Brick Slab"}, {"id": 285, "stackSize": 64, "name": "mud_brick_slab", "displayName": "Mud <PERSON> Slab"}, {"id": 286, "stackSize": 64, "name": "nether_brick_slab", "displayName": "Nether Brick Slab"}, {"id": 287, "stackSize": 64, "name": "quartz_slab", "displayName": "Quartz Slab"}, {"id": 288, "stackSize": 64, "name": "red_sandstone_slab", "displayName": "Red Sandstone Slab"}, {"id": 289, "stackSize": 64, "name": "cut_red_sandstone_slab", "displayName": "Cut Red Sandstone Slab"}, {"id": 290, "stackSize": 64, "name": "purpur_slab", "displayName": "Purpur Slab"}, {"id": 291, "stackSize": 64, "name": "prismarine_slab", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 292, "stackSize": 64, "name": "prismarine_brick_slab", "displayName": "Prismarine Brick Slab"}, {"id": 293, "stackSize": 64, "name": "dark_prismarine_slab", "displayName": "<PERSON> <PERSON><PERSON><PERSON><PERSON>"}, {"id": 294, "stackSize": 64, "name": "smooth_quartz", "displayName": "Smooth Quartz Block"}, {"id": 295, "stackSize": 64, "name": "smooth_red_sandstone", "displayName": "Smooth Red Sandstone"}, {"id": 296, "stackSize": 64, "name": "smooth_sandstone", "displayName": "Smooth Sandstone"}, {"id": 297, "stackSize": 64, "name": "smooth_stone", "displayName": "Smooth Stone"}, {"id": 298, "stackSize": 64, "name": "brick_block", "displayName": "Bricks"}, {"id": 299, "stackSize": 64, "name": "bookshelf", "displayName": "Bookshelf"}, {"id": 300, "stackSize": 64, "name": "chiseled_bookshelf", "displayName": "Chiseled Bookshelf"}, {"id": 301, "stackSize": 64, "name": "decorated_pot", "displayName": "Decorated Pot"}, {"id": 302, "stackSize": 64, "name": "mossy_cobblestone", "displayName": "<PERSON><PERSON>"}, {"id": 303, "stackSize": 64, "name": "obsidian", "displayName": "Obsidian"}, {"id": 304, "stackSize": 64, "name": "torch", "displayName": "<PERSON>ch"}, {"id": 305, "stackSize": 64, "name": "end_rod", "displayName": "End Rod"}, {"id": 306, "stackSize": 64, "name": "chorus_plant", "displayName": "Chorus Plant"}, {"id": 307, "stackSize": 64, "name": "chorus_flower", "displayName": "Chorus Flower"}, {"id": 308, "stackSize": 64, "name": "purpur_block", "displayName": "Purpur Block"}, {"id": 309, "stackSize": 64, "name": "purpur_pillar", "displayName": "Purpur Pillar"}, {"id": 310, "stackSize": 64, "name": "purpur_stairs", "displayName": "Purpur Stairs"}, {"id": 311, "stackSize": 64, "name": "mob_spawner", "displayName": "Monster Spawner"}, {"id": 312, "stackSize": 64, "name": "creaking_heart", "displayName": "Creaking Heart"}, {"id": 313, "stackSize": 64, "name": "chest", "displayName": "Chest"}, {"id": 314, "stackSize": 64, "name": "crafting_table", "displayName": "Crafting Table"}, {"id": 315, "stackSize": 64, "name": "farmland", "displayName": "Farmland"}, {"id": 316, "stackSize": 64, "name": "furnace", "displayName": "Furnace"}, {"id": 317, "stackSize": 64, "name": "ladder", "displayName": "Ladder"}, {"id": 318, "stackSize": 64, "name": "stone_stairs", "displayName": "Cobblestone Stairs"}, {"id": 319, "stackSize": 64, "name": "snow_layer", "displayName": "Snow"}, {"id": 320, "stackSize": 64, "name": "ice", "displayName": "Ice"}, {"id": 321, "stackSize": 64, "name": "snow", "displayName": "Snow Block"}, {"id": 322, "stackSize": 64, "name": "cactus", "displayName": "Cactus"}, {"id": 323, "stackSize": 64, "name": "clay", "displayName": "<PERSON>"}, {"id": 324, "stackSize": 64, "name": "jukebox", "displayName": "Jukebox"}, {"id": 325, "stackSize": 64, "name": "oak_fence", "displayName": "Oak Fence"}, {"id": 326, "stackSize": 64, "name": "spruce_fence", "displayName": "Spruce Fence"}, {"id": 327, "stackSize": 64, "name": "birch_fence", "displayName": "<PERSON>"}, {"id": 328, "stackSize": 64, "name": "jungle_fence", "displayName": "Jungle Fence"}, {"id": 329, "stackSize": 64, "name": "acacia_fence", "displayName": "Acacia Fence"}, {"id": 330, "stackSize": 64, "name": "cherry_fence", "displayName": "<PERSON>"}, {"id": 331, "stackSize": 64, "name": "dark_oak_fence", "displayName": "Dark Oak Fence"}, {"id": 332, "stackSize": 64, "name": "pale_oak_fence", "displayName": "Pale Oak Fence"}, {"id": 333, "stackSize": 64, "name": "mangrove_fence", "displayName": "Mangrove Fence"}, {"id": 334, "stackSize": 64, "name": "bamboo_fence", "displayName": "Bamboo Fence"}, {"id": 335, "stackSize": 64, "name": "crimson_fence", "displayName": "<PERSON> Fence"}, {"id": 336, "stackSize": 64, "name": "warped_fence", "displayName": "Warped <PERSON>"}, {"id": 337, "stackSize": 64, "name": "pumpkin", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 338, "stackSize": 64, "name": "carved_pumpkin", "displayName": "<PERSON><PERSON>", "enchantCategories": ["equippable", "vanishing"]}, {"id": 339, "stackSize": 64, "name": "lit_pumpkin", "displayName": "<PERSON>'<PERSON>"}, {"id": 340, "stackSize": 64, "name": "netherrack", "displayName": "Netherrack"}, {"id": 341, "stackSize": 64, "name": "soul_sand", "displayName": "Soul Sand"}, {"id": 342, "stackSize": 64, "name": "soul_soil", "displayName": "Soul Soil"}, {"id": 343, "stackSize": 64, "name": "basalt", "displayName": "Basalt"}, {"id": 344, "stackSize": 64, "name": "polished_basalt", "displayName": "Polished Ba<PERSON>t"}, {"id": 345, "stackSize": 64, "name": "smooth_basalt", "displayName": "Smooth Basalt"}, {"id": 346, "stackSize": 64, "name": "soul_torch", "displayName": "Soul Torch"}, {"id": 347, "stackSize": 64, "name": "glowstone", "displayName": "Glowstone"}, {"id": 348, "stackSize": 64, "name": "infested_stone", "displayName": "Infested Stone"}, {"id": 349, "stackSize": 64, "name": "infested_cobblestone", "displayName": "Infested Cobblestone"}, {"id": 350, "stackSize": 64, "name": "infested_stone_bricks", "displayName": "Infested Stone Bricks"}, {"id": 351, "stackSize": 64, "name": "infested_mossy_stone_bricks", "displayName": "Infested Mossy Stone Bricks"}, {"id": 352, "stackSize": 64, "name": "infested_cracked_stone_bricks", "displayName": "Infested Cracked Stone Bricks"}, {"id": 353, "stackSize": 64, "name": "infested_chiseled_stone_bricks", "displayName": "Infested Chiseled Stone Bricks"}, {"id": 354, "stackSize": 64, "name": "infested_deepslate", "displayName": "Infested Deepslate"}, {"id": 355, "stackSize": 64, "name": "stone_bricks", "displayName": "Stone Bricks"}, {"id": 356, "stackSize": 64, "name": "mossy_stone_bricks", "displayName": "Mossy Stone Bricks"}, {"id": 357, "stackSize": 64, "name": "cracked_stone_bricks", "displayName": "Cracked Stone Bricks"}, {"id": 358, "stackSize": 64, "name": "chiseled_stone_bricks", "displayName": "Chiseled Stone Bricks"}, {"id": 359, "stackSize": 64, "name": "packed_mud", "displayName": "Packed Mud"}, {"id": 360, "stackSize": 64, "name": "mud_bricks", "displayName": "Mud Bricks"}, {"id": 361, "stackSize": 64, "name": "deepslate_bricks", "displayName": "Deepslate Bricks"}, {"id": 362, "stackSize": 64, "name": "cracked_deepslate_bricks", "displayName": "Cracked Deepslate Bricks"}, {"id": 363, "stackSize": 64, "name": "deepslate_tiles", "displayName": "Deepslate Tiles"}, {"id": 364, "stackSize": 64, "name": "cracked_deepslate_tiles", "displayName": "Cracked Deepslate Tiles"}, {"id": 365, "stackSize": 64, "name": "chiseled_deepslate", "displayName": "Chiseled Deepslate"}, {"id": 366, "stackSize": 64, "name": "reinforced_deepslate", "displayName": "Reinforced Deepslate"}, {"id": 367, "displayName": "Brown Mushroom Block", "name": "brown_mushroom_block", "stackSize": 64, "metadata": 14, "variations": [{"metadata": 15, "id": 369, "name": "mushroom_stem", "displayName": "Mushroom Stem", "stackSize": 64}]}, {"id": 368, "stackSize": 64, "name": "red_mushroom_block", "displayName": "Red Mushroom Block"}, {"id": 370, "stackSize": 64, "name": "iron_bars", "displayName": "Iron Bars"}, {"id": 371, "stackSize": 64, "name": "chain", "displayName": "Chain"}, {"id": 372, "stackSize": 64, "name": "glass_pane", "displayName": "Glass Pane"}, {"id": 373, "stackSize": 64, "name": "melon_block", "displayName": "Melon"}, {"id": 374, "stackSize": 64, "name": "vine", "displayName": "Vines"}, {"id": 375, "stackSize": 64, "name": "glow_lichen", "displayName": "Glow Lichen"}, {"id": 376, "stackSize": 64, "name": "resin_clump", "displayName": "<PERSON><PERSON>"}, {"id": 377, "stackSize": 64, "name": "resin_block", "displayName": "Block of Resin"}, {"id": 378, "stackSize": 64, "name": "resin_bricks", "displayName": "Resin Bricks"}, {"id": 379, "stackSize": 64, "name": "resin_brick_stairs", "displayName": "Resin Brick Stairs"}, {"id": 380, "stackSize": 64, "name": "resin_brick_slab", "displayName": "Resin Brick Slab"}, {"id": 381, "stackSize": 64, "name": "resin_brick_wall", "displayName": "Resin Brick Wall"}, {"id": 382, "stackSize": 64, "name": "chiseled_resin_bricks", "displayName": "Chiseled Resin Bricks"}, {"id": 383, "stackSize": 64, "name": "brick_stairs", "displayName": "Brick Stairs"}, {"id": 384, "stackSize": 64, "name": "stone_brick_stairs", "displayName": "Stone Brick Stairs"}, {"id": 385, "stackSize": 64, "name": "mud_brick_stairs", "displayName": "Mud Brick Stairs"}, {"id": 386, "stackSize": 64, "name": "mycelium", "displayName": "Mycelium"}, {"id": 387, "stackSize": 64, "name": "waterlily", "displayName": "<PERSON>"}, {"id": 388, "stackSize": 64, "name": "nether_brick", "displayName": "Nether Bricks"}, {"id": 389, "stackSize": 64, "name": "cracked_nether_bricks", "displayName": "Cracked Nether Bricks"}, {"id": 390, "stackSize": 64, "name": "chiseled_nether_bricks", "displayName": "Chiseled Nether Bricks"}, {"id": 391, "stackSize": 64, "name": "nether_brick_fence", "displayName": "Nether Brick Fence"}, {"id": 392, "stackSize": 64, "name": "nether_brick_stairs", "displayName": "Nether Brick Stairs"}, {"id": 393, "stackSize": 64, "name": "sculk", "displayName": "Sculk"}, {"id": 394, "stackSize": 64, "name": "sculk_vein", "displayName": "Sculk Vein"}, {"id": 395, "stackSize": 64, "name": "sculk_catalyst", "displayName": "Sculk Catalyst"}, {"id": 396, "stackSize": 64, "name": "sculk_shrieker", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 397, "stackSize": 64, "name": "enchanting_table", "displayName": "Enchanting Table"}, {"id": 398, "stackSize": 64, "name": "end_portal_frame", "displayName": "End Portal Frame"}, {"id": 399, "stackSize": 64, "name": "end_stone", "displayName": "End Stone"}, {"id": 400, "stackSize": 64, "name": "end_bricks", "displayName": "End Stone Bricks"}, {"id": 401, "stackSize": 64, "name": "dragon_egg", "displayName": "Dragon Egg"}, {"id": 402, "stackSize": 64, "name": "sandstone_stairs", "displayName": "Sandstone Stairs"}, {"id": 403, "stackSize": 64, "name": "ender_chest", "displayName": "<PERSON><PERSON> Chest"}, {"id": 404, "stackSize": 64, "name": "emerald_block", "displayName": "Block of Emerald"}, {"id": 405, "stackSize": 64, "name": "oak_stairs", "displayName": "Oak Stairs"}, {"id": 406, "stackSize": 64, "name": "spruce_stairs", "displayName": "Spruce Stairs"}, {"id": 407, "stackSize": 64, "name": "birch_stairs", "displayName": "<PERSON> Stairs"}, {"id": 408, "stackSize": 64, "name": "jungle_stairs", "displayName": "Jungle Stairs"}, {"id": 409, "stackSize": 64, "name": "acacia_stairs", "displayName": "Acacia Stairs"}, {"id": 410, "stackSize": 64, "name": "cherry_stairs", "displayName": "<PERSON> Stairs"}, {"id": 411, "stackSize": 64, "name": "dark_oak_stairs", "displayName": "Dark Oak Stairs"}, {"id": 412, "stackSize": 64, "name": "pale_oak_stairs", "displayName": "Pale Oak Stairs"}, {"id": 413, "stackSize": 64, "name": "mangrove_stairs", "displayName": "Mangrove Stairs"}, {"id": 414, "stackSize": 64, "name": "bamboo_stairs", "displayName": "Bamboo Stairs"}, {"id": 415, "stackSize": 64, "name": "bamboo_mosaic_stairs", "displayName": "Bamboo Mosaic Stairs"}, {"id": 416, "stackSize": 64, "name": "crimson_stairs", "displayName": "Crimson Stairs"}, {"id": 417, "stackSize": 64, "name": "warped_stairs", "displayName": "Warped Stairs"}, {"id": 418, "stackSize": 64, "name": "command_block", "displayName": "Command Block"}, {"id": 419, "stackSize": 64, "name": "beacon", "displayName": "Beacon"}, {"id": 420, "stackSize": 64, "name": "cobblestone_wall", "displayName": "Cobblestone Wall"}, {"id": 421, "stackSize": 64, "name": "mossy_cobblestone_wall", "displayName": "<PERSON><PERSON>"}, {"id": 422, "stackSize": 64, "name": "brick_wall", "displayName": "Brick Wall"}, {"id": 423, "stackSize": 64, "name": "prismarine_wall", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 424, "stackSize": 64, "name": "red_sandstone_wall", "displayName": "Red Sandstone Wall"}, {"id": 425, "stackSize": 64, "name": "mossy_stone_brick_wall", "displayName": "Mossy Stone Brick Wall"}, {"id": 426, "stackSize": 64, "name": "granite_wall", "displayName": "Granite Wall"}, {"id": 427, "stackSize": 64, "name": "stone_brick_wall", "displayName": "Stone Brick Wall"}, {"id": 428, "stackSize": 64, "name": "mud_brick_wall", "displayName": "Mud Brick Wall"}, {"id": 429, "stackSize": 64, "name": "nether_brick_wall", "displayName": "Nether Brick Wall"}, {"id": 430, "stackSize": 64, "name": "andesite_wall", "displayName": "Andesite Wall"}, {"id": 431, "stackSize": 64, "name": "red_nether_brick_wall", "displayName": "Red Nether Brick Wall"}, {"id": 432, "stackSize": 64, "name": "sandstone_wall", "displayName": "Sandstone Wall"}, {"id": 433, "stackSize": 64, "name": "end_stone_brick_wall", "displayName": "End Stone Brick Wall"}, {"id": 434, "stackSize": 64, "name": "diorite_wall", "displayName": "Diorite Wall"}, {"id": 435, "stackSize": 64, "name": "blackstone_wall", "displayName": "Blackstone Wall"}, {"id": 436, "stackSize": 64, "name": "polished_blackstone_wall", "displayName": "Polished Blackstone Wall"}, {"id": 437, "stackSize": 64, "name": "polished_blackstone_brick_wall", "displayName": "Polished Blackstone Brick Wall"}, {"id": 438, "stackSize": 64, "name": "cobbled_deepslate_wall", "displayName": "Cobbled Deepslate Wall"}, {"id": 439, "stackSize": 64, "name": "polished_deepslate_wall", "displayName": "Polished Deepslate Wall"}, {"id": 440, "stackSize": 64, "name": "deepslate_brick_wall", "displayName": "Deepslate Brick Wall"}, {"id": 441, "stackSize": 64, "name": "deepslate_tile_wall", "displayName": "Deepslate Tile Wall"}, {"id": 442, "stackSize": 64, "name": "anvil", "displayName": "An<PERSON>"}, {"id": 443, "stackSize": 64, "name": "chipped_anvil", "displayName": "Chipped Anvil"}, {"id": 444, "stackSize": 64, "name": "damaged_anvil", "displayName": "Damaged Anvil"}, {"id": 445, "stackSize": 64, "name": "chiseled_quartz_block", "displayName": "Chiseled Quartz Block"}, {"id": 446, "stackSize": 64, "name": "quartz_block", "displayName": "Block of Quartz"}, {"id": 447, "stackSize": 64, "name": "quartz_bricks", "displayName": "Quartz Bricks"}, {"id": 448, "stackSize": 64, "name": "quartz_pillar", "displayName": "Quartz <PERSON>"}, {"id": 449, "stackSize": 64, "name": "quartz_stairs", "displayName": "Quartz Stairs"}, {"id": 450, "stackSize": 64, "name": "white_terracotta", "displayName": "White Terracotta"}, {"id": 451, "stackSize": 64, "name": "orange_terracotta", "displayName": "Orange Terracotta"}, {"id": 452, "stackSize": 64, "name": "magenta_terracotta", "displayName": "Magenta Terracotta"}, {"id": 453, "stackSize": 64, "name": "light_blue_terracotta", "displayName": "Light Blue Terracotta"}, {"id": 454, "stackSize": 64, "name": "yellow_terracotta", "displayName": "Yellow Terracotta"}, {"id": 455, "stackSize": 64, "name": "lime_terracotta", "displayName": "Lime Terracotta"}, {"id": 456, "stackSize": 64, "name": "pink_terracotta", "displayName": "Pink Terracotta"}, {"id": 457, "stackSize": 64, "name": "gray_terracotta", "displayName": "Gray <PERSON>"}, {"id": 458, "stackSize": 64, "name": "light_gray_terracotta", "displayName": "Light Gray Terracotta"}, {"id": 459, "stackSize": 64, "name": "cyan_terracotta", "displayName": "<PERSON><PERSON>"}, {"id": 460, "stackSize": 64, "name": "purple_terracotta", "displayName": "Purple Terracotta"}, {"id": 461, "stackSize": 64, "name": "blue_terracotta", "displayName": "Blue Terracotta"}, {"id": 462, "stackSize": 64, "name": "brown_terracotta", "displayName": "Brown Terracotta"}, {"id": 463, "stackSize": 64, "name": "green_terracotta", "displayName": "Green Terracotta"}, {"id": 464, "stackSize": 64, "name": "red_terracotta", "displayName": "Red Terracotta"}, {"id": 465, "stackSize": 64, "name": "black_terracotta", "displayName": "Black Terracotta"}, {"id": 466, "stackSize": 64, "name": "barrier", "displayName": "Barrier"}, {"id": 467, "stackSize": 64, "name": "light_block", "displayName": "Light"}, {"id": 468, "stackSize": 64, "name": "hay_block", "displayName": "<PERSON>"}, {"id": 469, "stackSize": 64, "name": "white_carpet", "displayName": "White Carpet"}, {"id": 470, "stackSize": 64, "name": "orange_carpet", "displayName": "Orange Carpet"}, {"id": 471, "stackSize": 64, "name": "magenta_carpet", "displayName": "Magenta Carpet"}, {"id": 472, "stackSize": 64, "name": "light_blue_carpet", "displayName": "Light Blue Carpet"}, {"id": 473, "stackSize": 64, "name": "yellow_carpet", "displayName": "Yellow Carpet"}, {"id": 474, "stackSize": 64, "name": "lime_carpet", "displayName": "Lime Carpet"}, {"id": 475, "stackSize": 64, "name": "pink_carpet", "displayName": "Pink Carpet"}, {"id": 476, "stackSize": 64, "name": "gray_carpet", "displayName": "<PERSON> Carpet"}, {"id": 477, "stackSize": 64, "name": "light_gray_carpet", "displayName": "Light Gray Carpet"}, {"id": 478, "stackSize": 64, "name": "cyan_carpet", "displayName": "<PERSON><PERSON>"}, {"id": 479, "stackSize": 64, "name": "purple_carpet", "displayName": "Purple Carpet"}, {"id": 480, "stackSize": 64, "name": "blue_carpet", "displayName": "Blue Carpet"}, {"id": 481, "stackSize": 64, "name": "brown_carpet", "displayName": "Brown Carpet"}, {"id": 482, "stackSize": 64, "name": "green_carpet", "displayName": "Green Carpet"}, {"id": 483, "stackSize": 64, "name": "red_carpet", "displayName": "Red Carpet"}, {"id": 484, "stackSize": 64, "name": "black_carpet", "displayName": "Black Carpet"}, {"id": 485, "stackSize": 64, "name": "hardened_clay", "displayName": "Terracotta"}, {"id": 486, "stackSize": 64, "name": "packed_ice", "displayName": "Packed Ice"}, {"id": 487, "stackSize": 64, "name": "grass_path", "displayName": "Dirt Path"}, {"id": 488, "stackSize": 64, "name": "sunflower", "displayName": "Sunflower"}, {"id": 489, "stackSize": 64, "name": "lilac", "displayName": "Lilac"}, {"id": 490, "stackSize": 64, "name": "rose_bush", "displayName": "<PERSON>"}, {"id": 491, "stackSize": 64, "name": "peony", "displayName": "Peony"}, {"id": 492, "stackSize": 64, "name": "tall_grass", "displayName": "Tall Grass"}, {"id": 493, "stackSize": 64, "name": "large_fern", "displayName": "Large Fern"}, {"id": 494, "stackSize": 64, "name": "white_stained_glass", "displayName": "White Stained Glass"}, {"id": 495, "stackSize": 64, "name": "orange_stained_glass", "displayName": "Orange Stained Glass"}, {"id": 496, "stackSize": 64, "name": "magenta_stained_glass", "displayName": "Magenta Stained Glass"}, {"id": 497, "stackSize": 64, "name": "light_blue_stained_glass", "displayName": "Light Blue Stained Glass"}, {"id": 498, "stackSize": 64, "name": "yellow_stained_glass", "displayName": "Yellow Stained Glass"}, {"id": 499, "stackSize": 64, "name": "lime_stained_glass", "displayName": "Lime Stained Glass"}, {"id": 500, "stackSize": 64, "name": "pink_stained_glass", "displayName": "Pink Stained Glass"}, {"id": 501, "stackSize": 64, "name": "gray_stained_glass", "displayName": "<PERSON> Stained Glass"}, {"id": 502, "stackSize": 64, "name": "light_gray_stained_glass", "displayName": "Light Gray Stained Glass"}, {"id": 503, "stackSize": 64, "name": "cyan_stained_glass", "displayName": "<PERSON><PERSON>"}, {"id": 504, "stackSize": 64, "name": "purple_stained_glass", "displayName": "Purple Stained Glass"}, {"id": 505, "stackSize": 64, "name": "blue_stained_glass", "displayName": "Blue Stained Glass"}, {"id": 506, "stackSize": 64, "name": "brown_stained_glass", "displayName": "<PERSON> Stained Glass"}, {"id": 507, "stackSize": 64, "name": "green_stained_glass", "displayName": "Green Stained Glass"}, {"id": 508, "stackSize": 64, "name": "red_stained_glass", "displayName": "Red Stained Glass"}, {"id": 509, "stackSize": 64, "name": "black_stained_glass", "displayName": "Black Stained Glass"}, {"id": 510, "stackSize": 64, "name": "white_stained_glass_pane", "displayName": "White Stained Glass Pane"}, {"id": 511, "stackSize": 64, "name": "orange_stained_glass_pane", "displayName": "Orange Stained Glass Pane"}, {"id": 512, "stackSize": 64, "name": "magenta_stained_glass_pane", "displayName": "Magenta Stained Glass Pane"}, {"id": 513, "stackSize": 64, "name": "light_blue_stained_glass_pane", "displayName": "Light Blue Stained Glass Pane"}, {"id": 514, "stackSize": 64, "name": "yellow_stained_glass_pane", "displayName": "Yellow Stained Glass Pane"}, {"id": 515, "stackSize": 64, "name": "lime_stained_glass_pane", "displayName": "Lime Stained Glass Pane"}, {"id": 516, "stackSize": 64, "name": "pink_stained_glass_pane", "displayName": "Pink Stained Glass Pane"}, {"id": 517, "stackSize": 64, "name": "gray_stained_glass_pane", "displayName": "Gray Stained Glass Pane"}, {"id": 518, "stackSize": 64, "name": "light_gray_stained_glass_pane", "displayName": "Light Gray Stained Glass Pane"}, {"id": 519, "stackSize": 64, "name": "cyan_stained_glass_pane", "displayName": "<PERSON><PERSON> Stained Glass Pane"}, {"id": 520, "stackSize": 64, "name": "purple_stained_glass_pane", "displayName": "Purple Stained Glass Pane"}, {"id": 521, "stackSize": 64, "name": "blue_stained_glass_pane", "displayName": "Blue Stained Glass Pane"}, {"id": 522, "stackSize": 64, "name": "brown_stained_glass_pane", "displayName": "<PERSON> Stained Glass Pane"}, {"id": 523, "stackSize": 64, "name": "green_stained_glass_pane", "displayName": "Green Stained Glass Pane"}, {"id": 524, "stackSize": 64, "name": "red_stained_glass_pane", "displayName": "Red Stained Glass Pane"}, {"id": 525, "stackSize": 64, "name": "black_stained_glass_pane", "displayName": "Black Stained Glass Pane"}, {"id": 526, "stackSize": 64, "name": "prismarine", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 527, "stackSize": 64, "name": "prismarine_bricks", "displayName": "Prismarine <PERSON>s"}, {"id": 528, "stackSize": 64, "name": "dark_prismarine", "displayName": "<PERSON>"}, {"id": 529, "stackSize": 64, "name": "prismarine_stairs", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 530, "stackSize": 64, "name": "prismarine_bricks_stairs", "displayName": "Prismarine Brick Stairs"}, {"id": 531, "stackSize": 64, "name": "dark_prismarine_stairs", "displayName": "<PERSON> <PERSON><PERSON><PERSON><PERSON>"}, {"id": 532, "stackSize": 64, "name": "sea_lantern", "displayName": "Sea Lantern"}, {"id": 533, "stackSize": 64, "name": "red_sandstone", "displayName": "Red Sandstone"}, {"id": 534, "stackSize": 64, "name": "chiseled_red_sandstone", "displayName": "Chiseled Red Sandstone"}, {"id": 535, "stackSize": 64, "name": "cut_red_sandstone", "displayName": "Cut Red Sandstone"}, {"id": 536, "stackSize": 64, "name": "red_sandstone_stairs", "displayName": "Red Sandstone Stairs"}, {"id": 537, "stackSize": 64, "name": "repeating_command_block", "displayName": "Repeating Command Block"}, {"id": 538, "stackSize": 64, "name": "chain_command_block", "displayName": "Chain Command Block"}, {"id": 539, "stackSize": 64, "name": "magma", "displayName": "Magma Block"}, {"id": 540, "stackSize": 64, "name": "nether_wart_block", "displayName": "Nether Wart Block"}, {"id": 541, "stackSize": 64, "name": "warped_wart_block", "displayName": "Warped Wart Block"}, {"id": 542, "stackSize": 64, "name": "red_nether_brick", "displayName": "Red Nether Bricks"}, {"id": 543, "stackSize": 64, "name": "bone_block", "displayName": "Bone Block"}, {"id": 544, "stackSize": 64, "name": "structure_void", "displayName": "Structure Void"}, {"id": 545, "stackSize": 1, "name": "undyed_shulker_box", "displayName": "Shulker Box"}, {"id": 546, "stackSize": 1, "name": "white_shulker_box", "displayName": "White Shulker Box"}, {"id": 547, "stackSize": 1, "name": "orange_shulker_box", "displayName": "Orange Shulker Box"}, {"id": 548, "stackSize": 1, "name": "magenta_shulker_box", "displayName": "<PERSON><PERSON>a <PERSON>er Box"}, {"id": 549, "stackSize": 1, "name": "light_blue_shulker_box", "displayName": "Light Blue Shulker Box"}, {"id": 550, "stackSize": 1, "name": "yellow_shulker_box", "displayName": "Yellow Shulker Box"}, {"id": 551, "stackSize": 1, "name": "lime_shulker_box", "displayName": "<PERSON>e <PERSON>er Box"}, {"id": 552, "stackSize": 1, "name": "pink_shulker_box", "displayName": "Pink Shulker Box"}, {"id": 553, "stackSize": 1, "name": "gray_shulker_box", "displayName": "<PERSON>"}, {"id": 554, "stackSize": 1, "name": "light_gray_shulker_box", "displayName": "Light Gray Shulker Box"}, {"id": 555, "stackSize": 1, "name": "cyan_shulker_box", "displayName": "<PERSON><PERSON>"}, {"id": 556, "stackSize": 1, "name": "purple_shulker_box", "displayName": "Purple Shulker Box"}, {"id": 557, "stackSize": 1, "name": "blue_shulker_box", "displayName": "Blue Shulker Box"}, {"id": 558, "stackSize": 1, "name": "brown_shulker_box", "displayName": "<PERSON> Shulker Box"}, {"id": 559, "stackSize": 1, "name": "green_shulker_box", "displayName": "Green Shulker Box"}, {"id": 560, "stackSize": 1, "name": "red_shulker_box", "displayName": "Red Shulker Box"}, {"id": 561, "stackSize": 1, "name": "black_shulker_box", "displayName": "Black Shulker Box"}, {"id": 562, "stackSize": 64, "name": "white_glazed_terracotta", "displayName": "White Glazed Terracotta"}, {"id": 563, "stackSize": 64, "name": "orange_glazed_terracotta", "displayName": "Orange Glazed Terracotta"}, {"id": 564, "stackSize": 64, "name": "magenta_glazed_terracotta", "displayName": "Magenta Glazed Terracotta"}, {"id": 565, "stackSize": 64, "name": "light_blue_glazed_terracotta", "displayName": "Light Blue Glazed Terracotta"}, {"id": 566, "stackSize": 64, "name": "yellow_glazed_terracotta", "displayName": "Yellow Glazed Terracotta"}, {"id": 567, "stackSize": 64, "name": "lime_glazed_terracotta", "displayName": "Lime Glazed Terracotta"}, {"id": 568, "stackSize": 64, "name": "pink_glazed_terracotta", "displayName": "Pink Glazed Terracotta"}, {"id": 569, "stackSize": 64, "name": "gray_glazed_terracotta", "displayName": "Gray Glazed Terracotta"}, {"id": 570, "stackSize": 64, "name": "silver_glazed_terracotta", "displayName": "Light Gray Glazed Terracotta"}, {"id": 571, "stackSize": 64, "name": "cyan_glazed_terracotta", "displayName": "<PERSON><PERSON>zed Terracotta"}, {"id": 572, "stackSize": 64, "name": "purple_glazed_terracotta", "displayName": "Purple Glazed Terracotta"}, {"id": 573, "stackSize": 64, "name": "blue_glazed_terracotta", "displayName": "Blue Glazed Terracotta"}, {"id": 574, "stackSize": 64, "name": "brown_glazed_terracotta", "displayName": "Brown Glazed Terracotta"}, {"id": 575, "stackSize": 64, "name": "green_glazed_terracotta", "displayName": "Green Glazed Terracotta"}, {"id": 576, "stackSize": 64, "name": "red_glazed_terracotta", "displayName": "Red Glazed Terracotta"}, {"id": 577, "stackSize": 64, "name": "black_glazed_terracotta", "displayName": "Black Glazed Terracotta"}, {"id": 578, "stackSize": 64, "name": "white_concrete", "displayName": "White Concrete"}, {"id": 579, "stackSize": 64, "name": "orange_concrete", "displayName": "Orange Concrete"}, {"id": 580, "stackSize": 64, "name": "magenta_concrete", "displayName": "Magenta Concrete"}, {"id": 581, "stackSize": 64, "name": "light_blue_concrete", "displayName": "Light Blue Concrete"}, {"id": 582, "stackSize": 64, "name": "yellow_concrete", "displayName": "Yellow Concrete"}, {"id": 583, "stackSize": 64, "name": "lime_concrete", "displayName": "Lime Concrete"}, {"id": 584, "stackSize": 64, "name": "pink_concrete", "displayName": "Pink Concrete"}, {"id": 585, "stackSize": 64, "name": "gray_concrete", "displayName": "<PERSON>"}, {"id": 586, "stackSize": 64, "name": "light_gray_concrete", "displayName": "Light Gray Concrete"}, {"id": 587, "stackSize": 64, "name": "cyan_concrete", "displayName": "<PERSON><PERSON>"}, {"id": 588, "stackSize": 64, "name": "purple_concrete", "displayName": "Purple Concrete"}, {"id": 589, "stackSize": 64, "name": "blue_concrete", "displayName": "Blue Concrete"}, {"id": 590, "stackSize": 64, "name": "brown_concrete", "displayName": "<PERSON> Concrete"}, {"id": 591, "stackSize": 64, "name": "green_concrete", "displayName": "Green Concrete"}, {"id": 592, "stackSize": 64, "name": "red_concrete", "displayName": "Red Concrete"}, {"id": 593, "stackSize": 64, "name": "black_concrete", "displayName": "Black Concrete"}, {"id": 594, "stackSize": 64, "name": "white_concrete_powder", "displayName": "White Concrete Powder"}, {"id": 595, "stackSize": 64, "name": "orange_concrete_powder", "displayName": "Orange Concrete Powder"}, {"id": 596, "stackSize": 64, "name": "magenta_concrete_powder", "displayName": "Magenta Concrete Powder"}, {"id": 597, "stackSize": 64, "name": "light_blue_concrete_powder", "displayName": "Light Blue Concrete Powder"}, {"id": 598, "stackSize": 64, "name": "yellow_concrete_powder", "displayName": "Yellow Concrete Powder"}, {"id": 599, "stackSize": 64, "name": "lime_concrete_powder", "displayName": "Lime Concrete <PERSON>"}, {"id": 600, "stackSize": 64, "name": "pink_concrete_powder", "displayName": "Pink Concrete Powder"}, {"id": 601, "stackSize": 64, "name": "gray_concrete_powder", "displayName": "<PERSON> Concre<PERSON>"}, {"id": 602, "stackSize": 64, "name": "light_gray_concrete_powder", "displayName": "Light Gray Concrete Powder"}, {"id": 603, "stackSize": 64, "name": "cyan_concrete_powder", "displayName": "<PERSON><PERSON>"}, {"id": 604, "stackSize": 64, "name": "purple_concrete_powder", "displayName": "Purple Concrete Powder"}, {"id": 605, "stackSize": 64, "name": "blue_concrete_powder", "displayName": "Blue Concrete Powder"}, {"id": 606, "stackSize": 64, "name": "brown_concrete_powder", "displayName": "<PERSON> Concrete <PERSON>"}, {"id": 607, "stackSize": 64, "name": "green_concrete_powder", "displayName": "Green Concrete Powder"}, {"id": 608, "stackSize": 64, "name": "red_concrete_powder", "displayName": "Red Concrete Powder"}, {"id": 609, "stackSize": 64, "name": "black_concrete_powder", "displayName": "Black Concrete Powder"}, {"id": 610, "stackSize": 64, "name": "turtle_egg", "displayName": "Turtle Egg"}, {"id": 611, "stackSize": 64, "name": "sniffer_egg", "displayName": "Sniffer Egg"}, {"id": 612, "stackSize": 64, "name": "dead_tube_coral_block", "displayName": "Dead Tube Coral Block"}, {"id": 613, "stackSize": 64, "name": "dead_brain_coral_block", "displayName": "Dead Brain Coral Block"}, {"id": 614, "stackSize": 64, "name": "dead_bubble_coral_block", "displayName": "Dead Bubble Coral Block"}, {"id": 615, "stackSize": 64, "name": "dead_fire_coral_block", "displayName": "Dead Fire Coral Block"}, {"id": 616, "stackSize": 64, "name": "dead_horn_coral_block", "displayName": "Dead Horn Coral Block"}, {"id": 617, "stackSize": 64, "name": "tube_coral_block", "displayName": "Tube Coral Block"}, {"id": 618, "stackSize": 64, "name": "brain_coral_block", "displayName": "Brain <PERSON>"}, {"id": 619, "stackSize": 64, "name": "bubble_coral_block", "displayName": "Bubble Coral Block"}, {"id": 620, "stackSize": 64, "name": "fire_coral_block", "displayName": "Fire Coral Block"}, {"id": 621, "stackSize": 64, "name": "horn_coral_block", "displayName": "Horn Coral Block"}, {"id": 622, "stackSize": 64, "name": "tube_coral", "displayName": "Tube Coral"}, {"id": 623, "stackSize": 64, "name": "brain_coral", "displayName": "Brain Coral"}, {"id": 624, "stackSize": 64, "name": "bubble_coral", "displayName": "Bubble Coral"}, {"id": 625, "stackSize": 64, "name": "fire_coral", "displayName": "Fire Coral"}, {"id": 626, "stackSize": 64, "name": "horn_coral", "displayName": "Horn Coral"}, {"id": 627, "stackSize": 64, "name": "dead_brain_coral", "displayName": "Dead Brain Coral"}, {"id": 628, "stackSize": 64, "name": "dead_bubble_coral", "displayName": "Dead Bubble Coral"}, {"id": 629, "stackSize": 64, "name": "dead_fire_coral", "displayName": "Dead Fire Coral"}, {"id": 630, "stackSize": 64, "name": "dead_horn_coral", "displayName": "Dead Horn Coral"}, {"id": 631, "stackSize": 64, "name": "dead_tube_coral", "displayName": "Dead Tube Coral"}, {"id": 632, "stackSize": 64, "name": "tube_coral_fan", "displayName": "Tube Coral Fan"}, {"id": 633, "stackSize": 64, "name": "brain_coral_fan", "displayName": "Brain Coral Fan"}, {"id": 634, "stackSize": 64, "name": "bubble_coral_fan", "displayName": "Bubble Coral Fan"}, {"id": 635, "stackSize": 64, "name": "fire_coral_fan", "displayName": "Fire Coral Fan"}, {"id": 636, "stackSize": 64, "name": "horn_coral_fan", "displayName": "Horn Coral Fan"}, {"id": 637, "stackSize": 64, "name": "dead_tube_coral_fan", "displayName": "Dead Tube Coral Fan"}, {"id": 638, "stackSize": 64, "name": "dead_brain_coral_fan", "displayName": "Dead Brain Coral Fan"}, {"id": 639, "stackSize": 64, "name": "dead_bubble_coral_fan", "displayName": "Dead Bubble Coral Fan"}, {"id": 640, "stackSize": 64, "name": "dead_fire_coral_fan", "displayName": "Dead Fire Coral Fan"}, {"id": 641, "stackSize": 64, "name": "dead_horn_coral_fan", "displayName": "Dead Horn Coral Fan"}, {"id": 642, "stackSize": 64, "name": "blue_ice", "displayName": "Blue Ice"}, {"id": 643, "stackSize": 64, "name": "conduit", "displayName": "Conduit"}, {"id": 644, "stackSize": 64, "name": "polished_granite_stairs", "displayName": "Polished Granite Stairs"}, {"id": 645, "stackSize": 64, "name": "smooth_red_sandstone_stairs", "displayName": "Smooth Red Sandstone Stairs"}, {"id": 646, "stackSize": 64, "name": "mossy_stone_brick_stairs", "displayName": "Mossy Stone Brick Stairs"}, {"id": 647, "stackSize": 64, "name": "polished_diorite_stairs", "displayName": "Polished Diorite Stairs"}, {"id": 648, "stackSize": 64, "name": "mossy_cobblestone_stairs", "displayName": "Mossy Cobblestone Stairs"}, {"id": 649, "stackSize": 64, "name": "end_brick_stairs", "displayName": "End Stone Brick Stairs"}, {"id": 650, "stackSize": 64, "name": "normal_stone_stairs", "displayName": "Stone Stairs"}, {"id": 651, "stackSize": 64, "name": "smooth_sandstone_stairs", "displayName": "Smooth Sandstone Stairs"}, {"id": 652, "stackSize": 64, "name": "smooth_quartz_stairs", "displayName": "Smooth Quartz Stairs"}, {"id": 653, "stackSize": 64, "name": "granite_stairs", "displayName": "Granite Stairs"}, {"id": 654, "stackSize": 64, "name": "andesite_stairs", "displayName": "Andesite Stairs"}, {"id": 655, "stackSize": 64, "name": "red_nether_brick_stairs", "displayName": "Red Nether Brick Stairs"}, {"id": 656, "stackSize": 64, "name": "polished_andesite_stairs", "displayName": "Polished Andesite Stairs"}, {"id": 657, "stackSize": 64, "name": "diorite_stairs", "displayName": "Diorite Stairs"}, {"id": 658, "stackSize": 64, "name": "cobbled_deepslate_stairs", "displayName": "Cobbled Deepslate Stairs"}, {"id": 659, "stackSize": 64, "name": "polished_deepslate_stairs", "displayName": "Polished Deepslate Stairs"}, {"id": 660, "stackSize": 64, "name": "deepslate_brick_stairs", "displayName": "Deepslate Brick Stairs"}, {"id": 661, "stackSize": 64, "name": "deepslate_tile_stairs", "displayName": "Deepslate Tile Stairs"}, {"id": 662, "stackSize": 64, "name": "polished_granite_slab", "displayName": "Polished Granite Slab"}, {"id": 663, "stackSize": 64, "name": "smooth_red_sandstone_slab", "displayName": "Smooth Red Sandstone Slab"}, {"id": 664, "stackSize": 64, "name": "mossy_stone_brick_slab", "displayName": "Mossy Stone Brick Slab"}, {"id": 665, "stackSize": 64, "name": "polished_diorite_slab", "displayName": "Polished Diorite S<PERSON>b"}, {"id": 666, "stackSize": 64, "name": "mossy_cobblestone_slab", "displayName": "<PERSON><PERSON> Slab"}, {"id": 667, "stackSize": 64, "name": "end_stone_brick_slab", "displayName": "End Stone Brick Slab"}, {"id": 668, "stackSize": 64, "name": "smooth_sandstone_slab", "displayName": "Smooth Sandstone Slab"}, {"id": 669, "stackSize": 64, "name": "smooth_quartz_slab", "displayName": "Smooth Quartz Slab"}, {"id": 670, "stackSize": 64, "name": "granite_slab", "displayName": "Granite Slab"}, {"id": 671, "stackSize": 64, "name": "andesite_slab", "displayName": "Andesite Slab"}, {"id": 672, "stackSize": 64, "name": "red_nether_brick_slab", "displayName": "Red Nether Brick Slab"}, {"id": 673, "stackSize": 64, "name": "polished_andesite_slab", "displayName": "Polished Andesite Slab"}, {"id": 674, "stackSize": 64, "name": "diorite_slab", "displayName": "Diorite Slab"}, {"id": 675, "stackSize": 64, "name": "cobbled_deepslate_slab", "displayName": "Cobbled Deepslate Slab"}, {"id": 676, "stackSize": 64, "name": "polished_deepslate_slab", "displayName": "Polished Deepslate Slab"}, {"id": 677, "stackSize": 64, "name": "deepslate_brick_slab", "displayName": "Deepslate Brick Slab"}, {"id": 678, "stackSize": 64, "name": "deepslate_tile_slab", "displayName": "Deepslate Tile Slab"}, {"id": 679, "stackSize": 64, "name": "scaffolding", "displayName": "Scaffolding"}, {"id": 680, "stackSize": 64, "name": "redstone", "displayName": "Redstone Dust"}, {"id": 681, "stackSize": 64, "name": "redstone_torch", "displayName": "Redstone Torch"}, {"id": 682, "stackSize": 64, "name": "redstone_block", "displayName": "Block of Redstone"}, {"id": 683, "stackSize": 64, "name": "repeater", "displayName": "Redstone Repeater"}, {"id": 684, "stackSize": 64, "name": "comparator", "displayName": "Redstone Comparator"}, {"id": 685, "stackSize": 64, "name": "piston", "displayName": "<PERSON><PERSON>"}, {"id": 686, "stackSize": 64, "name": "sticky_piston", "displayName": "<PERSON><PERSON>"}, {"id": 687, "stackSize": 64, "name": "slime", "displayName": "Slime Block"}, {"id": 688, "stackSize": 64, "name": "honey_block", "displayName": "Honey Block"}, {"id": 689, "stackSize": 64, "name": "observer", "displayName": "Observer"}, {"id": 690, "stackSize": 64, "name": "hopper", "displayName": "<PERSON>"}, {"id": 691, "stackSize": 64, "name": "dispenser", "displayName": "Dispenser"}, {"id": 692, "stackSize": 64, "name": "dropper", "displayName": "Dropper"}, {"id": 693, "stackSize": 64, "name": "lectern", "displayName": "Lectern"}, {"id": 694, "stackSize": 64, "name": "target", "displayName": "Target"}, {"id": 695, "stackSize": 64, "name": "lever", "displayName": "Lever"}, {"id": 696, "stackSize": 64, "name": "lightning_rod", "displayName": "Lightning Rod"}, {"id": 697, "stackSize": 64, "name": "daylight_detector", "displayName": "Daylight Detector"}, {"id": 698, "stackSize": 64, "name": "sculk_sensor", "displayName": "Sculk Sensor"}, {"id": 699, "stackSize": 64, "name": "calibrated_sculk_sensor", "displayName": "Calibrated Sculk Sensor"}, {"id": 700, "stackSize": 64, "name": "tripwire_hook", "displayName": "Tripwire Hook"}, {"id": 701, "stackSize": 64, "name": "trapped_chest", "displayName": "Trapped Chest"}, {"id": 702, "stackSize": 64, "name": "tnt", "displayName": "TNT"}, {"id": 703, "stackSize": 64, "name": "redstone_lamp", "displayName": "Redstone Lamp"}, {"id": 704, "stackSize": 64, "name": "noteblock", "displayName": "Note Block"}, {"id": 705, "stackSize": 64, "name": "stone_button", "displayName": "<PERSON>"}, {"id": 706, "stackSize": 64, "name": "polished_blackstone_button", "displayName": "Polished Blackstone Button"}, {"id": 707, "stackSize": 64, "name": "wooden_button", "displayName": "Oak Button"}, {"id": 708, "stackSize": 64, "name": "spruce_button", "displayName": "Spruce Button"}, {"id": 709, "stackSize": 64, "name": "birch_button", "displayName": "<PERSON>"}, {"id": 710, "stackSize": 64, "name": "jungle_button", "displayName": "<PERSON>ton"}, {"id": 711, "stackSize": 64, "name": "acacia_button", "displayName": "Acacia <PERSON>"}, {"id": 712, "stackSize": 64, "name": "cherry_button", "displayName": "<PERSON>"}, {"id": 713, "stackSize": 64, "name": "dark_oak_button", "displayName": "Dark Oak Button"}, {"id": 714, "stackSize": 64, "name": "pale_oak_button", "displayName": "Pale Oak Button"}, {"id": 715, "stackSize": 64, "name": "mangrove_button", "displayName": "Mangrove Button"}, {"id": 716, "stackSize": 64, "name": "bamboo_button", "displayName": "Bamboo Button"}, {"id": 717, "stackSize": 64, "name": "crimson_button", "displayName": "<PERSON>"}, {"id": 718, "stackSize": 64, "name": "warped_button", "displayName": "Warped <PERSON>"}, {"id": 719, "stackSize": 64, "name": "stone_pressure_plate", "displayName": "Stone Pressure Plate"}, {"id": 720, "stackSize": 64, "name": "polished_blackstone_pressure_plate", "displayName": "Polished Blackstone Pressure Plate"}, {"id": 721, "stackSize": 64, "name": "light_weighted_pressure_plate", "displayName": "Light Weighted Pressure Plate"}, {"id": 722, "stackSize": 64, "name": "heavy_weighted_pressure_plate", "displayName": "Heavy Weighted Pressure Plate"}, {"id": 723, "stackSize": 64, "name": "wooden_pressure_plate", "displayName": "Oak Pressure Plate"}, {"id": 724, "stackSize": 64, "name": "spruce_pressure_plate", "displayName": "Spruce Pressure Plate"}, {"id": 725, "stackSize": 64, "name": "birch_pressure_plate", "displayName": "Birch Pressure Plate"}, {"id": 726, "stackSize": 64, "name": "jungle_pressure_plate", "displayName": "Jungle Pressure Plate"}, {"id": 727, "stackSize": 64, "name": "acacia_pressure_plate", "displayName": "Acacia Pressure Plate"}, {"id": 728, "stackSize": 64, "name": "cherry_pressure_plate", "displayName": "Cherry Pressure Plate"}, {"id": 729, "stackSize": 64, "name": "dark_oak_pressure_plate", "displayName": "Dark Oak Pressure Plate"}, {"id": 730, "stackSize": 64, "name": "pale_oak_pressure_plate", "displayName": "Pale Oak Pressure Plate"}, {"id": 731, "stackSize": 64, "name": "mangrove_pressure_plate", "displayName": "Mangrove Pressure Plate"}, {"id": 732, "stackSize": 64, "name": "bamboo_pressure_plate", "displayName": "Bamboo Pressure Plate"}, {"id": 733, "stackSize": 64, "name": "crimson_pressure_plate", "displayName": "Crimson Pressure Plate"}, {"id": 734, "stackSize": 64, "name": "warped_pressure_plate", "displayName": "Warped Pressure Plate"}, {"id": 735, "stackSize": 64, "name": "iron_door", "displayName": "Iron Door"}, {"id": 736, "stackSize": 64, "name": "wooden_door", "displayName": "Oak Door"}, {"id": 737, "stackSize": 64, "name": "spruce_door", "displayName": "Spruce Door"}, {"id": 738, "stackSize": 64, "name": "birch_door", "displayName": "<PERSON>"}, {"id": 739, "stackSize": 64, "name": "jungle_door", "displayName": "Jungle Door"}, {"id": 740, "stackSize": 64, "name": "acacia_door", "displayName": "Acacia Door"}, {"id": 741, "stackSize": 64, "name": "cherry_door", "displayName": "Cherry Door"}, {"id": 742, "stackSize": 64, "name": "dark_oak_door", "displayName": "Dark Oak Door"}, {"id": 743, "stackSize": 64, "name": "pale_oak_door", "displayName": "Pale Oak Door"}, {"id": 744, "stackSize": 64, "name": "mangrove_door", "displayName": "Mangrove Door"}, {"id": 745, "stackSize": 64, "name": "bamboo_door", "displayName": "Bamboo Door"}, {"id": 746, "stackSize": 64, "name": "crimson_door", "displayName": "Crimson Door"}, {"id": 747, "stackSize": 64, "name": "warped_door", "displayName": "Warped Door"}, {"id": 748, "stackSize": 64, "name": "copper_door", "displayName": "Copper Door"}, {"id": 749, "stackSize": 64, "name": "exposed_copper_door", "displayName": "Exposed Copper Door"}, {"id": 750, "stackSize": 64, "name": "weathered_copper_door", "displayName": "Weathered Copper Door"}, {"id": 751, "stackSize": 64, "name": "oxidized_copper_door", "displayName": "Oxidized Copper Door"}, {"id": 752, "stackSize": 64, "name": "waxed_copper_door", "displayName": "Waxed Copper Door"}, {"id": 753, "stackSize": 64, "name": "waxed_exposed_copper_door", "displayName": "Waxed Exposed Copper Door"}, {"id": 754, "stackSize": 64, "name": "waxed_weathered_copper_door", "displayName": "Waxed Weathered Copper Door"}, {"id": 755, "stackSize": 64, "name": "waxed_oxidized_copper_door", "displayName": "Waxed Oxidized Copper Door"}, {"id": 756, "stackSize": 64, "name": "iron_trapdoor", "displayName": "Iron Trapdoor"}, {"id": 757, "stackSize": 64, "name": "trapdoor", "displayName": "Oak Trapdoor"}, {"id": 758, "stackSize": 64, "name": "spruce_trapdoor", "displayName": "Spruce Trapdoor"}, {"id": 759, "stackSize": 64, "name": "birch_trapdoor", "displayName": "<PERSON>"}, {"id": 760, "stackSize": 64, "name": "jungle_trapdoor", "displayName": "Jungle Trapdoor"}, {"id": 761, "stackSize": 64, "name": "acacia_trapdoor", "displayName": "Acacia T<PERSON>door"}, {"id": 762, "stackSize": 64, "name": "cherry_trapdoor", "displayName": "Cherry Trapdoor"}, {"id": 763, "stackSize": 64, "name": "dark_oak_trapdoor", "displayName": "Dark Oak Trapdoor"}, {"id": 764, "stackSize": 64, "name": "pale_oak_trapdoor", "displayName": "Pale Oak Trapdoor"}, {"id": 765, "stackSize": 64, "name": "mangrove_trapdoor", "displayName": "Mangrove Trapdoor"}, {"id": 766, "stackSize": 64, "name": "bamboo_trapdoor", "displayName": "Bamboo Trapdoor"}, {"id": 767, "stackSize": 64, "name": "crimson_trapdoor", "displayName": "Crimson Trapdoor"}, {"id": 768, "stackSize": 64, "name": "warped_trapdoor", "displayName": "Warped Trapdoor"}, {"id": 769, "stackSize": 64, "name": "copper_trapdoor", "displayName": "Copper Trapdoor"}, {"id": 770, "stackSize": 64, "name": "exposed_copper_trapdoor", "displayName": "Exposed Copper Trapdoor"}, {"id": 771, "stackSize": 64, "name": "weathered_copper_trapdoor", "displayName": "Weathered Copper Trapdoor"}, {"id": 772, "stackSize": 64, "name": "oxidized_copper_trapdoor", "displayName": "Oxidized Copper Trapdoor"}, {"id": 773, "stackSize": 64, "name": "waxed_copper_trapdoor", "displayName": "Waxed Copper Trapdoor"}, {"id": 774, "stackSize": 64, "name": "waxed_exposed_copper_trapdoor", "displayName": "Waxed Exposed Copper Trapdoor"}, {"id": 775, "stackSize": 64, "name": "waxed_weathered_copper_trapdoor", "displayName": "Waxed Weathered Copper Trapdoor"}, {"id": 776, "stackSize": 64, "name": "waxed_oxidized_copper_trapdoor", "displayName": "Waxed Oxidized Copper Trapdoor"}, {"id": 777, "stackSize": 64, "name": "fence_gate", "displayName": "Oak Fence Gate"}, {"id": 778, "stackSize": 64, "name": "spruce_fence_gate", "displayName": "Spruce Fence Gate"}, {"id": 779, "stackSize": 64, "name": "birch_fence_gate", "displayName": "Birch Fence Gate"}, {"id": 780, "stackSize": 64, "name": "jungle_fence_gate", "displayName": "Jungle Fence Gate"}, {"id": 781, "stackSize": 64, "name": "acacia_fence_gate", "displayName": "Acacia Fence Gate"}, {"id": 782, "stackSize": 64, "name": "cherry_fence_gate", "displayName": "Cherry Fence Gate"}, {"id": 783, "stackSize": 64, "name": "dark_oak_fence_gate", "displayName": "Dark Oak Fence Gate"}, {"id": 784, "stackSize": 64, "name": "pale_oak_fence_gate", "displayName": "Pale Oak Fence Gate"}, {"id": 785, "stackSize": 64, "name": "mangrove_fence_gate", "displayName": "Mangrove Fence Gate"}, {"id": 786, "stackSize": 64, "name": "bamboo_fence_gate", "displayName": "Bamboo Fence Gate"}, {"id": 787, "stackSize": 64, "name": "crimson_fence_gate", "displayName": "Crimson Fence Gate"}, {"id": 788, "stackSize": 64, "name": "warped_fence_gate", "displayName": "Warped Fence Gate"}, {"id": 789, "stackSize": 64, "name": "golden_rail", "displayName": "Powered Rail"}, {"id": 790, "stackSize": 64, "name": "detector_rail", "displayName": "Detector Rail"}, {"id": 791, "stackSize": 64, "name": "rail", "displayName": "Rail"}, {"id": 792, "stackSize": 64, "name": "activator_rail", "displayName": "Activator Rail"}, {"id": 793, "stackSize": 1, "name": "saddle", "displayName": "Saddle"}, {"id": 794, "stackSize": 1, "name": "minecart", "displayName": "Minecart"}, {"id": 795, "stackSize": 1, "name": "chest_minecart", "displayName": "Minecart with Chest"}, {"id": 796, "displayName": "Minecart with Furnace", "name": "hopper_minecart", "stackSize": 1, "metadata": 0, "variations": [{"metadata": 0, "id": 798, "name": "hopper_minecart", "displayName": "Minecart with <PERSON>", "stackSize": 1}]}, {"id": 797, "stackSize": 1, "name": "tnt_minecart", "displayName": "Minecart with TNT"}, {"id": 799, "stackSize": 1, "name": "carrot_on_a_stick", "displayName": "Carrot on a Stick", "enchantCategories": ["durability", "vanishing"], "maxDurability": 25}, {"id": 800, "stackSize": 1, "name": "warped_fungus_on_a_stick", "displayName": "Warped Fungus on a Stick", "enchantCategories": ["durability", "vanishing"], "maxDurability": 100}, {"id": 801, "stackSize": 64, "name": "phantom_membrane", "displayName": "Phantom Membrane"}, {"id": 802, "stackSize": 1, "name": "elytra", "displayName": "Elytra", "enchantCategories": ["equippable", "durability", "vanishing"], "repairWith": ["phantom_membrane"], "maxDurability": 432}, {"id": 803, "stackSize": 1, "name": "oak_boat", "displayName": "Oak Boat"}, {"id": 804, "stackSize": 1, "name": "oak_chest_boat", "displayName": "Oak Boat with Chest"}, {"id": 805, "stackSize": 1, "name": "spruce_boat", "displayName": "Spruce Boat"}, {"id": 806, "stackSize": 1, "name": "spruce_chest_boat", "displayName": "Spruce Boat with Chest"}, {"id": 807, "stackSize": 1, "name": "birch_boat", "displayName": "<PERSON> Boat"}, {"id": 808, "stackSize": 1, "name": "birch_chest_boat", "displayName": "<PERSON> Boat with Chest"}, {"id": 809, "stackSize": 1, "name": "jungle_boat", "displayName": "Jungle Boat"}, {"id": 810, "stackSize": 1, "name": "jungle_chest_boat", "displayName": "Jungle Boat with Chest"}, {"id": 811, "stackSize": 1, "name": "acacia_boat", "displayName": "Acacia Boat"}, {"id": 812, "stackSize": 1, "name": "acacia_chest_boat", "displayName": "Acacia Boat with Chest"}, {"id": 813, "stackSize": 1, "name": "cherry_boat", "displayName": "Cherry Boat"}, {"id": 814, "stackSize": 1, "name": "cherry_chest_boat", "displayName": "Cherry Boat with Chest"}, {"id": 815, "stackSize": 1, "name": "dark_oak_boat", "displayName": "Dark Oak Boat"}, {"id": 816, "stackSize": 1, "name": "dark_oak_chest_boat", "displayName": "Dark Oak Boat with Chest"}, {"id": 817, "stackSize": 1, "name": "pale_oak_boat", "displayName": "Pale Oak Boat"}, {"id": 818, "stackSize": 1, "name": "pale_oak_chest_boat", "displayName": "<PERSON>le Oak Boat with Chest"}, {"id": 819, "stackSize": 1, "name": "mangrove_boat", "displayName": "Mangrove Boat"}, {"id": 820, "stackSize": 1, "name": "mangrove_chest_boat", "displayName": "Mangrove Boat with Chest"}, {"id": 821, "stackSize": 1, "name": "bamboo_raft", "displayName": "Bamboo Raft"}, {"id": 822, "stackSize": 1, "name": "bamboo_chest_raft", "displayName": "Bamboo Raft with Chest"}, {"id": 823, "stackSize": 64, "name": "structure_block", "displayName": "Structure Block"}, {"id": 824, "stackSize": 64, "name": "jigsaw", "displayName": "Jigsaw Block"}, {"id": 825, "stackSize": 1, "name": "turtle_helmet", "displayName": "Turtle Shell", "enchantCategories": ["head_armor", "equippable", "armor", "durability", "vanishing"], "repairWith": ["turtle_scute"], "maxDurability": 275}, {"id": 826, "stackSize": 64, "name": "turtle_scute", "displayName": "<PERSON>"}, {"id": 827, "stackSize": 64, "name": "armadillo_scute", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 828, "stackSize": 1, "name": "wolf_armor", "displayName": "<PERSON>or", "repairWith": ["armadillo_scute"], "maxDurability": 64}, {"id": 829, "stackSize": 1, "name": "flint_and_steel", "displayName": "Flint and Steel", "enchantCategories": ["durability", "vanishing"], "maxDurability": 64}, {"id": 830, "stackSize": 64, "name": "bowl", "displayName": "Bowl"}, {"id": 831, "stackSize": 64, "name": "apple", "displayName": "Apple"}, {"id": 832, "stackSize": 1, "name": "bow", "displayName": "Bow", "enchantCategories": ["bow", "durability", "vanishing"], "maxDurability": 384}, {"id": 833, "displayName": "Arrow", "name": "arrow", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 0, "id": 1211, "name": "spectral_arrow", "displayName": "Spectral Arrow", "stackSize": 64}, {"metadata": 0, "id": 1212, "name": "tipped_arrow", "displayName": "Tipped Arrow", "stackSize": 64}]}, {"id": 834, "stackSize": 64, "name": "coal", "displayName": "Coal"}, {"id": 835, "stackSize": 64, "name": "charcoal", "displayName": "Charc<PERSON>l"}, {"id": 836, "stackSize": 64, "name": "diamond", "displayName": "Diamond"}, {"id": 837, "stackSize": 64, "name": "emerald", "displayName": "Emerald"}, {"id": 838, "stackSize": 64, "name": "lapis_lazuli", "displayName": "<PERSON><PERSON>"}, {"id": 839, "stackSize": 64, "name": "quartz", "displayName": "<PERSON><PERSON>"}, {"id": 840, "stackSize": 64, "name": "amethyst_shard", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 841, "stackSize": 64, "name": "raw_iron", "displayName": "Raw Iron"}, {"id": 842, "stackSize": 64, "name": "iron_ingot", "displayName": "Iron Ingot"}, {"id": 843, "stackSize": 64, "name": "raw_copper", "displayName": "Raw Copper"}, {"id": 844, "stackSize": 64, "name": "copper_ingot", "displayName": "Copper Ingot"}, {"id": 845, "stackSize": 64, "name": "raw_gold", "displayName": "Raw Gold"}, {"id": 846, "stackSize": 64, "name": "gold_ingot", "displayName": "Gold Ingot"}, {"id": 847, "stackSize": 64, "name": "netherite_ingot", "displayName": "Netherite Ingot"}, {"id": 848, "stackSize": 64, "name": "netherite_scrap", "displayName": "Netherite Scrap"}, {"id": 849, "stackSize": 1, "name": "wooden_sword", "displayName": "Wooden Sword", "enchantCategories": ["weapon", "fire_aspect", "sword", "durability", "sharp_weapon", "vanishing"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "cherry_planks", "dark_oak_planks", "pale_oak_planks", "mangrove_planks", "bamboo_planks", "crimson_planks", "warped_planks"], "maxDurability": 59}, {"id": 850, "stackSize": 1, "name": "wooden_shovel", "displayName": "<PERSON><PERSON>", "enchantCategories": ["mining", "mining_loot", "durability", "vanishing"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "cherry_planks", "dark_oak_planks", "pale_oak_planks", "mangrove_planks", "bamboo_planks", "crimson_planks", "warped_planks"], "maxDurability": 59}, {"id": 851, "stackSize": 1, "name": "wooden_pickaxe", "displayName": "<PERSON><PERSON> Pick<PERSON>e", "enchantCategories": ["mining", "mining_loot", "durability", "vanishing"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "cherry_planks", "dark_oak_planks", "pale_oak_planks", "mangrove_planks", "bamboo_planks", "crimson_planks", "warped_planks"], "maxDurability": 59}, {"id": 852, "stackSize": 1, "name": "wooden_axe", "displayName": "Wooden Axe", "enchantCategories": ["weapon", "mining", "mining_loot", "durability", "sharp_weapon", "vanishing"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "cherry_planks", "dark_oak_planks", "pale_oak_planks", "mangrove_planks", "bamboo_planks", "crimson_planks", "warped_planks"], "maxDurability": 59}, {"id": 853, "stackSize": 1, "name": "wooden_hoe", "displayName": "<PERSON><PERSON>e", "enchantCategories": ["mining", "mining_loot", "durability", "vanishing"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "cherry_planks", "dark_oak_planks", "pale_oak_planks", "mangrove_planks", "bamboo_planks", "crimson_planks", "warped_planks"], "maxDurability": 59}, {"id": 854, "stackSize": 1, "name": "stone_sword", "displayName": "Stone Sword", "enchantCategories": ["weapon", "fire_aspect", "sword", "durability", "sharp_weapon", "vanishing"], "repairWith": ["cobbled_deepslate", "cobblestone", "blackstone"], "maxDurability": 131}, {"id": 855, "stackSize": 1, "name": "stone_shovel", "displayName": "<PERSON>el", "enchantCategories": ["mining", "mining_loot", "durability", "vanishing"], "repairWith": ["cobbled_deepslate", "cobblestone", "blackstone"], "maxDurability": 131}, {"id": 856, "stackSize": 1, "name": "stone_pickaxe", "displayName": "<PERSON>", "enchantCategories": ["mining", "mining_loot", "durability", "vanishing"], "repairWith": ["cobbled_deepslate", "cobblestone", "blackstone"], "maxDurability": 131}, {"id": 857, "stackSize": 1, "name": "stone_axe", "displayName": "Stone Axe", "enchantCategories": ["weapon", "mining", "mining_loot", "durability", "sharp_weapon", "vanishing"], "repairWith": ["cobbled_deepslate", "cobblestone", "blackstone"], "maxDurability": 131}, {"id": 858, "stackSize": 1, "name": "stone_hoe", "displayName": "Stone Hoe", "enchantCategories": ["mining", "mining_loot", "durability", "vanishing"], "repairWith": ["cobbled_deepslate", "cobblestone", "blackstone"], "maxDurability": 131}, {"id": 859, "stackSize": 1, "name": "golden_sword", "displayName": "Golden Sword", "enchantCategories": ["weapon", "fire_aspect", "sword", "durability", "sharp_weapon", "vanishing"], "repairWith": ["gold_ingot"], "maxDurability": 32}, {"id": 860, "stackSize": 1, "name": "golden_shovel", "displayName": "Golden Shovel", "enchantCategories": ["mining", "mining_loot", "durability", "vanishing"], "repairWith": ["gold_ingot"], "maxDurability": 32}, {"id": 861, "stackSize": 1, "name": "golden_pickaxe", "displayName": "Golden Pickaxe", "enchantCategories": ["mining", "mining_loot", "durability", "vanishing"], "repairWith": ["gold_ingot"], "maxDurability": 32}, {"id": 862, "stackSize": 1, "name": "golden_axe", "displayName": "Golden Axe", "enchantCategories": ["weapon", "mining", "mining_loot", "durability", "sharp_weapon", "vanishing"], "repairWith": ["gold_ingot"], "maxDurability": 32}, {"id": 863, "stackSize": 1, "name": "golden_hoe", "displayName": "Golden Hoe", "enchantCategories": ["mining", "mining_loot", "durability", "vanishing"], "repairWith": ["gold_ingot"], "maxDurability": 32}, {"id": 864, "stackSize": 1, "name": "iron_sword", "displayName": "Iron Sword", "enchantCategories": ["weapon", "fire_aspect", "sword", "durability", "sharp_weapon", "vanishing"], "repairWith": ["iron_ingot"], "maxDurability": 250}, {"id": 865, "stackSize": 1, "name": "iron_shovel", "displayName": "Iron Shovel", "enchantCategories": ["mining", "mining_loot", "durability", "vanishing"], "repairWith": ["iron_ingot"], "maxDurability": 250}, {"id": 866, "stackSize": 1, "name": "iron_pickaxe", "displayName": "Iron Pickaxe", "enchantCategories": ["mining", "mining_loot", "durability", "vanishing"], "repairWith": ["iron_ingot"], "maxDurability": 250}, {"id": 867, "stackSize": 1, "name": "iron_axe", "displayName": "Iron Axe", "enchantCategories": ["weapon", "mining", "mining_loot", "durability", "sharp_weapon", "vanishing"], "repairWith": ["iron_ingot"], "maxDurability": 250}, {"id": 868, "stackSize": 1, "name": "iron_hoe", "displayName": "Iron Hoe", "enchantCategories": ["mining", "mining_loot", "durability", "vanishing"], "repairWith": ["iron_ingot"], "maxDurability": 250}, {"id": 869, "stackSize": 1, "name": "diamond_sword", "displayName": "Diamond Sword", "enchantCategories": ["weapon", "fire_aspect", "sword", "durability", "sharp_weapon", "vanishing"], "repairWith": ["diamond"], "maxDurability": 1561}, {"id": 870, "stackSize": 1, "name": "diamond_shovel", "displayName": "Diamond Shovel", "enchantCategories": ["mining", "mining_loot", "durability", "vanishing"], "repairWith": ["diamond"], "maxDurability": 1561}, {"id": 871, "stackSize": 1, "name": "diamond_pickaxe", "displayName": "Diamond Pickaxe", "enchantCategories": ["mining", "mining_loot", "durability", "vanishing"], "repairWith": ["diamond"], "maxDurability": 1561}, {"id": 872, "stackSize": 1, "name": "diamond_axe", "displayName": "Diamond Axe", "enchantCategories": ["weapon", "mining", "mining_loot", "durability", "sharp_weapon", "vanishing"], "repairWith": ["diamond"], "maxDurability": 1561}, {"id": 873, "stackSize": 1, "name": "diamond_hoe", "displayName": "Diamond Hoe", "enchantCategories": ["mining", "mining_loot", "durability", "vanishing"], "repairWith": ["diamond"], "maxDurability": 1561}, {"id": 874, "stackSize": 1, "name": "netherite_sword", "displayName": "Netherite Sword", "enchantCategories": ["weapon", "fire_aspect", "sword", "durability", "sharp_weapon", "vanishing"], "repairWith": ["netherite_ingot"], "maxDurability": 2031}, {"id": 875, "stackSize": 1, "name": "netherite_shovel", "displayName": "<PERSON><PERSON><PERSON>", "enchantCategories": ["mining", "mining_loot", "durability", "vanishing"], "repairWith": ["netherite_ingot"], "maxDurability": 2031}, {"id": 876, "stackSize": 1, "name": "netherite_pickaxe", "displayName": "Netherite Pickaxe", "enchantCategories": ["mining", "mining_loot", "durability", "vanishing"], "repairWith": ["netherite_ingot"], "maxDurability": 2031}, {"id": 877, "stackSize": 1, "name": "netherite_axe", "displayName": "Netherite Axe", "enchantCategories": ["weapon", "mining", "mining_loot", "durability", "sharp_weapon", "vanishing"], "repairWith": ["netherite_ingot"], "maxDurability": 2031}, {"id": 878, "stackSize": 1, "name": "netherite_hoe", "displayName": "Netherite Hoe", "enchantCategories": ["mining", "mining_loot", "durability", "vanishing"], "repairWith": ["netherite_ingot"], "maxDurability": 2031}, {"id": 879, "displayName": "Stick", "name": "stick", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 0, "id": 1219, "name": "debug_stick", "displayName": "Debug Stick", "stackSize": 1}]}, {"id": 880, "stackSize": 1, "name": "mushroom_stew", "displayName": "Mushroom Stew"}, {"id": 881, "stackSize": 64, "name": "string", "displayName": "String"}, {"id": 882, "stackSize": 64, "name": "feather", "displayName": "<PERSON><PERSON>"}, {"id": 883, "stackSize": 64, "name": "gunpowder", "displayName": "Gunpowder"}, {"id": 884, "stackSize": 64, "name": "wheat_seeds", "displayName": "Wheat Seeds"}, {"id": 885, "stackSize": 64, "name": "wheat", "displayName": "Wheat"}, {"id": 886, "stackSize": 64, "name": "bread", "displayName": "Bread"}, {"id": 887, "stackSize": 1, "name": "leather_helmet", "displayName": "Leather Cap", "enchantCategories": ["head_armor", "equippable", "armor", "durability", "vanishing"], "repairWith": ["leather"], "maxDurability": 55}, {"id": 888, "stackSize": 1, "name": "leather_chestplate", "displayName": "<PERSON><PERSON>", "enchantCategories": ["equippable", "armor", "durability", "vanishing"], "repairWith": ["leather"], "maxDurability": 80}, {"id": 889, "stackSize": 1, "name": "leather_leggings", "displayName": "<PERSON><PERSON>", "enchantCategories": ["equippable", "armor", "durability", "leg_armor", "vanishing"], "repairWith": ["leather"], "maxDurability": 75}, {"id": 890, "stackSize": 1, "name": "leather_boots", "displayName": "<PERSON><PERSON>", "enchantCategories": ["equippable", "armor", "foot_armor", "durability", "vanishing"], "repairWith": ["leather"], "maxDurability": 65}, {"id": 891, "stackSize": 1, "name": "chainmail_helmet", "displayName": "Chainmail Helmet", "enchantCategories": ["head_armor", "equippable", "armor", "durability", "vanishing"], "repairWith": ["iron_ingot"], "maxDurability": 165}, {"id": 892, "stackSize": 1, "name": "chainmail_chestplate", "displayName": "Chainmail Chestplate", "enchantCategories": ["equippable", "armor", "durability", "vanishing"], "repairWith": ["iron_ingot"], "maxDurability": 240}, {"id": 893, "stackSize": 1, "name": "chainmail_leggings", "displayName": "Chainmail Leggings", "enchantCategories": ["equippable", "armor", "durability", "leg_armor", "vanishing"], "repairWith": ["iron_ingot"], "maxDurability": 225}, {"id": 894, "stackSize": 1, "name": "chainmail_boots", "displayName": "Chainmail Boots", "enchantCategories": ["equippable", "armor", "foot_armor", "durability", "vanishing"], "repairWith": ["iron_ingot"], "maxDurability": 195}, {"id": 895, "stackSize": 1, "name": "iron_helmet", "displayName": "Iron Helmet", "enchantCategories": ["head_armor", "equippable", "armor", "durability", "vanishing"], "repairWith": ["iron_ingot"], "maxDurability": 165}, {"id": 896, "stackSize": 1, "name": "iron_chestplate", "displayName": "Iron Chestplate", "enchantCategories": ["equippable", "armor", "durability", "vanishing"], "repairWith": ["iron_ingot"], "maxDurability": 240}, {"id": 897, "stackSize": 1, "name": "iron_leggings", "displayName": "Iron Leggings", "enchantCategories": ["equippable", "armor", "durability", "leg_armor", "vanishing"], "repairWith": ["iron_ingot"], "maxDurability": 225}, {"id": 898, "stackSize": 1, "name": "iron_boots", "displayName": "Iron Boots", "enchantCategories": ["equippable", "armor", "foot_armor", "durability", "vanishing"], "repairWith": ["iron_ingot"], "maxDurability": 195}, {"id": 899, "stackSize": 1, "name": "diamond_helmet", "displayName": "Diamond Helmet", "enchantCategories": ["head_armor", "equippable", "armor", "durability", "vanishing"], "repairWith": ["diamond"], "maxDurability": 363}, {"id": 900, "stackSize": 1, "name": "diamond_chestplate", "displayName": "Diamond Chestplate", "enchantCategories": ["equippable", "armor", "durability", "vanishing"], "repairWith": ["diamond"], "maxDurability": 528}, {"id": 901, "stackSize": 1, "name": "diamond_leggings", "displayName": "Diamond Leggings", "enchantCategories": ["equippable", "armor", "durability", "leg_armor", "vanishing"], "repairWith": ["diamond"], "maxDurability": 495}, {"id": 902, "stackSize": 1, "name": "diamond_boots", "displayName": "Diamond Boots", "enchantCategories": ["equippable", "armor", "foot_armor", "durability", "vanishing"], "repairWith": ["diamond"], "maxDurability": 429}, {"id": 903, "stackSize": 1, "name": "golden_helmet", "displayName": "Golden Helmet", "enchantCategories": ["head_armor", "equippable", "armor", "durability", "vanishing"], "repairWith": ["gold_ingot"], "maxDurability": 77}, {"id": 904, "stackSize": 1, "name": "golden_chestplate", "displayName": "Golden Chestplate", "enchantCategories": ["equippable", "armor", "durability", "vanishing"], "repairWith": ["gold_ingot"], "maxDurability": 112}, {"id": 905, "stackSize": 1, "name": "golden_leggings", "displayName": "Golden Leggings", "enchantCategories": ["equippable", "armor", "durability", "leg_armor", "vanishing"], "repairWith": ["gold_ingot"], "maxDurability": 105}, {"id": 906, "stackSize": 1, "name": "golden_boots", "displayName": "Golden Boots", "enchantCategories": ["equippable", "armor", "foot_armor", "durability", "vanishing"], "repairWith": ["gold_ingot"], "maxDurability": 91}, {"id": 907, "stackSize": 1, "name": "netherite_helmet", "displayName": "Netherite Helmet", "enchantCategories": ["head_armor", "equippable", "armor", "durability", "vanishing"], "repairWith": ["netherite_ingot"], "maxDurability": 407}, {"id": 908, "stackSize": 1, "name": "netherite_chestplate", "displayName": "Netherite Chestplate", "enchantCategories": ["equippable", "armor", "durability", "vanishing"], "repairWith": ["netherite_ingot"], "maxDurability": 592}, {"id": 909, "stackSize": 1, "name": "netherite_leggings", "displayName": "Netherite Leggings", "enchantCategories": ["equippable", "armor", "durability", "leg_armor", "vanishing"], "repairWith": ["netherite_ingot"], "maxDurability": 555}, {"id": 910, "stackSize": 1, "name": "netherite_boots", "displayName": "Netherite Boots", "enchantCategories": ["equippable", "armor", "foot_armor", "durability", "vanishing"], "repairWith": ["netherite_ingot"], "maxDurability": 481}, {"id": 911, "stackSize": 64, "name": "flint", "displayName": "Flint"}, {"id": 912, "stackSize": 64, "name": "porkchop", "displayName": "Raw Porkchop"}, {"id": 913, "stackSize": 64, "name": "cooked_porkchop", "displayName": "Cooked Porkchop"}, {"id": 914, "stackSize": 64, "name": "painting", "displayName": "Painting"}, {"id": 915, "stackSize": 64, "name": "golden_apple", "displayName": "Golden Apple"}, {"id": 916, "stackSize": 64, "name": "enchanted_golden_apple", "displayName": "Enchanted Golden Apple"}, {"id": 917, "stackSize": 16, "name": "oak_sign", "displayName": "Oak Sign"}, {"id": 918, "stackSize": 16, "name": "spruce_sign", "displayName": "Spruce Sign"}, {"id": 919, "stackSize": 16, "name": "birch_sign", "displayName": "Birch Sign"}, {"id": 920, "stackSize": 16, "name": "jungle_sign", "displayName": "Jungle Sign"}, {"id": 921, "stackSize": 16, "name": "acacia_sign", "displayName": "Acacia Sign"}, {"id": 922, "stackSize": 16, "name": "cherry_sign", "displayName": "Cherry Sign"}, {"id": 923, "stackSize": 16, "name": "dark_oak_sign", "displayName": "Dark Oak Sign"}, {"id": 924, "stackSize": 16, "name": "pale_oak_sign", "displayName": "Pale Oak Sign"}, {"id": 925, "stackSize": 16, "name": "mangrove_sign", "displayName": "Mangrove Sign"}, {"id": 926, "stackSize": 16, "name": "bamboo_sign", "displayName": "Bamboo Sign"}, {"id": 927, "stackSize": 16, "name": "crimson_sign", "displayName": "Crimson Sign"}, {"id": 928, "stackSize": 16, "name": "warped_sign", "displayName": "Warped Sign"}, {"id": 929, "stackSize": 16, "name": "oak_hanging_sign", "displayName": "Oak Hanging Sign"}, {"id": 930, "stackSize": 16, "name": "spruce_hanging_sign", "displayName": "Spruce Hanging Sign"}, {"id": 931, "stackSize": 16, "name": "birch_hanging_sign", "displayName": "<PERSON> Hanging Sign"}, {"id": 932, "stackSize": 16, "name": "jungle_hanging_sign", "displayName": "Jungle Hanging Sign"}, {"id": 933, "stackSize": 16, "name": "acacia_hanging_sign", "displayName": "Acacia Hanging Sign"}, {"id": 934, "stackSize": 16, "name": "cherry_hanging_sign", "displayName": "Cherry Hanging Sign"}, {"id": 935, "stackSize": 16, "name": "dark_oak_hanging_sign", "displayName": "Dark Oak Hanging Sign"}, {"id": 936, "stackSize": 16, "name": "pale_oak_hanging_sign", "displayName": "Pale Oak Hanging Sign"}, {"id": 937, "stackSize": 16, "name": "mangrove_hanging_sign", "displayName": "Mangrove Hanging Sign"}, {"id": 938, "stackSize": 16, "name": "bamboo_hanging_sign", "displayName": "Bamboo Hanging Sign"}, {"id": 939, "stackSize": 16, "name": "crimson_hanging_sign", "displayName": "Crimson Hanging Sign"}, {"id": 940, "stackSize": 16, "name": "warped_hanging_sign", "displayName": "Warped Hanging Sign"}, {"id": 941, "stackSize": 16, "name": "bucket", "displayName": "Bucket"}, {"id": 942, "stackSize": 1, "name": "water_bucket", "displayName": "Water Bucket"}, {"id": 943, "stackSize": 1, "name": "lava_bucket", "displayName": "<PERSON><PERSON>et"}, {"id": 944, "stackSize": 1, "name": "powder_snow_bucket", "displayName": "Powder Snow Bucket"}, {"id": 945, "stackSize": 16, "name": "snowball", "displayName": "Snowball"}, {"id": 946, "stackSize": 64, "name": "leather", "displayName": "Leather"}, {"id": 947, "stackSize": 1, "name": "milk_bucket", "displayName": "Milk Bucket"}, {"id": 948, "stackSize": 1, "name": "pufferfish_bucket", "displayName": "Bucket of Pufferfish"}, {"id": 949, "stackSize": 1, "name": "salmon_bucket", "displayName": "Bucket of Salmon"}, {"id": 950, "stackSize": 1, "name": "cod_bucket", "displayName": "Bucket of Cod"}, {"id": 951, "stackSize": 1, "name": "tropical_fish_bucket", "displayName": "Bucket of Tropical Fish"}, {"id": 952, "stackSize": 1, "name": "axolotl_bucket", "displayName": "Bucket of Axolotl"}, {"id": 953, "stackSize": 1, "name": "tadpole_bucket", "displayName": "Bucket of Tadpole"}, {"id": 954, "stackSize": 64, "name": "brick", "displayName": "Brick"}, {"id": 955, "stackSize": 64, "name": "clay_ball", "displayName": "<PERSON>"}, {"id": 956, "stackSize": 64, "name": "dried_kelp_block", "displayName": "Dried Kelp Block"}, {"id": 957, "stackSize": 64, "name": "paper", "displayName": "Paper"}, {"id": 958, "displayName": "Book", "name": "book", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 0, "id": 1218, "name": "knowledge_book", "displayName": "Knowledge Book", "stackSize": 1}]}, {"id": 959, "stackSize": 64, "name": "slime_ball", "displayName": "Slimeball"}, {"id": 960, "stackSize": 16, "name": "egg", "displayName": "Egg"}, {"id": 961, "stackSize": 64, "name": "compass", "displayName": "<PERSON>mp<PERSON>", "enchantCategories": ["vanishing"]}, {"id": 962, "stackSize": 64, "name": "recovery_compass", "displayName": "Recovery Compass"}, {"id": 963, "stackSize": 1, "name": "bundle", "displayName": "Bundle"}, {"id": 964, "stackSize": 1, "name": "white_bundle", "displayName": "White Bundle"}, {"id": 965, "stackSize": 1, "name": "orange_bundle", "displayName": "Orange Bundle"}, {"id": 966, "stackSize": 1, "name": "magenta_bundle", "displayName": "Magenta Bundle"}, {"id": 967, "stackSize": 1, "name": "light_blue_bundle", "displayName": "Light Blue Bundle"}, {"id": 968, "stackSize": 1, "name": "yellow_bundle", "displayName": "Yellow Bundle"}, {"id": 969, "stackSize": 1, "name": "lime_bundle", "displayName": "Lime Bundle"}, {"id": 970, "stackSize": 1, "name": "pink_bundle", "displayName": "Pink Bundle"}, {"id": 971, "stackSize": 1, "name": "gray_bundle", "displayName": "<PERSON>"}, {"id": 972, "stackSize": 1, "name": "light_gray_bundle", "displayName": "Light Gray Bundle"}, {"id": 973, "stackSize": 1, "name": "cyan_bundle", "displayName": "<PERSON><PERSON>"}, {"id": 974, "stackSize": 1, "name": "purple_bundle", "displayName": "Purple Bundle"}, {"id": 975, "stackSize": 1, "name": "blue_bundle", "displayName": "Blue Bundle"}, {"id": 976, "stackSize": 1, "name": "brown_bundle", "displayName": "<PERSON> Bundle"}, {"id": 977, "stackSize": 1, "name": "green_bundle", "displayName": "Green Bundle"}, {"id": 978, "stackSize": 1, "name": "red_bundle", "displayName": "Red Bundle"}, {"id": 979, "stackSize": 1, "name": "black_bundle", "displayName": "Black Bundle"}, {"id": 980, "stackSize": 1, "name": "fishing_rod", "displayName": "Fishing Rod", "enchantCategories": ["fishing", "durability", "vanishing"], "maxDurability": 64}, {"id": 981, "stackSize": 64, "name": "clock", "displayName": "Clock"}, {"id": 982, "stackSize": 1, "name": "spyglass", "displayName": "Spyglass"}, {"id": 983, "stackSize": 64, "name": "glowstone_dust", "displayName": "Glowstone Dust"}, {"id": 984, "stackSize": 64, "name": "cod", "displayName": "Raw Cod"}, {"id": 985, "stackSize": 64, "name": "salmon", "displayName": "Raw Salmon"}, {"id": 986, "stackSize": 64, "name": "tropical_fish", "displayName": "Tropical Fish"}, {"id": 987, "stackSize": 64, "name": "pufferfish", "displayName": "Pufferfish"}, {"id": 988, "stackSize": 64, "name": "cooked_cod", "displayName": "Cooked Cod"}, {"id": 989, "stackSize": 64, "name": "cooked_salmon", "displayName": "Cooked Salmon"}, {"id": 990, "stackSize": 64, "name": "ink_sac", "displayName": "Ink Sac"}, {"id": 991, "stackSize": 64, "name": "glow_ink_sac", "displayName": "Glow Ink Sac"}, {"id": 992, "stackSize": 64, "name": "cocoa_beans", "displayName": "Cocoa Beans"}, {"id": 993, "stackSize": 64, "name": "white_dye", "displayName": "White Dye"}, {"id": 994, "stackSize": 64, "name": "orange_dye", "displayName": "Orange Dye"}, {"id": 995, "stackSize": 64, "name": "magenta_dye", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 996, "stackSize": 64, "name": "light_blue_dye", "displayName": "Light Blue Dye"}, {"id": 997, "stackSize": 64, "name": "yellow_dye", "displayName": "Yellow Dye"}, {"id": 998, "stackSize": 64, "name": "lime_dye", "displayName": "Lime Dye"}, {"id": 999, "stackSize": 64, "name": "pink_dye", "displayName": "Pink Dye"}, {"id": 1000, "stackSize": 64, "name": "gray_dye", "displayName": "<PERSON>"}, {"id": 1001, "stackSize": 64, "name": "light_gray_dye", "displayName": "Light Gray D<PERSON>"}, {"id": 1002, "stackSize": 64, "name": "cyan_dye", "displayName": "<PERSON><PERSON>"}, {"id": 1003, "stackSize": 64, "name": "purple_dye", "displayName": "Purple Dye"}, {"id": 1004, "stackSize": 64, "name": "blue_dye", "displayName": "Blue Dye"}, {"id": 1005, "stackSize": 64, "name": "brown_dye", "displayName": "<PERSON>"}, {"id": 1006, "stackSize": 64, "name": "green_dye", "displayName": "Green Dye"}, {"id": 1007, "stackSize": 64, "name": "red_dye", "displayName": "Red Dye"}, {"id": 1008, "stackSize": 64, "name": "black_dye", "displayName": "Black Dye"}, {"id": 1009, "stackSize": 64, "name": "bone_meal", "displayName": "<PERSON>"}, {"id": 1010, "stackSize": 64, "name": "bone", "displayName": "Bone"}, {"id": 1011, "stackSize": 64, "name": "sugar", "displayName": "Sugar"}, {"id": 1012, "stackSize": 1, "name": "cake", "displayName": "Cake"}, {"id": 1013, "displayName": "White Bed", "name": "bed", "stackSize": 1, "metadata": 0, "variations": [{"metadata": 1, "id": 1014, "name": "orange_bed", "displayName": "Orange Bed", "stackSize": 1}, {"metadata": 2, "id": 1015, "name": "magenta_bed", "displayName": "Magenta Bed", "stackSize": 1}, {"metadata": 3, "id": 1016, "name": "light_blue_bed", "displayName": "Light Blue Bed", "stackSize": 1}, {"metadata": 4, "id": 1017, "name": "yellow_bed", "displayName": "Yellow Bed", "stackSize": 1}, {"metadata": 5, "id": 1018, "name": "lime_bed", "displayName": "Lime Bed", "stackSize": 1}, {"metadata": 6, "id": 1019, "name": "pink_bed", "displayName": "Pink Bed", "stackSize": 1}, {"metadata": 7, "id": 1020, "name": "gray_bed", "displayName": "Gray Bed", "stackSize": 1}, {"metadata": 8, "id": 1021, "name": "light_gray_bed", "displayName": "Light Gray Bed", "stackSize": 1}, {"metadata": 9, "id": 1022, "name": "cyan_bed", "displayName": "<PERSON><PERSON>", "stackSize": 1}, {"metadata": 10, "id": 1023, "name": "purple_bed", "displayName": "Purple Bed", "stackSize": 1}, {"metadata": 11, "id": 1024, "name": "blue_bed", "displayName": "Blue Bed", "stackSize": 1}, {"metadata": 12, "id": 1025, "name": "brown_bed", "displayName": "Brown Bed", "stackSize": 1}, {"metadata": 13, "id": 1026, "name": "green_bed", "displayName": "Green Bed", "stackSize": 1}, {"metadata": 14, "id": 1027, "name": "red_bed", "displayName": "Red Bed", "stackSize": 1}, {"metadata": 15, "id": 1028, "name": "black_bed", "displayName": "Black Bed", "stackSize": 1}]}, {"id": 1029, "stackSize": 64, "name": "cookie", "displayName": "<PERSON><PERSON>"}, {"id": 1030, "stackSize": 64, "name": "crafter", "displayName": "Crafter"}, {"id": 1031, "stackSize": 64, "name": "filled_map", "displayName": "Map"}, {"id": 1032, "stackSize": 1, "name": "shears", "displayName": "Shears", "enchantCategories": ["mining", "durability", "vanishing"], "maxDurability": 238}, {"id": 1033, "stackSize": 64, "name": "melon_slice", "displayName": "<PERSON><PERSON>"}, {"id": 1034, "stackSize": 64, "name": "dried_kelp", "displayName": "<PERSON><PERSON>"}, {"id": 1035, "stackSize": 64, "name": "pumpkin_seeds", "displayName": "<PERSON><PERSON><PERSON> Seeds"}, {"id": 1036, "stackSize": 64, "name": "melon_seeds", "displayName": "<PERSON>on Seeds"}, {"id": 1037, "stackSize": 64, "name": "beef", "displayName": "Raw Beef"}, {"id": 1038, "stackSize": 64, "name": "cooked_beef", "displayName": "Steak"}, {"id": 1039, "stackSize": 64, "name": "chicken", "displayName": "Raw Chicken"}, {"id": 1040, "stackSize": 64, "name": "cooked_chicken", "displayName": "Cooked Chicken"}, {"id": 1041, "stackSize": 64, "name": "rotten_flesh", "displayName": "Rotten Flesh"}, {"id": 1042, "stackSize": 16, "name": "ender_pearl", "displayName": "<PERSON><PERSON>"}, {"id": 1043, "stackSize": 64, "name": "blaze_rod", "displayName": "<PERSON>"}, {"id": 1044, "stackSize": 64, "name": "ghast_tear", "displayName": "Ghast Tear"}, {"id": 1045, "stackSize": 64, "name": "gold_nugget", "displayName": "Gold Nugget"}, {"id": 1046, "stackSize": 64, "name": "nether_wart", "displayName": "Nether Wart"}, {"id": 1047, "stackSize": 64, "name": "glass_bottle", "displayName": "Glass Bottle"}, {"id": 1048, "stackSize": 1, "name": "potion", "displayName": "Potion"}, {"id": 1049, "stackSize": 64, "name": "spider_eye", "displayName": "Spider Eye"}, {"id": 1050, "stackSize": 64, "name": "fermented_spider_eye", "displayName": "Fermented Spider Eye"}, {"id": 1051, "stackSize": 64, "name": "blaze_powder", "displayName": "<PERSON>"}, {"id": 1052, "stackSize": 64, "name": "magma_cream", "displayName": "Magma Cream"}, {"id": 1053, "stackSize": 64, "name": "brewing_stand", "displayName": "Brewing Stand"}, {"id": 1054, "stackSize": 64, "name": "cauldron", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 1055, "stackSize": 64, "name": "ender_eye", "displayName": "Eye of <PERSON>er"}, {"id": 1056, "stackSize": 64, "name": "glistering_melon_slice", "displayName": "Glistering <PERSON><PERSON>"}, {"id": 1057, "stackSize": 64, "name": "armadillo_spawn_egg", "displayName": "Armadillo Spawn Egg"}, {"id": 1058, "stackSize": 64, "name": "allay_spawn_egg", "displayName": "Allay Spawn Egg"}, {"id": 1059, "stackSize": 64, "name": "axolotl_spawn_egg", "displayName": "Axolotl Spawn Egg"}, {"id": 1060, "stackSize": 64, "name": "bat_spawn_egg", "displayName": "Bat Spawn Egg"}, {"id": 1061, "stackSize": 64, "name": "bee_spawn_egg", "displayName": "Bee Spawn Egg"}, {"id": 1062, "stackSize": 64, "name": "blaze_spawn_egg", "displayName": "Blaze Spawn Egg"}, {"id": 1063, "stackSize": 64, "name": "bogged_spawn_egg", "displayName": "Bogged Spawn Egg"}, {"id": 1064, "stackSize": 64, "name": "breeze_spawn_egg", "displayName": "Breeze Spawn Egg"}, {"id": 1065, "stackSize": 64, "name": "cat_spawn_egg", "displayName": "Cat Spawn Egg"}, {"id": 1066, "stackSize": 64, "name": "camel_spawn_egg", "displayName": "Camel Spawn Egg"}, {"id": 1067, "stackSize": 64, "name": "cave_spider_spawn_egg", "displayName": "Cave Spider Spawn Egg"}, {"id": 1068, "stackSize": 64, "name": "chicken_spawn_egg", "displayName": "Chicken Spawn Egg"}, {"id": 1069, "stackSize": 64, "name": "cod_spawn_egg", "displayName": "Cod Spawn Egg"}, {"id": 1070, "stackSize": 64, "name": "cow_spawn_egg", "displayName": "Cow Spawn Egg"}, {"id": 1071, "stackSize": 64, "name": "creeper_spawn_egg", "displayName": "Creeper Spawn Egg"}, {"id": 1072, "stackSize": 64, "name": "dolphin_spawn_egg", "displayName": "Dolphin Spawn Egg"}, {"id": 1073, "stackSize": 64, "name": "donkey_spawn_egg", "displayName": "Donkey Spawn Egg"}, {"id": 1074, "stackSize": 64, "name": "drowned_spawn_egg", "displayName": "Drowned Spawn Egg"}, {"id": 1075, "stackSize": 64, "name": "elder_guardian_spawn_egg", "displayName": "Elder Guardian Spawn Egg"}, {"id": 1076, "stackSize": 64, "name": "ender_dragon_spawn_egg", "displayName": "Ender Dragon Spawn Egg"}, {"id": 1077, "stackSize": 64, "name": "enderman_spawn_egg", "displayName": "Enderman Spawn Egg"}, {"id": 1078, "stackSize": 64, "name": "endermite_spawn_egg", "displayName": "Endermite Spawn Egg"}, {"id": 1079, "stackSize": 64, "name": "evoker_spawn_egg", "displayName": "Evoker Spawn Egg"}, {"id": 1080, "stackSize": 64, "name": "fox_spawn_egg", "displayName": "Fox Spawn Egg"}, {"id": 1081, "stackSize": 64, "name": "frog_spawn_egg", "displayName": "Frog Spawn Egg"}, {"id": 1082, "stackSize": 64, "name": "ghast_spawn_egg", "displayName": "Ghast Spawn Egg"}, {"id": 1083, "stackSize": 64, "name": "glow_squid_spawn_egg", "displayName": "Glow Squid Spawn Egg"}, {"id": 1084, "stackSize": 64, "name": "goat_spawn_egg", "displayName": "Goat Spawn Egg"}, {"id": 1085, "stackSize": 64, "name": "guardian_spawn_egg", "displayName": "Guardian Spawn Egg"}, {"id": 1086, "stackSize": 64, "name": "hoglin_spawn_egg", "displayName": "Hoglin Spawn Egg"}, {"id": 1087, "stackSize": 64, "name": "horse_spawn_egg", "displayName": "Horse Spawn Egg"}, {"id": 1088, "stackSize": 64, "name": "husk_spawn_egg", "displayName": "Husk Spawn Egg"}, {"id": 1089, "stackSize": 64, "name": "iron_golem_spawn_egg", "displayName": "Iron Golem Spawn Egg"}, {"id": 1090, "stackSize": 64, "name": "llama_spawn_egg", "displayName": "Llama Spawn Egg"}, {"id": 1091, "stackSize": 64, "name": "magma_cube_spawn_egg", "displayName": "Magma Cube Spawn Egg"}, {"id": 1092, "stackSize": 64, "name": "mooshroom_spawn_egg", "displayName": "Mooshroom Spawn Egg"}, {"id": 1093, "stackSize": 64, "name": "mule_spawn_egg", "displayName": "Mule Spawn Egg"}, {"id": 1094, "stackSize": 64, "name": "ocelot_spawn_egg", "displayName": "Ocelot Spawn Egg"}, {"id": 1095, "stackSize": 64, "name": "panda_spawn_egg", "displayName": "Panda Spawn Egg"}, {"id": 1096, "stackSize": 64, "name": "parrot_spawn_egg", "displayName": "Parrot Spawn Egg"}, {"id": 1097, "stackSize": 64, "name": "phantom_spawn_egg", "displayName": "Phantom Spawn Egg"}, {"id": 1098, "stackSize": 64, "name": "pig_spawn_egg", "displayName": "Pig Spawn Egg"}, {"id": 1099, "stackSize": 64, "name": "piglin_spawn_egg", "displayName": "Piglin Spawn Egg"}, {"id": 1100, "stackSize": 64, "name": "piglin_brute_spawn_egg", "displayName": "Piglin Brute Spawn Egg"}, {"id": 1101, "stackSize": 64, "name": "pillager_spawn_egg", "displayName": "Pillager Spawn Egg"}, {"id": 1102, "stackSize": 64, "name": "polar_bear_spawn_egg", "displayName": "Polar Bear Spawn Egg"}, {"id": 1103, "stackSize": 64, "name": "pufferfish_spawn_egg", "displayName": "Pufferfish Spawn Egg"}, {"id": 1104, "stackSize": 64, "name": "rabbit_spawn_egg", "displayName": "Rabbit Spawn Egg"}, {"id": 1105, "stackSize": 64, "name": "ravager_spawn_egg", "displayName": "Ravager Spawn Egg"}, {"id": 1106, "stackSize": 64, "name": "salmon_spawn_egg", "displayName": "Salmon Spawn Egg"}, {"id": 1107, "stackSize": 64, "name": "sheep_spawn_egg", "displayName": "Sheep Spawn Egg"}, {"id": 1108, "stackSize": 64, "name": "shulker_spawn_egg", "displayName": "Shulker Spawn Egg"}, {"id": 1109, "stackSize": 64, "name": "silverfish_spawn_egg", "displayName": "Silverfish Spawn Egg"}, {"id": 1110, "stackSize": 64, "name": "skeleton_spawn_egg", "displayName": "Skeleton Spawn Egg"}, {"id": 1111, "stackSize": 64, "name": "skeleton_horse_spawn_egg", "displayName": "Skeleton Horse Spawn Egg"}, {"id": 1112, "stackSize": 64, "name": "slime_spawn_egg", "displayName": "Slime Spawn Egg"}, {"id": 1113, "stackSize": 64, "name": "sniffer_spawn_egg", "displayName": "Sniffer Spawn Egg"}, {"id": 1114, "stackSize": 64, "name": "snow_golem_spawn_egg", "displayName": "Snow Golem Spawn Egg"}, {"id": 1115, "stackSize": 64, "name": "spider_spawn_egg", "displayName": "Spider Spawn Egg"}, {"id": 1116, "stackSize": 64, "name": "squid_spawn_egg", "displayName": "Squid Spawn Egg"}, {"id": 1117, "stackSize": 64, "name": "stray_spawn_egg", "displayName": "Stray Spawn Egg"}, {"id": 1118, "stackSize": 64, "name": "strider_spawn_egg", "displayName": "Strider Spawn Egg"}, {"id": 1119, "stackSize": 64, "name": "tadpole_spawn_egg", "displayName": "Tadpole Spawn Egg"}, {"id": 1120, "stackSize": 64, "name": "trader_llama_spawn_egg", "displayName": "Trader <PERSON>lama Spawn Egg"}, {"id": 1121, "stackSize": 64, "name": "tropical_fish_spawn_egg", "displayName": "Tropical Fish Spawn Egg"}, {"id": 1122, "stackSize": 64, "name": "turtle_spawn_egg", "displayName": "Turtle Spawn Egg"}, {"id": 1123, "stackSize": 64, "name": "vex_spawn_egg", "displayName": "Vex Spawn Egg"}, {"id": 1124, "stackSize": 64, "name": "villager_spawn_egg", "displayName": "Villager Spawn Egg"}, {"id": 1125, "stackSize": 64, "name": "vindicator_spawn_egg", "displayName": "Vindicator Spawn Egg"}, {"id": 1126, "stackSize": 64, "name": "wandering_trader_spawn_egg", "displayName": "Wandering Trader Spawn Egg"}, {"id": 1127, "stackSize": 64, "name": "warden_spawn_egg", "displayName": "Warden Spawn Egg"}, {"id": 1128, "stackSize": 64, "name": "witch_spawn_egg", "displayName": "Witch Spawn Egg"}, {"id": 1129, "stackSize": 64, "name": "wither_spawn_egg", "displayName": "Wither Spawn Egg"}, {"id": 1130, "stackSize": 64, "name": "wither_skeleton_spawn_egg", "displayName": "Wither Skeleton Spawn Egg"}, {"id": 1131, "stackSize": 64, "name": "wolf_spawn_egg", "displayName": "Wolf Spawn Egg"}, {"id": 1132, "stackSize": 64, "name": "zoglin_spawn_egg", "displayName": "Zoglin Spawn Egg"}, {"id": 1133, "stackSize": 64, "name": "creaking_spawn_egg", "displayName": "Creaking Spawn Egg"}, {"id": 1134, "stackSize": 64, "name": "zombie_spawn_egg", "displayName": "Zombie Spawn Egg"}, {"id": 1135, "stackSize": 64, "name": "zombie_horse_spawn_egg", "displayName": "Zombie Horse Spawn Egg"}, {"id": 1136, "stackSize": 64, "name": "zombie_villager_spawn_egg", "displayName": "Zombie Villager Spawn Egg"}, {"id": 1137, "stackSize": 64, "name": "zombie_pigman_spawn_egg", "displayName": "Zombified Piglin Spawn Egg"}, {"id": 1138, "stackSize": 64, "name": "experience_bottle", "displayName": "Bottle o' Enchanting"}, {"id": 1139, "stackSize": 64, "name": "fire_charge", "displayName": "Fire Charge"}, {"id": 1140, "stackSize": 64, "name": "wind_charge", "displayName": "Wind Charge"}, {"id": 1141, "stackSize": 1, "name": "writable_book", "displayName": "Book and Quill"}, {"id": 1142, "stackSize": 16, "name": "written_book", "displayName": "Written Book"}, {"id": 1143, "stackSize": 64, "name": "breeze_rod", "displayName": "<PERSON><PERSON>"}, {"id": 1144, "stackSize": 1, "name": "mace", "displayName": "Mace", "enchantCategories": ["weapon", "mace", "fire_aspect", "durability", "vanishing"], "repairWith": ["breeze_rod"], "maxDurability": 500}, {"id": 1145, "stackSize": 64, "name": "frame", "displayName": "<PERSON><PERSON>"}, {"id": 1146, "stackSize": 64, "name": "glow_frame", "displayName": "G<PERSON> Item <PERSON>"}, {"id": 1147, "stackSize": 64, "name": "flower_pot", "displayName": "Flower Pot"}, {"id": 1148, "stackSize": 64, "name": "carrot", "displayName": "Carrot"}, {"id": 1149, "stackSize": 64, "name": "potato", "displayName": "Potato"}, {"id": 1150, "stackSize": 64, "name": "baked_potato", "displayName": "Baked Potato"}, {"id": 1151, "stackSize": 64, "name": "poisonous_potato", "displayName": "Poisonous Potato"}, {"id": 1152, "stackSize": 64, "name": "empty_map", "displayName": "Empty Map"}, {"id": 1153, "stackSize": 64, "name": "golden_carrot", "displayName": "Golden Carrot"}, {"id": 1154, "stackSize": 64, "name": "skeleton_skull", "displayName": "Skeleton Skull", "enchantCategories": ["equippable", "vanishing"]}, {"id": 1155, "stackSize": 64, "name": "wither_skeleton_skull", "displayName": "Wither Skeleton Skull", "enchantCategories": ["equippable", "vanishing"]}, {"id": 1156, "stackSize": 64, "name": "player_head", "displayName": "Player Head", "enchantCategories": ["equippable", "vanishing"]}, {"id": 1157, "stackSize": 64, "name": "zombie_head", "displayName": "Zombie Head", "enchantCategories": ["equippable", "vanishing"]}, {"id": 1158, "stackSize": 64, "name": "creeper_head", "displayName": "Creeper Head", "enchantCategories": ["equippable", "vanishing"]}, {"id": 1159, "stackSize": 64, "name": "dragon_head", "displayName": "Dragon Head", "enchantCategories": ["equippable", "vanishing"]}, {"id": 1160, "stackSize": 64, "name": "piglin_head", "displayName": "<PERSON><PERSON>", "enchantCategories": ["equippable", "vanishing"]}, {"id": 1161, "stackSize": 64, "name": "nether_star", "displayName": "Nether Star"}, {"id": 1162, "stackSize": 64, "name": "pumpkin_pie", "displayName": "Pumpkin Pie"}, {"id": 1163, "stackSize": 64, "name": "firework_rocket", "displayName": "Firework Rocket"}, {"id": 1164, "stackSize": 64, "name": "firework_star", "displayName": "Firework Star"}, {"id": 1165, "stackSize": 1, "name": "enchanted_book", "displayName": "Enchanted Book"}, {"id": 1166, "stackSize": 64, "name": "netherbrick", "displayName": "Nether Brick"}, {"id": 1167, "stackSize": 64, "name": "resin_brick", "displayName": "Resin Brick"}, {"id": 1168, "stackSize": 64, "name": "prismarine_shard", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 1169, "stackSize": 64, "name": "prismarine_crystals", "displayName": "Prismarine Crystals"}, {"id": 1170, "stackSize": 64, "name": "rabbit", "displayName": "Raw Rabbit"}, {"id": 1171, "stackSize": 64, "name": "cooked_rabbit", "displayName": "Cooked Rabbit"}, {"id": 1172, "stackSize": 1, "name": "rabbit_stew", "displayName": "Rabbit Stew"}, {"id": 1173, "stackSize": 64, "name": "rabbit_foot", "displayName": "<PERSON>'s Foot"}, {"id": 1174, "stackSize": 64, "name": "rabbit_hide", "displayName": "<PERSON>"}, {"id": 1175, "stackSize": 16, "name": "armor_stand", "displayName": "Armor Stand"}, {"id": 1176, "stackSize": 1, "name": "iron_horse_armor", "displayName": "Iron Horse Armor"}, {"id": 1177, "stackSize": 1, "name": "golden_horse_armor", "displayName": "Golden Horse Armor"}, {"id": 1178, "stackSize": 1, "name": "diamond_horse_armor", "displayName": "Diamond Horse Armor"}, {"id": 1179, "stackSize": 1, "name": "leather_horse_armor", "displayName": "Leather Horse Armor"}, {"id": 1180, "stackSize": 64, "name": "lead", "displayName": "Lead"}, {"id": 1181, "stackSize": 64, "name": "name_tag", "displayName": "Name Tag"}, {"id": 1182, "stackSize": 1, "name": "command_block_minecart", "displayName": "Minecart with Command Block"}, {"id": 1183, "stackSize": 64, "name": "mutton", "displayName": "<PERSON>"}, {"id": 1184, "stackSize": 64, "name": "cooked_mutton", "displayName": "Cooked <PERSON>tton"}, {"id": 1200, "displayName": "Black Banner", "name": "banner", "stackSize": 16, "metadata": 0, "variations": [{"metadata": 1, "id": 1199, "name": "red_banner", "displayName": "Red Banner", "stackSize": 16}, {"metadata": 2, "id": 1198, "name": "green_banner", "displayName": "<PERSON> Banner", "stackSize": 16}, {"metadata": 3, "id": 1197, "name": "brown_banner", "displayName": "<PERSON>", "stackSize": 16}, {"metadata": 4, "id": 1196, "name": "blue_banner", "displayName": "Blue Banner", "stackSize": 16}, {"metadata": 5, "id": 1195, "name": "purple_banner", "displayName": "<PERSON> Banner", "stackSize": 16}, {"metadata": 6, "id": 1194, "name": "cyan_banner", "displayName": "<PERSON><PERSON>", "stackSize": 16}, {"metadata": 7, "id": 1193, "name": "light_gray_banner", "displayName": "<PERSON> Gray Banner", "stackSize": 16}, {"metadata": 8, "id": 1192, "name": "gray_banner", "displayName": "<PERSON>", "stackSize": 16}, {"metadata": 9, "id": 1191, "name": "pink_banner", "displayName": "Pink Banner", "stackSize": 16}, {"metadata": 10, "id": 1190, "name": "lime_banner", "displayName": "Lime Banner", "stackSize": 16}, {"metadata": 11, "id": 1189, "name": "yellow_banner", "displayName": "Yellow Banner", "stackSize": 16}, {"metadata": 12, "id": 1188, "name": "light_blue_banner", "displayName": "Light Blue Banner", "stackSize": 16}, {"metadata": 13, "id": 1187, "name": "magenta_banner", "displayName": "Magenta Banner", "stackSize": 16}, {"metadata": 14, "id": 1186, "name": "orange_banner", "displayName": "Orange Banner", "stackSize": 16}, {"metadata": 15, "id": 1185, "name": "white_banner", "displayName": "White Banner", "stackSize": 16}]}, {"id": 1201, "stackSize": 64, "name": "end_crystal", "displayName": "End Crystal"}, {"id": 1202, "stackSize": 64, "name": "chorus_fruit", "displayName": "Chorus Fruit"}, {"id": 1203, "stackSize": 64, "name": "popped_chorus_fruit", "displayName": "Popped Chorus Fruit"}, {"id": 1204, "stackSize": 64, "name": "torchflower_seeds", "displayName": "Torchflower Seeds"}, {"id": 1205, "stackSize": 64, "name": "pitcher_pod", "displayName": "Pitcher Pod"}, {"id": 1206, "stackSize": 64, "name": "beetroot", "displayName": "Beetroot"}, {"id": 1207, "stackSize": 64, "name": "beetroot_seeds", "displayName": "Beetroot Seeds"}, {"id": 1208, "stackSize": 1, "name": "beetroot_soup", "displayName": "Beetroot Soup"}, {"id": 1209, "stackSize": 64, "name": "dragon_breath", "displayName": "Dragon's Breath"}, {"id": 1210, "stackSize": 1, "name": "splash_potion", "displayName": "Splash Potion"}, {"id": 1213, "stackSize": 1, "name": "lingering_potion", "displayName": "Lingering Potion"}, {"id": 1214, "stackSize": 1, "name": "shield", "displayName": "Shield", "enchantCategories": ["durability", "vanishing"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "cherry_planks", "dark_oak_planks", "pale_oak_planks", "mangrove_planks", "bamboo_planks", "crimson_planks", "warped_planks"], "maxDurability": 336}, {"id": 1215, "stackSize": 1, "name": "totem_of_undying", "displayName": "Totem of Undying"}, {"id": 1216, "stackSize": 64, "name": "shulker_shell", "displayName": "Shulker Shell"}, {"id": 1217, "stackSize": 64, "name": "iron_nugget", "displayName": "Iron Nugget"}, {"id": 1220, "stackSize": 1, "name": "music_disc_13", "displayName": "Music Disc"}, {"id": 1221, "stackSize": 1, "name": "music_disc_cat", "displayName": "Music Disc"}, {"id": 1222, "stackSize": 1, "name": "music_disc_blocks", "displayName": "Music Disc"}, {"id": 1223, "stackSize": 1, "name": "music_disc_chirp", "displayName": "Music Disc"}, {"id": 1224, "stackSize": 1, "name": "music_disc_creator", "displayName": "Music Disc"}, {"id": 1225, "stackSize": 1, "name": "music_disc_creator_music_box", "displayName": "Music Disc"}, {"id": 1226, "stackSize": 1, "name": "music_disc_far", "displayName": "Music Disc"}, {"id": 1227, "stackSize": 1, "name": "music_disc_mall", "displayName": "Music Disc"}, {"id": 1228, "stackSize": 1, "name": "music_disc_mellohi", "displayName": "Music Disc"}, {"id": 1229, "stackSize": 1, "name": "music_disc_stal", "displayName": "Music Disc"}, {"id": 1230, "stackSize": 1, "name": "music_disc_strad", "displayName": "Music Disc"}, {"id": 1231, "stackSize": 1, "name": "music_disc_ward", "displayName": "Music Disc"}, {"id": 1232, "stackSize": 1, "name": "music_disc_11", "displayName": "Music Disc"}, {"id": 1233, "stackSize": 1, "name": "music_disc_wait", "displayName": "Music Disc"}, {"id": 1234, "stackSize": 1, "name": "music_disc_otherside", "displayName": "Music Disc"}, {"id": 1235, "stackSize": 1, "name": "music_disc_relic", "displayName": "Music Disc"}, {"id": 1236, "stackSize": 1, "name": "music_disc_5", "displayName": "Music Disc"}, {"id": 1237, "stackSize": 1, "name": "music_disc_pigstep", "displayName": "Music Disc"}, {"id": 1238, "stackSize": 1, "name": "music_disc_precipice", "displayName": "Music Disc"}, {"id": 1239, "stackSize": 64, "name": "disc_fragment_5", "displayName": "Disc Fragment"}, {"id": 1240, "stackSize": 1, "name": "trident", "displayName": "Trident", "enchantCategories": ["trident", "durability", "vanishing"], "maxDurability": 250}, {"id": 1241, "stackSize": 64, "name": "nautilus_shell", "displayName": "Nautilus Shell"}, {"id": 1242, "stackSize": 64, "name": "heart_of_the_sea", "displayName": "Heart of the Sea"}, {"id": 1243, "stackSize": 1, "name": "crossbow", "displayName": "Crossbow", "enchantCategories": ["durability", "crossbow", "vanishing"], "maxDurability": 465}, {"id": 1244, "stackSize": 1, "name": "suspicious_stew", "displayName": "Suspicious Stew"}, {"id": 1245, "stackSize": 64, "name": "loom", "displayName": "Loom"}, {"id": 1246, "stackSize": 1, "name": "flower_banner_pattern", "displayName": "Flower Charge Banner Pattern"}, {"id": 1247, "stackSize": 1, "name": "creeper_banner_pattern", "displayName": "Creeper Charge Banner Pattern"}, {"id": 1248, "stackSize": 1, "name": "skull_banner_pattern", "displayName": "Skull Charge Banner Pattern"}, {"id": 1249, "stackSize": 1, "name": "mojang_banner_pattern", "displayName": "<PERSON> Banner <PERSON>"}, {"id": 1250, "stackSize": 1, "name": "globe_banner_pattern", "displayName": "Globe Banner Pattern"}, {"id": 1251, "stackSize": 1, "name": "piglin_banner_pattern", "displayName": "Snout Banner Pattern"}, {"id": 1252, "stackSize": 1, "name": "flow_banner_pattern", "displayName": "<PERSON> <PERSON> Pattern"}, {"id": 1253, "stackSize": 1, "name": "guster_banner_pattern", "displayName": "<PERSON><PERSON>"}, {"id": 1254, "stackSize": 1, "name": "field_masoned_banner_pattern", "displayName": "Field Masoned Banner Pattern"}, {"id": 1255, "stackSize": 1, "name": "bordure_indented_banner_pattern", "displayName": "Bordure Indented Banner Pattern"}, {"id": 1256, "stackSize": 1, "name": "goat_horn", "displayName": "<PERSON><PERSON>"}, {"id": 1257, "stackSize": 64, "name": "composter", "displayName": "Composter"}, {"id": 1258, "stackSize": 64, "name": "barrel", "displayName": "Barrel"}, {"id": 1259, "stackSize": 64, "name": "smoker", "displayName": "Smoker"}, {"id": 1260, "stackSize": 64, "name": "blast_furnace", "displayName": "Blast Furnace"}, {"id": 1261, "stackSize": 64, "name": "cartography_table", "displayName": "Cartography Table"}, {"id": 1262, "stackSize": 64, "name": "fletching_table", "displayName": "Fletching Table"}, {"id": 1263, "stackSize": 64, "name": "grindstone", "displayName": "Grindstone"}, {"id": 1264, "stackSize": 64, "name": "smithing_table", "displayName": "Smithing Table"}, {"id": 1265, "stackSize": 64, "name": "stonecutter_block", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 1266, "stackSize": 64, "name": "bell", "displayName": "Bell"}, {"id": 1267, "stackSize": 64, "name": "lantern", "displayName": "Lantern"}, {"id": 1268, "stackSize": 64, "name": "soul_lantern", "displayName": "Soul Lantern"}, {"id": 1269, "stackSize": 64, "name": "sweet_berries", "displayName": "Sweet Berries"}, {"id": 1270, "stackSize": 64, "name": "glow_berries", "displayName": "Glow Berries"}, {"id": 1271, "stackSize": 64, "name": "campfire", "displayName": "Campfire"}, {"id": 1272, "stackSize": 64, "name": "soul_campfire", "displayName": "Soul Campfire"}, {"id": 1273, "stackSize": 64, "name": "shroomlight", "displayName": "Shroomlight"}, {"id": 1274, "stackSize": 64, "name": "honeycomb", "displayName": "Honeycomb"}, {"id": 1275, "stackSize": 64, "name": "bee_nest", "displayName": "Bee Nest"}, {"id": 1276, "stackSize": 64, "name": "beehive", "displayName": "Beehive"}, {"id": 1277, "stackSize": 16, "name": "honey_bottle", "displayName": "<PERSON>"}, {"id": 1278, "stackSize": 64, "name": "honeycomb_block", "displayName": "Honeycomb Block"}, {"id": 1279, "stackSize": 64, "name": "lodestone", "displayName": "Lodestone"}, {"id": 1280, "stackSize": 64, "name": "crying_obsidian", "displayName": "Crying Obsidian"}, {"id": 1281, "stackSize": 64, "name": "blackstone", "displayName": "Blackstone"}, {"id": 1282, "stackSize": 64, "name": "blackstone_slab", "displayName": "Blackstone Slab"}, {"id": 1283, "stackSize": 64, "name": "blackstone_stairs", "displayName": "Blackstone Stairs"}, {"id": 1284, "stackSize": 64, "name": "gilded_blackstone", "displayName": "Gilded Blackstone"}, {"id": 1285, "stackSize": 64, "name": "polished_blackstone", "displayName": "Polished Blackstone"}, {"id": 1286, "stackSize": 64, "name": "polished_blackstone_slab", "displayName": "Polished Blackstone Slab"}, {"id": 1287, "stackSize": 64, "name": "polished_blackstone_stairs", "displayName": "Polished Blackstone Stairs"}, {"id": 1288, "stackSize": 64, "name": "chiseled_polished_blackstone", "displayName": "Chiseled Polished Blackstone"}, {"id": 1289, "stackSize": 64, "name": "polished_blackstone_bricks", "displayName": "Polished Blackstone Bricks"}, {"id": 1290, "stackSize": 64, "name": "polished_blackstone_brick_slab", "displayName": "Polished Blackstone Brick Slab"}, {"id": 1291, "stackSize": 64, "name": "polished_blackstone_brick_stairs", "displayName": "Polished Blackstone Brick Stairs"}, {"id": 1292, "stackSize": 64, "name": "cracked_polished_blackstone_bricks", "displayName": "Cracked Polished Blackstone Bricks"}, {"id": 1293, "stackSize": 64, "name": "respawn_anchor", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 1294, "stackSize": 64, "name": "candle", "displayName": "Candle"}, {"id": 1295, "stackSize": 64, "name": "white_candle", "displayName": "White Candle"}, {"id": 1296, "stackSize": 64, "name": "orange_candle", "displayName": "Orange Candle"}, {"id": 1297, "stackSize": 64, "name": "magenta_candle", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 1298, "stackSize": 64, "name": "light_blue_candle", "displayName": "Light Blue Candle"}, {"id": 1299, "stackSize": 64, "name": "yellow_candle", "displayName": "Yellow Candle"}, {"id": 1300, "stackSize": 64, "name": "lime_candle", "displayName": "<PERSON><PERSON>"}, {"id": 1301, "stackSize": 64, "name": "pink_candle", "displayName": "Pink Candle"}, {"id": 1302, "stackSize": 64, "name": "gray_candle", "displayName": "<PERSON>"}, {"id": 1303, "stackSize": 64, "name": "light_gray_candle", "displayName": "Light Gray Candle"}, {"id": 1304, "stackSize": 64, "name": "cyan_candle", "displayName": "<PERSON><PERSON>"}, {"id": 1305, "stackSize": 64, "name": "purple_candle", "displayName": "Purple Candle"}, {"id": 1306, "stackSize": 64, "name": "blue_candle", "displayName": "Blue Candle"}, {"id": 1307, "stackSize": 64, "name": "brown_candle", "displayName": "<PERSON> Candle"}, {"id": 1308, "stackSize": 64, "name": "green_candle", "displayName": "Green Candle"}, {"id": 1309, "stackSize": 64, "name": "red_candle", "displayName": "<PERSON> Candle"}, {"id": 1310, "stackSize": 64, "name": "black_candle", "displayName": "Black Candle"}, {"id": 1311, "stackSize": 64, "name": "small_amethyst_bud", "displayName": "Small Amethyst Bud"}, {"id": 1312, "stackSize": 64, "name": "medium_amethyst_bud", "displayName": "Medium Amethyst Bud"}, {"id": 1313, "stackSize": 64, "name": "large_amethyst_bud", "displayName": "Large Amethyst Bud"}, {"id": 1314, "stackSize": 64, "name": "amethyst_cluster", "displayName": "Amethyst Cluster"}, {"id": 1315, "stackSize": 64, "name": "pointed_dripstone", "displayName": "Pointed Dripstone"}, {"id": 1316, "stackSize": 64, "name": "ochre_froglight", "displayName": "Ochre Froglight"}, {"id": 1317, "stackSize": 64, "name": "verdant_froglight", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 1318, "stackSize": 64, "name": "pearlescent_froglight", "displayName": "Pearlescent Froglight"}, {"id": 1319, "stackSize": 64, "name": "frog_spawn", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 1320, "stackSize": 64, "name": "echo_shard", "displayName": "Echo Shard"}, {"id": 1321, "stackSize": 1, "name": "brush", "displayName": "Brush", "enchantCategories": ["durability", "vanishing"], "maxDurability": 64}, {"id": 1322, "stackSize": 64, "name": "netherite_upgrade_smithing_template", "displayName": "Netherite Upgrade"}, {"id": 1323, "stackSize": 64, "name": "sentry_armor_trim_smithing_template", "displayName": "Sentry Armor Trim"}, {"id": 1324, "stackSize": 64, "name": "dune_armor_trim_smithing_template", "displayName": "<PERSON>ne Armor <PERSON>"}, {"id": 1325, "stackSize": 64, "name": "coast_armor_trim_smithing_template", "displayName": "Coast Armor Trim"}, {"id": 1326, "stackSize": 64, "name": "wild_armor_trim_smithing_template", "displayName": "Wild Armor Trim"}, {"id": 1327, "stackSize": 64, "name": "ward_armor_trim_smithing_template", "displayName": "<PERSON>"}, {"id": 1328, "stackSize": 64, "name": "eye_armor_trim_smithing_template", "displayName": "Eye Armor Trim"}, {"id": 1329, "stackSize": 64, "name": "vex_armor_trim_smithing_template", "displayName": "Vex Armor Trim"}, {"id": 1330, "stackSize": 64, "name": "tide_armor_trim_smithing_template", "displayName": "Tide Armor Trim"}, {"id": 1331, "stackSize": 64, "name": "snout_armor_trim_smithing_template", "displayName": "Snout Armor Trim"}, {"id": 1332, "stackSize": 64, "name": "rib_armor_trim_smithing_template", "displayName": "<PERSON><PERSON>"}, {"id": 1333, "stackSize": 64, "name": "spire_armor_trim_smithing_template", "displayName": "Spire Arm<PERSON>"}, {"id": 1334, "stackSize": 64, "name": "wayfinder_armor_trim_smithing_template", "displayName": "Wayfinder Armor Trim"}, {"id": 1335, "stackSize": 64, "name": "shaper_armor_trim_smithing_template", "displayName": "<PERSON><PERSON><PERSON> Armor <PERSON>"}, {"id": 1336, "stackSize": 64, "name": "silence_armor_trim_smithing_template", "displayName": "Silence Armor Trim"}, {"id": 1337, "stackSize": 64, "name": "raiser_armor_trim_smithing_template", "displayName": "Raiser Armor Trim"}, {"id": 1338, "stackSize": 64, "name": "host_armor_trim_smithing_template", "displayName": "Host <PERSON><PERSON>"}, {"id": 1339, "stackSize": 64, "name": "flow_armor_trim_smithing_template", "displayName": "Flow Armor Trim"}, {"id": 1340, "stackSize": 64, "name": "bolt_armor_trim_smithing_template", "displayName": "Bolt Armor Trim"}, {"id": 1341, "stackSize": 64, "name": "angler_pottery_sherd", "displayName": "Angler Pottery Sherd"}, {"id": 1342, "stackSize": 64, "name": "archer_pottery_sherd", "displayName": "Archer <PERSON>y Sherd"}, {"id": 1343, "stackSize": 64, "name": "arms_up_pottery_sherd", "displayName": "Arms Up Pottery Sherd"}, {"id": 1344, "stackSize": 64, "name": "blade_pottery_sherd", "displayName": "Blade Pottery Sherd"}, {"id": 1345, "stackSize": 64, "name": "brewer_pottery_sherd", "displayName": "Brewer Pottery Sherd"}, {"id": 1346, "stackSize": 64, "name": "burn_pottery_sherd", "displayName": "Burn Pottery Sherd"}, {"id": 1347, "stackSize": 64, "name": "danger_pottery_sherd", "displayName": "Danger Pottery Sherd"}, {"id": 1348, "stackSize": 64, "name": "explorer_pottery_sherd", "displayName": "Explorer <PERSON><PERSON> She<PERSON>"}, {"id": 1349, "stackSize": 64, "name": "flow_pottery_sherd", "displayName": "Flow Pottery Sherd"}, {"id": 1350, "stackSize": 64, "name": "friend_pottery_sherd", "displayName": "<PERSON> <PERSON>"}, {"id": 1351, "stackSize": 64, "name": "guster_pottery_sherd", "displayName": "<PERSON><PERSON>y <PERSON>"}, {"id": 1352, "stackSize": 64, "name": "heart_pottery_sherd", "displayName": "Heart Pottery Sherd"}, {"id": 1353, "stackSize": 64, "name": "heartbreak_pottery_sherd", "displayName": "Heartbreak Pottery Sherd"}, {"id": 1354, "stackSize": 64, "name": "howl_pottery_sherd", "displayName": "Howl Pottery Sherd"}, {"id": 1355, "stackSize": 64, "name": "miner_pottery_sherd", "displayName": "Miner <PERSON>"}, {"id": 1356, "stackSize": 64, "name": "mourner_pottery_sherd", "displayName": "Mourner Pottery Sherd"}, {"id": 1357, "stackSize": 64, "name": "plenty_pottery_sherd", "displayName": "Plenty Pottery Sherd"}, {"id": 1358, "stackSize": 64, "name": "prize_pottery_sherd", "displayName": "Prize <PERSON><PERSON> Sherd"}, {"id": 1359, "stackSize": 64, "name": "scrape_pottery_sherd", "displayName": "Scrape Pottery Sherd"}, {"id": 1360, "stackSize": 64, "name": "sheaf_pottery_sherd", "displayName": "<PERSON><PERSON>"}, {"id": 1361, "stackSize": 64, "name": "shelter_pottery_sherd", "displayName": "<PERSON>lter Pottery Sherd"}, {"id": 1362, "stackSize": 64, "name": "skull_pottery_sherd", "displayName": "Skull Pottery Sherd"}, {"id": 1363, "stackSize": 64, "name": "snort_pottery_sherd", "displayName": "<PERSON><PERSON><PERSON> Pottery Sherd"}, {"id": 1364, "stackSize": 64, "name": "copper_grate", "displayName": "Copper Grate"}, {"id": 1365, "stackSize": 64, "name": "exposed_copper_grate", "displayName": "Exposed Copper Grate"}, {"id": 1366, "stackSize": 64, "name": "weathered_copper_grate", "displayName": "Weathered Copper Grate"}, {"id": 1367, "stackSize": 64, "name": "oxidized_copper_grate", "displayName": "Oxidized Copper Grate"}, {"id": 1368, "stackSize": 64, "name": "waxed_copper_grate", "displayName": "Waxed Copper Grate"}, {"id": 1369, "stackSize": 64, "name": "waxed_exposed_copper_grate", "displayName": "Waxed Exposed Copper Grate"}, {"id": 1370, "stackSize": 64, "name": "waxed_weathered_copper_grate", "displayName": "Waxed Weathered Copper Grate"}, {"id": 1371, "stackSize": 64, "name": "waxed_oxidized_copper_grate", "displayName": "Waxed Oxidized Copper Grate"}, {"id": 1372, "stackSize": 64, "name": "copper_bulb", "displayName": "Copper Bulb"}, {"id": 1373, "stackSize": 64, "name": "exposed_copper_bulb", "displayName": "Exposed Copper Bulb"}, {"id": 1374, "stackSize": 64, "name": "weathered_copper_bulb", "displayName": "Weathered Copper Bulb"}, {"id": 1375, "stackSize": 64, "name": "oxidized_copper_bulb", "displayName": "Oxidized Copper Bulb"}, {"id": 1376, "stackSize": 64, "name": "waxed_copper_bulb", "displayName": "Waxed Copper Bulb"}, {"id": 1377, "stackSize": 64, "name": "waxed_exposed_copper_bulb", "displayName": "Waxed Exposed Copper Bulb"}, {"id": 1378, "stackSize": 64, "name": "waxed_weathered_copper_bulb", "displayName": "Waxed Weathered Copper Bulb"}, {"id": 1379, "stackSize": 64, "name": "waxed_oxidized_copper_bulb", "displayName": "Waxed Oxidized Copper Bulb"}, {"id": 1380, "stackSize": 64, "name": "trial_spawner", "displayName": "Trial Spawner"}, {"id": 1381, "stackSize": 64, "name": "trial_key", "displayName": "Trial Key"}, {"id": 1382, "stackSize": 64, "name": "ominous_trial_key", "displayName": "Ominous Trial Key"}, {"id": 1383, "stackSize": 64, "name": "vault", "displayName": "<PERSON><PERSON>"}, {"id": 1384, "stackSize": 64, "name": "ominous_bottle", "displayName": "Ominous <PERSON>"}, {"id": 9017, "stackSize": 1, "name": "item.mangrove_door", "displayName": "Mangrove Door"}, {"id": 9022, "stackSize": 1, "name": "rapid_fertilizer", "displayName": "Rapid Fertilizer"}, {"id": 9039, "stackSize": 1, "name": "sparkler", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 9044, "stackSize": 1, "name": "underwater_tnt", "displayName": "Underwater Tnt"}, {"id": 9046, "stackSize": 1, "name": "item.frame", "displayName": "<PERSON>ame"}, {"id": 9056, "stackSize": 1, "name": "element_15", "displayName": "Element 15"}, {"id": 9060, "stackSize": 1, "name": "polished_tuff_double_slab", "displayName": "Polished Tuff Double Slab"}, {"id": 9073, "stackSize": 1, "name": "balloon", "displayName": "Balloon"}, {"id": 9074, "stackSize": 1, "name": "smooth_sandstone_double_slab", "displayName": "Smooth Sandstone Double Slab"}, {"id": 9078, "stackSize": 1, "name": "element_104", "displayName": "Element 104"}, {"id": 9080, "stackSize": 1, "name": "white_candle_cake", "displayName": "White Candle Cake"}, {"id": 9083, "stackSize": 1, "name": "dead_tube_coral_wall_fan", "displayName": "Dead Tube Coral Wall Fan"}, {"id": 9096, "stackSize": 1, "name": "element_13", "displayName": "Element 13"}, {"id": 9117, "stackSize": 1, "name": "element_43", "displayName": "Element 43"}, {"id": 9124, "stackSize": 1, "name": "element_68", "displayName": "Element 68"}, {"id": 9130, "stackSize": 1, "name": "element_50", "displayName": "Element 50"}, {"id": 9131, "stackSize": 1, "name": "board", "displayName": "Board"}, {"id": 9132, "stackSize": 1, "name": "hard_blue_stained_glass", "displayName": "Hard Blue Stained Glass"}, {"id": 9146, "stackSize": 1, "name": "item.iron_door", "displayName": "Iron Door"}, {"id": 9149, "stackSize": 1, "name": "element_27", "displayName": "Element 27"}, {"id": 9156, "stackSize": 1, "name": "wooden_slab", "displayName": "<PERSON><PERSON>"}, {"id": 9169, "stackSize": 1, "name": "magenta_candle_cake", "displayName": "Ma<PERSON>a Candle Cake"}, {"id": 9170, "stackSize": 1, "name": "item.campfire", "displayName": "Campfire"}, {"id": 9178, "stackSize": 1, "name": "element_62", "displayName": "Element 62"}, {"id": 9198, "stackSize": 1, "name": "hard_pink_stained_glass", "displayName": "Hard Pink Stained Glass"}, {"id": 9202, "stackSize": 1, "name": "element_2", "displayName": "Element 2"}, {"id": 9207, "stackSize": 1, "name": "powder_snow", "displayName": "Powder Snow"}, {"id": 9210, "stackSize": 1, "name": "element_80", "displayName": "Element 80"}, {"id": 9213, "stackSize": 1, "name": "hard_brown_stained_glass", "displayName": "<PERSON> Brown Stained Glass"}, {"id": 9223, "stackSize": 1, "name": "carrots", "displayName": "Carrots"}, {"id": 9224, "stackSize": 1, "name": "hard_gray_stained_glass_pane", "displayName": "Hard Gray Stained Glass Pane"}, {"id": 9232, "stackSize": 1, "name": "portal", "displayName": "Portal"}, {"id": 9249, "stackSize": 1, "name": "light_block_15", "displayName": "Light Block 15"}, {"id": 9254, "stackSize": 1, "name": "planks", "displayName": "Planks"}, {"id": 9257, "stackSize": 1, "name": "stained_glass_pane", "displayName": "Stained Glass Pane"}, {"id": 9260, "stackSize": 1, "name": "colored_torch_purple", "displayName": "Colored Torch Purple"}, {"id": 9271, "stackSize": 1, "name": "hard_glass", "displayName": "Hard Glass"}, {"id": 9277, "stackSize": 1, "name": "flowing_water", "displayName": "Flowing Water"}, {"id": 9287, "stackSize": 1, "name": "lit_deepslate_redstone_ore", "displayName": "Lit Deepslate Redstone Ore"}, {"id": 9291, "stackSize": 1, "name": "lit_redstone_lamp", "displayName": "Lit Redstone Lamp"}, {"id": 9293, "stackSize": 1, "name": "element_52", "displayName": "Element 52"}, {"id": 9311, "stackSize": 1, "name": "element_86", "displayName": "Element 86"}, {"id": 9319, "stackSize": 1, "name": "petrified_oak_double_slab", "displayName": "Petrified Oak Double Slab"}, {"id": 9325, "stackSize": 1, "name": "end_gateway", "displayName": "End Gateway"}, {"id": 9327, "stackSize": 1, "name": "item.beetroot", "displayName": "Beetroot"}, {"id": 9328, "stackSize": 1, "name": "dark_oak_double_slab", "displayName": "Dark Oak Double Slab"}, {"id": 9337, "stackSize": 1, "name": "hard_cyan_stained_glass_pane", "displayName": "Hard <PERSON>an Stained Glass Pane"}, {"id": 9348, "stackSize": 1, "name": "element_51", "displayName": "Element 51"}, {"id": 9356, "stackSize": 1, "name": "hard_cyan_stained_glass", "displayName": "<PERSON> <PERSON><PERSON> Stained Glass"}, {"id": 9358, "stackSize": 1, "name": "agent_spawn_egg", "displayName": "Agent Spawn Egg"}, {"id": 9359, "stackSize": 1, "name": "carpet", "displayName": "Carpet"}, {"id": 9365, "stackSize": 1, "name": "colored_torch_blue", "displayName": "Colored Torch Blue"}, {"id": 9372, "stackSize": 1, "name": "cherry_wall_sign", "displayName": "Cherry Wall Sign"}, {"id": 9373, "stackSize": 1, "name": "element_74", "displayName": "Element 74"}, {"id": 9377, "stackSize": 1, "name": "dead_brain_coral_wall_fan", "displayName": "Dead Brain Coral Wall Fan"}, {"id": 9392, "stackSize": 1, "name": "item.nether_wart", "displayName": "Nether Wart"}, {"id": 9411, "stackSize": 1, "name": "dead_fire_coral_wall_fan", "displayName": "Dead Fire Coral Wall Fan"}, {"id": 9415, "stackSize": 1, "name": "element_97", "displayName": "Element 97"}, {"id": 9417, "stackSize": 1, "name": "hard_magenta_stained_glass_pane", "displayName": "Hard Magenta Stained Glass Pane"}, {"id": 9421, "stackSize": 1, "name": "chemistry_table", "displayName": "Chemistry Table"}, {"id": 9435, "stackSize": 1, "name": "hard_brown_stained_glass_pane", "displayName": "Hard Brown Stained Glass Pane"}, {"id": 9437, "stackSize": 1, "name": "short_dry_grass", "displayName": "Short Dry Grass"}, {"id": 9443, "stackSize": 1, "name": "hard_lime_stained_glass_pane", "displayName": "Hard Lime Stained Glass Pane"}, {"id": 9447, "stackSize": 1, "name": "element_23", "displayName": "Element 23"}, {"id": 9451, "stackSize": 1, "name": "coral", "displayName": "Coral"}, {"id": 9465, "stackSize": 1, "name": "reserved6", "displayName": "Reserved6"}, {"id": 9471, "stackSize": 1, "name": "shulker_box", "displayName": "Shulker Box"}, {"id": 9472, "stackSize": 1, "name": "red_candle_cake", "displayName": "Red Candle Cake"}, {"id": 9474, "stackSize": 1, "name": "deny", "displayName": "<PERSON><PERSON>"}, {"id": 9480, "stackSize": 1, "name": "pale_oak_wall_sign", "displayName": "Pale Oak Wall Sign"}, {"id": 9485, "stackSize": 1, "name": "item.cake", "displayName": "Cake"}, {"id": 9492, "stackSize": 1, "name": "andesite_double_slab", "displayName": "Andesite Double Slab"}, {"id": 9495, "stackSize": 1, "name": "sticky_piston_arm_collision", "displayName": "Sticky Piston Arm Collision"}, {"id": 9499, "stackSize": 1, "name": "quartz_double_slab", "displayName": "Quartz Double Slab"}, {"id": 9500, "stackSize": 1, "name": "element_41", "displayName": "Element 41"}, {"id": 9506, "stackSize": 1, "name": "light_block_0", "displayName": "Light Block 0"}, {"id": 9513, "stackSize": 1, "name": "stained_glass", "displayName": "Stained Glass"}, {"id": 9523, "stackSize": 1, "name": "item.flower_pot", "displayName": "Flower Pot"}, {"id": 9526, "stackSize": 1, "name": "hard_white_stained_glass_pane", "displayName": "Hard White Stained Glass Pane"}, {"id": 9529, "stackSize": 1, "name": "compound_creator", "displayName": "Compound Creator"}, {"id": 9531, "stackSize": 1, "name": "camera", "displayName": "Camera"}, {"id": 9550, "stackSize": 1, "name": "nether_brick_double_slab", "displayName": "Nether Brick Double Slab"}, {"id": 9555, "stackSize": 1, "name": "smooth_stone_double_slab", "displayName": "Smooth Stone Double Slab"}, {"id": 9556, "stackSize": 1, "name": "hard_red_stained_glass", "displayName": "Hard Red Stained Glass"}, {"id": 9559, "stackSize": 1, "name": "element_44", "displayName": "Element 44"}, {"id": 9564, "stackSize": 1, "name": "tall_dry_grass", "displayName": "Tall Dry Grass"}, {"id": 9579, "stackSize": 1, "name": "bleach", "displayName": "Bleach"}, {"id": 9581, "stackSize": 1, "name": "colored_torch_rg", "displayName": "Colored Torch Rg"}, {"id": 9583, "stackSize": 1, "name": "hard_red_stained_glass_pane", "displayName": "Hard Red Stained Glass Pane"}, {"id": 9602, "stackSize": 1, "name": "chalkboard", "displayName": "Chalkboard"}, {"id": 9607, "stackSize": 1, "name": "pink_candle_cake", "displayName": "Pink Candle Cake"}, {"id": 9614, "stackSize": 1, "name": "double_plant", "displayName": "Double Plant"}, {"id": 9617, "stackSize": 1, "name": "element_109", "displayName": "Element 109"}, {"id": 9620, "stackSize": 1, "name": "light_block_14", "displayName": "Light Block 14"}, {"id": 9627, "stackSize": 1, "name": "npc_spawn_egg", "displayName": "Npc Spawn Egg"}, {"id": 9632, "stackSize": 1, "name": "spruce_wall_sign", "displayName": "Spruce Wall Sign"}, {"id": 9637, "stackSize": 1, "name": "daylight_detector_inverted", "displayName": "Daylight Detector Inverted"}, {"id": 9641, "stackSize": 1, "name": "diorite_double_slab", "displayName": "Diorite Double Slab"}, {"id": 9645, "stackSize": 1, "name": "standing_sign", "displayName": "Standing Sign"}, {"id": 9649, "stackSize": 1, "name": "normal_stone_double_slab", "displayName": "Normal Stone Double Slab"}, {"id": 9653, "stackSize": 1, "name": "double_cut_copper_slab", "displayName": "Double Cut Copper Slab"}, {"id": 9655, "stackSize": 1, "name": "element_constructor", "displayName": "Element Constructor"}, {"id": 9656, "stackSize": 1, "name": "item.acacia_door", "displayName": "Acacia Door"}, {"id": 9661, "stackSize": 1, "name": "purpur_double_slab", "displayName": "Purpur Double Slab"}, {"id": 9686, "stackSize": 1, "name": "orange_candle_cake", "displayName": "Orange Candle Cake"}, {"id": 9694, "stackSize": 1, "name": "double_stone_block_slab3", "displayName": "Double Stone Block Slab3"}, {"id": 9710, "stackSize": 1, "name": "element_69", "displayName": "Element 69"}, {"id": 9713, "stackSize": 1, "name": "deepslate_brick_double_slab", "displayName": "Deepslate Brick Double Slab"}, {"id": 9715, "stackSize": 1, "name": "dark_prismarine_double_slab", "displayName": "<PERSON> Prismarine <PERSON> Slab"}, {"id": 9722, "stackSize": 1, "name": "element_102", "displayName": "Element 102"}, {"id": 9724, "stackSize": 1, "name": "colored_torch_bp", "displayName": "Colored Torch Bp"}, {"id": 9728, "stackSize": 1, "name": "dead_bubble_coral_wall_fan", "displayName": "Dead Bubble Coral Wall Fan"}, {"id": 9743, "stackSize": 1, "name": "info_update2", "displayName": "Info Update2"}, {"id": 9747, "stackSize": 1, "name": "element_32", "displayName": "Element 32"}, {"id": 9755, "stackSize": 1, "name": "element_42", "displayName": "Element 42"}, {"id": 9774, "stackSize": 1, "name": "coral_fan_dead", "displayName": "Coral Fan Dead"}, {"id": 9781, "stackSize": 1, "name": "monster_egg", "displayName": "Monster Egg"}, {"id": 9785, "stackSize": 1, "name": "purple_candle_cake", "displayName": "Purple Candle Cake"}, {"id": 9788, "stackSize": 1, "name": "potatoes", "displayName": "Potatoes"}, {"id": 9791, "stackSize": 1, "name": "boat", "displayName": "Boat"}, {"id": 9797, "stackSize": 1, "name": "blue_candle_cake", "displayName": "Blue Candle Cake"}, {"id": 9798, "stackSize": 1, "name": "element_22", "displayName": "Element 22"}, {"id": 9801, "stackSize": 1, "name": "compound", "displayName": "Compound"}, {"id": 9803, "stackSize": 1, "name": "ice_bomb", "displayName": "Ice Bomb"}, {"id": 9804, "stackSize": 1, "name": "medicine", "displayName": "Medicine"}, {"id": 9805, "stackSize": 1, "name": "hard_light_blue_stained_glass_pane", "displayName": "Hard Light Blue Stained Glass Pane"}, {"id": 9806, "stackSize": 1, "name": "glow_stick", "displayName": "Glow Stick"}, {"id": 9807, "stackSize": 1, "name": "element_83", "displayName": "Element 83"}, {"id": 9808, "stackSize": 1, "name": "lodestone_compass", "displayName": "Lodestone Compass"}, {"id": 9815, "stackSize": 1, "name": "polished_deepslate_double_slab", "displayName": "Polished Deepslate Double Slab"}, {"id": 9818, "stackSize": 1, "name": "mossy_cobblestone_double_slab", "displayName": "<PERSON><PERSON> Double Slab"}, {"id": 9825, "stackSize": 1, "name": "concrete", "displayName": "Concrete"}, {"id": 9840, "stackSize": 1, "name": "element_33", "displayName": "Element 33"}, {"id": 9847, "stackSize": 1, "name": "waxed_weathered_double_cut_copper_slab", "displayName": "Waxed Weathered Double Cut Copper Slab"}, {"id": 9855, "stackSize": 1, "name": "jungle_standing_sign", "displayName": "Jungle Standing Sign"}, {"id": 9857, "stackSize": 1, "name": "candle_cake", "displayName": "Candle Cake"}, {"id": 9868, "stackSize": 1, "name": "info_update", "displayName": "Info Update"}, {"id": 9874, "stackSize": 1, "name": "chest_boat", "displayName": "Chest Boat"}, {"id": 9875, "stackSize": 1, "name": "light_block_1", "displayName": "Light Block 1"}, {"id": 9882, "stackSize": 1, "name": "lit_furnace", "displayName": "Lit Furnace"}, {"id": 9890, "stackSize": 1, "name": "element_89", "displayName": "Element 89"}, {"id": 9902, "stackSize": 1, "name": "double_stone_block_slab", "displayName": "Double Stone Block Slab"}, {"id": 9904, "stackSize": 1, "name": "stone_brick_double_slab", "displayName": "Stone Brick Double Slab"}, {"id": 9914, "stackSize": 1, "name": "brick_double_slab", "displayName": "Brick Double Slab"}, {"id": 9916, "stackSize": 1, "name": "unlit_redstone_torch", "displayName": "Unlit Redstone Torch"}, {"id": 9927, "stackSize": 1, "name": "element_118", "displayName": "Element 118"}, {"id": 9929, "stackSize": 1, "name": "element_4", "displayName": "Element 4"}, {"id": 9933, "stackSize": 1, "name": "wool", "displayName": "Wool"}, {"id": 9938, "stackSize": 1, "name": "light_block_10", "displayName": "Light Block 10"}, {"id": 9940, "stackSize": 1, "name": "element_11", "displayName": "Element 11"}, {"id": 9942, "stackSize": 1, "name": "cobblestone_double_slab", "displayName": "Cobblestone Double Slab"}, {"id": 9951, "stackSize": 1, "name": "skull", "displayName": "Skull"}, {"id": 9960, "stackSize": 1, "name": "smooth_red_sandstone_double_slab", "displayName": "Smooth Red Sandstone Double Slab"}, {"id": 9968, "stackSize": 1, "name": "stained_hardened_clay", "displayName": "Stained Hardened Clay"}, {"id": 9973, "stackSize": 1, "name": "element_9", "displayName": "Element 9"}, {"id": 9986, "stackSize": 1, "name": "stone_block_slab4", "displayName": "Stone Block Slab4"}, {"id": 9998, "stackSize": 1, "name": "double_stone_block_slab2", "displayName": "Double Stone Block Slab2"}, {"id": 10000, "stackSize": 1, "name": "blue_egg", "displayName": "Blue Egg"}, {"id": 10002, "stackSize": 1, "name": "brown_egg", "displayName": "<PERSON>"}, {"id": 10013, "stackSize": 1, "name": "element_18", "displayName": "Element 18"}, {"id": 10015, "stackSize": 1, "name": "cherry_double_slab", "displayName": "Cherry Double Slab"}, {"id": 10017, "stackSize": 1, "name": "element_29", "displayName": "Element 29"}, {"id": 10020, "stackSize": 1, "name": "spruce_standing_sign", "displayName": "Spruce Standing Sign"}, {"id": 10029, "stackSize": 1, "name": "hard_black_stained_glass", "displayName": "Hard Black Stained Glass"}, {"id": 10035, "stackSize": 1, "name": "log", "displayName": "Log"}, {"id": 10037, "stackSize": 1, "name": "hard_orange_stained_glass_pane", "displayName": "Hard Orange Stained Glass Pane"}, {"id": 10043, "stackSize": 1, "name": "element_53", "displayName": "Element 53"}, {"id": 10044, "stackSize": 1, "name": "fence", "displayName": "<PERSON><PERSON>"}, {"id": 10045, "stackSize": 1, "name": "waxed_oxidized_double_cut_copper_slab", "displayName": "Waxed Oxidized Double Cut Copper Slab"}, {"id": 10051, "stackSize": 1, "name": "stonebrick", "displayName": "Stonebrick"}, {"id": 10060, "stackSize": 1, "name": "lit_blast_furnace", "displayName": "Lit Blast Furnace"}, {"id": 10062, "stackSize": 1, "name": "coral_block", "displayName": "Coral Block"}, {"id": 10068, "stackSize": 1, "name": "stone_block_slab", "displayName": "Stone Block Slab"}, {"id": 10069, "stackSize": 1, "name": "leaves", "displayName": "Leaves"}, {"id": 10074, "stackSize": 1, "name": "stone_block_slab2", "displayName": "Stone Block Slab2"}, {"id": 10075, "stackSize": 1, "name": "leaves2", "displayName": "Leaves2"}, {"id": 10080, "stackSize": 1, "name": "hard_black_stained_glass_pane", "displayName": "Hard Black Stained Glass Pane"}, {"id": 10087, "stackSize": 1, "name": "birch_standing_sign", "displayName": "<PERSON> Standing Sign"}, {"id": 10090, "stackSize": 1, "name": "stone_block_slab3", "displayName": "Stone Block Slab3"}, {"id": 10093, "stackSize": 1, "name": "element_16", "displayName": "Element 16"}, {"id": 10095, "stackSize": 1, "name": "sandstone_double_slab", "displayName": "Sandstone Double Slab"}, {"id": 10096, "stackSize": 1, "name": "red_sandstone_double_slab", "displayName": "Red Sandstone Double Slab"}, {"id": 10098, "stackSize": 1, "name": "prismarine_double_slab", "displayName": "Prismarine <PERSON> Slab"}, {"id": 10099, "stackSize": 1, "name": "prismarine_brick_double_slab", "displayName": "Prismarine Brick Double Slab"}, {"id": 10101, "stackSize": 1, "name": "red_nether_brick_double_slab", "displayName": "Red Nether Brick Double Slab"}, {"id": 10103, "stackSize": 1, "name": "end_stone_brick_double_slab", "displayName": "End Stone Brick Double Slab"}, {"id": 10104, "stackSize": 1, "name": "polished_andesite_double_slab", "displayName": "Polished Andesite Double Slab"}, {"id": 10105, "stackSize": 1, "name": "border_block", "displayName": "Border Block"}, {"id": 10106, "stackSize": 1, "name": "polished_diorite_double_slab", "displayName": "Polished Diorite Double Slab"}, {"id": 10107, "stackSize": 1, "name": "granite_double_slab", "displayName": "Granite Double Slab"}, {"id": 10108, "stackSize": 1, "name": "element_10", "displayName": "Element 10"}, {"id": 10109, "stackSize": 1, "name": "polished_granite_double_slab", "displayName": "Polished Granite Double Slab"}, {"id": 10110, "stackSize": 1, "name": "mossy_stone_brick_double_slab", "displayName": "<PERSON>y Stone Brick Double Slab"}, {"id": 10113, "stackSize": 1, "name": "smooth_quartz_double_slab", "displayName": "Smooth Quartz Double Slab"}, {"id": 10114, "stackSize": 1, "name": "cut_sandstone_double_slab", "displayName": "Cut Sandstone Double Slab"}, {"id": 10116, "stackSize": 1, "name": "cut_red_sandstone_double_slab", "displayName": "Cut Red Sandstone Double Slab"}, {"id": 10119, "stackSize": 1, "name": "double_stone_block_slab4", "displayName": "Double Stone Block Slab4"}, {"id": 10120, "stackSize": 1, "name": "sweet_berry_bush", "displayName": "Sweet <PERSON>"}, {"id": 10127, "stackSize": 1, "name": "coral_fan", "displayName": "Coral Fan"}, {"id": 10135, "stackSize": 1, "name": "sapling", "displayName": "Sapling"}, {"id": 10140, "stackSize": 1, "name": "soul_fire", "displayName": "Soul Fire"}, {"id": 10145, "stackSize": 1, "name": "hard_light_gray_stained_glass_pane", "displayName": "Hard Light Gray Stained Glass Pane"}, {"id": 10161, "stackSize": 1, "name": "red_flower", "displayName": "Red Flower"}, {"id": 10162, "stackSize": 1, "name": "hard_pink_stained_glass_pane", "displayName": "Hard Pink Stained Glass Pane"}, {"id": 10177, "stackSize": 1, "name": "deprecated_purpur_block_1", "displayName": "Deprecated Purpur Block 1"}, {"id": 10178, "stackSize": 1, "name": "element_77", "displayName": "Element 77"}, {"id": 10180, "stackSize": 1, "name": "deprecated_purpur_block_2", "displayName": "Deprecated Purpur Block 2"}, {"id": 10191, "stackSize": 1, "name": "tallgrass", "displayName": "Tallgrass"}, {"id": 10192, "stackSize": 1, "name": "element_103", "displayName": "Element 103"}, {"id": 10193, "stackSize": 1, "name": "mushroom_stem", "displayName": "Mushroom Stem"}, {"id": 10195, "stackSize": 1, "name": "log2", "displayName": "Log2"}, {"id": 10201, "stackSize": 1, "name": "deprecated_anvil", "displayName": "Deprecated Anvil"}, {"id": 10204, "stackSize": 1, "name": "leaf_litter", "displayName": "Leaf Litter"}, {"id": 10210, "stackSize": 1, "name": "element_56", "displayName": "Element 56"}, {"id": 10220, "stackSize": 1, "name": "firefly_bush", "displayName": "Firefly Bush"}, {"id": 10227, "stackSize": 1, "name": "concrete_powder", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 10228, "stackSize": 1, "name": "element_75", "displayName": "Element 75"}, {"id": 10230, "stackSize": 1, "name": "element_64", "displayName": "Element 64"}, {"id": 10234, "stackSize": 1, "name": "item.hopper", "displayName": "<PERSON>"}, {"id": 10235, "stackSize": 1, "name": "wood", "displayName": "<PERSON>"}, {"id": 10236, "stackSize": 1, "name": "hard_magenta_stained_glass", "displayName": "Hard Magenta Stained Glass"}, {"id": 10237, "stackSize": 1, "name": "mud_brick_double_slab", "displayName": "Mud Brick Double Slab"}, {"id": 10239, "stackSize": 1, "name": "crimson_double_slab", "displayName": "Crimson Double Slab"}, {"id": 10240, "stackSize": 1, "name": "hard_purple_stained_glass", "displayName": "Hard Purple Stained Glass"}, {"id": 10248, "stackSize": 1, "name": "item.dark_oak_door", "displayName": "Dark Oak Door"}, {"id": 10253, "stackSize": 1, "name": "hard_purple_stained_glass_pane", "displayName": "Hard Purple Stained Glass Pane"}, {"id": 10258, "stackSize": 1, "name": "hard_green_stained_glass_pane", "displayName": "Hard Green Stained Glass Pane"}, {"id": 10270, "stackSize": 1, "name": "torchflower_crop", "displayName": "Torchflower <PERSON>"}, {"id": 10284, "stackSize": 1, "name": "material_reducer", "displayName": "Material Reducer"}, {"id": 10285, "stackSize": 1, "name": "lab_table", "displayName": "Lab Table"}, {"id": 10286, "stackSize": 1, "name": "hard_white_stained_glass", "displayName": "Hard White Stained Glass"}, {"id": 10288, "stackSize": 1, "name": "hard_orange_stained_glass", "displayName": "Hard Orange Stained Glass"}, {"id": 10290, "stackSize": 1, "name": "hard_light_blue_stained_glass", "displayName": "Hard Light Blue Stained Glass"}, {"id": 10291, "stackSize": 1, "name": "hard_yellow_stained_glass", "displayName": "Hard Yellow Stained Glass"}, {"id": 10292, "stackSize": 1, "name": "hard_lime_stained_glass", "displayName": "Hard Lime Stained Glass"}, {"id": 10293, "stackSize": 1, "name": "hard_gray_stained_glass", "displayName": "Hard Gray Stained Glass"}, {"id": 10294, "stackSize": 1, "name": "hard_light_gray_stained_glass", "displayName": "Hard Light Gray Stained Glass"}, {"id": 10295, "stackSize": 1, "name": "hard_green_stained_glass", "displayName": "Hard Green Stained Glass"}, {"id": 10296, "stackSize": 1, "name": "element_84", "displayName": "Element 84"}, {"id": 10297, "stackSize": 1, "name": "hard_stained_glass", "displayName": "Hard Stained Glass"}, {"id": 10298, "stackSize": 1, "name": "hard_yellow_stained_glass_pane", "displayName": "Hard Yellow Stained Glass Pane"}, {"id": 10299, "stackSize": 1, "name": "hard_blue_stained_glass_pane", "displayName": "Hard Blue Stained Glass Pane"}, {"id": 10300, "stackSize": 1, "name": "hard_stained_glass_pane", "displayName": "Hard Stained Glass Pane"}, {"id": 10302, "stackSize": 1, "name": "colored_torch_red", "displayName": "Colored Torch Red"}, {"id": 10304, "stackSize": 1, "name": "colored_torch_green", "displayName": "Colored Torch Green"}, {"id": 10305, "stackSize": 1, "name": "light_block_2", "displayName": "Light Block 2"}, {"id": 10306, "stackSize": 1, "name": "light_block_3", "displayName": "Light Block 3"}, {"id": 10307, "stackSize": 1, "name": "light_block_4", "displayName": "Light Block 4"}, {"id": 10308, "stackSize": 1, "name": "gray_candle_cake", "displayName": "Gray Candle Cake"}, {"id": 10309, "stackSize": 1, "name": "light_block_5", "displayName": "Light Block 5"}, {"id": 10310, "stackSize": 1, "name": "light_block_6", "displayName": "Light Block 6"}, {"id": 10313, "stackSize": 1, "name": "light_block_7", "displayName": "Light Block 7"}, {"id": 10314, "stackSize": 1, "name": "deepslate_tile_double_slab", "displayName": "Deepslate Tile Double Slab"}, {"id": 10315, "stackSize": 1, "name": "light_block_8", "displayName": "Light Block 8"}, {"id": 10316, "stackSize": 1, "name": "light_block_9", "displayName": "Light Block 9"}, {"id": 10317, "stackSize": 1, "name": "light_block_11", "displayName": "Light Block 11"}, {"id": 10319, "stackSize": 1, "name": "light_block_12", "displayName": "Light Block 12"}, {"id": 10320, "stackSize": 1, "name": "light_block_13", "displayName": "Light Block 13"}, {"id": 10327, "stackSize": 1, "name": "fire", "displayName": "Fire"}, {"id": 10330, "stackSize": 1, "name": "black_candle_cake", "displayName": "Black Candle Cake"}, {"id": 10337, "stackSize": 1, "name": "element_0", "displayName": "Element 0"}, {"id": 10338, "stackSize": 1, "name": "element_1", "displayName": "Element 1"}, {"id": 10339, "stackSize": 1, "name": "element_3", "displayName": "Element 3"}, {"id": 10340, "stackSize": 1, "name": "element_5", "displayName": "Element 5"}, {"id": 10341, "stackSize": 1, "name": "element_6", "displayName": "Element 6"}, {"id": 10343, "stackSize": 1, "name": "element_7", "displayName": "Element 7"}, {"id": 10344, "stackSize": 1, "name": "element_8", "displayName": "Element 8"}, {"id": 10345, "stackSize": 1, "name": "element_12", "displayName": "Element 12"}, {"id": 10346, "stackSize": 1, "name": "element_14", "displayName": "Element 14"}, {"id": 10347, "stackSize": 1, "name": "pale_oak_standing_sign", "displayName": "Pale Oak Standing Sign"}, {"id": 10348, "stackSize": 1, "name": "client_request_placeholder_block", "displayName": "Client Request Placeholder Block"}, {"id": 10349, "stackSize": 1, "name": "element_17", "displayName": "Element 17"}, {"id": 10350, "stackSize": 1, "name": "element_19", "displayName": "Element 19"}, {"id": 10352, "stackSize": 1, "name": "element_20", "displayName": "Element 20"}, {"id": 10353, "stackSize": 1, "name": "element_21", "displayName": "Element 21"}, {"id": 10354, "stackSize": 1, "name": "element_24", "displayName": "Element 24"}, {"id": 10355, "stackSize": 1, "name": "element_25", "displayName": "Element 25"}, {"id": 10356, "stackSize": 1, "name": "element_26", "displayName": "Element 26"}, {"id": 10357, "stackSize": 1, "name": "element_28", "displayName": "Element 28"}, {"id": 10358, "stackSize": 1, "name": "element_30", "displayName": "Element 30"}, {"id": 10359, "stackSize": 1, "name": "element_31", "displayName": "Element 31"}, {"id": 10360, "stackSize": 1, "name": "element_34", "displayName": "Element 34"}, {"id": 10361, "stackSize": 1, "name": "bamboo_double_slab", "displayName": "Bamboo Double Slab"}, {"id": 10362, "stackSize": 1, "name": "element_35", "displayName": "Element 35"}, {"id": 10363, "stackSize": 1, "name": "element_36", "displayName": "Element 36"}, {"id": 10364, "stackSize": 1, "name": "element_37", "displayName": "Element 37"}, {"id": 10365, "stackSize": 1, "name": "element_38", "displayName": "Element 38"}, {"id": 10366, "stackSize": 1, "name": "element_39", "displayName": "Element 39"}, {"id": 10367, "stackSize": 1, "name": "element_40", "displayName": "Element 40"}, {"id": 10368, "stackSize": 1, "name": "element_45", "displayName": "Element 45"}, {"id": 10369, "stackSize": 1, "name": "element_46", "displayName": "Element 46"}, {"id": 10370, "stackSize": 1, "name": "element_47", "displayName": "Element 47"}, {"id": 10371, "stackSize": 1, "name": "element_48", "displayName": "Element 48"}, {"id": 10373, "stackSize": 1, "name": "element_49", "displayName": "Element 49"}, {"id": 10374, "stackSize": 1, "name": "element_54", "displayName": "Element 54"}, {"id": 10375, "stackSize": 1, "name": "element_55", "displayName": "Element 55"}, {"id": 10377, "stackSize": 1, "name": "element_57", "displayName": "Element 57"}, {"id": 10378, "stackSize": 1, "name": "element_58", "displayName": "Element 58"}, {"id": 10379, "stackSize": 1, "name": "element_59", "displayName": "Element 59"}, {"id": 10380, "stackSize": 1, "name": "element_60", "displayName": "Element 60"}, {"id": 10381, "stackSize": 1, "name": "element_61", "displayName": "Element 61"}, {"id": 10382, "stackSize": 1, "name": "element_63", "displayName": "Element 63"}, {"id": 10383, "stackSize": 1, "name": "element_65", "displayName": "Element 65"}, {"id": 10384, "stackSize": 1, "name": "element_66", "displayName": "Element 66"}, {"id": 10385, "stackSize": 1, "name": "element_67", "displayName": "Element 67"}, {"id": 10386, "stackSize": 1, "name": "element_70", "displayName": "Element 70"}, {"id": 10387, "stackSize": 1, "name": "element_71", "displayName": "Element 71"}, {"id": 10388, "stackSize": 1, "name": "element_72", "displayName": "Element 72"}, {"id": 10389, "stackSize": 1, "name": "element_73", "displayName": "Element 73"}, {"id": 10391, "stackSize": 1, "name": "element_76", "displayName": "Element 76"}, {"id": 10392, "stackSize": 1, "name": "element_78", "displayName": "Element 78"}, {"id": 10395, "stackSize": 1, "name": "element_79", "displayName": "Element 79"}, {"id": 10396, "stackSize": 1, "name": "element_81", "displayName": "Element 81"}, {"id": 10397, "stackSize": 1, "name": "element_82", "displayName": "Element 82"}, {"id": 10399, "stackSize": 1, "name": "element_85", "displayName": "Element 85"}, {"id": 10400, "stackSize": 1, "name": "element_87", "displayName": "Element 87"}, {"id": 10401, "stackSize": 1, "name": "element_88", "displayName": "Element 88"}, {"id": 10402, "stackSize": 1, "name": "element_90", "displayName": "Element 90"}, {"id": 10403, "stackSize": 1, "name": "element_91", "displayName": "Element 91"}, {"id": 10404, "stackSize": 1, "name": "element_92", "displayName": "Element 92"}, {"id": 10405, "stackSize": 1, "name": "element_93", "displayName": "Element 93"}, {"id": 10406, "stackSize": 1, "name": "element_94", "displayName": "Element 94"}, {"id": 10407, "stackSize": 1, "name": "element_95", "displayName": "Element 95"}, {"id": 10409, "stackSize": 1, "name": "element_96", "displayName": "Element 96"}, {"id": 10410, "stackSize": 1, "name": "element_98", "displayName": "Element 98"}, {"id": 10412, "stackSize": 1, "name": "element_99", "displayName": "Element 99"}, {"id": 10414, "stackSize": 1, "name": "element_100", "displayName": "Element 100"}, {"id": 10415, "stackSize": 1, "name": "element_101", "displayName": "Element 101"}, {"id": 10416, "stackSize": 1, "name": "element_105", "displayName": "Element 105"}, {"id": 10417, "stackSize": 1, "name": "element_106", "displayName": "Element 106"}, {"id": 10418, "stackSize": 1, "name": "element_107", "displayName": "Element 107"}, {"id": 10419, "stackSize": 1, "name": "element_108", "displayName": "Element 108"}, {"id": 10420, "stackSize": 1, "name": "element_110", "displayName": "Element 110"}, {"id": 10421, "stackSize": 1, "name": "element_111", "displayName": "Element 111"}, {"id": 10422, "stackSize": 1, "name": "element_112", "displayName": "Element 112"}, {"id": 10424, "stackSize": 1, "name": "element_113", "displayName": "Element 113"}, {"id": 10426, "stackSize": 1, "name": "element_114", "displayName": "Element 114"}, {"id": 10427, "stackSize": 1, "name": "element_115", "displayName": "Element 115"}, {"id": 10429, "stackSize": 1, "name": "element_116", "displayName": "Element 116"}, {"id": 10430, "stackSize": 1, "name": "element_117", "displayName": "Element 117"}, {"id": 10433, "stackSize": 1, "name": "dye", "displayName": "Dye"}, {"id": 10434, "stackSize": 1, "name": "banner_pattern", "displayName": "<PERSON>"}, {"id": 10435, "stackSize": 1, "name": "spawn_egg", "displayName": "Spawn Egg"}, {"id": 10439, "stackSize": 1, "name": "item.warped_door", "displayName": "Warped Door"}, {"id": 10441, "stackSize": 1, "name": "piston_arm_collision", "displayName": "Piston Arm Collision"}, {"id": 10446, "stackSize": 1, "name": "blackstone_double_slab", "displayName": "Blackstone Double Slab"}, {"id": 10450, "stackSize": 1, "name": "crimson_wall_sign", "displayName": "Crimson Wall Sign"}, {"id": 10452, "stackSize": 1, "name": "item.glow_frame", "displayName": "Glow Frame"}, {"id": 10459, "stackSize": 1, "name": "cactus_flower", "displayName": "Cactus Flower"}, {"id": 10463, "stackSize": 1, "name": "warped_standing_sign", "displayName": "Warped Standing Sign"}, {"id": 10465, "stackSize": 1, "name": "pitcher_crop", "displayName": "Pitcher C<PERSON>"}, {"id": 10471, "stackSize": 1, "name": "light_blue_candle_cake", "displayName": "Light Blue Candle Cake"}, {"id": 10482, "stackSize": 1, "name": "powered_comparator", "displayName": "Powered Comparator"}, {"id": 10483, "stackSize": 1, "name": "warped_wall_sign", "displayName": "Warped Wall Sign"}, {"id": 10486, "stackSize": 1, "name": "mangrove_double_slab", "displayName": "Mangrove Double Slab"}, {"id": 10488, "stackSize": 1, "name": "oxidized_double_cut_copper_slab", "displayName": "Oxidized Double Cut Copper Slab"}, {"id": 10490, "stackSize": 1, "name": "exposed_double_cut_copper_slab", "displayName": "Exposed Double Cut Copper Slab"}, {"id": 10495, "stackSize": 1, "name": "polished_blackstone_double_slab", "displayName": "Polished Blackstone Double Slab"}, {"id": 10496, "stackSize": 1, "name": "hard_glass_pane", "displayName": "Hard Glass Pane"}, {"id": 10498, "stackSize": 1, "name": "polished_blackstone_brick_double_slab", "displayName": "Polished Blackstone Brick Double Slab"}, {"id": 10501, "stackSize": 1, "name": "resin_brick_double_slab", "displayName": "Resin Brick Double Slab"}, {"id": 10502, "stackSize": 1, "name": "cyan_candle_cake", "displayName": "<PERSON><PERSON>"}, {"id": 10509, "stackSize": 1, "name": "stonecutter", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 10512, "stackSize": 1, "name": "invisible_bedrock", "displayName": "Invisible Bedrock"}, {"id": 10517, "stackSize": 1, "name": "underwater_torch", "displayName": "Underwater Torch"}, {"id": 10518, "stackSize": 1, "name": "wall_banner", "displayName": "<PERSON>"}, {"id": 10519, "stackSize": 1, "name": "spruce_double_slab", "displayName": "Spruce Double Slab"}, {"id": 10523, "stackSize": 1, "name": "glowingobsidian", "displayName": "Glowingobsidian"}, {"id": 10527, "stackSize": 1, "name": "moving_block", "displayName": "Moving Block"}, {"id": 10531, "stackSize": 1, "name": "cave_vines_body_with_berries", "displayName": "Cave Vines Body With Berries"}, {"id": 10535, "stackSize": 1, "name": "green_candle_cake", "displayName": "Green Candle Cake"}, {"id": 10538, "stackSize": 1, "name": "oak_double_slab", "displayName": "Oak Double Slab"}, {"id": 10539, "stackSize": 1, "name": "brown_candle_cake", "displayName": "Brown Candle Cake"}, {"id": 10541, "stackSize": 1, "name": "acacia_wall_sign", "displayName": "Acacia Wall Sign"}, {"id": 10549, "stackSize": 1, "name": "item.wooden_door", "displayName": "Wooden Door"}, {"id": 10554, "stackSize": 1, "name": "redstone_wire", "displayName": "Redstone Wire"}, {"id": 10556, "stackSize": 1, "name": "lava", "displayName": "<PERSON><PERSON>"}, {"id": 10557, "stackSize": 1, "name": "item.crimson_door", "displayName": "Crimson Door"}, {"id": 10564, "stackSize": 1, "name": "brain_coral_wall_fan", "displayName": "Brain <PERSON>"}, {"id": 10567, "stackSize": 1, "name": "bamboo_mosaic_double_slab", "displayName": "Bamboo Mosaic Double Slab"}, {"id": 10571, "stackSize": 1, "name": "mangrove_standing_sign", "displayName": "Mangrove Standing Sign"}, {"id": 10573, "stackSize": 1, "name": "lit_redstone_ore", "displayName": "Lit Redstone Ore"}, {"id": 10582, "stackSize": 1, "name": "wildflowers", "displayName": "Wildflowers"}, {"id": 10588, "stackSize": 1, "name": "warped_double_slab", "displayName": "Warped Double Slab"}, {"id": 10589, "stackSize": 1, "name": "jungle_wall_sign", "displayName": "Jungle Wall Sign"}, {"id": 10598, "stackSize": 1, "name": "powered_repeater", "displayName": "Powered Repeater"}, {"id": 10604, "stackSize": 1, "name": "unknown", "displayName": "Unknown"}, {"id": 10605, "stackSize": 1, "name": "yellow_candle_cake", "displayName": "Yellow Candle Cake"}, {"id": 10615, "stackSize": 1, "name": "item.wheat", "displayName": "Wheat"}, {"id": 10616, "stackSize": 1, "name": "item.spruce_door", "displayName": "Spruce Door"}, {"id": 10618, "stackSize": 1, "name": "frosted_ice", "displayName": "Frosted Ice"}, {"id": 10623, "stackSize": 1, "name": "cave_vines", "displayName": "Cave Vines"}, {"id": 10624, "stackSize": 1, "name": "melon_stem", "displayName": "Melon Stem"}, {"id": 10627, "stackSize": 1, "name": "horn_coral_wall_fan", "displayName": "Horn <PERSON> Wall Fan"}, {"id": 10630, "stackSize": 1, "name": "wall_sign", "displayName": "Wall Sign"}, {"id": 10633, "stackSize": 1, "name": "birch_double_slab", "displayName": "Birch Double Slab"}, {"id": 10637, "stackSize": 1, "name": "mangrove_wall_sign", "displayName": "Mangrove Wall Sign"}, {"id": 10638, "stackSize": 1, "name": "light_gray_candle_cake", "displayName": "Light Gray Candle Cake"}, {"id": 10640, "stackSize": 1, "name": "darkoak_wall_sign", "displayName": "Darkoak Wall Sign"}, {"id": 10644, "stackSize": 1, "name": "fire_coral_wall_fan", "displayName": "Fire Coral Wall Fan"}, {"id": 10645, "stackSize": 1, "name": "flowing_lava", "displayName": "Flowing Lava"}, {"id": 10650, "stackSize": 1, "name": "waxed_double_cut_copper_slab", "displayName": "Waxed Double Cut Copper Slab"}, {"id": 10651, "stackSize": 1, "name": "item.kelp", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 10653, "stackSize": 1, "name": "water", "displayName": "Water"}, {"id": 10654, "stackSize": 1, "name": "chemical_heat", "displayName": "Chemical Heat"}, {"id": 10656, "stackSize": 1, "name": "unpowered_repeater", "displayName": "Unpowered Repeater"}, {"id": 10660, "stackSize": 1, "name": "acacia_double_slab", "displayName": "Acacia Double Slab"}, {"id": 10664, "stackSize": 1, "name": "bubble_column", "displayName": "Bubble Column"}, {"id": 10668, "stackSize": 1, "name": "cobbled_deepslate_double_slab", "displayName": "Cobbled Deepslate Double Slab"}, {"id": 10670, "stackSize": 1, "name": "cherry_standing_sign", "displayName": "<PERSON> Standing Sign"}, {"id": 10671, "stackSize": 1, "name": "item.chain", "displayName": "Chain"}, {"id": 10677, "stackSize": 1, "name": "tuff_brick_double_slab", "displayName": "Tuff Brick Double Slab"}, {"id": 10682, "stackSize": 1, "name": "item.reeds", "displayName": "<PERSON><PERSON>"}, {"id": 10685, "stackSize": 1, "name": "item.camera", "displayName": "Camera"}, {"id": 10688, "stackSize": 1, "name": "item.jungle_door", "displayName": "Jungle Door"}, {"id": 10694, "stackSize": 1, "name": "acacia_standing_sign", "displayName": "Acacia Standing Sign"}, {"id": 10700, "stackSize": 1, "name": "pumpkin_stem", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 10703, "stackSize": 1, "name": "unpowered_comparator", "displayName": "Unpowered Comparator"}, {"id": 10705, "stackSize": 1, "name": "item.nether_sprouts", "displayName": "Nether Sprouts"}, {"id": 10710, "stackSize": 1, "name": "cocoa", "displayName": "Cocoa"}, {"id": 10716, "stackSize": 1, "name": "item.bed", "displayName": "Bed"}, {"id": 10720, "stackSize": 1, "name": "waxed_exposed_double_cut_copper_slab", "displayName": "Waxed Exposed Double Cut Copper Slab"}, {"id": 10721, "stackSize": 1, "name": "bubble_coral_wall_fan", "displayName": "Bubble Coral Wall Fan"}, {"id": 10724, "stackSize": 1, "name": "birch_wall_sign", "displayName": "<PERSON> Sign"}, {"id": 10725, "stackSize": 1, "name": "bamboo_wall_sign", "displayName": "Bamboo Wall Sign"}, {"id": 10726, "stackSize": 1, "name": "bush", "displayName": "<PERSON>"}, {"id": 10727, "stackSize": 1, "name": "bamboo_sapling", "displayName": "Bamboo Sapling"}, {"id": 10728, "stackSize": 1, "name": "standing_banner", "displayName": "Standing Banner"}, {"id": 10730, "stackSize": 1, "name": "jungle_double_slab", "displayName": "Jungle Double Slab"}, {"id": 10737, "stackSize": 1, "name": "dead_horn_coral_wall_fan", "displayName": "Dead Horn Coral Wall Fan"}, {"id": 10738, "stackSize": 1, "name": "weathered_double_cut_copper_slab", "displayName": "Weathered Double Cut Copper Slab"}, {"id": 10741, "stackSize": 1, "name": "allow", "displayName": "Allow"}, {"id": 10742, "stackSize": 1, "name": "item.birch_door", "displayName": "<PERSON>"}, {"id": 10744, "stackSize": 1, "name": "bamboo_standing_sign", "displayName": "Bamboo Standing Sign"}, {"id": 10752, "stackSize": 1, "name": "crimson_standing_sign", "displayName": "Crimson Standing Sign"}, {"id": 10753, "stackSize": 1, "name": "darkoak_standing_sign", "displayName": "Darkoak Standing Sign"}, {"id": 10756, "stackSize": 1, "name": "netherreactor", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 10757, "stackSize": 1, "name": "trip_wire", "displayName": "<PERSON>"}, {"id": 10758, "stackSize": 1, "name": "item.cauldron", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 10759, "stackSize": 1, "name": "cave_vines_head_with_berries", "displayName": "Cave Vines Head With Berries"}, {"id": 10761, "stackSize": 1, "name": "item.brewing_stand", "displayName": "Brewing Stand"}, {"id": 10763, "stackSize": 1, "name": "end_portal", "displayName": "End Portal"}, {"id": 10765, "stackSize": 1, "name": "lit_smoker", "displayName": "Lit Smoker"}, {"id": 10770, "stackSize": 1, "name": "tuff_double_slab", "displayName": "<PERSON>ff <PERSON> Slab"}, {"id": 10776, "stackSize": 1, "name": "item.soul_campfire", "displayName": "Soul Campfire"}, {"id": 10778, "stackSize": 1, "name": "lime_candle_cake", "displayName": "Lime Candle Cake"}, {"id": 10782, "stackSize": 1, "name": "pale_oak_double_slab", "displayName": "Pale Oak Double Slab"}, {"id": 10789, "stackSize": 1, "name": "tube_coral_wall_fan", "displayName": "Tube Coral Wall Fan"}]