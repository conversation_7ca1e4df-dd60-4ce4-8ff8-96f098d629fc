[{"id": 0, "name": "ambient.cave"}, {"id": 1, "name": "ambient.underwater.enter"}, {"id": 2, "name": "ambient.underwater.exit"}, {"id": 3, "name": "ambient.underwater.loop"}, {"id": 4, "name": "ambient.underwater.loop.additions"}, {"id": 5, "name": "ambient.underwater.loop.additions.rare"}, {"id": 6, "name": "ambient.underwater.loop.additions.ultra_rare"}, {"id": 7, "name": "block.anvil.break"}, {"id": 8, "name": "block.anvil.destroy"}, {"id": 9, "name": "block.anvil.fall"}, {"id": 10, "name": "block.anvil.hit"}, {"id": 11, "name": "block.anvil.land"}, {"id": 12, "name": "block.anvil.place"}, {"id": 13, "name": "block.anvil.step"}, {"id": 14, "name": "block.anvil.use"}, {"id": 15, "name": "block.beacon.activate"}, {"id": 16, "name": "block.beacon.ambient"}, {"id": 17, "name": "block.beacon.deactivate"}, {"id": 18, "name": "block.beacon.power_select"}, {"id": 19, "name": "block.brewing_stand.brew"}, {"id": 20, "name": "block.bubble_column.bubble_pop"}, {"id": 21, "name": "block.bubble_column.upwards_ambient"}, {"id": 22, "name": "block.bubble_column.upwards_inside"}, {"id": 23, "name": "block.bubble_column.whirlpool_ambient"}, {"id": 24, "name": "block.bubble_column.whirlpool_inside"}, {"id": 25, "name": "block.chest.close"}, {"id": 26, "name": "block.chest.locked"}, {"id": 27, "name": "block.chest.open"}, {"id": 28, "name": "block.chorus_flower.death"}, {"id": 29, "name": "block.chorus_flower.grow"}, {"id": 30, "name": "block.wool.break"}, {"id": 31, "name": "block.wool.fall"}, {"id": 32, "name": "block.wool.hit"}, {"id": 33, "name": "block.wool.place"}, {"id": 34, "name": "block.wool.step"}, {"id": 35, "name": "block.comparator.click"}, {"id": 36, "name": "block.conduit.activate"}, {"id": 37, "name": "block.conduit.ambient"}, {"id": 38, "name": "block.conduit.ambient.short"}, {"id": 39, "name": "block.conduit.attack.target"}, {"id": 40, "name": "block.conduit.deactivate"}, {"id": 41, "name": "block.dispenser.dispense"}, {"id": 42, "name": "block.dispenser.fail"}, {"id": 43, "name": "block.dispenser.launch"}, {"id": 44, "name": "block.enchantment_table.use"}, {"id": 45, "name": "block.end_gateway.spawn"}, {"id": 46, "name": "block.end_portal.spawn"}, {"id": 47, "name": "block.end_portal_frame.fill"}, {"id": 48, "name": "block.ender_chest.close"}, {"id": 49, "name": "block.ender_chest.open"}, {"id": 50, "name": "block.fence_gate.close"}, {"id": 51, "name": "block.fence_gate.open"}, {"id": 52, "name": "block.fire.ambient"}, {"id": 53, "name": "block.fire.extinguish"}, {"id": 54, "name": "block.furnace.fire_crackle"}, {"id": 55, "name": "block.glass.break"}, {"id": 56, "name": "block.glass.fall"}, {"id": 57, "name": "block.glass.hit"}, {"id": 58, "name": "block.glass.place"}, {"id": 59, "name": "block.glass.step"}, {"id": 60, "name": "block.grass.break"}, {"id": 61, "name": "block.grass.fall"}, {"id": 62, "name": "block.grass.hit"}, {"id": 63, "name": "block.grass.place"}, {"id": 64, "name": "block.grass.step"}, {"id": 65, "name": "block.wet_grass.break"}, {"id": 66, "name": "block.wet_grass.fall"}, {"id": 67, "name": "block.wet_grass.hit"}, {"id": 68, "name": "block.wet_grass.place"}, {"id": 69, "name": "block.wet_grass.step"}, {"id": 70, "name": "block.coral_block.break"}, {"id": 71, "name": "block.coral_block.fall"}, {"id": 72, "name": "block.coral_block.hit"}, {"id": 73, "name": "block.coral_block.place"}, {"id": 74, "name": "block.coral_block.step"}, {"id": 75, "name": "block.gravel.break"}, {"id": 76, "name": "block.gravel.fall"}, {"id": 77, "name": "block.gravel.hit"}, {"id": 78, "name": "block.gravel.place"}, {"id": 79, "name": "block.gravel.step"}, {"id": 80, "name": "block.iron_door.close"}, {"id": 81, "name": "block.iron_door.open"}, {"id": 82, "name": "block.iron_trapdoor.close"}, {"id": 83, "name": "block.iron_trapdoor.open"}, {"id": 84, "name": "block.ladder.break"}, {"id": 85, "name": "block.ladder.fall"}, {"id": 86, "name": "block.ladder.hit"}, {"id": 87, "name": "block.ladder.place"}, {"id": 88, "name": "block.ladder.step"}, {"id": 89, "name": "block.lava.ambient"}, {"id": 90, "name": "block.lava.extinguish"}, {"id": 91, "name": "block.lava.pop"}, {"id": 92, "name": "block.lever.click"}, {"id": 93, "name": "block.metal.break"}, {"id": 94, "name": "block.metal.fall"}, {"id": 95, "name": "block.metal.hit"}, {"id": 96, "name": "block.metal.place"}, {"id": 97, "name": "block.metal.step"}, {"id": 98, "name": "block.metal_pressure_plate.click_off"}, {"id": 99, "name": "block.metal_pressure_plate.click_on"}, {"id": 100, "name": "block.note_block.basedrum"}, {"id": 101, "name": "block.note_block.bass"}, {"id": 102, "name": "block.note_block.bell"}, {"id": 103, "name": "block.note_block.chime"}, {"id": 104, "name": "block.note_block.flute"}, {"id": 105, "name": "block.note_block.guitar"}, {"id": 106, "name": "block.note_block.harp"}, {"id": 107, "name": "block.note_block.hat"}, {"id": 108, "name": "block.note_block.pling"}, {"id": 109, "name": "block.note_block.snare"}, {"id": 110, "name": "block.note_block.xylophone"}, {"id": 111, "name": "block.piston.contract"}, {"id": 112, "name": "block.piston.extend"}, {"id": 113, "name": "block.portal.ambient"}, {"id": 114, "name": "block.portal.travel"}, {"id": 115, "name": "block.portal.trigger"}, {"id": 116, "name": "block.pumpkin.carve"}, {"id": 117, "name": "block.redstone_torch.burnout"}, {"id": 118, "name": "block.sand.break"}, {"id": 119, "name": "block.sand.fall"}, {"id": 120, "name": "block.sand.hit"}, {"id": 121, "name": "block.sand.place"}, {"id": 122, "name": "block.sand.step"}, {"id": 123, "name": "block.shulker_box.close"}, {"id": 124, "name": "block.shulker_box.open"}, {"id": 125, "name": "block.slime_block.break"}, {"id": 126, "name": "block.slime_block.fall"}, {"id": 127, "name": "block.slime_block.hit"}, {"id": 128, "name": "block.slime_block.place"}, {"id": 129, "name": "block.slime_block.step"}, {"id": 130, "name": "block.snow.break"}, {"id": 131, "name": "block.snow.fall"}, {"id": 132, "name": "block.snow.hit"}, {"id": 133, "name": "block.snow.place"}, {"id": 134, "name": "block.snow.step"}, {"id": 135, "name": "block.stone.break"}, {"id": 136, "name": "block.stone.fall"}, {"id": 137, "name": "block.stone.hit"}, {"id": 138, "name": "block.stone.place"}, {"id": 139, "name": "block.stone.step"}, {"id": 140, "name": "block.stone_button.click_off"}, {"id": 141, "name": "block.stone_button.click_on"}, {"id": 142, "name": "block.stone_pressure_plate.click_off"}, {"id": 143, "name": "block.stone_pressure_plate.click_on"}, {"id": 144, "name": "block.tripwire.attach"}, {"id": 145, "name": "block.tripwire.click_off"}, {"id": 146, "name": "block.tripwire.click_on"}, {"id": 147, "name": "block.tripwire.detach"}, {"id": 148, "name": "block.water.ambient"}, {"id": 149, "name": "block.lily_pad.place"}, {"id": 150, "name": "block.wood.break"}, {"id": 151, "name": "block.wood.fall"}, {"id": 152, "name": "block.wood.hit"}, {"id": 153, "name": "block.wood.place"}, {"id": 154, "name": "block.wood.step"}, {"id": 155, "name": "block.wooden_button.click_off"}, {"id": 156, "name": "block.wooden_button.click_on"}, {"id": 157, "name": "block.wooden_pressure_plate.click_off"}, {"id": 158, "name": "block.wooden_pressure_plate.click_on"}, {"id": 159, "name": "block.wooden_door.close"}, {"id": 160, "name": "block.wooden_door.open"}, {"id": 161, "name": "block.wooden_trapdoor.close"}, {"id": 162, "name": "block.wooden_trapdoor.open"}, {"id": 163, "name": "enchant.thorns.hit"}, {"id": 164, "name": "entity.armor_stand.break"}, {"id": 165, "name": "entity.armor_stand.fall"}, {"id": 166, "name": "entity.armor_stand.hit"}, {"id": 167, "name": "entity.armor_stand.place"}, {"id": 168, "name": "entity.arrow.hit"}, {"id": 169, "name": "entity.arrow.hit_player"}, {"id": 170, "name": "entity.arrow.shoot"}, {"id": 171, "name": "entity.bat.ambient"}, {"id": 172, "name": "entity.bat.death"}, {"id": 173, "name": "entity.bat.hurt"}, {"id": 174, "name": "entity.bat.loop"}, {"id": 175, "name": "entity.bat.takeoff"}, {"id": 176, "name": "entity.blaze.ambient"}, {"id": 177, "name": "entity.blaze.burn"}, {"id": 178, "name": "entity.blaze.death"}, {"id": 179, "name": "entity.blaze.hurt"}, {"id": 180, "name": "entity.blaze.shoot"}, {"id": 181, "name": "entity.boat.paddle_land"}, {"id": 182, "name": "entity.boat.paddle_water"}, {"id": 183, "name": "entity.fishing_bobber.retrieve"}, {"id": 184, "name": "entity.fishing_bobber.splash"}, {"id": 185, "name": "entity.fishing_bobber.throw"}, {"id": 186, "name": "entity.cat.ambient"}, {"id": 187, "name": "entity.cat.death"}, {"id": 188, "name": "entity.cat.hiss"}, {"id": 189, "name": "entity.cat.hurt"}, {"id": 190, "name": "entity.cat.purr"}, {"id": 191, "name": "entity.cat.purreow"}, {"id": 192, "name": "entity.chicken.ambient"}, {"id": 193, "name": "entity.chicken.death"}, {"id": 194, "name": "entity.chicken.egg"}, {"id": 195, "name": "entity.chicken.hurt"}, {"id": 196, "name": "entity.chicken.step"}, {"id": 197, "name": "entity.cod.ambient"}, {"id": 198, "name": "entity.cod.death"}, {"id": 199, "name": "entity.cod.flop"}, {"id": 200, "name": "entity.cod.hurt"}, {"id": 201, "name": "entity.cow.ambient"}, {"id": 202, "name": "entity.cow.death"}, {"id": 203, "name": "entity.cow.hurt"}, {"id": 204, "name": "entity.cow.milk"}, {"id": 205, "name": "entity.cow.step"}, {"id": 206, "name": "entity.creeper.death"}, {"id": 207, "name": "entity.creeper.hurt"}, {"id": 208, "name": "entity.creeper.primed"}, {"id": 209, "name": "entity.dolphin.ambient"}, {"id": 210, "name": "entity.dolphin.ambient_water"}, {"id": 211, "name": "entity.dolphin.attack"}, {"id": 212, "name": "entity.dolphin.death"}, {"id": 213, "name": "entity.dolphin.eat"}, {"id": 214, "name": "entity.dolphin.hurt"}, {"id": 215, "name": "entity.dolphin.jump"}, {"id": 216, "name": "entity.dolphin.play"}, {"id": 217, "name": "entity.dolphin.splash"}, {"id": 218, "name": "entity.dolphin.swim"}, {"id": 219, "name": "entity.donkey.ambient"}, {"id": 220, "name": "entity.donkey.angry"}, {"id": 221, "name": "entity.donkey.chest"}, {"id": 222, "name": "entity.donkey.death"}, {"id": 223, "name": "entity.donkey.hurt"}, {"id": 224, "name": "entity.drowned.ambient"}, {"id": 225, "name": "entity.drowned.ambient_water"}, {"id": 226, "name": "entity.drowned.death"}, {"id": 227, "name": "entity.drowned.death_water"}, {"id": 228, "name": "entity.drowned.hurt"}, {"id": 229, "name": "entity.drowned.hurt_water"}, {"id": 230, "name": "entity.drowned.shoot"}, {"id": 231, "name": "entity.drowned.step"}, {"id": 232, "name": "entity.drowned.swim"}, {"id": 233, "name": "entity.egg.throw"}, {"id": 234, "name": "entity.elder_guardian.ambient"}, {"id": 235, "name": "entity.elder_guardian.ambient_land"}, {"id": 236, "name": "entity.elder_guardian.curse"}, {"id": 237, "name": "entity.elder_guardian.death"}, {"id": 238, "name": "entity.elder_guardian.death_land"}, {"id": 239, "name": "entity.elder_guardian.flop"}, {"id": 240, "name": "entity.elder_guardian.hurt"}, {"id": 241, "name": "entity.elder_guardian.hurt_land"}, {"id": 242, "name": "entity.ender_dragon.ambient"}, {"id": 243, "name": "entity.ender_dragon.death"}, {"id": 244, "name": "entity.ender_dragon.flap"}, {"id": 245, "name": "entity.ender_dragon.growl"}, {"id": 246, "name": "entity.ender_dragon.hurt"}, {"id": 247, "name": "entity.ender_dragon.shoot"}, {"id": 248, "name": "entity.dragon_fireball.explode"}, {"id": 249, "name": "entity.ender_eye.death"}, {"id": 250, "name": "entity.ender_eye.launch"}, {"id": 251, "name": "entity.enderman.ambient"}, {"id": 252, "name": "entity.enderman.death"}, {"id": 253, "name": "entity.enderman.hurt"}, {"id": 254, "name": "entity.enderman.scream"}, {"id": 255, "name": "entity.enderman.stare"}, {"id": 256, "name": "entity.enderman.teleport"}, {"id": 257, "name": "entity.endermite.ambient"}, {"id": 258, "name": "entity.endermite.death"}, {"id": 259, "name": "entity.endermite.hurt"}, {"id": 260, "name": "entity.endermite.step"}, {"id": 261, "name": "entity.ender_pearl.throw"}, {"id": 262, "name": "entity.evoker.ambient"}, {"id": 263, "name": "entity.evoker.cast_spell"}, {"id": 264, "name": "entity.evoker.death"}, {"id": 265, "name": "entity.evoker.hurt"}, {"id": 266, "name": "entity.evoker.prepare_attack"}, {"id": 267, "name": "entity.evoker.prepare_summon"}, {"id": 268, "name": "entity.evoker.prepare_wololo"}, {"id": 269, "name": "entity.evoker_fangs.attack"}, {"id": 270, "name": "entity.experience_bottle.throw"}, {"id": 271, "name": "entity.experience_orb.pickup"}, {"id": 272, "name": "entity.firework_rocket.blast"}, {"id": 273, "name": "entity.firework_rocket.blast_far"}, {"id": 274, "name": "entity.firework_rocket.large_blast"}, {"id": 275, "name": "entity.firework_rocket.large_blast_far"}, {"id": 276, "name": "entity.firework_rocket.launch"}, {"id": 277, "name": "entity.firework_rocket.shoot"}, {"id": 278, "name": "entity.firework_rocket.twinkle"}, {"id": 279, "name": "entity.firework_rocket.twinkle_far"}, {"id": 280, "name": "entity.fish.swim"}, {"id": 281, "name": "entity.generic.big_fall"}, {"id": 282, "name": "entity.generic.burn"}, {"id": 283, "name": "entity.generic.death"}, {"id": 284, "name": "entity.generic.drink"}, {"id": 285, "name": "entity.generic.eat"}, {"id": 286, "name": "entity.generic.explode"}, {"id": 287, "name": "entity.generic.extinguish_fire"}, {"id": 288, "name": "entity.generic.hurt"}, {"id": 289, "name": "entity.generic.small_fall"}, {"id": 290, "name": "entity.generic.splash"}, {"id": 291, "name": "entity.generic.swim"}, {"id": 292, "name": "entity.ghast.ambient"}, {"id": 293, "name": "entity.ghast.death"}, {"id": 294, "name": "entity.ghast.hurt"}, {"id": 295, "name": "entity.ghast.scream"}, {"id": 296, "name": "entity.ghast.shoot"}, {"id": 297, "name": "entity.ghast.warn"}, {"id": 298, "name": "entity.guardian.ambient"}, {"id": 299, "name": "entity.guardian.ambient_land"}, {"id": 300, "name": "entity.guardian.attack"}, {"id": 301, "name": "entity.guardian.death"}, {"id": 302, "name": "entity.guardian.death_land"}, {"id": 303, "name": "entity.guardian.flop"}, {"id": 304, "name": "entity.guardian.hurt"}, {"id": 305, "name": "entity.guardian.hurt_land"}, {"id": 306, "name": "entity.horse.ambient"}, {"id": 307, "name": "entity.horse.angry"}, {"id": 308, "name": "entity.horse.armor"}, {"id": 309, "name": "entity.horse.breathe"}, {"id": 310, "name": "entity.horse.death"}, {"id": 311, "name": "entity.horse.eat"}, {"id": 312, "name": "entity.horse.gallop"}, {"id": 313, "name": "entity.horse.hurt"}, {"id": 314, "name": "entity.horse.jump"}, {"id": 315, "name": "entity.horse.land"}, {"id": 316, "name": "entity.horse.saddle"}, {"id": 317, "name": "entity.horse.step"}, {"id": 318, "name": "entity.horse.step_wood"}, {"id": 319, "name": "entity.hostile.big_fall"}, {"id": 320, "name": "entity.hostile.death"}, {"id": 321, "name": "entity.hostile.hurt"}, {"id": 322, "name": "entity.hostile.small_fall"}, {"id": 323, "name": "entity.hostile.splash"}, {"id": 324, "name": "entity.hostile.swim"}, {"id": 325, "name": "entity.husk.ambient"}, {"id": 326, "name": "entity.husk.converted_to_zombie"}, {"id": 327, "name": "entity.husk.death"}, {"id": 328, "name": "entity.husk.hurt"}, {"id": 329, "name": "entity.husk.step"}, {"id": 330, "name": "entity.illusioner.ambient"}, {"id": 331, "name": "entity.illusioner.cast_spell"}, {"id": 332, "name": "entity.illusioner.death"}, {"id": 333, "name": "entity.illusioner.hurt"}, {"id": 334, "name": "entity.illusioner.mirror_move"}, {"id": 335, "name": "entity.illusioner.prepare_blindness"}, {"id": 336, "name": "entity.illusioner.prepare_mirror"}, {"id": 337, "name": "entity.iron_golem.attack"}, {"id": 338, "name": "entity.iron_golem.death"}, {"id": 339, "name": "entity.iron_golem.hurt"}, {"id": 340, "name": "entity.iron_golem.step"}, {"id": 341, "name": "entity.item.break"}, {"id": 342, "name": "entity.item.pickup"}, {"id": 343, "name": "entity.item_frame.add_item"}, {"id": 344, "name": "entity.item_frame.break"}, {"id": 345, "name": "entity.item_frame.place"}, {"id": 346, "name": "entity.item_frame.remove_item"}, {"id": 347, "name": "entity.item_frame.rotate_item"}, {"id": 348, "name": "entity.leash_knot.break"}, {"id": 349, "name": "entity.leash_knot.place"}, {"id": 350, "name": "entity.lightning_bolt.impact"}, {"id": 351, "name": "entity.lightning_bolt.thunder"}, {"id": 352, "name": "entity.lingering_potion.throw"}, {"id": 353, "name": "entity.llama.ambient"}, {"id": 354, "name": "entity.llama.angry"}, {"id": 355, "name": "entity.llama.chest"}, {"id": 356, "name": "entity.llama.death"}, {"id": 357, "name": "entity.llama.eat"}, {"id": 358, "name": "entity.llama.hurt"}, {"id": 359, "name": "entity.llama.spit"}, {"id": 360, "name": "entity.llama.step"}, {"id": 361, "name": "entity.llama.swag"}, {"id": 362, "name": "entity.magma_cube.death"}, {"id": 363, "name": "entity.magma_cube.hurt"}, {"id": 364, "name": "entity.magma_cube.jump"}, {"id": 365, "name": "entity.magma_cube.squish"}, {"id": 366, "name": "entity.minecart.inside"}, {"id": 367, "name": "entity.minecart.riding"}, {"id": 368, "name": "entity.mooshroom.shear"}, {"id": 369, "name": "entity.mule.ambient"}, {"id": 370, "name": "entity.mule.chest"}, {"id": 371, "name": "entity.mule.death"}, {"id": 372, "name": "entity.mule.hurt"}, {"id": 373, "name": "entity.painting.break"}, {"id": 374, "name": "entity.painting.place"}, {"id": 375, "name": "entity.parrot.ambient"}, {"id": 376, "name": "entity.parrot.death"}, {"id": 377, "name": "entity.parrot.eat"}, {"id": 378, "name": "entity.parrot.fly"}, {"id": 379, "name": "entity.parrot.hurt"}, {"id": 380, "name": "entity.parrot.imitate.blaze"}, {"id": 381, "name": "entity.parrot.imitate.creeper"}, {"id": 382, "name": "entity.parrot.imitate.drowned"}, {"id": 383, "name": "entity.parrot.imitate.elder_guardian"}, {"id": 384, "name": "entity.parrot.imitate.ender_dragon"}, {"id": 385, "name": "entity.parrot.imitate.enderman"}, {"id": 386, "name": "entity.parrot.imitate.endermite"}, {"id": 387, "name": "entity.parrot.imitate.evoker"}, {"id": 388, "name": "entity.parrot.imitate.ghast"}, {"id": 389, "name": "entity.parrot.imitate.husk"}, {"id": 390, "name": "entity.parrot.imitate.illusioner"}, {"id": 391, "name": "entity.parrot.imitate.magma_cube"}, {"id": 392, "name": "entity.parrot.imitate.phantom"}, {"id": 393, "name": "entity.parrot.imitate.polar_bear"}, {"id": 394, "name": "entity.parrot.imitate.shulker"}, {"id": 395, "name": "entity.parrot.imitate.silverfish"}, {"id": 396, "name": "entity.parrot.imitate.skeleton"}, {"id": 397, "name": "entity.parrot.imitate.slime"}, {"id": 398, "name": "entity.parrot.imitate.spider"}, {"id": 399, "name": "entity.parrot.imitate.stray"}, {"id": 400, "name": "entity.parrot.imitate.vex"}, {"id": 401, "name": "entity.parrot.imitate.vindicator"}, {"id": 402, "name": "entity.parrot.imitate.witch"}, {"id": 403, "name": "entity.parrot.imitate.wither"}, {"id": 404, "name": "entity.parrot.imitate.wither_skeleton"}, {"id": 405, "name": "entity.parrot.imitate.wolf"}, {"id": 406, "name": "entity.parrot.imitate.zombie"}, {"id": 407, "name": "entity.parrot.imitate.zombie_pigman"}, {"id": 408, "name": "entity.parrot.imitate.zombie_villager"}, {"id": 409, "name": "entity.parrot.step"}, {"id": 410, "name": "entity.phantom.ambient"}, {"id": 411, "name": "entity.phantom.bite"}, {"id": 412, "name": "entity.phantom.death"}, {"id": 413, "name": "entity.phantom.flap"}, {"id": 414, "name": "entity.phantom.hurt"}, {"id": 415, "name": "entity.phantom.swoop"}, {"id": 416, "name": "entity.pig.ambient"}, {"id": 417, "name": "entity.pig.death"}, {"id": 418, "name": "entity.pig.hurt"}, {"id": 419, "name": "entity.pig.saddle"}, {"id": 420, "name": "entity.pig.step"}, {"id": 421, "name": "entity.player.attack.crit"}, {"id": 422, "name": "entity.player.attack.knockback"}, {"id": 423, "name": "entity.player.attack.nodamage"}, {"id": 424, "name": "entity.player.attack.strong"}, {"id": 425, "name": "entity.player.attack.sweep"}, {"id": 426, "name": "entity.player.attack.weak"}, {"id": 427, "name": "entity.player.big_fall"}, {"id": 428, "name": "entity.player.breath"}, {"id": 429, "name": "entity.player.burp"}, {"id": 430, "name": "entity.player.death"}, {"id": 431, "name": "entity.player.hurt"}, {"id": 432, "name": "entity.player.hurt_drown"}, {"id": 433, "name": "entity.player.hurt_on_fire"}, {"id": 434, "name": "entity.player.levelup"}, {"id": 435, "name": "entity.player.small_fall"}, {"id": 436, "name": "entity.player.splash"}, {"id": 437, "name": "entity.player.splash.high_speed"}, {"id": 438, "name": "entity.player.swim"}, {"id": 439, "name": "entity.polar_bear.ambient"}, {"id": 440, "name": "entity.polar_bear.ambient_baby"}, {"id": 441, "name": "entity.polar_bear.death"}, {"id": 442, "name": "entity.polar_bear.hurt"}, {"id": 443, "name": "entity.polar_bear.step"}, {"id": 444, "name": "entity.polar_bear.warning"}, {"id": 445, "name": "entity.puffer_fish.ambient"}, {"id": 446, "name": "entity.puffer_fish.blow_out"}, {"id": 447, "name": "entity.puffer_fish.blow_up"}, {"id": 448, "name": "entity.puffer_fish.death"}, {"id": 449, "name": "entity.puffer_fish.flop"}, {"id": 450, "name": "entity.puffer_fish.hurt"}, {"id": 451, "name": "entity.puffer_fish.sting"}, {"id": 452, "name": "entity.rabbit.ambient"}, {"id": 453, "name": "entity.rabbit.attack"}, {"id": 454, "name": "entity.rabbit.death"}, {"id": 455, "name": "entity.rabbit.hurt"}, {"id": 456, "name": "entity.rabbit.jump"}, {"id": 457, "name": "entity.salmon.ambient"}, {"id": 458, "name": "entity.salmon.death"}, {"id": 459, "name": "entity.salmon.flop"}, {"id": 460, "name": "entity.salmon.hurt"}, {"id": 461, "name": "entity.sheep.ambient"}, {"id": 462, "name": "entity.sheep.death"}, {"id": 463, "name": "entity.sheep.hurt"}, {"id": 464, "name": "entity.sheep.shear"}, {"id": 465, "name": "entity.sheep.step"}, {"id": 466, "name": "entity.shulker.ambient"}, {"id": 467, "name": "entity.shulker.close"}, {"id": 468, "name": "entity.shulker.death"}, {"id": 469, "name": "entity.shulker.hurt"}, {"id": 470, "name": "entity.shulker.hurt_closed"}, {"id": 471, "name": "entity.shulker.open"}, {"id": 472, "name": "entity.shulker.shoot"}, {"id": 473, "name": "entity.shulker.teleport"}, {"id": 474, "name": "entity.shulker_bullet.hit"}, {"id": 475, "name": "entity.shulker_bullet.hurt"}, {"id": 476, "name": "entity.silverfish.ambient"}, {"id": 477, "name": "entity.silverfish.death"}, {"id": 478, "name": "entity.silverfish.hurt"}, {"id": 479, "name": "entity.silverfish.step"}, {"id": 480, "name": "entity.skeleton.ambient"}, {"id": 481, "name": "entity.skeleton.death"}, {"id": 482, "name": "entity.skeleton.hurt"}, {"id": 483, "name": "entity.skeleton.shoot"}, {"id": 484, "name": "entity.skeleton.step"}, {"id": 485, "name": "entity.skeleton_horse.ambient"}, {"id": 486, "name": "entity.skeleton_horse.death"}, {"id": 487, "name": "entity.skeleton_horse.hurt"}, {"id": 488, "name": "entity.skeleton_horse.swim"}, {"id": 489, "name": "entity.skeleton_horse.ambient_water"}, {"id": 490, "name": "entity.skeleton_horse.gallop_water"}, {"id": 491, "name": "entity.skeleton_horse.jump_water"}, {"id": 492, "name": "entity.skeleton_horse.step_water"}, {"id": 493, "name": "entity.slime.attack"}, {"id": 494, "name": "entity.slime.death"}, {"id": 495, "name": "entity.slime.hurt"}, {"id": 496, "name": "entity.slime.jump"}, {"id": 497, "name": "entity.slime.squish"}, {"id": 498, "name": "entity.magma_cube.death_small"}, {"id": 499, "name": "entity.magma_cube.hurt_small"}, {"id": 500, "name": "entity.magma_cube.squish_small"}, {"id": 501, "name": "entity.slime.death_small"}, {"id": 502, "name": "entity.slime.hurt_small"}, {"id": 503, "name": "entity.slime.jump_small"}, {"id": 504, "name": "entity.slime.squish_small"}, {"id": 505, "name": "entity.snow_golem.ambient"}, {"id": 506, "name": "entity.snow_golem.death"}, {"id": 507, "name": "entity.snow_golem.hurt"}, {"id": 508, "name": "entity.snow_golem.shoot"}, {"id": 509, "name": "entity.snowball.throw"}, {"id": 510, "name": "entity.spider.ambient"}, {"id": 511, "name": "entity.spider.death"}, {"id": 512, "name": "entity.spider.hurt"}, {"id": 513, "name": "entity.spider.step"}, {"id": 514, "name": "entity.splash_potion.break"}, {"id": 515, "name": "entity.splash_potion.throw"}, {"id": 516, "name": "entity.squid.ambient"}, {"id": 517, "name": "entity.squid.death"}, {"id": 518, "name": "entity.squid.hurt"}, {"id": 519, "name": "entity.squid.squirt"}, {"id": 520, "name": "entity.stray.ambient"}, {"id": 521, "name": "entity.stray.death"}, {"id": 522, "name": "entity.stray.hurt"}, {"id": 523, "name": "entity.stray.step"}, {"id": 524, "name": "entity.tnt.primed"}, {"id": 525, "name": "entity.tropical_fish.ambient"}, {"id": 526, "name": "entity.tropical_fish.death"}, {"id": 527, "name": "entity.tropical_fish.flop"}, {"id": 528, "name": "entity.tropical_fish.hurt"}, {"id": 529, "name": "entity.turtle.ambient_land"}, {"id": 530, "name": "entity.turtle.death"}, {"id": 531, "name": "entity.turtle.death_baby"}, {"id": 532, "name": "entity.turtle.egg_break"}, {"id": 533, "name": "entity.turtle.egg_crack"}, {"id": 534, "name": "entity.turtle.egg_hatch"}, {"id": 535, "name": "entity.turtle.hurt"}, {"id": 536, "name": "entity.turtle.hurt_baby"}, {"id": 537, "name": "entity.turtle.lay_egg"}, {"id": 538, "name": "entity.turtle.shamble"}, {"id": 539, "name": "entity.turtle.shamble_baby"}, {"id": 540, "name": "entity.turtle.swim"}, {"id": 541, "name": "entity.vex.ambient"}, {"id": 542, "name": "entity.vex.charge"}, {"id": 543, "name": "entity.vex.death"}, {"id": 544, "name": "entity.vex.hurt"}, {"id": 545, "name": "entity.villager.ambient"}, {"id": 546, "name": "entity.villager.death"}, {"id": 547, "name": "entity.villager.hurt"}, {"id": 548, "name": "entity.villager.no"}, {"id": 549, "name": "entity.villager.trade"}, {"id": 550, "name": "entity.villager.yes"}, {"id": 551, "name": "entity.vindicator.ambient"}, {"id": 552, "name": "entity.vindicator.death"}, {"id": 553, "name": "entity.vindicator.hurt"}, {"id": 554, "name": "entity.witch.ambient"}, {"id": 555, "name": "entity.witch.death"}, {"id": 556, "name": "entity.witch.drink"}, {"id": 557, "name": "entity.witch.hurt"}, {"id": 558, "name": "entity.witch.throw"}, {"id": 559, "name": "entity.wither.ambient"}, {"id": 560, "name": "entity.wither.break_block"}, {"id": 561, "name": "entity.wither.death"}, {"id": 562, "name": "entity.wither.hurt"}, {"id": 563, "name": "entity.wither.shoot"}, {"id": 564, "name": "entity.wither.spawn"}, {"id": 565, "name": "entity.wither_skeleton.ambient"}, {"id": 566, "name": "entity.wither_skeleton.death"}, {"id": 567, "name": "entity.wither_skeleton.hurt"}, {"id": 568, "name": "entity.wither_skeleton.step"}, {"id": 569, "name": "entity.wolf.ambient"}, {"id": 570, "name": "entity.wolf.death"}, {"id": 571, "name": "entity.wolf.growl"}, {"id": 572, "name": "entity.wolf.howl"}, {"id": 573, "name": "entity.wolf.hurt"}, {"id": 574, "name": "entity.wolf.pant"}, {"id": 575, "name": "entity.wolf.shake"}, {"id": 576, "name": "entity.wolf.step"}, {"id": 577, "name": "entity.wolf.whine"}, {"id": 578, "name": "entity.zombie.ambient"}, {"id": 579, "name": "entity.zombie.attack_wooden_door"}, {"id": 580, "name": "entity.zombie.attack_iron_door"}, {"id": 581, "name": "entity.zombie.break_wooden_door"}, {"id": 582, "name": "entity.zombie.converted_to_drowned"}, {"id": 583, "name": "entity.zombie.death"}, {"id": 584, "name": "entity.zombie.destroy_egg"}, {"id": 585, "name": "entity.zombie.hurt"}, {"id": 586, "name": "entity.zombie.infect"}, {"id": 587, "name": "entity.zombie.step"}, {"id": 588, "name": "entity.zombie_horse.ambient"}, {"id": 589, "name": "entity.zombie_horse.death"}, {"id": 590, "name": "entity.zombie_horse.hurt"}, {"id": 591, "name": "entity.zombie_pigman.ambient"}, {"id": 592, "name": "entity.zombie_pigman.angry"}, {"id": 593, "name": "entity.zombie_pigman.death"}, {"id": 594, "name": "entity.zombie_pigman.hurt"}, {"id": 595, "name": "entity.zombie_villager.ambient"}, {"id": 596, "name": "entity.zombie_villager.converted"}, {"id": 597, "name": "entity.zombie_villager.cure"}, {"id": 598, "name": "entity.zombie_villager.death"}, {"id": 599, "name": "entity.zombie_villager.hurt"}, {"id": 600, "name": "entity.zombie_villager.step"}, {"id": 601, "name": "item.armor.equip_chain"}, {"id": 602, "name": "item.armor.equip_diamond"}, {"id": 603, "name": "item.armor.equip_elytra"}, {"id": 604, "name": "item.armor.equip_generic"}, {"id": 605, "name": "item.armor.equip_gold"}, {"id": 606, "name": "item.armor.equip_iron"}, {"id": 607, "name": "item.armor.equip_leather"}, {"id": 608, "name": "item.armor.equip_turtle"}, {"id": 609, "name": "item.axe.strip"}, {"id": 610, "name": "item.bottle.empty"}, {"id": 611, "name": "item.bottle.fill"}, {"id": 612, "name": "item.bottle.fill_dragonbreath"}, {"id": 613, "name": "item.bucket.empty"}, {"id": 614, "name": "item.bucket.empty_fish"}, {"id": 615, "name": "item.bucket.empty_lava"}, {"id": 616, "name": "item.bucket.fill"}, {"id": 617, "name": "item.bucket.fill_fish"}, {"id": 618, "name": "item.bucket.fill_lava"}, {"id": 619, "name": "item.chorus_fruit.teleport"}, {"id": 620, "name": "item.elytra.flying"}, {"id": 621, "name": "item.firecharge.use"}, {"id": 622, "name": "item.flintandsteel.use"}, {"id": 623, "name": "item.hoe.till"}, {"id": 624, "name": "item.shield.block"}, {"id": 625, "name": "item.shield.break"}, {"id": 626, "name": "item.shovel.flatten"}, {"id": 627, "name": "item.totem.use"}, {"id": 628, "name": "item.trident.hit"}, {"id": 629, "name": "item.trident.hit_ground"}, {"id": 630, "name": "item.trident.return"}, {"id": 631, "name": "item.trident.riptide_1"}, {"id": 632, "name": "item.trident.riptide_2"}, {"id": 633, "name": "item.trident.riptide_3"}, {"id": 634, "name": "item.trident.throw"}, {"id": 635, "name": "item.trident.thunder"}, {"id": 636, "name": "music.creative"}, {"id": 637, "name": "music.credits"}, {"id": 638, "name": "music.dragon"}, {"id": 639, "name": "music.end"}, {"id": 640, "name": "music.game"}, {"id": 641, "name": "music.menu"}, {"id": 642, "name": "music.nether"}, {"id": 643, "name": "music.under_water"}, {"id": 644, "name": "music_disc.11"}, {"id": 645, "name": "music_disc.13"}, {"id": 646, "name": "music_disc.blocks"}, {"id": 647, "name": "music_disc.cat"}, {"id": 648, "name": "music_disc.chirp"}, {"id": 649, "name": "music_disc.far"}, {"id": 650, "name": "music_disc.mall"}, {"id": 651, "name": "music_disc.mellohi"}, {"id": 652, "name": "music_disc.stal"}, {"id": 653, "name": "music_disc.strad"}, {"id": 654, "name": "music_disc.wait"}, {"id": 655, "name": "music_disc.ward"}, {"id": 656, "name": "ui.button.click"}, {"id": 657, "name": "ui.toast.challenge_complete"}, {"id": 658, "name": "ui.toast.in"}, {"id": 659, "name": "ui.toast.out"}, {"id": 660, "name": "weather.rain"}, {"id": 661, "name": "weather.rain.above"}]