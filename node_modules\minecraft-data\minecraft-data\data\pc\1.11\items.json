[{"id": 1, "displayName": "Stone", "name": "stone", "stackSize": 64}, {"id": 2, "displayName": "Grass Block", "name": "grass", "stackSize": 64}, {"id": 3, "displayName": "Dirt", "name": "dirt", "stackSize": 64}, {"id": 4, "displayName": "Cobblestone", "name": "cobblestone", "stackSize": 64}, {"id": 5, "displayName": "Wooden Planks", "name": "planks", "stackSize": 64}, {"id": 6, "displayName": "Sapling", "name": "sapling", "stackSize": 64}, {"id": 7, "displayName": "Bedrock", "name": "bedrock", "stackSize": 64}, {"id": 12, "displayName": "Sand", "name": "sand", "stackSize": 64}, {"id": 13, "displayName": "<PERSON>l", "name": "gravel", "stackSize": 64}, {"id": 14, "displayName": "Gold Ore", "name": "gold_ore", "stackSize": 64}, {"id": 15, "displayName": "Iron Ore", "name": "iron_ore", "stackSize": 64}, {"id": 16, "displayName": "Coal Ore", "name": "coal_ore", "stackSize": 64}, {"id": 17, "displayName": "<PERSON>", "name": "log", "stackSize": 64}, {"id": 18, "displayName": "Leaves", "name": "leaves", "stackSize": 64}, {"id": 19, "displayName": "Sponge", "name": "sponge", "stackSize": 64}, {"id": 20, "displayName": "Glass", "name": "glass", "stackSize": 64}, {"id": 21, "displayName": "Lapis <PERSON> Ore", "name": "lapis_ore", "stackSize": 64}, {"id": 22, "displayName": "Lapis <PERSON>", "name": "lapis_block", "stackSize": 64}, {"id": 23, "displayName": "Dispenser", "name": "dispenser", "stackSize": 64}, {"id": 24, "displayName": "Sandstone", "name": "sandstone", "stackSize": 64}, {"id": 25, "displayName": "Note Block", "name": "noteblock", "stackSize": 64}, {"id": 27, "displayName": "Powered Rail", "name": "golden_rail", "stackSize": 64}, {"id": 28, "displayName": "Detector Rail", "name": "detector_rail", "stackSize": 64}, {"id": 29, "displayName": "<PERSON><PERSON>", "name": "sticky_piston", "stackSize": 64}, {"id": 30, "displayName": "Cobweb", "name": "web", "stackSize": 64}, {"id": 31, "displayName": "Grass", "name": "tallgrass", "stackSize": 64}, {"id": 32, "displayName": "Dead Bush", "name": "deadbush", "stackSize": 64}, {"id": 33, "displayName": "<PERSON><PERSON>", "name": "piston", "stackSize": 64}, {"id": 35, "displayName": "Wool", "name": "wool", "stackSize": 64}, {"id": 37, "displayName": "Dandelion", "name": "yellow_flower", "stackSize": 64}, {"id": 38, "displayName": "<PERSON><PERSON>", "name": "red_flower", "stackSize": 64}, {"id": 39, "displayName": "Brown Mushroom", "name": "brown_mushroom", "stackSize": 64}, {"id": 40, "displayName": "Red Mushroom", "name": "red_mushroom", "stackSize": 64}, {"id": 41, "displayName": "Block of Gold", "name": "gold_block", "stackSize": 64}, {"id": 42, "displayName": "Block of Iron", "name": "iron_block", "stackSize": 64}, {"id": 44, "displayName": "<PERSON> Slab", "name": "stone_slab", "stackSize": 64}, {"id": 45, "displayName": "Brick", "name": "brick_block", "stackSize": 64}, {"id": 46, "displayName": "TNT", "name": "tnt", "stackSize": 64}, {"id": 47, "displayName": "Bookshelf", "name": "bookshelf", "stackSize": 64}, {"id": 48, "displayName": "<PERSON>", "name": "mossy_cobblestone", "stackSize": 64}, {"id": 49, "displayName": "Obsidian", "name": "obsidian", "stackSize": 64}, {"id": 50, "displayName": "<PERSON>ch", "name": "torch", "stackSize": 64}, {"id": 52, "displayName": "Monster Spawner", "name": "mob_spawner", "stackSize": 64}, {"id": 53, "displayName": "Oak Wood Stairs", "name": "oak_stairs", "stackSize": 64}, {"id": 54, "displayName": "Chest", "name": "chest", "stackSize": 64}, {"id": 56, "displayName": "Diamond Ore", "name": "diamond_ore", "stackSize": 64}, {"id": 57, "displayName": "Block of Diamond", "name": "diamond_block", "stackSize": 64}, {"id": 58, "displayName": "Crafting Table", "name": "crafting_table", "stackSize": 64}, {"id": 60, "displayName": "Farmland", "name": "farmland", "stackSize": 64}, {"id": 61, "displayName": "Furnace", "name": "furnace", "stackSize": 64}, {"id": 65, "displayName": "Ladder", "name": "ladder", "stackSize": 64}, {"id": 66, "displayName": "Rail", "name": "rail", "stackSize": 64}, {"id": 67, "displayName": "Cobblestone Stairs", "name": "stone_stairs", "stackSize": 64}, {"id": 69, "displayName": "Lever", "name": "lever", "stackSize": 64}, {"id": 70, "displayName": "Stone Pressure Plate", "name": "stone_pressure_plate", "stackSize": 64}, {"id": 72, "displayName": "Wooden Pressure Plate", "name": "wooden_pressure_plate", "stackSize": 64}, {"id": 73, "displayName": "Redstone Ore", "name": "redstone_ore", "stackSize": 64}, {"id": 76, "displayName": "Redstone Torch", "name": "redstone_torch", "stackSize": 64}, {"id": 77, "displayName": "<PERSON>", "name": "stone_button", "stackSize": 64}, {"id": 78, "displayName": "Snow", "name": "snow_layer", "stackSize": 64}, {"id": 79, "displayName": "Ice", "name": "ice", "stackSize": 64}, {"id": 80, "displayName": "Snow", "name": "snow", "stackSize": 64}, {"id": 81, "displayName": "Cactus", "name": "cactus", "stackSize": 64}, {"id": 82, "displayName": "<PERSON>", "name": "clay", "stackSize": 64}, {"id": 84, "displayName": "Jukebox", "name": "jukebox", "stackSize": 64}, {"id": 85, "displayName": "Oak Fence", "name": "fence", "stackSize": 64}, {"id": 86, "displayName": "<PERSON><PERSON><PERSON>", "name": "pumpkin", "stackSize": 64}, {"id": 87, "displayName": "Netherrack", "name": "netherrack", "stackSize": 64}, {"id": 88, "displayName": "Soul Sand", "name": "soul_sand", "stackSize": 64}, {"id": 89, "displayName": "Glowstone", "name": "glowstone", "stackSize": 64}, {"id": 91, "displayName": "<PERSON>'<PERSON>", "name": "lit_pumpkin", "stackSize": 64}, {"id": 95, "displayName": "Stained Glass", "name": "stained_glass", "stackSize": 64}, {"id": 96, "displayName": "<PERSON><PERSON>", "name": "trapdoor", "stackSize": 64}, {"id": 97, "displayName": "Monster Egg", "name": "monster_egg", "stackSize": 64}, {"id": 98, "displayName": "Stone Bricks", "name": "stonebrick", "stackSize": 64}, {"id": 99, "displayName": "Brown Mushroom Block", "name": "brown_mushroom_block", "stackSize": 64}, {"id": 100, "displayName": "Red Mushroom Block", "name": "red_mushroom_block", "stackSize": 64}, {"id": 101, "displayName": "Iron Bars", "name": "iron_bars", "stackSize": 64}, {"id": 102, "displayName": "Glass Pane", "name": "glass_pane", "stackSize": 64}, {"id": 103, "displayName": "Melon", "name": "melon_block", "stackSize": 64}, {"id": 106, "displayName": "Vines", "name": "vine", "stackSize": 64}, {"id": 107, "displayName": "Oak Fence Gate", "name": "fence_gate", "stackSize": 64}, {"id": 108, "displayName": "Brick Stairs", "name": "brick_stairs", "stackSize": 64}, {"id": 109, "displayName": "Stone Brick Stairs", "name": "stone_brick_stairs", "stackSize": 64}, {"id": 110, "displayName": "Mycelium", "name": "mycelium", "stackSize": 64}, {"id": 111, "displayName": "<PERSON>", "name": "waterlily", "stackSize": 64}, {"id": 112, "displayName": "Nether Brick", "name": "nether_brick", "stackSize": 64}, {"id": 113, "displayName": "Nether Brick Fence", "name": "nether_brick_fence", "stackSize": 64}, {"id": 114, "displayName": "Nether Brick Stairs", "name": "nether_brick_stairs", "stackSize": 64}, {"id": 116, "displayName": "Enchantment Table", "name": "enchanting_table", "stackSize": 64}, {"id": 120, "displayName": "End Portal Frame", "name": "end_portal_frame", "stackSize": 64}, {"id": 121, "displayName": "End Stone", "name": "end_stone", "stackSize": 64}, {"id": 122, "displayName": "Dragon Egg", "name": "dragon_egg", "stackSize": 64}, {"id": 123, "displayName": "Redstone Lamp", "name": "redstone_lamp", "stackSize": 64}, {"id": 126, "displayName": "<PERSON>b", "name": "wooden_slab", "stackSize": 64}, {"id": 128, "displayName": "Sandstone Stairs", "name": "sandstone_stairs", "stackSize": 64}, {"id": 129, "displayName": "Emerald Ore", "name": "emerald_ore", "stackSize": 64}, {"id": 130, "displayName": "<PERSON><PERSON> Chest", "name": "ender_chest", "stackSize": 64}, {"id": 131, "displayName": "Tripwire Hook", "name": "tripwire_hook", "stackSize": 64}, {"id": 133, "displayName": "Block of Emerald", "name": "emerald_block", "stackSize": 64}, {"id": 134, "displayName": "Spruce Wood Stairs", "name": "spruce_stairs", "stackSize": 64}, {"id": 135, "displayName": "Birch Wood Stairs", "name": "birch_stairs", "stackSize": 64}, {"id": 136, "displayName": "Jungle Wood Stairs", "name": "jungle_stairs", "stackSize": 64}, {"id": 137, "displayName": "Command Block", "name": "command_block", "stackSize": 64}, {"id": 138, "displayName": "Beacon", "name": "beacon", "stackSize": 64}, {"id": 139, "displayName": "Cobblestone Wall", "name": "cobblestone_wall", "stackSize": 64}, {"id": 143, "displayName": "<PERSON><PERSON>", "name": "wooden_button", "stackSize": 64}, {"id": 145, "displayName": "An<PERSON>", "name": "anvil", "stackSize": 64}, {"id": 146, "displayName": "Trapped Chest", "name": "trapped_chest", "stackSize": 64}, {"id": 147, "displayName": "Weighted Pressure Plate (Light)", "name": "light_weighted_pressure_plate", "stackSize": 64}, {"id": 148, "displayName": "Weighted Pressure Plate (Heavy)", "name": "heavy_weighted_pressure_plate", "stackSize": 64}, {"id": 151, "displayName": "Daylight Detector", "name": "daylight_detector", "stackSize": 64}, {"id": 152, "displayName": "Block of Redstone", "name": "redstone_block", "stackSize": 64}, {"id": 153, "displayName": "<PERSON><PERSON>", "name": "quartz_ore", "stackSize": 64}, {"id": 154, "displayName": "<PERSON>", "name": "hopper", "stackSize": 64}, {"id": 155, "displayName": "Block of Quartz", "name": "quartz_block", "stackSize": 64}, {"id": 156, "displayName": "Quartz Stairs", "name": "quartz_stairs", "stackSize": 64}, {"id": 157, "displayName": "Activator Rail", "name": "activator_rail", "stackSize": 64}, {"id": 158, "displayName": "Dropper", "name": "dropper", "stackSize": 64}, {"id": 159, "displayName": "Stained Hardened Clay", "name": "stained_hardened_clay", "stackSize": 64}, {"id": 160, "displayName": "Stained Glass Pane", "name": "stained_glass_pane", "stackSize": 64}, {"id": 161, "displayName": "Leaves", "name": "leaves2", "stackSize": 64}, {"id": 162, "displayName": "<PERSON>", "name": "log2", "stackSize": 64}, {"id": 163, "displayName": "Acacia Wood Stairs", "name": "acacia_stairs", "stackSize": 64}, {"id": 164, "displayName": "Dark Oak Wood Stairs", "name": "dark_oak_stairs", "stackSize": 64}, {"id": 165, "displayName": "Slime Block", "name": "slime", "stackSize": 64}, {"id": 166, "displayName": "Barrier", "name": "barrier", "stackSize": 64}, {"id": 167, "displayName": "Iron Trapdoor", "name": "iron_trapdoor", "stackSize": 64}, {"id": 168, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "prismarine", "stackSize": 64}, {"id": 169, "displayName": "Sea Lantern", "name": "sea_lantern", "stackSize": 64}, {"id": 170, "displayName": "<PERSON>", "name": "hay_block", "stackSize": 64}, {"id": 171, "displayName": "Carpet", "name": "carpet", "stackSize": 64}, {"id": 172, "displayName": "Hardened Clay", "name": "hardened_clay", "stackSize": 64}, {"id": 173, "displayName": "Block of Coal", "name": "coal_block", "stackSize": 64}, {"id": 174, "displayName": "Packed Ice", "name": "packed_ice", "stackSize": 64}, {"id": 175, "displayName": "Large Flowers", "name": "double_plant", "stackSize": 64}, {"id": 179, "displayName": "Red Sandstone", "name": "red_sandstone", "stackSize": 64}, {"id": 180, "displayName": "Red Sandstone Stairs", "name": "red_sandstone_stairs", "stackSize": 64}, {"id": 182, "displayName": "Red Sandstone Slab", "name": "stone_slab2", "stackSize": 64}, {"id": 183, "displayName": "Spruce Fence Gate", "name": "spruce_fence_gate", "stackSize": 64}, {"id": 184, "displayName": "Birch Fence Gate", "name": "birch_fence_gate", "stackSize": 64}, {"id": 185, "displayName": "Jungle Fence Gate", "name": "jungle_fence_gate", "stackSize": 64}, {"id": 186, "displayName": "Dark Oak Fence Gate", "name": "dark_oak_fence_gate", "stackSize": 64}, {"id": 187, "displayName": "Acacia Fence Gate", "name": "acacia_fence_gate", "stackSize": 64}, {"id": 188, "displayName": "Spruce Fence", "name": "spruce_fence", "stackSize": 64}, {"id": 189, "displayName": "<PERSON>", "name": "birch_fence", "stackSize": 64}, {"id": 190, "displayName": "Jungle Fence", "name": "jungle_fence", "stackSize": 64}, {"id": 191, "displayName": "Dark Oak Fence", "name": "dark_oak_fence", "stackSize": 64}, {"id": 192, "displayName": "Acacia Fence", "name": "acacia_fence", "stackSize": 64}, {"id": 198, "displayName": "End Rod", "name": "end_rod", "stackSize": 64}, {"id": 199, "displayName": "Chorus Plant", "name": "chorus_plant", "stackSize": 64}, {"id": 200, "displayName": "Chorus Flower", "name": "chorus_flower", "stackSize": 64}, {"id": 201, "displayName": "Purpur Block", "name": "purpur_block", "stackSize": 64}, {"id": 202, "displayName": "Purpur Pillar", "name": "purpur_pillar", "stackSize": 64}, {"id": 203, "displayName": "Purpur Stairs", "name": "purpur_stairs", "stackSize": 64}, {"id": 205, "displayName": "Purpur Slab", "name": "purpur_slab", "stackSize": 64}, {"id": 206, "displayName": "End Stone Bricks", "name": "end_bricks", "stackSize": 64}, {"id": 208, "displayName": "Grass Path", "name": "grass_path", "stackSize": 64}, {"id": 210, "displayName": "Repeating Command Block", "name": "repeating_command_block", "stackSize": 64}, {"id": 211, "displayName": "Chain Command Block", "name": "chain_command_block", "stackSize": 64}, {"id": 213, "displayName": "Magma Block", "name": "magma", "stackSize": 64}, {"id": 214, "displayName": "Nether Wart Block", "name": "nether_wart_block", "stackSize": 64}, {"id": 215, "displayName": "Red Nether Brick", "name": "red_nether_brick", "stackSize": 64}, {"id": 216, "displayName": "Bone Block", "name": "bone_block", "stackSize": 64}, {"id": 217, "displayName": "Structure Void", "name": "structure_void", "stackSize": 64}, {"id": 218, "displayName": "Observer", "name": "observer", "stackSize": 64}, {"id": 219, "displayName": "White Shulker Box", "name": "white_shulker_box", "stackSize": 1}, {"id": 220, "displayName": "Orange Shulker Box", "name": "orange_shulker_box", "stackSize": 1}, {"id": 221, "displayName": "<PERSON><PERSON>a <PERSON>er Box", "name": "magenta_shulker_box", "stackSize": 1}, {"id": 222, "displayName": "Light Blue Shulker Box", "name": "light_blue_shulker_box", "stackSize": 1}, {"id": 223, "displayName": "Yellow Shulker Box", "name": "yellow_shulker_box", "stackSize": 1}, {"id": 224, "displayName": "<PERSON>e <PERSON>er Box", "name": "lime_shulker_box", "stackSize": 1}, {"id": 225, "displayName": "Pink Shulker Box", "name": "pink_shulker_box", "stackSize": 1}, {"id": 226, "displayName": "<PERSON>", "name": "gray_shulker_box", "stackSize": 1}, {"id": 227, "displayName": "Light Gray Shulker Box", "name": "silver_shulker_box", "stackSize": 1}, {"id": 228, "displayName": "<PERSON><PERSON>", "name": "cyan_shulker_box", "stackSize": 1}, {"id": 229, "displayName": "Purple Shulker Box", "name": "purple_shulker_box", "stackSize": 1}, {"id": 230, "displayName": "Blue Shulker Box", "name": "blue_shulker_box", "stackSize": 1}, {"id": 231, "displayName": "<PERSON> Shulker Box", "name": "brown_shulker_box", "stackSize": 1}, {"id": 232, "displayName": "Green Shulker Box", "name": "green_shulker_box", "stackSize": 1}, {"id": 233, "displayName": "Red Shulker Box", "name": "red_shulker_box", "stackSize": 1}, {"id": 234, "displayName": "Black Shulker Box", "name": "black_shulker_box", "stackSize": 1}, {"id": 255, "displayName": "Structure Block", "name": "structure_block", "stackSize": 64}, {"id": 256, "displayName": "Iron Shovel", "name": "iron_shovel", "stackSize": 1, "maxDurability": 250, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 257, "displayName": "Iron Pickaxe", "name": "iron_pickaxe", "stackSize": 1, "maxDurability": 250, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 258, "displayName": "Iron Axe", "name": "iron_axe", "stackSize": 1, "maxDurability": 250, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 259, "displayName": "Flint and Steel", "name": "flint_and_steel", "stackSize": 1, "maxDurability": 64, "enchantCategories": ["breakable", "vanishable"]}, {"id": 260, "displayName": "Apple", "name": "apple", "stackSize": 64}, {"id": 261, "displayName": "Bow", "name": "bow", "stackSize": 1, "maxDurability": 384, "enchantCategories": ["breakable", "bow", "vanishable"]}, {"id": 262, "displayName": "Arrow", "name": "arrow", "stackSize": 64}, {"id": 263, "displayName": "Coal", "name": "coal", "stackSize": 64, "variations": [{"metadata": 0, "displayName": "Coal"}, {"metadata": 1, "displayName": "Charc<PERSON>l"}]}, {"id": 264, "displayName": "Diamond", "name": "diamond", "stackSize": 64}, {"id": 265, "displayName": "Iron Ingot", "name": "iron_ingot", "stackSize": 64}, {"id": 266, "displayName": "Gold Ingot", "name": "gold_ingot", "stackSize": 64}, {"id": 267, "displayName": "Iron Sword", "name": "iron_sword", "stackSize": 1, "maxDurability": 250, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 268, "displayName": "Wooden Sword", "name": "wooden_sword", "stackSize": 1, "maxDurability": 59, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"]}, {"id": 269, "displayName": "<PERSON><PERSON>", "name": "wooden_shovel", "stackSize": 1, "maxDurability": 59, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"]}, {"id": 270, "displayName": "<PERSON><PERSON> Pick<PERSON>e", "name": "wooden_pickaxe", "stackSize": 1, "maxDurability": 59, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"]}, {"id": 271, "displayName": "Wooden Axe", "name": "wooden_axe", "stackSize": 1, "maxDurability": 59, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"]}, {"id": 272, "displayName": "Stone Sword", "name": "stone_sword", "stackSize": 1, "maxDurability": 131, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["cobblestone", "blackstone"]}, {"id": 273, "displayName": "<PERSON>el", "name": "stone_shovel", "stackSize": 1, "maxDurability": 131, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["cobblestone", "blackstone"]}, {"id": 274, "displayName": "<PERSON>", "name": "stone_pickaxe", "stackSize": 1, "maxDurability": 131, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["cobblestone", "blackstone"]}, {"id": 275, "displayName": "Stone Axe", "name": "stone_axe", "stackSize": 1, "maxDurability": 131, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["cobblestone", "blackstone"]}, {"id": 276, "displayName": "Diamond Sword", "name": "diamond_sword", "stackSize": 1, "maxDurability": 1561, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["diamond"]}, {"id": 277, "displayName": "Diamond Shovel", "name": "diamond_shovel", "stackSize": 1, "maxDurability": 1561, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["diamond"]}, {"id": 278, "displayName": "Diamond Pickaxe", "name": "diamond_pickaxe", "stackSize": 1, "maxDurability": 1561, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["diamond"]}, {"id": 279, "displayName": "Diamond Axe", "name": "diamond_axe", "stackSize": 1, "maxDurability": 1561, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["diamond"]}, {"id": 280, "displayName": "Stick", "name": "stick", "stackSize": 64}, {"id": 281, "displayName": "Bowl", "name": "bowl", "stackSize": 64}, {"id": 282, "displayName": "Mushroom Stew", "name": "mushroom_stew", "stackSize": 1}, {"id": 283, "displayName": "Golden Sword", "name": "golden_sword", "stackSize": 1, "maxDurability": 32, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 284, "displayName": "Golden Shovel", "name": "golden_shovel", "stackSize": 1, "maxDurability": 32, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 285, "displayName": "Golden Pickaxe", "name": "golden_pickaxe", "stackSize": 1, "maxDurability": 32, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 286, "displayName": "Golden Axe", "name": "golden_axe", "stackSize": 1, "maxDurability": 32, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 287, "displayName": "String", "name": "string", "stackSize": 64}, {"id": 288, "displayName": "<PERSON><PERSON>", "name": "feather", "stackSize": 64}, {"id": 289, "displayName": "Gunpowder", "name": "gunpowder", "stackSize": 64}, {"id": 290, "displayName": "<PERSON><PERSON>e", "name": "wooden_hoe", "stackSize": 1, "maxDurability": 59, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"]}, {"id": 291, "displayName": "Stone Hoe", "name": "stone_hoe", "stackSize": 1, "maxDurability": 131, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["cobblestone", "blackstone"]}, {"id": 292, "displayName": "Iron Hoe", "name": "iron_hoe", "stackSize": 1, "maxDurability": 250, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 293, "displayName": "Diamond Hoe", "name": "diamond_hoe", "stackSize": 1, "maxDurability": 1561, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["diamond"]}, {"id": 294, "displayName": "Golden Hoe", "name": "golden_hoe", "stackSize": 1, "maxDurability": 32, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 295, "displayName": "Seeds", "name": "wheat_seeds", "stackSize": 64}, {"id": 296, "displayName": "Wheat", "name": "wheat", "stackSize": 64}, {"id": 297, "displayName": "Bread", "name": "bread", "stackSize": 64}, {"id": 298, "displayName": "Leather Cap", "name": "leather_helmet", "stackSize": 1, "maxDurability": 55, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["leather"]}, {"id": 299, "displayName": "<PERSON><PERSON>", "name": "leather_chestplate", "stackSize": 1, "maxDurability": 80, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["leather"]}, {"id": 300, "displayName": "<PERSON><PERSON>", "name": "leather_leggings", "stackSize": 1, "maxDurability": 75, "enchantCategories": ["armor", "breakable", "wearable", "vanishable"], "repairWith": ["leather"]}, {"id": 301, "displayName": "<PERSON><PERSON>", "name": "leather_boots", "stackSize": 1, "maxDurability": 65, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["leather"]}, {"id": 302, "displayName": "Chain Helmet", "name": "chainmail_helmet", "stackSize": 1, "maxDurability": 165, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 303, "displayName": "Chain Chestplate", "name": "chainmail_chestplate", "stackSize": 1, "maxDurability": 240, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 304, "displayName": "Chain Leggings", "name": "chainmail_leggings", "stackSize": 1, "maxDurability": 225, "enchantCategories": ["armor", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 305, "displayName": "Chain Boots", "name": "chainmail_boots", "stackSize": 1, "maxDurability": 195, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 306, "displayName": "Iron Helmet", "name": "iron_helmet", "stackSize": 1, "maxDurability": 165, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 307, "displayName": "Iron Chestplate", "name": "iron_chestplate", "stackSize": 1, "maxDurability": 240, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 308, "displayName": "Iron Leggings", "name": "iron_leggings", "stackSize": 1, "maxDurability": 225, "enchantCategories": ["armor", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 309, "displayName": "Iron Boots", "name": "iron_boots", "stackSize": 1, "maxDurability": 195, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 310, "displayName": "Diamond Helmet", "name": "diamond_helmet", "stackSize": 1, "maxDurability": 363, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["diamond"]}, {"id": 311, "displayName": "Diamond Chestplate", "name": "diamond_chestplate", "stackSize": 1, "maxDurability": 528, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["diamond"]}, {"id": 312, "displayName": "Diamond Leggings", "name": "diamond_leggings", "stackSize": 1, "maxDurability": 495, "enchantCategories": ["armor", "breakable", "wearable", "vanishable"], "repairWith": ["diamond"]}, {"id": 313, "displayName": "Diamond Boots", "name": "diamond_boots", "stackSize": 1, "maxDurability": 429, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["diamond"]}, {"id": 314, "displayName": "Golden Helmet", "name": "golden_helmet", "stackSize": 1, "maxDurability": 77, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 315, "displayName": "Golden Chestplate", "name": "golden_chestplate", "stackSize": 1, "maxDurability": 112, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 316, "displayName": "Golden Leggings", "name": "golden_leggings", "stackSize": 1, "maxDurability": 105, "enchantCategories": ["armor", "breakable", "wearable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 317, "displayName": "Golden Boots", "name": "golden_boots", "stackSize": 1, "maxDurability": 91, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 318, "displayName": "Flint", "name": "flint", "stackSize": 64}, {"id": 319, "displayName": "Raw Porkchop", "name": "porkchop", "stackSize": 64}, {"id": 320, "displayName": "Cooked Porkchop", "name": "cooked_porkchop", "stackSize": 64}, {"id": 321, "displayName": "Painting", "name": "painting", "stackSize": 64}, {"id": 322, "displayName": "Golden Apple", "name": "golden_apple", "stackSize": 64, "variations": [{"metadata": 0, "displayName": "Golden Apple"}, {"metadata": 1, "displayName": "Enchanted Golden Apple"}]}, {"id": 323, "displayName": "Sign", "name": "sign", "stackSize": 16}, {"id": 324, "displayName": "Oak Door", "name": "wooden_door", "stackSize": 64}, {"id": 325, "displayName": "Bucket", "name": "bucket", "stackSize": 16}, {"id": 326, "displayName": "Water Bucket", "name": "water_bucket", "stackSize": 1}, {"id": 327, "displayName": "<PERSON><PERSON>et", "name": "lava_bucket", "stackSize": 1}, {"id": 328, "displayName": "Minecart", "name": "minecart", "stackSize": 1}, {"id": 329, "displayName": "Saddle", "name": "saddle", "stackSize": 1}, {"id": 330, "displayName": "Iron Door", "name": "iron_door", "stackSize": 64}, {"id": 331, "displayName": "Redstone", "name": "redstone", "stackSize": 64}, {"id": 332, "displayName": "Snowball", "name": "snowball", "stackSize": 16}, {"id": 333, "displayName": "Boat", "name": "boat", "stackSize": 1}, {"id": 334, "displayName": "Leather", "name": "leather", "stackSize": 64}, {"id": 335, "displayName": "Milk", "name": "milk_bucket", "stackSize": 1}, {"id": 336, "displayName": "Brick", "name": "brick", "stackSize": 64}, {"id": 337, "displayName": "<PERSON>", "name": "clay_ball", "stackSize": 64}, {"id": 338, "displayName": "Sugar Canes", "name": "reeds", "stackSize": 64}, {"id": 339, "displayName": "Paper", "name": "paper", "stackSize": 64}, {"id": 340, "displayName": "Book", "name": "book", "stackSize": 64}, {"id": 341, "displayName": "Slimeball", "name": "slime_ball", "stackSize": 64}, {"id": 342, "displayName": "Minecart with Chest", "name": "chest_minecart", "stackSize": 1}, {"id": 343, "displayName": "Minecart with Furnace", "name": "furnace_minecart", "stackSize": 1}, {"id": 344, "displayName": "Egg", "name": "egg", "stackSize": 16}, {"id": 345, "displayName": "<PERSON>mp<PERSON>", "name": "compass", "stackSize": 64}, {"id": 346, "displayName": "Fishing Rod", "name": "fishing_rod", "stackSize": 1, "maxDurability": 64, "enchantCategories": ["breakable", "fishing_rod", "vanishable"]}, {"id": 347, "displayName": "Clock", "name": "clock", "stackSize": 64}, {"id": 348, "displayName": "Glowstone Dust", "name": "glowstone_dust", "stackSize": 64}, {"id": 349, "displayName": "Fish", "name": "fish", "stackSize": 64, "variations": [{"metadata": 0, "displayName": "Raw Fish"}, {"metadata": 1, "displayName": "Raw Salmon"}, {"metadata": 2, "displayName": "Clownfish"}, {"metadata": 3, "displayName": "Pufferfish"}]}, {"id": 350, "displayName": "Cooked Fish", "name": "cooked_fish", "stackSize": 64}, {"id": 351, "displayName": "Dye", "name": "dye", "stackSize": 64, "variations": [{"metadata": 0, "displayName": "Ink Sac"}, {"metadata": 1, "displayName": "Rose Red"}, {"metadata": 2, "displayName": "Cactus <PERSON>"}, {"metadata": 3, "displayName": "Cocoa Beans"}, {"metadata": 4, "displayName": "<PERSON><PERSON>"}, {"metadata": 5, "displayName": "Purple Dye"}, {"metadata": 6, "displayName": "<PERSON><PERSON>"}, {"metadata": 7, "displayName": "Light Gray D<PERSON>"}, {"metadata": 8, "displayName": "<PERSON>"}, {"metadata": 9, "displayName": "Pink Dye"}, {"metadata": 10, "displayName": "Lime Dye"}, {"metadata": 11, "displayName": "Dandelion Yellow"}, {"metadata": 12, "displayName": "Light Blue Dye"}, {"metadata": 13, "displayName": "<PERSON><PERSON><PERSON>"}, {"metadata": 14, "displayName": "Orange Dye"}, {"metadata": 15, "displayName": "<PERSON>"}]}, {"id": 352, "displayName": "Bone", "name": "bone", "stackSize": 64}, {"id": 353, "displayName": "Sugar", "name": "sugar", "stackSize": 64}, {"id": 354, "displayName": "Cake", "name": "cake", "stackSize": 1}, {"id": 355, "displayName": "Bed", "name": "bed", "stackSize": 1}, {"id": 356, "displayName": "Redstone Repeater", "name": "repeater", "stackSize": 64}, {"id": 357, "displayName": "<PERSON><PERSON>", "name": "cookie", "stackSize": 64}, {"id": 358, "displayName": "Map", "name": "filled_map", "stackSize": 64}, {"id": 359, "displayName": "Shears", "name": "shears", "stackSize": 1, "maxDurability": 238, "enchantCategories": ["breakable", "vanishable"]}, {"id": 360, "displayName": "Melon", "name": "melon", "stackSize": 64}, {"id": 361, "displayName": "<PERSON><PERSON><PERSON> Seeds", "name": "pumpkin_seeds", "stackSize": 64}, {"id": 362, "displayName": "<PERSON>on Seeds", "name": "melon_seeds", "stackSize": 64}, {"id": 363, "displayName": "Raw Beef", "name": "beef", "stackSize": 64}, {"id": 364, "displayName": "Steak", "name": "cooked_beef", "stackSize": 64}, {"id": 365, "displayName": "Raw Chicken", "name": "chicken", "stackSize": 64}, {"id": 366, "displayName": "Cooked Chicken", "name": "cooked_chicken", "stackSize": 64}, {"id": 367, "displayName": "Rotten Flesh", "name": "rotten_flesh", "stackSize": 64}, {"id": 368, "displayName": "<PERSON><PERSON>", "name": "ender_pearl", "stackSize": 16}, {"id": 369, "displayName": "<PERSON>", "name": "blaze_rod", "stackSize": 64}, {"id": 370, "displayName": "Ghast Tear", "name": "ghast_tear", "stackSize": 64}, {"id": 371, "displayName": "Gold Nugget", "name": "gold_nugget", "stackSize": 64}, {"id": 372, "displayName": "Nether Wart", "name": "nether_wart", "stackSize": 64}, {"id": 373, "displayName": "Potion", "name": "potion", "stackSize": 1}, {"id": 374, "displayName": "Glass Bottle", "name": "glass_bottle", "stackSize": 64}, {"id": 375, "displayName": "Spider Eye", "name": "spider_eye", "stackSize": 64}, {"id": 376, "displayName": "Fermented Spider Eye", "name": "fermented_spider_eye", "stackSize": 64}, {"id": 377, "displayName": "<PERSON>", "name": "blaze_powder", "stackSize": 64}, {"id": 378, "displayName": "Magma Cream", "name": "magma_cream", "stackSize": 64}, {"id": 379, "displayName": "Brewing Stand", "name": "brewing_stand", "stackSize": 64}, {"id": 380, "displayName": "<PERSON><PERSON><PERSON>", "name": "cauldron", "stackSize": 64}, {"id": 381, "displayName": "Eye of <PERSON>er", "name": "ender_eye", "stackSize": 64}, {"id": 382, "displayName": "Glistering <PERSON><PERSON>", "name": "speckled_melon", "stackSize": 64}, {"id": 383, "displayName": "Spawn Egg", "name": "spawn_egg", "stackSize": 64}, {"id": 384, "displayName": "Bottle o' Enchanting", "name": "experience_bottle", "stackSize": 64}, {"id": 385, "displayName": "Fire Charge", "name": "fire_charge", "stackSize": 64}, {"id": 386, "displayName": "Book and Quill", "name": "writable_book", "stackSize": 1}, {"id": 387, "displayName": "Written Book", "name": "written_book", "stackSize": 16}, {"id": 388, "displayName": "Emerald", "name": "emerald", "stackSize": 64}, {"id": 389, "displayName": "<PERSON><PERSON>", "name": "item_frame", "stackSize": 64}, {"id": 390, "displayName": "Flower Pot", "name": "flower_pot", "stackSize": 64, "variations": [{"metadata": 0, "displayName": "Empty Flower Pot"}, {"metadata": 1, "displayName": "Poppy Flower Pot"}, {"metadata": 2, "displayName": "Dandelion Flower Pot"}, {"metadata": 3, "displayName": "Oak sapling Flower Pot"}, {"metadata": 4, "displayName": "Spruce sapling Flower Pot"}, {"metadata": 5, "displayName": "Birch sapling Flower Pot"}, {"metadata": 6, "displayName": "Jungle sapling Flower Pot"}, {"metadata": 7, "displayName": "Red mushroom Flower Pot"}, {"metadata": 8, "displayName": "Brown mushroom Flower Pot"}, {"metadata": 9, "displayName": "Cactus Flower Pot"}, {"metadata": 10, "displayName": "Dead bush Flower Pot"}, {"metadata": 11, "displayName": "<PERSON><PERSON>"}, {"metadata": 12, "displayName": "Acacia sapling Flower Pot"}, {"metadata": 13, "displayName": "Dark oak sapling Flower Pot"}]}, {"id": 391, "displayName": "Carrot", "name": "carrot", "stackSize": 64}, {"id": 392, "displayName": "Potato", "name": "potato", "stackSize": 64}, {"id": 393, "displayName": "Baked Potato", "name": "baked_potato", "stackSize": 64}, {"id": 394, "displayName": "Poisonous Potato", "name": "poisonous_potato", "stackSize": 64}, {"id": 395, "displayName": "Empty Map", "name": "map", "stackSize": 64}, {"id": 396, "displayName": "Golden Carrot", "name": "golden_carrot", "stackSize": 64}, {"id": 397, "displayName": "Skull", "name": "skull", "stackSize": 64, "variations": [{"metadata": 0, "displayName": "Skeleton Skull"}, {"metadata": 1, "displayName": "Wither Skeleton Skull"}, {"metadata": 2, "displayName": "Zombie Head"}, {"metadata": 3, "displayName": "Head"}, {"metadata": 4, "displayName": "Creeper Head"}]}, {"id": 398, "displayName": "Carrot on a Stick", "name": "carrot_on_a_stick", "stackSize": 1, "maxDurability": 25, "enchantCategories": ["breakable", "vanishable"]}, {"id": 399, "displayName": "Nether Star", "name": "nether_star", "stackSize": 64}, {"id": 400, "displayName": "Pumpkin Pie", "name": "pumpkin_pie", "stackSize": 64}, {"id": 401, "displayName": "Firework Rocket", "name": "fireworks", "stackSize": 64}, {"id": 402, "displayName": "Firework Star", "name": "firework_charge", "stackSize": 64}, {"id": 403, "displayName": "Enchanted Book", "name": "enchanted_book", "stackSize": 1}, {"id": 404, "displayName": "Redstone Comparator", "name": "comparator", "stackSize": 64}, {"id": 405, "displayName": "Nether Brick", "name": "netherbrick", "stackSize": 64}, {"id": 406, "displayName": "<PERSON><PERSON>", "name": "quartz", "stackSize": 64}, {"id": 407, "displayName": "Minecart with TNT", "name": "tnt_minecart", "stackSize": 1}, {"id": 408, "displayName": "Minecart with <PERSON>", "name": "hopper_minecart", "stackSize": 1}, {"id": 409, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "prismarine_shard", "stackSize": 64}, {"id": 410, "displayName": "Prismarine Crystals", "name": "prismarine_crystals", "stackSize": 64}, {"id": 411, "displayName": "Raw Rabbit", "name": "rabbit", "stackSize": 64}, {"id": 412, "displayName": "Cooked Rabbit", "name": "cooked_rabbit", "stackSize": 64}, {"id": 413, "displayName": "Rabbit Stew", "name": "rabbit_stew", "stackSize": 1}, {"id": 414, "displayName": "<PERSON>'s Foot", "name": "rabbit_foot", "stackSize": 64}, {"id": 415, "displayName": "<PERSON>", "name": "rabbit_hide", "stackSize": 64}, {"id": 416, "displayName": "Armor Stand", "name": "armor_stand", "stackSize": 16}, {"id": 417, "displayName": "Iron Horse Armor", "name": "iron_horse_armor", "stackSize": 1}, {"id": 418, "displayName": "Gold Horse Armor", "name": "golden_horse_armor", "stackSize": 1}, {"id": 419, "displayName": "Diamond Horse Armor", "name": "diamond_horse_armor", "stackSize": 1}, {"id": 420, "displayName": "Lead", "name": "lead", "stackSize": 64}, {"id": 421, "displayName": "Name Tag", "name": "name_tag", "stackSize": 64}, {"id": 422, "displayName": "Minecart with Command Block", "name": "command_block_minecart", "stackSize": 1}, {"id": 423, "displayName": "<PERSON>", "name": "mutton", "stackSize": 64}, {"id": 424, "displayName": "Cooked <PERSON>tton", "name": "cooked_mutton", "stackSize": 64}, {"id": 425, "displayName": "Banner", "name": "banner", "stackSize": 16}, {"id": 426, "displayName": "End Crystal", "name": "end_crystal", "stackSize": 64}, {"id": 427, "displayName": "Spruce Door", "name": "spruce_door", "stackSize": 64}, {"id": 428, "displayName": "<PERSON>", "name": "birch_door", "stackSize": 64}, {"id": 429, "displayName": "Jungle Door", "name": "jungle_door", "stackSize": 64}, {"id": 430, "displayName": "Acacia Door", "name": "acacia_door", "stackSize": 64}, {"id": 431, "displayName": "Dark Oak Door", "name": "dark_oak_door", "stackSize": 64}, {"id": 432, "displayName": "Chorus Fruit", "name": "chorus_fruit", "stackSize": 64}, {"id": 433, "displayName": "Popped Chorus Fruit", "name": "chorus_fruit_popped", "stackSize": 64}, {"id": 434, "displayName": "Beetroot", "name": "beetroot", "stackSize": 64}, {"id": 435, "displayName": "Beetroot Seeds", "name": "beetroot_seeds", "stackSize": 64}, {"id": 436, "displayName": "Beetroot Soup", "name": "beetroot_soup", "stackSize": 1}, {"id": 437, "displayName": "Dragon's Breath", "name": "dragon_breath", "stackSize": 64}, {"id": 438, "displayName": "Splash Potion", "name": "splash_potion", "stackSize": 1}, {"id": 439, "displayName": "Spectral Arrow", "name": "spectral_arrow", "stackSize": 64}, {"id": 440, "displayName": "Tipped Arrow", "name": "tipped_arrow", "stackSize": 64}, {"id": 441, "displayName": "Lingering Potion", "name": "lingering_potion", "stackSize": 1}, {"id": 442, "displayName": "Shield", "name": "shield", "stackSize": 1, "maxDurability": 336, "enchantCategories": ["breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"]}, {"id": 443, "displayName": "Elytra", "name": "elytra", "stackSize": 1, "maxDurability": 432, "enchantCategories": ["breakable", "wearable", "vanishable"], "repairWith": ["phantom_membrane"]}, {"id": 444, "displayName": "Spruce Boat", "name": "spruce_boat", "stackSize": 1}, {"id": 445, "displayName": "<PERSON> Boat", "name": "birch_boat", "stackSize": 1}, {"id": 446, "displayName": "Jungle Boat", "name": "jungle_boat", "stackSize": 1}, {"id": 447, "displayName": "Acacia Boat", "name": "acacia_boat", "stackSize": 1}, {"id": 448, "displayName": "Dark Oak Boat", "name": "dark_oak_boat", "stackSize": 1}, {"id": 449, "displayName": "Totem of Undying", "name": "totem", "stackSize": 1}, {"id": 450, "displayName": "Shulker Shell", "name": "shulker_shell", "stackSize": 64}, {"id": 2256, "displayName": "13 Disc", "name": "record_13", "stackSize": 64}, {"id": 2257, "displayName": "Cat Disc", "name": "record_cat", "stackSize": 1}, {"id": 2258, "displayName": "Blocks Disc", "name": "record_blocks", "stackSize": 1}, {"id": 2259, "displayName": "Chirp Disc", "name": "record_chirp", "stackSize": 1}, {"id": 2260, "displayName": "Far Disc", "name": "record_far", "stackSize": 1}, {"id": 2261, "displayName": "Mall Disc", "name": "record_mall", "stackSize": 1}, {"id": 2262, "displayName": "Mellohi Disc", "name": "record_mellohi", "stackSize": 1}, {"id": 2263, "displayName": "Stal Disc", "name": "record_stal", "stackSize": 1}, {"id": 2264, "displayName": "Strad Disc", "name": "record_strad", "stackSize": 1}, {"id": 2265, "displayName": "Ward Disc", "name": "record_ward", "stackSize": 1}, {"id": 2266, "displayName": "11 Disc", "name": "record_11", "stackSize": 1}, {"id": 2267, "displayName": "Wait Disc", "name": "record_wait", "stackSize": 1}]