{"name": "mineflayer-mapart-maker", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"node_modules/@azure/msal-common": {"version": "14.16.0", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/@azure/msal-node": {"version": "2.16.2", "license": "MIT", "dependencies": {"@azure/msal-common": "14.16.0", "jsonwebtoken": "^9.0.0", "uuid": "^8.3.0"}, "engines": {"node": ">=16"}}, "node_modules/@types/node": {"version": "24.0.1", "license": "MIT", "dependencies": {"undici-types": "~7.8.0"}}, "node_modules/@types/node-rsa": {"version": "1.1.4", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/readable-stream": {"version": "4.0.21", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@xboxreplay/errors": {"version": "0.1.0", "license": "MIT"}, "node_modules/@xboxreplay/xboxlive-auth": {"version": "3.3.3", "license": "MIT", "dependencies": {"@xboxreplay/errors": "^0.1.0", "axios": "^0.21.1"}}, "node_modules/abort-controller": {"version": "3.0.0", "license": "MIT", "dependencies": {"event-target-shim": "^5.0.0"}, "engines": {"node": ">=6.5"}}, "node_modules/aes-js": {"version": "3.1.2", "license": "MIT"}, "node_modules/ajv": {"version": "6.12.6", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/asn1": {"version": "0.2.3", "license": "MIT"}, "node_modules/axios": {"version": "0.21.4", "license": "MIT", "dependencies": {"follow-redirects": "^1.14.0"}}, "node_modules/base64-js": {"version": "1.5.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/buffer": {"version": "6.0.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "node_modules/buffer-equal": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/buffer-equal-constant-time": {"version": "1.0.1", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/commander": {"version": "2.20.3", "license": "MIT"}, "node_modules/debug": {"version": "4.4.1", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/discontinuous-range": {"version": "1.0.0", "license": "MIT"}, "node_modules/ecdsa-sig-formatter": {"version": "1.0.11", "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}}, "node_modules/endian-toggle": {"version": "0.0.0", "license": "MIT"}, "node_modules/event-target-shim": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/events": {"version": "3.3.0", "license": "MIT", "engines": {"node": ">=0.8.x"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "license": "MIT"}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "license": "MIT"}, "node_modules/follow-redirects": {"version": "1.15.9", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/ieee754": {"version": "1.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "license": "MIT"}, "node_modules/jsonwebtoken": {"version": "9.0.2", "license": "MIT", "dependencies": {"jws": "^3.2.2", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.1.1", "semver": "^7.5.4"}, "engines": {"node": ">=12", "npm": ">=6"}}, "node_modules/jwa": {"version": "1.4.2", "license": "MIT", "dependencies": {"buffer-equal-constant-time": "^1.0.1", "ecdsa-sig-formatter": "1.0.11", "safe-buffer": "^5.0.1"}}, "node_modules/jws": {"version": "3.2.2", "license": "MIT", "dependencies": {"jwa": "^1.4.1", "safe-buffer": "^5.0.1"}}, "node_modules/lodash.includes": {"version": "4.3.0", "license": "MIT"}, "node_modules/lodash.isboolean": {"version": "3.0.3", "license": "MIT"}, "node_modules/lodash.isinteger": {"version": "4.0.4", "license": "MIT"}, "node_modules/lodash.isnumber": {"version": "3.0.3", "license": "MIT"}, "node_modules/lodash.isplainobject": {"version": "4.0.6", "license": "MIT"}, "node_modules/lodash.isstring": {"version": "4.0.1", "license": "MIT"}, "node_modules/lodash.merge": {"version": "4.6.2", "license": "MIT"}, "node_modules/lodash.once": {"version": "4.1.1", "license": "MIT"}, "node_modules/lodash.reduce": {"version": "4.6.0", "license": "MIT"}, "node_modules/macaddress": {"version": "0.5.3", "license": "MIT"}, "node_modules/minecraft-data": {"version": "3.89.0", "resolved": "https://registry.npmjs.org/minecraft-data/-/minecraft-data-3.89.0.tgz", "integrity": "sha512-v6dUr1M7Pjc6N4ujanrBZu3IP4/HbSBpxSSXNbK6HVFVJqfaqKSMXN57G/JAlDcwqXYsVd9H4tbKFHCO+VmQpg==", "license": "MIT"}, "node_modules/minecraft-folder-path": {"version": "1.2.0", "license": "MIT"}, "node_modules/minecraft-protocol": {"version": "1.57.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@types/node-rsa": "^1.1.4", "@types/readable-stream": "^4.0.0", "aes-js": "^3.1.2", "buffer-equal": "^1.0.0", "debug": "^4.3.2", "endian-toggle": "^0.0.0", "lodash.merge": "^4.3.0", "minecraft-data": "^3.78.0", "minecraft-folder-path": "^1.2.0", "node-fetch": "^2.6.1", "node-rsa": "^0.4.2", "prismarine-auth": "^2.2.0", "prismarine-chat": "^1.10.0", "prismarine-nbt": "^2.5.0", "prismarine-realms": "^1.2.0", "protodef": "^1.17.0", "readable-stream": "^4.1.0", "uuid-1345": "^1.0.1", "yggdrasil": "^1.4.0"}, "engines": {"node": ">=22"}}, "node_modules/mineflayer": {"version": "4.29.0", "resolved": "https://registry.npmjs.org/mineflayer/-/mineflayer-4.29.0.tgz", "integrity": "sha512-NMH+1EiQi4inLKhnt2HCpqSAcK0cYc5dGOYrzoewTUoNZQ7w3BZ6tnmq3bpzyubn717IwJ/OfS8n+BKXFE13tQ==", "license": "MIT", "dependencies": {"minecraft-data": "^3.76.0", "minecraft-protocol": "^1.51.0", "prismarine-biome": "^1.1.1", "prismarine-block": "^1.17.0", "prismarine-chat": "^1.7.1", "prismarine-chunk": "^1.36.0", "prismarine-entity": "^2.5.0", "prismarine-item": "^1.15.0", "prismarine-nbt": "^2.0.0", "prismarine-physics": "^1.9.0", "prismarine-recipe": "^1.3.0", "prismarine-registry": "^1.10.0", "prismarine-windows": "^2.9.0", "prismarine-world": "^3.6.0", "protodef": "^1.18.0", "typed-emitter": "^1.0.0", "vec3": "^0.1.7"}, "engines": {"node": ">=22"}}, "node_modules/mojangson": {"version": "2.0.4", "license": "MIT", "dependencies": {"nearley": "^2.19.5"}}, "node_modules/moo": {"version": "0.5.2", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "node_modules/nearley": {"version": "2.20.1", "license": "MIT", "dependencies": {"commander": "^2.19.0", "moo": "^0.5.0", "railroad-diagrams": "^1.0.0", "randexp": "0.4.6"}, "bin": {"nearley-railroad": "bin/nearley-railroad.js", "nearley-test": "bin/nearley-test.js", "nearley-unparse": "bin/nearley-unparse.js", "nearleyc": "bin/nearleyc.js"}, "funding": {"type": "individual", "url": "https://nearley.js.org/#give-to-nearley"}}, "node_modules/node-fetch": {"version": "2.7.0", "license": "MIT", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/node-rsa": {"version": "0.4.2", "license": "MIT", "dependencies": {"asn1": "0.2.3"}}, "node_modules/prismarine-auth": {"version": "2.7.0", "license": "MIT", "dependencies": {"@azure/msal-node": "^2.0.2", "@xboxreplay/xboxlive-auth": "^3.3.3", "debug": "^4.3.3", "smart-buffer": "^4.1.0", "uuid-1345": "^1.0.2"}}, "node_modules/prismarine-biome": {"version": "1.3.0", "license": "MIT", "peerDependencies": {"minecraft-data": "^3.0.0", "prismarine-registry": "^1.1.0"}}, "node_modules/prismarine-block": {"version": "1.21.0", "license": "MIT", "dependencies": {"minecraft-data": "^3.38.0", "prismarine-biome": "^1.1.0", "prismarine-chat": "^1.5.0", "prismarine-item": "^1.10.1", "prismarine-nbt": "^2.0.0", "prismarine-registry": "^1.1.0"}}, "node_modules/prismarine-chat": {"version": "1.11.0", "license": "MIT", "dependencies": {"mojangson": "^2.0.1", "prismarine-nbt": "^2.0.0", "prismarine-registry": "^1.4.0"}}, "node_modules/prismarine-chunk": {"version": "1.38.1", "license": "MIT", "dependencies": {"prismarine-biome": "^1.2.0", "prismarine-block": "^1.14.1", "prismarine-nbt": "^2.2.1", "prismarine-registry": "^1.1.0", "smart-buffer": "^4.1.0", "uint4": "^0.1.2", "vec3": "^0.1.3", "xxhash-wasm": "^0.4.2"}, "engines": {"node": ">=14"}}, "node_modules/prismarine-entity": {"version": "2.5.0", "license": "MIT", "dependencies": {"prismarine-chat": "^1.4.1", "prismarine-item": "^1.11.2", "prismarine-registry": "^1.4.0", "vec3": "^0.1.4"}}, "node_modules/prismarine-item": {"version": "1.16.0", "license": "MIT", "dependencies": {"prismarine-nbt": "^2.0.0", "prismarine-registry": "^1.4.0"}}, "node_modules/prismarine-nbt": {"version": "2.7.0", "license": "MIT", "dependencies": {"protodef": "^1.18.0"}}, "node_modules/prismarine-physics": {"version": "1.10.0", "license": "MIT", "dependencies": {"minecraft-data": "^3.0.0", "prismarine-nbt": "^2.0.0", "vec3": "^0.1.7"}}, "node_modules/prismarine-realms": {"version": "1.3.2", "license": "MIT", "dependencies": {"debug": "^4.3.3", "node-fetch": "^2.6.1"}}, "node_modules/prismarine-recipe": {"version": "1.3.1", "license": "MIT", "peerDependencies": {"prismarine-registry": "^1.4.0"}}, "node_modules/prismarine-registry": {"version": "1.11.0", "license": "MIT", "dependencies": {"minecraft-data": "^3.70.0", "prismarine-block": "^1.17.1", "prismarine-nbt": "^2.0.0"}}, "node_modules/prismarine-windows": {"version": "2.9.0", "license": "MIT", "dependencies": {"prismarine-item": "^1.12.2", "prismarine-registry": "^1.7.0", "typed-emitter": "^2.1.0"}}, "node_modules/prismarine-windows/node_modules/typed-emitter": {"version": "2.1.0", "license": "MIT", "optionalDependencies": {"rxjs": "*"}}, "node_modules/prismarine-world": {"version": "3.6.3", "license": "MIT", "dependencies": {"vec3": "^0.1.7"}, "engines": {"node": ">=8.0.0"}}, "node_modules/process": {"version": "0.11.10", "license": "MIT", "engines": {"node": ">= 0.6.0"}}, "node_modules/protodef": {"version": "1.19.0", "license": "MIT", "dependencies": {"lodash.reduce": "^4.6.0", "protodef-validator": "^1.3.0", "readable-stream": "^4.4.0"}, "engines": {"node": ">=14"}}, "node_modules/protodef-validator": {"version": "1.4.0", "license": "MIT", "dependencies": {"ajv": "^6.5.4"}, "bin": {"protodef-validator": "cli.js"}}, "node_modules/punycode": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/railroad-diagrams": {"version": "1.0.0", "license": "CC0-1.0"}, "node_modules/randexp": {"version": "0.4.6", "license": "MIT", "dependencies": {"discontinuous-range": "1.0.0", "ret": "~0.1.10"}, "engines": {"node": ">=0.12"}}, "node_modules/readable-stream": {"version": "4.7.0", "license": "MIT", "dependencies": {"abort-controller": "^3.0.0", "buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "string_decoder": "^1.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/ret": {"version": "0.1.15", "license": "MIT", "engines": {"node": ">=0.12"}}, "node_modules/safe-buffer": {"version": "5.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/semver": {"version": "7.7.2", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/smart-buffer": {"version": "4.2.0", "license": "MIT", "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}}, "node_modules/string_decoder": {"version": "1.3.0", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/tr46": {"version": "0.0.3", "license": "MIT"}, "node_modules/typed-emitter": {"version": "1.4.0", "license": "MIT"}, "node_modules/uint4": {"version": "0.1.2", "license": "MIT"}, "node_modules/undici-types": {"version": "7.8.0", "license": "MIT"}, "node_modules/uri-js": {"version": "4.4.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/uuid": {"version": "8.3.2", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/uuid-1345": {"version": "1.0.2", "license": "MIT", "dependencies": {"macaddress": "^0.5.1"}}, "node_modules/vec3": {"version": "0.1.10", "resolved": "https://registry.npmjs.org/vec3/-/vec3-0.1.10.tgz", "integrity": "sha512-Sr1U3mYtMqCOonGd3LAN9iqy0qF6C+Gjil92awyK/i2OwiUo9bm7PnLgFpafymun50mOjnDcg4ToTgRssrlTcw==", "license": "BSD"}, "node_modules/webidl-conversions": {"version": "3.0.1", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/whatwg-url": {"version": "5.0.0", "license": "MIT", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/xxhash-wasm": {"version": "0.4.2", "license": "MIT"}, "node_modules/yggdrasil": {"version": "1.7.0", "license": "MIT", "dependencies": {"node-fetch": "^2.6.1", "uuid": "^8.2.0"}}}}