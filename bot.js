const mineflayer = require('mineflayer');
const fs = require('fs');

// Load configuration
let config;
try {
    config = JSON.parse(fs.readFileSync('config.json', 'utf8'));
} catch (error) {
    console.error('Error loading config.json, using defaults:', error.message);
    config = {
        server: {
            host: 'localhost',
            port: 25565,
            username: 'MapArtBot'
        },
        building: {
            areaSize: 16,
            blockType: 'white_wool'
        },
        delays: {
            betweenBlocks: 100,
            afterSpawn: 2000,
            inventoryCheck: 5000
        },
        monitoring: {
            enableInventoryChecks: true,
            showDetailedProgress: true
        }
    };
}

const BOT_CONFIG = config.server;
const AREA_SIZE = config.building.areaSize;

class MapArtBot {
    constructor() {
        this.bot = null;
        this.isBuilding = false;
        this.startPosition = null;
        this.inventoryCheckInterval = null;
        this.lastWoolCount = 0;
    }

    connect() {
        console.log('Connecting to Minecraft server...');
        this.bot = mineflayer.createBot(BOT_CONFIG);

        this.bot.on('login', () => {
            console.log(`Bot logged in as ${this.bot.username}`);
        });

        this.bot.on('spawn', () => {
            console.log('Bot spawned in the world');
            this.startPosition = this.bot.entity.position.clone();
            console.log(`Starting position: ${this.startPosition.x.toFixed(1)}, ${this.startPosition.y.toFixed(1)}, ${this.startPosition.z.toFixed(1)}`);

            // Start inventory monitoring
            this.startInventoryMonitoring();

            // Show some debug info about the environment
            setTimeout(() => {
                this.showEnvironmentInfo();
            }, 1000);

            // Wait a bit then start building
            setTimeout(() => {
                this.startBuilding();
            }, config.delays?.afterSpawn || 2000);
        });

        this.bot.on('error', (err) => {
            console.error('Bot error:', err);
        });

        this.bot.on('end', () => {
            console.log('Bot disconnected');
            this.stopInventoryMonitoring();
        });
    }

    async startBuilding() {
        if (this.isBuilding) return;
        
        console.log('Starting to build 16x16 white wool area...');
        this.isBuilding = true;

        try {
            await this.buildArea();
        } catch (error) {
            console.error('Error during building:', error);
        } finally {
            this.isBuilding = false;
            this.stopInventoryMonitoring();
        }
    }

    async buildArea() {
        const startX = Math.floor(this.startPosition.x);
        const startZ = Math.floor(this.startPosition.z);

        console.log(`Building 16x16 area on ground level starting from (${startX}, ${startZ})`);

        for (let x = 0; x < AREA_SIZE; x++) {
            for (let z = 0; z < AREA_SIZE; z++) {
                const targetX = startX + x;
                const targetZ = startZ + z;

                // Check if we have white wool
                const woolInfo = this.getWoolInfo();
                if (!woolInfo.hasWool) {
                    console.log('❌ No white wool found in inventory! Stopping build.');
                    return;
                }

                try {
                    console.log(`Attempting to place wool at (${targetX}, ${targetZ})...`);
                    await this.placeWoolOnGround(targetX, targetZ);
                    const progress = ((x * AREA_SIZE + z + 1) / (AREA_SIZE * AREA_SIZE) * 100).toFixed(1);
                    const remainingWool = this.getWoolInfo().count;
                    console.log(`✅ Progress: ${progress}% - Wool left: ${remainingWool}`);

                    // Configurable delay between blocks
                    await this.sleep(config.delays?.betweenBlocks || 100);
                } catch (error) {
                    console.error(`❌ Failed to place wool at (${targetX}, ${targetZ}):`, error.message);
                    // Continue with next position instead of stopping
                }
            }
        }

        console.log('Finished building 16x16 white wool area on ground!');
    }

    startInventoryMonitoring() {
        if (!config.monitoring || !config.monitoring.enableInventoryChecks) return;

        // Check inventory at configurable intervals
        const checkInterval = config.delays?.inventoryCheck || 5000;
        this.inventoryCheckInterval = setInterval(() => {
            this.checkInventoryStatus();
        }, checkInterval);

        // Initial check
        this.checkInventoryStatus();
    }

    stopInventoryMonitoring() {
        if (this.inventoryCheckInterval) {
            clearInterval(this.inventoryCheckInterval);
            this.inventoryCheckInterval = null;
        }
    }

    checkInventoryStatus() {
        const woolInfo = this.getWoolInfo();

        if (woolInfo.count !== this.lastWoolCount) {
            if (woolInfo.count === 0) {
                console.log('❌ No white wool found in inventory!');
            } else {
                console.log(`🧶 White wool in inventory: ${woolInfo.count} blocks`);
            }
            this.lastWoolCount = woolInfo.count;
        }
    }

    getWoolInfo() {
        const blockType = config.building?.blockType || 'white_wool';
        const whiteWool = this.bot.inventory.items().find(item =>
            item.name === blockType ||
            item.name === 'white_wool' ||
            item.name === 'wool'
        );

        return {
            item: whiteWool,
            count: whiteWool ? whiteWool.count : 0,
            hasWool: whiteWool && whiteWool.count > 0
        };
    }

    hasWhiteWool() {
        return this.getWoolInfo().hasWool;
    }

    async placeWoolOnGround(x, z) {
        // Get white wool info
        const woolInfo = this.getWoolInfo();

        if (!woolInfo.hasWool) {
            throw new Error('No white wool in inventory');
        }

        // Equip white wool
        await this.bot.equip(woolInfo.item, 'hand');

        // Simple movement - just get close to the target
        await this.moveCloseToTarget(x, z);

        // Find a solid block to place on (search around the target area)
        const targetBlock = await this.findPlaceableBlock(x, z);

        if (!targetBlock) {
            throw new Error(`No suitable block found to place on near (${x}, ${z})`);
        }

        // Place the wool block
        await this.bot.placeBlock(targetBlock.block, targetBlock.face);
        console.log(`Placed wool at (${targetBlock.block.position.x}, ${targetBlock.block.position.y + 1}, ${targetBlock.block.position.z})`);
    }

    async moveCloseToTarget(x, z) {
        // Simple movement - just look at the target and move if too far
        const botPos = this.bot.entity.position;
        const distance = Math.sqrt((x - botPos.x) ** 2 + (z - botPos.z) ** 2);

        try {
            // Try to get Vec3 from mineflayer first
            const Vec3 = this.bot.vec3 || require('vec3');
            const lookTarget = new Vec3(x, botPos.y, z);
            await this.bot.lookAt(lookTarget);
        } catch (error) {
            console.log(`Look at failed: ${error.message}`);
        }

        // If too far, try to move closer (but don't worry if it fails)
        if (distance > 5) {
            console.log(`Moving closer to (${x}, ${z}) - current distance: ${distance.toFixed(1)}`);
            // Just wait a bit for the bot to potentially move
            await this.sleep(500);
        }
    }

    async findPlaceableBlock(targetX, targetZ) {
        const botPos = this.bot.entity.position;
        const searchRadius = 2;

        try {
            // Try to get Vec3 from mineflayer first
            const Vec3 = this.bot.vec3 || require('vec3');

            // Search in a small area around the target position
            for (let dx = -searchRadius; dx <= searchRadius; dx++) {
                for (let dz = -searchRadius; dz <= searchRadius; dz++) {
                    for (let dy = -2; dy <= 2; dy++) {
                        const x = targetX + dx;
                        const z = targetZ + dz;
                        const y = Math.floor(botPos.y) + dy;

                        const blockPos = new Vec3(x, y, z);
                        const blockAbovePos = new Vec3(x, y + 1, z);

                        const block = this.bot.blockAt(blockPos);
                        const blockAbove = this.bot.blockAt(blockAbovePos);

                        // Check if this is a solid block with air above it
                        if (block && block.name !== 'air' && blockAbove && blockAbove.name === 'air') {
                            // Check if bot can reach this block
                            const blockDistance = Math.sqrt((x - botPos.x) ** 2 + (y - botPos.y) ** 2 + (z - botPos.z) ** 2);
                            if (blockDistance <= 5) {
                                return {
                                    block: block,
                                    face: new Vec3(0, 1, 0) // Place on top
                                };
                            }
                        }
                    }
                }
            }
        } catch (error) {
            console.log(`Block search failed: ${error.message}`);
        }

        return null;
    }

    showEnvironmentInfo() {
        try {
            const pos = this.bot.entity.position;
            console.log(`🤖 Bot position: (${pos.x.toFixed(1)}, ${pos.y.toFixed(1)}, ${pos.z.toFixed(1)})`);

            // Try to get Vec3 from mineflayer first, fallback to require
            let Vec3;
            try {
                Vec3 = this.bot.vec3 || require('vec3');
            } catch (e) {
                console.log('Vec3 not available, using simple position objects');
                Vec3 = null;
            }

            if (Vec3) {
                const botPos = new Vec3(Math.floor(pos.x), Math.floor(pos.y), Math.floor(pos.z));
                const belowPos = new Vec3(Math.floor(pos.x), Math.floor(pos.y) - 1, Math.floor(pos.z));

                const blockBelow = this.bot.blockAt(belowPos);
                const blockAt = this.bot.blockAt(botPos);

                console.log(`🧱 Block below bot: ${blockBelow ? blockBelow.name : 'null'}`);
                console.log(`🧱 Block at bot: ${blockAt ? blockAt.name : 'null'}`);
            } else {
                console.log('🧱 Block checking disabled (Vec3 not available)');
            }

            // Check wool inventory
            const woolInfo = this.getWoolInfo();
            console.log(`🧶 Wool in inventory: ${woolInfo.count} blocks`);
        } catch (error) {
            console.log('Error showing environment info:', error.message);
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Create and start the bot
const mapArtBot = new MapArtBot();
mapArtBot.connect();

// Handle process termination
process.on('SIGINT', () => {
    console.log('Shutting down bot...');
    if (mapArtBot.bot) {
        mapArtBot.stopInventoryMonitoring();
        mapArtBot.bot.quit();
    }
    process.exit(0);
});

// Add console commands for manual inventory checking
process.stdin.on('data', (data) => {
    const command = data.toString().trim().toLowerCase();

    if (command === 'check' || command === 'inventory') {
        if (mapArtBot.bot && mapArtBot.bot.inventory) {
            mapArtBot.checkInventoryStatus();
            console.log('Manual inventory check completed.');
        } else {
            console.log('Bot not connected yet.');
        }
    } else if (command === 'help') {
        console.log('Available commands:');
        console.log('  check/inventory - Check current wool inventory');
        console.log('  help - Show this help message');
    }
});
