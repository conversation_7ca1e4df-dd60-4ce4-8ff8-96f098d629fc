const mineflayer = require('mineflayer');
const fs = require('fs');

// Load configuration
let config;
try {
    config = JSON.parse(fs.readFileSync('config.json', 'utf8'));
} catch (error) {
    console.error('Error loading config.json, using defaults:', error.message);
    config = {
        server: {
            host: 'localhost',
            port: 25565,
            username: 'MapArtBot'
        },
        building: {
            areaSize: 16,
            buildHeight: 2,
            blockType: 'white_wool',
            scaffoldingMaterials: ['dirt', 'cobblestone', 'stone', 'oak_planks']
        },
        delays: {
            betweenBlocks: 100,
            afterSpawn: 2000
        }
    };
}

const BOT_CONFIG = config.server;
const AREA_SIZE = config.building.areaSize;

class MapArtBot {
    constructor() {
        this.bot = null;
        this.isBuilding = false;
        this.startPosition = null;
        this.inventoryCheckInterval = null;
        this.lastWoolCount = 0;
    }

    connect() {
        console.log('Connecting to Minecraft server...');
        this.bot = mineflayer.createBot(BOT_CONFIG);

        this.bot.on('login', () => {
            console.log(`Bot logged in as ${this.bot.username}`);
        });

        this.bot.on('spawn', () => {
            console.log('Bot spawned in the world');
            this.startPosition = this.bot.entity.position.clone();
            console.log(`Starting position: ${this.startPosition.x}, ${this.startPosition.y}, ${this.startPosition.z}`);

            // Start inventory monitoring
            this.startInventoryMonitoring();

            // Wait a bit then start building
            setTimeout(() => {
                this.startBuilding();
            }, config.delays.afterSpawn);
        });

        this.bot.on('error', (err) => {
            console.error('Bot error:', err);
        });

        this.bot.on('end', () => {
            console.log('Bot disconnected');
            this.stopInventoryMonitoring();
        });
    }

    async startBuilding() {
        if (this.isBuilding) return;
        
        console.log('Starting to build 16x16 white wool area...');
        this.isBuilding = true;

        try {
            await this.buildArea();
        } catch (error) {
            console.error('Error during building:', error);
        } finally {
            this.isBuilding = false;
            this.stopInventoryMonitoring();
        }
    }

    async buildArea() {
        const startX = Math.floor(this.startPosition.x);
        const startZ = Math.floor(this.startPosition.z);

        console.log(`Building 16x16 area on ground level starting from (${startX}, ${startZ})`);

        for (let x = 0; x < AREA_SIZE; x++) {
            for (let z = 0; z < AREA_SIZE; z++) {
                const targetX = startX + x;
                const targetZ = startZ + z;

                // Check if we have white wool
                const woolInfo = this.getWoolInfo();
                if (!woolInfo.hasWool) {
                    console.log('❌ No white wool found in inventory! Stopping build.');
                    return;
                }

                try {
                    await this.placeWoolOnGround(targetX, targetZ);
                    const progress = ((x * AREA_SIZE + z + 1) / (AREA_SIZE * AREA_SIZE) * 100).toFixed(1);
                    const remainingWool = this.getWoolInfo().count;
                    console.log(`✅ Placed wool at (${targetX}, ?, ${targetZ}) - Progress: ${progress}% - Wool left: ${remainingWool}`);

                    // Configurable delay between blocks
                    await this.sleep(config.delays.betweenBlocks);
                } catch (error) {
                    console.error(`❌ Failed to place wool at (${targetX}, ?, ${targetZ}):`, error.message);
                }
            }
        }

        console.log('Finished building 16x16 white wool area on ground!');
    }

    startInventoryMonitoring() {
        if (!config.monitoring.enableInventoryChecks) return;

        // Check inventory at configurable intervals
        this.inventoryCheckInterval = setInterval(() => {
            this.checkInventoryStatus();
        }, config.delays.inventoryCheck);

        // Initial check
        this.checkInventoryStatus();
    }

    stopInventoryMonitoring() {
        if (this.inventoryCheckInterval) {
            clearInterval(this.inventoryCheckInterval);
            this.inventoryCheckInterval = null;
        }
    }

    checkInventoryStatus() {
        const woolInfo = this.getWoolInfo();

        if (woolInfo.count !== this.lastWoolCount) {
            if (woolInfo.count === 0) {
                console.log('❌ No white wool found in inventory!');
            } else {
                console.log(`🧶 White wool in inventory: ${woolInfo.count} blocks`);
            }
            this.lastWoolCount = woolInfo.count;
        }
    }

    getWoolInfo() {
        const whiteWool = this.bot.inventory.items().find(item =>
            item.name === config.building.blockType ||
            item.name === 'white_wool' ||
            item.name === 'wool'
        );

        return {
            item: whiteWool,
            count: whiteWool ? whiteWool.count : 0,
            hasWool: whiteWool && whiteWool.count > 0
        };
    }

    hasWhiteWool() {
        return this.getWoolInfo().hasWool;
    }

    async placeWoolOnGround(x, z) {
        // Get white wool info
        const woolInfo = this.getWoolInfo();

        if (!woolInfo.hasWool) {
            throw new Error('No white wool in inventory');
        }

        // Equip white wool
        await this.bot.equip(woolInfo.item, 'hand');

        // Move to the target position
        await this.moveToPosition(x, z);

        // Find the ground level at this position
        const groundY = await this.findGroundLevel(x, z);

        if (groundY === null) {
            throw new Error(`Could not find ground level at (${x}, ${z})`);
        }

        // Check if there's already a block at ground level
        const existingBlock = this.bot.blockAt({ x: x, y: groundY, z: z });
        if (existingBlock && existingBlock.name !== 'air') {
            console.log(`Block already exists at (${x}, ${groundY}, ${z}): ${existingBlock.name}`);
            return;
        }

        // Get the block below ground level to place on
        const blockBelow = this.bot.blockAt({ x: x, y: groundY - 1, z: z });
        if (blockBelow && blockBelow.name !== 'air') {
            await this.bot.placeBlock(blockBelow, { x: 0, y: 1, z: 0 });
        } else {
            throw new Error(`No solid block to place on at (${x}, ${groundY - 1}, ${z})`);
        }
    }

    async findGroundLevel(x, z) {
        // Start from bot's current Y level and search down for solid ground
        const startY = Math.floor(this.bot.entity.position.y);

        // Search down from current position to find solid ground
        for (let y = startY; y >= startY - 10; y--) {
            const block = this.bot.blockAt({ x: x, y: y, z: z });
            const blockBelow = this.bot.blockAt({ x: x, y: y - 1, z: z });

            // If current block is air and block below is solid, this is ground level
            if (block && block.name === 'air' && blockBelow && blockBelow.name !== 'air') {
                return y;
            }
        }

        // If no ground found, use current Y level
        return startY;
    }

    async moveToPosition(x, z) {
        try {
            // Try to use pathfinder if available
            const pathfinder = require('mineflayer-pathfinder');
            if (!this.bot.pathfinder) {
                this.bot.loadPlugin(pathfinder.pathfinder);
            }

            const { GoalBlock } = pathfinder.goals;
            const groundY = await this.findGroundLevel(x, z);
            const goal = new GoalBlock(x, groundY, z);

            await this.bot.pathfinder.goto(goal);
        } catch (error) {
            console.log(`Pathfinding failed to (${x}, ${z}), trying simple movement`);
            // Simple movement fallback
            await this.bot.lookAt({ x: x, y: this.bot.entity.position.y, z: z });

            // Move closer if too far
            const distance = Math.sqrt((x - this.bot.entity.position.x) ** 2 + (z - this.bot.entity.position.z) ** 2);
            if (distance > 4) {
                // Move towards the target position
                const dx = x - this.bot.entity.position.x;
                const dz = z - this.bot.entity.position.z;
                const moveX = this.bot.entity.position.x + (dx / distance) * 3;
                const moveZ = this.bot.entity.position.z + (dz / distance) * 3;

                await this.bot.lookAt({ x: moveX, y: this.bot.entity.position.y, z: moveZ });
            }
        }
    }

    // Scaffolding methods removed - no longer needed for ground-level building

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Create and start the bot
const mapArtBot = new MapArtBot();
mapArtBot.connect();

// Handle process termination
process.on('SIGINT', () => {
    console.log('Shutting down bot...');
    if (mapArtBot.bot) {
        mapArtBot.stopInventoryMonitoring();
        mapArtBot.bot.quit();
    }
    process.exit(0);
});

// Add console commands for manual inventory checking
process.stdin.on('data', (data) => {
    const command = data.toString().trim().toLowerCase();

    if (command === 'check' || command === 'inventory') {
        if (mapArtBot.bot && mapArtBot.bot.inventory) {
            mapArtBot.checkInventoryStatus();
            console.log('Manual inventory check completed.');
        } else {
            console.log('Bot not connected yet.');
        }
    } else if (command === 'help') {
        console.log('Available commands:');
        console.log('  check/inventory - Check current wool inventory');
        console.log('  help - Show this help message');
    }
});
