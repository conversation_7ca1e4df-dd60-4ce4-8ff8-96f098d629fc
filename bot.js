const mineflayer = require('mineflayer');
const fs = require('fs');
const Vec3 = require('vec3');

// Load configuration
let config;
try {
    config = JSON.parse(fs.readFileSync('config.json', 'utf8'));
} catch (error) {
    console.error('Error loading config.json, using defaults:', error.message);
    config = {
        server: {
            host: 'localhost',
            port: 25565,
            username: 'SmartMapArtBot'
        },
        building: {
            areaSize: 16,
            blockType: 'white_wool',
            maxReach: 4.5,
            moveTimeout: 5000,
            enableJumping: true,
            stuckDetectionTime: 2000
        },
        delays: {
            betweenBlocks: 200,
            afterSpawn: 3000,
            inventoryCheck: 5000,
            movement: 100,
            jumpSequence: 300,
            inventoryOperation: 100
        },
        monitoring: {
            enableInventoryChecks: true,
            showDetailedProgress: true
        }
    };
}

const BOT_CONFIG = config.server;
const AREA_SIZE = config.building.areaSize;

class MapArtBot {
    constructor() {
        this.bot = null;
        this.isBuilding = false;
        this.startPosition = null;
        this.inventoryCheckInterval = null;
        this.lastWoolCount = 0;
        this.buildGrid = [];
        this.currentTarget = null;
        this.placedBlocks = 0;
        this.failedPlacements = [];
        this.inventoryReady = true;
        this.isCreativeMode = false;
    }

    connect() {
        console.log('Connecting to Minecraft server...');
        this.bot = mineflayer.createBot(BOT_CONFIG);

        this.bot.on('login', () => {
            console.log(`Bot logged in as ${this.bot.username}`);
        });

        this.bot.on('spawn', () => {
            console.log('Bot spawned in the world');
            this.startPosition = this.bot.entity.position.clone();
            console.log(`Starting position: ${this.startPosition.x.toFixed(1)}, ${this.startPosition.y.toFixed(1)}, ${this.startPosition.z.toFixed(1)}`);

            // Detect game mode
            this.detectGameMode();

            // Start inventory monitoring
            this.startInventoryMonitoring();
            
            // Show some debug info about the environment
            setTimeout(() => {
                this.showEnvironmentInfo();
            }, 1000);
            
            // Wait a bit then start building
            setTimeout(() => {
                this.startBuilding();
            }, config.delays?.afterSpawn || 3000);
        });

        this.bot.on('error', (err) => {
            console.error('Bot error:', err);
        });

        this.bot.on('end', () => {
            console.log('Bot disconnected');
            this.stopInventoryMonitoring();
        });
    }

    detectGameMode() {
        // Check if bot has creative abilities
        this.isCreativeMode = this.bot.creative !== undefined;
        console.log(`🎮 Game mode detected: ${this.isCreativeMode ? 'Creative' : 'Survival'}`);
    }

    async startBuilding() {
        if (this.isBuilding) return;
        
        console.log('Starting to build 16x16 white wool area...');
        this.isBuilding = true;

        try {
            await this.buildArea();
        } catch (error) {
            console.error('Error during building:', error);
        } finally {
            this.isBuilding = false;
            this.stopInventoryMonitoring();
        }
    }

    async buildArea() {
        const startX = Math.floor(this.startPosition.x);
        const startZ = Math.floor(this.startPosition.z);
        
        console.log(`🏗️ Building exact 16x16 area starting from (${startX}, ${startZ})`);
        console.log(`📍 Target area: (${startX}, ${startZ}) to (${startX + 15}, ${startZ + 15})`);

        // Initialize build grid
        this.initializeBuildGrid(startX, startZ);
        
        // Build systematically with smart movement
        await this.smartBuild();
        
        // Report final results
        this.reportBuildResults();
    }

    initializeBuildGrid(startX, startZ) {
        this.buildGrid = [];
        for (let x = 0; x < AREA_SIZE; x++) {
            for (let z = 0; z < AREA_SIZE; z++) {
                this.buildGrid.push({
                    x: startX + x,
                    z: startZ + z,
                    placed: false,
                    attempts: 0,
                    lastAttempt: 0
                });
            }
        }
        console.log(`📋 Initialized build grid with ${this.buildGrid.length} positions`);
    }

    async smartBuild() {
        let maxAttempts = 3;
        let currentAttempt = 1;
        
        while (currentAttempt <= maxAttempts) {
            console.log(`\n🔄 Build attempt ${currentAttempt}/${maxAttempts}`);
            
            const unplacedBlocks = this.buildGrid.filter(pos => !pos.placed);
            if (unplacedBlocks.length === 0) {
                console.log('🎉 All blocks placed successfully!');
                break;
            }
            
            console.log(`📊 Remaining blocks to place: ${unplacedBlocks.length}`);
            
            // Sort by distance from bot for efficient movement
            const sortedPositions = this.sortPositionsByDistance(unplacedBlocks);
            
            for (const position of sortedPositions) {
                // Check wool availability
                const woolInfo = this.getWoolInfo();
                if (!woolInfo.hasWool) {
                    console.log('❌ No white wool found in inventory! Stopping build.');
                    return;
                }
                
                try {
                    await this.placeBlockAtPosition(position);
                    this.placedBlocks++;
                    position.placed = true;
                    
                    const progress = (this.placedBlocks / (AREA_SIZE * AREA_SIZE) * 100).toFixed(1);
                    console.log(`✅ Placed ${this.placedBlocks}/${AREA_SIZE * AREA_SIZE} (${progress}%) - Wool left: ${woolInfo.count}`);
                    
                    await this.sleep(config.delays?.betweenBlocks || 200);
                } catch (error) {
                    position.attempts++;
                    position.lastAttempt = currentAttempt;
                    console.error(`❌ Failed to place at (${position.x}, ${position.z}): ${error.message}`);
                }
            }
            
            currentAttempt++;
        }
    }

    sortPositionsByDistance(positions) {
        const botPos = this.bot.entity.position;
        return positions.sort((a, b) => {
            const distA = Math.sqrt((a.x - botPos.x) ** 2 + (a.z - botPos.z) ** 2);
            const distB = Math.sqrt((b.x - botPos.x) ** 2 + (b.z - botPos.z) ** 2);
            return distA - distB;
        });
    }

    async placeBlockAtPosition(position) {
        this.currentTarget = position;
        
        // Get white wool info
        const woolInfo = this.getWoolInfo();
        if (!woolInfo.hasWool) {
            throw new Error('No white wool in inventory');
        }

        // Ensure we have wool in hand (with proper inventory management)
        await this.ensureWoolInHand(woolInfo);

        // Move to optimal position for placement
        await this.moveToOptimalPosition(position.x, position.z);

        // Find the exact placement spot
        const placementInfo = await this.findExactPlacementSpot(position.x, position.z);
        
        if (!placementInfo) {
            throw new Error(`No suitable placement spot found at (${position.x}, ${position.z})`);
        }

        // Verify we can reach the placement spot
        if (!this.canReachPosition(placementInfo.targetPos)) {
            throw new Error(`Cannot reach placement position (${position.x}, ${position.z})`);
        }

        // Place the wool block
        await this.bot.placeBlock(placementInfo.referenceBlock, placementInfo.face);
        
        // Handle potential stuck situation after placement
        await this.sleep(100);
        await this.handleStuckSituation();
        
        // Verify placement was successful
        await this.sleep(100);
        const placedBlock = this.bot.blockAt(placementInfo.targetPos);
        if (!placedBlock || placedBlock.name === 'air') {
            throw new Error(`Block placement verification failed at (${position.x}, ${position.z})`);
        }
        
        console.log(`🧱 Placed wool at exact position (${placementInfo.targetPos.x}, ${placementInfo.targetPos.y}, ${placementInfo.targetPos.z})`);
        
        // Final position adjustment if needed
        await this.adjustPositionAfterPlacement();
    }

    async ensureWoolInHand(woolInfo) {
        if (!this.inventoryReady) {
            await this.sleep(config.delays?.inventoryOperation || 100);
        }

        this.inventoryReady = false;

        try {
            if (this.isCreativeMode && this.bot.creative) {
                // Creative mode - set inventory slots with proper delays
                await this.setCreativeInventory(woolInfo.item);
            } else {
                // Survival mode - equip existing wool
                await this.bot.equip(woolInfo.item, 'hand');
            }
            
            // Additional delay to ensure inventory is ready
            await this.sleep(config.delays?.inventoryOperation || 100);
            
        } catch (error) {
            throw new Error(`Failed to prepare wool in hand: ${error.message}`);
        } finally {
            this.inventoryReady = true;
        }
    }

    async setCreativeInventory(woolItem) {
        // For newer Minecraft versions, we need to be more careful with creative inventory
        try {
            // Only set the hotbar slot we need (slot 36 = first hotbar slot)
            await this.bot.creative.setInventorySlot(36, woolItem);
            await this.sleep(50); // Small delay
            
            // Equip the item from the hotbar
            await this.bot.equip(woolItem, 'hand');
            
        } catch (error) {
            console.log(`⚠️ Creative inventory failed, trying survival equip: ${error.message}`);
            // Fallback to survival mode equipping
            await this.bot.equip(woolItem, 'hand');
        }
    }

    async moveToOptimalPosition(targetX, targetZ) {
        const maxReach = config.building?.maxReach || 4.5;
        const botPos = this.bot.entity.position;
        const distance = Math.sqrt((targetX - botPos.x) ** 2 + (targetZ - botPos.z) ** 2);

        if (distance <= maxReach) {
            console.log(`🎯 Already in range of (${targetX}, ${targetZ}) - distance: ${distance.toFixed(1)}`);
            await this.handleStuckSituation();
            return;
        }

        console.log(`🚶 Moving to optimal position for (${targetX}, ${targetZ}) - current distance: ${distance.toFixed(1)}`);

        // Calculate optimal position (within reach but not too close)
        const optimalDistance = maxReach - 1;
        const dx = targetX - botPos.x;
        const dz = targetZ - botPos.z;
        const currentDistance = Math.sqrt(dx * dx + dz * dz);

        const moveX = targetX - (dx / currentDistance) * optimalDistance;
        const moveZ = targetZ - (dz / currentDistance) * optimalDistance;
        const moveY = await this.findSafeYLevel(moveX, moveZ);

        const targetPos = new Vec3(moveX, moveY, moveZ);

        try {
            // Use pathfinder for precise movement with jumping enabled
            const pathfinder = require('mineflayer-pathfinder');
            if (!this.bot.pathfinder) {
                this.bot.loadPlugin(pathfinder.pathfinder);
                this.bot.pathfinder.setMovements(new pathfinder.Movements(this.bot));
            }

            const { GoalNear } = pathfinder.goals;
            const goal = new GoalNear(targetPos.x, targetPos.y, targetPos.z, 1);

            await Promise.race([
                this.bot.pathfinder.goto(goal),
                this.sleep(config.building?.moveTimeout || 5000)
            ]);

            const finalDistance = Math.sqrt((targetX - this.bot.entity.position.x) ** 2 + (targetZ - this.bot.entity.position.z) ** 2);
            console.log(`📍 Final distance to target: ${finalDistance.toFixed(1)}`);

        } catch (error) {
            console.log(`⚠️ Pathfinding failed, trying manual movement: ${error.message}`);
            await this.manualMovementWithJumping(targetX, targetZ);
        }
    }

    async findSafeYLevel(x, z) {
        const botY = Math.floor(this.bot.entity.position.y);

        for (let y = botY + 2; y >= botY - 5; y--) {
            const blockPos = new Vec3(Math.floor(x), y, Math.floor(z));
            const blockAbovePos = new Vec3(Math.floor(x), y + 1, Math.floor(z));
            const blockBelowPos = new Vec3(Math.floor(x), y - 1, Math.floor(z));

            const block = this.bot.blockAt(blockPos);
            const blockAbove = this.bot.blockAt(blockAbovePos);
            const blockBelow = this.bot.blockAt(blockBelowPos);

            if (block && block.name === 'air' &&
                blockAbove && blockAbove.name === 'air' &&
                blockBelow && blockBelow.name !== 'air') {
                return y;
            }
        }

        return botY;
    }

    async findExactPlacementSpot(targetX, targetZ) {
        const botPos = this.bot.entity.position;
        const maxReach = config.building?.maxReach || 4.5;

        // First, try to place exactly at the target coordinates
        for (let y = Math.floor(botPos.y) + 2; y >= Math.floor(botPos.y) - 3; y--) {
            const targetPos = new Vec3(targetX, y, targetZ);
            const belowPos = new Vec3(targetX, y - 1, targetZ);

            const targetBlock = this.bot.blockAt(targetPos);
            const belowBlock = this.bot.blockAt(belowPos);

            if (targetBlock && targetBlock.name === 'air' &&
                belowBlock && belowBlock.name !== 'air') {

                const distance = botPos.distanceTo(targetPos);
                if (distance <= maxReach) {
                    return {
                        targetPos: targetPos,
                        referenceBlock: belowBlock,
                        face: new Vec3(0, 1, 0)
                    };
                }
            }
        }

        // If exact placement fails, try adjacent positions
        const offsets = [[0, 0], [1, 0], [-1, 0], [0, 1], [0, -1]];

        for (const [dx, dz] of offsets) {
            const x = targetX + dx;
            const z = targetZ + dz;

            for (let y = Math.floor(botPos.y) + 2; y >= Math.floor(botPos.y) - 3; y--) {
                const targetPos = new Vec3(x, y, z);
                const belowPos = new Vec3(x, y - 1, z);

                const targetBlock = this.bot.blockAt(targetPos);
                const belowBlock = this.bot.blockAt(belowPos);

                if (targetBlock && targetBlock.name === 'air' &&
                    belowBlock && belowBlock.name !== 'air') {

                    const distance = botPos.distanceTo(targetPos);
                    if (distance <= maxReach) {
                        return {
                            targetPos: targetPos,
                            referenceBlock: belowBlock,
                            face: new Vec3(0, 1, 0)
                        };
                    }
                }
            }
        }

        return null;
    }

    canReachPosition(position) {
        const botPos = this.bot.entity.position;
        const distance = botPos.distanceTo(position);
        const maxReach = config.building?.maxReach || 4.5;
        return distance <= maxReach;
    }

    async handleStuckSituation() {
        const botPos = this.bot.entity.position;

        const blockBelow = this.bot.blockAt(new Vec3(Math.floor(botPos.x), Math.floor(botPos.y) - 1, Math.floor(botPos.z)));
        const blockAt = this.bot.blockAt(new Vec3(Math.floor(botPos.x), Math.floor(botPos.y), Math.floor(botPos.z)));

        if (blockBelow && blockBelow.name === 'white_wool') {
            console.log(`🦘 Bot is on placed wool, jumping to get better position`);
            await this.smartJump();
        }

        if (blockAt && blockAt.name !== 'air') {
            console.log(`🆙 Bot is inside a block, jumping up`);
            await this.smartJump();
        }
    }

    async smartJump() {
        console.log(`🦘 Executing smart jump sequence`);

        this.bot.setControlState('jump', true);
        await this.sleep(100);
        this.bot.setControlState('jump', false);

        this.bot.setControlState('forward', true);
        await this.sleep(300);
        this.bot.setControlState('forward', false);

        await this.sleep(200);

        const botPos = this.bot.entity.position;
        const blockAt = this.bot.blockAt(new Vec3(Math.floor(botPos.x), Math.floor(botPos.y), Math.floor(botPos.z)));

        if (blockAt && blockAt.name !== 'air') {
            console.log(`🆙 Still inside block, jumping again`);
            this.bot.setControlState('jump', true);
            await this.sleep(100);
            this.bot.setControlState('jump', false);
            await this.sleep(300);
        }
    }

    async manualMovementWithJumping(targetX, targetZ) {
        const botPos = this.bot.entity.position;

        const lookTarget = new Vec3(targetX, botPos.y, targetZ);
        await this.bot.lookAt(lookTarget);

        const blockInFront = this.bot.blockAt(new Vec3(
            Math.floor(botPos.x + (targetX - botPos.x) * 0.5),
            Math.floor(botPos.y),
            Math.floor(botPos.z + (targetZ - botPos.z) * 0.5)
        ));

        if (blockInFront && blockInFront.name !== 'air') {
            console.log(`🦘 Block detected in path, jumping while moving`);
            this.bot.setControlState('jump', true);
        }

        this.bot.setControlState('forward', true);
        await this.sleep(1000);
        this.bot.setControlState('forward', false);
        this.bot.setControlState('jump', false);

        await this.sleep(config.delays?.movement || 100);
    }

    async adjustPositionAfterPlacement() {
        const botPos = this.bot.entity.position;

        const surroundingBlocks = [
            new Vec3(Math.floor(botPos.x) + 1, Math.floor(botPos.y), Math.floor(botPos.z)),
            new Vec3(Math.floor(botPos.x) - 1, Math.floor(botPos.y), Math.floor(botPos.z)),
            new Vec3(Math.floor(botPos.x), Math.floor(botPos.y), Math.floor(botPos.z) + 1),
            new Vec3(Math.floor(botPos.x), Math.floor(botPos.y), Math.floor(botPos.z) - 1)
        ];

        let blockedSides = 0;
        for (const pos of surroundingBlocks) {
            const block = this.bot.blockAt(pos);
            if (block && block.name === 'white_wool') {
                blockedSides++;
            }
        }

        if (blockedSides >= 3) {
            console.log(`🚫 Bot surrounded by ${blockedSides} wool blocks, jumping to better position`);
            await this.smartJump();

            this.bot.setControlState('back', true);
            await this.sleep(300);
            this.bot.setControlState('back', false);
        }
    }

    startInventoryMonitoring() {
        if (!config.monitoring || !config.monitoring.enableInventoryChecks) return;

        const checkInterval = config.delays?.inventoryCheck || 5000;
        this.inventoryCheckInterval = setInterval(() => {
            this.checkInventoryStatus();
        }, checkInterval);

        this.checkInventoryStatus();
    }

    stopInventoryMonitoring() {
        if (this.inventoryCheckInterval) {
            clearInterval(this.inventoryCheckInterval);
            this.inventoryCheckInterval = null;
        }
    }

    checkInventoryStatus() {
        const woolInfo = this.getWoolInfo();

        if (woolInfo.count !== this.lastWoolCount) {
            if (woolInfo.count === 0) {
                console.log('❌ No white wool found in inventory!');
            } else {
                console.log(`🧶 White wool in inventory: ${woolInfo.count} blocks`);
            }
            this.lastWoolCount = woolInfo.count;
        }
    }

    getWoolInfo() {
        const blockType = config.building?.blockType || 'white_wool';
        const whiteWool = this.bot.inventory.items().find(item =>
            item.name === blockType ||
            item.name === 'white_wool' ||
            item.name === 'wool'
        );

        return {
            item: whiteWool,
            count: whiteWool ? whiteWool.count : 0,
            hasWool: whiteWool && whiteWool.count > 0
        };
    }

    hasWhiteWool() {
        return this.getWoolInfo().hasWool;
    }

    reportBuildResults() {
        const totalBlocks = AREA_SIZE * AREA_SIZE;
        const placedCount = this.buildGrid.filter(pos => pos.placed).length;
        const failedCount = totalBlocks - placedCount;

        console.log('\n📊 BUILD RESULTS:');
        console.log(`✅ Successfully placed: ${placedCount}/${totalBlocks} blocks (${(placedCount/totalBlocks*100).toFixed(1)}%)`);

        if (failedCount > 0) {
            console.log(`❌ Failed placements: ${failedCount} blocks`);
            console.log('Failed positions:');
            this.buildGrid.filter(pos => !pos.placed).forEach(pos => {
                console.log(`  - (${pos.x}, ${pos.z}) - ${pos.attempts} attempts`);
            });
        } else {
            console.log('🎉 Perfect 16x16 area completed!');
        }

        console.log(`🧶 Total wool used: ${this.placedBlocks} blocks`);
        console.log(`⏱️ Build completed`);
    }

    showEnvironmentInfo() {
        try {
            const pos = this.bot.entity.position;
            console.log(`🤖 Bot position: (${pos.x.toFixed(1)}, ${pos.y.toFixed(1)}, ${pos.z.toFixed(1)})`);

            const botPos = new Vec3(Math.floor(pos.x), Math.floor(pos.y), Math.floor(pos.z));
            const belowPos = new Vec3(Math.floor(pos.x), Math.floor(pos.y) - 1, Math.floor(pos.z));

            const blockBelow = this.bot.blockAt(belowPos);
            const blockAt = this.bot.blockAt(botPos);

            console.log(`🧱 Block below bot: ${blockBelow ? blockBelow.name : 'null'}`);
            console.log(`🧱 Block at bot: ${blockAt ? blockAt.name : 'null'}`);

            const woolInfo = this.getWoolInfo();
            console.log(`🧶 Wool in inventory: ${woolInfo.count} blocks`);
            console.log(`🎯 Max reach distance: ${config.building?.maxReach || 4.5} blocks`);
        } catch (error) {
            console.log('Error showing environment info:', error.message);
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Create and start the bot
const mapArtBot = new MapArtBot();
mapArtBot.connect();

// Handle process termination
process.on('SIGINT', () => {
    console.log('Shutting down bot...');
    if (mapArtBot.bot) {
        mapArtBot.stopInventoryMonitoring();
        mapArtBot.bot.quit();
    }
    process.exit(0);
});

// Add console commands for manual inventory checking
process.stdin.on('data', (data) => {
    const command = data.toString().trim().toLowerCase();

    if (command === 'check' || command === 'inventory') {
        if (mapArtBot.bot && mapArtBot.bot.inventory) {
            mapArtBot.checkInventoryStatus();
            console.log('Manual inventory check completed.');
        } else {
            console.log('Bot not connected yet.');
        }
    } else if (command === 'help') {
        console.log('Available commands:');
        console.log('  check/inventory - Check current wool inventory');
        console.log('  help - Show this help message');
    }
});
