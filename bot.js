const mineflayer = require('mineflayer');
const fs = require('fs');

// Load configuration
let config;
try {
    config = JSON.parse(fs.readFileSync('config.json', 'utf8'));
} catch (error) {
    console.error('Error loading config.json, using defaults:', error.message);
    config = {
        server: {
            host: 'localhost',
            port: 25565,
            username: 'MapArtBot'
        },
        building: {
            areaSize: 16,
            buildHeight: 2,
            blockType: 'white_wool',
            scaffoldingMaterials: ['dirt', 'cobblestone', 'stone', 'oak_planks']
        },
        delays: {
            betweenBlocks: 100,
            afterSpawn: 2000
        }
    };
}

const BOT_CONFIG = config.server;
const AREA_SIZE = config.building.areaSize;
const BUILD_HEIGHT = config.building.buildHeight;

class MapArtBot {
    constructor() {
        this.bot = null;
        this.isBuilding = false;
        this.startPosition = null;
        this.scaffoldingBlocks = [];
    }

    connect() {
        console.log('Connecting to Minecraft server...');
        this.bot = mineflayer.createBot(BOT_CONFIG);

        this.bot.on('login', () => {
            console.log(`Bot logged in as ${this.bot.username}`);
        });

        this.bot.on('spawn', () => {
            console.log('Bot spawned in the world');
            this.startPosition = this.bot.entity.position.clone();
            console.log(`Starting position: ${this.startPosition.x}, ${this.startPosition.y}, ${this.startPosition.z}`);

            
            setTimeout(() => {
                this.startBuilding();
            }, 2000);
        });

        this.bot.on('error', (err) => {
            console.error('Bot error:', err);
        });

        this.bot.on('end', () => {
            console.log('Bot disconnected');
        });
    }

    async startBuilding() {
        if (this.isBuilding) return;
        
        console.log('Starting to build 16x16 white wool area...');
        this.isBuilding = true;

        try {
            await this.buildArea();
        } catch (error) {
            console.error('Error during building:', error);
        } finally {
            this.isBuilding = false;
            await this.cleanupScaffolding();
        }
    }

    async buildArea() {
        const startX = Math.floor(this.startPosition.x);
        const startZ = Math.floor(this.startPosition.z);
        const buildY = Math.floor(this.startPosition.y) + BUILD_HEIGHT;

        console.log(`Building area from (${startX}, ${buildY}, ${startZ}) to (${startX + AREA_SIZE - 1}, ${buildY}, ${startZ + AREA_SIZE - 1})`);

        for (let x = 0; x < AREA_SIZE; x++) {
            for (let z = 0; z < AREA_SIZE; z++) {
                const targetX = startX + x;
                const targetZ = startZ + z;
                const targetPos = { x: targetX, y: buildY, z: targetZ };

                // Check if we have white wool
                if (!this.hasWhiteWool()) {
                    console.log('No white wool found in inventory!');
                    return;
                }

                try {
                    await this.placeWhiteWoolAt(targetPos);
                    console.log(`Placed white wool at (${targetX}, ${buildY}, ${targetZ}) - Progress: ${((x * AREA_SIZE + z + 1) / (AREA_SIZE * AREA_SIZE) * 100).toFixed(1)}%`);
                    
                    // Small delay to prevent spam
                    await this.sleep(100);
                } catch (error) {
                    console.error(`Failed to place wool at (${targetX}, ${buildY}, ${targetZ}):`, error.message);
                }
            }
        }

        console.log('Finished building 16x16 white wool area!');
    }

    hasWhiteWool() {
        const whiteWool = this.bot.inventory.items().find(item =>
            item.name === config.building.blockType ||
            item.name === 'white_wool' ||
            item.name === 'wool'
        );
        return whiteWool && whiteWool.count > 0;
    }

    async placeWhiteWoolAt(position) {
        // Find white wool in inventory
        const whiteWool = this.bot.inventory.items().find(item => 
            item.name === 'white_wool' || item.name === 'wool'
        );

        if (!whiteWool) {
            throw new Error('No white wool in inventory');
        }

        // Equip white wool
        await this.bot.equip(whiteWool, 'hand');

        // Check if we need scaffolding to reach the position
        await this.ensureCanReach(position);

        // Get the block below the target position for reference
        const blockBelow = this.bot.blockAt({ x: position.x, y: position.y - 1, z: position.z });
        
        if (!blockBelow) {
            // If no block below, we need to build scaffolding
            await this.buildScaffolding(position);
        }

        // Move close to the target position
        await this.moveNear(position);

        // Place the block
        const referenceBlock = this.bot.blockAt({ x: position.x, y: position.y - 1, z: position.z });
        if (referenceBlock) {
            await this.bot.placeBlock(referenceBlock, { x: 0, y: 1, z: 0 });
        } else {
            throw new Error('No reference block to place against');
        }
    }

    async ensureCanReach(position) {
        const distance = this.bot.entity.position.distanceTo(position);
        if (distance > 4.5) {
            await this.moveNear(position);
        }
    }

    async moveNear(position) {
        const pathfinder = require('mineflayer-pathfinder');
        if (!this.bot.pathfinder) {
            this.bot.loadPlugin(pathfinder.pathfinder);
        }

        const { GoalNear } = pathfinder.goals;
        const goal = new GoalNear(position.x, position.y - 1, position.z, 3);
        
        try {
            await this.bot.pathfinder.goto(goal);
        } catch (error) {
            console.log(`Pathfinding failed, trying to move manually near (${position.x}, ${position.y}, ${position.z})`);
            // Manual movement as fallback
            await this.bot.lookAt(position);
        }
    }

    async buildScaffolding(targetPosition) {
        // Build a simple pillar to reach the target height
        const botY = Math.floor(this.bot.entity.position.y);
        const targetY = targetPosition.y;
        
        if (targetY <= botY + 1) return; // No scaffolding needed

        // Use dirt or cobblestone for scaffolding
        const scaffoldMaterial = this.bot.inventory.items().find(item => 
            item.name === 'dirt' || item.name === 'cobblestone' || item.name === 'stone'
        );

        if (!scaffoldMaterial) {
            console.log('No scaffolding material found, trying to place without scaffolding');
            return;
        }

        console.log(`Building scaffolding to reach height ${targetY}`);
        
        // Build pillar next to target position
        const scaffoldX = targetPosition.x + 1;
        const scaffoldZ = targetPosition.z;

        for (let y = botY + 1; y < targetY; y++) {
            const scaffoldPos = { x: scaffoldX, y: y, z: scaffoldZ };
            try {
                await this.bot.equip(scaffoldMaterial, 'hand');
                const blockBelow = this.bot.blockAt({ x: scaffoldX, y: y - 1, z: scaffoldZ });
                if (blockBelow) {
                    await this.bot.placeBlock(blockBelow, { x: 0, y: 1, z: 0 });
                    this.scaffoldingBlocks.push(scaffoldPos);
                }
            } catch (error) {
                console.log(`Failed to place scaffolding at height ${y}:`, error.message);
            }
        }
    }

    async cleanupScaffolding() {
        if (this.scaffoldingBlocks.length === 0) return;

        console.log('Cleaning up scaffolding...');
        
        for (const pos of this.scaffoldingBlocks) {
            try {
                const block = this.bot.blockAt(pos);
                if (block) {
                    await this.moveNear(pos);
                    await this.bot.dig(block);
                }
            } catch (error) {
                console.log(`Failed to remove scaffolding at (${pos.x}, ${pos.y}, ${pos.z}):`, error.message);
            }
        }

        this.scaffoldingBlocks = [];
        console.log('Scaffolding cleanup completed');
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Create and start the bot
const mapArtBot = new MapArtBot();
mapArtBot.connect();

// Handle process termination
process.on('SIGINT', () => {
    console.log('Shutting down bot...');
    if (mapArtBot.bot) {
        mapArtBot.bot.quit();
    }
    process.exit(0);
});
