[{"id": 0, "stackSize": 64, "name": "air", "displayName": "Air"}, {"id": 1, "displayName": "Stone", "name": "stone", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 0, "id": 3, "name": "polished_granite", "displayName": "Polished Granite", "stackSize": 64}, {"metadata": 1, "id": 2, "name": "granite", "displayName": "Granite", "stackSize": 64}, {"metadata": 3, "id": 4, "name": "diorite", "displayName": "Diorite", "stackSize": 64}, {"metadata": 4, "id": 5, "name": "polished_diorite", "displayName": "Polished Diorite", "stackSize": 64}, {"metadata": 5, "id": 6, "name": "andesite", "displayName": "Andesite", "stackSize": 64}, {"metadata": 6, "id": 7, "name": "polished_andesite", "displayName": "Polished Andesite", "stackSize": 64}]}, {"id": 8, "stackSize": 64, "name": "deepslate", "displayName": "Deepslate"}, {"id": 9, "stackSize": 64, "name": "cobbled_deepslate", "displayName": "Cobbled Deepslate"}, {"id": 10, "stackSize": 64, "name": "polished_deepslate", "displayName": "Polished Deepslate"}, {"id": 11, "stackSize": 64, "name": "calcite", "displayName": "Calcite"}, {"id": 12, "stackSize": 64, "name": "tuff", "displayName": "<PERSON><PERSON>"}, {"id": 13, "stackSize": 64, "name": "dripstone_block", "displayName": "Dripstone Block"}, {"id": 14, "stackSize": 64, "name": "grass", "displayName": "Grass Block"}, {"id": 15, "displayName": "Dirt", "name": "dirt", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 16, "name": "coarse_dirt", "displayName": "Coarse Dirt", "stackSize": 64}]}, {"id": 17, "stackSize": 64, "name": "podzol", "displayName": "Podzol"}, {"id": 18, "stackSize": 64, "name": "dirt_with_roots", "displayName": "Rooted Dirt"}, {"id": 19, "stackSize": 64, "name": "mud", "displayName": "Mud"}, {"id": 20, "stackSize": 64, "name": "crimson_nylium", "displayName": "Crimson Nylium"}, {"id": 21, "stackSize": 64, "name": "warped_nylium", "displayName": "Warped Nylium"}, {"id": 22, "stackSize": 64, "name": "cobblestone", "displayName": "Cobblestone"}, {"id": 23, "displayName": "Oak Planks", "name": "planks", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 24, "name": "spruce_planks", "displayName": "Spruce Planks", "stackSize": 64}, {"metadata": 2, "id": 25, "name": "birch_planks", "displayName": "Birch Planks", "stackSize": 64}, {"metadata": 3, "id": 26, "name": "jungle_planks", "displayName": "Jungle Planks", "stackSize": 64}, {"metadata": 4, "id": 27, "name": "acacia_planks", "displayName": "Acacia Planks", "stackSize": 64}, {"metadata": 5, "id": 28, "name": "dark_oak_planks", "displayName": "Dark Oak Planks", "stackSize": 64}]}, {"id": 29, "stackSize": 64, "name": "mangrove_planks", "displayName": "Mangrove Planks"}, {"id": 30, "stackSize": 64, "name": "crimson_planks", "displayName": "Crimson Planks"}, {"id": 31, "stackSize": 64, "name": "warped_planks", "displayName": "Warped Planks"}, {"id": 32, "displayName": "Oak Sapling", "name": "sapling", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 33, "name": "spruce_sapling", "displayName": "Spruce Sapling", "stackSize": 64}, {"metadata": 2, "id": 34, "name": "birch_sapling", "displayName": "Birch Sapling", "stackSize": 64}, {"metadata": 3, "id": 35, "name": "jungle_sapling", "displayName": "Jungle Sapling", "stackSize": 64}, {"metadata": 4, "id": 36, "name": "acacia_sapling", "displayName": "Acacia Sapling", "stackSize": 64}, {"metadata": 5, "id": 37, "name": "dark_oak_sapling", "displayName": "Dark Oak Sapling", "stackSize": 64}]}, {"id": 38, "stackSize": 64, "name": "mangrove_propagule", "displayName": "Mangrove Propagule"}, {"id": 39, "stackSize": 64, "name": "bedrock", "displayName": "Bedrock"}, {"id": 40, "displayName": "Sand", "name": "sand", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 41, "name": "red_sand", "displayName": "Red Sand", "stackSize": 64}]}, {"id": 42, "stackSize": 64, "name": "gravel", "displayName": "<PERSON>l"}, {"id": 43, "stackSize": 64, "name": "coal_ore", "displayName": "Coal Ore"}, {"id": 44, "stackSize": 64, "name": "deepslate_coal_ore", "displayName": "Deepslate Coal Ore"}, {"id": 45, "stackSize": 64, "name": "iron_ore", "displayName": "Iron Ore"}, {"id": 46, "stackSize": 64, "name": "deepslate_iron_ore", "displayName": "Deepslate Iron Ore"}, {"id": 47, "stackSize": 64, "name": "copper_ore", "displayName": "Copper Ore"}, {"id": 48, "stackSize": 64, "name": "deepslate_copper_ore", "displayName": "Deepslate Copper Ore"}, {"id": 49, "stackSize": 64, "name": "gold_ore", "displayName": "Gold Ore"}, {"id": 50, "stackSize": 64, "name": "deepslate_gold_ore", "displayName": "Deepslate Gold Ore"}, {"id": 51, "stackSize": 64, "name": "redstone_ore", "displayName": "Redstone Ore"}, {"id": 52, "stackSize": 64, "name": "deepslate_redstone_ore", "displayName": "Deepslate Redstone Ore"}, {"id": 53, "stackSize": 64, "name": "emerald_ore", "displayName": "Emerald Ore"}, {"id": 54, "stackSize": 64, "name": "deepslate_emerald_ore", "displayName": "Deepslate Emerald Ore"}, {"id": 55, "stackSize": 64, "name": "lapis_ore", "displayName": "Lapis <PERSON> Ore"}, {"id": 56, "stackSize": 64, "name": "deepslate_lapis_ore", "displayName": "Deepslate Lapis Lazuli Ore"}, {"id": 57, "stackSize": 64, "name": "diamond_ore", "displayName": "Diamond Ore"}, {"id": 58, "stackSize": 64, "name": "deepslate_diamond_ore", "displayName": "Deepslate Diamond Ore"}, {"id": 59, "stackSize": 64, "name": "nether_gold_ore", "displayName": "Nether Gold Ore"}, {"id": 60, "stackSize": 64, "name": "quartz_ore", "displayName": "<PERSON><PERSON>"}, {"id": 61, "stackSize": 64, "name": "ancient_debris", "displayName": "Ancient Debris"}, {"id": 62, "stackSize": 64, "name": "coal_block", "displayName": "Block of Coal"}, {"id": 63, "stackSize": 64, "name": "raw_iron_block", "displayName": "Block of Raw Iron"}, {"id": 64, "stackSize": 64, "name": "raw_copper_block", "displayName": "Block of Raw Copper"}, {"id": 65, "stackSize": 64, "name": "raw_gold_block", "displayName": "Block of Raw Gold"}, {"id": 66, "stackSize": 64, "name": "amethyst_block", "displayName": "Block of Amethyst"}, {"id": 67, "stackSize": 64, "name": "budding_amethyst", "displayName": "Budding Amethyst"}, {"id": 68, "stackSize": 64, "name": "iron_block", "displayName": "Block of Iron"}, {"id": 69, "stackSize": 64, "name": "copper_block", "displayName": "Block of Copper"}, {"id": 70, "stackSize": 64, "name": "gold_block", "displayName": "Block of Gold"}, {"id": 71, "stackSize": 64, "name": "diamond_block", "displayName": "Block of Diamond"}, {"id": 72, "stackSize": 64, "name": "netherite_block", "displayName": "Block of Netherite"}, {"id": 73, "stackSize": 64, "name": "exposed_copper", "displayName": "Exposed Copper"}, {"id": 74, "stackSize": 64, "name": "weathered_copper", "displayName": "Weathered Copper"}, {"id": 75, "stackSize": 64, "name": "oxidized_copper", "displayName": "Oxidized Copper"}, {"id": 76, "stackSize": 64, "name": "cut_copper", "displayName": "Cut Copper"}, {"id": 77, "stackSize": 64, "name": "exposed_cut_copper", "displayName": "Exposed Cut Copper"}, {"id": 78, "stackSize": 64, "name": "weathered_cut_copper", "displayName": "Weathered Cut Copper"}, {"id": 79, "stackSize": 64, "name": "oxidized_cut_copper", "displayName": "Oxidized Cut Copper"}, {"id": 80, "stackSize": 64, "name": "cut_copper_stairs", "displayName": "Cut Copper Stairs"}, {"id": 81, "stackSize": 64, "name": "exposed_cut_copper_stairs", "displayName": "Exposed Cut Copper Stairs"}, {"id": 82, "stackSize": 64, "name": "weathered_cut_copper_stairs", "displayName": "Weathered Cut Copper Stairs"}, {"id": 83, "stackSize": 64, "name": "oxidized_cut_copper_stairs", "displayName": "Oxidized Cut Copper Stairs"}, {"id": 84, "stackSize": 64, "name": "cut_copper_slab", "displayName": "Cut Copper Slab"}, {"id": 85, "stackSize": 64, "name": "exposed_cut_copper_slab", "displayName": "Exposed Cut Copper Slab"}, {"id": 86, "stackSize": 64, "name": "weathered_cut_copper_slab", "displayName": "Weathered Cut Copper Slab"}, {"id": 87, "stackSize": 64, "name": "oxidized_cut_copper_slab", "displayName": "Oxidized Cut Copper Slab"}, {"id": 88, "stackSize": 64, "name": "waxed_copper", "displayName": "Waxed Block of Copper"}, {"id": 89, "stackSize": 64, "name": "waxed_exposed_copper", "displayName": "Waxed Exposed Copper"}, {"id": 90, "stackSize": 64, "name": "waxed_weathered_copper", "displayName": "Waxed Weathered Copper"}, {"id": 91, "stackSize": 64, "name": "waxed_oxidized_copper", "displayName": "Waxed Oxidized Copper"}, {"id": 92, "stackSize": 64, "name": "waxed_cut_copper", "displayName": "Waxed Cut Copper"}, {"id": 93, "stackSize": 64, "name": "waxed_exposed_cut_copper", "displayName": "Waxed Exposed Cut Copper"}, {"id": 94, "stackSize": 64, "name": "waxed_weathered_cut_copper", "displayName": "Waxed Weathered Cut Copper"}, {"id": 95, "stackSize": 64, "name": "waxed_oxidized_cut_copper", "displayName": "Waxed Oxidized Cut Copper"}, {"id": 96, "stackSize": 64, "name": "waxed_cut_copper_stairs", "displayName": "Waxed Cut Copper Stairs"}, {"id": 97, "stackSize": 64, "name": "waxed_exposed_cut_copper_stairs", "displayName": "Waxed Exposed Cut Copper Stairs"}, {"id": 98, "stackSize": 64, "name": "waxed_weathered_cut_copper_stairs", "displayName": "Waxed Weathered Cut Copper Stairs"}, {"id": 99, "stackSize": 64, "name": "waxed_oxidized_cut_copper_stairs", "displayName": "Waxed Oxidized Cut Copper Stairs"}, {"id": 100, "stackSize": 64, "name": "waxed_cut_copper_slab", "displayName": "Waxed Cut Copper Slab"}, {"id": 101, "stackSize": 64, "name": "waxed_exposed_cut_copper_slab", "displayName": "Waxed Exposed Cut Copper Slab"}, {"id": 102, "stackSize": 64, "name": "waxed_weathered_cut_copper_slab", "displayName": "Waxed Weathered Cut Copper Slab"}, {"id": 103, "stackSize": 64, "name": "waxed_oxidized_cut_copper_slab", "displayName": "Waxed Oxidized Cut Copper Slab"}, {"id": 104, "displayName": "Oak Log", "name": "log", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 105, "name": "spruce_log", "displayName": "Spruce Log", "stackSize": 64}, {"metadata": 2, "id": 106, "name": "birch_log", "displayName": "Birch Log", "stackSize": 64}, {"metadata": 3, "id": 107, "name": "jungle_log", "displayName": "Jungle Log", "stackSize": 64}]}, {"id": 108, "displayName": "Acacia Log", "name": "log2", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 109, "name": "dark_oak_log", "displayName": "Dark Oak Log", "stackSize": 64}]}, {"id": 110, "stackSize": 64, "name": "mangrove_log", "displayName": "Mangrove Log"}, {"id": 111, "stackSize": 64, "name": "mangrove_roots", "displayName": "Mangrove Roots"}, {"id": 112, "stackSize": 64, "name": "muddy_mangrove_roots", "displayName": "Muddy Mangrove Roots"}, {"id": 113, "stackSize": 64, "name": "crimson_stem", "displayName": "Crimson Stem"}, {"id": 114, "stackSize": 64, "name": "warped_stem", "displayName": "Warped Stem"}, {"id": 115, "stackSize": 64, "name": "stripped_oak_log", "displayName": "Stripped Oak Log"}, {"id": 116, "stackSize": 64, "name": "stripped_spruce_log", "displayName": "Stripped Spruce Log"}, {"id": 117, "stackSize": 64, "name": "stripped_birch_log", "displayName": "Stripped Birch Log"}, {"id": 118, "stackSize": 64, "name": "stripped_jungle_log", "displayName": "Stripped Jungle Log"}, {"id": 119, "stackSize": 64, "name": "stripped_acacia_log", "displayName": "Stripped Acacia Log"}, {"id": 120, "stackSize": 64, "name": "stripped_dark_oak_log", "displayName": "Stripped Dark Oak Log"}, {"id": 121, "stackSize": 64, "name": "stripped_mangrove_log", "displayName": "Stripped Mangrove Log"}, {"id": 122, "stackSize": 64, "name": "stripped_crimson_stem", "displayName": "Stripped Crimson Stem"}, {"id": 123, "stackSize": 64, "name": "stripped_warped_stem", "displayName": "Stripped Warped Stem"}, {"id": 130, "stackSize": 64, "name": "stripped_mangrove_wood", "displayName": "Stripped Mangrove Wood"}, {"id": 131, "stackSize": 64, "name": "stripped_crimson_hyphae", "displayName": "Stripped Crimson Hyphae"}, {"id": 132, "stackSize": 64, "name": "stripped_warped_hyphae", "displayName": "Stripped Warped Hyphae"}, {"id": 133, "displayName": "Oak Wood", "name": "wood", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 134, "name": "spruce_wood", "displayName": "Spruce Wood", "stackSize": 64}, {"metadata": 2, "id": 135, "name": "birch_wood", "displayName": "Birch Wood", "stackSize": 64}, {"metadata": 3, "id": 136, "name": "jungle_wood", "displayName": "Jungle Wood", "stackSize": 64}, {"metadata": 4, "id": 137, "name": "acacia_wood", "displayName": "Acacia Wood", "stackSize": 64}, {"metadata": 5, "id": 138, "name": "dark_oak_wood", "displayName": "Dark Oak Wood", "stackSize": 64}, {"metadata": 8, "id": 124, "name": "stripped_oak_wood", "displayName": "Stripped Oak Wood", "stackSize": 64}, {"metadata": 9, "id": 125, "name": "stripped_spruce_wood", "displayName": "Stripped Spruce Wood", "stackSize": 64}, {"metadata": 10, "id": 126, "name": "stripped_birch_wood", "displayName": "Stripped Birch Wood", "stackSize": 64}, {"metadata": 11, "id": 127, "name": "stripped_jungle_wood", "displayName": "Stripped Jungle Wood", "stackSize": 64}, {"metadata": 12, "id": 128, "name": "stripped_acacia_wood", "displayName": "Stripped Acacia Wood", "stackSize": 64}, {"metadata": 13, "id": 129, "name": "stripped_dark_oak_wood", "displayName": "Stripped Dark Oak Wood", "stackSize": 64}]}, {"id": 139, "stackSize": 64, "name": "mangrove_wood", "displayName": "Mangrove Wood"}, {"id": 140, "stackSize": 64, "name": "crimson_hyphae", "displayName": "Crimson Hyphae"}, {"id": 141, "stackSize": 64, "name": "warped_hyphae", "displayName": "Warped Hyphae"}, {"id": 142, "displayName": "Oak Leaves", "name": "leaves", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 143, "name": "spruce_leaves", "displayName": "Spruce Leaves", "stackSize": 64}, {"metadata": 2, "id": 144, "name": "birch_leaves", "displayName": "Birch Leaves", "stackSize": 64}, {"metadata": 3, "id": 145, "name": "jungle_leaves", "displayName": "Jungle Leaves", "stackSize": 64}]}, {"id": 146, "displayName": "Acacia Leaves", "name": "leaves2", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 147, "name": "dark_oak_leaves", "displayName": "Dark Oak Leaves", "stackSize": 64}]}, {"id": 148, "stackSize": 64, "name": "mangrove_leaves", "displayName": "Mangrove Leaves"}, {"id": 149, "stackSize": 64, "name": "azalea_leaves", "displayName": "Azalea Leaves"}, {"id": 150, "stackSize": 64, "name": "azalea_leaves_flowered", "displayName": "Flowering Azalea Leaves"}, {"id": 151, "displayName": "Sponge", "name": "sponge", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 152, "name": "wet_sponge", "displayName": "Wet Sponge", "stackSize": 64}]}, {"id": 153, "stackSize": 64, "name": "glass", "displayName": "Glass"}, {"id": 154, "stackSize": 64, "name": "tinted_glass", "displayName": "Tinted Glass"}, {"id": 155, "stackSize": 64, "name": "lapis_block", "displayName": "Block of Lapis Lazuli"}, {"id": 156, "displayName": "Sandstone", "name": "sandstone", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 157, "name": "chiseled_sandstone", "displayName": "Chiseled Sandstone", "stackSize": 64}, {"metadata": 2, "id": 158, "name": "cut_sandstone", "displayName": "Cut Sandstone", "stackSize": 64}, {"metadata": 3, "id": 242, "name": "smooth_sandstone", "displayName": "Smooth Sandstone", "stackSize": 64}]}, {"id": 159, "stackSize": 64, "name": "web", "displayName": "Cobweb"}, {"id": 160, "displayName": "Grass", "name": "tallgrass", "stackSize": 64, "metadata": 1, "variations": [{"metadata": 2, "id": 161, "name": "fern", "displayName": "Fern", "stackSize": 64}]}, {"id": 162, "stackSize": 64, "name": "azalea", "displayName": "Azalea"}, {"id": 163, "stackSize": 64, "name": "flowering_azalea", "displayName": "Flowering Azalea"}, {"id": 164, "stackSize": 64, "name": "deadbush", "displayName": "Dead Bush"}, {"id": 165, "stackSize": 64, "name": "seagrass", "displayName": "Seagrass"}, {"id": 166, "stackSize": 64, "name": "sea_pickle", "displayName": "<PERSON>"}, {"id": 167, "displayName": "White Wool", "name": "wool", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 168, "name": "orange_wool", "displayName": "Orange Wool", "stackSize": 64}, {"metadata": 2, "id": 169, "name": "magenta_wool", "displayName": "Magenta Wool", "stackSize": 64}, {"metadata": 3, "id": 170, "name": "light_blue_wool", "displayName": "Light Blue Wool", "stackSize": 64}, {"metadata": 4, "id": 171, "name": "yellow_wool", "displayName": "Yellow Wool", "stackSize": 64}, {"metadata": 5, "id": 172, "name": "lime_wool", "displayName": "Lime Wool", "stackSize": 64}, {"metadata": 6, "id": 173, "name": "pink_wool", "displayName": "Pink Wool", "stackSize": 64}, {"metadata": 7, "id": 174, "name": "gray_wool", "displayName": "Gray <PERSON>", "stackSize": 64}, {"metadata": 8, "id": 175, "name": "light_gray_wool", "displayName": "Light Gray Wool", "stackSize": 64}, {"metadata": 9, "id": 176, "name": "cyan_wool", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"metadata": 10, "id": 177, "name": "purple_wool", "displayName": "Purple Wool", "stackSize": 64}, {"metadata": 11, "id": 178, "name": "blue_wool", "displayName": "Blue Wool", "stackSize": 64}, {"metadata": 12, "id": 179, "name": "brown_wool", "displayName": "Brown Wool", "stackSize": 64}, {"metadata": 13, "id": 180, "name": "green_wool", "displayName": "Green Wool", "stackSize": 64}, {"metadata": 14, "id": 181, "name": "red_wool", "displayName": "Red Wool", "stackSize": 64}, {"metadata": 15, "id": 182, "name": "black_wool", "displayName": "Black Wool", "stackSize": 64}]}, {"id": 183, "stackSize": 64, "name": "yellow_flower", "displayName": "Dandelion"}, {"id": 184, "displayName": "<PERSON><PERSON>", "name": "red_flower", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 185, "name": "blue_orchid", "displayName": "Blue Orchid", "stackSize": 64}, {"metadata": 2, "id": 186, "name": "allium", "displayName": "Allium", "stackSize": 64}, {"metadata": 3, "id": 187, "name": "azure_bluet", "displayName": "Azure Bluet", "stackSize": 64}, {"metadata": 4, "id": 188, "name": "red_tulip", "displayName": "<PERSON>lip", "stackSize": 64}, {"metadata": 5, "id": 189, "name": "orange_tulip", "displayName": "Orange Tulip", "stackSize": 64}, {"metadata": 6, "id": 190, "name": "white_tulip", "displayName": "White Tulip", "stackSize": 64}, {"metadata": 7, "id": 191, "name": "pink_tulip", "displayName": "<PERSON> Tulip", "stackSize": 64}, {"metadata": 8, "id": 192, "name": "oxeye_daisy", "displayName": "Oxeye Daisy", "stackSize": 64}, {"metadata": 9, "id": 193, "name": "cornflower", "displayName": "Corn<PERSON>", "stackSize": 64}, {"metadata": 10, "id": 194, "name": "lily_of_the_valley", "displayName": "Lily of the Valley", "stackSize": 64}]}, {"id": 195, "stackSize": 64, "name": "wither_rose", "displayName": "<PERSON><PERSON>"}, {"id": 196, "stackSize": 64, "name": "spore_blossom", "displayName": "Spore Blossom"}, {"id": 197, "stackSize": 64, "name": "brown_mushroom", "displayName": "Brown Mushroom"}, {"id": 198, "stackSize": 64, "name": "red_mushroom", "displayName": "Red Mushroom"}, {"id": 199, "stackSize": 64, "name": "crimson_fungus", "displayName": "Crimson Fungus"}, {"id": 200, "stackSize": 64, "name": "warped_fungus", "displayName": "Warped Fungus"}, {"id": 201, "stackSize": 64, "name": "crimson_roots", "displayName": "Crimson Roots"}, {"id": 202, "stackSize": 64, "name": "warped_roots", "displayName": "Warped Roots"}, {"id": 203, "stackSize": 64, "name": "nether_sprouts", "displayName": "Nether Sprouts"}, {"id": 204, "stackSize": 64, "name": "weeping_vines", "displayName": "Weeping Vines"}, {"id": 205, "stackSize": 64, "name": "twisting_vines", "displayName": "Twisting Vines"}, {"id": 206, "stackSize": 64, "name": "sugar_cane", "displayName": "Sugar Cane"}, {"id": 207, "stackSize": 64, "name": "kelp", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 208, "stackSize": 64, "name": "moss_carpet", "displayName": "Moss Carpet"}, {"id": 209, "stackSize": 64, "name": "moss_block", "displayName": "Moss Block"}, {"id": 210, "stackSize": 64, "name": "hanging_roots", "displayName": "Hanging Roots"}, {"id": 211, "stackSize": 64, "name": "big_dripleaf", "displayName": "Big Dripleaf"}, {"id": 212, "stackSize": 64, "name": "small_dripleaf_block", "displayName": "Small Dripleaf"}, {"id": 213, "stackSize": 64, "name": "bamboo", "displayName": "Bamboo"}, {"id": 214, "displayName": "Oak Slab", "name": "wooden_slab", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 215, "name": "spruce_slab", "displayName": "Spruce Slab", "stackSize": 64}, {"metadata": 2, "id": 216, "name": "birch_slab", "displayName": "<PERSON>", "stackSize": 64}, {"metadata": 3, "id": 217, "name": "jungle_slab", "displayName": "Jungle Slab", "stackSize": 64}, {"metadata": 4, "id": 218, "name": "acacia_slab", "displayName": "Acacia <PERSON>b", "stackSize": 64}, {"metadata": 5, "id": 219, "name": "dark_oak_slab", "displayName": "Dark Oak Slab", "stackSize": 64}]}, {"id": 220, "stackSize": 64, "name": "mangrove_slab", "displayName": "Mangrove Slab"}, {"id": 221, "stackSize": 64, "name": "crimson_slab", "displayName": "Crimson Slab"}, {"id": 222, "stackSize": 64, "name": "warped_slab", "displayName": "Warped Slab"}, {"id": 224, "displayName": "Smooth Stone Slab", "name": "stone_block_slab", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 225, "name": "sandstone_slab", "displayName": "Sandstone Slab", "stackSize": 64}, {"metadata": 2, "id": 227, "name": "petrified_oak_slab", "displayName": "Petrified Oak Slab", "stackSize": 64}, {"metadata": 3, "id": 228, "name": "cobblestone_slab", "displayName": "Cobblestone Slab", "stackSize": 64}, {"metadata": 4, "id": 229, "name": "brick_slab", "displayName": "Brick Slab", "stackSize": 64}, {"metadata": 5, "id": 230, "name": "stone_brick_slab", "displayName": "Stone Brick Slab", "stackSize": 64}, {"metadata": 6, "id": 233, "name": "quartz_slab", "displayName": "Quartz Slab", "stackSize": 64}, {"metadata": 7, "id": 232, "name": "nether_brick_slab", "displayName": "Nether Brick Slab", "stackSize": 64}]}, {"id": 231, "stackSize": 64, "name": "mud_brick_slab", "displayName": "Mud <PERSON> Slab"}, {"id": 234, "displayName": "Red Sandstone Slab", "name": "stone_block_slab2", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 236, "name": "purpur_slab", "displayName": "Purpur Slab", "stackSize": 64}, {"metadata": 2, "id": 237, "name": "prismarine_slab", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "stackSize": 64}, {"metadata": 3, "id": 239, "name": "dark_prismarine_slab", "displayName": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "stackSize": 64}, {"metadata": 4, "id": 238, "name": "prismarine_brick_slab", "displayName": "Prismarine Brick Slab", "stackSize": 64}, {"metadata": 5, "id": 594, "name": "mossy_cobblestone_slab", "displayName": "<PERSON><PERSON> Slab", "stackSize": 64}, {"metadata": 6, "id": 596, "name": "smooth_sandstone_slab", "displayName": "Smooth Sandstone Slab", "stackSize": 64}, {"metadata": 7, "id": 600, "name": "red_nether_brick_slab", "displayName": "Red Nether Brick Slab", "stackSize": 64}]}, {"id": 243, "stackSize": 64, "name": "smooth_stone", "displayName": "Smooth Stone"}, {"id": 244, "stackSize": 64, "name": "brick_block", "displayName": "Bricks"}, {"id": 245, "stackSize": 64, "name": "bookshelf", "displayName": "Bookshelf"}, {"id": 246, "stackSize": 64, "name": "mossy_cobblestone", "displayName": "<PERSON><PERSON>"}, {"id": 247, "stackSize": 64, "name": "obsidian", "displayName": "Obsidian"}, {"id": 248, "stackSize": 64, "name": "torch", "displayName": "<PERSON>ch"}, {"id": 249, "stackSize": 64, "name": "end_rod", "displayName": "End Rod"}, {"id": 250, "stackSize": 64, "name": "chorus_plant", "displayName": "Chorus Plant"}, {"id": 251, "stackSize": 64, "name": "chorus_flower", "displayName": "Chorus Flower"}, {"id": 252, "displayName": "Purpur Block", "name": "purpur_block", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 2, "id": 253, "name": "purpur_pillar", "displayName": "Purpur Pillar", "stackSize": 64}]}, {"id": 254, "stackSize": 64, "name": "purpur_stairs", "displayName": "Purpur Stairs"}, {"id": 255, "stackSize": 64, "name": "mob_spawner", "displayName": "Spawner"}, {"id": 256, "stackSize": 64, "name": "chest", "displayName": "Chest"}, {"id": 257, "stackSize": 64, "name": "crafting_table", "displayName": "Crafting Table"}, {"id": 258, "stackSize": 64, "name": "farmland", "displayName": "Farmland"}, {"id": 259, "stackSize": 64, "name": "furnace", "displayName": "Furnace"}, {"id": 260, "stackSize": 64, "name": "ladder", "displayName": "Ladder"}, {"id": 261, "stackSize": 64, "name": "stone_stairs", "displayName": "Cobblestone Stairs"}, {"id": 262, "stackSize": 64, "name": "snow_layer", "displayName": "Snow"}, {"id": 263, "stackSize": 64, "name": "ice", "displayName": "Ice"}, {"id": 264, "stackSize": 64, "name": "snow", "displayName": "Snow Block"}, {"id": 265, "stackSize": 64, "name": "cactus", "displayName": "Cactus"}, {"id": 266, "stackSize": 64, "name": "clay", "displayName": "<PERSON>"}, {"id": 267, "stackSize": 64, "name": "jukebox", "displayName": "Jukebox"}, {"id": 268, "displayName": "Oak Fence", "name": "fence", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 269, "name": "spruce_fence", "displayName": "Spruce Fence", "stackSize": 64}, {"metadata": 2, "id": 270, "name": "birch_fence", "displayName": "<PERSON>", "stackSize": 64}, {"metadata": 3, "id": 271, "name": "jungle_fence", "displayName": "Jungle Fence", "stackSize": 64}, {"metadata": 4, "id": 272, "name": "acacia_fence", "displayName": "Acacia Fence", "stackSize": 64}, {"metadata": 5, "id": 273, "name": "dark_oak_fence", "displayName": "Dark Oak Fence", "stackSize": 64}]}, {"id": 274, "stackSize": 64, "name": "mangrove_fence", "displayName": "Mangrove Fence"}, {"id": 275, "stackSize": 64, "name": "crimson_fence", "displayName": "<PERSON> Fence"}, {"id": 276, "stackSize": 64, "name": "warped_fence", "displayName": "Warped <PERSON>"}, {"id": 277, "stackSize": 64, "name": "pumpkin", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 278, "stackSize": 64, "name": "carved_pumpkin", "displayName": "<PERSON><PERSON>", "enchantCategories": ["wearable", "vanishable"]}, {"id": 279, "stackSize": 64, "name": "lit_pumpkin", "displayName": "<PERSON>'<PERSON>", "enchantCategories": ["wearable", "vanishable"]}, {"id": 280, "stackSize": 64, "name": "netherrack", "displayName": "Netherrack"}, {"id": 281, "stackSize": 64, "name": "soul_sand", "displayName": "Soul Sand"}, {"id": 282, "stackSize": 64, "name": "soul_soil", "displayName": "Soul Soil"}, {"id": 283, "stackSize": 64, "name": "basalt", "displayName": "Basalt"}, {"id": 284, "stackSize": 64, "name": "polished_basalt", "displayName": "Polished Ba<PERSON>t"}, {"id": 285, "stackSize": 64, "name": "smooth_basalt", "displayName": "Smooth Basalt"}, {"id": 286, "stackSize": 64, "name": "soul_torch", "displayName": "Soul Torch"}, {"id": 287, "stackSize": 64, "name": "glowstone", "displayName": "Glowstone"}, {"id": 288, "displayName": "Infested Stone", "name": "monster_egg", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 289, "name": "infested_cobblestone", "displayName": "Infested Cobblestone", "stackSize": 64}, {"metadata": 2, "id": 290, "name": "infested_stone_bricks", "displayName": "Infested Stone Bricks", "stackSize": 64}, {"metadata": 3, "id": 291, "name": "infested_mossy_stone_bricks", "displayName": "Infested Mossy Stone Bricks", "stackSize": 64}, {"metadata": 4, "id": 292, "name": "infested_cracked_stone_bricks", "displayName": "Infested Cracked Stone Bricks", "stackSize": 64}, {"metadata": 5, "id": 293, "name": "infested_chiseled_stone_bricks", "displayName": "Infested Chiseled Stone Bricks", "stackSize": 64}]}, {"id": 294, "stackSize": 64, "name": "infested_deepslate", "displayName": "Infested Deepslate"}, {"id": 295, "displayName": "Stone Bricks", "name": "stonebrick", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 296, "name": "mossy_stone_bricks", "displayName": "Mossy Stone Bricks", "stackSize": 64}, {"metadata": 2, "id": 297, "name": "cracked_stone_bricks", "displayName": "Cracked Stone Bricks", "stackSize": 64}, {"metadata": 3, "id": 298, "name": "chiseled_stone_bricks", "displayName": "Chiseled Stone Bricks", "stackSize": 64}]}, {"id": 299, "stackSize": 64, "name": "packed_mud", "displayName": "Packed Mud"}, {"id": 300, "stackSize": 64, "name": "mud_bricks", "displayName": "Mud Bricks"}, {"id": 301, "stackSize": 64, "name": "deepslate_bricks", "displayName": "Deepslate Bricks"}, {"id": 302, "stackSize": 64, "name": "cracked_deepslate_bricks", "displayName": "Cracked Deepslate Bricks"}, {"id": 303, "stackSize": 64, "name": "deepslate_tiles", "displayName": "Deepslate Tiles"}, {"id": 304, "stackSize": 64, "name": "cracked_deepslate_tiles", "displayName": "Cracked Deepslate Tiles"}, {"id": 305, "stackSize": 64, "name": "chiseled_deepslate", "displayName": "Chiseled Deepslate"}, {"id": 306, "stackSize": 64, "name": "reinforced_deepslate", "displayName": "Reinforced Deepslate"}, {"id": 307, "displayName": "Brown Mushroom Block", "name": "brown_mushroom_block", "stackSize": 64, "metadata": 14, "variations": [{"metadata": 15, "id": 309, "name": "mushroom_stem", "displayName": "Mushroom Stem", "stackSize": 64}]}, {"id": 308, "stackSize": 64, "name": "red_mushroom_block", "displayName": "Red Mushroom Block"}, {"id": 310, "stackSize": 64, "name": "iron_bars", "displayName": "Iron Bars"}, {"id": 311, "stackSize": 64, "name": "chain", "displayName": "Chain"}, {"id": 312, "stackSize": 64, "name": "glass_pane", "displayName": "Glass Pane"}, {"id": 313, "stackSize": 64, "name": "melon_block", "displayName": "Melon"}, {"id": 314, "stackSize": 64, "name": "vine", "displayName": "Vines"}, {"id": 315, "stackSize": 64, "name": "glow_lichen", "displayName": "Glow Lichen"}, {"id": 316, "stackSize": 64, "name": "brick_stairs", "displayName": "Brick Stairs"}, {"id": 317, "stackSize": 64, "name": "stone_brick_stairs", "displayName": "Stone Brick Stairs"}, {"id": 318, "stackSize": 64, "name": "mud_brick_stairs", "displayName": "Mud Brick Stairs"}, {"id": 319, "stackSize": 64, "name": "mycelium", "displayName": "Mycelium"}, {"id": 320, "stackSize": 64, "name": "waterlily", "displayName": "<PERSON>"}, {"id": 321, "stackSize": 64, "name": "nether_brick", "displayName": "Nether Bricks"}, {"id": 322, "stackSize": 64, "name": "cracked_nether_bricks", "displayName": "Cracked Nether Bricks"}, {"id": 323, "stackSize": 64, "name": "chiseled_nether_bricks", "displayName": "Chiseled Nether Bricks"}, {"id": 324, "stackSize": 64, "name": "nether_brick_fence", "displayName": "Nether Brick Fence"}, {"id": 325, "stackSize": 64, "name": "nether_brick_stairs", "displayName": "Nether Brick Stairs"}, {"id": 326, "stackSize": 64, "name": "sculk", "displayName": "Sculk"}, {"id": 327, "stackSize": 64, "name": "sculk_vein", "displayName": "Sculk Vein"}, {"id": 328, "stackSize": 64, "name": "sculk_catalyst", "displayName": "Sculk Catalyst"}, {"id": 329, "stackSize": 64, "name": "sculk_shrieker", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 330, "stackSize": 64, "name": "enchanting_table", "displayName": "Enchanting Table"}, {"id": 331, "stackSize": 64, "name": "end_portal_frame", "displayName": "End Portal Frame"}, {"id": 332, "stackSize": 64, "name": "end_stone", "displayName": "End Stone"}, {"id": 333, "stackSize": 64, "name": "end_bricks", "displayName": "End Stone Bricks"}, {"id": 334, "stackSize": 64, "name": "dragon_egg", "displayName": "Dragon Egg"}, {"id": 335, "stackSize": 64, "name": "sandstone_stairs", "displayName": "Sandstone Stairs"}, {"id": 336, "stackSize": 64, "name": "ender_chest", "displayName": "<PERSON><PERSON> Chest"}, {"id": 337, "stackSize": 64, "name": "emerald_block", "displayName": "Block of Emerald"}, {"id": 338, "stackSize": 64, "name": "oak_stairs", "displayName": "Oak Stairs"}, {"id": 339, "stackSize": 64, "name": "spruce_stairs", "displayName": "Spruce Stairs"}, {"id": 340, "stackSize": 64, "name": "birch_stairs", "displayName": "<PERSON> Stairs"}, {"id": 341, "stackSize": 64, "name": "jungle_stairs", "displayName": "Jungle Stairs"}, {"id": 342, "stackSize": 64, "name": "acacia_stairs", "displayName": "Acacia Stairs"}, {"id": 343, "stackSize": 64, "name": "dark_oak_stairs", "displayName": "Dark Oak Stairs"}, {"id": 344, "stackSize": 64, "name": "mangrove_stairs", "displayName": "Mangrove Stairs"}, {"id": 345, "stackSize": 64, "name": "crimson_stairs", "displayName": "Crimson Stairs"}, {"id": 346, "stackSize": 64, "name": "warped_stairs", "displayName": "Warped Stairs"}, {"id": 347, "stackSize": 64, "name": "command_block", "displayName": "Command Block"}, {"id": 348, "stackSize": 64, "name": "beacon", "displayName": "Beacon"}, {"id": 349, "displayName": "Cobblestone Wall", "name": "cobblestone_wall", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 350, "name": "mossy_cobblestone_wall", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"metadata": 2, "id": 355, "name": "granite_wall", "displayName": "Granite Wall", "stackSize": 64}, {"metadata": 3, "id": 363, "name": "diorite_wall", "displayName": "Diorite Wall", "stackSize": 64}, {"metadata": 4, "id": 359, "name": "andesite_wall", "displayName": "Andesite Wall", "stackSize": 64}, {"metadata": 5, "id": 361, "name": "sandstone_wall", "displayName": "Sandstone Wall", "stackSize": 64}, {"metadata": 6, "id": 351, "name": "brick_wall", "displayName": "Brick Wall", "stackSize": 64}, {"metadata": 7, "id": 356, "name": "stone_brick_wall", "displayName": "Stone Brick Wall", "stackSize": 64}, {"metadata": 8, "id": 354, "name": "mossy_stone_brick_wall", "displayName": "Mossy Stone Brick Wall", "stackSize": 64}, {"metadata": 9, "id": 358, "name": "nether_brick_wall", "displayName": "Nether Brick Wall", "stackSize": 64}, {"metadata": 10, "id": 362, "name": "end_stone_brick_wall", "displayName": "End Stone Brick Wall", "stackSize": 64}, {"metadata": 11, "id": 352, "name": "prismarine_wall", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "stackSize": 64}, {"metadata": 12, "id": 353, "name": "red_sandstone_wall", "displayName": "Red Sandstone Wall", "stackSize": 64}, {"metadata": 13, "id": 360, "name": "red_nether_brick_wall", "displayName": "Red Nether Brick Wall", "stackSize": 64}]}, {"id": 357, "stackSize": 64, "name": "mud_brick_wall", "displayName": "Mud Brick Wall"}, {"id": 364, "stackSize": 64, "name": "blackstone_wall", "displayName": "Blackstone Wall"}, {"id": 365, "stackSize": 64, "name": "polished_blackstone_wall", "displayName": "Polished Blackstone Wall"}, {"id": 366, "stackSize": 64, "name": "polished_blackstone_brick_wall", "displayName": "Polished Blackstone Brick Wall"}, {"id": 367, "stackSize": 64, "name": "cobbled_deepslate_wall", "displayName": "Cobbled Deepslate Wall"}, {"id": 368, "stackSize": 64, "name": "polished_deepslate_wall", "displayName": "Polished Deepslate Wall"}, {"id": 369, "stackSize": 64, "name": "deepslate_brick_wall", "displayName": "Deepslate Brick Wall"}, {"id": 370, "stackSize": 64, "name": "deepslate_tile_wall", "displayName": "Deepslate Tile Wall"}, {"id": 371, "displayName": "An<PERSON>", "name": "anvil", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 4, "id": 372, "name": "chipped_anvil", "displayName": "Chipped Anvil", "stackSize": 64}, {"metadata": 8, "id": 373, "name": "damaged_anvil", "displayName": "Damaged Anvil", "stackSize": 64}]}, {"id": 375, "displayName": "Block of Quartz", "name": "quartz_block", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 374, "name": "chiseled_quartz_block", "displayName": "Chiseled Quartz Block", "stackSize": 64}, {"metadata": 2, "id": 377, "name": "quartz_pillar", "displayName": "Quartz <PERSON>", "stackSize": 64}, {"metadata": 3, "id": 240, "name": "smooth_quartz", "displayName": "Smooth Quartz Block", "stackSize": 64}]}, {"id": 376, "stackSize": 64, "name": "quartz_bricks", "displayName": "Quartz Bricks"}, {"id": 378, "stackSize": 64, "name": "quartz_stairs", "displayName": "Quartz Stairs"}, {"id": 379, "displayName": "White Terracotta", "name": "stained_hardened_clay", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 380, "name": "orange_terracotta", "displayName": "Orange Terracotta", "stackSize": 64}, {"metadata": 2, "id": 381, "name": "magenta_terracotta", "displayName": "Magenta Terracotta", "stackSize": 64}, {"metadata": 3, "id": 382, "name": "light_blue_terracotta", "displayName": "Light Blue Terracotta", "stackSize": 64}, {"metadata": 4, "id": 383, "name": "yellow_terracotta", "displayName": "Yellow Terracotta", "stackSize": 64}, {"metadata": 5, "id": 384, "name": "lime_terracotta", "displayName": "Lime Terracotta", "stackSize": 64}, {"metadata": 6, "id": 385, "name": "pink_terracotta", "displayName": "Pink Terracotta", "stackSize": 64}, {"metadata": 7, "id": 386, "name": "gray_terracotta", "displayName": "Gray <PERSON>", "stackSize": 64}, {"metadata": 8, "id": 387, "name": "light_gray_terracotta", "displayName": "Light Gray Terracotta", "stackSize": 64}, {"metadata": 9, "id": 388, "name": "cyan_terracotta", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"metadata": 10, "id": 389, "name": "purple_terracotta", "displayName": "Purple Terracotta", "stackSize": 64}, {"metadata": 11, "id": 390, "name": "blue_terracotta", "displayName": "Blue Terracotta", "stackSize": 64}, {"metadata": 12, "id": 391, "name": "brown_terracotta", "displayName": "Brown Terracotta", "stackSize": 64}, {"metadata": 13, "id": 392, "name": "green_terracotta", "displayName": "Green Terracotta", "stackSize": 64}, {"metadata": 14, "id": 393, "name": "red_terracotta", "displayName": "Red Terracotta", "stackSize": 64}, {"metadata": 15, "id": 394, "name": "black_terracotta", "displayName": "Black Terracotta", "stackSize": 64}]}, {"id": 395, "stackSize": 64, "name": "barrier", "displayName": "Barrier"}, {"id": 396, "stackSize": 64, "name": "light_block", "displayName": "Light"}, {"id": 397, "stackSize": 64, "name": "hay_block", "displayName": "<PERSON>"}, {"id": 398, "displayName": "White Carpet", "name": "carpet", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 399, "name": "orange_carpet", "displayName": "Orange Carpet", "stackSize": 64}, {"metadata": 2, "id": 400, "name": "magenta_carpet", "displayName": "Magenta Carpet", "stackSize": 64}, {"metadata": 3, "id": 401, "name": "light_blue_carpet", "displayName": "Light Blue Carpet", "stackSize": 64}, {"metadata": 4, "id": 402, "name": "yellow_carpet", "displayName": "Yellow Carpet", "stackSize": 64}, {"metadata": 5, "id": 403, "name": "lime_carpet", "displayName": "Lime Carpet", "stackSize": 64}, {"metadata": 6, "id": 404, "name": "pink_carpet", "displayName": "Pink Carpet", "stackSize": 64}, {"metadata": 7, "id": 405, "name": "gray_carpet", "displayName": "<PERSON> Carpet", "stackSize": 64}, {"metadata": 8, "id": 406, "name": "light_gray_carpet", "displayName": "Light Gray Carpet", "stackSize": 64}, {"metadata": 9, "id": 407, "name": "cyan_carpet", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"metadata": 10, "id": 408, "name": "purple_carpet", "displayName": "Purple Carpet", "stackSize": 64}, {"metadata": 11, "id": 409, "name": "blue_carpet", "displayName": "Blue Carpet", "stackSize": 64}, {"metadata": 12, "id": 410, "name": "brown_carpet", "displayName": "Brown Carpet", "stackSize": 64}, {"metadata": 13, "id": 411, "name": "green_carpet", "displayName": "Green Carpet", "stackSize": 64}, {"metadata": 14, "id": 412, "name": "red_carpet", "displayName": "Red Carpet", "stackSize": 64}, {"metadata": 15, "id": 413, "name": "black_carpet", "displayName": "Black Carpet", "stackSize": 64}]}, {"id": 414, "stackSize": 64, "name": "hardened_clay", "displayName": "Terracotta"}, {"id": 415, "stackSize": 64, "name": "packed_ice", "displayName": "Packed Ice"}, {"id": 416, "stackSize": 64, "name": "grass_path", "displayName": "Dirt Path"}, {"id": 417, "displayName": "Sunflower", "name": "double_plant", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 418, "name": "lilac", "displayName": "Lilac", "stackSize": 64}, {"metadata": 2, "id": 421, "name": "tall_grass", "displayName": "Tall Grass", "stackSize": 64}, {"metadata": 3, "id": 422, "name": "large_fern", "displayName": "Large Fern", "stackSize": 64}, {"metadata": 4, "id": 419, "name": "rose_bush", "displayName": "<PERSON>", "stackSize": 64}, {"metadata": 5, "id": 420, "name": "peony", "displayName": "Peony", "stackSize": 64}]}, {"id": 423, "displayName": "White Stained Glass", "name": "stained_glass", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 424, "name": "orange_stained_glass", "displayName": "Orange Stained Glass", "stackSize": 64}, {"metadata": 2, "id": 425, "name": "magenta_stained_glass", "displayName": "Magenta Stained Glass", "stackSize": 64}, {"metadata": 3, "id": 426, "name": "light_blue_stained_glass", "displayName": "Light Blue Stained Glass", "stackSize": 64}, {"metadata": 4, "id": 427, "name": "yellow_stained_glass", "displayName": "Yellow Stained Glass", "stackSize": 64}, {"metadata": 5, "id": 428, "name": "lime_stained_glass", "displayName": "Lime Stained Glass", "stackSize": 64}, {"metadata": 6, "id": 429, "name": "pink_stained_glass", "displayName": "Pink Stained Glass", "stackSize": 64}, {"metadata": 7, "id": 430, "name": "gray_stained_glass", "displayName": "<PERSON> Stained Glass", "stackSize": 64}, {"metadata": 8, "id": 431, "name": "light_gray_stained_glass", "displayName": "Light Gray Stained Glass", "stackSize": 64}, {"metadata": 9, "id": 432, "name": "cyan_stained_glass", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"metadata": 10, "id": 433, "name": "purple_stained_glass", "displayName": "Purple Stained Glass", "stackSize": 64}, {"metadata": 11, "id": 434, "name": "blue_stained_glass", "displayName": "Blue Stained Glass", "stackSize": 64}, {"metadata": 12, "id": 435, "name": "brown_stained_glass", "displayName": "<PERSON> Stained Glass", "stackSize": 64}, {"metadata": 13, "id": 436, "name": "green_stained_glass", "displayName": "Green Stained Glass", "stackSize": 64}, {"metadata": 14, "id": 437, "name": "red_stained_glass", "displayName": "Red Stained Glass", "stackSize": 64}, {"metadata": 15, "id": 438, "name": "black_stained_glass", "displayName": "Black Stained Glass", "stackSize": 64}]}, {"id": 439, "displayName": "White Stained Glass Pane", "name": "stained_glass_pane", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 440, "name": "orange_stained_glass_pane", "displayName": "Orange Stained Glass Pane", "stackSize": 64}, {"metadata": 2, "id": 441, "name": "magenta_stained_glass_pane", "displayName": "Magenta Stained Glass Pane", "stackSize": 64}, {"metadata": 3, "id": 442, "name": "light_blue_stained_glass_pane", "displayName": "Light Blue Stained Glass Pane", "stackSize": 64}, {"metadata": 4, "id": 443, "name": "yellow_stained_glass_pane", "displayName": "Yellow Stained Glass Pane", "stackSize": 64}, {"metadata": 5, "id": 444, "name": "lime_stained_glass_pane", "displayName": "Lime Stained Glass Pane", "stackSize": 64}, {"metadata": 6, "id": 445, "name": "pink_stained_glass_pane", "displayName": "Pink Stained Glass Pane", "stackSize": 64}, {"metadata": 7, "id": 446, "name": "gray_stained_glass_pane", "displayName": "Gray Stained Glass Pane", "stackSize": 64}, {"metadata": 8, "id": 447, "name": "light_gray_stained_glass_pane", "displayName": "Light Gray Stained Glass Pane", "stackSize": 64}, {"metadata": 9, "id": 448, "name": "cyan_stained_glass_pane", "displayName": "<PERSON><PERSON> Stained Glass Pane", "stackSize": 64}, {"metadata": 10, "id": 449, "name": "purple_stained_glass_pane", "displayName": "Purple Stained Glass Pane", "stackSize": 64}, {"metadata": 11, "id": 450, "name": "blue_stained_glass_pane", "displayName": "Blue Stained Glass Pane", "stackSize": 64}, {"metadata": 12, "id": 451, "name": "brown_stained_glass_pane", "displayName": "<PERSON> Stained Glass Pane", "stackSize": 64}, {"metadata": 13, "id": 452, "name": "green_stained_glass_pane", "displayName": "Green Stained Glass Pane", "stackSize": 64}, {"metadata": 14, "id": 453, "name": "red_stained_glass_pane", "displayName": "Red Stained Glass Pane", "stackSize": 64}, {"metadata": 15, "id": 454, "name": "black_stained_glass_pane", "displayName": "Black Stained Glass Pane", "stackSize": 64}]}, {"id": 455, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "prismarine", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 457, "name": "dark_prismarine", "displayName": "<PERSON>", "stackSize": 64}, {"metadata": 2, "id": 456, "name": "prismarine_bricks", "displayName": "Prismarine <PERSON>s", "stackSize": 64}]}, {"id": 458, "stackSize": 64, "name": "prismarine_stairs", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 459, "stackSize": 64, "name": "prismarine_bricks_stairs", "displayName": "Prismarine Brick Stairs"}, {"id": 460, "stackSize": 64, "name": "dark_prismarine_stairs", "displayName": "<PERSON> <PERSON><PERSON><PERSON><PERSON>"}, {"id": 461, "stackSize": 64, "name": "sea_lantern", "displayName": "Sea Lantern"}, {"id": 462, "displayName": "Red Sandstone", "name": "red_sandstone", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 463, "name": "chiseled_red_sandstone", "displayName": "Chiseled Red Sandstone", "stackSize": 64}, {"metadata": 2, "id": 464, "name": "cut_red_sandstone", "displayName": "Cut Red Sandstone", "stackSize": 64}, {"metadata": 3, "id": 241, "name": "smooth_red_sandstone", "displayName": "Smooth Red Sandstone", "stackSize": 64}]}, {"id": 465, "stackSize": 64, "name": "red_sandstone_stairs", "displayName": "Red Sandstone Stairs"}, {"id": 466, "stackSize": 64, "name": "repeating_command_block", "displayName": "Repeating Command Block"}, {"id": 467, "stackSize": 64, "name": "chain_command_block", "displayName": "Chain Command Block"}, {"id": 468, "stackSize": 64, "name": "magma", "displayName": "Magma Block"}, {"id": 469, "stackSize": 64, "name": "nether_wart_block", "displayName": "Nether Wart Block"}, {"id": 470, "stackSize": 64, "name": "warped_wart_block", "displayName": "Warped Wart Block"}, {"id": 471, "stackSize": 64, "name": "red_nether_brick", "displayName": "Red Nether Bricks"}, {"id": 472, "stackSize": 64, "name": "bone_block", "displayName": "Bone Block"}, {"id": 473, "stackSize": 64, "name": "structure_void", "displayName": "Structure Void"}, {"id": 474, "stackSize": 1, "name": "undyed_shulker_box", "displayName": "Shulker Box"}, {"id": 475, "displayName": "White Shulker Box", "name": "shulker_box", "stackSize": 1, "metadata": 0, "variations": [{"metadata": 1, "id": 476, "name": "orange_shulker_box", "displayName": "Orange Shulker Box", "stackSize": 1}, {"metadata": 2, "id": 477, "name": "magenta_shulker_box", "displayName": "<PERSON><PERSON>a <PERSON>er Box", "stackSize": 1}, {"metadata": 3, "id": 478, "name": "light_blue_shulker_box", "displayName": "Light Blue Shulker Box", "stackSize": 1}, {"metadata": 4, "id": 479, "name": "yellow_shulker_box", "displayName": "Yellow Shulker Box", "stackSize": 1}, {"metadata": 5, "id": 480, "name": "lime_shulker_box", "displayName": "<PERSON>e <PERSON>er Box", "stackSize": 1}, {"metadata": 6, "id": 481, "name": "pink_shulker_box", "displayName": "Pink Shulker Box", "stackSize": 1}, {"metadata": 7, "id": 482, "name": "gray_shulker_box", "displayName": "<PERSON>", "stackSize": 1}, {"metadata": 8, "id": 483, "name": "light_gray_shulker_box", "displayName": "Light Gray Shulker Box", "stackSize": 1}, {"metadata": 9, "id": 484, "name": "cyan_shulker_box", "displayName": "<PERSON><PERSON>", "stackSize": 1}, {"metadata": 10, "id": 485, "name": "purple_shulker_box", "displayName": "Purple Shulker Box", "stackSize": 1}, {"metadata": 11, "id": 486, "name": "blue_shulker_box", "displayName": "Blue Shulker Box", "stackSize": 1}, {"metadata": 12, "id": 487, "name": "brown_shulker_box", "displayName": "<PERSON> Shulker Box", "stackSize": 1}, {"metadata": 13, "id": 488, "name": "green_shulker_box", "displayName": "Green Shulker Box", "stackSize": 1}, {"metadata": 14, "id": 489, "name": "red_shulker_box", "displayName": "Red Shulker Box", "stackSize": 1}, {"metadata": 15, "id": 490, "name": "black_shulker_box", "displayName": "Black Shulker Box", "stackSize": 1}]}, {"id": 491, "stackSize": 64, "name": "white_glazed_terracotta", "displayName": "White Glazed Terracotta"}, {"id": 492, "stackSize": 64, "name": "orange_glazed_terracotta", "displayName": "Orange Glazed Terracotta"}, {"id": 493, "stackSize": 64, "name": "magenta_glazed_terracotta", "displayName": "Magenta Glazed Terracotta"}, {"id": 494, "stackSize": 64, "name": "light_blue_glazed_terracotta", "displayName": "Light Blue Glazed Terracotta"}, {"id": 495, "stackSize": 64, "name": "yellow_glazed_terracotta", "displayName": "Yellow Glazed Terracotta"}, {"id": 496, "stackSize": 64, "name": "lime_glazed_terracotta", "displayName": "Lime Glazed Terracotta"}, {"id": 497, "stackSize": 64, "name": "pink_glazed_terracotta", "displayName": "Pink Glazed Terracotta"}, {"id": 498, "stackSize": 64, "name": "gray_glazed_terracotta", "displayName": "Gray Glazed Terracotta"}, {"id": 499, "stackSize": 64, "name": "silver_glazed_terracotta", "displayName": "Light Gray Glazed Terracotta"}, {"id": 500, "stackSize": 64, "name": "cyan_glazed_terracotta", "displayName": "<PERSON><PERSON>zed Terracotta"}, {"id": 501, "stackSize": 64, "name": "purple_glazed_terracotta", "displayName": "Purple Glazed Terracotta"}, {"id": 502, "stackSize": 64, "name": "blue_glazed_terracotta", "displayName": "Blue Glazed Terracotta"}, {"id": 503, "stackSize": 64, "name": "brown_glazed_terracotta", "displayName": "Brown Glazed Terracotta"}, {"id": 504, "stackSize": 64, "name": "green_glazed_terracotta", "displayName": "Green Glazed Terracotta"}, {"id": 505, "stackSize": 64, "name": "red_glazed_terracotta", "displayName": "Red Glazed Terracotta"}, {"id": 506, "stackSize": 64, "name": "black_glazed_terracotta", "displayName": "Black Glazed Terracotta"}, {"id": 507, "displayName": "White Concrete", "name": "concrete", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 508, "name": "orange_concrete", "displayName": "Orange Concrete", "stackSize": 64}, {"metadata": 2, "id": 509, "name": "magenta_concrete", "displayName": "Magenta Concrete", "stackSize": 64}, {"metadata": 3, "id": 510, "name": "light_blue_concrete", "displayName": "Light Blue Concrete", "stackSize": 64}, {"metadata": 4, "id": 511, "name": "yellow_concrete", "displayName": "Yellow Concrete", "stackSize": 64}, {"metadata": 5, "id": 512, "name": "lime_concrete", "displayName": "Lime Concrete", "stackSize": 64}, {"metadata": 6, "id": 513, "name": "pink_concrete", "displayName": "Pink Concrete", "stackSize": 64}, {"metadata": 7, "id": 514, "name": "gray_concrete", "displayName": "<PERSON>", "stackSize": 64}, {"metadata": 8, "id": 515, "name": "light_gray_concrete", "displayName": "Light Gray Concrete", "stackSize": 64}, {"metadata": 9, "id": 516, "name": "cyan_concrete", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"metadata": 10, "id": 517, "name": "purple_concrete", "displayName": "Purple Concrete", "stackSize": 64}, {"metadata": 11, "id": 518, "name": "blue_concrete", "displayName": "Blue Concrete", "stackSize": 64}, {"metadata": 12, "id": 519, "name": "brown_concrete", "displayName": "<PERSON> Concrete", "stackSize": 64}, {"metadata": 13, "id": 520, "name": "green_concrete", "displayName": "Green Concrete", "stackSize": 64}, {"metadata": 14, "id": 521, "name": "red_concrete", "displayName": "Red Concrete", "stackSize": 64}, {"metadata": 15, "id": 522, "name": "black_concrete", "displayName": "Black Concrete", "stackSize": 64}]}, {"id": 523, "displayName": "White Concrete Powder", "name": "concrete_powder", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 524, "name": "orange_concrete_powder", "displayName": "Orange Concrete Powder", "stackSize": 64}, {"metadata": 2, "id": 525, "name": "magenta_concrete_powder", "displayName": "Magenta Concrete Powder", "stackSize": 64}, {"metadata": 3, "id": 526, "name": "light_blue_concrete_powder", "displayName": "Light Blue Concrete Powder", "stackSize": 64}, {"metadata": 4, "id": 527, "name": "yellow_concrete_powder", "displayName": "Yellow Concrete Powder", "stackSize": 64}, {"metadata": 5, "id": 528, "name": "lime_concrete_powder", "displayName": "Lime Concrete <PERSON>", "stackSize": 64}, {"metadata": 6, "id": 529, "name": "pink_concrete_powder", "displayName": "Pink Concrete Powder", "stackSize": 64}, {"metadata": 7, "id": 530, "name": "gray_concrete_powder", "displayName": "<PERSON> Concre<PERSON>", "stackSize": 64}, {"metadata": 8, "id": 531, "name": "light_gray_concrete_powder", "displayName": "Light Gray Concrete Powder", "stackSize": 64}, {"metadata": 9, "id": 532, "name": "cyan_concrete_powder", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"metadata": 10, "id": 533, "name": "purple_concrete_powder", "displayName": "Purple Concrete Powder", "stackSize": 64}, {"metadata": 11, "id": 534, "name": "blue_concrete_powder", "displayName": "Blue Concrete Powder", "stackSize": 64}, {"metadata": 12, "id": 535, "name": "brown_concrete_powder", "displayName": "<PERSON> Concrete <PERSON>", "stackSize": 64}, {"metadata": 13, "id": 536, "name": "green_concrete_powder", "displayName": "Green Concrete Powder", "stackSize": 64}, {"metadata": 14, "id": 537, "name": "red_concrete_powder", "displayName": "Red Concrete Powder", "stackSize": 64}, {"metadata": 15, "id": 538, "name": "black_concrete_powder", "displayName": "Black Concrete Powder", "stackSize": 64}]}, {"id": 539, "stackSize": 64, "name": "turtle_egg", "displayName": "Turtle Egg"}, {"id": 545, "displayName": "Tube Coral Block", "name": "coral_block", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 546, "name": "brain_coral_block", "displayName": "Brain <PERSON>", "stackSize": 64}, {"metadata": 2, "id": 547, "name": "bubble_coral_block", "displayName": "Bubble Coral Block", "stackSize": 64}, {"metadata": 3, "id": 548, "name": "fire_coral_block", "displayName": "Fire Coral Block", "stackSize": 64}, {"metadata": 4, "id": 549, "name": "horn_coral_block", "displayName": "Horn Coral Block", "stackSize": 64}, {"metadata": 8, "id": 540, "name": "dead_tube_coral_block", "displayName": "Dead Tube Coral Block", "stackSize": 64}, {"metadata": 9, "id": 541, "name": "dead_brain_coral_block", "displayName": "Dead Brain Coral Block", "stackSize": 64}, {"metadata": 10, "id": 542, "name": "dead_bubble_coral_block", "displayName": "Dead Bubble Coral Block", "stackSize": 64}, {"metadata": 11, "id": 543, "name": "dead_fire_coral_block", "displayName": "Dead Fire Coral Block", "stackSize": 64}, {"metadata": 12, "id": 544, "name": "dead_horn_coral_block", "displayName": "Dead Horn Coral Block", "stackSize": 64}]}, {"id": 550, "displayName": "Tube Coral", "name": "coral", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 551, "name": "brain_coral", "displayName": "Brain Coral", "stackSize": 64}, {"metadata": 2, "id": 552, "name": "bubble_coral", "displayName": "Bubble Coral", "stackSize": 64}, {"metadata": 3, "id": 553, "name": "fire_coral", "displayName": "Fire Coral", "stackSize": 64}, {"metadata": 4, "id": 554, "name": "horn_coral", "displayName": "Horn Coral", "stackSize": 64}, {"metadata": 8, "id": 559, "name": "dead_tube_coral", "displayName": "Dead Tube Coral", "stackSize": 64}, {"metadata": 9, "id": 555, "name": "dead_brain_coral", "displayName": "Dead Brain Coral", "stackSize": 64}, {"metadata": 10, "id": 556, "name": "dead_bubble_coral", "displayName": "Dead Bubble Coral", "stackSize": 64}, {"metadata": 11, "id": 557, "name": "dead_fire_coral", "displayName": "Dead Fire Coral", "stackSize": 64}, {"metadata": 12, "id": 558, "name": "dead_horn_coral", "displayName": "Dead Horn Coral", "stackSize": 64}]}, {"id": 560, "displayName": "Tube Coral Fan", "name": "coral_fan", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 561, "name": "brain_coral_fan", "displayName": "Brain Coral Fan", "stackSize": 64}, {"metadata": 2, "id": 562, "name": "bubble_coral_fan", "displayName": "Bubble Coral Fan", "stackSize": 64}, {"metadata": 3, "id": 563, "name": "fire_coral_fan", "displayName": "Fire Coral Fan", "stackSize": 64}, {"metadata": 4, "id": 564, "name": "horn_coral_fan", "displayName": "Horn Coral Fan", "stackSize": 64}]}, {"id": 565, "displayName": "Dead Tube Coral Fan", "name": "coral_fan_dead", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 566, "name": "dead_brain_coral_fan", "displayName": "Dead Brain Coral Fan", "stackSize": 64}, {"metadata": 2, "id": 567, "name": "dead_bubble_coral_fan", "displayName": "Dead Bubble Coral Fan", "stackSize": 64}, {"metadata": 3, "id": 568, "name": "dead_fire_coral_fan", "displayName": "Dead Fire Coral Fan", "stackSize": 64}, {"metadata": 4, "id": 569, "name": "dead_horn_coral_fan", "displayName": "Dead Horn Coral Fan", "stackSize": 64}]}, {"id": 570, "stackSize": 64, "name": "blue_ice", "displayName": "Blue Ice"}, {"id": 571, "stackSize": 64, "name": "conduit", "displayName": "Conduit"}, {"id": 572, "stackSize": 64, "name": "polished_granite_stairs", "displayName": "Polished Granite Stairs"}, {"id": 573, "stackSize": 64, "name": "smooth_red_sandstone_stairs", "displayName": "Smooth Red Sandstone Stairs"}, {"id": 574, "stackSize": 64, "name": "mossy_stone_brick_stairs", "displayName": "Mossy Stone Brick Stairs"}, {"id": 575, "stackSize": 64, "name": "polished_diorite_stairs", "displayName": "Polished Diorite Stairs"}, {"id": 576, "stackSize": 64, "name": "mossy_cobblestone_stairs", "displayName": "Mossy Cobblestone Stairs"}, {"id": 577, "stackSize": 64, "name": "end_brick_stairs", "displayName": "End Stone Brick Stairs"}, {"id": 578, "stackSize": 64, "name": "normal_stone_stairs", "displayName": "Stone Stairs"}, {"id": 579, "stackSize": 64, "name": "smooth_sandstone_stairs", "displayName": "Smooth Sandstone Stairs"}, {"id": 580, "stackSize": 64, "name": "smooth_quartz_stairs", "displayName": "Smooth Quartz Stairs"}, {"id": 581, "stackSize": 64, "name": "granite_stairs", "displayName": "Granite Stairs"}, {"id": 582, "stackSize": 64, "name": "andesite_stairs", "displayName": "Andesite Stairs"}, {"id": 583, "stackSize": 64, "name": "red_nether_brick_stairs", "displayName": "Red Nether Brick Stairs"}, {"id": 584, "stackSize": 64, "name": "polished_andesite_stairs", "displayName": "Polished Andesite Stairs"}, {"id": 585, "stackSize": 64, "name": "diorite_stairs", "displayName": "Diorite Stairs"}, {"id": 586, "stackSize": 64, "name": "cobbled_deepslate_stairs", "displayName": "Cobbled Deepslate Stairs"}, {"id": 587, "stackSize": 64, "name": "polished_deepslate_stairs", "displayName": "Polished Deepslate Stairs"}, {"id": 588, "stackSize": 64, "name": "deepslate_brick_stairs", "displayName": "Deepslate Brick Stairs"}, {"id": 589, "stackSize": 64, "name": "deepslate_tile_stairs", "displayName": "Deepslate Tile Stairs"}, {"id": 592, "displayName": "Mossy Stone Brick Slab", "name": "stone_block_slab4", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 597, "name": "smooth_quartz_slab", "displayName": "Smooth Quartz Slab", "stackSize": 64}, {"metadata": 2, "id": 223, "name": "stone_slab", "displayName": "<PERSON> Slab", "stackSize": 64}, {"metadata": 3, "id": 226, "name": "cut_sandstone_slab", "displayName": "Cut Sandstone Slab", "stackSize": 64}, {"metadata": 4, "id": 235, "name": "cut_red_sandstone_slab", "displayName": "Cut Red Sandstone Slab", "stackSize": 64}]}, {"id": 595, "displayName": "End Stone Brick Slab", "name": "stone_block_slab3", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 591, "name": "smooth_red_sandstone_slab", "displayName": "Smooth Red Sandstone Slab", "stackSize": 64}, {"metadata": 2, "id": 601, "name": "polished_andesite_slab", "displayName": "Polished Andesite Slab", "stackSize": 64}, {"metadata": 3, "id": 599, "name": "andesite_slab", "displayName": "Andesite Slab", "stackSize": 64}, {"metadata": 4, "id": 602, "name": "diorite_slab", "displayName": "Diorite Slab", "stackSize": 64}, {"metadata": 5, "id": 593, "name": "polished_diorite_slab", "displayName": "Polished Diorite S<PERSON>b", "stackSize": 64}, {"metadata": 6, "id": 598, "name": "granite_slab", "displayName": "Granite Slab", "stackSize": 64}, {"metadata": 7, "id": 590, "name": "polished_granite_slab", "displayName": "Polished Granite Slab", "stackSize": 64}]}, {"id": 603, "stackSize": 64, "name": "cobbled_deepslate_slab", "displayName": "Cobbled Deepslate Slab"}, {"id": 604, "stackSize": 64, "name": "polished_deepslate_slab", "displayName": "Polished Deepslate Slab"}, {"id": 605, "stackSize": 64, "name": "deepslate_brick_slab", "displayName": "Deepslate Brick Slab"}, {"id": 606, "stackSize": 64, "name": "deepslate_tile_slab", "displayName": "Deepslate Tile Slab"}, {"id": 607, "stackSize": 64, "name": "scaffolding", "displayName": "Scaffolding"}, {"id": 608, "stackSize": 64, "name": "redstone", "displayName": "Redstone Dust"}, {"id": 609, "stackSize": 64, "name": "redstone_torch", "displayName": "Redstone Torch"}, {"id": 610, "stackSize": 64, "name": "redstone_block", "displayName": "Block of Redstone"}, {"id": 611, "stackSize": 64, "name": "repeater", "displayName": "Redstone Repeater"}, {"id": 612, "stackSize": 64, "name": "comparator", "displayName": "Redstone Comparator"}, {"id": 613, "stackSize": 64, "name": "piston", "displayName": "<PERSON><PERSON>"}, {"id": 614, "stackSize": 64, "name": "sticky_piston", "displayName": "<PERSON><PERSON>"}, {"id": 615, "stackSize": 64, "name": "slime", "displayName": "Slime Block"}, {"id": 616, "stackSize": 64, "name": "honey_block", "displayName": "Honey Block"}, {"id": 617, "stackSize": 64, "name": "observer", "displayName": "Observer"}, {"id": 618, "stackSize": 64, "name": "hopper", "displayName": "<PERSON>"}, {"id": 619, "stackSize": 64, "name": "dispenser", "displayName": "Dispenser"}, {"id": 620, "stackSize": 64, "name": "dropper", "displayName": "Dropper"}, {"id": 621, "stackSize": 64, "name": "lectern", "displayName": "Lectern"}, {"id": 622, "stackSize": 64, "name": "target", "displayName": "Target"}, {"id": 623, "stackSize": 64, "name": "lever", "displayName": "Lever"}, {"id": 624, "stackSize": 64, "name": "lightning_rod", "displayName": "Lightning Rod"}, {"id": 625, "stackSize": 64, "name": "daylight_detector", "displayName": "Daylight Detector"}, {"id": 626, "stackSize": 64, "name": "sculk_sensor", "displayName": "Sculk Sensor"}, {"id": 627, "stackSize": 64, "name": "tripwire_hook", "displayName": "Tripwire Hook"}, {"id": 628, "stackSize": 64, "name": "trapped_chest", "displayName": "Trapped Chest"}, {"id": 629, "stackSize": 64, "name": "tnt", "displayName": "TNT"}, {"id": 630, "stackSize": 64, "name": "redstone_lamp", "displayName": "Redstone Lamp"}, {"id": 631, "stackSize": 64, "name": "noteblock", "displayName": "Note Block"}, {"id": 632, "stackSize": 64, "name": "stone_button", "displayName": "<PERSON>"}, {"id": 633, "stackSize": 64, "name": "polished_blackstone_button", "displayName": "Polished Blackstone Button"}, {"id": 634, "stackSize": 64, "name": "wooden_button", "displayName": "Oak Button"}, {"id": 635, "stackSize": 64, "name": "spruce_button", "displayName": "Spruce Button"}, {"id": 636, "stackSize": 64, "name": "birch_button", "displayName": "<PERSON>"}, {"id": 637, "stackSize": 64, "name": "jungle_button", "displayName": "<PERSON>ton"}, {"id": 638, "stackSize": 64, "name": "acacia_button", "displayName": "Acacia <PERSON>"}, {"id": 639, "stackSize": 64, "name": "dark_oak_button", "displayName": "Dark Oak Button"}, {"id": 640, "stackSize": 64, "name": "mangrove_button", "displayName": "Mangrove Button"}, {"id": 641, "stackSize": 64, "name": "crimson_button", "displayName": "<PERSON>"}, {"id": 642, "stackSize": 64, "name": "warped_button", "displayName": "Warped <PERSON>"}, {"id": 643, "stackSize": 64, "name": "stone_pressure_plate", "displayName": "Stone Pressure Plate"}, {"id": 644, "stackSize": 64, "name": "polished_blackstone_pressure_plate", "displayName": "Polished Blackstone Pressure Plate"}, {"id": 645, "stackSize": 64, "name": "light_weighted_pressure_plate", "displayName": "Light Weighted Pressure Plate"}, {"id": 646, "stackSize": 64, "name": "heavy_weighted_pressure_plate", "displayName": "Heavy Weighted Pressure Plate"}, {"id": 647, "stackSize": 64, "name": "wooden_pressure_plate", "displayName": "Oak Pressure Plate"}, {"id": 648, "stackSize": 64, "name": "spruce_pressure_plate", "displayName": "Spruce Pressure Plate"}, {"id": 649, "stackSize": 64, "name": "birch_pressure_plate", "displayName": "Birch Pressure Plate"}, {"id": 650, "stackSize": 64, "name": "jungle_pressure_plate", "displayName": "Jungle Pressure Plate"}, {"id": 651, "stackSize": 64, "name": "acacia_pressure_plate", "displayName": "Acacia Pressure Plate"}, {"id": 652, "stackSize": 64, "name": "dark_oak_pressure_plate", "displayName": "Dark Oak Pressure Plate"}, {"id": 653, "stackSize": 64, "name": "mangrove_pressure_plate", "displayName": "Mangrove Pressure Plate"}, {"id": 654, "stackSize": 64, "name": "crimson_pressure_plate", "displayName": "Crimson Pressure Plate"}, {"id": 655, "stackSize": 64, "name": "warped_pressure_plate", "displayName": "Warped Pressure Plate"}, {"id": 656, "stackSize": 64, "name": "iron_door", "displayName": "Iron Door"}, {"id": 657, "stackSize": 64, "name": "wooden_door", "displayName": "Oak Door"}, {"id": 658, "stackSize": 64, "name": "spruce_door", "displayName": "Spruce Door"}, {"id": 659, "stackSize": 64, "name": "birch_door", "displayName": "<PERSON>"}, {"id": 660, "stackSize": 64, "name": "jungle_door", "displayName": "Jungle Door"}, {"id": 661, "stackSize": 64, "name": "acacia_door", "displayName": "Acacia Door"}, {"id": 662, "stackSize": 64, "name": "dark_oak_door", "displayName": "Dark Oak Door"}, {"id": 663, "stackSize": 64, "name": "mangrove_door", "displayName": "Mangrove Door"}, {"id": 664, "stackSize": 64, "name": "crimson_door", "displayName": "Crimson Door"}, {"id": 665, "stackSize": 64, "name": "warped_door", "displayName": "Warped Door"}, {"id": 666, "stackSize": 64, "name": "iron_trapdoor", "displayName": "Iron Trapdoor"}, {"id": 667, "stackSize": 64, "name": "trapdoor", "displayName": "Oak Trapdoor"}, {"id": 668, "stackSize": 64, "name": "spruce_trapdoor", "displayName": "Spruce Trapdoor"}, {"id": 669, "stackSize": 64, "name": "birch_trapdoor", "displayName": "<PERSON>"}, {"id": 670, "stackSize": 64, "name": "jungle_trapdoor", "displayName": "Jungle Trapdoor"}, {"id": 671, "stackSize": 64, "name": "acacia_trapdoor", "displayName": "Acacia T<PERSON>door"}, {"id": 672, "stackSize": 64, "name": "dark_oak_trapdoor", "displayName": "Dark Oak Trapdoor"}, {"id": 673, "stackSize": 64, "name": "mangrove_trapdoor", "displayName": "Mangrove Trapdoor"}, {"id": 674, "stackSize": 64, "name": "crimson_trapdoor", "displayName": "Crimson Trapdoor"}, {"id": 675, "stackSize": 64, "name": "warped_trapdoor", "displayName": "Warped Trapdoor"}, {"id": 676, "stackSize": 64, "name": "fence_gate", "displayName": "Oak Fence Gate"}, {"id": 677, "stackSize": 64, "name": "spruce_fence_gate", "displayName": "Spruce Fence Gate"}, {"id": 678, "stackSize": 64, "name": "birch_fence_gate", "displayName": "Birch Fence Gate"}, {"id": 679, "stackSize": 64, "name": "jungle_fence_gate", "displayName": "Jungle Fence Gate"}, {"id": 680, "stackSize": 64, "name": "acacia_fence_gate", "displayName": "Acacia Fence Gate"}, {"id": 681, "stackSize": 64, "name": "dark_oak_fence_gate", "displayName": "Dark Oak Fence Gate"}, {"id": 682, "stackSize": 64, "name": "mangrove_fence_gate", "displayName": "Mangrove Fence Gate"}, {"id": 683, "stackSize": 64, "name": "crimson_fence_gate", "displayName": "Crimson Fence Gate"}, {"id": 684, "stackSize": 64, "name": "warped_fence_gate", "displayName": "Warped Fence Gate"}, {"id": 685, "stackSize": 64, "name": "golden_rail", "displayName": "Powered Rail"}, {"id": 686, "stackSize": 64, "name": "detector_rail", "displayName": "Detector Rail"}, {"id": 687, "stackSize": 64, "name": "rail", "displayName": "Rail"}, {"id": 688, "stackSize": 64, "name": "activator_rail", "displayName": "Activator Rail"}, {"id": 689, "stackSize": 1, "name": "saddle", "displayName": "Saddle"}, {"id": 690, "stackSize": 1, "name": "minecart", "displayName": "Minecart"}, {"id": 691, "stackSize": 1, "name": "chest_minecart", "displayName": "Minecart with Chest"}, {"id": 692, "displayName": "Minecart with Furnace", "name": "hopper_minecart", "stackSize": 1, "metadata": 0, "variations": [{"metadata": 0, "id": 694, "name": "hopper_minecart", "displayName": "Minecart with <PERSON>", "stackSize": 1}]}, {"id": 693, "stackSize": 1, "name": "tnt_minecart", "displayName": "Minecart with TNT"}, {"id": 695, "stackSize": 1, "name": "carrot_on_a_stick", "displayName": "Carrot on a Stick", "enchantCategories": ["breakable", "vanishable"], "maxDurability": 25}, {"id": 696, "stackSize": 1, "name": "warped_fungus_on_a_stick", "displayName": "Warped Fungus on a Stick", "enchantCategories": ["breakable", "vanishable"], "maxDurability": 100}, {"id": 697, "stackSize": 1, "name": "elytra", "displayName": "Elytra", "enchantCategories": ["breakable", "wearable", "vanishable"], "repairWith": ["phantom_membrane"], "maxDurability": 432}, {"id": 698, "stackSize": 1, "name": "oak_boat", "displayName": "Oak Boat"}, {"id": 699, "stackSize": 1, "name": "oak_chest_boat", "displayName": "Oak Boat with Chest"}, {"id": 700, "stackSize": 1, "name": "spruce_boat", "displayName": "Spruce Boat"}, {"id": 701, "stackSize": 1, "name": "spruce_chest_boat", "displayName": "Spruce Boat with Chest"}, {"id": 702, "stackSize": 1, "name": "birch_boat", "displayName": "<PERSON> Boat"}, {"id": 703, "stackSize": 1, "name": "birch_chest_boat", "displayName": "<PERSON> Boat with Chest"}, {"id": 704, "stackSize": 1, "name": "jungle_boat", "displayName": "Jungle Boat"}, {"id": 705, "stackSize": 1, "name": "jungle_chest_boat", "displayName": "Jungle Boat with Chest"}, {"id": 706, "stackSize": 1, "name": "acacia_boat", "displayName": "Acacia Boat"}, {"id": 707, "stackSize": 1, "name": "acacia_chest_boat", "displayName": "Acacia Boat with Chest"}, {"id": 708, "stackSize": 1, "name": "dark_oak_boat", "displayName": "Dark Oak Boat"}, {"id": 709, "stackSize": 1, "name": "dark_oak_chest_boat", "displayName": "Dark Oak Boat with Chest"}, {"id": 710, "stackSize": 1, "name": "mangrove_boat", "displayName": "Mangrove Boat"}, {"id": 711, "stackSize": 1, "name": "mangrove_chest_boat", "displayName": "Mangrove Boat with Chest"}, {"id": 712, "stackSize": 64, "name": "structure_block", "displayName": "Structure Block"}, {"id": 713, "stackSize": 64, "name": "jigsaw", "displayName": "Jigsaw Block"}, {"id": 714, "stackSize": 1, "name": "turtle_helmet", "displayName": "Turtle Shell", "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["scute"], "maxDurability": 275}, {"id": 715, "stackSize": 64, "name": "scute", "displayName": "<PERSON><PERSON>"}, {"id": 716, "stackSize": 1, "name": "flint_and_steel", "displayName": "Flint and Steel", "enchantCategories": ["breakable", "vanishable"], "maxDurability": 64}, {"id": 717, "stackSize": 64, "name": "apple", "displayName": "Apple"}, {"id": 718, "stackSize": 1, "name": "bow", "displayName": "Bow", "enchantCategories": ["breakable", "bow", "vanishable"], "maxDurability": 384}, {"id": 719, "displayName": "Arrow", "name": "arrow", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 0, "id": 1049, "name": "spectral_arrow", "displayName": "Spectral Arrow", "stackSize": 64}, {"metadata": 0, "id": 1050, "name": "tipped_arrow", "displayName": "Tipped Arrow", "stackSize": 64}]}, {"id": 720, "stackSize": 64, "name": "coal", "displayName": "Coal"}, {"id": 721, "stackSize": 64, "name": "charcoal", "displayName": "Charc<PERSON>l"}, {"id": 722, "stackSize": 64, "name": "diamond", "displayName": "Diamond"}, {"id": 723, "stackSize": 64, "name": "emerald", "displayName": "Emerald"}, {"id": 724, "stackSize": 64, "name": "lapis_lazuli", "displayName": "<PERSON><PERSON>"}, {"id": 725, "stackSize": 64, "name": "quartz", "displayName": "<PERSON><PERSON>"}, {"id": 726, "stackSize": 64, "name": "amethyst_shard", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 727, "stackSize": 64, "name": "raw_iron", "displayName": "Raw Iron"}, {"id": 728, "stackSize": 64, "name": "iron_ingot", "displayName": "Iron Ingot"}, {"id": 729, "stackSize": 64, "name": "raw_copper", "displayName": "Raw Copper"}, {"id": 730, "stackSize": 64, "name": "copper_ingot", "displayName": "Copper Ingot"}, {"id": 731, "stackSize": 64, "name": "raw_gold", "displayName": "Raw Gold"}, {"id": 732, "stackSize": 64, "name": "gold_ingot", "displayName": "Gold Ingot"}, {"id": 733, "stackSize": 64, "name": "netherite_ingot", "displayName": "Netherite Ingot"}, {"id": 734, "stackSize": 64, "name": "netherite_scrap", "displayName": "Netherite Scrap"}, {"id": 735, "stackSize": 1, "name": "wooden_sword", "displayName": "Wooden Sword", "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "mangrove_planks", "crimson_planks", "warped_planks"], "maxDurability": 59}, {"id": 736, "stackSize": 1, "name": "wooden_shovel", "displayName": "<PERSON><PERSON>", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "mangrove_planks", "crimson_planks", "warped_planks"], "maxDurability": 59}, {"id": 737, "stackSize": 1, "name": "wooden_pickaxe", "displayName": "<PERSON><PERSON> Pick<PERSON>e", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "mangrove_planks", "crimson_planks", "warped_planks"], "maxDurability": 59}, {"id": 738, "stackSize": 1, "name": "wooden_axe", "displayName": "Wooden Axe", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "mangrove_planks", "crimson_planks", "warped_planks"], "maxDurability": 59}, {"id": 739, "stackSize": 1, "name": "wooden_hoe", "displayName": "<PERSON><PERSON>e", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "mangrove_planks", "crimson_planks", "warped_planks"], "maxDurability": 59}, {"id": 740, "stackSize": 1, "name": "stone_sword", "displayName": "Stone Sword", "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["cobbled_deepslate", "cobblestone", "blackstone"], "maxDurability": 131}, {"id": 741, "stackSize": 1, "name": "stone_shovel", "displayName": "<PERSON>el", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["cobbled_deepslate", "cobblestone", "blackstone"], "maxDurability": 131}, {"id": 742, "stackSize": 1, "name": "stone_pickaxe", "displayName": "<PERSON>", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["cobbled_deepslate", "cobblestone", "blackstone"], "maxDurability": 131}, {"id": 743, "stackSize": 1, "name": "stone_axe", "displayName": "Stone Axe", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["cobbled_deepslate", "cobblestone", "blackstone"], "maxDurability": 131}, {"id": 744, "stackSize": 1, "name": "stone_hoe", "displayName": "Stone Hoe", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["cobbled_deepslate", "cobblestone", "blackstone"], "maxDurability": 131}, {"id": 745, "stackSize": 1, "name": "golden_sword", "displayName": "Golden Sword", "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["gold_ingot"], "maxDurability": 32}, {"id": 746, "stackSize": 1, "name": "golden_shovel", "displayName": "Golden Shovel", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["gold_ingot"], "maxDurability": 32}, {"id": 747, "stackSize": 1, "name": "golden_pickaxe", "displayName": "Golden Pickaxe", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["gold_ingot"], "maxDurability": 32}, {"id": 748, "stackSize": 1, "name": "golden_axe", "displayName": "Golden Axe", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["gold_ingot"], "maxDurability": 32}, {"id": 749, "stackSize": 1, "name": "golden_hoe", "displayName": "Golden Hoe", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["gold_ingot"], "maxDurability": 32}, {"id": 750, "stackSize": 1, "name": "iron_sword", "displayName": "Iron Sword", "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 250}, {"id": 751, "stackSize": 1, "name": "iron_shovel", "displayName": "Iron Shovel", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 250}, {"id": 752, "stackSize": 1, "name": "iron_pickaxe", "displayName": "Iron Pickaxe", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 250}, {"id": 753, "stackSize": 1, "name": "iron_axe", "displayName": "Iron Axe", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 250}, {"id": 754, "stackSize": 1, "name": "iron_hoe", "displayName": "Iron Hoe", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 250}, {"id": 755, "stackSize": 1, "name": "diamond_sword", "displayName": "Diamond Sword", "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["diamond"], "maxDurability": 1561}, {"id": 756, "stackSize": 1, "name": "diamond_shovel", "displayName": "Diamond Shovel", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["diamond"], "maxDurability": 1561}, {"id": 757, "stackSize": 1, "name": "diamond_pickaxe", "displayName": "Diamond Pickaxe", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["diamond"], "maxDurability": 1561}, {"id": 758, "stackSize": 1, "name": "diamond_axe", "displayName": "Diamond Axe", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["diamond"], "maxDurability": 1561}, {"id": 759, "stackSize": 1, "name": "diamond_hoe", "displayName": "Diamond Hoe", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["diamond"], "maxDurability": 1561}, {"id": 760, "stackSize": 1, "name": "netherite_sword", "displayName": "Netherite Sword", "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["netherite_ingot"], "maxDurability": 2031}, {"id": 761, "stackSize": 1, "name": "netherite_shovel", "displayName": "<PERSON><PERSON><PERSON>", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["netherite_ingot"], "maxDurability": 2031}, {"id": 762, "stackSize": 1, "name": "netherite_pickaxe", "displayName": "Netherite Pickaxe", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["netherite_ingot"], "maxDurability": 2031}, {"id": 763, "stackSize": 1, "name": "netherite_axe", "displayName": "Netherite Axe", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["netherite_ingot"], "maxDurability": 2031}, {"id": 764, "stackSize": 1, "name": "netherite_hoe", "displayName": "Netherite Hoe", "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["netherite_ingot"], "maxDurability": 2031}, {"id": 765, "displayName": "Stick", "name": "stick", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 0, "id": 1057, "name": "debug_stick", "displayName": "Debug Stick", "stackSize": 1}]}, {"id": 766, "stackSize": 64, "name": "bowl", "displayName": "Bowl"}, {"id": 767, "stackSize": 1, "name": "mushroom_stew", "displayName": "Mushroom Stew"}, {"id": 768, "stackSize": 64, "name": "string", "displayName": "String"}, {"id": 769, "stackSize": 64, "name": "feather", "displayName": "<PERSON><PERSON>"}, {"id": 770, "stackSize": 64, "name": "gunpowder", "displayName": "Gunpowder"}, {"id": 771, "stackSize": 64, "name": "wheat_seeds", "displayName": "Wheat Seeds"}, {"id": 772, "stackSize": 64, "name": "wheat", "displayName": "Wheat"}, {"id": 773, "stackSize": 64, "name": "bread", "displayName": "Bread"}, {"id": 774, "stackSize": 1, "name": "leather_helmet", "displayName": "Leather Cap", "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["leather"], "maxDurability": 55}, {"id": 775, "stackSize": 1, "name": "leather_chestplate", "displayName": "<PERSON><PERSON>", "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["leather"], "maxDurability": 80}, {"id": 776, "stackSize": 1, "name": "leather_leggings", "displayName": "<PERSON><PERSON>", "enchantCategories": ["armor", "armor_legs", "breakable", "wearable", "vanishable"], "repairWith": ["leather"], "maxDurability": 75}, {"id": 777, "stackSize": 1, "name": "leather_boots", "displayName": "<PERSON><PERSON>", "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["leather"], "maxDurability": 65}, {"id": 778, "stackSize": 1, "name": "chainmail_helmet", "displayName": "Chainmail Helmet", "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 165}, {"id": 779, "stackSize": 1, "name": "chainmail_chestplate", "displayName": "Chainmail Chestplate", "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 240}, {"id": 780, "stackSize": 1, "name": "chainmail_leggings", "displayName": "Chainmail Leggings", "enchantCategories": ["armor", "armor_legs", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 225}, {"id": 781, "stackSize": 1, "name": "chainmail_boots", "displayName": "Chainmail Boots", "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 195}, {"id": 782, "stackSize": 1, "name": "iron_helmet", "displayName": "Iron Helmet", "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 165}, {"id": 783, "stackSize": 1, "name": "iron_chestplate", "displayName": "Iron Chestplate", "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 240}, {"id": 784, "stackSize": 1, "name": "iron_leggings", "displayName": "Iron Leggings", "enchantCategories": ["armor", "armor_legs", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 225}, {"id": 785, "stackSize": 1, "name": "iron_boots", "displayName": "Iron Boots", "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 195}, {"id": 786, "stackSize": 1, "name": "diamond_helmet", "displayName": "Diamond Helmet", "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["diamond"], "maxDurability": 363}, {"id": 787, "stackSize": 1, "name": "diamond_chestplate", "displayName": "Diamond Chestplate", "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["diamond"], "maxDurability": 528}, {"id": 788, "stackSize": 1, "name": "diamond_leggings", "displayName": "Diamond Leggings", "enchantCategories": ["armor", "armor_legs", "breakable", "wearable", "vanishable"], "repairWith": ["diamond"], "maxDurability": 495}, {"id": 789, "stackSize": 1, "name": "diamond_boots", "displayName": "Diamond Boots", "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["diamond"], "maxDurability": 429}, {"id": 790, "stackSize": 1, "name": "golden_helmet", "displayName": "Golden Helmet", "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["gold_ingot"], "maxDurability": 77}, {"id": 791, "stackSize": 1, "name": "golden_chestplate", "displayName": "Golden Chestplate", "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["gold_ingot"], "maxDurability": 112}, {"id": 792, "stackSize": 1, "name": "golden_leggings", "displayName": "Golden Leggings", "enchantCategories": ["armor", "armor_legs", "breakable", "wearable", "vanishable"], "repairWith": ["gold_ingot"], "maxDurability": 105}, {"id": 793, "stackSize": 1, "name": "golden_boots", "displayName": "Golden Boots", "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["gold_ingot"], "maxDurability": 91}, {"id": 794, "stackSize": 1, "name": "netherite_helmet", "displayName": "Netherite Helmet", "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["netherite_ingot"], "maxDurability": 407}, {"id": 795, "stackSize": 1, "name": "netherite_chestplate", "displayName": "Netherite Chestplate", "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["netherite_ingot"], "maxDurability": 592}, {"id": 796, "stackSize": 1, "name": "netherite_leggings", "displayName": "Netherite Leggings", "enchantCategories": ["armor", "armor_legs", "breakable", "wearable", "vanishable"], "repairWith": ["netherite_ingot"], "maxDurability": 555}, {"id": 797, "stackSize": 1, "name": "netherite_boots", "displayName": "Netherite Boots", "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["netherite_ingot"], "maxDurability": 481}, {"id": 798, "stackSize": 64, "name": "flint", "displayName": "Flint"}, {"id": 799, "stackSize": 64, "name": "porkchop", "displayName": "Raw Porkchop"}, {"id": 800, "stackSize": 64, "name": "cooked_porkchop", "displayName": "Cooked Porkchop"}, {"id": 801, "stackSize": 64, "name": "painting", "displayName": "Painting"}, {"id": 802, "stackSize": 64, "name": "golden_apple", "displayName": "Golden Apple"}, {"id": 803, "stackSize": 64, "name": "enchanted_golden_apple", "displayName": "Enchanted Golden Apple"}, {"id": 804, "stackSize": 16, "name": "oak_sign", "displayName": "Oak Sign"}, {"id": 805, "stackSize": 16, "name": "spruce_sign", "displayName": "Spruce Sign"}, {"id": 806, "stackSize": 16, "name": "birch_sign", "displayName": "Birch Sign"}, {"id": 807, "stackSize": 16, "name": "jungle_sign", "displayName": "Jungle Sign"}, {"id": 808, "stackSize": 16, "name": "acacia_sign", "displayName": "Acacia Sign"}, {"id": 809, "stackSize": 16, "name": "dark_oak_sign", "displayName": "Dark Oak Sign"}, {"id": 810, "stackSize": 16, "name": "mangrove_sign", "displayName": "Mangrove Sign"}, {"id": 811, "stackSize": 16, "name": "crimson_sign", "displayName": "Crimson Sign"}, {"id": 812, "stackSize": 16, "name": "warped_sign", "displayName": "Warped Sign"}, {"id": 813, "stackSize": 16, "name": "bucket", "displayName": "Bucket"}, {"id": 814, "stackSize": 1, "name": "water_bucket", "displayName": "Water Bucket"}, {"id": 815, "stackSize": 1, "name": "lava_bucket", "displayName": "<PERSON><PERSON>et"}, {"id": 816, "stackSize": 1, "name": "powder_snow_bucket", "displayName": "Powder Snow Bucket"}, {"id": 817, "stackSize": 16, "name": "snowball", "displayName": "Snowball"}, {"id": 818, "stackSize": 64, "name": "leather", "displayName": "Leather"}, {"id": 819, "stackSize": 1, "name": "milk_bucket", "displayName": "Milk Bucket"}, {"id": 820, "stackSize": 1, "name": "pufferfish_bucket", "displayName": "Bucket of Pufferfish"}, {"id": 821, "stackSize": 1, "name": "salmon_bucket", "displayName": "Bucket of Salmon"}, {"id": 822, "stackSize": 1, "name": "cod_bucket", "displayName": "Bucket of Cod"}, {"id": 823, "stackSize": 1, "name": "tropical_fish_bucket", "displayName": "Bucket of Tropical Fish"}, {"id": 824, "stackSize": 1, "name": "axolotl_bucket", "displayName": "Bucket of Axolotl"}, {"id": 825, "stackSize": 1, "name": "tadpole_bucket", "displayName": "Bucket of Tadpole"}, {"id": 826, "stackSize": 64, "name": "brick", "displayName": "Brick"}, {"id": 827, "stackSize": 64, "name": "clay_ball", "displayName": "<PERSON>"}, {"id": 828, "stackSize": 64, "name": "dried_kelp_block", "displayName": "Dried Kelp Block"}, {"id": 829, "stackSize": 64, "name": "paper", "displayName": "Paper"}, {"id": 830, "displayName": "Book", "name": "book", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 0, "id": 1056, "name": "knowledge_book", "displayName": "Knowledge Book", "stackSize": 1}]}, {"id": 831, "stackSize": 64, "name": "slime_ball", "displayName": "Slimeball"}, {"id": 832, "stackSize": 16, "name": "egg", "displayName": "Egg"}, {"id": 833, "stackSize": 64, "name": "compass", "displayName": "<PERSON>mp<PERSON>", "enchantCategories": ["vanishable"]}, {"id": 834, "stackSize": 64, "name": "recovery_compass", "displayName": "Recovery Compass"}, {"id": 835, "displayName": "Bundle", "name": "shulker_shell", "stackSize": 1, "metadata": 0, "variations": [{"metadata": 0, "id": 1054, "name": "shulker_shell", "displayName": "Shulker Shell", "stackSize": 64}]}, {"id": 836, "stackSize": 1, "name": "fishing_rod", "displayName": "Fishing Rod", "enchantCategories": ["fishing_rod", "breakable", "vanishable"], "maxDurability": 64}, {"id": 837, "stackSize": 64, "name": "clock", "displayName": "Clock"}, {"id": 838, "stackSize": 1, "name": "spyglass", "displayName": "Spyglass"}, {"id": 839, "stackSize": 64, "name": "glowstone_dust", "displayName": "Glowstone Dust"}, {"id": 840, "stackSize": 64, "name": "cod", "displayName": "Raw Cod"}, {"id": 841, "stackSize": 64, "name": "salmon", "displayName": "Raw Salmon"}, {"id": 842, "stackSize": 64, "name": "tropical_fish", "displayName": "Tropical Fish"}, {"id": 843, "stackSize": 64, "name": "pufferfish", "displayName": "Pufferfish"}, {"id": 844, "stackSize": 64, "name": "cooked_cod", "displayName": "Cooked Cod"}, {"id": 845, "stackSize": 64, "name": "cooked_salmon", "displayName": "Cooked Salmon"}, {"id": 846, "stackSize": 64, "name": "ink_sac", "displayName": "Ink Sac"}, {"id": 847, "stackSize": 64, "name": "glow_ink_sac", "displayName": "Glow Ink Sac"}, {"id": 848, "stackSize": 64, "name": "cocoa_beans", "displayName": "Cocoa Beans"}, {"id": 849, "stackSize": 64, "name": "white_dye", "displayName": "White Dye"}, {"id": 850, "stackSize": 64, "name": "orange_dye", "displayName": "Orange Dye"}, {"id": 851, "stackSize": 64, "name": "magenta_dye", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 852, "stackSize": 64, "name": "light_blue_dye", "displayName": "Light Blue Dye"}, {"id": 853, "stackSize": 64, "name": "yellow_dye", "displayName": "Yellow Dye"}, {"id": 854, "stackSize": 64, "name": "lime_dye", "displayName": "Lime Dye"}, {"id": 855, "stackSize": 64, "name": "pink_dye", "displayName": "Pink Dye"}, {"id": 856, "stackSize": 64, "name": "gray_dye", "displayName": "<PERSON>"}, {"id": 857, "stackSize": 64, "name": "light_gray_dye", "displayName": "Light Gray D<PERSON>"}, {"id": 858, "stackSize": 64, "name": "cyan_dye", "displayName": "<PERSON><PERSON>"}, {"id": 859, "stackSize": 64, "name": "purple_dye", "displayName": "Purple Dye"}, {"id": 860, "stackSize": 64, "name": "blue_dye", "displayName": "Blue Dye"}, {"id": 861, "stackSize": 64, "name": "brown_dye", "displayName": "<PERSON>"}, {"id": 862, "stackSize": 64, "name": "green_dye", "displayName": "Green Dye"}, {"id": 863, "stackSize": 64, "name": "red_dye", "displayName": "Red Dye"}, {"id": 864, "stackSize": 64, "name": "black_dye", "displayName": "Black Dye"}, {"id": 865, "stackSize": 64, "name": "bone_meal", "displayName": "<PERSON>"}, {"id": 866, "stackSize": 64, "name": "bone", "displayName": "Bone"}, {"id": 867, "stackSize": 64, "name": "sugar", "displayName": "Sugar"}, {"id": 868, "stackSize": 1, "name": "cake", "displayName": "Cake"}, {"id": 869, "displayName": "White Bed", "name": "bed", "stackSize": 1, "metadata": 0, "variations": [{"metadata": 1, "id": 870, "name": "orange_bed", "displayName": "Orange Bed", "stackSize": 1}, {"metadata": 2, "id": 871, "name": "magenta_bed", "displayName": "Magenta Bed", "stackSize": 1}, {"metadata": 3, "id": 872, "name": "light_blue_bed", "displayName": "Light Blue Bed", "stackSize": 1}, {"metadata": 4, "id": 873, "name": "yellow_bed", "displayName": "Yellow Bed", "stackSize": 1}, {"metadata": 5, "id": 874, "name": "lime_bed", "displayName": "Lime Bed", "stackSize": 1}, {"metadata": 6, "id": 875, "name": "pink_bed", "displayName": "Pink Bed", "stackSize": 1}, {"metadata": 7, "id": 876, "name": "gray_bed", "displayName": "Gray Bed", "stackSize": 1}, {"metadata": 8, "id": 877, "name": "light_gray_bed", "displayName": "Light Gray Bed", "stackSize": 1}, {"metadata": 9, "id": 878, "name": "cyan_bed", "displayName": "<PERSON><PERSON>", "stackSize": 1}, {"metadata": 10, "id": 879, "name": "purple_bed", "displayName": "Purple Bed", "stackSize": 1}, {"metadata": 11, "id": 880, "name": "blue_bed", "displayName": "Blue Bed", "stackSize": 1}, {"metadata": 12, "id": 881, "name": "brown_bed", "displayName": "Brown Bed", "stackSize": 1}, {"metadata": 13, "id": 882, "name": "green_bed", "displayName": "Green Bed", "stackSize": 1}, {"metadata": 14, "id": 883, "name": "red_bed", "displayName": "Red Bed", "stackSize": 1}, {"metadata": 15, "id": 884, "name": "black_bed", "displayName": "Black Bed", "stackSize": 1}]}, {"id": 885, "stackSize": 64, "name": "cookie", "displayName": "<PERSON><PERSON>"}, {"id": 886, "stackSize": 64, "name": "filled_map", "displayName": "Map"}, {"id": 887, "stackSize": 1, "name": "shears", "displayName": "Shears", "enchantCategories": ["breakable", "vanishable"], "maxDurability": 238}, {"id": 888, "stackSize": 64, "name": "melon_slice", "displayName": "<PERSON><PERSON>"}, {"id": 889, "stackSize": 64, "name": "dried_kelp", "displayName": "<PERSON><PERSON>"}, {"id": 890, "stackSize": 64, "name": "pumpkin_seeds", "displayName": "<PERSON><PERSON><PERSON> Seeds"}, {"id": 891, "stackSize": 64, "name": "melon_seeds", "displayName": "<PERSON>on Seeds"}, {"id": 892, "stackSize": 64, "name": "beef", "displayName": "Raw Beef"}, {"id": 893, "stackSize": 64, "name": "cooked_beef", "displayName": "Steak"}, {"id": 894, "stackSize": 64, "name": "chicken", "displayName": "Raw Chicken"}, {"id": 895, "stackSize": 64, "name": "cooked_chicken", "displayName": "Cooked Chicken"}, {"id": 896, "stackSize": 64, "name": "rotten_flesh", "displayName": "Rotten Flesh"}, {"id": 897, "stackSize": 16, "name": "ender_pearl", "displayName": "<PERSON><PERSON>"}, {"id": 898, "stackSize": 64, "name": "blaze_rod", "displayName": "<PERSON>"}, {"id": 899, "stackSize": 64, "name": "ghast_tear", "displayName": "Ghast Tear"}, {"id": 900, "stackSize": 64, "name": "gold_nugget", "displayName": "Gold Nugget"}, {"id": 901, "stackSize": 64, "name": "nether_wart", "displayName": "Nether Wart"}, {"id": 902, "stackSize": 1, "name": "potion", "displayName": "Potion"}, {"id": 903, "stackSize": 64, "name": "glass_bottle", "displayName": "Glass Bottle"}, {"id": 904, "stackSize": 64, "name": "spider_eye", "displayName": "Spider Eye"}, {"id": 905, "stackSize": 64, "name": "fermented_spider_eye", "displayName": "Fermented Spider Eye"}, {"id": 906, "stackSize": 64, "name": "blaze_powder", "displayName": "<PERSON>"}, {"id": 907, "stackSize": 64, "name": "magma_cream", "displayName": "Magma Cream"}, {"id": 908, "stackSize": 64, "name": "brewing_stand", "displayName": "Brewing Stand"}, {"id": 909, "stackSize": 64, "name": "cauldron", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 910, "stackSize": 64, "name": "ender_eye", "displayName": "Eye of <PERSON>er"}, {"id": 911, "stackSize": 64, "name": "glistering_melon_slice", "displayName": "Glistering <PERSON><PERSON>"}, {"id": 912, "stackSize": 64, "name": "allay_spawn_egg", "displayName": "Allay Spawn Egg"}, {"id": 913, "stackSize": 64, "name": "axolotl_spawn_egg", "displayName": "Axolotl Spawn Egg"}, {"id": 914, "stackSize": 64, "name": "bat_spawn_egg", "displayName": "Bat Spawn Egg"}, {"id": 915, "stackSize": 64, "name": "bee_spawn_egg", "displayName": "Bee Spawn Egg"}, {"id": 916, "stackSize": 64, "name": "blaze_spawn_egg", "displayName": "Blaze Spawn Egg"}, {"id": 917, "stackSize": 64, "name": "cat_spawn_egg", "displayName": "Cat Spawn Egg"}, {"id": 918, "stackSize": 64, "name": "cave_spider_spawn_egg", "displayName": "Cave Spider Spawn Egg"}, {"id": 919, "stackSize": 64, "name": "chicken_spawn_egg", "displayName": "Chicken Spawn Egg"}, {"id": 920, "stackSize": 64, "name": "cod_spawn_egg", "displayName": "Cod Spawn Egg"}, {"id": 921, "stackSize": 64, "name": "cow_spawn_egg", "displayName": "Cow Spawn Egg"}, {"id": 922, "stackSize": 64, "name": "creeper_spawn_egg", "displayName": "Creeper Spawn Egg"}, {"id": 923, "stackSize": 64, "name": "dolphin_spawn_egg", "displayName": "Dolphin Spawn Egg"}, {"id": 924, "stackSize": 64, "name": "donkey_spawn_egg", "displayName": "Donkey Spawn Egg"}, {"id": 925, "stackSize": 64, "name": "drowned_spawn_egg", "displayName": "Drowned Spawn Egg"}, {"id": 926, "stackSize": 64, "name": "elder_guardian_spawn_egg", "displayName": "Elder Guardian Spawn Egg"}, {"id": 927, "stackSize": 64, "name": "enderman_spawn_egg", "displayName": "Enderman Spawn Egg"}, {"id": 928, "stackSize": 64, "name": "endermite_spawn_egg", "displayName": "Endermite Spawn Egg"}, {"id": 929, "stackSize": 64, "name": "evoker_spawn_egg", "displayName": "Evoker Spawn Egg"}, {"id": 930, "stackSize": 64, "name": "fox_spawn_egg", "displayName": "Fox Spawn Egg"}, {"id": 931, "stackSize": 64, "name": "frog_spawn_egg", "displayName": "Frog Spawn Egg"}, {"id": 932, "stackSize": 64, "name": "ghast_spawn_egg", "displayName": "Ghast Spawn Egg"}, {"id": 933, "stackSize": 64, "name": "glow_squid_spawn_egg", "displayName": "Glow Squid Spawn Egg"}, {"id": 934, "stackSize": 64, "name": "goat_spawn_egg", "displayName": "Goat Spawn Egg"}, {"id": 935, "stackSize": 64, "name": "guardian_spawn_egg", "displayName": "Guardian Spawn Egg"}, {"id": 936, "stackSize": 64, "name": "hoglin_spawn_egg", "displayName": "Hoglin Spawn Egg"}, {"id": 937, "stackSize": 64, "name": "horse_spawn_egg", "displayName": "Horse Spawn Egg"}, {"id": 938, "stackSize": 64, "name": "husk_spawn_egg", "displayName": "Husk Spawn Egg"}, {"id": 939, "displayName": "Llama Spawn Egg", "name": "llama_spawn_egg", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 0, "id": 967, "name": "trader_llama_spawn_egg", "displayName": "Trader <PERSON>lama Spawn Egg", "stackSize": 64}]}, {"id": 940, "stackSize": 64, "name": "magma_cube_spawn_egg", "displayName": "Magma Cube Spawn Egg"}, {"id": 941, "stackSize": 64, "name": "mooshroom_spawn_egg", "displayName": "Mooshroom Spawn Egg"}, {"id": 942, "stackSize": 64, "name": "mule_spawn_egg", "displayName": "Mule Spawn Egg"}, {"id": 943, "stackSize": 64, "name": "ocelot_spawn_egg", "displayName": "Ocelot Spawn Egg"}, {"id": 944, "stackSize": 64, "name": "panda_spawn_egg", "displayName": "Panda Spawn Egg"}, {"id": 945, "stackSize": 64, "name": "parrot_spawn_egg", "displayName": "Parrot Spawn Egg"}, {"id": 946, "stackSize": 64, "name": "phantom_spawn_egg", "displayName": "Phantom Spawn Egg"}, {"id": 947, "stackSize": 64, "name": "pig_spawn_egg", "displayName": "Pig Spawn Egg"}, {"id": 948, "stackSize": 64, "name": "piglin_spawn_egg", "displayName": "Piglin Spawn Egg"}, {"id": 949, "stackSize": 64, "name": "piglin_brute_spawn_egg", "displayName": "Piglin Brute Spawn Egg"}, {"id": 950, "stackSize": 64, "name": "pillager_spawn_egg", "displayName": "Pillager Spawn Egg"}, {"id": 951, "stackSize": 64, "name": "polar_bear_spawn_egg", "displayName": "Polar Bear Spawn Egg"}, {"id": 952, "stackSize": 64, "name": "pufferfish_spawn_egg", "displayName": "Pufferfish Spawn Egg"}, {"id": 953, "stackSize": 64, "name": "rabbit_spawn_egg", "displayName": "Rabbit Spawn Egg"}, {"id": 954, "stackSize": 64, "name": "ravager_spawn_egg", "displayName": "Ravager Spawn Egg"}, {"id": 955, "stackSize": 64, "name": "salmon_spawn_egg", "displayName": "Salmon Spawn Egg"}, {"id": 956, "stackSize": 64, "name": "sheep_spawn_egg", "displayName": "Sheep Spawn Egg"}, {"id": 957, "stackSize": 64, "name": "shulker_spawn_egg", "displayName": "Shulker Spawn Egg"}, {"id": 958, "stackSize": 64, "name": "silverfish_spawn_egg", "displayName": "Silverfish Spawn Egg"}, {"id": 959, "stackSize": 64, "name": "skeleton_spawn_egg", "displayName": "Skeleton Spawn Egg"}, {"id": 960, "stackSize": 64, "name": "skeleton_horse_spawn_egg", "displayName": "Skeleton Horse Spawn Egg"}, {"id": 961, "stackSize": 64, "name": "slime_spawn_egg", "displayName": "Slime Spawn Egg"}, {"id": 962, "stackSize": 64, "name": "spider_spawn_egg", "displayName": "Spider Spawn Egg"}, {"id": 963, "stackSize": 64, "name": "squid_spawn_egg", "displayName": "Squid Spawn Egg"}, {"id": 964, "stackSize": 64, "name": "stray_spawn_egg", "displayName": "Stray Spawn Egg"}, {"id": 965, "stackSize": 64, "name": "strider_spawn_egg", "displayName": "Strider Spawn Egg"}, {"id": 966, "stackSize": 64, "name": "tadpole_spawn_egg", "displayName": "Tadpole Spawn Egg"}, {"id": 968, "stackSize": 64, "name": "tropical_fish_spawn_egg", "displayName": "Tropical Fish Spawn Egg"}, {"id": 969, "stackSize": 64, "name": "turtle_spawn_egg", "displayName": "Turtle Spawn Egg"}, {"id": 970, "stackSize": 64, "name": "vex_spawn_egg", "displayName": "Vex Spawn Egg"}, {"id": 971, "stackSize": 64, "name": "villager_spawn_egg", "displayName": "Villager Spawn Egg"}, {"id": 972, "stackSize": 64, "name": "vindicator_spawn_egg", "displayName": "Vindicator Spawn Egg"}, {"id": 973, "stackSize": 64, "name": "wandering_trader_spawn_egg", "displayName": "Wandering Trader Spawn Egg"}, {"id": 974, "stackSize": 64, "name": "warden_spawn_egg", "displayName": "Warden Spawn Egg"}, {"id": 975, "stackSize": 64, "name": "witch_spawn_egg", "displayName": "Witch Spawn Egg"}, {"id": 976, "stackSize": 64, "name": "wither_skeleton_spawn_egg", "displayName": "Wither Skeleton Spawn Egg"}, {"id": 977, "stackSize": 64, "name": "wolf_spawn_egg", "displayName": "Wolf Spawn Egg"}, {"id": 978, "stackSize": 64, "name": "zoglin_spawn_egg", "displayName": "Zoglin Spawn Egg"}, {"id": 979, "stackSize": 64, "name": "zombie_spawn_egg", "displayName": "Zombie Spawn Egg"}, {"id": 980, "stackSize": 64, "name": "zombie_horse_spawn_egg", "displayName": "Zombie Horse Spawn Egg"}, {"id": 981, "stackSize": 64, "name": "zombie_villager_spawn_egg", "displayName": "Zombie Villager Spawn Egg"}, {"id": 982, "stackSize": 64, "name": "zombie_pigman_spawn_egg", "displayName": "Zombified Piglin Spawn Egg"}, {"id": 983, "stackSize": 64, "name": "experience_bottle", "displayName": "Bottle o' Enchanting"}, {"id": 984, "stackSize": 64, "name": "fire_charge", "displayName": "Fire Charge"}, {"id": 985, "stackSize": 1, "name": "writable_book", "displayName": "Book and Quill"}, {"id": 986, "stackSize": 16, "name": "written_book", "displayName": "Written Book"}, {"id": 987, "stackSize": 64, "name": "frame", "displayName": "<PERSON><PERSON>"}, {"id": 988, "stackSize": 64, "name": "glow_frame", "displayName": "G<PERSON> Item <PERSON>"}, {"id": 989, "stackSize": 64, "name": "flower_pot", "displayName": "Flower Pot"}, {"id": 990, "stackSize": 64, "name": "carrot", "displayName": "Carrot"}, {"id": 991, "stackSize": 64, "name": "potato", "displayName": "Potato"}, {"id": 992, "stackSize": 64, "name": "baked_potato", "displayName": "Baked Potato"}, {"id": 993, "stackSize": 64, "name": "poisonous_potato", "displayName": "Poisonous Potato"}, {"id": 994, "stackSize": 64, "name": "empty_map", "displayName": "Empty Map"}, {"id": 995, "stackSize": 64, "name": "golden_carrot", "displayName": "Golden Carrot"}, {"id": 996, "displayName": "Skeleton Skull", "name": "skull", "stackSize": 64, "metadata": 0, "enchantCategories": ["wearable", "vanishable"], "variations": [{"metadata": 1, "id": 997, "name": "wither_skeleton_skull", "displayName": "Wither Skeleton Skull", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"metadata": 2, "id": 999, "name": "zombie_head", "displayName": "Zombie Head", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"metadata": 3, "id": 998, "name": "player_head", "displayName": "Player Head", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"metadata": 4, "id": 1000, "name": "creeper_head", "displayName": "Creeper Head", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"metadata": 5, "id": 1001, "name": "dragon_head", "displayName": "Dragon Head", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}]}, {"id": 1002, "stackSize": 64, "name": "nether_star", "displayName": "Nether Star"}, {"id": 1003, "stackSize": 64, "name": "pumpkin_pie", "displayName": "Pumpkin Pie"}, {"id": 1004, "stackSize": 64, "name": "firework_rocket", "displayName": "Firework Rocket"}, {"id": 1005, "stackSize": 64, "name": "firework_star", "displayName": "Firework Star"}, {"id": 1006, "stackSize": 1, "name": "enchanted_book", "displayName": "Enchanted Book"}, {"id": 1007, "stackSize": 64, "name": "netherbrick", "displayName": "Nether Brick"}, {"id": 1008, "stackSize": 64, "name": "prismarine_shard", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 1009, "stackSize": 64, "name": "prismarine_crystals", "displayName": "Prismarine Crystals"}, {"id": 1010, "stackSize": 64, "name": "rabbit", "displayName": "Raw Rabbit"}, {"id": 1011, "stackSize": 64, "name": "cooked_rabbit", "displayName": "Cooked Rabbit"}, {"id": 1012, "stackSize": 1, "name": "rabbit_stew", "displayName": "Rabbit Stew"}, {"id": 1013, "stackSize": 64, "name": "rabbit_foot", "displayName": "<PERSON>'s Foot"}, {"id": 1014, "stackSize": 64, "name": "rabbit_hide", "displayName": "<PERSON>"}, {"id": 1015, "stackSize": 16, "name": "armor_stand", "displayName": "Armor Stand"}, {"id": 1016, "stackSize": 1, "name": "iron_horse_armor", "displayName": "Iron Horse Armor"}, {"id": 1017, "stackSize": 1, "name": "golden_horse_armor", "displayName": "Golden Horse Armor"}, {"id": 1018, "stackSize": 1, "name": "diamond_horse_armor", "displayName": "Diamond Horse Armor"}, {"id": 1019, "stackSize": 1, "name": "leather_horse_armor", "displayName": "Leather Horse Armor"}, {"id": 1020, "stackSize": 64, "name": "lead", "displayName": "Lead"}, {"id": 1021, "stackSize": 64, "name": "name_tag", "displayName": "Name Tag"}, {"id": 1022, "stackSize": 1, "name": "command_block_minecart", "displayName": "Minecart with Command Block"}, {"id": 1023, "stackSize": 64, "name": "mutton", "displayName": "<PERSON>"}, {"id": 1024, "stackSize": 64, "name": "cooked_mutton", "displayName": "Cooked <PERSON>tton"}, {"id": 1040, "displayName": "Black Banner", "name": "banner", "stackSize": 16, "metadata": 0, "variations": [{"metadata": 1, "id": 1039, "name": "red_banner", "displayName": "Red Banner", "stackSize": 16}, {"metadata": 2, "id": 1038, "name": "green_banner", "displayName": "<PERSON> Banner", "stackSize": 16}, {"metadata": 3, "id": 1037, "name": "brown_banner", "displayName": "<PERSON>", "stackSize": 16}, {"metadata": 4, "id": 1036, "name": "blue_banner", "displayName": "Blue Banner", "stackSize": 16}, {"metadata": 5, "id": 1035, "name": "purple_banner", "displayName": "<PERSON> Banner", "stackSize": 16}, {"metadata": 6, "id": 1034, "name": "cyan_banner", "displayName": "<PERSON><PERSON>", "stackSize": 16}, {"metadata": 7, "id": 1033, "name": "light_gray_banner", "displayName": "<PERSON> Gray Banner", "stackSize": 16}, {"metadata": 8, "id": 1032, "name": "gray_banner", "displayName": "<PERSON>", "stackSize": 16}, {"metadata": 9, "id": 1031, "name": "pink_banner", "displayName": "Pink Banner", "stackSize": 16}, {"metadata": 10, "id": 1030, "name": "lime_banner", "displayName": "Lime Banner", "stackSize": 16}, {"metadata": 11, "id": 1029, "name": "yellow_banner", "displayName": "Yellow Banner", "stackSize": 16}, {"metadata": 12, "id": 1028, "name": "light_blue_banner", "displayName": "Light Blue Banner", "stackSize": 16}, {"metadata": 13, "id": 1027, "name": "magenta_banner", "displayName": "Magenta Banner", "stackSize": 16}, {"metadata": 14, "id": 1026, "name": "orange_banner", "displayName": "Orange Banner", "stackSize": 16}, {"metadata": 15, "id": 1025, "name": "white_banner", "displayName": "White Banner", "stackSize": 16}]}, {"id": 1041, "stackSize": 64, "name": "end_crystal", "displayName": "End Crystal"}, {"id": 1042, "stackSize": 64, "name": "chorus_fruit", "displayName": "Chorus Fruit"}, {"id": 1043, "stackSize": 64, "name": "popped_chorus_fruit", "displayName": "Popped Chorus Fruit"}, {"id": 1044, "stackSize": 64, "name": "beetroot", "displayName": "Beetroot"}, {"id": 1045, "stackSize": 64, "name": "beetroot_seeds", "displayName": "Beetroot Seeds"}, {"id": 1046, "stackSize": 1, "name": "beetroot_soup", "displayName": "Beetroot Soup"}, {"id": 1047, "stackSize": 64, "name": "dragon_breath", "displayName": "Dragon's Breath"}, {"id": 1048, "stackSize": 1, "name": "splash_potion", "displayName": "Splash Potion"}, {"id": 1051, "stackSize": 1, "name": "lingering_potion", "displayName": "Lingering Potion"}, {"id": 1052, "stackSize": 1, "name": "shield", "displayName": "Shield", "enchantCategories": ["breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "mangrove_planks", "crimson_planks", "warped_planks"], "maxDurability": 336}, {"id": 1053, "stackSize": 1, "name": "totem_of_undying", "displayName": "Totem of Undying"}, {"id": 1055, "stackSize": 64, "name": "iron_nugget", "displayName": "Iron Nugget"}, {"id": 1058, "stackSize": 1, "name": "music_disc_13", "displayName": "Music Disc"}, {"id": 1059, "stackSize": 1, "name": "music_disc_cat", "displayName": "Music Disc"}, {"id": 1060, "stackSize": 1, "name": "music_disc_blocks", "displayName": "Music Disc"}, {"id": 1061, "stackSize": 1, "name": "music_disc_chirp", "displayName": "Music Disc"}, {"id": 1062, "stackSize": 1, "name": "music_disc_far", "displayName": "Music Disc"}, {"id": 1063, "stackSize": 1, "name": "music_disc_mall", "displayName": "Music Disc"}, {"id": 1064, "stackSize": 1, "name": "music_disc_mellohi", "displayName": "Music Disc"}, {"id": 1065, "stackSize": 1, "name": "music_disc_stal", "displayName": "Music Disc"}, {"id": 1066, "stackSize": 1, "name": "music_disc_strad", "displayName": "Music Disc"}, {"id": 1067, "stackSize": 1, "name": "music_disc_ward", "displayName": "Music Disc"}, {"id": 1068, "stackSize": 1, "name": "music_disc_11", "displayName": "Music Disc"}, {"id": 1069, "stackSize": 1, "name": "music_disc_wait", "displayName": "Music Disc"}, {"id": 1070, "stackSize": 1, "name": "music_disc_otherside", "displayName": "Music Disc"}, {"id": 1071, "stackSize": 1, "name": "music_disc_5", "displayName": "Music Disc"}, {"id": 1072, "stackSize": 1, "name": "music_disc_pigstep", "displayName": "Music Disc"}, {"id": 1073, "stackSize": 64, "name": "disc_fragment_5", "displayName": "Disc Fragment"}, {"id": 1074, "stackSize": 1, "name": "trident", "displayName": "Trident", "enchantCategories": ["trident", "breakable", "vanishable"], "maxDurability": 250}, {"id": 1075, "stackSize": 64, "name": "phantom_membrane", "displayName": "Phantom Membrane"}, {"id": 1076, "stackSize": 64, "name": "nautilus_shell", "displayName": "Nautilus Shell"}, {"id": 1077, "stackSize": 64, "name": "heart_of_the_sea", "displayName": "Heart of the Sea"}, {"id": 1078, "stackSize": 1, "name": "crossbow", "displayName": "Crossbow", "enchantCategories": ["breakable", "crossbow", "vanishable"], "maxDurability": 465}, {"id": 1079, "stackSize": 1, "name": "suspicious_stew", "displayName": "Suspicious Stew"}, {"id": 1080, "stackSize": 64, "name": "loom", "displayName": "Loom"}, {"id": 1081, "stackSize": 1, "name": "flower_banner_pattern", "displayName": "<PERSON>"}, {"id": 1082, "stackSize": 1, "name": "creeper_banner_pattern", "displayName": "<PERSON>"}, {"id": 1083, "stackSize": 1, "name": "skull_banner_pattern", "displayName": "<PERSON>"}, {"id": 1084, "stackSize": 1, "name": "mojang_banner_pattern", "displayName": "<PERSON>"}, {"id": 1085, "stackSize": 1, "name": "globe_banner_pattern", "displayName": "<PERSON>"}, {"id": 1086, "stackSize": 1, "name": "piglin_banner_pattern", "displayName": "<PERSON>"}, {"id": 1087, "stackSize": 1, "name": "goat_horn", "displayName": "<PERSON><PERSON>"}, {"id": 1088, "stackSize": 64, "name": "composter", "displayName": "Composter"}, {"id": 1089, "stackSize": 64, "name": "barrel", "displayName": "Barrel"}, {"id": 1090, "stackSize": 64, "name": "smoker", "displayName": "Smoker"}, {"id": 1091, "stackSize": 64, "name": "blast_furnace", "displayName": "Blast Furnace"}, {"id": 1092, "stackSize": 64, "name": "cartography_table", "displayName": "Cartography Table"}, {"id": 1093, "stackSize": 64, "name": "fletching_table", "displayName": "Fletching Table"}, {"id": 1094, "stackSize": 64, "name": "grindstone", "displayName": "Grindstone"}, {"id": 1095, "stackSize": 64, "name": "smithing_table", "displayName": "Smithing Table"}, {"id": 1096, "stackSize": 64, "name": "stonecutter_block", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 1097, "stackSize": 64, "name": "bell", "displayName": "Bell"}, {"id": 1098, "stackSize": 64, "name": "lantern", "displayName": "Lantern"}, {"id": 1099, "stackSize": 64, "name": "soul_lantern", "displayName": "Soul Lantern"}, {"id": 1100, "stackSize": 64, "name": "sweet_berries", "displayName": "Sweet Berries"}, {"id": 1101, "stackSize": 64, "name": "glow_berries", "displayName": "Glow Berries"}, {"id": 1102, "stackSize": 64, "name": "campfire", "displayName": "Campfire"}, {"id": 1103, "stackSize": 64, "name": "soul_campfire", "displayName": "Soul Campfire"}, {"id": 1104, "stackSize": 64, "name": "shroomlight", "displayName": "Shroomlight"}, {"id": 1105, "stackSize": 64, "name": "honeycomb", "displayName": "Honeycomb"}, {"id": 1106, "stackSize": 64, "name": "bee_nest", "displayName": "Bee Nest"}, {"id": 1107, "stackSize": 64, "name": "beehive", "displayName": "Beehive"}, {"id": 1108, "stackSize": 16, "name": "honey_bottle", "displayName": "<PERSON>"}, {"id": 1109, "stackSize": 64, "name": "honeycomb_block", "displayName": "Honeycomb Block"}, {"id": 1110, "stackSize": 64, "name": "lodestone", "displayName": "Lodestone"}, {"id": 1111, "stackSize": 64, "name": "crying_obsidian", "displayName": "Crying Obsidian"}, {"id": 1112, "stackSize": 64, "name": "blackstone", "displayName": "Blackstone"}, {"id": 1113, "stackSize": 64, "name": "blackstone_slab", "displayName": "Blackstone Slab"}, {"id": 1114, "stackSize": 64, "name": "blackstone_stairs", "displayName": "Blackstone Stairs"}, {"id": 1115, "stackSize": 64, "name": "gilded_blackstone", "displayName": "Gilded Blackstone"}, {"id": 1116, "stackSize": 64, "name": "polished_blackstone", "displayName": "Polished Blackstone"}, {"id": 1117, "stackSize": 64, "name": "polished_blackstone_slab", "displayName": "Polished Blackstone Slab"}, {"id": 1118, "stackSize": 64, "name": "polished_blackstone_stairs", "displayName": "Polished Blackstone Stairs"}, {"id": 1119, "stackSize": 64, "name": "chiseled_polished_blackstone", "displayName": "Chiseled Polished Blackstone"}, {"id": 1120, "stackSize": 64, "name": "polished_blackstone_bricks", "displayName": "Polished Blackstone Bricks"}, {"id": 1121, "stackSize": 64, "name": "polished_blackstone_brick_slab", "displayName": "Polished Blackstone Brick Slab"}, {"id": 1122, "stackSize": 64, "name": "polished_blackstone_brick_stairs", "displayName": "Polished Blackstone Brick Stairs"}, {"id": 1123, "stackSize": 64, "name": "cracked_polished_blackstone_bricks", "displayName": "Cracked Polished Blackstone Bricks"}, {"id": 1124, "stackSize": 64, "name": "respawn_anchor", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 1125, "stackSize": 64, "name": "candle", "displayName": "Candle"}, {"id": 1126, "stackSize": 64, "name": "white_candle", "displayName": "White Candle"}, {"id": 1127, "stackSize": 64, "name": "orange_candle", "displayName": "Orange Candle"}, {"id": 1128, "stackSize": 64, "name": "magenta_candle", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 1129, "stackSize": 64, "name": "light_blue_candle", "displayName": "Light Blue Candle"}, {"id": 1130, "stackSize": 64, "name": "yellow_candle", "displayName": "Yellow Candle"}, {"id": 1131, "stackSize": 64, "name": "lime_candle", "displayName": "<PERSON><PERSON>"}, {"id": 1132, "stackSize": 64, "name": "pink_candle", "displayName": "Pink Candle"}, {"id": 1133, "stackSize": 64, "name": "gray_candle", "displayName": "<PERSON>"}, {"id": 1134, "stackSize": 64, "name": "light_gray_candle", "displayName": "Light Gray Candle"}, {"id": 1135, "stackSize": 64, "name": "cyan_candle", "displayName": "<PERSON><PERSON>"}, {"id": 1136, "stackSize": 64, "name": "purple_candle", "displayName": "Purple Candle"}, {"id": 1137, "stackSize": 64, "name": "blue_candle", "displayName": "Blue Candle"}, {"id": 1138, "stackSize": 64, "name": "brown_candle", "displayName": "<PERSON> Candle"}, {"id": 1139, "stackSize": 64, "name": "green_candle", "displayName": "Green Candle"}, {"id": 1140, "stackSize": 64, "name": "red_candle", "displayName": "<PERSON> Candle"}, {"id": 1141, "stackSize": 64, "name": "black_candle", "displayName": "Black Candle"}, {"id": 1142, "stackSize": 64, "name": "small_amethyst_bud", "displayName": "Small Amethyst Bud"}, {"id": 1143, "stackSize": 64, "name": "medium_amethyst_bud", "displayName": "Medium Amethyst Bud"}, {"id": 1144, "stackSize": 64, "name": "large_amethyst_bud", "displayName": "Large Amethyst Bud"}, {"id": 1145, "stackSize": 64, "name": "amethyst_cluster", "displayName": "Amethyst Cluster"}, {"id": 1146, "stackSize": 64, "name": "pointed_dripstone", "displayName": "Pointed Dripstone"}, {"id": 1147, "stackSize": 64, "name": "ochre_froglight", "displayName": "Ochre Froglight"}, {"id": 1148, "stackSize": 64, "name": "verdant_froglight", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 1149, "stackSize": 64, "name": "pearlescent_froglight", "displayName": "Pearlescent Froglight"}, {"id": 1150, "stackSize": 64, "name": "frog_spawn", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 1151, "stackSize": 64, "name": "echo_shard", "displayName": "Echo Shard"}, {"id": 9010, "stackSize": 1, "name": "element_25", "displayName": "Element 25"}, {"id": 9017, "stackSize": 1, "name": "element_50", "displayName": "Element 50"}, {"id": 9026, "stackSize": 1, "name": "element_15", "displayName": "Element 15"}, {"id": 9028, "stackSize": 1, "name": "item.dark_oak_door", "displayName": "Dark Oak Door"}, {"id": 9031, "stackSize": 1, "name": "portal", "displayName": "Portal"}, {"id": 9039, "stackSize": 1, "name": "element_7", "displayName": "Element 7"}, {"id": 9048, "stackSize": 1, "name": "item.iron_door", "displayName": "Iron Door"}, {"id": 9063, "stackSize": 1, "name": "sparkler", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 9065, "stackSize": 1, "name": "element_61", "displayName": "Element 61"}, {"id": 9072, "stackSize": 1, "name": "item.campfire", "displayName": "Campfire"}, {"id": 9073, "stackSize": 1, "name": "magenta_candle_cake", "displayName": "Ma<PERSON>a Candle Cake"}, {"id": 9074, "stackSize": 1, "name": "mangrove_double_slab", "displayName": "Mangrove Double Slab"}, {"id": 9078, "stackSize": 1, "name": "element_1", "displayName": "Element 1"}, {"id": 9092, "stackSize": 1, "name": "element_62", "displayName": "Element 62"}, {"id": 9109, "stackSize": 1, "name": "element_13", "displayName": "Element 13"}, {"id": 9117, "stackSize": 1, "name": "element_43", "displayName": "Element 43"}, {"id": 9125, "stackSize": 1, "name": "item.crimson_door", "displayName": "Crimson Door"}, {"id": 9128, "stackSize": 1, "name": "element_2", "displayName": "Element 2"}, {"id": 9130, "stackSize": 1, "name": "spawn_egg", "displayName": "Spawn Egg"}, {"id": 9134, "stackSize": 1, "name": "element_35", "displayName": "Element 35"}, {"id": 9137, "stackSize": 1, "name": "element_104", "displayName": "Element 104"}, {"id": 9146, "stackSize": 1, "name": "carrots", "displayName": "Carrots"}, {"id": 9166, "stackSize": 1, "name": "flowing_water", "displayName": "Flowing Water"}, {"id": 9169, "stackSize": 1, "name": "lit_deepslate_redstone_ore", "displayName": "Lit Deepslate Redstone Ore"}, {"id": 9173, "stackSize": 1, "name": "lit_redstone_lamp", "displayName": "Lit Redstone Lamp"}, {"id": 9175, "stackSize": 1, "name": "element_52", "displayName": "Element 52"}, {"id": 9186, "stackSize": 1, "name": "element_86", "displayName": "Element 86"}, {"id": 9200, "stackSize": 1, "name": "end_gateway", "displayName": "End Gateway"}, {"id": 9202, "stackSize": 1, "name": "item.beetroot", "displayName": "Beetroot"}, {"id": 9203, "stackSize": 1, "name": "element_101", "displayName": "Element 101"}, {"id": 9205, "stackSize": 1, "name": "element_49", "displayName": "Element 49"}, {"id": 9227, "stackSize": 1, "name": "element_51", "displayName": "Element 51"}, {"id": 9228, "stackSize": 1, "name": "double_wooden_slab", "displayName": "Double Wooden Slab"}, {"id": 9229, "stackSize": 1, "name": "hard_stained_glass", "displayName": "Hard Stained Glass"}, {"id": 9230, "stackSize": 1, "name": "element_84", "displayName": "Element 84"}, {"id": 9237, "stackSize": 1, "name": "flowing_lava", "displayName": "Flowing Lava"}, {"id": 9239, "stackSize": 1, "name": "agent_spawn_egg", "displayName": "Agent Spawn Egg"}, {"id": 9245, "stackSize": 1, "name": "element_55", "displayName": "Element 55"}, {"id": 9253, "stackSize": 1, "name": "element_74", "displayName": "Element 74"}, {"id": 9267, "stackSize": 1, "name": "item.nether_wart", "displayName": "Nether Wart"}, {"id": 9274, "stackSize": 1, "name": "element_116", "displayName": "Element 116"}, {"id": 9284, "stackSize": 1, "name": "element_97", "displayName": "Element 97"}, {"id": 9288, "stackSize": 1, "name": "chemistry_table", "displayName": "Chemistry Table"}, {"id": 9304, "stackSize": 1, "name": "element_23", "displayName": "Element 23"}, {"id": 9315, "stackSize": 1, "name": "item.reeds", "displayName": "<PERSON><PERSON>"}, {"id": 9320, "stackSize": 1, "name": "reserved6", "displayName": "Reserved6"}, {"id": 9329, "stackSize": 1, "name": "deny", "displayName": "<PERSON><PERSON>"}, {"id": 9334, "stackSize": 1, "name": "item.cake", "displayName": "Cake"}, {"id": 9336, "stackSize": 1, "name": "dye", "displayName": "Dye"}, {"id": 9341, "stackSize": 1, "name": "sticky_piston_arm_collision", "displayName": "Sticky Piston Arm Collision"}, {"id": 9343, "stackSize": 1, "name": "element_41", "displayName": "Element 41"}, {"id": 9358, "stackSize": 1, "name": "item.flower_pot", "displayName": "Flower Pot"}, {"id": 9360, "stackSize": 1, "name": "unknown", "displayName": "Unknown"}, {"id": 9365, "stackSize": 1, "name": "camera", "displayName": "Camera"}, {"id": 9380, "stackSize": 1, "name": "unpowered_comparator", "displayName": "Unpowered Comparator"}, {"id": 9387, "stackSize": 1, "name": "element_44", "displayName": "Element 44"}, {"id": 9389, "stackSize": 1, "name": "green_candle_cake", "displayName": "Green Candle Cake"}, {"id": 9398, "stackSize": 1, "name": "element_111", "displayName": "Element 111"}, {"id": 9407, "stackSize": 1, "name": "colored_torch_rg", "displayName": "Colored Torch Rg"}, {"id": 9408, "stackSize": 1, "name": "bleach", "displayName": "Bleach"}, {"id": 9410, "stackSize": 1, "name": "element_21", "displayName": "Element 21"}, {"id": 9415, "stackSize": 1, "name": "powered_comparator", "displayName": "Powered Comparator"}, {"id": 9417, "stackSize": 1, "name": "element_0", "displayName": "Element 0"}, {"id": 9428, "stackSize": 1, "name": "item.mangrove_door", "displayName": "Mangrove Door"}, {"id": 9433, "stackSize": 1, "name": "pink_candle_cake", "displayName": "Pink Candle Cake"}, {"id": 9435, "stackSize": 1, "name": "glowingobsidian", "displayName": "Glowingobsidian"}, {"id": 9440, "stackSize": 1, "name": "element_109", "displayName": "Element 109"}, {"id": 9445, "stackSize": 1, "name": "npc_spawn_egg", "displayName": "Npc Spawn Egg"}, {"id": 9447, "stackSize": 1, "name": "element_80", "displayName": "Element 80"}, {"id": 9449, "stackSize": 1, "name": "powder_snow", "displayName": "Powder Snow"}, {"id": 9451, "stackSize": 1, "name": "spruce_wall_sign", "displayName": "Spruce Wall Sign"}, {"id": 9454, "stackSize": 1, "name": "daylight_detector_inverted", "displayName": "Daylight Detector Inverted"}, {"id": 9458, "stackSize": 1, "name": "rapid_fertilizer", "displayName": "Rapid Fertilizer"}, {"id": 9462, "stackSize": 1, "name": "standing_sign", "displayName": "Standing Sign"}, {"id": 9465, "stackSize": 1, "name": "item.frame", "displayName": "<PERSON>ame"}, {"id": 9473, "stackSize": 1, "name": "double_cut_copper_slab", "displayName": "Double Cut Copper Slab"}, {"id": 9474, "stackSize": 1, "name": "element_28", "displayName": "Element 28"}, {"id": 9477, "stackSize": 1, "name": "item.acacia_door", "displayName": "Acacia Door"}, {"id": 9488, "stackSize": 1, "name": "orange_candle_cake", "displayName": "Orange Candle Cake"}, {"id": 9490, "stackSize": 1, "name": "double_stone_block_slab2", "displayName": "Double Stone Block Slab2"}, {"id": 9499, "stackSize": 1, "name": "double_stone_block_slab3", "displayName": "Double Stone Block Slab3"}, {"id": 9506, "stackSize": 1, "name": "birch_standing_sign", "displayName": "<PERSON> Standing Sign"}, {"id": 9510, "stackSize": 1, "name": "element_47", "displayName": "Element 47"}, {"id": 9515, "stackSize": 1, "name": "element_69", "displayName": "Element 69"}, {"id": 9517, "stackSize": 1, "name": "deepslate_brick_double_slab", "displayName": "Deepslate Brick Double Slab"}, {"id": 9519, "stackSize": 1, "name": "mangrove_wall_sign", "displayName": "Mangrove Wall Sign"}, {"id": 9523, "stackSize": 1, "name": "colored_torch_bp", "displayName": "Colored Torch Bp"}, {"id": 9525, "stackSize": 1, "name": "element_102", "displayName": "Element 102"}, {"id": 9529, "stackSize": 1, "name": "element_63", "displayName": "Element 63"}, {"id": 9544, "stackSize": 1, "name": "info_update2", "displayName": "Info Update2"}, {"id": 9554, "stackSize": 1, "name": "element_42", "displayName": "Element 42"}, {"id": 9558, "stackSize": 1, "name": "element_73", "displayName": "Element 73"}, {"id": 9565, "stackSize": 1, "name": "coral_fan_hang2", "displayName": "Coral Fan Hang2"}, {"id": 9566, "stackSize": 1, "name": "balloon", "displayName": "Balloon"}, {"id": 9573, "stackSize": 1, "name": "field_masoned_banner_pattern", "displayName": "Field Masoned Banner Pattern"}, {"id": 9576, "stackSize": 1, "name": "bordure_indented_banner_pattern", "displayName": "Bordure Indented Banner Pattern"}, {"id": 9577, "stackSize": 1, "name": "purple_candle_cake", "displayName": "Purple Candle Cake"}, {"id": 9579, "stackSize": 1, "name": "potatoes", "displayName": "Potatoes"}, {"id": 9580, "stackSize": 1, "name": "element_78", "displayName": "Element 78"}, {"id": 9585, "stackSize": 1, "name": "compound", "displayName": "Compound"}, {"id": 9586, "stackSize": 1, "name": "ice_bomb", "displayName": "Ice Bomb"}, {"id": 9587, "stackSize": 1, "name": "medicine", "displayName": "Medicine"}, {"id": 9590, "stackSize": 1, "name": "element_92", "displayName": "Element 92"}, {"id": 9591, "stackSize": 1, "name": "glow_stick", "displayName": "Glow Stick"}, {"id": 9592, "stackSize": 1, "name": "lodestone_compass", "displayName": "Lodestone Compass"}, {"id": 9593, "stackSize": 1, "name": "element_83", "displayName": "Element 83"}, {"id": 9598, "stackSize": 1, "name": "polished_deepslate_double_slab", "displayName": "Polished Deepslate Double Slab"}, {"id": 9599, "stackSize": 1, "name": "item.warped_door", "displayName": "Warped Door"}, {"id": 9605, "stackSize": 1, "name": "black_candle_cake", "displayName": "Black Candle Cake"}, {"id": 9619, "stackSize": 1, "name": "hard_glass", "displayName": "Hard Glass"}, {"id": 9621, "stackSize": 1, "name": "element_76", "displayName": "Element 76"}, {"id": 9624, "stackSize": 1, "name": "element_33", "displayName": "Element 33"}, {"id": 9629, "stackSize": 1, "name": "item.hopper", "displayName": "<PERSON>"}, {"id": 9630, "stackSize": 1, "name": "mud_brick_double_slab", "displayName": "Mud Brick Double Slab"}, {"id": 9633, "stackSize": 1, "name": "waxed_weathered_double_cut_copper_slab", "displayName": "Waxed Weathered Double Cut Copper Slab"}, {"id": 9635, "stackSize": 1, "name": "element_22", "displayName": "Element 22"}, {"id": 9636, "stackSize": 1, "name": "blue_candle_cake", "displayName": "Blue Candle Cake"}, {"id": 9641, "stackSize": 1, "name": "powered_repeater", "displayName": "Powered Repeater"}, {"id": 9647, "stackSize": 1, "name": "jungle_standing_sign", "displayName": "Jungle Standing Sign"}, {"id": 9650, "stackSize": 1, "name": "cyan_candle_cake", "displayName": "<PERSON><PERSON>"}, {"id": 9651, "stackSize": 1, "name": "candle_cake", "displayName": "Candle Cake"}, {"id": 9658, "stackSize": 1, "name": "info_update", "displayName": "Info Update"}, {"id": 9661, "stackSize": 1, "name": "chest_boat", "displayName": "Chest Boat"}, {"id": 9664, "stackSize": 1, "name": "trader_llama_spawn_egg", "displayName": "Trader <PERSON>lama Spawn Egg"}, {"id": 9668, "stackSize": 1, "name": "element_53", "displayName": "Element 53"}, {"id": 9669, "stackSize": 1, "name": "lit_blast_furnace", "displayName": "Lit Blast Furnace"}, {"id": 9672, "stackSize": 1, "name": "double_stone_block_slab", "displayName": "Double Stone Block Slab"}, {"id": 9673, "stackSize": 1, "name": "double_stone_block_slab4", "displayName": "Double Stone Block Slab4"}, {"id": 9679, "stackSize": 1, "name": "polished_blackstone_double_slab", "displayName": "Polished Blackstone Double Slab"}, {"id": 9680, "stackSize": 1, "name": "blackstone_double_slab", "displayName": "Blackstone Double Slab"}, {"id": 9684, "stackSize": 1, "name": "soul_fire", "displayName": "Soul Fire"}, {"id": 9689, "stackSize": 1, "name": "element_103", "displayName": "Element 103"}, {"id": 9692, "stackSize": 1, "name": "spruce_standing_sign", "displayName": "Spruce Standing Sign"}, {"id": 9699, "stackSize": 1, "name": "hard_stained_glass_pane", "displayName": "Hard Stained Glass Pane"}, {"id": 9708, "stackSize": 1, "name": "fire", "displayName": "Fire"}, {"id": 9714, "stackSize": 1, "name": "birch_wall_sign", "displayName": "<PERSON> Sign"}, {"id": 9717, "stackSize": 1, "name": "element_3", "displayName": "Element 3"}, {"id": 9718, "stackSize": 1, "name": "element_4", "displayName": "Element 4"}, {"id": 9720, "stackSize": 1, "name": "element_5", "displayName": "Element 5"}, {"id": 9722, "stackSize": 1, "name": "element_6", "displayName": "Element 6"}, {"id": 9723, "stackSize": 1, "name": "element_8", "displayName": "Element 8"}, {"id": 9725, "stackSize": 1, "name": "element_9", "displayName": "Element 9"}, {"id": 9726, "stackSize": 1, "name": "element_10", "displayName": "Element 10"}, {"id": 9727, "stackSize": 1, "name": "element_11", "displayName": "Element 11"}, {"id": 9728, "stackSize": 1, "name": "element_12", "displayName": "Element 12"}, {"id": 9729, "stackSize": 1, "name": "element_14", "displayName": "Element 14"}, {"id": 9730, "stackSize": 1, "name": "element_16", "displayName": "Element 16"}, {"id": 9731, "stackSize": 1, "name": "element_17", "displayName": "Element 17"}, {"id": 9732, "stackSize": 1, "name": "client_request_placeholder_block", "displayName": "Client Request Placeholder Block"}, {"id": 9733, "stackSize": 1, "name": "element_18", "displayName": "Element 18"}, {"id": 9734, "stackSize": 1, "name": "element_19", "displayName": "Element 19"}, {"id": 9735, "stackSize": 1, "name": "element_20", "displayName": "Element 20"}, {"id": 9737, "stackSize": 1, "name": "element_24", "displayName": "Element 24"}, {"id": 9738, "stackSize": 1, "name": "element_26", "displayName": "Element 26"}, {"id": 9739, "stackSize": 1, "name": "element_27", "displayName": "Element 27"}, {"id": 9740, "stackSize": 1, "name": "element_29", "displayName": "Element 29"}, {"id": 9741, "stackSize": 1, "name": "element_30", "displayName": "Element 30"}, {"id": 9742, "stackSize": 1, "name": "element_31", "displayName": "Element 31"}, {"id": 9743, "stackSize": 1, "name": "element_32", "displayName": "Element 32"}, {"id": 9744, "stackSize": 1, "name": "element_34", "displayName": "Element 34"}, {"id": 9745, "stackSize": 1, "name": "element_36", "displayName": "Element 36"}, {"id": 9747, "stackSize": 1, "name": "element_37", "displayName": "Element 37"}, {"id": 9748, "stackSize": 1, "name": "element_38", "displayName": "Element 38"}, {"id": 9749, "stackSize": 1, "name": "element_39", "displayName": "Element 39"}, {"id": 9750, "stackSize": 1, "name": "element_40", "displayName": "Element 40"}, {"id": 9751, "stackSize": 1, "name": "element_45", "displayName": "Element 45"}, {"id": 9752, "stackSize": 1, "name": "element_46", "displayName": "Element 46"}, {"id": 9753, "stackSize": 1, "name": "element_48", "displayName": "Element 48"}, {"id": 9754, "stackSize": 1, "name": "element_54", "displayName": "Element 54"}, {"id": 9755, "stackSize": 1, "name": "element_56", "displayName": "Element 56"}, {"id": 9756, "stackSize": 1, "name": "element_57", "displayName": "Element 57"}, {"id": 9759, "stackSize": 1, "name": "lit_redstone_ore", "displayName": "Lit Redstone Ore"}, {"id": 9761, "stackSize": 1, "name": "element_58", "displayName": "Element 58"}, {"id": 9762, "stackSize": 1, "name": "element_59", "displayName": "Element 59"}, {"id": 9763, "stackSize": 1, "name": "element_60", "displayName": "Element 60"}, {"id": 9764, "stackSize": 1, "name": "light_gray_candle_cake", "displayName": "Light Gray Candle Cake"}, {"id": 9765, "stackSize": 1, "name": "element_64", "displayName": "Element 64"}, {"id": 9766, "stackSize": 1, "name": "element_65", "displayName": "Element 65"}, {"id": 9767, "stackSize": 1, "name": "element_66", "displayName": "Element 66"}, {"id": 9768, "stackSize": 1, "name": "element_67", "displayName": "Element 67"}, {"id": 9769, "stackSize": 1, "name": "element_68", "displayName": "Element 68"}, {"id": 9770, "stackSize": 1, "name": "element_70", "displayName": "Element 70"}, {"id": 9771, "stackSize": 1, "name": "element_71", "displayName": "Element 71"}, {"id": 9773, "stackSize": 1, "name": "element_72", "displayName": "Element 72"}, {"id": 9774, "stackSize": 1, "name": "element_75", "displayName": "Element 75"}, {"id": 9776, "stackSize": 1, "name": "element_77", "displayName": "Element 77"}, {"id": 9778, "stackSize": 1, "name": "element_79", "displayName": "Element 79"}, {"id": 9780, "stackSize": 1, "name": "element_81", "displayName": "Element 81"}, {"id": 9781, "stackSize": 1, "name": "element_82", "displayName": "Element 82"}, {"id": 9783, "stackSize": 1, "name": "element_85", "displayName": "Element 85"}, {"id": 9784, "stackSize": 1, "name": "element_87", "displayName": "Element 87"}, {"id": 9785, "stackSize": 1, "name": "element_88", "displayName": "Element 88"}, {"id": 9786, "stackSize": 1, "name": "element_89", "displayName": "Element 89"}, {"id": 9787, "stackSize": 1, "name": "element_90", "displayName": "Element 90"}, {"id": 9788, "stackSize": 1, "name": "element_91", "displayName": "Element 91"}, {"id": 9789, "stackSize": 1, "name": "element_93", "displayName": "Element 93"}, {"id": 9790, "stackSize": 1, "name": "element_94", "displayName": "Element 94"}, {"id": 9791, "stackSize": 1, "name": "element_95", "displayName": "Element 95"}, {"id": 9792, "stackSize": 1, "name": "element_96", "displayName": "Element 96"}, {"id": 9794, "stackSize": 1, "name": "element_98", "displayName": "Element 98"}, {"id": 9796, "stackSize": 1, "name": "element_99", "displayName": "Element 99"}, {"id": 9797, "stackSize": 1, "name": "element_100", "displayName": "Element 100"}, {"id": 9799, "stackSize": 1, "name": "element_105", "displayName": "Element 105"}, {"id": 9800, "stackSize": 1, "name": "element_106", "displayName": "Element 106"}, {"id": 9802, "stackSize": 1, "name": "element_107", "displayName": "Element 107"}, {"id": 9803, "stackSize": 1, "name": "element_108", "displayName": "Element 108"}, {"id": 9804, "stackSize": 1, "name": "element_110", "displayName": "Element 110"}, {"id": 9805, "stackSize": 1, "name": "element_112", "displayName": "Element 112"}, {"id": 9807, "stackSize": 1, "name": "element_113", "displayName": "Element 113"}, {"id": 9810, "stackSize": 1, "name": "element_114", "displayName": "Element 114"}, {"id": 9812, "stackSize": 1, "name": "element_115", "displayName": "Element 115"}, {"id": 9813, "stackSize": 1, "name": "element_117", "displayName": "Element 117"}, {"id": 9814, "stackSize": 1, "name": "element_118", "displayName": "Element 118"}, {"id": 9816, "stackSize": 1, "name": "white_candle_cake", "displayName": "White Candle Cake"}, {"id": 9819, "stackSize": 1, "name": "boat", "displayName": "Boat"}, {"id": 9820, "stackSize": 1, "name": "banner_pattern", "displayName": "<PERSON>"}, {"id": 9824, "stackSize": 1, "name": "item.glow_frame", "displayName": "Glow Frame"}, {"id": 9827, "stackSize": 1, "name": "piston_arm_collision", "displayName": "Piston Arm Collision"}, {"id": 9832, "stackSize": 1, "name": "gray_candle_cake", "displayName": "Gray Candle Cake"}, {"id": 9833, "stackSize": 1, "name": "crimson_wall_sign", "displayName": "Crimson Wall Sign"}, {"id": 9846, "stackSize": 1, "name": "warped_wall_sign", "displayName": "Warped Wall Sign"}, {"id": 9848, "stackSize": 1, "name": "coral_fan_hang", "displayName": "Coral Fan Hang"}, {"id": 9852, "stackSize": 1, "name": "light_blue_candle_cake", "displayName": "Light Blue Candle Cake"}, {"id": 9861, "stackSize": 1, "name": "deepslate_tile_double_slab", "displayName": "Deepslate Tile Double Slab"}, {"id": 9862, "stackSize": 1, "name": "oxidized_double_cut_copper_slab", "displayName": "Oxidized Double Cut Copper Slab"}, {"id": 9865, "stackSize": 1, "name": "lava_cauldron", "displayName": "<PERSON><PERSON>"}, {"id": 9866, "stackSize": 1, "name": "exposed_double_cut_copper_slab", "displayName": "Exposed Double Cut Copper Slab"}, {"id": 9868, "stackSize": 1, "name": "hard_glass_pane", "displayName": "Hard Glass Pane"}, {"id": 9869, "stackSize": 1, "name": "polished_blackstone_brick_double_slab", "displayName": "Polished Blackstone Brick Double Slab"}, {"id": 9870, "stackSize": 1, "name": "crimson_double_slab", "displayName": "Crimson Double Slab"}, {"id": 9879, "stackSize": 1, "name": "stonecutter", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 9881, "stackSize": 1, "name": "invisible_bedrock", "displayName": "Invisible Bedrock"}, {"id": 9883, "stackSize": 1, "name": "warped_standing_sign", "displayName": "Warped Standing Sign"}, {"id": 9885, "stackSize": 1, "name": "wall_sign", "displayName": "Wall Sign"}, {"id": 9892, "stackSize": 1, "name": "underwater_torch", "displayName": "Underwater Torch"}, {"id": 9896, "stackSize": 1, "name": "moving_block", "displayName": "Moving Block"}, {"id": 9900, "stackSize": 1, "name": "cave_vines_body_with_berries", "displayName": "Cave Vines Body With Berries"}, {"id": 9902, "stackSize": 1, "name": "brown_candle_cake", "displayName": "Brown Candle Cake"}, {"id": 9904, "stackSize": 1, "name": "acacia_wall_sign", "displayName": "Acacia Wall Sign"}, {"id": 9909, "stackSize": 1, "name": "item.wooden_door", "displayName": "Wooden Door"}, {"id": 9912, "stackSize": 1, "name": "redstone_wire", "displayName": "Redstone Wire"}, {"id": 9913, "stackSize": 1, "name": "lava", "displayName": "<PERSON><PERSON>"}, {"id": 9917, "stackSize": 1, "name": "coral_fan_hang3", "displayName": "Coral Fan Hang3"}, {"id": 9923, "stackSize": 1, "name": "mangrove_standing_sign", "displayName": "Mangrove Standing Sign"}, {"id": 9932, "stackSize": 1, "name": "warped_double_slab", "displayName": "Warped Double Slab"}, {"id": 9933, "stackSize": 1, "name": "jungle_wall_sign", "displayName": "Jungle Wall Sign"}, {"id": 9942, "stackSize": 1, "name": "yellow_candle_cake", "displayName": "Yellow Candle Cake"}, {"id": 9948, "stackSize": 1, "name": "item.wheat", "displayName": "Wheat"}, {"id": 9949, "stackSize": 1, "name": "item.spruce_door", "displayName": "Spruce Door"}, {"id": 9950, "stackSize": 1, "name": "frosted_ice", "displayName": "Frosted Ice"}, {"id": 9953, "stackSize": 1, "name": "cave_vines", "displayName": "Cave Vines"}, {"id": 9955, "stackSize": 1, "name": "melon_stem", "displayName": "Melon Stem"}, {"id": 9957, "stackSize": 1, "name": "border_block", "displayName": "Border Block"}, {"id": 9961, "stackSize": 1, "name": "item.skull", "displayName": "Skull"}, {"id": 9962, "stackSize": 1, "name": "darkoak_wall_sign", "displayName": "Darkoak Wall Sign"}, {"id": 9965, "stackSize": 1, "name": "waxed_double_cut_copper_slab", "displayName": "Waxed Double Cut Copper Slab"}, {"id": 9967, "stackSize": 1, "name": "item.kelp", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 9969, "stackSize": 1, "name": "water", "displayName": "Water"}, {"id": 9970, "stackSize": 1, "name": "chemical_heat", "displayName": "Chemical Heat"}, {"id": 9972, "stackSize": 1, "name": "unpowered_repeater", "displayName": "Unpowered Repeater"}, {"id": 9976, "stackSize": 1, "name": "wall_banner", "displayName": "<PERSON>"}, {"id": 9979, "stackSize": 1, "name": "bubble_column", "displayName": "Bubble Column"}, {"id": 9983, "stackSize": 1, "name": "cobbled_deepslate_double_slab", "displayName": "Cobbled Deepslate Double Slab"}, {"id": 9984, "stackSize": 1, "name": "item.nether_sprouts", "displayName": "Nether Sprouts"}, {"id": 9989, "stackSize": 1, "name": "sweet_berry_bush", "displayName": "Sweet <PERSON>"}, {"id": 9992, "stackSize": 1, "name": "item.jungle_door", "displayName": "Jungle Door"}, {"id": 9998, "stackSize": 1, "name": "acacia_standing_sign", "displayName": "Acacia Standing Sign"}, {"id": 10000, "stackSize": 1, "name": "pumpkin_stem", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 10006, "stackSize": 1, "name": "cocoa", "displayName": "Cocoa"}, {"id": 10009, "stackSize": 1, "name": "item.bed", "displayName": "Bed"}, {"id": 10012, "stackSize": 1, "name": "waxed_exposed_double_cut_copper_slab", "displayName": "Waxed Exposed Double Cut Copper Slab"}, {"id": 10015, "stackSize": 1, "name": "bamboo_sapling", "displayName": "Bamboo Sapling"}, {"id": 10016, "stackSize": 1, "name": "standing_banner", "displayName": "Standing Banner"}, {"id": 10028, "stackSize": 1, "name": "weathered_double_cut_copper_slab", "displayName": "Weathered Double Cut Copper Slab"}, {"id": 10031, "stackSize": 1, "name": "allow", "displayName": "Allow"}, {"id": 10032, "stackSize": 1, "name": "item.birch_door", "displayName": "<PERSON>"}, {"id": 10033, "stackSize": 1, "name": "item.chain", "displayName": "Chain"}, {"id": 10035, "stackSize": 1, "name": "lit_furnace", "displayName": "Lit Furnace"}, {"id": 10040, "stackSize": 1, "name": "item.camera", "displayName": "Camera"}, {"id": 10043, "stackSize": 1, "name": "crimson_standing_sign", "displayName": "Crimson Standing Sign"}, {"id": 10044, "stackSize": 1, "name": "darkoak_standing_sign", "displayName": "Darkoak Standing Sign"}, {"id": 10048, "stackSize": 1, "name": "netherreactor", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 10051, "stackSize": 1, "name": "trip_wire", "displayName": "<PERSON>"}, {"id": 10052, "stackSize": 1, "name": "item.cauldron", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 10053, "stackSize": 1, "name": "cave_vines_head_with_berries", "displayName": "Cave Vines Head With Berries"}, {"id": 10055, "stackSize": 1, "name": "item.brewing_stand", "displayName": "Brewing Stand"}, {"id": 10056, "stackSize": 1, "name": "end_portal", "displayName": "End Portal"}, {"id": 10058, "stackSize": 1, "name": "lit_smoker", "displayName": "Lit Smoker"}, {"id": 10060, "stackSize": 1, "name": "red_candle_cake", "displayName": "Red Candle Cake"}, {"id": 10062, "stackSize": 1, "name": "waxed_oxidized_double_cut_copper_slab", "displayName": "Waxed Oxidized Double Cut Copper Slab"}, {"id": 10069, "stackSize": 1, "name": "item.soul_campfire", "displayName": "Soul Campfire"}, {"id": 10071, "stackSize": 1, "name": "lime_candle_cake", "displayName": "Lime Candle Cake"}, {"id": 10075, "stackSize": 1, "name": "unlit_redstone_torch", "displayName": "Unlit Redstone Torch"}]