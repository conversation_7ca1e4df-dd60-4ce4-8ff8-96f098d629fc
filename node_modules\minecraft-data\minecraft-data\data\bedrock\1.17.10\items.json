[{"id": 0, "stackSize": 64, "name": "air", "displayName": "Air", "enchantCategories": [], "blockStateId": 134}, {"id": 1, "displayName": "Stone", "name": "stone", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 2, "name": "granite", "displayName": "Granite", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 3, "name": "polished_granite", "displayName": "Polished Granite", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 4, "name": "diorite", "displayName": "Diorite", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 5, "name": "polished_diorite", "displayName": "Polished Diorite", "stackSize": 64, "enchantCategories": []}, {"metadata": 5, "id": 6, "name": "andesite", "displayName": "Andesite", "stackSize": 64, "enchantCategories": []}, {"metadata": 6, "id": 7, "name": "polished_andesite", "displayName": "Polished Andesite", "stackSize": 64, "enchantCategories": []}]}, {"id": 8, "stackSize": 64, "name": "deepslate", "displayName": "Deepslate", "enchantCategories": [], "blockStateId": 4051}, {"id": 9, "stackSize": 64, "name": "cobbled_deepslate", "displayName": "Cobbled Deepslate", "enchantCategories": [], "blockStateId": 1102}, {"id": 10, "stackSize": 64, "name": "polished_deepslate", "displayName": "Polished Deepslate", "enchantCategories": [], "blockStateId": 6046}, {"id": 11, "stackSize": 64, "name": "calcite", "displayName": "Calcite", "enchantCategories": [], "blockStateId": 913}, {"id": 12, "stackSize": 64, "name": "tuff", "displayName": "<PERSON><PERSON>", "enchantCategories": [], "blockStateId": 7171}, {"id": 13, "stackSize": 64, "name": "dripstone_block", "displayName": "Dripstone Block", "enchantCategories": [], "blockStateId": 4534}, {"id": 14, "stackSize": 64, "name": "grass", "displayName": "Grass Block", "enchantCategories": []}, {"id": 15, "displayName": "Dirt", "name": "dirt", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 16, "name": "coarse_dirt", "displayName": "Coarse Dirt", "stackSize": 64, "enchantCategories": []}]}, {"id": 17, "stackSize": 64, "name": "podzol", "displayName": "Podzol", "enchantCategories": []}, {"id": 18, "stackSize": 64, "name": "dirt_with_roots", "displayName": "Rooted Dirt", "enchantCategories": [], "blockStateId": 4435}, {"id": 19, "stackSize": 64, "name": "crimson_nylium", "displayName": "Crimson Nylium", "enchantCategories": [], "blockStateId": 3798}, {"id": 20, "stackSize": 64, "name": "warped_nylium", "displayName": "Warped Nylium", "enchantCategories": [], "blockStateId": 7351}, {"id": 21, "stackSize": 64, "name": "cobblestone", "displayName": "Cobblestone", "enchantCategories": []}, {"id": 22, "displayName": "Oak Planks", "name": "planks", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 23, "name": "spruce_planks", "displayName": "Spruce Planks", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 24, "name": "birch_planks", "displayName": "Birch Planks", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 25, "name": "jungle_planks", "displayName": "Jungle Planks", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 26, "name": "acacia_planks", "displayName": "Acacia Planks", "stackSize": 64, "enchantCategories": []}, {"metadata": 5, "id": 27, "name": "dark_oak_planks", "displayName": "Dark Oak Planks", "stackSize": 64, "enchantCategories": []}]}, {"id": 28, "stackSize": 64, "name": "crimson_planks", "displayName": "Crimson Planks", "enchantCategories": [], "blockStateId": 3799}, {"id": 29, "stackSize": 64, "name": "warped_planks", "displayName": "Warped Planks", "enchantCategories": [], "blockStateId": 7352}, {"id": 30, "displayName": "Oak Sapling", "name": "sapling", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 31, "name": "spruce_sapling", "displayName": "Spruce Sapling", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 32, "name": "birch_sapling", "displayName": "Birch Sapling", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 33, "name": "jungle_sapling", "displayName": "Jungle Sapling", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 34, "name": "acacia_sapling", "displayName": "Acacia Sapling", "stackSize": 64, "enchantCategories": []}, {"metadata": 5, "id": 35, "name": "dark_oak_sapling", "displayName": "Dark Oak Sapling", "stackSize": 64, "enchantCategories": []}]}, {"id": 36, "stackSize": 64, "name": "bedrock", "displayName": "Bedrock", "enchantCategories": []}, {"id": 37, "displayName": "Sand", "name": "sand", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 38, "name": "red_sand", "displayName": "Red Sand", "stackSize": 64, "enchantCategories": []}]}, {"id": 39, "stackSize": 64, "name": "gravel", "displayName": "<PERSON>l", "enchantCategories": []}, {"id": 40, "stackSize": 64, "name": "coal_ore", "displayName": "Coal Ore", "enchantCategories": []}, {"id": 41, "stackSize": 64, "name": "deepslate_coal_ore", "displayName": "Deepslate Coal Ore", "enchantCategories": [], "blockStateId": 4227}, {"id": 42, "stackSize": 64, "name": "iron_ore", "displayName": "Iron Ore", "enchantCategories": []}, {"id": 43, "stackSize": 64, "name": "deepslate_iron_ore", "displayName": "Deepslate Iron Ore", "enchantCategories": [], "blockStateId": 4232}, {"id": 44, "stackSize": 64, "name": "copper_ore", "displayName": "Copper Ore", "enchantCategories": [], "blockStateId": 3637}, {"id": 45, "stackSize": 64, "name": "deepslate_copper_ore", "displayName": "Deepslate Copper Ore", "enchantCategories": [], "blockStateId": 4228}, {"id": 46, "stackSize": 64, "name": "gold_ore", "displayName": "Gold Ore", "enchantCategories": []}, {"id": 47, "stackSize": 64, "name": "deepslate_gold_ore", "displayName": "Deepslate Gold Ore", "enchantCategories": [], "blockStateId": 4231}, {"id": 48, "stackSize": 64, "name": "redstone_ore", "displayName": "Redstone Ore", "enchantCategories": []}, {"id": 49, "stackSize": 64, "name": "deepslate_redstone_ore", "displayName": "Deepslate Redstone Ore", "enchantCategories": [], "blockStateId": 4234}, {"id": 50, "stackSize": 64, "name": "emerald_ore", "displayName": "Emerald Ore", "enchantCategories": []}, {"id": 51, "stackSize": 64, "name": "deepslate_emerald_ore", "displayName": "Deepslate Emerald Ore", "enchantCategories": [], "blockStateId": 4230}, {"id": 52, "stackSize": 64, "name": "lapis_ore", "displayName": "Lapis <PERSON> Ore", "enchantCategories": []}, {"id": 53, "stackSize": 64, "name": "deepslate_lapis_ore", "displayName": "Deepslate Lapis Lazuli Ore", "enchantCategories": [], "blockStateId": 4233}, {"id": 54, "stackSize": 64, "name": "diamond_ore", "displayName": "Diamond Ore", "enchantCategories": []}, {"id": 55, "stackSize": 64, "name": "deepslate_diamond_ore", "displayName": "Deepslate Diamond Ore", "enchantCategories": [], "blockStateId": 4229}, {"id": 56, "stackSize": 64, "name": "nether_gold_ore", "displayName": "Nether Gold Ore", "enchantCategories": [], "blockStateId": 5561}, {"id": 57, "stackSize": 64, "name": "quartz_ore", "displayName": "<PERSON><PERSON>", "enchantCategories": []}, {"id": 58, "stackSize": 64, "name": "ancient_debris", "displayName": "Ancient Debris", "enchantCategories": [], "blockStateId": 143}, {"id": 59, "stackSize": 64, "name": "coal_block", "displayName": "Block of Coal", "enchantCategories": []}, {"id": 60, "stackSize": 64, "name": "raw_iron_block", "displayName": "Block of Raw Iron", "enchantCategories": [], "blockStateId": 6412}, {"id": 61, "stackSize": 64, "name": "raw_copper_block", "displayName": "Block of Raw Copper", "enchantCategories": [], "blockStateId": 6410}, {"id": 62, "stackSize": 64, "name": "raw_gold_block", "displayName": "Block of Raw Gold", "enchantCategories": [], "blockStateId": 6411}, {"id": 63, "stackSize": 64, "name": "amethyst_block", "displayName": "Block of Amethyst", "enchantCategories": [], "blockStateId": 136}, {"id": 64, "stackSize": 64, "name": "budding_amethyst", "displayName": "Budding Amethyst", "enchantCategories": [], "blockStateId": 889}, {"id": 65, "stackSize": 64, "name": "iron_block", "displayName": "Block of Iron", "enchantCategories": []}, {"id": 66, "stackSize": 64, "name": "copper_block", "displayName": "Block of Copper", "enchantCategories": [], "blockStateId": 3636}, {"id": 67, "stackSize": 64, "name": "gold_block", "displayName": "Block of Gold", "enchantCategories": []}, {"id": 68, "stackSize": 64, "name": "diamond_block", "displayName": "Block of Diamond", "enchantCategories": []}, {"id": 69, "stackSize": 64, "name": "netherite_block", "displayName": "Block of Netherite", "enchantCategories": [], "blockStateId": 5568}, {"id": 70, "stackSize": 64, "name": "exposed_copper", "displayName": "Exposed Copper", "enchantCategories": [], "blockStateId": 4701}, {"id": 71, "stackSize": 64, "name": "weathered_copper", "displayName": "Weathered Copper", "enchantCategories": [], "blockStateId": 7495}, {"id": 72, "stackSize": 64, "name": "oxidized_copper", "displayName": "Oxidized Copper", "enchantCategories": [], "blockStateId": 5607}, {"id": 73, "stackSize": 64, "name": "cut_copper", "displayName": "Cut Copper", "enchantCategories": [], "blockStateId": 3869}, {"id": 74, "stackSize": 64, "name": "exposed_cut_copper", "displayName": "Exposed Cut Copper", "enchantCategories": [], "blockStateId": 4702}, {"id": 75, "stackSize": 64, "name": "weathered_cut_copper", "displayName": "Weathered Cut Copper", "enchantCategories": [], "blockStateId": 7496}, {"id": 76, "stackSize": 64, "name": "oxidized_cut_copper", "displayName": "Oxidized Cut Copper", "enchantCategories": [], "blockStateId": 5608}, {"id": 77, "stackSize": 64, "name": "cut_copper_stairs", "displayName": "Cut Copper Stairs", "enchantCategories": [], "blockStateId": 3879}, {"id": 78, "stackSize": 64, "name": "exposed_cut_copper_stairs", "displayName": "Exposed Cut Copper Stairs", "enchantCategories": [], "blockStateId": 4712}, {"id": 79, "stackSize": 64, "name": "weathered_cut_copper_stairs", "displayName": "Weathered Cut Copper Stairs", "enchantCategories": [], "blockStateId": 7506}, {"id": 80, "stackSize": 64, "name": "oxidized_cut_copper_stairs", "displayName": "Oxidized Cut Copper Stairs", "enchantCategories": [], "blockStateId": 5618}, {"id": 81, "stackSize": 64, "name": "cut_copper_slab", "displayName": "Cut Copper Slab", "enchantCategories": [], "blockStateId": 3871}, {"id": 82, "stackSize": 64, "name": "exposed_cut_copper_slab", "displayName": "Exposed Cut Copper Slab", "enchantCategories": [], "blockStateId": 4704}, {"id": 83, "stackSize": 64, "name": "weathered_cut_copper_slab", "displayName": "Weathered Cut Copper Slab", "enchantCategories": [], "blockStateId": 7498}, {"id": 84, "stackSize": 64, "name": "oxidized_cut_copper_slab", "displayName": "Oxidized Cut Copper Slab", "enchantCategories": [], "blockStateId": 5610}, {"id": 85, "stackSize": 64, "name": "waxed_copper", "displayName": "Waxed Block of Copper", "enchantCategories": [], "blockStateId": 7439}, {"id": 86, "stackSize": 64, "name": "waxed_exposed_copper", "displayName": "Waxed Exposed Copper", "enchantCategories": [], "blockStateId": 7453}, {"id": 87, "stackSize": 64, "name": "waxed_weathered_copper", "displayName": "Waxed Weathered Copper", "enchantCategories": [], "blockStateId": 7481}, {"id": 88, "stackSize": 64, "name": "waxed_oxidized_copper", "displayName": "Waxed Oxidized Copper", "enchantCategories": [], "blockStateId": 7467}, {"id": 89, "stackSize": 64, "name": "waxed_cut_copper", "displayName": "Waxed Cut Copper", "enchantCategories": [], "blockStateId": 7440}, {"id": 90, "stackSize": 64, "name": "waxed_exposed_cut_copper", "displayName": "Waxed Exposed Cut Copper", "enchantCategories": [], "blockStateId": 7454}, {"id": 91, "stackSize": 64, "name": "waxed_weathered_cut_copper", "displayName": "Waxed Weathered Cut Copper", "enchantCategories": [], "blockStateId": 7482}, {"id": 92, "stackSize": 64, "name": "waxed_oxidized_cut_copper", "displayName": "Waxed Oxidized Cut Copper", "enchantCategories": [], "blockStateId": 7468}, {"id": 93, "stackSize": 64, "name": "waxed_cut_copper_stairs", "displayName": "Waxed Cut Copper Stairs", "enchantCategories": [], "blockStateId": 7450}, {"id": 94, "stackSize": 64, "name": "waxed_exposed_cut_copper_stairs", "displayName": "Waxed Exposed Cut Copper Stairs", "enchantCategories": [], "blockStateId": 7464}, {"id": 95, "stackSize": 64, "name": "waxed_weathered_cut_copper_stairs", "displayName": "Waxed Weathered Cut Copper Stairs", "enchantCategories": [], "blockStateId": 7492}, {"id": 96, "stackSize": 64, "name": "waxed_oxidized_cut_copper_stairs", "displayName": "Waxed Oxidized Cut Copper Stairs", "enchantCategories": [], "blockStateId": 7478}, {"id": 97, "stackSize": 64, "name": "waxed_cut_copper_slab", "displayName": "Waxed Cut Copper Slab", "enchantCategories": [], "blockStateId": 7442}, {"id": 98, "stackSize": 64, "name": "waxed_exposed_cut_copper_slab", "displayName": "Waxed Exposed Cut Copper Slab", "enchantCategories": [], "blockStateId": 7456}, {"id": 99, "stackSize": 64, "name": "waxed_weathered_cut_copper_slab", "displayName": "Waxed Weathered Cut Copper Slab", "enchantCategories": [], "blockStateId": 7484}, {"id": 100, "stackSize": 64, "name": "waxed_oxidized_cut_copper_slab", "displayName": "Waxed Oxidized Cut Copper Slab", "enchantCategories": [], "blockStateId": 7470}, {"id": 101, "displayName": "Oak Log", "name": "log", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 102, "name": "spruce_log", "displayName": "Spruce Log", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 103, "name": "birch_log", "displayName": "Birch Log", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 104, "name": "jungle_log", "displayName": "Jungle Log", "stackSize": 64, "enchantCategories": []}]}, {"id": 105, "displayName": "Acacia Log", "name": "log2", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 106, "name": "dark_oak_log", "displayName": "Dark Oak Log", "stackSize": 64, "enchantCategories": []}]}, {"id": 107, "stackSize": 64, "name": "crimson_stem", "displayName": "Crimson Stem", "enchantCategories": [], "blockStateId": 3845}, {"id": 108, "stackSize": 64, "name": "warped_stem", "displayName": "Warped Stem", "enchantCategories": [], "blockStateId": 7398}, {"id": 109, "stackSize": 64, "name": "stripped_oak_log", "displayName": "Stripped Oak Log", "enchantCategories": [], "blockStateId": 7075}, {"id": 110, "stackSize": 64, "name": "stripped_spruce_log", "displayName": "Stripped Spruce Log", "enchantCategories": [], "blockStateId": 7078}, {"id": 111, "stackSize": 64, "name": "stripped_birch_log", "displayName": "Stripped Birch Log", "enchantCategories": [], "blockStateId": 7060}, {"id": 112, "stackSize": 64, "name": "stripped_jungle_log", "displayName": "Stripped Jungle Log", "enchantCategories": [], "blockStateId": 7072}, {"id": 113, "stackSize": 64, "name": "stripped_acacia_log", "displayName": "Stripped Acacia Log", "enchantCategories": [], "blockStateId": 7057}, {"id": 114, "stackSize": 64, "name": "stripped_dark_oak_log", "displayName": "Stripped Dark Oak Log", "enchantCategories": [], "blockStateId": 7069}, {"id": 115, "stackSize": 64, "name": "stripped_crimson_stem", "displayName": "Stripped Crimson Stem", "enchantCategories": [], "blockStateId": 7066}, {"id": 116, "stackSize": 64, "name": "stripped_warped_stem", "displayName": "Stripped Warped Stem", "enchantCategories": [], "blockStateId": 7084}, {"id": 123, "stackSize": 64, "name": "stripped_crimson_hyphae", "displayName": "Stripped Crimson Hyphae", "enchantCategories": [], "blockStateId": 7063}, {"id": 124, "stackSize": 64, "name": "stripped_warped_hyphae", "displayName": "Stripped Warped Hyphae", "enchantCategories": [], "blockStateId": 7081}, {"id": 125, "displayName": "Oak Wood", "name": "wood", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 126, "name": "spruce_wood", "displayName": "Spruce Wood", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 127, "name": "birch_wood", "displayName": "Birch Wood", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 128, "name": "jungle_wood", "displayName": "Jungle Wood", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 129, "name": "acacia_wood", "displayName": "Acacia Wood", "stackSize": 64, "enchantCategories": []}, {"metadata": 5, "id": 130, "name": "dark_oak_wood", "displayName": "Dark Oak Wood", "stackSize": 64, "enchantCategories": []}, {"metadata": 8, "id": 117, "name": "stripped_oak_wood", "displayName": "Stripped Oak Wood", "stackSize": 64, "enchantCategories": []}, {"metadata": 9, "id": 118, "name": "stripped_spruce_wood", "displayName": "Stripped Spruce Wood", "stackSize": 64, "enchantCategories": []}, {"metadata": 10, "id": 119, "name": "stripped_birch_wood", "displayName": "Stripped Birch Wood", "stackSize": 64, "enchantCategories": []}, {"metadata": 11, "id": 120, "name": "stripped_jungle_wood", "displayName": "Stripped Jungle Wood", "stackSize": 64, "enchantCategories": []}, {"metadata": 12, "id": 121, "name": "stripped_acacia_wood", "displayName": "Stripped Acacia Wood", "stackSize": 64, "enchantCategories": []}, {"metadata": 13, "id": 122, "name": "stripped_dark_oak_wood", "displayName": "Stripped Dark Oak Wood", "stackSize": 64, "enchantCategories": []}], "blockStateId": 7586}, {"id": 131, "stackSize": 64, "name": "crimson_hyphae", "displayName": "Crimson Hyphae", "enchantCategories": [], "blockStateId": 3797}, {"id": 132, "stackSize": 64, "name": "warped_hyphae", "displayName": "Warped Hyphae", "enchantCategories": [], "blockStateId": 7350}, {"id": 133, "displayName": "Oak Leaves", "name": "leaves", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 134, "name": "spruce_leaves", "displayName": "Spruce Leaves", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 135, "name": "birch_leaves", "displayName": "Birch Leaves", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 136, "name": "jungle_leaves", "displayName": "Jungle Leaves", "stackSize": 64, "enchantCategories": []}]}, {"id": 137, "displayName": "Acacia Leaves", "name": "leaves2", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 138, "name": "dark_oak_leaves", "displayName": "Dark Oak Leaves", "stackSize": 64, "enchantCategories": []}]}, {"id": 139, "stackSize": 64, "name": "azalea_leaves", "displayName": "Azalea Leaves", "enchantCategories": [], "blockStateId": 172}, {"id": 140, "stackSize": 64, "name": "azalea_leaves_flowered", "displayName": "Flowering Azalea Leaves", "enchantCategories": [], "blockStateId": 176}, {"id": 141, "displayName": "Sponge", "name": "sponge", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 142, "name": "wet_sponge", "displayName": "Wet Sponge", "stackSize": 64, "enchantCategories": []}]}, {"id": 143, "stackSize": 64, "name": "glass", "displayName": "Glass", "enchantCategories": []}, {"id": 144, "stackSize": 64, "name": "tinted_glass", "displayName": "Tinted Glass", "enchantCategories": [], "blockStateId": 7106}, {"id": 145, "stackSize": 64, "name": "lapis_block", "displayName": "Block of Lapis Lazuli", "enchantCategories": []}, {"id": 146, "displayName": "Sandstone", "name": "sandstone", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 147, "name": "chiseled_sandstone", "displayName": "Chiseled Sandstone", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 148, "name": "cut_sandstone", "displayName": "Cut Sandstone", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 230, "name": "smooth_sandstone", "displayName": "Smooth Sandstone", "stackSize": 64, "enchantCategories": []}]}, {"id": 149, "stackSize": 64, "name": "web", "displayName": "Cobweb", "enchantCategories": []}, {"id": 150, "displayName": "Grass", "name": "tallgrass", "stackSize": 64, "metadata": 1, "enchantCategories": [], "variations": [{"metadata": 2, "id": 151, "name": "fern", "displayName": "Fern", "stackSize": 64, "enchantCategories": []}]}, {"id": 152, "stackSize": 64, "name": "azalea", "displayName": "Azalea", "enchantCategories": [], "blockStateId": 168}, {"id": 153, "stackSize": 64, "name": "flowering_azalea", "displayName": "Flowering Azalea", "enchantCategories": [], "blockStateId": 4764}, {"id": 154, "stackSize": 64, "name": "deadbush", "displayName": "Dead Bush", "enchantCategories": []}, {"id": 155, "stackSize": 64, "name": "seagrass", "displayName": "Seagrass", "enchantCategories": [], "blockStateId": 6581}, {"id": 156, "displayName": "<PERSON>", "name": "sea_pickle", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 0, "id": 1078, "name": "candle", "displayName": "Candle", "stackSize": 64, "enchantCategories": []}, {"metadata": 0, "id": 1079, "name": "white_candle", "displayName": "White Candle", "stackSize": 64, "enchantCategories": []}, {"metadata": 0, "id": 1080, "name": "orange_candle", "displayName": "Orange Candle", "stackSize": 64, "enchantCategories": []}, {"metadata": 0, "id": 1081, "name": "magenta_candle", "displayName": "<PERSON><PERSON><PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 0, "id": 1082, "name": "light_blue_candle", "displayName": "Light Blue Candle", "stackSize": 64, "enchantCategories": []}, {"metadata": 0, "id": 1083, "name": "yellow_candle", "displayName": "Yellow Candle", "stackSize": 64, "enchantCategories": []}, {"metadata": 0, "id": 1084, "name": "lime_candle", "displayName": "<PERSON><PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 0, "id": 1085, "name": "pink_candle", "displayName": "Pink Candle", "stackSize": 64, "enchantCategories": []}, {"metadata": 0, "id": 1086, "name": "gray_candle", "displayName": "<PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 0, "id": 1087, "name": "light_gray_candle", "displayName": "Light Gray Candle", "stackSize": 64, "enchantCategories": []}, {"metadata": 0, "id": 1088, "name": "cyan_candle", "displayName": "<PERSON><PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 0, "id": 1089, "name": "purple_candle", "displayName": "Purple Candle", "stackSize": 64, "enchantCategories": []}, {"metadata": 0, "id": 1090, "name": "blue_candle", "displayName": "Blue Candle", "stackSize": 64, "enchantCategories": []}, {"metadata": 0, "id": 1091, "name": "brown_candle", "displayName": "<PERSON> Candle", "stackSize": 64, "enchantCategories": []}, {"metadata": 0, "id": 1092, "name": "green_candle", "displayName": "Green Candle", "stackSize": 64, "enchantCategories": []}, {"metadata": 0, "id": 1093, "name": "red_candle", "displayName": "<PERSON> Candle", "stackSize": 64, "enchantCategories": []}, {"metadata": 0, "id": 1094, "name": "black_candle", "displayName": "Black Candle", "stackSize": 64, "enchantCategories": []}], "blockStateId": 6578}, {"id": 157, "displayName": "White Wool", "name": "wool", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 158, "name": "orange_wool", "displayName": "Orange Wool", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 159, "name": "magenta_wool", "displayName": "Magenta Wool", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 160, "name": "light_blue_wool", "displayName": "Light Blue Wool", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 161, "name": "yellow_wool", "displayName": "Yellow Wool", "stackSize": 64, "enchantCategories": []}, {"metadata": 5, "id": 162, "name": "lime_wool", "displayName": "Lime Wool", "stackSize": 64, "enchantCategories": []}, {"metadata": 6, "id": 163, "name": "pink_wool", "displayName": "Pink Wool", "stackSize": 64, "enchantCategories": []}, {"metadata": 7, "id": 164, "name": "gray_wool", "displayName": "Gray <PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 8, "id": 165, "name": "light_gray_wool", "displayName": "Light Gray Wool", "stackSize": 64, "enchantCategories": []}, {"metadata": 9, "id": 166, "name": "cyan_wool", "displayName": "<PERSON><PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 10, "id": 167, "name": "purple_wool", "displayName": "Purple Wool", "stackSize": 64, "enchantCategories": []}, {"metadata": 11, "id": 168, "name": "blue_wool", "displayName": "Blue Wool", "stackSize": 64, "enchantCategories": []}, {"metadata": 12, "id": 169, "name": "brown_wool", "displayName": "Brown Wool", "stackSize": 64, "enchantCategories": []}, {"metadata": 13, "id": 170, "name": "green_wool", "displayName": "Green Wool", "stackSize": 64, "enchantCategories": []}, {"metadata": 14, "id": 171, "name": "red_wool", "displayName": "Red Wool", "stackSize": 64, "enchantCategories": []}, {"metadata": 15, "id": 172, "name": "black_wool", "displayName": "Black Wool", "stackSize": 64, "enchantCategories": []}]}, {"id": 173, "stackSize": 64, "name": "yellow_flower", "displayName": "Dandelion", "enchantCategories": []}, {"id": 174, "displayName": "<PERSON><PERSON>", "name": "red_flower", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 175, "name": "blue_orchid", "displayName": "Blue Orchid", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 176, "name": "allium", "displayName": "Allium", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 177, "name": "azure_bluet", "displayName": "Azure Bluet", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 178, "name": "red_tulip", "displayName": "<PERSON>lip", "stackSize": 64, "enchantCategories": []}, {"metadata": 5, "id": 179, "name": "orange_tulip", "displayName": "Orange Tulip", "stackSize": 64, "enchantCategories": []}, {"metadata": 6, "id": 180, "name": "white_tulip", "displayName": "White Tulip", "stackSize": 64, "enchantCategories": []}, {"metadata": 7, "id": 181, "name": "pink_tulip", "displayName": "<PERSON> Tulip", "stackSize": 64, "enchantCategories": []}, {"metadata": 8, "id": 182, "name": "oxeye_daisy", "displayName": "Oxeye Daisy", "stackSize": 64, "enchantCategories": []}, {"metadata": 9, "id": 183, "name": "cornflower", "displayName": "Corn<PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 10, "id": 184, "name": "lily_of_the_valley", "displayName": "Lily of the Valley", "stackSize": 64, "enchantCategories": []}]}, {"id": 185, "stackSize": 64, "name": "wither_rose", "displayName": "<PERSON><PERSON>", "enchantCategories": [], "blockStateId": 7550}, {"id": 186, "stackSize": 64, "name": "spore_blossom", "displayName": "Spore Blossom", "enchantCategories": [], "blockStateId": 6719}, {"id": 187, "stackSize": 64, "name": "brown_mushroom", "displayName": "Brown Mushroom", "enchantCategories": []}, {"id": 188, "stackSize": 64, "name": "red_mushroom", "displayName": "Red Mushroom", "enchantCategories": []}, {"id": 189, "stackSize": 64, "name": "crimson_fungus", "displayName": "Crimson Fungus", "enchantCategories": [], "blockStateId": 3794}, {"id": 190, "stackSize": 64, "name": "warped_fungus", "displayName": "Warped Fungus", "enchantCategories": [], "blockStateId": 7347}, {"id": 191, "stackSize": 64, "name": "crimson_roots", "displayName": "Crimson Roots", "enchantCategories": [], "blockStateId": 3816}, {"id": 192, "stackSize": 64, "name": "warped_roots", "displayName": "Warped Roots", "enchantCategories": [], "blockStateId": 7369}, {"id": 193, "stackSize": 64, "name": "nether_sprouts", "displayName": "Nether Sprouts", "enchantCategories": []}, {"id": 194, "stackSize": 64, "name": "weeping_vines", "displayName": "Weeping Vines", "enchantCategories": [], "blockStateId": 7535}, {"id": 195, "stackSize": 64, "name": "twisting_vines", "displayName": "Twisting Vines", "enchantCategories": [], "blockStateId": 7209}, {"id": 196, "stackSize": 64, "name": "sugar_cane", "displayName": "Sugar Cane", "enchantCategories": []}, {"id": 197, "stackSize": 64, "name": "kelp", "displayName": "<PERSON><PERSON><PERSON>", "enchantCategories": []}, {"id": 198, "stackSize": 64, "name": "moss_carpet", "displayName": "Moss Carpet", "enchantCategories": [], "blockStateId": 5531}, {"id": 199, "stackSize": 64, "name": "moss_block", "displayName": "Moss Block", "enchantCategories": [], "blockStateId": 5530}, {"id": 200, "stackSize": 64, "name": "hanging_roots", "displayName": "Hanging Roots", "enchantCategories": [], "blockStateId": 4953}, {"id": 201, "stackSize": 64, "name": "big_dripleaf", "displayName": "Big Dripleaf", "enchantCategories": [], "blockStateId": 355}, {"id": 202, "stackSize": 64, "name": "small_dripleaf_block", "displayName": "Small Dripleaf", "enchantCategories": [], "blockStateId": 6632}, {"id": 203, "stackSize": 64, "name": "bamboo", "displayName": "Bamboo", "enchantCategories": [], "blockStateId": 188}, {"id": 204, "displayName": "Oak Slab", "name": "wooden_slab", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 205, "name": "spruce_slab", "displayName": "Spruce Slab", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 206, "name": "birch_slab", "displayName": "<PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 207, "name": "jungle_slab", "displayName": "Jungle Slab", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 208, "name": "acacia_slab", "displayName": "Acacia <PERSON>b", "stackSize": 64, "enchantCategories": []}, {"metadata": 5, "id": 209, "name": "dark_oak_slab", "displayName": "Dark Oak Slab", "stackSize": 64, "enchantCategories": []}]}, {"id": 210, "stackSize": 64, "name": "crimson_slab", "displayName": "Crimson Slab", "enchantCategories": [], "blockStateId": 3818}, {"id": 211, "stackSize": 64, "name": "warped_slab", "displayName": "Warped Slab", "enchantCategories": [], "blockStateId": 7371}, {"id": 213, "displayName": "Smooth Stone Slab", "name": "double_stone_slab", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 214, "name": "sandstone_slab", "displayName": "Sandstone Slab", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 216, "name": "petrified_oak_slab", "displayName": "Petrified Oak Slab", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 217, "name": "cobblestone_slab", "displayName": "Cobblestone Slab", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 218, "name": "brick_slab", "displayName": "Brick Slab", "stackSize": 64, "enchantCategories": []}, {"metadata": 5, "id": 219, "name": "stone_brick_slab", "displayName": "Stone Brick Slab", "stackSize": 64, "enchantCategories": []}, {"metadata": 6, "id": 221, "name": "quartz_slab", "displayName": "Quartz Slab", "stackSize": 64, "enchantCategories": []}, {"metadata": 7, "id": 220, "name": "nether_brick_slab", "displayName": "Nether Brick Slab", "stackSize": 64, "enchantCategories": []}]}, {"id": 222, "displayName": "Red Sandstone Slab", "name": "double_stone_slab2", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 224, "name": "purpur_slab", "displayName": "Purpur Slab", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 225, "name": "prismarine_slab", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 227, "name": "dark_prismarine_slab", "displayName": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 226, "name": "prismarine_brick_slab", "displayName": "Prismarine Brick Slab", "stackSize": 64, "enchantCategories": []}, {"metadata": 5, "id": 571, "name": "mossy_cobblestone_slab", "displayName": "<PERSON><PERSON> Slab", "stackSize": 64, "enchantCategories": []}, {"metadata": 6, "id": 573, "name": "smooth_sandstone_slab", "displayName": "Smooth Sandstone Slab", "stackSize": 64, "enchantCategories": []}, {"metadata": 7, "id": 577, "name": "red_nether_brick_slab", "displayName": "Red Nether Brick Slab", "stackSize": 64, "enchantCategories": []}]}, {"id": 231, "stackSize": 64, "name": "smooth_stone", "displayName": "Smooth Stone", "enchantCategories": [], "blockStateId": 6665}, {"id": 232, "stackSize": 64, "name": "brick_block", "displayName": "Bricks", "enchantCategories": []}, {"id": 233, "stackSize": 64, "name": "bookshelf", "displayName": "Bookshelf", "enchantCategories": []}, {"id": 234, "stackSize": 64, "name": "mossy_cobblestone", "displayName": "<PERSON><PERSON>", "enchantCategories": []}, {"id": 235, "stackSize": 64, "name": "obsidian", "displayName": "Obsidian", "enchantCategories": []}, {"id": 236, "stackSize": 64, "name": "torch", "displayName": "<PERSON>ch", "enchantCategories": []}, {"id": 237, "stackSize": 64, "name": "end_rod", "displayName": "End Rod", "enchantCategories": []}, {"id": 238, "stackSize": 64, "name": "chorus_plant", "displayName": "Chorus Plant", "enchantCategories": []}, {"id": 239, "stackSize": 64, "name": "chorus_flower", "displayName": "Chorus Flower", "enchantCategories": []}, {"id": 240, "displayName": "Purpur Block", "name": "purpur_block", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 2, "id": 241, "name": "purpur_pillar", "displayName": "Purpur Pillar", "stackSize": 64, "enchantCategories": []}]}, {"id": 242, "stackSize": 64, "name": "purpur_stairs", "displayName": "Purpur Stairs", "enchantCategories": []}, {"id": 243, "stackSize": 64, "name": "mob_spawner", "displayName": "Spawner", "enchantCategories": []}, {"id": 244, "stackSize": 64, "name": "oak_stairs", "displayName": "Oak Stairs", "enchantCategories": []}, {"id": 245, "stackSize": 64, "name": "chest", "displayName": "Chest", "enchantCategories": []}, {"id": 246, "stackSize": 64, "name": "crafting_table", "displayName": "Crafting Table", "enchantCategories": []}, {"id": 247, "stackSize": 64, "name": "farmland", "displayName": "Farmland", "enchantCategories": []}, {"id": 248, "stackSize": 64, "name": "furnace", "displayName": "Furnace", "enchantCategories": []}, {"id": 249, "stackSize": 64, "name": "ladder", "displayName": "Ladder", "enchantCategories": []}, {"id": 250, "stackSize": 64, "name": "stone_stairs", "displayName": "Cobblestone Stairs", "enchantCategories": []}, {"id": 251, "stackSize": 64, "name": "snow_layer", "displayName": "Snow", "enchantCategories": []}, {"id": 252, "stackSize": 64, "name": "ice", "displayName": "Ice", "enchantCategories": []}, {"id": 253, "stackSize": 64, "name": "snow", "displayName": "Snow Block", "enchantCategories": []}, {"id": 254, "stackSize": 64, "name": "cactus", "displayName": "Cactus", "enchantCategories": []}, {"id": 255, "stackSize": 64, "name": "clay", "displayName": "<PERSON>", "enchantCategories": []}, {"id": 256, "stackSize": 64, "name": "jukebox", "displayName": "Jukebox", "enchantCategories": []}, {"id": 257, "displayName": "Oak Fence", "name": "fence", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 258, "name": "spruce_fence", "displayName": "Spruce Fence", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 259, "name": "birch_fence", "displayName": "<PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 260, "name": "jungle_fence", "displayName": "Jungle Fence", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 261, "name": "acacia_fence", "displayName": "Acacia Fence", "stackSize": 64, "enchantCategories": []}, {"metadata": 5, "id": 262, "name": "dark_oak_fence", "displayName": "Dark Oak Fence", "stackSize": 64, "enchantCategories": []}]}, {"id": 263, "stackSize": 64, "name": "crimson_fence", "displayName": "<PERSON> Fence", "enchantCategories": [], "blockStateId": 3777}, {"id": 264, "stackSize": 64, "name": "warped_fence", "displayName": "Warped <PERSON>", "enchantCategories": [], "blockStateId": 7330}, {"id": 265, "stackSize": 64, "name": "pumpkin", "displayName": "<PERSON><PERSON><PERSON>", "enchantCategories": []}, {"id": 266, "stackSize": 64, "name": "carved_pumpkin", "displayName": "<PERSON><PERSON>", "enchantCategories": ["wearable", "vanishable"], "blockStateId": 951}, {"id": 267, "stackSize": 64, "name": "lit_pumpkin", "displayName": "<PERSON>'<PERSON>", "enchantCategories": ["wearable", "vanishable"]}, {"id": 268, "stackSize": 64, "name": "netherrack", "displayName": "Netherrack", "enchantCategories": []}, {"id": 269, "stackSize": 64, "name": "soul_sand", "displayName": "Soul Sand", "enchantCategories": []}, {"id": 270, "stackSize": 64, "name": "soul_soil", "displayName": "Soul Soil", "enchantCategories": [], "blockStateId": 6710}, {"id": 271, "stackSize": 64, "name": "basalt", "displayName": "Basalt", "enchantCategories": [], "blockStateId": 216}, {"id": 272, "stackSize": 64, "name": "polished_basalt", "displayName": "Polished Ba<PERSON>t", "enchantCategories": [], "blockStateId": 5667}, {"id": 273, "stackSize": 64, "name": "smooth_basalt", "displayName": "Smooth Basalt", "enchantCategories": [], "blockStateId": 6640}, {"id": 274, "stackSize": 64, "name": "soul_torch", "displayName": "Soul Torch", "enchantCategories": [], "blockStateId": 6716}, {"id": 275, "stackSize": 64, "name": "glowstone", "displayName": "Glowstone", "enchantCategories": []}, {"id": 276, "displayName": "Infested Stone", "name": "monster_egg", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 277, "name": "infested_cobblestone", "displayName": "Infested Cobblestone", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 278, "name": "infested_stone_bricks", "displayName": "Infested Stone Bricks", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 279, "name": "infested_mossy_stone_bricks", "displayName": "Infested Mossy Stone Bricks", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 280, "name": "infested_cracked_stone_bricks", "displayName": "Infested Cracked Stone Bricks", "stackSize": 64, "enchantCategories": []}, {"metadata": 5, "id": 281, "name": "infested_chiseled_stone_bricks", "displayName": "Infested Chiseled Stone Bricks", "stackSize": 64, "enchantCategories": []}]}, {"id": 282, "stackSize": 64, "name": "infested_deepslate", "displayName": "Infested Deepslate", "enchantCategories": [], "blockStateId": 5034}, {"id": 283, "displayName": "Stone Bricks", "name": "stonebrick", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 284, "name": "mossy_stone_bricks", "displayName": "Mossy Stone Bricks", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 285, "name": "cracked_stone_bricks", "displayName": "Cracked Stone Bricks", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 286, "name": "chiseled_stone_bricks", "displayName": "Chiseled Stone Bricks", "stackSize": 64, "enchantCategories": []}]}, {"id": 287, "stackSize": 64, "name": "deepslate_bricks", "displayName": "Deepslate Bricks", "enchantCategories": [], "blockStateId": 4226}, {"id": 288, "stackSize": 64, "name": "cracked_deepslate_bricks", "displayName": "Cracked Deepslate Bricks", "enchantCategories": [], "blockStateId": 3726}, {"id": 289, "stackSize": 64, "name": "deepslate_tiles", "displayName": "Deepslate Tiles", "enchantCategories": [], "blockStateId": 4409}, {"id": 290, "stackSize": 64, "name": "cracked_deepslate_tiles", "displayName": "Cracked Deepslate Tiles", "enchantCategories": [], "blockStateId": 3727}, {"id": 291, "stackSize": 64, "name": "chiseled_deepslate", "displayName": "Chiseled Deepslate", "enchantCategories": [], "blockStateId": 1089}, {"id": 292, "displayName": "Brown Mushroom Block", "name": "brown_mushroom_block", "stackSize": 64, "metadata": 14, "enchantCategories": [], "variations": [{"metadata": 15, "id": 294, "name": "mushroom_stem", "displayName": "Mushroom Stem", "stackSize": 64, "enchantCategories": []}]}, {"id": 293, "stackSize": 64, "name": "red_mushroom_block", "displayName": "Red Mushroom Block", "enchantCategories": []}, {"id": 295, "stackSize": 64, "name": "iron_bars", "displayName": "Iron Bars", "enchantCategories": []}, {"id": 296, "stackSize": 64, "name": "chain", "displayName": "Chain", "enchantCategories": []}, {"id": 297, "stackSize": 64, "name": "glass_pane", "displayName": "Glass Pane", "enchantCategories": []}, {"id": 298, "stackSize": 64, "name": "melon_block", "displayName": "Melon", "enchantCategories": []}, {"id": 299, "stackSize": 64, "name": "vine", "displayName": "Vines", "enchantCategories": []}, {"id": 300, "stackSize": 64, "name": "glow_lichen", "displayName": "Glow Lichen", "enchantCategories": [], "blockStateId": 4897}, {"id": 301, "stackSize": 64, "name": "brick_stairs", "displayName": "Brick Stairs", "enchantCategories": []}, {"id": 302, "stackSize": 64, "name": "stone_brick_stairs", "displayName": "Stone Brick Stairs", "enchantCategories": []}, {"id": 303, "stackSize": 64, "name": "mycelium", "displayName": "Mycelium", "enchantCategories": []}, {"id": 304, "stackSize": 64, "name": "waterlily", "displayName": "<PERSON>", "enchantCategories": []}, {"id": 305, "displayName": "Nether Bricks", "name": "nether_brick", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 0, "id": 964, "name": "nether_brick", "displayName": "Nether Brick", "stackSize": 64, "enchantCategories": []}]}, {"id": 306, "stackSize": 64, "name": "cracked_nether_bricks", "displayName": "Cracked Nether Bricks", "enchantCategories": [], "blockStateId": 3728}, {"id": 307, "stackSize": 64, "name": "chiseled_nether_bricks", "displayName": "Chiseled Nether Bricks", "enchantCategories": [], "blockStateId": 1090}, {"id": 308, "stackSize": 64, "name": "nether_brick_fence", "displayName": "Nether Brick Fence", "enchantCategories": []}, {"id": 309, "stackSize": 64, "name": "nether_brick_stairs", "displayName": "Nether Brick Stairs", "enchantCategories": []}, {"id": 310, "stackSize": 64, "name": "enchanting_table", "displayName": "Enchanting Table", "enchantCategories": []}, {"id": 311, "stackSize": 64, "name": "end_portal_frame", "displayName": "End Portal Frame", "enchantCategories": []}, {"id": 312, "stackSize": 64, "name": "end_stone", "displayName": "End Stone", "enchantCategories": []}, {"id": 313, "stackSize": 64, "name": "end_bricks", "displayName": "End Stone Bricks", "enchantCategories": []}, {"id": 314, "stackSize": 64, "name": "dragon_egg", "displayName": "Dragon Egg", "enchantCategories": []}, {"id": 315, "stackSize": 64, "name": "sandstone_stairs", "displayName": "Sandstone Stairs", "enchantCategories": []}, {"id": 316, "stackSize": 64, "name": "ender_chest", "displayName": "<PERSON><PERSON> Chest", "enchantCategories": []}, {"id": 317, "stackSize": 64, "name": "emerald_block", "displayName": "Block of Emerald", "enchantCategories": []}, {"id": 318, "stackSize": 64, "name": "spruce_stairs", "displayName": "Spruce Stairs", "enchantCategories": []}, {"id": 319, "stackSize": 64, "name": "birch_stairs", "displayName": "<PERSON> Stairs", "enchantCategories": []}, {"id": 320, "stackSize": 64, "name": "jungle_stairs", "displayName": "Jungle Stairs", "enchantCategories": []}, {"id": 321, "stackSize": 64, "name": "crimson_stairs", "displayName": "Crimson Stairs", "enchantCategories": [], "blockStateId": 3826}, {"id": 322, "stackSize": 64, "name": "warped_stairs", "displayName": "Warped Stairs", "enchantCategories": [], "blockStateId": 7379}, {"id": 323, "stackSize": 64, "name": "command_block", "displayName": "Command Block", "enchantCategories": []}, {"id": 324, "stackSize": 64, "name": "beacon", "displayName": "Beacon", "enchantCategories": []}, {"id": 325, "displayName": "Cobblestone Wall", "name": "cobblestone_wall", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 326, "name": "mossy_cobblestone_wall", "displayName": "<PERSON><PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 331, "name": "granite_wall", "displayName": "Granite Wall", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 338, "name": "diorite_wall", "displayName": "Diorite Wall", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 334, "name": "andesite_wall", "displayName": "Andesite Wall", "stackSize": 64, "enchantCategories": []}, {"metadata": 5, "id": 336, "name": "sandstone_wall", "displayName": "Sandstone Wall", "stackSize": 64, "enchantCategories": []}, {"metadata": 6, "id": 327, "name": "brick_wall", "displayName": "Brick Wall", "stackSize": 64, "enchantCategories": []}, {"metadata": 7, "id": 332, "name": "stone_brick_wall", "displayName": "Stone Brick Wall", "stackSize": 64, "enchantCategories": []}, {"metadata": 8, "id": 330, "name": "mossy_stone_brick_wall", "displayName": "Mossy Stone Brick Wall", "stackSize": 64, "enchantCategories": []}, {"metadata": 9, "id": 333, "name": "nether_brick_wall", "displayName": "Nether Brick Wall", "stackSize": 64, "enchantCategories": []}, {"metadata": 10, "id": 337, "name": "end_stone_brick_wall", "displayName": "End Stone Brick Wall", "stackSize": 64, "enchantCategories": []}, {"metadata": 11, "id": 328, "name": "prismarine_wall", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 12, "id": 329, "name": "red_sandstone_wall", "displayName": "Red Sandstone Wall", "stackSize": 64, "enchantCategories": []}, {"metadata": 13, "id": 335, "name": "red_nether_brick_wall", "displayName": "Red Nether Brick Wall", "stackSize": 64, "enchantCategories": []}]}, {"id": 339, "stackSize": 64, "name": "blackstone_wall", "displayName": "Blackstone Wall", "enchantCategories": [], "blockStateId": 658}, {"id": 340, "stackSize": 64, "name": "polished_blackstone_wall", "displayName": "Polished Blackstone Wall", "enchantCategories": [], "blockStateId": 6045}, {"id": 341, "stackSize": 64, "name": "polished_blackstone_brick_wall", "displayName": "Polished Blackstone Brick Wall", "enchantCategories": [], "blockStateId": 5842}, {"id": 342, "stackSize": 64, "name": "cobbled_deepslate_wall", "displayName": "Cobbled Deepslate Wall", "enchantCategories": [], "blockStateId": 1276}, {"id": 343, "stackSize": 64, "name": "polished_deepslate_wall", "displayName": "Polished Deepslate Wall", "enchantCategories": [], "blockStateId": 6220}, {"id": 344, "stackSize": 64, "name": "deepslate_brick_wall", "displayName": "Deepslate Brick Wall", "enchantCategories": [], "blockStateId": 4225}, {"id": 345, "stackSize": 64, "name": "deepslate_tile_wall", "displayName": "Deepslate Tile Wall", "enchantCategories": [], "blockStateId": 4408}, {"id": 346, "displayName": "An<PERSON>", "name": "anvil", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 4, "id": 347, "name": "chipped_anvil", "displayName": "Chipped Anvil", "stackSize": 64, "enchantCategories": []}, {"metadata": 8, "id": 348, "name": "damaged_anvil", "displayName": "Damaged Anvil", "stackSize": 64, "enchantCategories": []}]}, {"id": 350, "displayName": "Block of Quartz", "name": "quartz_block", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 349, "name": "chiseled_quartz_block", "displayName": "Chiseled Quartz Block", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 352, "name": "quartz_pillar", "displayName": "Quartz <PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 228, "name": "smooth_quartz", "displayName": "Smooth Quartz Block", "stackSize": 64, "enchantCategories": []}]}, {"id": 351, "stackSize": 64, "name": "quartz_bricks", "displayName": "Quartz Bricks", "enchantCategories": [], "blockStateId": 6390}, {"id": 353, "stackSize": 64, "name": "quartz_stairs", "displayName": "Quartz Stairs", "enchantCategories": []}, {"id": 354, "displayName": "White Terracotta", "name": "stained_hardened_clay", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 355, "name": "orange_terracotta", "displayName": "Orange Terracotta", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 356, "name": "magenta_terracotta", "displayName": "Magenta Terracotta", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 357, "name": "light_blue_terracotta", "displayName": "Light Blue Terracotta", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 358, "name": "yellow_terracotta", "displayName": "Yellow Terracotta", "stackSize": 64, "enchantCategories": []}, {"metadata": 5, "id": 359, "name": "lime_terracotta", "displayName": "Lime Terracotta", "stackSize": 64, "enchantCategories": []}, {"metadata": 6, "id": 360, "name": "pink_terracotta", "displayName": "Pink Terracotta", "stackSize": 64, "enchantCategories": []}, {"metadata": 7, "id": 361, "name": "gray_terracotta", "displayName": "Gray <PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 8, "id": 362, "name": "light_gray_terracotta", "displayName": "Light Gray Terracotta", "stackSize": 64, "enchantCategories": []}, {"metadata": 9, "id": 363, "name": "cyan_terracotta", "displayName": "<PERSON><PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 10, "id": 364, "name": "purple_terracotta", "displayName": "Purple Terracotta", "stackSize": 64, "enchantCategories": []}, {"metadata": 11, "id": 365, "name": "blue_terracotta", "displayName": "Blue Terracotta", "stackSize": 64, "enchantCategories": []}, {"metadata": 12, "id": 366, "name": "brown_terracotta", "displayName": "Brown Terracotta", "stackSize": 64, "enchantCategories": []}, {"metadata": 13, "id": 367, "name": "green_terracotta", "displayName": "Green Terracotta", "stackSize": 64, "enchantCategories": []}, {"metadata": 14, "id": 368, "name": "red_terracotta", "displayName": "Red Terracotta", "stackSize": 64, "enchantCategories": []}, {"metadata": 15, "id": 369, "name": "black_terracotta", "displayName": "Black Terracotta", "stackSize": 64, "enchantCategories": []}]}, {"id": 370, "stackSize": 64, "name": "barrier", "displayName": "Barrier", "enchantCategories": [], "blockStateId": 213}, {"id": 371, "stackSize": 64, "name": "light_block", "displayName": "Light", "enchantCategories": [], "blockStateId": 5378}, {"id": 372, "stackSize": 64, "name": "hay_block", "displayName": "<PERSON>", "enchantCategories": []}, {"id": 373, "displayName": "White Carpet", "name": "carpet", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 374, "name": "orange_carpet", "displayName": "Orange Carpet", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 375, "name": "magenta_carpet", "displayName": "Magenta Carpet", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 376, "name": "light_blue_carpet", "displayName": "Light Blue Carpet", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 377, "name": "yellow_carpet", "displayName": "Yellow Carpet", "stackSize": 64, "enchantCategories": []}, {"metadata": 5, "id": 378, "name": "lime_carpet", "displayName": "Lime Carpet", "stackSize": 64, "enchantCategories": []}, {"metadata": 6, "id": 379, "name": "pink_carpet", "displayName": "Pink Carpet", "stackSize": 64, "enchantCategories": []}, {"metadata": 7, "id": 380, "name": "gray_carpet", "displayName": "<PERSON> Carpet", "stackSize": 64, "enchantCategories": []}, {"metadata": 8, "id": 381, "name": "light_gray_carpet", "displayName": "Light Gray Carpet", "stackSize": 64, "enchantCategories": []}, {"metadata": 9, "id": 382, "name": "cyan_carpet", "displayName": "<PERSON><PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 10, "id": 383, "name": "purple_carpet", "displayName": "Purple Carpet", "stackSize": 64, "enchantCategories": []}, {"metadata": 11, "id": 384, "name": "blue_carpet", "displayName": "Blue Carpet", "stackSize": 64, "enchantCategories": []}, {"metadata": 12, "id": 385, "name": "brown_carpet", "displayName": "Brown Carpet", "stackSize": 64, "enchantCategories": []}, {"metadata": 13, "id": 386, "name": "green_carpet", "displayName": "Green Carpet", "stackSize": 64, "enchantCategories": []}, {"metadata": 14, "id": 387, "name": "red_carpet", "displayName": "Red Carpet", "stackSize": 64, "enchantCategories": []}, {"metadata": 15, "id": 388, "name": "black_carpet", "displayName": "Black Carpet", "stackSize": 64, "enchantCategories": []}]}, {"id": 389, "stackSize": 64, "name": "hardened_clay", "displayName": "Terracotta", "enchantCategories": []}, {"id": 390, "stackSize": 64, "name": "packed_ice", "displayName": "Packed Ice", "enchantCategories": []}, {"id": 391, "stackSize": 64, "name": "acacia_stairs", "displayName": "Acacia Stairs", "enchantCategories": []}, {"id": 392, "stackSize": 64, "name": "dark_oak_stairs", "displayName": "Dark Oak Stairs", "enchantCategories": []}, {"id": 393, "stackSize": 64, "name": "grass_path", "displayName": "Dirt Path", "enchantCategories": []}, {"id": 394, "displayName": "Sunflower", "name": "double_plant", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 395, "name": "lilac", "displayName": "Lilac", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 398, "name": "tall_grass", "displayName": "Tall Grass", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 399, "name": "large_fern", "displayName": "Large Fern", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 396, "name": "rose_bush", "displayName": "<PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 5, "id": 397, "name": "peony", "displayName": "Peony", "stackSize": 64, "enchantCategories": []}]}, {"id": 400, "displayName": "White Stained Glass", "name": "stained_glass", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 401, "name": "orange_stained_glass", "displayName": "Orange Stained Glass", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 402, "name": "magenta_stained_glass", "displayName": "Magenta Stained Glass", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 403, "name": "light_blue_stained_glass", "displayName": "Light Blue Stained Glass", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 404, "name": "yellow_stained_glass", "displayName": "Yellow Stained Glass", "stackSize": 64, "enchantCategories": []}, {"metadata": 5, "id": 405, "name": "lime_stained_glass", "displayName": "Lime Stained Glass", "stackSize": 64, "enchantCategories": []}, {"metadata": 6, "id": 406, "name": "pink_stained_glass", "displayName": "Pink Stained Glass", "stackSize": 64, "enchantCategories": []}, {"metadata": 7, "id": 407, "name": "gray_stained_glass", "displayName": "<PERSON> Stained Glass", "stackSize": 64, "enchantCategories": []}, {"metadata": 8, "id": 408, "name": "light_gray_stained_glass", "displayName": "Light Gray Stained Glass", "stackSize": 64, "enchantCategories": []}, {"metadata": 9, "id": 409, "name": "cyan_stained_glass", "displayName": "<PERSON><PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 10, "id": 410, "name": "purple_stained_glass", "displayName": "Purple Stained Glass", "stackSize": 64, "enchantCategories": []}, {"metadata": 11, "id": 411, "name": "blue_stained_glass", "displayName": "Blue Stained Glass", "stackSize": 64, "enchantCategories": []}, {"metadata": 12, "id": 412, "name": "brown_stained_glass", "displayName": "<PERSON> Stained Glass", "stackSize": 64, "enchantCategories": []}, {"metadata": 13, "id": 413, "name": "green_stained_glass", "displayName": "Green Stained Glass", "stackSize": 64, "enchantCategories": []}, {"metadata": 14, "id": 414, "name": "red_stained_glass", "displayName": "Red Stained Glass", "stackSize": 64, "enchantCategories": []}, {"metadata": 15, "id": 415, "name": "black_stained_glass", "displayName": "Black Stained Glass", "stackSize": 64, "enchantCategories": []}]}, {"id": 416, "displayName": "White Stained Glass Pane", "name": "stained_glass_pane", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 417, "name": "orange_stained_glass_pane", "displayName": "Orange Stained Glass Pane", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 418, "name": "magenta_stained_glass_pane", "displayName": "Magenta Stained Glass Pane", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 419, "name": "light_blue_stained_glass_pane", "displayName": "Light Blue Stained Glass Pane", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 420, "name": "yellow_stained_glass_pane", "displayName": "Yellow Stained Glass Pane", "stackSize": 64, "enchantCategories": []}, {"metadata": 5, "id": 421, "name": "lime_stained_glass_pane", "displayName": "Lime Stained Glass Pane", "stackSize": 64, "enchantCategories": []}, {"metadata": 6, "id": 422, "name": "pink_stained_glass_pane", "displayName": "Pink Stained Glass Pane", "stackSize": 64, "enchantCategories": []}, {"metadata": 7, "id": 423, "name": "gray_stained_glass_pane", "displayName": "Gray Stained Glass Pane", "stackSize": 64, "enchantCategories": []}, {"metadata": 8, "id": 424, "name": "light_gray_stained_glass_pane", "displayName": "Light Gray Stained Glass Pane", "stackSize": 64, "enchantCategories": []}, {"metadata": 9, "id": 425, "name": "cyan_stained_glass_pane", "displayName": "<PERSON><PERSON> Stained Glass Pane", "stackSize": 64, "enchantCategories": []}, {"metadata": 10, "id": 426, "name": "purple_stained_glass_pane", "displayName": "Purple Stained Glass Pane", "stackSize": 64, "enchantCategories": []}, {"metadata": 11, "id": 427, "name": "blue_stained_glass_pane", "displayName": "Blue Stained Glass Pane", "stackSize": 64, "enchantCategories": []}, {"metadata": 12, "id": 428, "name": "brown_stained_glass_pane", "displayName": "<PERSON> Stained Glass Pane", "stackSize": 64, "enchantCategories": []}, {"metadata": 13, "id": 429, "name": "green_stained_glass_pane", "displayName": "Green Stained Glass Pane", "stackSize": 64, "enchantCategories": []}, {"metadata": 14, "id": 430, "name": "red_stained_glass_pane", "displayName": "Red Stained Glass Pane", "stackSize": 64, "enchantCategories": []}, {"metadata": 15, "id": 431, "name": "black_stained_glass_pane", "displayName": "Black Stained Glass Pane", "stackSize": 64, "enchantCategories": []}]}, {"id": 432, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "prismarine", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 434, "name": "dark_prismarine", "displayName": "<PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 433, "name": "prismarine_bricks", "displayName": "Prismarine <PERSON>s", "stackSize": 64, "enchantCategories": []}]}, {"id": 435, "stackSize": 64, "name": "prismarine_stairs", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "enchantCategories": [], "blockStateId": 6299}, {"id": 436, "stackSize": 64, "name": "prismarine_bricks_stairs", "displayName": "Prismarine Brick Stairs", "enchantCategories": [], "blockStateId": 6291}, {"id": 437, "stackSize": 64, "name": "dark_prismarine_stairs", "displayName": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "enchantCategories": [], "blockStateId": 3993}, {"id": 438, "stackSize": 64, "name": "sealantern", "displayName": "Sea Lantern", "enchantCategories": []}, {"id": 439, "displayName": "Red Sandstone", "name": "red_sandstone", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 440, "name": "chiseled_red_sandstone", "displayName": "Chiseled Red Sandstone", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 441, "name": "cut_red_sandstone", "displayName": "Cut Red Sandstone", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 229, "name": "smooth_red_sandstone", "displayName": "Smooth Red Sandstone", "stackSize": 64, "enchantCategories": []}]}, {"id": 442, "stackSize": 64, "name": "red_sandstone_stairs", "displayName": "Red Sandstone Stairs", "enchantCategories": []}, {"id": 443, "stackSize": 64, "name": "repeating_command_block", "displayName": "Repeating Command Block", "enchantCategories": []}, {"id": 444, "stackSize": 64, "name": "chain_command_block", "displayName": "Chain Command Block", "enchantCategories": []}, {"id": 445, "stackSize": 64, "name": "magma", "displayName": "Magma Block", "enchantCategories": []}, {"id": 446, "stackSize": 64, "name": "nether_wart_block", "displayName": "Nether Wart Block", "enchantCategories": []}, {"id": 447, "stackSize": 64, "name": "warped_wart_block", "displayName": "Warped Wart Block", "enchantCategories": [], "blockStateId": 7421}, {"id": 448, "stackSize": 64, "name": "red_nether_brick", "displayName": "Red Nether Bricks", "enchantCategories": []}, {"id": 449, "stackSize": 64, "name": "bone_block", "displayName": "Bone Block", "enchantCategories": []}, {"id": 450, "stackSize": 64, "name": "structure_void", "displayName": "Structure Void", "enchantCategories": []}, {"id": 451, "stackSize": 1, "name": "undyed_shulker_box", "displayName": "Shulker Box", "enchantCategories": []}, {"id": 452, "displayName": "White Shulker Box", "name": "shulker_box", "stackSize": 1, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 453, "name": "orange_shulker_box", "displayName": "Orange Shulker Box", "stackSize": 1, "enchantCategories": []}, {"metadata": 2, "id": 454, "name": "magenta_shulker_box", "displayName": "<PERSON><PERSON>a <PERSON>er Box", "stackSize": 1, "enchantCategories": []}, {"metadata": 3, "id": 455, "name": "light_blue_shulker_box", "displayName": "Light Blue Shulker Box", "stackSize": 1, "enchantCategories": []}, {"metadata": 4, "id": 456, "name": "yellow_shulker_box", "displayName": "Yellow Shulker Box", "stackSize": 1, "enchantCategories": []}, {"metadata": 5, "id": 457, "name": "lime_shulker_box", "displayName": "<PERSON>e <PERSON>er Box", "stackSize": 1, "enchantCategories": []}, {"metadata": 6, "id": 458, "name": "pink_shulker_box", "displayName": "Pink Shulker Box", "stackSize": 1, "enchantCategories": []}, {"metadata": 7, "id": 459, "name": "gray_shulker_box", "displayName": "<PERSON>", "stackSize": 1, "enchantCategories": []}, {"metadata": 8, "id": 460, "name": "light_gray_shulker_box", "displayName": "Light Gray Shulker Box", "stackSize": 1, "enchantCategories": []}, {"metadata": 9, "id": 461, "name": "cyan_shulker_box", "displayName": "<PERSON><PERSON>", "stackSize": 1, "enchantCategories": []}, {"metadata": 10, "id": 462, "name": "purple_shulker_box", "displayName": "Purple Shulker Box", "stackSize": 1, "enchantCategories": []}, {"metadata": 11, "id": 463, "name": "blue_shulker_box", "displayName": "Blue Shulker Box", "stackSize": 1, "enchantCategories": []}, {"metadata": 12, "id": 464, "name": "brown_shulker_box", "displayName": "<PERSON> Shulker Box", "stackSize": 1, "enchantCategories": []}, {"metadata": 13, "id": 465, "name": "green_shulker_box", "displayName": "Green Shulker Box", "stackSize": 1, "enchantCategories": []}, {"metadata": 14, "id": 466, "name": "red_shulker_box", "displayName": "Red Shulker Box", "stackSize": 1, "enchantCategories": []}, {"metadata": 15, "id": 467, "name": "black_shulker_box", "displayName": "Black Shulker Box", "stackSize": 1, "enchantCategories": []}]}, {"id": 468, "stackSize": 64, "name": "white_glazed_terracotta", "displayName": "White Glazed Terracotta", "enchantCategories": []}, {"id": 469, "stackSize": 64, "name": "orange_glazed_terracotta", "displayName": "Orange Glazed Terracotta", "enchantCategories": []}, {"id": 470, "stackSize": 64, "name": "magenta_glazed_terracotta", "displayName": "Magenta Glazed Terracotta", "enchantCategories": []}, {"id": 471, "stackSize": 64, "name": "light_blue_glazed_terracotta", "displayName": "Light Blue Glazed Terracotta", "enchantCategories": []}, {"id": 472, "stackSize": 64, "name": "yellow_glazed_terracotta", "displayName": "Yellow Glazed Terracotta", "enchantCategories": []}, {"id": 473, "stackSize": 64, "name": "lime_glazed_terracotta", "displayName": "Lime Glazed Terracotta", "enchantCategories": []}, {"id": 474, "stackSize": 64, "name": "pink_glazed_terracotta", "displayName": "Pink Glazed Terracotta", "enchantCategories": []}, {"id": 475, "stackSize": 64, "name": "gray_glazed_terracotta", "displayName": "Gray Glazed Terracotta", "enchantCategories": []}, {"id": 476, "stackSize": 64, "name": "silver_glazed_terracotta", "displayName": "Light Gray Glazed Terracotta", "enchantCategories": []}, {"id": 477, "stackSize": 64, "name": "cyan_glazed_terracotta", "displayName": "<PERSON><PERSON>zed Terracotta", "enchantCategories": []}, {"id": 478, "stackSize": 64, "name": "purple_glazed_terracotta", "displayName": "Purple Glazed Terracotta", "enchantCategories": []}, {"id": 479, "stackSize": 64, "name": "blue_glazed_terracotta", "displayName": "Blue Glazed Terracotta", "enchantCategories": []}, {"id": 480, "stackSize": 64, "name": "brown_glazed_terracotta", "displayName": "Brown Glazed Terracotta", "enchantCategories": []}, {"id": 481, "stackSize": 64, "name": "green_glazed_terracotta", "displayName": "Green Glazed Terracotta", "enchantCategories": []}, {"id": 482, "stackSize": 64, "name": "red_glazed_terracotta", "displayName": "Red Glazed Terracotta", "enchantCategories": []}, {"id": 483, "stackSize": 64, "name": "black_glazed_terracotta", "displayName": "Black Glazed Terracotta", "enchantCategories": []}, {"id": 484, "displayName": "White Concrete", "name": "concrete", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 485, "name": "orange_concrete", "displayName": "Orange Concrete", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 486, "name": "magenta_concrete", "displayName": "Magenta Concrete", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 487, "name": "light_blue_concrete", "displayName": "Light Blue Concrete", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 488, "name": "yellow_concrete", "displayName": "Yellow Concrete", "stackSize": 64, "enchantCategories": []}, {"metadata": 5, "id": 489, "name": "lime_concrete", "displayName": "Lime Concrete", "stackSize": 64, "enchantCategories": []}, {"metadata": 6, "id": 490, "name": "pink_concrete", "displayName": "Pink Concrete", "stackSize": 64, "enchantCategories": []}, {"metadata": 7, "id": 491, "name": "gray_concrete", "displayName": "<PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 8, "id": 492, "name": "light_gray_concrete", "displayName": "Light Gray Concrete", "stackSize": 64, "enchantCategories": []}, {"metadata": 9, "id": 493, "name": "cyan_concrete", "displayName": "<PERSON><PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 10, "id": 494, "name": "purple_concrete", "displayName": "Purple Concrete", "stackSize": 64, "enchantCategories": []}, {"metadata": 11, "id": 495, "name": "blue_concrete", "displayName": "Blue Concrete", "stackSize": 64, "enchantCategories": []}, {"metadata": 12, "id": 496, "name": "brown_concrete", "displayName": "<PERSON> Concrete", "stackSize": 64, "enchantCategories": []}, {"metadata": 13, "id": 497, "name": "green_concrete", "displayName": "Green Concrete", "stackSize": 64, "enchantCategories": []}, {"metadata": 14, "id": 498, "name": "red_concrete", "displayName": "Red Concrete", "stackSize": 64, "enchantCategories": []}, {"metadata": 15, "id": 499, "name": "black_concrete", "displayName": "Black Concrete", "stackSize": 64, "enchantCategories": []}]}, {"id": 500, "displayName": "White Concrete Powder", "name": "concrete_powder", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 501, "name": "orange_concrete_powder", "displayName": "Orange Concrete Powder", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 502, "name": "magenta_concrete_powder", "displayName": "Magenta Concrete Powder", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 503, "name": "light_blue_concrete_powder", "displayName": "Light Blue Concrete Powder", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 504, "name": "yellow_concrete_powder", "displayName": "Yellow Concrete Powder", "stackSize": 64, "enchantCategories": []}, {"metadata": 5, "id": 505, "name": "lime_concrete_powder", "displayName": "Lime Concrete <PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 6, "id": 506, "name": "pink_concrete_powder", "displayName": "Pink Concrete Powder", "stackSize": 64, "enchantCategories": []}, {"metadata": 7, "id": 507, "name": "gray_concrete_powder", "displayName": "<PERSON> Concre<PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 8, "id": 508, "name": "light_gray_concrete_powder", "displayName": "Light Gray Concrete Powder", "stackSize": 64, "enchantCategories": []}, {"metadata": 9, "id": 509, "name": "cyan_concrete_powder", "displayName": "<PERSON><PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 10, "id": 510, "name": "purple_concrete_powder", "displayName": "Purple Concrete Powder", "stackSize": 64, "enchantCategories": []}, {"metadata": 11, "id": 511, "name": "blue_concrete_powder", "displayName": "Blue Concrete Powder", "stackSize": 64, "enchantCategories": []}, {"metadata": 12, "id": 512, "name": "brown_concrete_powder", "displayName": "<PERSON> Concrete <PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 13, "id": 513, "name": "green_concrete_powder", "displayName": "Green Concrete Powder", "stackSize": 64, "enchantCategories": []}, {"metadata": 14, "id": 514, "name": "red_concrete_powder", "displayName": "Red Concrete Powder", "stackSize": 64, "enchantCategories": []}, {"metadata": 15, "id": 515, "name": "black_concrete_powder", "displayName": "Black Concrete Powder", "stackSize": 64, "enchantCategories": []}]}, {"id": 516, "stackSize": 64, "name": "turtle_egg", "displayName": "Turtle Egg", "enchantCategories": [], "blockStateId": 7183}, {"id": 522, "displayName": "Tube Coral Block", "name": "coral_block", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 523, "name": "brain_coral_block", "displayName": "Brain <PERSON>", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 524, "name": "bubble_coral_block", "displayName": "Bubble Coral Block", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 525, "name": "fire_coral_block", "displayName": "Fire Coral Block", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 526, "name": "horn_coral_block", "displayName": "Horn Coral Block", "stackSize": 64, "enchantCategories": []}, {"metadata": 8, "id": 517, "name": "dead_tube_coral_block", "displayName": "Dead Tube Coral Block", "stackSize": 64, "enchantCategories": []}, {"metadata": 9, "id": 518, "name": "dead_brain_coral_block", "displayName": "Dead Brain Coral Block", "stackSize": 64, "enchantCategories": []}, {"metadata": 10, "id": 519, "name": "dead_bubble_coral_block", "displayName": "Dead Bubble Coral Block", "stackSize": 64, "enchantCategories": []}, {"metadata": 11, "id": 520, "name": "dead_fire_coral_block", "displayName": "Dead Fire Coral Block", "stackSize": 64, "enchantCategories": []}, {"metadata": 12, "id": 521, "name": "dead_horn_coral_block", "displayName": "Dead Horn Coral Block", "stackSize": 64, "enchantCategories": []}], "blockStateId": 3657}, {"id": 527, "displayName": "Tube Coral", "name": "coral", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 528, "name": "brain_coral", "displayName": "Brain Coral", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 529, "name": "bubble_coral", "displayName": "Bubble Coral", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 530, "name": "fire_coral", "displayName": "Fire Coral", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 531, "name": "horn_coral", "displayName": "Horn Coral", "stackSize": 64, "enchantCategories": []}, {"metadata": 8, "id": 536, "name": "dead_tube_coral", "displayName": "Dead Tube Coral", "stackSize": 64, "enchantCategories": []}, {"metadata": 9, "id": 532, "name": "dead_brain_coral", "displayName": "Dead Brain Coral", "stackSize": 64, "enchantCategories": []}, {"metadata": 10, "id": 533, "name": "dead_bubble_coral", "displayName": "Dead Bubble Coral", "stackSize": 64, "enchantCategories": []}, {"metadata": 11, "id": 534, "name": "dead_fire_coral", "displayName": "Dead Fire Coral", "stackSize": 64, "enchantCategories": []}, {"metadata": 12, "id": 535, "name": "dead_horn_coral", "displayName": "Dead Horn Coral", "stackSize": 64, "enchantCategories": []}], "blockStateId": 3647}, {"id": 537, "displayName": "Tube Coral Fan", "name": "coral_fan", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 538, "name": "brain_coral_fan", "displayName": "Brain Coral Fan", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 539, "name": "bubble_coral_fan", "displayName": "Bubble Coral Fan", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 540, "name": "fire_coral_fan", "displayName": "Fire Coral Fan", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 541, "name": "horn_coral_fan", "displayName": "Horn Coral Fan", "stackSize": 64, "enchantCategories": []}], "blockStateId": 3667}, {"id": 542, "displayName": "Dead Tube Coral Fan", "name": "coral_fan_dead", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 543, "name": "dead_brain_coral_fan", "displayName": "Dead Brain Coral Fan", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 544, "name": "dead_bubble_coral_fan", "displayName": "Dead Bubble Coral Fan", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 545, "name": "dead_fire_coral_fan", "displayName": "Dead Fire Coral Fan", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 546, "name": "dead_horn_coral_fan", "displayName": "Dead Horn Coral Fan", "stackSize": 64, "enchantCategories": []}], "blockStateId": 3677}, {"id": 547, "stackSize": 64, "name": "blue_ice", "displayName": "Blue Ice", "enchantCategories": [], "blockStateId": 671}, {"id": 548, "stackSize": 64, "name": "conduit", "displayName": "Conduit", "enchantCategories": [], "blockStateId": 3635}, {"id": 549, "stackSize": 64, "name": "polished_granite_stairs", "displayName": "Polished Granite Stairs", "enchantCategories": [], "blockStateId": 6236}, {"id": 550, "stackSize": 64, "name": "smooth_red_sandstone_stairs", "displayName": "Smooth Red Sandstone Stairs", "enchantCategories": [], "blockStateId": 6656}, {"id": 551, "stackSize": 64, "name": "mossy_stone_brick_stairs", "displayName": "Mossy Stone Brick Stairs", "enchantCategories": [], "blockStateId": 5548}, {"id": 552, "stackSize": 64, "name": "polished_diorite_stairs", "displayName": "Polished Diorite Stairs", "enchantCategories": [], "blockStateId": 6228}, {"id": 553, "stackSize": 64, "name": "mossy_cobblestone_stairs", "displayName": "Mossy Cobblestone Stairs", "enchantCategories": [], "blockStateId": 5540}, {"id": 554, "stackSize": 64, "name": "end_brick_stairs", "displayName": "End Stone Brick Stairs", "enchantCategories": [], "blockStateId": 4676}, {"id": 555, "stackSize": 64, "name": "normal_stone_stairs", "displayName": "Stone Stairs", "enchantCategories": [], "blockStateId": 5578}, {"id": 556, "stackSize": 64, "name": "smooth_sandstone_stairs", "displayName": "Smooth Sandstone Stairs", "enchantCategories": [], "blockStateId": 6664}, {"id": 557, "stackSize": 64, "name": "smooth_quartz_stairs", "displayName": "Smooth Quartz Stairs", "enchantCategories": [], "blockStateId": 6648}, {"id": 558, "stackSize": 64, "name": "granite_stairs", "displayName": "Granite Stairs", "enchantCategories": [], "blockStateId": 4921}, {"id": 559, "stackSize": 64, "name": "andesite_stairs", "displayName": "Andesite Stairs", "enchantCategories": [], "blockStateId": 151}, {"id": 560, "stackSize": 64, "name": "red_nether_brick_stairs", "displayName": "Red Nether Brick Stairs", "enchantCategories": [], "blockStateId": 6455}, {"id": 561, "stackSize": 64, "name": "polished_andesite_stairs", "displayName": "Polished Andesite Stairs", "enchantCategories": [], "blockStateId": 5664}, {"id": 562, "stackSize": 64, "name": "diorite_stairs", "displayName": "Diorite Stairs", "enchantCategories": [], "blockStateId": 4432}, {"id": 563, "stackSize": 64, "name": "cobbled_deepslate_stairs", "displayName": "Cobbled Deepslate Stairs", "enchantCategories": [], "blockStateId": 1114}, {"id": 564, "stackSize": 64, "name": "polished_deepslate_stairs", "displayName": "Polished Deepslate Stairs", "enchantCategories": [], "blockStateId": 6058}, {"id": 565, "stackSize": 64, "name": "deepslate_brick_stairs", "displayName": "Deepslate Brick Stairs", "enchantCategories": [], "blockStateId": 4063}, {"id": 566, "stackSize": 64, "name": "deepslate_tile_stairs", "displayName": "Deepslate Tile Stairs", "enchantCategories": [], "blockStateId": 4246}, {"id": 569, "displayName": "Mossy Stone Brick Slab", "name": "double_stone_slab4", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 574, "name": "smooth_quartz_slab", "displayName": "Smooth Quartz Slab", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 212, "name": "stone_slab", "displayName": "<PERSON> Slab", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 215, "name": "cut_sandstone_slab", "displayName": "Cut Sandstone Slab", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 223, "name": "cut_red_sandstone_slab", "displayName": "Cut Red Sandstone Slab", "stackSize": 64, "enchantCategories": []}], "blockStateId": 4519}, {"id": 572, "displayName": "End Stone Brick Slab", "name": "double_stone_slab3", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 568, "name": "smooth_red_sandstone_slab", "displayName": "Smooth Red Sandstone Slab", "stackSize": 64, "enchantCategories": []}, {"metadata": 2, "id": 578, "name": "polished_andesite_slab", "displayName": "Polished Andesite Slab", "stackSize": 64, "enchantCategories": []}, {"metadata": 3, "id": 576, "name": "andesite_slab", "displayName": "Andesite Slab", "stackSize": 64, "enchantCategories": []}, {"metadata": 4, "id": 579, "name": "diorite_slab", "displayName": "Diorite Slab", "stackSize": 64, "enchantCategories": []}, {"metadata": 5, "id": 570, "name": "polished_diorite_slab", "displayName": "Polished Diorite S<PERSON>b", "stackSize": 64, "enchantCategories": []}, {"metadata": 6, "id": 575, "name": "granite_slab", "displayName": "Granite Slab", "stackSize": 64, "enchantCategories": []}, {"metadata": 7, "id": 567, "name": "polished_granite_slab", "displayName": "Polished Granite Slab", "stackSize": 64, "enchantCategories": []}], "blockStateId": 4509}, {"id": 580, "stackSize": 64, "name": "cobbled_deepslate_slab", "displayName": "Cobbled Deepslate Slab", "enchantCategories": [], "blockStateId": 1106}, {"id": 581, "stackSize": 64, "name": "polished_deepslate_slab", "displayName": "Polished Deepslate Slab", "enchantCategories": [], "blockStateId": 6050}, {"id": 582, "stackSize": 64, "name": "deepslate_brick_slab", "displayName": "Deepslate Brick Slab", "enchantCategories": [], "blockStateId": 4055}, {"id": 583, "stackSize": 64, "name": "deepslate_tile_slab", "displayName": "Deepslate Tile Slab", "enchantCategories": [], "blockStateId": 4238}, {"id": 584, "stackSize": 64, "name": "scaffolding", "displayName": "Scaffolding", "enchantCategories": [], "blockStateId": 6568}, {"id": 585, "stackSize": 64, "name": "redstone", "displayName": "Redstone Dust", "enchantCategories": []}, {"id": 586, "stackSize": 64, "name": "redstone_torch", "displayName": "Redstone Torch", "enchantCategories": []}, {"id": 587, "stackSize": 64, "name": "redstone_block", "displayName": "Block of Redstone", "enchantCategories": []}, {"id": 588, "stackSize": 64, "name": "repeater", "displayName": "Redstone Repeater", "enchantCategories": []}, {"id": 589, "stackSize": 64, "name": "comparator", "displayName": "Redstone Comparator", "enchantCategories": []}, {"id": 590, "stackSize": 64, "name": "piston", "displayName": "<PERSON><PERSON>", "enchantCategories": []}, {"id": 591, "stackSize": 64, "name": "sticky_piston", "displayName": "<PERSON><PERSON>", "enchantCategories": []}, {"id": 592, "stackSize": 64, "name": "slime", "displayName": "Slime Block", "enchantCategories": []}, {"id": 593, "stackSize": 64, "name": "honey_block", "displayName": "Honey Block", "enchantCategories": [], "blockStateId": 5017}, {"id": 594, "stackSize": 64, "name": "observer", "displayName": "Observer", "enchantCategories": []}, {"id": 595, "stackSize": 64, "name": "hopper", "displayName": "<PERSON>", "enchantCategories": []}, {"id": 596, "stackSize": 64, "name": "dispenser", "displayName": "Dispenser", "enchantCategories": []}, {"id": 597, "stackSize": 64, "name": "dropper", "displayName": "Dropper", "enchantCategories": []}, {"id": 598, "stackSize": 64, "name": "lectern", "displayName": "Lectern", "enchantCategories": [], "blockStateId": 5346}, {"id": 599, "stackSize": 64, "name": "target", "displayName": "Target", "enchantCategories": [], "blockStateId": 7105}, {"id": 600, "stackSize": 64, "name": "lever", "displayName": "Lever", "enchantCategories": []}, {"id": 601, "stackSize": 64, "name": "lightning_rod", "displayName": "Lightning Rod", "enchantCategories": [], "blockStateId": 5406}, {"id": 602, "stackSize": 64, "name": "daylight_detector", "displayName": "Daylight Detector", "enchantCategories": []}, {"id": 603, "stackSize": 64, "name": "info_update", "displayName": "Sculk Sensor", "enchantCategories": []}, {"id": 604, "stackSize": 64, "name": "tripwire_hook", "displayName": "Tripwire Hook", "enchantCategories": []}, {"id": 605, "stackSize": 64, "name": "trapped_chest", "displayName": "Trapped Chest", "enchantCategories": []}, {"id": 606, "stackSize": 64, "name": "tnt", "displayName": "TNT", "enchantCategories": []}, {"id": 607, "stackSize": 64, "name": "redstone_lamp", "displayName": "Redstone Lamp", "enchantCategories": []}, {"id": 608, "stackSize": 64, "name": "noteblock", "displayName": "Note Block", "enchantCategories": []}, {"id": 609, "stackSize": 64, "name": "stone_button", "displayName": "<PERSON>", "enchantCategories": []}, {"id": 610, "stackSize": 64, "name": "polished_blackstone_button", "displayName": "Polished Blackstone Button", "enchantCategories": [], "blockStateId": 5855}, {"id": 611, "stackSize": 64, "name": "wooden_button", "displayName": "Oak Button", "enchantCategories": []}, {"id": 612, "stackSize": 64, "name": "spruce_button", "displayName": "Spruce Button", "enchantCategories": [], "blockStateId": 6731}, {"id": 613, "stackSize": 64, "name": "birch_button", "displayName": "<PERSON>", "enchantCategories": [], "blockStateId": 367}, {"id": 614, "stackSize": 64, "name": "jungle_button", "displayName": "<PERSON>ton", "enchantCategories": [], "blockStateId": 5125}, {"id": 615, "stackSize": 64, "name": "acacia_button", "displayName": "Acacia <PERSON>", "enchantCategories": [], "blockStateId": 11}, {"id": 616, "stackSize": 64, "name": "dark_oak_button", "displayName": "Dark Oak Button", "enchantCategories": [], "blockStateId": 3897}, {"id": 617, "stackSize": 64, "name": "crimson_button", "displayName": "<PERSON>", "enchantCategories": [], "blockStateId": 3742}, {"id": 618, "stackSize": 64, "name": "warped_button", "displayName": "Warped <PERSON>", "enchantCategories": [], "blockStateId": 7295}, {"id": 619, "stackSize": 64, "name": "stone_pressure_plate", "displayName": "Stone Pressure Plate", "enchantCategories": []}, {"id": 620, "stackSize": 64, "name": "polished_blackstone_pressure_plate", "displayName": "Polished Blackstone Pressure Plate", "enchantCategories": [], "blockStateId": 5873}, {"id": 621, "stackSize": 64, "name": "light_weighted_pressure_plate", "displayName": "Light Weighted Pressure Plate", "enchantCategories": []}, {"id": 622, "stackSize": 64, "name": "heavy_weighted_pressure_plate", "displayName": "Heavy Weighted Pressure Plate", "enchantCategories": []}, {"id": 623, "stackSize": 64, "name": "wooden_pressure_plate", "displayName": "Oak Pressure Plate", "enchantCategories": []}, {"id": 624, "stackSize": 64, "name": "spruce_pressure_plate", "displayName": "Spruce Pressure Plate", "enchantCategories": [], "blockStateId": 6795}, {"id": 625, "stackSize": 64, "name": "birch_pressure_plate", "displayName": "Birch Pressure Plate", "enchantCategories": [], "blockStateId": 431}, {"id": 626, "stackSize": 64, "name": "jungle_pressure_plate", "displayName": "Jungle Pressure Plate", "enchantCategories": [], "blockStateId": 5189}, {"id": 627, "stackSize": 64, "name": "acacia_pressure_plate", "displayName": "Acacia Pressure Plate", "enchantCategories": [], "blockStateId": 75}, {"id": 628, "stackSize": 64, "name": "dark_oak_pressure_plate", "displayName": "Dark Oak Pressure Plate", "enchantCategories": [], "blockStateId": 3961}, {"id": 629, "stackSize": 64, "name": "crimson_pressure_plate", "displayName": "Crimson Pressure Plate", "enchantCategories": [], "blockStateId": 3815}, {"id": 630, "stackSize": 64, "name": "warped_pressure_plate", "displayName": "Warped Pressure Plate", "enchantCategories": [], "blockStateId": 7368}, {"id": 631, "stackSize": 64, "name": "iron_door", "displayName": "Iron Door", "enchantCategories": []}, {"id": 632, "stackSize": 64, "name": "wooden_door", "displayName": "Oak Door", "enchantCategories": []}, {"id": 633, "stackSize": 64, "name": "spruce_door", "displayName": "Spruce Door", "enchantCategories": []}, {"id": 634, "stackSize": 64, "name": "birch_door", "displayName": "<PERSON>", "enchantCategories": []}, {"id": 635, "stackSize": 64, "name": "jungle_door", "displayName": "Jungle Door", "enchantCategories": []}, {"id": 636, "stackSize": 64, "name": "acacia_door", "displayName": "Acacia Door", "enchantCategories": []}, {"id": 637, "stackSize": 64, "name": "dark_oak_door", "displayName": "Dark Oak Door", "enchantCategories": []}, {"id": 638, "stackSize": 64, "name": "crimson_door", "displayName": "Crimson Door", "enchantCategories": []}, {"id": 639, "stackSize": 64, "name": "warped_door", "displayName": "Warped Door", "enchantCategories": []}, {"id": 640, "stackSize": 64, "name": "iron_trapdoor", "displayName": "Iron Trapdoor", "enchantCategories": []}, {"id": 641, "stackSize": 64, "name": "trapdoor", "displayName": "Oak Trapdoor", "enchantCategories": []}, {"id": 642, "stackSize": 64, "name": "spruce_trapdoor", "displayName": "Spruce Trapdoor", "enchantCategories": [], "blockStateId": 6835}, {"id": 643, "stackSize": 64, "name": "birch_trapdoor", "displayName": "<PERSON>", "enchantCategories": [], "blockStateId": 471}, {"id": 644, "stackSize": 64, "name": "jungle_trapdoor", "displayName": "Jungle Trapdoor", "enchantCategories": [], "blockStateId": 5229}, {"id": 645, "stackSize": 64, "name": "acacia_trapdoor", "displayName": "Acacia T<PERSON>door", "enchantCategories": [], "blockStateId": 115}, {"id": 646, "stackSize": 64, "name": "dark_oak_trapdoor", "displayName": "Dark Oak Trapdoor", "enchantCategories": [], "blockStateId": 3985}, {"id": 647, "stackSize": 64, "name": "crimson_trapdoor", "displayName": "Crimson Trapdoor", "enchantCategories": [], "blockStateId": 3861}, {"id": 648, "stackSize": 64, "name": "warped_trapdoor", "displayName": "Warped Trapdoor", "enchantCategories": [], "blockStateId": 7414}, {"id": 649, "stackSize": 64, "name": "fence_gate", "displayName": "Oak Fence Gate", "enchantCategories": []}, {"id": 650, "stackSize": 64, "name": "spruce_fence_gate", "displayName": "Spruce Fence Gate", "enchantCategories": []}, {"id": 651, "stackSize": 64, "name": "birch_fence_gate", "displayName": "Birch Fence Gate", "enchantCategories": []}, {"id": 652, "stackSize": 64, "name": "jungle_fence_gate", "displayName": "Jungle Fence Gate", "enchantCategories": []}, {"id": 653, "stackSize": 64, "name": "acacia_fence_gate", "displayName": "Acacia Fence Gate", "enchantCategories": []}, {"id": 654, "stackSize": 64, "name": "dark_oak_fence_gate", "displayName": "Dark Oak Fence Gate", "enchantCategories": []}, {"id": 655, "stackSize": 64, "name": "crimson_fence_gate", "displayName": "Crimson Fence Gate", "enchantCategories": [], "blockStateId": 3793}, {"id": 656, "stackSize": 64, "name": "warped_fence_gate", "displayName": "Warped Fence Gate", "enchantCategories": [], "blockStateId": 7346}, {"id": 657, "stackSize": 64, "name": "golden_rail", "displayName": "Powered Rail", "enchantCategories": []}, {"id": 658, "stackSize": 64, "name": "detector_rail", "displayName": "Detector Rail", "enchantCategories": []}, {"id": 659, "stackSize": 64, "name": "rail", "displayName": "Rail", "enchantCategories": []}, {"id": 660, "stackSize": 64, "name": "activator_rail", "displayName": "Activator Rail", "enchantCategories": []}, {"id": 661, "stackSize": 1, "name": "saddle", "displayName": "Saddle", "enchantCategories": []}, {"id": 662, "stackSize": 1, "name": "minecart", "displayName": "Minecart", "enchantCategories": []}, {"id": 663, "stackSize": 1, "name": "chest_minecart", "displayName": "Minecart with Chest", "enchantCategories": []}, {"id": 664, "displayName": "Minecart with Furnace", "name": "hopper_minecart", "stackSize": 1, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 0, "id": 666, "name": "hopper_minecart", "displayName": "Minecart with <PERSON>", "stackSize": 1, "enchantCategories": []}]}, {"id": 665, "stackSize": 1, "name": "tnt_minecart", "displayName": "Minecart with TNT", "enchantCategories": []}, {"id": 667, "stackSize": 1, "name": "carrot_on_a_stick", "displayName": "Carrot on a Stick", "enchantCategories": ["breakable", "vanishable"], "fixedWith": [], "durability": 25, "maxDurability": 25}, {"id": 668, "stackSize": 1, "name": "warped_fungus_on_a_stick", "displayName": "Warped Fungus on a Stick", "enchantCategories": ["breakable", "vanishable"], "fixedWith": [], "durability": 100, "maxDurability": 100}, {"id": 669, "stackSize": 1, "name": "elytra", "displayName": "Elytra", "enchantCategories": ["breakable", "wearable", "vanishable"], "fixedWith": ["phantom_membrane"], "durability": 432, "maxDurability": 432}, {"id": 670, "stackSize": 1, "name": "oak_boat", "displayName": "Oak Boat", "enchantCategories": []}, {"id": 671, "stackSize": 1, "name": "spruce_boat", "displayName": "Spruce Boat", "enchantCategories": []}, {"id": 672, "stackSize": 1, "name": "birch_boat", "displayName": "<PERSON> Boat", "enchantCategories": []}, {"id": 673, "stackSize": 1, "name": "jungle_boat", "displayName": "Jungle Boat", "enchantCategories": []}, {"id": 674, "stackSize": 1, "name": "acacia_boat", "displayName": "Acacia Boat", "enchantCategories": []}, {"id": 675, "stackSize": 1, "name": "dark_oak_boat", "displayName": "Dark Oak Boat", "enchantCategories": []}, {"id": 676, "stackSize": 64, "name": "structure_block", "displayName": "Structure Block", "enchantCategories": []}, {"id": 677, "stackSize": 64, "name": "jigsaw", "displayName": "Jigsaw Block", "enchantCategories": [], "blockStateId": 5112}, {"id": 678, "stackSize": 1, "name": "turtle_helmet", "displayName": "Turtle Shell", "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "fixedWith": ["scute"], "durability": 275, "maxDurability": 275}, {"id": 679, "stackSize": 64, "name": "scute", "displayName": "<PERSON><PERSON>", "enchantCategories": []}, {"id": 680, "stackSize": 1, "name": "flint_and_steel", "displayName": "Flint and Steel", "enchantCategories": ["breakable", "vanishable"], "fixedWith": [], "durability": 64, "maxDurability": 64}, {"id": 681, "stackSize": 64, "name": "apple", "displayName": "Apple", "enchantCategories": []}, {"id": 682, "stackSize": 1, "name": "bow", "displayName": "Bow", "enchantCategories": ["breakable", "bow", "vanishable"], "fixedWith": [], "durability": 384, "maxDurability": 384}, {"id": 683, "displayName": "Arrow", "name": "arrow", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 0, "id": 1006, "name": "spectral_arrow", "displayName": "Spectral Arrow", "stackSize": 64, "enchantCategories": []}, {"metadata": 0, "id": 1007, "name": "tipped_arrow", "displayName": "Tipped Arrow", "stackSize": 64, "enchantCategories": []}]}, {"id": 684, "stackSize": 64, "name": "coal", "displayName": "Coal", "enchantCategories": []}, {"id": 685, "stackSize": 64, "name": "charcoal", "displayName": "Charc<PERSON>l", "enchantCategories": []}, {"id": 686, "stackSize": 64, "name": "diamond", "displayName": "Diamond", "enchantCategories": []}, {"id": 687, "stackSize": 64, "name": "emerald", "displayName": "Emerald", "enchantCategories": []}, {"id": 688, "stackSize": 64, "name": "lapis_lazuli", "displayName": "<PERSON><PERSON>", "enchantCategories": []}, {"id": 689, "stackSize": 64, "name": "quartz", "displayName": "<PERSON><PERSON>", "enchantCategories": []}, {"id": 690, "stackSize": 64, "name": "amethyst_shard", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "enchantCategories": []}, {"id": 691, "stackSize": 64, "name": "raw_iron", "displayName": "Raw Iron", "enchantCategories": []}, {"id": 692, "stackSize": 64, "name": "iron_ingot", "displayName": "Iron Ingot", "enchantCategories": []}, {"id": 693, "stackSize": 64, "name": "raw_copper", "displayName": "Raw Copper", "enchantCategories": []}, {"id": 694, "stackSize": 64, "name": "copper_ingot", "displayName": "Copper Ingot", "enchantCategories": []}, {"id": 695, "stackSize": 64, "name": "raw_gold", "displayName": "Raw Gold", "enchantCategories": []}, {"id": 696, "stackSize": 64, "name": "gold_ingot", "displayName": "Gold Ingot", "enchantCategories": []}, {"id": 697, "stackSize": 64, "name": "netherite_ingot", "displayName": "Netherite Ingot", "enchantCategories": []}, {"id": 698, "stackSize": 64, "name": "netherite_scrap", "displayName": "Netherite Scrap", "enchantCategories": []}, {"id": 699, "stackSize": 1, "name": "wooden_sword", "displayName": "Wooden Sword", "enchantCategories": ["weapon", "breakable", "vanishable"], "fixedWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"], "durability": 59, "maxDurability": 59}, {"id": 700, "stackSize": 1, "name": "wooden_shovel", "displayName": "<PERSON><PERSON>", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"], "durability": 59, "maxDurability": 59}, {"id": 701, "stackSize": 1, "name": "wooden_pickaxe", "displayName": "<PERSON><PERSON> Pick<PERSON>e", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"], "durability": 59, "maxDurability": 59}, {"id": 702, "stackSize": 1, "name": "wooden_axe", "displayName": "Wooden Axe", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"], "durability": 59, "maxDurability": 59}, {"id": 703, "stackSize": 1, "name": "wooden_hoe", "displayName": "<PERSON><PERSON>e", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"], "durability": 59, "maxDurability": 59}, {"id": 704, "stackSize": 1, "name": "stone_sword", "displayName": "Stone Sword", "enchantCategories": ["weapon", "breakable", "vanishable"], "fixedWith": ["cobbled_deepslate", "cobblestone", "blackstone"], "durability": 131, "maxDurability": 131}, {"id": 705, "stackSize": 1, "name": "stone_shovel", "displayName": "<PERSON>el", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["cobbled_deepslate", "cobblestone", "blackstone"], "durability": 131, "maxDurability": 131}, {"id": 706, "stackSize": 1, "name": "stone_pickaxe", "displayName": "<PERSON>", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["cobbled_deepslate", "cobblestone", "blackstone"], "durability": 131, "maxDurability": 131}, {"id": 707, "stackSize": 1, "name": "stone_axe", "displayName": "Stone Axe", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["cobbled_deepslate", "cobblestone", "blackstone"], "durability": 131, "maxDurability": 131}, {"id": 708, "stackSize": 1, "name": "stone_hoe", "displayName": "Stone Hoe", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["cobbled_deepslate", "cobblestone", "blackstone"], "durability": 131, "maxDurability": 131}, {"id": 709, "stackSize": 1, "name": "golden_sword", "displayName": "Golden Sword", "enchantCategories": ["weapon", "breakable", "vanishable"], "fixedWith": ["gold_ingot"], "durability": 32, "maxDurability": 32}, {"id": 710, "stackSize": 1, "name": "golden_shovel", "displayName": "Golden Shovel", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["gold_ingot"], "durability": 32, "maxDurability": 32}, {"id": 711, "stackSize": 1, "name": "golden_pickaxe", "displayName": "Golden Pickaxe", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["gold_ingot"], "durability": 32, "maxDurability": 32}, {"id": 712, "stackSize": 1, "name": "golden_axe", "displayName": "Golden Axe", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["gold_ingot"], "durability": 32, "maxDurability": 32}, {"id": 713, "stackSize": 1, "name": "golden_hoe", "displayName": "Golden Hoe", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["gold_ingot"], "durability": 32, "maxDurability": 32}, {"id": 714, "stackSize": 1, "name": "iron_sword", "displayName": "Iron Sword", "enchantCategories": ["weapon", "breakable", "vanishable"], "fixedWith": ["iron_ingot"], "durability": 250, "maxDurability": 250}, {"id": 715, "stackSize": 1, "name": "iron_shovel", "displayName": "Iron Shovel", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["iron_ingot"], "durability": 250, "maxDurability": 250}, {"id": 716, "stackSize": 1, "name": "iron_pickaxe", "displayName": "Iron Pickaxe", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["iron_ingot"], "durability": 250, "maxDurability": 250}, {"id": 717, "stackSize": 1, "name": "iron_axe", "displayName": "Iron Axe", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["iron_ingot"], "durability": 250, "maxDurability": 250}, {"id": 718, "stackSize": 1, "name": "iron_hoe", "displayName": "Iron Hoe", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["iron_ingot"], "durability": 250, "maxDurability": 250}, {"id": 719, "stackSize": 1, "name": "diamond_sword", "displayName": "Diamond Sword", "enchantCategories": ["weapon", "breakable", "vanishable"], "fixedWith": ["diamond"], "durability": 1561, "maxDurability": 1561}, {"id": 720, "stackSize": 1, "name": "diamond_shovel", "displayName": "Diamond Shovel", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["diamond"], "durability": 1561, "maxDurability": 1561}, {"id": 721, "stackSize": 1, "name": "diamond_pickaxe", "displayName": "Diamond Pickaxe", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["diamond"], "durability": 1561, "maxDurability": 1561}, {"id": 722, "stackSize": 1, "name": "diamond_axe", "displayName": "Diamond Axe", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["diamond"], "durability": 1561, "maxDurability": 1561}, {"id": 723, "stackSize": 1, "name": "diamond_hoe", "displayName": "Diamond Hoe", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["diamond"], "durability": 1561, "maxDurability": 1561}, {"id": 724, "stackSize": 1, "name": "netherite_sword", "displayName": "Netherite Sword", "enchantCategories": ["weapon", "breakable", "vanishable"], "fixedWith": ["netherite_ingot"], "durability": 2031, "maxDurability": 2031}, {"id": 725, "stackSize": 1, "name": "netherite_shovel", "displayName": "<PERSON><PERSON><PERSON>", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["netherite_ingot"], "durability": 2031, "maxDurability": 2031}, {"id": 726, "stackSize": 1, "name": "netherite_pickaxe", "displayName": "Netherite Pickaxe", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["netherite_ingot"], "durability": 2031, "maxDurability": 2031}, {"id": 727, "stackSize": 1, "name": "netherite_axe", "displayName": "Netherite Axe", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["netherite_ingot"], "durability": 2031, "maxDurability": 2031}, {"id": 728, "stackSize": 1, "name": "netherite_hoe", "displayName": "Netherite Hoe", "enchantCategories": ["digger", "breakable", "vanishable"], "fixedWith": ["netherite_ingot"], "durability": 2031, "maxDurability": 2031}, {"id": 729, "displayName": "Stick", "name": "stick", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 0, "id": 1014, "name": "debug_stick", "displayName": "Debug Stick", "stackSize": 1, "enchantCategories": []}]}, {"id": 730, "stackSize": 64, "name": "bowl", "displayName": "Bowl", "enchantCategories": []}, {"id": 731, "stackSize": 1, "name": "mushroom_stew", "displayName": "Mushroom Stew", "enchantCategories": []}, {"id": 732, "stackSize": 64, "name": "string", "displayName": "String", "enchantCategories": []}, {"id": 733, "stackSize": 64, "name": "feather", "displayName": "<PERSON><PERSON>", "enchantCategories": []}, {"id": 734, "stackSize": 64, "name": "gunpowder", "displayName": "Gunpowder", "enchantCategories": []}, {"id": 735, "stackSize": 64, "name": "wheat_seeds", "displayName": "Wheat Seeds", "enchantCategories": []}, {"id": 736, "stackSize": 64, "name": "wheat", "displayName": "Wheat", "enchantCategories": []}, {"id": 737, "stackSize": 64, "name": "bread", "displayName": "Bread", "enchantCategories": []}, {"id": 738, "stackSize": 1, "name": "leather_helmet", "displayName": "Leather Cap", "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "fixedWith": ["leather"], "durability": 55, "maxDurability": 55}, {"id": 739, "stackSize": 1, "name": "leather_chestplate", "displayName": "<PERSON><PERSON>", "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "fixedWith": ["leather"], "durability": 80, "maxDurability": 80}, {"id": 740, "stackSize": 1, "name": "leather_leggings", "displayName": "<PERSON><PERSON>", "enchantCategories": ["armor", "armor_legs", "breakable", "wearable", "vanishable"], "fixedWith": ["leather"], "durability": 75, "maxDurability": 75}, {"id": 741, "stackSize": 1, "name": "leather_boots", "displayName": "<PERSON><PERSON>", "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "fixedWith": ["leather"], "durability": 65, "maxDurability": 65}, {"id": 742, "stackSize": 1, "name": "chainmail_helmet", "displayName": "Chainmail Helmet", "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "fixedWith": ["iron_ingot"], "durability": 165, "maxDurability": 165}, {"id": 743, "stackSize": 1, "name": "chainmail_chestplate", "displayName": "Chainmail Chestplate", "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "fixedWith": ["iron_ingot"], "durability": 240, "maxDurability": 240}, {"id": 744, "stackSize": 1, "name": "chainmail_leggings", "displayName": "Chainmail Leggings", "enchantCategories": ["armor", "armor_legs", "breakable", "wearable", "vanishable"], "fixedWith": ["iron_ingot"], "durability": 225, "maxDurability": 225}, {"id": 745, "stackSize": 1, "name": "chainmail_boots", "displayName": "Chainmail Boots", "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "fixedWith": ["iron_ingot"], "durability": 195, "maxDurability": 195}, {"id": 746, "stackSize": 1, "name": "iron_helmet", "displayName": "Iron Helmet", "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "fixedWith": ["iron_ingot"], "durability": 165, "maxDurability": 165}, {"id": 747, "stackSize": 1, "name": "iron_chestplate", "displayName": "Iron Chestplate", "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "fixedWith": ["iron_ingot"], "durability": 240, "maxDurability": 240}, {"id": 748, "stackSize": 1, "name": "iron_leggings", "displayName": "Iron Leggings", "enchantCategories": ["armor", "armor_legs", "breakable", "wearable", "vanishable"], "fixedWith": ["iron_ingot"], "durability": 225, "maxDurability": 225}, {"id": 749, "stackSize": 1, "name": "iron_boots", "displayName": "Iron Boots", "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "fixedWith": ["iron_ingot"], "durability": 195, "maxDurability": 195}, {"id": 750, "stackSize": 1, "name": "diamond_helmet", "displayName": "Diamond Helmet", "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "fixedWith": ["diamond"], "durability": 363, "maxDurability": 363}, {"id": 751, "stackSize": 1, "name": "diamond_chestplate", "displayName": "Diamond Chestplate", "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "fixedWith": ["diamond"], "durability": 528, "maxDurability": 528}, {"id": 752, "stackSize": 1, "name": "diamond_leggings", "displayName": "Diamond Leggings", "enchantCategories": ["armor", "armor_legs", "breakable", "wearable", "vanishable"], "fixedWith": ["diamond"], "durability": 495, "maxDurability": 495}, {"id": 753, "stackSize": 1, "name": "diamond_boots", "displayName": "Diamond Boots", "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "fixedWith": ["diamond"], "durability": 429, "maxDurability": 429}, {"id": 754, "stackSize": 1, "name": "golden_helmet", "displayName": "Golden Helmet", "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "fixedWith": ["gold_ingot"], "durability": 77, "maxDurability": 77}, {"id": 755, "stackSize": 1, "name": "golden_chestplate", "displayName": "Golden Chestplate", "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "fixedWith": ["gold_ingot"], "durability": 112, "maxDurability": 112}, {"id": 756, "stackSize": 1, "name": "golden_leggings", "displayName": "Golden Leggings", "enchantCategories": ["armor", "armor_legs", "breakable", "wearable", "vanishable"], "fixedWith": ["gold_ingot"], "durability": 105, "maxDurability": 105}, {"id": 757, "stackSize": 1, "name": "golden_boots", "displayName": "Golden Boots", "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "fixedWith": ["gold_ingot"], "durability": 91, "maxDurability": 91}, {"id": 758, "stackSize": 1, "name": "netherite_helmet", "displayName": "Netherite Helmet", "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "fixedWith": ["netherite_ingot"], "durability": 407, "maxDurability": 407}, {"id": 759, "stackSize": 1, "name": "netherite_chestplate", "displayName": "Netherite Chestplate", "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "fixedWith": ["netherite_ingot"], "durability": 592, "maxDurability": 592}, {"id": 760, "stackSize": 1, "name": "netherite_leggings", "displayName": "Netherite Leggings", "enchantCategories": ["armor", "armor_legs", "breakable", "wearable", "vanishable"], "fixedWith": ["netherite_ingot"], "durability": 555, "maxDurability": 555}, {"id": 761, "stackSize": 1, "name": "netherite_boots", "displayName": "Netherite Boots", "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "fixedWith": ["netherite_ingot"], "durability": 481, "maxDurability": 481}, {"id": 762, "stackSize": 64, "name": "flint", "displayName": "Flint", "enchantCategories": []}, {"id": 763, "stackSize": 64, "name": "porkchop", "displayName": "Raw Porkchop", "enchantCategories": []}, {"id": 764, "stackSize": 64, "name": "cooked_porkchop", "displayName": "Cooked Porkchop", "enchantCategories": []}, {"id": 765, "stackSize": 64, "name": "painting", "displayName": "Painting", "enchantCategories": []}, {"id": 766, "stackSize": 64, "name": "golden_apple", "displayName": "Golden Apple", "enchantCategories": []}, {"id": 767, "stackSize": 64, "name": "enchanted_golden_apple", "displayName": "Enchanted Golden Apple", "enchantCategories": []}, {"id": 768, "stackSize": 16, "name": "oak_sign", "displayName": "Oak Sign", "enchantCategories": []}, {"id": 769, "stackSize": 16, "name": "spruce_sign", "displayName": "Spruce Sign", "enchantCategories": []}, {"id": 770, "stackSize": 16, "name": "birch_sign", "displayName": "Birch Sign", "enchantCategories": []}, {"id": 771, "stackSize": 16, "name": "jungle_sign", "displayName": "Jungle Sign", "enchantCategories": []}, {"id": 772, "stackSize": 16, "name": "acacia_sign", "displayName": "Acacia Sign", "enchantCategories": []}, {"id": 773, "stackSize": 16, "name": "dark_oak_sign", "displayName": "Dark Oak Sign", "enchantCategories": []}, {"id": 774, "stackSize": 16, "name": "crimson_sign", "displayName": "Crimson Sign", "enchantCategories": []}, {"id": 775, "stackSize": 16, "name": "warped_sign", "displayName": "Warped Sign", "enchantCategories": []}, {"id": 776, "stackSize": 16, "name": "bucket", "displayName": "Bucket", "enchantCategories": []}, {"id": 777, "stackSize": 1, "name": "water_bucket", "displayName": "Water Bucket", "enchantCategories": []}, {"id": 778, "stackSize": 1, "name": "lava_bucket", "displayName": "<PERSON><PERSON>et", "enchantCategories": []}, {"id": 779, "stackSize": 1, "name": "powder_snow_bucket", "displayName": "Powder Snow Bucket", "enchantCategories": []}, {"id": 780, "stackSize": 16, "name": "snowball", "displayName": "Snowball", "enchantCategories": []}, {"id": 781, "stackSize": 64, "name": "leather", "displayName": "Leather", "enchantCategories": []}, {"id": 782, "stackSize": 1, "name": "milk_bucket", "displayName": "Milk Bucket", "enchantCategories": []}, {"id": 783, "stackSize": 1, "name": "pufferfish_bucket", "displayName": "Bucket of Pufferfish", "enchantCategories": []}, {"id": 784, "stackSize": 1, "name": "salmon_bucket", "displayName": "Bucket of Salmon", "enchantCategories": []}, {"id": 785, "stackSize": 1, "name": "cod_bucket", "displayName": "Bucket of Cod", "enchantCategories": []}, {"id": 786, "stackSize": 1, "name": "tropical_fish_bucket", "displayName": "Bucket of Tropical Fish", "enchantCategories": []}, {"id": 787, "stackSize": 1, "name": "axolotl_bucket", "displayName": "Bucket of Axolotl", "enchantCategories": []}, {"id": 788, "stackSize": 64, "name": "brick", "displayName": "Brick", "enchantCategories": []}, {"id": 789, "stackSize": 64, "name": "clay_ball", "displayName": "<PERSON>", "enchantCategories": []}, {"id": 790, "stackSize": 64, "name": "dried_kelp_block", "displayName": "Dried Kelp Block", "enchantCategories": [], "blockStateId": 4533}, {"id": 791, "stackSize": 64, "name": "paper", "displayName": "Paper", "enchantCategories": []}, {"id": 792, "displayName": "Book", "name": "book", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 0, "id": 1013, "name": "knowledge_book", "displayName": "Knowledge Book", "stackSize": 1, "enchantCategories": []}]}, {"id": 793, "stackSize": 64, "name": "slime_ball", "displayName": "Slimeball", "enchantCategories": []}, {"id": 794, "stackSize": 16, "name": "egg", "displayName": "Egg", "enchantCategories": []}, {"id": 795, "stackSize": 64, "name": "compass", "displayName": "<PERSON>mp<PERSON>", "enchantCategories": ["vanishable"]}, {"id": 796, "displayName": "Bundle", "name": "shulker_shell", "stackSize": 1, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 0, "id": 1011, "name": "shulker_shell", "displayName": "Shulker Shell", "stackSize": 64, "enchantCategories": []}]}, {"id": 797, "stackSize": 1, "name": "fishing_rod", "displayName": "Fishing Rod", "enchantCategories": ["fishing_rod", "breakable", "vanishable"], "fixedWith": [], "durability": 64, "maxDurability": 64}, {"id": 798, "stackSize": 64, "name": "clock", "displayName": "Clock", "enchantCategories": []}, {"id": 799, "stackSize": 1, "name": "spyglass", "displayName": "Spyglass", "enchantCategories": []}, {"id": 800, "stackSize": 64, "name": "glowstone_dust", "displayName": "Glowstone Dust", "enchantCategories": []}, {"id": 801, "stackSize": 64, "name": "cod", "displayName": "Raw Cod", "enchantCategories": []}, {"id": 802, "stackSize": 64, "name": "salmon", "displayName": "Raw Salmon", "enchantCategories": []}, {"id": 803, "stackSize": 64, "name": "tropical_fish", "displayName": "Tropical Fish", "enchantCategories": []}, {"id": 804, "stackSize": 64, "name": "pufferfish", "displayName": "Pufferfish", "enchantCategories": []}, {"id": 805, "stackSize": 64, "name": "cooked_cod", "displayName": "Cooked Cod", "enchantCategories": []}, {"id": 806, "stackSize": 64, "name": "cooked_salmon", "displayName": "Cooked Salmon", "enchantCategories": []}, {"id": 807, "stackSize": 64, "name": "ink_sac", "displayName": "Ink Sac", "enchantCategories": []}, {"id": 808, "stackSize": 64, "name": "glow_ink_sac", "displayName": "Glow Ink Sac", "enchantCategories": []}, {"id": 809, "stackSize": 64, "name": "cocoa_beans", "displayName": "Cocoa Beans", "enchantCategories": []}, {"id": 810, "stackSize": 64, "name": "white_dye", "displayName": "White Dye", "enchantCategories": []}, {"id": 811, "stackSize": 64, "name": "orange_dye", "displayName": "Orange Dye", "enchantCategories": []}, {"id": 812, "stackSize": 64, "name": "magenta_dye", "displayName": "<PERSON><PERSON><PERSON>", "enchantCategories": []}, {"id": 813, "stackSize": 64, "name": "light_blue_dye", "displayName": "Light Blue Dye", "enchantCategories": []}, {"id": 814, "stackSize": 64, "name": "yellow_dye", "displayName": "Yellow Dye", "enchantCategories": []}, {"id": 815, "stackSize": 64, "name": "lime_dye", "displayName": "Lime Dye", "enchantCategories": []}, {"id": 816, "stackSize": 64, "name": "pink_dye", "displayName": "Pink Dye", "enchantCategories": []}, {"id": 817, "stackSize": 64, "name": "gray_dye", "displayName": "<PERSON>", "enchantCategories": []}, {"id": 818, "stackSize": 64, "name": "light_gray_dye", "displayName": "Light Gray D<PERSON>", "enchantCategories": []}, {"id": 819, "stackSize": 64, "name": "cyan_dye", "displayName": "<PERSON><PERSON>", "enchantCategories": []}, {"id": 820, "stackSize": 64, "name": "purple_dye", "displayName": "Purple Dye", "enchantCategories": []}, {"id": 821, "stackSize": 64, "name": "blue_dye", "displayName": "Blue Dye", "enchantCategories": []}, {"id": 822, "stackSize": 64, "name": "brown_dye", "displayName": "<PERSON>", "enchantCategories": []}, {"id": 823, "stackSize": 64, "name": "green_dye", "displayName": "Green Dye", "enchantCategories": []}, {"id": 824, "stackSize": 64, "name": "red_dye", "displayName": "Red Dye", "enchantCategories": []}, {"id": 825, "stackSize": 64, "name": "black_dye", "displayName": "Black Dye", "enchantCategories": []}, {"id": 826, "stackSize": 64, "name": "bone_meal", "displayName": "<PERSON>", "enchantCategories": []}, {"id": 827, "stackSize": 64, "name": "bone", "displayName": "Bone", "enchantCategories": []}, {"id": 828, "stackSize": 64, "name": "sugar", "displayName": "Sugar", "enchantCategories": []}, {"id": 829, "stackSize": 1, "name": "cake", "displayName": "Cake", "enchantCategories": []}, {"id": 830, "displayName": "White Bed", "name": "bed", "stackSize": 1, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 831, "name": "orange_bed", "displayName": "Orange Bed", "stackSize": 1, "enchantCategories": []}, {"metadata": 2, "id": 832, "name": "magenta_bed", "displayName": "Magenta Bed", "stackSize": 1, "enchantCategories": []}, {"metadata": 3, "id": 833, "name": "light_blue_bed", "displayName": "Light Blue Bed", "stackSize": 1, "enchantCategories": []}, {"metadata": 4, "id": 834, "name": "yellow_bed", "displayName": "Yellow Bed", "stackSize": 1, "enchantCategories": []}, {"metadata": 5, "id": 835, "name": "lime_bed", "displayName": "Lime Bed", "stackSize": 1, "enchantCategories": []}, {"metadata": 6, "id": 836, "name": "pink_bed", "displayName": "Pink Bed", "stackSize": 1, "enchantCategories": []}, {"metadata": 7, "id": 837, "name": "gray_bed", "displayName": "Gray Bed", "stackSize": 1, "enchantCategories": []}, {"metadata": 8, "id": 838, "name": "light_gray_bed", "displayName": "Light Gray Bed", "stackSize": 1, "enchantCategories": []}, {"metadata": 9, "id": 839, "name": "cyan_bed", "displayName": "<PERSON><PERSON>", "stackSize": 1, "enchantCategories": []}, {"metadata": 10, "id": 840, "name": "purple_bed", "displayName": "Purple Bed", "stackSize": 1, "enchantCategories": []}, {"metadata": 11, "id": 841, "name": "blue_bed", "displayName": "Blue Bed", "stackSize": 1, "enchantCategories": []}, {"metadata": 12, "id": 842, "name": "brown_bed", "displayName": "Brown Bed", "stackSize": 1, "enchantCategories": []}, {"metadata": 13, "id": 843, "name": "green_bed", "displayName": "Green Bed", "stackSize": 1, "enchantCategories": []}, {"metadata": 14, "id": 844, "name": "red_bed", "displayName": "Red Bed", "stackSize": 1, "enchantCategories": []}, {"metadata": 15, "id": 845, "name": "black_bed", "displayName": "Black Bed", "stackSize": 1, "enchantCategories": []}]}, {"id": 846, "stackSize": 64, "name": "cookie", "displayName": "<PERSON><PERSON>", "enchantCategories": []}, {"id": 847, "stackSize": 64, "name": "filled_map", "displayName": "Map", "enchantCategories": []}, {"id": 848, "stackSize": 1, "name": "shears", "displayName": "Shears", "enchantCategories": ["breakable", "vanishable"], "fixedWith": [], "durability": 238, "maxDurability": 238}, {"id": 849, "stackSize": 64, "name": "melon_slice", "displayName": "<PERSON><PERSON>", "enchantCategories": []}, {"id": 850, "stackSize": 64, "name": "dried_kelp", "displayName": "<PERSON><PERSON>", "enchantCategories": []}, {"id": 851, "stackSize": 64, "name": "pumpkin_seeds", "displayName": "<PERSON><PERSON><PERSON> Seeds", "enchantCategories": []}, {"id": 852, "stackSize": 64, "name": "melon_seeds", "displayName": "<PERSON>on Seeds", "enchantCategories": []}, {"id": 853, "stackSize": 64, "name": "beef", "displayName": "Raw Beef", "enchantCategories": []}, {"id": 854, "stackSize": 64, "name": "cooked_beef", "displayName": "Steak", "enchantCategories": []}, {"id": 855, "stackSize": 64, "name": "chicken", "displayName": "Raw Chicken", "enchantCategories": []}, {"id": 856, "stackSize": 64, "name": "cooked_chicken", "displayName": "Cooked Chicken", "enchantCategories": []}, {"id": 857, "stackSize": 64, "name": "rotten_flesh", "displayName": "Rotten Flesh", "enchantCategories": []}, {"id": 858, "stackSize": 16, "name": "ender_pearl", "displayName": "<PERSON><PERSON>", "enchantCategories": []}, {"id": 859, "stackSize": 64, "name": "blaze_rod", "displayName": "<PERSON>", "enchantCategories": []}, {"id": 860, "stackSize": 64, "name": "ghast_tear", "displayName": "Ghast Tear", "enchantCategories": []}, {"id": 861, "stackSize": 64, "name": "gold_nugget", "displayName": "Gold Nugget", "enchantCategories": []}, {"id": 862, "stackSize": 64, "name": "nether_wart", "displayName": "Nether Wart", "enchantCategories": []}, {"id": 863, "stackSize": 1, "name": "potion", "displayName": "Potion", "enchantCategories": []}, {"id": 864, "stackSize": 64, "name": "glass_bottle", "displayName": "Glass Bottle", "enchantCategories": []}, {"id": 865, "stackSize": 64, "name": "spider_eye", "displayName": "Spider Eye", "enchantCategories": []}, {"id": 866, "stackSize": 64, "name": "fermented_spider_eye", "displayName": "Fermented Spider Eye", "enchantCategories": []}, {"id": 867, "stackSize": 64, "name": "blaze_powder", "displayName": "<PERSON>", "enchantCategories": []}, {"id": 868, "stackSize": 64, "name": "magma_cream", "displayName": "Magma Cream", "enchantCategories": []}, {"id": 869, "stackSize": 64, "name": "brewing_stand", "displayName": "Brewing Stand", "enchantCategories": []}, {"id": 870, "stackSize": 64, "name": "cauldron", "displayName": "<PERSON><PERSON><PERSON>", "enchantCategories": []}, {"id": 871, "stackSize": 64, "name": "ender_eye", "displayName": "Eye of <PERSON>er", "enchantCategories": []}, {"id": 872, "stackSize": 64, "name": "glistering_melon_slice", "displayName": "Glistering <PERSON><PERSON>", "enchantCategories": []}, {"id": 873, "stackSize": 64, "name": "axolotl_spawn_egg", "displayName": "Axolotl Spawn Egg", "enchantCategories": []}, {"id": 874, "stackSize": 64, "name": "bat_spawn_egg", "displayName": "Bat Spawn Egg", "enchantCategories": []}, {"id": 875, "stackSize": 64, "name": "bee_spawn_egg", "displayName": "Bee Spawn Egg", "enchantCategories": []}, {"id": 876, "stackSize": 64, "name": "blaze_spawn_egg", "displayName": "Blaze Spawn Egg", "enchantCategories": []}, {"id": 877, "stackSize": 64, "name": "cat_spawn_egg", "displayName": "Cat Spawn Egg", "enchantCategories": []}, {"id": 878, "stackSize": 64, "name": "cave_spider_spawn_egg", "displayName": "Cave Spider Spawn Egg", "enchantCategories": []}, {"id": 879, "stackSize": 64, "name": "chicken_spawn_egg", "displayName": "Chicken Spawn Egg", "enchantCategories": []}, {"id": 880, "stackSize": 64, "name": "cod_spawn_egg", "displayName": "Cod Spawn Egg", "enchantCategories": []}, {"id": 881, "stackSize": 64, "name": "cow_spawn_egg", "displayName": "Cow Spawn Egg", "enchantCategories": []}, {"id": 882, "stackSize": 64, "name": "creeper_spawn_egg", "displayName": "Creeper Spawn Egg", "enchantCategories": []}, {"id": 883, "stackSize": 64, "name": "dolphin_spawn_egg", "displayName": "Dolphin Spawn Egg", "enchantCategories": []}, {"id": 884, "stackSize": 64, "name": "donkey_spawn_egg", "displayName": "Donkey Spawn Egg", "enchantCategories": []}, {"id": 885, "stackSize": 64, "name": "drowned_spawn_egg", "displayName": "Drowned Spawn Egg", "enchantCategories": []}, {"id": 886, "stackSize": 64, "name": "elder_guardian_spawn_egg", "displayName": "Elder Guardian Spawn Egg", "enchantCategories": []}, {"id": 887, "stackSize": 64, "name": "enderman_spawn_egg", "displayName": "Enderman Spawn Egg", "enchantCategories": []}, {"id": 888, "stackSize": 64, "name": "endermite_spawn_egg", "displayName": "Endermite Spawn Egg", "enchantCategories": []}, {"id": 889, "stackSize": 64, "name": "evoker_spawn_egg", "displayName": "Evoker Spawn Egg", "enchantCategories": []}, {"id": 890, "stackSize": 64, "name": "fox_spawn_egg", "displayName": "Fox Spawn Egg", "enchantCategories": []}, {"id": 891, "stackSize": 64, "name": "ghast_spawn_egg", "displayName": "Ghast Spawn Egg", "enchantCategories": []}, {"id": 892, "stackSize": 64, "name": "glow_squid_spawn_egg", "displayName": "Glow Squid Spawn Egg", "enchantCategories": []}, {"id": 893, "stackSize": 64, "name": "goat_spawn_egg", "displayName": "Goat Spawn Egg", "enchantCategories": []}, {"id": 894, "stackSize": 64, "name": "guardian_spawn_egg", "displayName": "Guardian Spawn Egg", "enchantCategories": []}, {"id": 895, "stackSize": 64, "name": "hoglin_spawn_egg", "displayName": "Hoglin Spawn Egg", "enchantCategories": []}, {"id": 896, "stackSize": 64, "name": "horse_spawn_egg", "displayName": "Horse Spawn Egg", "enchantCategories": []}, {"id": 897, "stackSize": 64, "name": "husk_spawn_egg", "displayName": "Husk Spawn Egg", "enchantCategories": []}, {"id": 898, "displayName": "Llama Spawn Egg", "name": "llama_spawn_egg", "stackSize": 64, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 0, "id": 925, "name": "trader_llama_spawn_egg", "displayName": "Trader <PERSON>lama Spawn Egg", "stackSize": 64, "enchantCategories": []}]}, {"id": 899, "stackSize": 64, "name": "magma_cube_spawn_egg", "displayName": "Magma Cube Spawn Egg", "enchantCategories": []}, {"id": 900, "stackSize": 64, "name": "mooshroom_spawn_egg", "displayName": "Mooshroom Spawn Egg", "enchantCategories": []}, {"id": 901, "stackSize": 64, "name": "mule_spawn_egg", "displayName": "Mule Spawn Egg", "enchantCategories": []}, {"id": 902, "stackSize": 64, "name": "ocelot_spawn_egg", "displayName": "Ocelot Spawn Egg", "enchantCategories": []}, {"id": 903, "stackSize": 64, "name": "panda_spawn_egg", "displayName": "Panda Spawn Egg", "enchantCategories": []}, {"id": 904, "stackSize": 64, "name": "parrot_spawn_egg", "displayName": "Parrot Spawn Egg", "enchantCategories": []}, {"id": 905, "stackSize": 64, "name": "phantom_spawn_egg", "displayName": "Phantom Spawn Egg", "enchantCategories": []}, {"id": 906, "stackSize": 64, "name": "pig_spawn_egg", "displayName": "Pig Spawn Egg", "enchantCategories": []}, {"id": 907, "stackSize": 64, "name": "piglin_spawn_egg", "displayName": "Piglin Spawn Egg", "enchantCategories": []}, {"id": 908, "stackSize": 64, "name": "piglin_brute_spawn_egg", "displayName": "Piglin Brute Spawn Egg", "enchantCategories": []}, {"id": 909, "stackSize": 64, "name": "pillager_spawn_egg", "displayName": "Pillager Spawn Egg", "enchantCategories": []}, {"id": 910, "stackSize": 64, "name": "polar_bear_spawn_egg", "displayName": "Polar Bear Spawn Egg", "enchantCategories": []}, {"id": 911, "stackSize": 64, "name": "pufferfish_spawn_egg", "displayName": "Pufferfish Spawn Egg", "enchantCategories": []}, {"id": 912, "stackSize": 64, "name": "rabbit_spawn_egg", "displayName": "Rabbit Spawn Egg", "enchantCategories": []}, {"id": 913, "stackSize": 64, "name": "ravager_spawn_egg", "displayName": "Ravager Spawn Egg", "enchantCategories": []}, {"id": 914, "stackSize": 64, "name": "salmon_spawn_egg", "displayName": "Salmon Spawn Egg", "enchantCategories": []}, {"id": 915, "stackSize": 64, "name": "sheep_spawn_egg", "displayName": "Sheep Spawn Egg", "enchantCategories": []}, {"id": 916, "stackSize": 64, "name": "shulker_spawn_egg", "displayName": "Shulker Spawn Egg", "enchantCategories": []}, {"id": 917, "stackSize": 64, "name": "silverfish_spawn_egg", "displayName": "Silverfish Spawn Egg", "enchantCategories": []}, {"id": 918, "stackSize": 64, "name": "skeleton_spawn_egg", "displayName": "Skeleton Spawn Egg", "enchantCategories": []}, {"id": 919, "stackSize": 64, "name": "skeleton_horse_spawn_egg", "displayName": "Skeleton Horse Spawn Egg", "enchantCategories": []}, {"id": 920, "stackSize": 64, "name": "slime_spawn_egg", "displayName": "Slime Spawn Egg", "enchantCategories": []}, {"id": 921, "stackSize": 64, "name": "spider_spawn_egg", "displayName": "Spider Spawn Egg", "enchantCategories": []}, {"id": 922, "stackSize": 64, "name": "squid_spawn_egg", "displayName": "Squid Spawn Egg", "enchantCategories": []}, {"id": 923, "stackSize": 64, "name": "stray_spawn_egg", "displayName": "Stray Spawn Egg", "enchantCategories": []}, {"id": 924, "stackSize": 64, "name": "strider_spawn_egg", "displayName": "Strider Spawn Egg", "enchantCategories": []}, {"id": 926, "stackSize": 64, "name": "tropical_fish_spawn_egg", "displayName": "Tropical Fish Spawn Egg", "enchantCategories": []}, {"id": 927, "stackSize": 64, "name": "turtle_spawn_egg", "displayName": "Turtle Spawn Egg", "enchantCategories": []}, {"id": 928, "stackSize": 64, "name": "vex_spawn_egg", "displayName": "Vex Spawn Egg", "enchantCategories": []}, {"id": 929, "stackSize": 64, "name": "villager_spawn_egg", "displayName": "Villager Spawn Egg", "enchantCategories": []}, {"id": 930, "stackSize": 64, "name": "vindicator_spawn_egg", "displayName": "Vindicator Spawn Egg", "enchantCategories": []}, {"id": 931, "stackSize": 64, "name": "wandering_trader_spawn_egg", "displayName": "Wandering Trader Spawn Egg", "enchantCategories": []}, {"id": 932, "stackSize": 64, "name": "witch_spawn_egg", "displayName": "Witch Spawn Egg", "enchantCategories": []}, {"id": 933, "stackSize": 64, "name": "wither_skeleton_spawn_egg", "displayName": "Wither Skeleton Spawn Egg", "enchantCategories": []}, {"id": 934, "stackSize": 64, "name": "wolf_spawn_egg", "displayName": "Wolf Spawn Egg", "enchantCategories": []}, {"id": 935, "stackSize": 64, "name": "zoglin_spawn_egg", "displayName": "Zoglin Spawn Egg", "enchantCategories": []}, {"id": 936, "stackSize": 64, "name": "zombie_spawn_egg", "displayName": "Zombie Spawn Egg", "enchantCategories": []}, {"id": 937, "stackSize": 64, "name": "zombie_horse_spawn_egg", "displayName": "Zombie Horse Spawn Egg", "enchantCategories": []}, {"id": 938, "stackSize": 64, "name": "zombie_villager_spawn_egg", "displayName": "Zombie Villager Spawn Egg", "enchantCategories": []}, {"id": 939, "stackSize": 64, "name": "zombie_pigman_spawn_egg", "displayName": "Zombified Piglin Spawn Egg", "enchantCategories": []}, {"id": 940, "stackSize": 64, "name": "experience_bottle", "displayName": "Bottle o' Enchanting", "enchantCategories": []}, {"id": 941, "stackSize": 64, "name": "fire_charge", "displayName": "Fire Charge", "enchantCategories": []}, {"id": 942, "stackSize": 1, "name": "writable_book", "displayName": "Book and Quill", "enchantCategories": []}, {"id": 943, "stackSize": 16, "name": "written_book", "displayName": "Written Book", "enchantCategories": []}, {"id": 944, "stackSize": 64, "name": "frame", "displayName": "<PERSON><PERSON>", "enchantCategories": []}, {"id": 945, "stackSize": 64, "name": "glow_frame", "displayName": "G<PERSON> Item <PERSON>", "enchantCategories": []}, {"id": 946, "stackSize": 64, "name": "flower_pot", "displayName": "Flower Pot", "enchantCategories": []}, {"id": 947, "stackSize": 64, "name": "carrot", "displayName": "Carrot", "enchantCategories": []}, {"id": 948, "stackSize": 64, "name": "potato", "displayName": "Potato", "enchantCategories": []}, {"id": 949, "stackSize": 64, "name": "baked_potato", "displayName": "Baked Potato", "enchantCategories": []}, {"id": 950, "stackSize": 64, "name": "poisonous_potato", "displayName": "Poisonous Potato", "enchantCategories": []}, {"id": 951, "stackSize": 64, "name": "empty_map", "displayName": "Empty Map", "enchantCategories": []}, {"id": 952, "stackSize": 64, "name": "golden_carrot", "displayName": "Golden Carrot", "enchantCategories": []}, {"id": 953, "displayName": "Skeleton Skull", "name": "skull", "stackSize": 64, "metadata": 0, "enchantCategories": ["wearable", "vanishable"], "variations": [{"metadata": 1, "id": 954, "name": "wither_skeleton_skull", "displayName": "Wither Skeleton Skull", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"metadata": 2, "id": 956, "name": "zombie_head", "displayName": "Zombie Head", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"metadata": 3, "id": 955, "name": "player_head", "displayName": "Player Head", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"metadata": 4, "id": 957, "name": "creeper_head", "displayName": "Creeper Head", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"metadata": 5, "id": 958, "name": "dragon_head", "displayName": "Dragon Head", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}]}, {"id": 959, "stackSize": 64, "name": "nether_star", "displayName": "Nether Star", "enchantCategories": []}, {"id": 960, "stackSize": 64, "name": "pumpkin_pie", "displayName": "Pumpkin Pie", "enchantCategories": []}, {"id": 961, "stackSize": 64, "name": "firework_rocket", "displayName": "Firework Rocket", "enchantCategories": []}, {"id": 962, "stackSize": 64, "name": "firework_star", "displayName": "Firework Star", "enchantCategories": []}, {"id": 963, "stackSize": 1, "name": "enchanted_book", "displayName": "Enchanted Book", "enchantCategories": []}, {"id": 965, "stackSize": 64, "name": "prismarine_shard", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "enchantCategories": []}, {"id": 966, "stackSize": 64, "name": "prismarine_crystals", "displayName": "Prismarine Crystals", "enchantCategories": []}, {"id": 967, "stackSize": 64, "name": "rabbit", "displayName": "Raw Rabbit", "enchantCategories": []}, {"id": 968, "stackSize": 64, "name": "cooked_rabbit", "displayName": "Cooked Rabbit", "enchantCategories": []}, {"id": 969, "stackSize": 1, "name": "rabbit_stew", "displayName": "Rabbit Stew", "enchantCategories": []}, {"id": 970, "stackSize": 64, "name": "rabbit_foot", "displayName": "<PERSON>'s Foot", "enchantCategories": []}, {"id": 971, "stackSize": 64, "name": "rabbit_hide", "displayName": "<PERSON>", "enchantCategories": []}, {"id": 972, "stackSize": 16, "name": "armor_stand", "displayName": "Armor Stand", "enchantCategories": []}, {"id": 973, "stackSize": 1, "name": "iron_horse_armor", "displayName": "Iron Horse Armor", "enchantCategories": []}, {"id": 974, "stackSize": 1, "name": "golden_horse_armor", "displayName": "Golden Horse Armor", "enchantCategories": []}, {"id": 975, "stackSize": 1, "name": "diamond_horse_armor", "displayName": "Diamond Horse Armor", "enchantCategories": []}, {"id": 976, "stackSize": 1, "name": "leather_horse_armor", "displayName": "Leather Horse Armor", "enchantCategories": []}, {"id": 977, "stackSize": 64, "name": "lead", "displayName": "Lead", "enchantCategories": []}, {"id": 978, "stackSize": 64, "name": "name_tag", "displayName": "Name Tag", "enchantCategories": []}, {"id": 979, "stackSize": 1, "name": "command_block_minecart", "displayName": "Minecart with Command Block", "enchantCategories": []}, {"id": 980, "stackSize": 64, "name": "mutton", "displayName": "<PERSON>", "enchantCategories": []}, {"id": 981, "stackSize": 64, "name": "cooked_mutton", "displayName": "Cooked <PERSON>tton", "enchantCategories": []}, {"id": 997, "displayName": "Black Banner", "name": "banner", "stackSize": 16, "metadata": 0, "enchantCategories": [], "variations": [{"metadata": 1, "id": 996, "name": "red_banner", "displayName": "Red Banner", "stackSize": 16, "enchantCategories": []}, {"metadata": 2, "id": 995, "name": "green_banner", "displayName": "<PERSON> Banner", "stackSize": 16, "enchantCategories": []}, {"metadata": 3, "id": 994, "name": "brown_banner", "displayName": "<PERSON>", "stackSize": 16, "enchantCategories": []}, {"metadata": 4, "id": 993, "name": "blue_banner", "displayName": "Blue Banner", "stackSize": 16, "enchantCategories": []}, {"metadata": 5, "id": 992, "name": "purple_banner", "displayName": "<PERSON> Banner", "stackSize": 16, "enchantCategories": []}, {"metadata": 6, "id": 991, "name": "cyan_banner", "displayName": "<PERSON><PERSON>", "stackSize": 16, "enchantCategories": []}, {"metadata": 7, "id": 990, "name": "light_gray_banner", "displayName": "<PERSON> Gray Banner", "stackSize": 16, "enchantCategories": []}, {"metadata": 8, "id": 989, "name": "gray_banner", "displayName": "<PERSON>", "stackSize": 16, "enchantCategories": []}, {"metadata": 9, "id": 988, "name": "pink_banner", "displayName": "Pink Banner", "stackSize": 16, "enchantCategories": []}, {"metadata": 10, "id": 987, "name": "lime_banner", "displayName": "Lime Banner", "stackSize": 16, "enchantCategories": []}, {"metadata": 11, "id": 986, "name": "yellow_banner", "displayName": "Yellow Banner", "stackSize": 16, "enchantCategories": []}, {"metadata": 12, "id": 985, "name": "light_blue_banner", "displayName": "Light Blue Banner", "stackSize": 16, "enchantCategories": []}, {"metadata": 13, "id": 984, "name": "magenta_banner", "displayName": "Magenta Banner", "stackSize": 16, "enchantCategories": []}, {"metadata": 14, "id": 983, "name": "orange_banner", "displayName": "Orange Banner", "stackSize": 16, "enchantCategories": []}, {"metadata": 15, "id": 982, "name": "white_banner", "displayName": "White Banner", "stackSize": 16, "enchantCategories": []}]}, {"id": 998, "stackSize": 64, "name": "end_crystal", "displayName": "End Crystal", "enchantCategories": []}, {"id": 999, "stackSize": 64, "name": "chorus_fruit", "displayName": "Chorus Fruit", "enchantCategories": []}, {"id": 1000, "stackSize": 64, "name": "popped_chorus_fruit", "displayName": "Popped Chorus Fruit", "enchantCategories": []}, {"id": 1001, "stackSize": 64, "name": "beetroot", "displayName": "Beetroot", "enchantCategories": []}, {"id": 1002, "stackSize": 64, "name": "beetroot_seeds", "displayName": "Beetroot Seeds", "enchantCategories": []}, {"id": 1003, "stackSize": 1, "name": "beetroot_soup", "displayName": "Beetroot Soup", "enchantCategories": []}, {"id": 1004, "stackSize": 64, "name": "dragon_breath", "displayName": "Dragon's Breath", "enchantCategories": []}, {"id": 1005, "stackSize": 1, "name": "splash_potion", "displayName": "Splash Potion", "enchantCategories": []}, {"id": 1008, "stackSize": 1, "name": "lingering_potion", "displayName": "Lingering Potion", "enchantCategories": []}, {"id": 1009, "stackSize": 1, "name": "shield", "displayName": "Shield", "enchantCategories": ["breakable", "vanishable"], "fixedWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"], "durability": 336, "maxDurability": 336}, {"id": 1010, "stackSize": 1, "name": "totem_of_undying", "displayName": "Totem of Undying", "enchantCategories": []}, {"id": 1012, "stackSize": 64, "name": "iron_nugget", "displayName": "Iron Nugget", "enchantCategories": []}, {"id": 1015, "stackSize": 1, "name": "music_disc_13", "displayName": "Music Disc", "enchantCategories": []}, {"id": 1016, "stackSize": 1, "name": "music_disc_cat", "displayName": "Music Disc", "enchantCategories": []}, {"id": 1017, "stackSize": 1, "name": "music_disc_blocks", "displayName": "Music Disc", "enchantCategories": []}, {"id": 1018, "stackSize": 1, "name": "music_disc_chirp", "displayName": "Music Disc", "enchantCategories": []}, {"id": 1019, "stackSize": 1, "name": "music_disc_far", "displayName": "Music Disc", "enchantCategories": []}, {"id": 1020, "stackSize": 1, "name": "music_disc_mall", "displayName": "Music Disc", "enchantCategories": []}, {"id": 1021, "stackSize": 1, "name": "music_disc_mellohi", "displayName": "Music Disc", "enchantCategories": []}, {"id": 1022, "stackSize": 1, "name": "music_disc_stal", "displayName": "Music Disc", "enchantCategories": []}, {"id": 1023, "stackSize": 1, "name": "music_disc_strad", "displayName": "Music Disc", "enchantCategories": []}, {"id": 1024, "stackSize": 1, "name": "music_disc_ward", "displayName": "Music Disc", "enchantCategories": []}, {"id": 1025, "stackSize": 1, "name": "music_disc_11", "displayName": "Music Disc", "enchantCategories": []}, {"id": 1026, "stackSize": 1, "name": "music_disc_wait", "displayName": "Music Disc", "enchantCategories": []}, {"id": 1027, "stackSize": 1, "name": "music_disc_pigstep", "displayName": "Music Disc", "enchantCategories": []}, {"id": 1028, "stackSize": 1, "name": "trident", "displayName": "Trident", "enchantCategories": ["trident", "breakable", "vanishable"], "fixedWith": [], "durability": 250, "maxDurability": 250}, {"id": 1029, "stackSize": 64, "name": "phantom_membrane", "displayName": "Phantom Membrane", "enchantCategories": []}, {"id": 1030, "stackSize": 64, "name": "nautilus_shell", "displayName": "Nautilus Shell", "enchantCategories": []}, {"id": 1031, "stackSize": 64, "name": "heart_of_the_sea", "displayName": "Heart of the Sea", "enchantCategories": []}, {"id": 1032, "stackSize": 1, "name": "crossbow", "displayName": "Crossbow", "enchantCategories": ["breakable", "crossbow", "vanishable"], "fixedWith": [], "durability": 326, "maxDurability": 326}, {"id": 1033, "stackSize": 1, "name": "suspicious_stew", "displayName": "Suspicious Stew", "enchantCategories": []}, {"id": 1034, "stackSize": 64, "name": "loom", "displayName": "Loom", "enchantCategories": [], "blockStateId": 5460}, {"id": 1035, "stackSize": 1, "name": "flower_banner_pattern", "displayName": "<PERSON>", "enchantCategories": []}, {"id": 1036, "stackSize": 1, "name": "creeper_banner_pattern", "displayName": "<PERSON>", "enchantCategories": []}, {"id": 1037, "stackSize": 1, "name": "skull_banner_pattern", "displayName": "<PERSON>", "enchantCategories": []}, {"id": 1038, "stackSize": 1, "name": "mojang_banner_pattern", "displayName": "<PERSON>", "enchantCategories": []}, {"id": 1039, "stackSize": 1, "name": "banner_pattern", "displayName": "<PERSON>", "enchantCategories": []}, {"id": 1040, "stackSize": 1, "name": "piglin_banner_pattern", "displayName": "<PERSON>", "enchantCategories": []}, {"id": 1041, "stackSize": 64, "name": "composter", "displayName": "Composter", "enchantCategories": [], "blockStateId": 3602}, {"id": 1042, "stackSize": 64, "name": "barrel", "displayName": "Barrel", "enchantCategories": [], "blockStateId": 212}, {"id": 1043, "stackSize": 64, "name": "smoker", "displayName": "Smoker", "enchantCategories": [], "blockStateId": 6639}, {"id": 1044, "stackSize": 64, "name": "blast_furnace", "displayName": "Blast Furnace", "enchantCategories": [], "blockStateId": 664}, {"id": 1045, "stackSize": 64, "name": "cartography_table", "displayName": "Cartography Table", "enchantCategories": [], "blockStateId": 947}, {"id": 1046, "stackSize": 64, "name": "fletching_table", "displayName": "Fletching Table", "enchantCategories": [], "blockStateId": 4761}, {"id": 1047, "stackSize": 64, "name": "grindstone", "displayName": "Grindstone", "enchantCategories": [], "blockStateId": 4952}, {"id": 1048, "stackSize": 64, "name": "smithing_table", "displayName": "Smithing Table", "enchantCategories": [], "blockStateId": 6633}, {"id": 1049, "stackSize": 64, "name": "stonecutter_block", "displayName": "<PERSON><PERSON><PERSON>", "enchantCategories": [], "blockStateId": 7054}, {"id": 1050, "stackSize": 64, "name": "bell", "displayName": "Bell", "enchantCategories": [], "blockStateId": 323}, {"id": 1051, "stackSize": 64, "name": "lantern", "displayName": "Lantern", "enchantCategories": [], "blockStateId": 5269}, {"id": 1052, "stackSize": 64, "name": "soul_lantern", "displayName": "Soul Lantern", "enchantCategories": [], "blockStateId": 6708}, {"id": 1053, "stackSize": 64, "name": "sweet_berries", "displayName": "Sweet Berries", "enchantCategories": []}, {"id": 1054, "stackSize": 64, "name": "glow_berries", "displayName": "Glow Berries", "enchantCategories": []}, {"id": 1055, "stackSize": 64, "name": "campfire", "displayName": "Campfire", "enchantCategories": []}, {"id": 1056, "stackSize": 64, "name": "soul_campfire", "displayName": "Soul Campfire", "enchantCategories": []}, {"id": 1057, "stackSize": 64, "name": "shroomlight", "displayName": "Shroomlight", "enchantCategories": [], "blockStateId": 6583}, {"id": 1058, "stackSize": 64, "name": "honeycomb", "displayName": "Honeycomb", "enchantCategories": []}, {"id": 1059, "stackSize": 64, "name": "bee_nest", "displayName": "Bee Nest", "enchantCategories": [], "blockStateId": 259}, {"id": 1060, "stackSize": 64, "name": "beehive", "displayName": "Beehive", "enchantCategories": [], "blockStateId": 283}, {"id": 1061, "stackSize": 16, "name": "honey_bottle", "displayName": "<PERSON>", "enchantCategories": []}, {"id": 1062, "stackSize": 64, "name": "honeycomb_block", "displayName": "Honeycomb Block", "enchantCategories": [], "blockStateId": 5018}, {"id": 1063, "stackSize": 64, "name": "lodestone", "displayName": "Lodestone", "enchantCategories": [], "blockStateId": 5438}, {"id": 1064, "stackSize": 64, "name": "crying_obsidian", "displayName": "Crying Obsidian", "enchantCategories": [], "blockStateId": 3868}, {"id": 1065, "stackSize": 64, "name": "blackstone", "displayName": "Blackstone", "enchantCategories": [], "blockStateId": 484}, {"id": 1066, "stackSize": 64, "name": "blackstone_slab", "displayName": "Blackstone Slab", "enchantCategories": [], "blockStateId": 488}, {"id": 1067, "stackSize": 64, "name": "blackstone_stairs", "displayName": "Blackstone Stairs", "enchantCategories": [], "blockStateId": 496}, {"id": 1068, "stackSize": 64, "name": "gilded_blackstone", "displayName": "Gilded Blackstone", "enchantCategories": [], "blockStateId": 4819}, {"id": 1069, "stackSize": 64, "name": "polished_blackstone", "displayName": "Polished Blackstone", "enchantCategories": [], "blockStateId": 5668}, {"id": 1070, "stackSize": 64, "name": "polished_blackstone_slab", "displayName": "Polished Blackstone Slab", "enchantCategories": [], "blockStateId": 5875}, {"id": 1071, "stackSize": 64, "name": "polished_blackstone_stairs", "displayName": "Polished Blackstone Stairs", "enchantCategories": [], "blockStateId": 5883}, {"id": 1072, "stackSize": 64, "name": "chiseled_polished_blackstone", "displayName": "Chiseled Polished Blackstone", "enchantCategories": [], "blockStateId": 1091}, {"id": 1073, "stackSize": 64, "name": "polished_blackstone_bricks", "displayName": "Polished Blackstone Bricks", "enchantCategories": [], "blockStateId": 5843}, {"id": 1074, "stackSize": 64, "name": "polished_blackstone_brick_slab", "displayName": "Polished Blackstone Brick Slab", "enchantCategories": [], "blockStateId": 5672}, {"id": 1075, "stackSize": 64, "name": "polished_blackstone_brick_stairs", "displayName": "Polished Blackstone Brick Stairs", "enchantCategories": [], "blockStateId": 5680}, {"id": 1076, "stackSize": 64, "name": "cracked_polished_blackstone_bricks", "displayName": "Cracked Polished Blackstone Bricks", "enchantCategories": [], "blockStateId": 3729}, {"id": 1077, "stackSize": 64, "name": "respawn_anchor", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "enchantCategories": [], "blockStateId": 6526}, {"id": 1095, "stackSize": 64, "name": "small_amethyst_bud", "displayName": "Small Amethyst Bud", "enchantCategories": [], "blockStateId": 6624}, {"id": 1096, "stackSize": 64, "name": "medium_amethyst_bud", "displayName": "Medium Amethyst Bud", "enchantCategories": [], "blockStateId": 5473}, {"id": 1097, "stackSize": 64, "name": "large_amethyst_bud", "displayName": "Large Amethyst Bud", "enchantCategories": [], "blockStateId": 5277}, {"id": 1098, "stackSize": 64, "name": "amethyst_cluster", "displayName": "Amethyst Cluster", "enchantCategories": [], "blockStateId": 142}, {"id": 1099, "stackSize": 64, "name": "pointed_dripstone", "displayName": "Pointed Dripstone", "enchantCategories": [], "blockStateId": 5656}, {"id": 9000, "stackSize": 1, "name": "yellow_candle_cake", "displayName": "Yellow Candle Cake"}, {"id": 9002, "stackSize": 1, "name": "item.wooden_door", "displayName": "Wooden Door"}, {"id": 9005, "stackSize": 1, "name": "weathered_double_cut_copper_slab", "blockStateId": 7508, "displayName": "Weathered Double Cut Copper Slab"}, {"id": 9008, "stackSize": 1, "name": "waxed_weathered_double_cut_copper_slab", "blockStateId": 7494, "displayName": "Waxed Weathered Double Cut Copper Slab"}, {"id": 9011, "stackSize": 1, "name": "waxed_exposed_double_cut_copper_slab", "blockStateId": 7466, "displayName": "Waxed Exposed Double Cut Copper Slab"}, {"id": 9014, "stackSize": 1, "name": "waxed_double_cut_copper_slab", "blockStateId": 7452, "displayName": "Waxed Double Cut Copper Slab"}, {"id": 9018, "stackSize": 1, "name": "warped_standing_sign", "blockStateId": 7395, "displayName": "Warped Standing Sign"}, {"id": 9023, "stackSize": 1, "name": "unknown", "blockStateId": 7217, "displayName": "Unknown"}, {"id": 9024, "stackSize": 1, "name": "underwater_torch", "displayName": "Underwater Torch"}, {"id": 9028, "stackSize": 1, "name": "sweet_berry_bush", "blockStateId": 7100, "displayName": "Sweet <PERSON>"}, {"id": 9037, "stackSize": 1, "name": "warped_double_slab", "blockStateId": 7329, "displayName": "Warped Double Slab"}, {"id": 9041, "stackSize": 1, "name": "stickypistonarmcollision", "displayName": "Stickypistonarmcollision"}, {"id": 9043, "stackSize": 1, "name": "standing_sign", "displayName": "Standing Sign"}, {"id": 9048, "stackSize": 1, "name": "warped_wall_sign", "blockStateId": 7420, "displayName": "Warped Wall Sign"}, {"id": 9053, "stackSize": 1, "name": "item.skull", "displayName": "Skull"}, {"id": 9056, "stackSize": 1, "name": "sculk_sensor", "blockStateId": 6570, "displayName": "Sculk Sensor"}, {"id": 9058, "stackSize": 1, "name": "item.reeds", "displayName": "<PERSON><PERSON>"}, {"id": 9071, "stackSize": 1, "name": "powered_repeater", "displayName": "Powered Repeater"}, {"id": 9072, "stackSize": 1, "name": "potatoes", "displayName": "Potatoes"}, {"id": 9073, "stackSize": 1, "name": "purple_candle_cake", "displayName": "Purple Candle Cake"}, {"id": 9080, "stackSize": 1, "name": "polished_blackstone_double_slab", "blockStateId": 5857, "displayName": "Polished Blackstone Double Slab"}, {"id": 9083, "stackSize": 1, "name": "unlit_redstone_torch", "displayName": "Unlit Redstone Torch"}, {"id": 9086, "stackSize": 1, "name": "polished_blackstone_brick_double_slab", "blockStateId": 5670, "displayName": "Polished Blackstone Brick Double Slab"}, {"id": 9091, "stackSize": 1, "name": "pistonarmcollision", "displayName": "Pistonarmcollision"}, {"id": 9093, "stackSize": 1, "name": "oxidized_double_cut_copper_slab", "blockStateId": 5620, "displayName": "Oxidized Double Cut Copper Slab"}, {"id": 9100, "stackSize": 1, "name": "movingblock", "displayName": "Movingblock"}, {"id": 9107, "stackSize": 1, "name": "lit_smoker", "blockStateId": 5437, "displayName": "Lit Smoker"}, {"id": 9108, "stackSize": 1, "name": "lit_deepslate_redstone_ore", "blockStateId": 5419, "displayName": "Lit Deepslate Redstone Ore"}, {"id": 9109, "stackSize": 1, "name": "white_candle_cake", "displayName": "White Candle Cake"}, {"id": 9110, "stackSize": 1, "name": "lit_blast_furnace", "blockStateId": 5418, "displayName": "Lit Blast Furnace"}, {"id": 9113, "stackSize": 1, "name": "lime_candle_cake", "displayName": "Lime Candle Cake"}, {"id": 9115, "stackSize": 1, "name": "lava_cauldron", "blockStateId": 5314, "displayName": "<PERSON><PERSON>"}, {"id": 9117, "stackSize": 1, "name": "spruce_wall_sign", "blockStateId": 6841, "displayName": "Spruce Wall Sign"}, {"id": 9124, "stackSize": 1, "name": "unpowered_repeater", "displayName": "Unpowered Repeater"}, {"id": 9125, "stackSize": 1, "name": "tripwire", "displayName": "Tripwire"}, {"id": 9129, "stackSize": 1, "name": "info_update2", "displayName": "Info Update2"}, {"id": 9135, "stackSize": 1, "name": "hard_glass", "displayName": "Hard Glass"}, {"id": 9136, "stackSize": 1, "name": "item.warped_door", "displayName": "Warped Door"}, {"id": 9137, "stackSize": 1, "name": "green_candle_cake", "displayName": "Green Candle Cake"}, {"id": 9144, "stackSize": 1, "name": "item.frame", "displayName": "<PERSON>ame"}, {"id": 9145, "stackSize": 1, "name": "item.flower_pot", "displayName": "Flower Pot"}, {"id": 9149, "stackSize": 1, "name": "light_blue_candle_cake", "displayName": "Light Blue Candle Cake"}, {"id": 9150, "stackSize": 1, "name": "end_portal", "displayName": "End Portal"}, {"id": 9151, "stackSize": 1, "name": "end_gateway", "displayName": "End Gateway"}, {"id": 9158, "stackSize": 1, "name": "double_wooden_slab", "displayName": "Double Wooden Slab"}, {"id": 9170, "stackSize": 1, "name": "spruce_standing_sign", "blockStateId": 6819, "displayName": "Spruce Standing Sign"}, {"id": 9171, "stackSize": 1, "name": "daylight_detector_inverted", "displayName": "Daylight Detector Inverted"}, {"id": 9174, "stackSize": 1, "name": "darkoak_wall_sign", "blockStateId": 4015, "displayName": "Darkoak Wall Sign"}, {"id": 9175, "stackSize": 1, "name": "darkoak_standing_sign", "blockStateId": 4009, "displayName": "Darkoak Standing Sign"}, {"id": 9178, "stackSize": 1, "name": "item.dark_oak_door", "displayName": "Dark Oak Door"}, {"id": 9180, "stackSize": 1, "name": "cyan_candle_cake", "displayName": "<PERSON><PERSON>"}, {"id": 9181, "stackSize": 1, "name": "wall_banner", "displayName": "<PERSON>"}, {"id": 9183, "stackSize": 1, "name": "crimson_standing_sign", "blockStateId": 3842, "displayName": "Crimson Standing Sign"}, {"id": 9186, "stackSize": 1, "name": "deepslate_brick_double_slab", "blockStateId": 4053, "displayName": "Deepslate Brick Double Slab"}, {"id": 9191, "stackSize": 1, "name": "crimson_double_slab", "blockStateId": 3776, "displayName": "Crimson Double Slab"}, {"id": 9194, "stackSize": 1, "name": "coral_fan_hang3", "blockStateId": 3725, "displayName": "Coral Fan Hang3"}, {"id": 9196, "stackSize": 1, "name": "glowingobsidian", "displayName": "Glowingobsidian"}, {"id": 9199, "stackSize": 1, "name": "cobbled_deepslate_double_slab", "blockStateId": 1104, "displayName": "Cobbled Deepslate Double Slab"}, {"id": 9205, "stackSize": 1, "name": "chemical_heat", "displayName": "Chemical Heat"}, {"id": 9207, "stackSize": 1, "name": "item.chain", "displayName": "Chain"}, {"id": 9208, "stackSize": 1, "name": "item.cauldron", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 9209, "stackSize": 1, "name": "carrots", "displayName": "Carrots"}, {"id": 9210, "stackSize": 1, "name": "item.camera", "displayName": "Camera"}, {"id": 9211, "stackSize": 1, "name": "light_gray_candle_cake", "displayName": "Light Gray Candle Cake"}, {"id": 9216, "stackSize": 1, "name": "bubble_column", "blockStateId": 888, "displayName": "Bubble Column"}, {"id": 9221, "stackSize": 1, "name": "border_block", "displayName": "Border Block"}, {"id": 9228, "stackSize": 1, "name": "black_candle_cake", "displayName": "Black Candle Cake"}, {"id": 9229, "stackSize": 1, "name": "birch_wall_sign", "blockStateId": 477, "displayName": "<PERSON> Sign"}, {"id": 9231, "stackSize": 1, "name": "birch_standing_sign", "blockStateId": 455, "displayName": "<PERSON> Standing Sign"}, {"id": 9234, "stackSize": 1, "name": "item.birch_door", "displayName": "<PERSON>"}, {"id": 9235, "stackSize": 1, "name": "item.beetroot", "displayName": "Beetroot"}, {"id": 9246, "stackSize": 1, "name": "item.spruce_door", "displayName": "Spruce Door"}, {"id": 9247, "stackSize": 1, "name": "acacia_standing_sign", "blockStateId": 99, "displayName": "Acacia Standing Sign"}, {"id": 9252, "stackSize": 1, "name": "item.acacia_door", "displayName": "Acacia Door"}, {"id": 9254, "stackSize": 1, "name": "polished_deepslate_double_slab", "blockStateId": 6048, "displayName": "Polished Deepslate Double Slab"}, {"id": 9255, "stackSize": 1, "name": "spawn_egg", "displayName": "Spawn Egg"}, {"id": 9257, "stackSize": 1, "name": "dye", "displayName": "Dye"}, {"id": 9260, "stackSize": 1, "name": "boat", "displayName": "Boat"}, {"id": 9267, "stackSize": 1, "name": "element_118", "blockStateId": 4569, "displayName": "Element 118"}, {"id": 9268, "stackSize": 1, "name": "element_117", "blockStateId": 4568, "displayName": "Element 117"}, {"id": 9269, "stackSize": 1, "name": "element_116", "blockStateId": 4567, "displayName": "Element 116"}, {"id": 9270, "stackSize": 1, "name": "element_115", "blockStateId": 4566, "displayName": "Element 115"}, {"id": 9271, "stackSize": 1, "name": "element_114", "blockStateId": 4565, "displayName": "Element 114"}, {"id": 9272, "stackSize": 1, "name": "element_113", "blockStateId": 4564, "displayName": "Element 113"}, {"id": 9273, "stackSize": 1, "name": "element_112", "blockStateId": 4563, "displayName": "Element 112"}, {"id": 9274, "stackSize": 1, "name": "element_109", "blockStateId": 4559, "displayName": "Element 109"}, {"id": 9276, "stackSize": 1, "name": "element_108", "blockStateId": 4558, "displayName": "Element 108"}, {"id": 9278, "stackSize": 1, "name": "element_103", "blockStateId": 4553, "displayName": "Element 103"}, {"id": 9279, "stackSize": 1, "name": "element_102", "blockStateId": 4552, "displayName": "Element 102"}, {"id": 9280, "stackSize": 1, "name": "element_101", "blockStateId": 4551, "displayName": "Element 101"}, {"id": 9281, "stackSize": 1, "name": "element_100", "blockStateId": 4550, "displayName": "Element 100"}, {"id": 9282, "stackSize": 1, "name": "element_98", "blockStateId": 4664, "displayName": "Element 98"}, {"id": 9283, "stackSize": 1, "name": "element_97", "blockStateId": 4663, "displayName": "Element 97"}, {"id": 9284, "stackSize": 1, "name": "element_96", "blockStateId": 4662, "displayName": "Element 96"}, {"id": 9285, "stackSize": 1, "name": "element_95", "blockStateId": 4661, "displayName": "Element 95"}, {"id": 9286, "stackSize": 1, "name": "element_94", "blockStateId": 4660, "displayName": "Element 94"}, {"id": 9289, "stackSize": 1, "name": "element_93", "blockStateId": 4659, "displayName": "Element 93"}, {"id": 9290, "stackSize": 1, "name": "reserved6", "displayName": "Reserved6"}, {"id": 9291, "stackSize": 1, "name": "element_92", "blockStateId": 4658, "displayName": "Element 92"}, {"id": 9292, "stackSize": 1, "name": "element_90", "blockStateId": 4656, "displayName": "Element 90"}, {"id": 9293, "stackSize": 1, "name": "element_89", "blockStateId": 4654, "displayName": "Element 89"}, {"id": 9294, "stackSize": 1, "name": "element_88", "blockStateId": 4653, "displayName": "Element 88"}, {"id": 9295, "stackSize": 1, "name": "jungle_wall_sign", "blockStateId": 5235, "displayName": "Jungle Wall Sign"}, {"id": 9296, "stackSize": 1, "name": "element_87", "blockStateId": 4652, "displayName": "Element 87"}, {"id": 9297, "stackSize": 1, "name": "element_86", "blockStateId": 4651, "displayName": "Element 86"}, {"id": 9298, "stackSize": 1, "name": "element_85", "blockStateId": 4650, "displayName": "Element 85"}, {"id": 9300, "stackSize": 1, "name": "element_84", "blockStateId": 4649, "displayName": "Element 84"}, {"id": 9301, "stackSize": 1, "name": "element_83", "blockStateId": 4648, "displayName": "Element 83"}, {"id": 9303, "stackSize": 1, "name": "element_81", "blockStateId": 4646, "displayName": "Element 81"}, {"id": 9304, "stackSize": 1, "name": "element_80", "blockStateId": 4645, "displayName": "Element 80"}, {"id": 9305, "stackSize": 1, "name": "element_79", "blockStateId": 4643, "displayName": "Element 79"}, {"id": 9307, "stackSize": 1, "name": "element_78", "blockStateId": 4642, "displayName": "Element 78"}, {"id": 9309, "stackSize": 1, "name": "element_76", "blockStateId": 4640, "displayName": "Element 76"}, {"id": 9310, "stackSize": 1, "name": "element_75", "blockStateId": 4639, "displayName": "Element 75"}, {"id": 9311, "stackSize": 1, "name": "element_74", "blockStateId": 4638, "displayName": "Element 74"}, {"id": 9312, "stackSize": 1, "name": "element_72", "blockStateId": 4636, "displayName": "Element 72"}, {"id": 9313, "stackSize": 1, "name": "element_68", "blockStateId": 4631, "displayName": "Element 68"}, {"id": 9315, "stackSize": 1, "name": "element_67", "blockStateId": 4630, "displayName": "Element 67"}, {"id": 9318, "stackSize": 1, "name": "element_66", "blockStateId": 4629, "displayName": "Element 66"}, {"id": 9319, "stackSize": 1, "name": "element_64", "blockStateId": 4627, "displayName": "Element 64"}, {"id": 9320, "stackSize": 1, "name": "element_61", "blockStateId": 4624, "displayName": "Element 61"}, {"id": 9321, "stackSize": 1, "name": "element_59", "blockStateId": 4621, "displayName": "Element 59"}, {"id": 9322, "stackSize": 1, "name": "element_58", "blockStateId": 4620, "displayName": "Element 58"}, {"id": 9323, "stackSize": 1, "name": "element_57", "blockStateId": 4619, "displayName": "Element 57"}, {"id": 9324, "stackSize": 1, "name": "item.soul_campfire", "displayName": "Soul Campfire"}, {"id": 9325, "stackSize": 1, "name": "element_56", "blockStateId": 4618, "displayName": "Element 56"}, {"id": 9326, "stackSize": 1, "name": "element_52", "blockStateId": 4614, "displayName": "Element 52"}, {"id": 9328, "stackSize": 1, "name": "element_47", "blockStateId": 4608, "displayName": "Element 47"}, {"id": 9331, "stackSize": 1, "name": "element_54", "blockStateId": 4616, "displayName": "Element 54"}, {"id": 9336, "stackSize": 1, "name": "element_33", "blockStateId": 4593, "displayName": "Element 33"}, {"id": 9337, "stackSize": 1, "name": "powder_snow", "blockStateId": 6248, "displayName": "Powder Snow"}, {"id": 9341, "stackSize": 1, "name": "pumpkin_stem", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 9358, "stackSize": 1, "name": "element_48", "blockStateId": 4609, "displayName": "Element 48"}, {"id": 9362, "stackSize": 1, "name": "deepslate_tile_double_slab", "blockStateId": 4236, "displayName": "Deepslate Tile Double Slab"}, {"id": 9365, "stackSize": 1, "name": "lit_redstone_lamp", "displayName": "Lit Redstone Lamp"}, {"id": 9381, "stackSize": 1, "name": "element_27", "blockStateId": 4586, "displayName": "Element 27"}, {"id": 9384, "stackSize": 1, "name": "bleach", "displayName": "Bleach"}, {"id": 9390, "stackSize": 1, "name": "element_70", "blockStateId": 4634, "displayName": "Element 70"}, {"id": 9393, "stackSize": 1, "name": "item.nether_sprouts", "displayName": "Nether Sprouts"}, {"id": 9394, "stackSize": 1, "name": "item.iron_door", "displayName": "Iron Door"}, {"id": 9398, "stackSize": 1, "name": "brewingstandblock", "displayName": "Brewingstandblock"}, {"id": 9400, "stackSize": 1, "name": "element_69", "blockStateId": 4632, "displayName": "Element 69"}, {"id": 9411, "stackSize": 1, "name": "element_62", "blockStateId": 4625, "displayName": "Element 62"}, {"id": 9418, "stackSize": 1, "name": "hard_stained_glass", "displayName": "Hard Stained Glass"}, {"id": 9421, "stackSize": 1, "name": "glow_stick", "displayName": "Glow Stick"}, {"id": 9429, "stackSize": 1, "name": "powered_comparator", "displayName": "Powered Comparator"}, {"id": 9432, "stackSize": 1, "name": "hard_glass_pane", "displayName": "Hard Glass Pane"}, {"id": 9433, "stackSize": 1, "name": "standing_banner", "displayName": "Standing Banner"}, {"id": 9436, "stackSize": 1, "name": "item.campfire", "displayName": "Campfire"}, {"id": 9441, "stackSize": 1, "name": "colored_torch_rg", "displayName": "Colored Torch Rg"}, {"id": 9452, "stackSize": 1, "name": "cave_vines_head_with_berries", "blockStateId": 1050, "displayName": "Cave Vines Head With Berries"}, {"id": 9455, "stackSize": 1, "name": "element_15", "blockStateId": 4573, "displayName": "Element 15"}, {"id": 9458, "stackSize": 1, "name": "agent_spawn_egg", "displayName": "Agent Spawn Egg"}, {"id": 9461, "stackSize": 1, "name": "element_71", "blockStateId": 4635, "displayName": "Element 71"}, {"id": 9469, "stackSize": 1, "name": "white_candle", "displayName": "White Candle"}, {"id": 9471, "stackSize": 1, "name": "element_110", "blockStateId": 4561, "displayName": "Element 110"}, {"id": 9475, "stackSize": 1, "name": "brown_candle_cake", "displayName": "Brown Candle Cake"}, {"id": 9483, "stackSize": 1, "name": "element_55", "blockStateId": 4617, "displayName": "Element 55"}, {"id": 9499, "stackSize": 1, "name": "element_73", "blockStateId": 4637, "displayName": "Element 73"}, {"id": 9507, "stackSize": 1, "name": "element_63", "blockStateId": 4626, "displayName": "Element 63"}, {"id": 9510, "stackSize": 1, "name": "flowing_water", "displayName": "Flowing Water"}, {"id": 9511, "stackSize": 1, "name": "element_111", "blockStateId": 4562, "displayName": "Element 111"}, {"id": 9520, "stackSize": 1, "name": "frosted_ice", "displayName": "Frosted Ice"}, {"id": 9523, "stackSize": 1, "name": "element_82", "blockStateId": 4647, "displayName": "Element 82"}, {"id": 9530, "stackSize": 1, "name": "element_18", "blockStateId": 4576, "displayName": "Element 18"}, {"id": 9534, "stackSize": 1, "name": "chemistry_table", "displayName": "Chemistry Table"}, {"id": 9543, "stackSize": 1, "name": "double_cut_copper_slab", "blockStateId": 4449, "displayName": "Double Cut Copper Slab"}, {"id": 9545, "stackSize": 1, "name": "jungle_standing_sign", "blockStateId": 5213, "displayName": "Jungle Standing Sign"}, {"id": 9546, "stackSize": 1, "name": "element_46", "blockStateId": 4607, "displayName": "Element 46"}, {"id": 9560, "stackSize": 1, "name": "element_105", "blockStateId": 4555, "displayName": "Element 105"}, {"id": 9572, "stackSize": 1, "name": "candle_cake", "displayName": "Candle Cake"}, {"id": 9576, "stackSize": 1, "name": "element_35", "blockStateId": 4595, "displayName": "Element 35"}, {"id": 9580, "stackSize": 1, "name": "waxed_oxidized_double_cut_copper_slab", "blockStateId": 7480, "displayName": "Waxed Oxidized Double Cut Copper Slab"}, {"id": 9581, "stackSize": 1, "name": "unpowered_comparator", "displayName": "Unpowered Comparator"}, {"id": 9582, "stackSize": 1, "name": "item.crimson_door", "displayName": "Crimson Door"}, {"id": 9583, "stackSize": 1, "name": "element_49", "blockStateId": 4610, "displayName": "Element 49"}, {"id": 9585, "stackSize": 1, "name": "item.bed", "displayName": "Bed"}, {"id": 9592, "stackSize": 1, "name": "npc_spawn_egg", "displayName": "Npc Spawn Egg"}, {"id": 9596, "stackSize": 1, "name": "allow", "displayName": "Allow"}, {"id": 9597, "stackSize": 1, "name": "element_106", "blockStateId": 4556, "displayName": "Element 106"}, {"id": 9603, "stackSize": 1, "name": "gray_candle_cake", "displayName": "Gray Candle Cake"}, {"id": 9605, "stackSize": 1, "name": "element_91", "blockStateId": 4657, "displayName": "Element 91"}, {"id": 9609, "stackSize": 1, "name": "magenta_candle", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 9622, "stackSize": 1, "name": "element_104", "blockStateId": 4554, "displayName": "Element 104"}, {"id": 9629, "stackSize": 1, "name": "camera", "displayName": "Camera"}, {"id": 9632, "stackSize": 1, "name": "element_2", "blockStateId": 4578, "displayName": "Element 2"}, {"id": 9633, "stackSize": 1, "name": "lit_redstone_ore", "displayName": "Lit Redstone Ore"}, {"id": 9638, "stackSize": 1, "name": "item.hopper", "displayName": "<PERSON>"}, {"id": 9639, "stackSize": 1, "name": "exposed_double_cut_copper_slab", "blockStateId": 4714, "displayName": "Exposed Double Cut Copper Slab"}, {"id": 9643, "stackSize": 1, "name": "lit_furnace", "displayName": "Lit Furnace"}, {"id": 9650, "stackSize": 1, "name": "item.glow_frame", "displayName": "Glow Frame"}, {"id": 9658, "stackSize": 1, "name": "element_50", "blockStateId": 4612, "displayName": "Element 50"}, {"id": 9661, "stackSize": 1, "name": "element_12", "blockStateId": 4570, "displayName": "Element 12"}, {"id": 9667, "stackSize": 1, "name": "element_25", "blockStateId": 4584, "displayName": "Element 25"}, {"id": 9668, "stackSize": 1, "name": "element_9", "blockStateId": 4655, "displayName": "Element 9"}, {"id": 9670, "stackSize": 1, "name": "element_45", "blockStateId": 4606, "displayName": "Element 45"}, {"id": 9674, "stackSize": 1, "name": "portal", "displayName": "Portal"}, {"id": 9677, "stackSize": 1, "name": "coral_fan_hang", "blockStateId": 3693, "displayName": "Coral Fan Hang"}, {"id": 9683, "stackSize": 1, "name": "element_44", "blockStateId": 4605, "displayName": "Element 44"}, {"id": 9689, "stackSize": 1, "name": "orange_candle", "displayName": "Orange Candle"}, {"id": 9692, "stackSize": 1, "name": "element_37", "blockStateId": 4597, "displayName": "Element 37"}, {"id": 9695, "stackSize": 1, "name": "soul_fire", "blockStateId": 6706, "displayName": "Soul Fire"}, {"id": 9700, "stackSize": 1, "name": "item.jungle_door", "displayName": "Jungle Door"}, {"id": 9702, "stackSize": 1, "name": "blackstone_double_slab", "blockStateId": 486, "displayName": "Blackstone Double Slab"}, {"id": 9705, "stackSize": 1, "name": "netherbrick", "displayName": "Netherbrick"}, {"id": 9708, "stackSize": 1, "name": "stonecutter", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 9710, "stackSize": 1, "name": "element_60", "blockStateId": 4623, "displayName": "Element 60"}, {"id": 9730, "stackSize": 1, "name": "element_5", "blockStateId": 4611, "displayName": "Element 5"}, {"id": 9731, "stackSize": 1, "name": "element_21", "blockStateId": 4580, "displayName": "Element 21"}, {"id": 9735, "stackSize": 1, "name": "goat_horn", "displayName": "<PERSON><PERSON>"}, {"id": 9737, "stackSize": 1, "name": "item.kelp", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 9740, "stackSize": 1, "name": "red_candle", "displayName": "<PERSON> Candle"}, {"id": 9743, "stackSize": 1, "name": "element_77", "blockStateId": 4641, "displayName": "Element 77"}, {"id": 9753, "stackSize": 1, "name": "element_0", "displayName": "Element 0"}, {"id": 9757, "stackSize": 1, "name": "deny", "displayName": "<PERSON><PERSON>"}, {"id": 9758, "stackSize": 1, "name": "candle", "displayName": "Candle"}, {"id": 9760, "stackSize": 1, "name": "item.nether_wart", "displayName": "Nether Wart"}, {"id": 9788, "stackSize": 1, "name": "lava", "displayName": "<PERSON><PERSON>"}, {"id": 9795, "stackSize": 1, "name": "element_42", "blockStateId": 4603, "displayName": "Element 42"}, {"id": 9801, "stackSize": 1, "name": "element_65", "blockStateId": 4628, "displayName": "Element 65"}, {"id": 9805, "stackSize": 1, "name": "blue_candle_cake", "displayName": "Blue Candle Cake"}, {"id": 9810, "stackSize": 1, "name": "element_29", "blockStateId": 4588, "displayName": "Element 29"}, {"id": 9815, "stackSize": 1, "name": "element_53", "blockStateId": 4615, "displayName": "Element 53"}, {"id": 9822, "stackSize": 1, "name": "hard_stained_glass_pane", "displayName": "Hard Stained Glass Pane"}, {"id": 9823, "stackSize": 1, "name": "balloon", "displayName": "Balloon"}, {"id": 9828, "stackSize": 1, "name": "item.wheat", "displayName": "Wheat"}, {"id": 9829, "stackSize": 1, "name": "fire", "displayName": "Fire"}, {"id": 9832, "stackSize": 1, "name": "bordure_indented_banner_pattern", "displayName": "Bordure Indented Banner Pattern"}, {"id": 9833, "stackSize": 1, "name": "red_candle_cake", "displayName": "Red Candle Cake"}, {"id": 9836, "stackSize": 1, "name": "cave_vines", "blockStateId": 998, "displayName": "Cave Vines"}, {"id": 9838, "stackSize": 1, "name": "light_blue_candle", "displayName": "Light Blue Candle"}, {"id": 9842, "stackSize": 1, "name": "element_40", "blockStateId": 4601, "displayName": "Element 40"}, {"id": 9846, "stackSize": 1, "name": "rapid_fertilizer", "displayName": "Rapid Fertilizer"}, {"id": 9864, "stackSize": 1, "name": "field_masoned_banner_pattern", "displayName": "Field Masoned Banner Pattern"}, {"id": 9865, "stackSize": 1, "name": "compound", "displayName": "Compound"}, {"id": 9866, "stackSize": 1, "name": "ice_bomb", "displayName": "Ice Bomb"}, {"id": 9867, "stackSize": 1, "name": "element_99", "blockStateId": 4665, "displayName": "Element 99"}, {"id": 9868, "stackSize": 1, "name": "medicine", "displayName": "Medicine"}, {"id": 9869, "stackSize": 1, "name": "sparkler", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 9871, "stackSize": 1, "name": "netherreactor", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 9874, "stackSize": 1, "name": "lodestone_compass", "displayName": "Lodestone Compass"}, {"id": 9875, "stackSize": 1, "name": "element_51", "blockStateId": 4613, "displayName": "Element 51"}, {"id": 9878, "stackSize": 1, "name": "cave_vines_body_with_berries", "blockStateId": 1024, "displayName": "Cave Vines Body With Berries"}, {"id": 9885, "stackSize": 1, "name": "melon_stem", "displayName": "Melon Stem"}, {"id": 9893, "stackSize": 1, "name": "magenta_candle_cake", "displayName": "Ma<PERSON>a Candle Cake"}, {"id": 9900, "stackSize": 1, "name": "yellow_candle", "displayName": "Yellow Candle"}, {"id": 9903, "stackSize": 1, "name": "element_10", "blockStateId": 4549, "displayName": "Element 10"}, {"id": 9904, "stackSize": 1, "name": "lime_candle", "displayName": "<PERSON><PERSON>"}, {"id": 9905, "stackSize": 1, "name": "cocoa", "displayName": "Cocoa"}, {"id": 9906, "stackSize": 1, "name": "crimson_wall_sign", "blockStateId": 3867, "displayName": "Crimson Wall Sign"}, {"id": 9910, "stackSize": 1, "name": "element_32", "blockStateId": 4592, "displayName": "Element 32"}, {"id": 9913, "stackSize": 1, "name": "invisiblebedrock", "displayName": "Invisiblebedrock"}, {"id": 9919, "stackSize": 1, "name": "real_double_stone_slab2", "displayName": "Real Double Stone Slab2"}, {"id": 9920, "stackSize": 1, "name": "real_double_stone_slab3", "displayName": "Real Double Stone Slab3"}, {"id": 9921, "stackSize": 1, "name": "real_double_stone_slab4", "displayName": "Real Double Stone Slab4"}, {"id": 9922, "stackSize": 1, "name": "item.cake", "displayName": "Cake"}, {"id": 9923, "stackSize": 1, "name": "bamboo_sapling", "blockStateId": 200, "displayName": "Bamboo Sapling"}, {"id": 9932, "stackSize": 1, "name": "element_7", "blockStateId": 4633, "displayName": "Element 7"}, {"id": 9934, "stackSize": 1, "name": "element_23", "blockStateId": 4582, "displayName": "Element 23"}, {"id": 9937, "stackSize": 1, "name": "element_3", "blockStateId": 4589, "displayName": "Element 3"}, {"id": 9938, "stackSize": 1, "name": "orange_candle_cake", "displayName": "Orange Candle Cake"}, {"id": 9942, "stackSize": 1, "name": "brown_candle", "displayName": "<PERSON> Candle"}, {"id": 9947, "stackSize": 1, "name": "wall_sign", "displayName": "Wall Sign"}, {"id": 9950, "stackSize": 1, "name": "real_double_stone_slab", "displayName": "Real Double Stone Slab"}, {"id": 9956, "stackSize": 1, "name": "element_107", "blockStateId": 4557, "displayName": "Element 107"}, {"id": 9959, "stackSize": 1, "name": "flowing_lava", "displayName": "Flowing Lava"}, {"id": 9966, "stackSize": 1, "name": "element_41", "blockStateId": 4602, "displayName": "Element 41"}, {"id": 9972, "stackSize": 1, "name": "colored_torch_bp", "displayName": "Colored Torch Bp"}, {"id": 9973, "stackSize": 1, "name": "element_43", "blockStateId": 4604, "displayName": "Element 43"}, {"id": 9974, "stackSize": 1, "name": "pink_candle", "displayName": "Pink Candle"}, {"id": 9975, "stackSize": 1, "name": "gray_candle", "displayName": "<PERSON>"}, {"id": 9977, "stackSize": 1, "name": "light_gray_candle", "displayName": "Light Gray Candle"}, {"id": 9978, "stackSize": 1, "name": "cyan_candle", "displayName": "<PERSON><PERSON>"}, {"id": 9980, "stackSize": 1, "name": "purple_candle", "displayName": "Purple Candle"}, {"id": 9981, "stackSize": 1, "name": "blue_candle", "displayName": "Blue Candle"}, {"id": 9982, "stackSize": 1, "name": "green_candle", "displayName": "Green Candle"}, {"id": 9984, "stackSize": 1, "name": "black_candle", "displayName": "Black Candle"}, {"id": 9985, "stackSize": 1, "name": "element_1", "blockStateId": 4548, "displayName": "Element 1"}, {"id": 9987, "stackSize": 1, "name": "element_20", "blockStateId": 4579, "displayName": "Element 20"}, {"id": 9988, "stackSize": 1, "name": "element_4", "blockStateId": 4600, "displayName": "Element 4"}, {"id": 9989, "stackSize": 1, "name": "water", "displayName": "Water"}, {"id": 9990, "stackSize": 1, "name": "element_24", "blockStateId": 4583, "displayName": "Element 24"}, {"id": 9991, "stackSize": 1, "name": "element_8", "blockStateId": 4644, "displayName": "Element 8"}, {"id": 9992, "stackSize": 1, "name": "acacia_wall_sign", "blockStateId": 121, "displayName": "Acacia Wall Sign"}, {"id": 9993, "stackSize": 1, "name": "element_11", "blockStateId": 4560, "displayName": "Element 11"}, {"id": 9995, "stackSize": 1, "name": "element_13", "blockStateId": 4571, "displayName": "Element 13"}, {"id": 9996, "stackSize": 1, "name": "element_14", "blockStateId": 4572, "displayName": "Element 14"}, {"id": 9997, "stackSize": 1, "name": "redstone_wire", "displayName": "Redstone Wire"}, {"id": 9998, "stackSize": 1, "name": "element_16", "blockStateId": 4574, "displayName": "Element 16"}, {"id": 9999, "stackSize": 1, "name": "element_17", "blockStateId": 4575, "displayName": "Element 17"}, {"id": 10000, "stackSize": 1, "name": "element_19", "blockStateId": 4577, "displayName": "Element 19"}, {"id": 10003, "stackSize": 1, "name": "element_6", "blockStateId": 4622, "displayName": "Element 6"}, {"id": 10004, "stackSize": 1, "name": "element_22", "blockStateId": 4581, "displayName": "Element 22"}, {"id": 10005, "stackSize": 1, "name": "coral_fan_hang2", "blockStateId": 3709, "displayName": "Coral Fan Hang2"}, {"id": 10007, "stackSize": 1, "name": "element_26", "blockStateId": 4585, "displayName": "Element 26"}, {"id": 10008, "stackSize": 1, "name": "pink_candle_cake", "displayName": "Pink Candle Cake"}, {"id": 10009, "stackSize": 1, "name": "element_28", "blockStateId": 4587, "displayName": "Element 28"}, {"id": 10011, "stackSize": 1, "name": "element_30", "blockStateId": 4590, "displayName": "Element 30"}, {"id": 10012, "stackSize": 1, "name": "element_31", "blockStateId": 4591, "displayName": "Element 31"}, {"id": 10013, "stackSize": 1, "name": "element_34", "blockStateId": 4594, "displayName": "Element 34"}, {"id": 10015, "stackSize": 1, "name": "element_36", "blockStateId": 4596, "displayName": "Element 36"}, {"id": 10017, "stackSize": 1, "name": "element_38", "blockStateId": 4598, "displayName": "Element 38"}, {"id": 10018, "stackSize": 1, "name": "element_39", "blockStateId": 4599, "displayName": "Element 39"}]