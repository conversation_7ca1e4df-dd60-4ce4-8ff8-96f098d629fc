[{"id": 1, "displayName": "Stone", "name": "stone", "stackSize": 64}, {"id": 2, "displayName": "Granite", "name": "granite", "stackSize": 64}, {"id": 3, "displayName": "Polished Granite", "name": "polished_granite", "stackSize": 64}, {"id": 4, "displayName": "Diorite", "name": "diorite", "stackSize": 64}, {"id": 5, "displayName": "Polished Diorite", "name": "polished_diorite", "stackSize": 64}, {"id": 6, "displayName": "Andesite", "name": "andesite", "stackSize": 64}, {"id": 7, "displayName": "Polished Andesite", "name": "polished_andesite", "stackSize": 64}, {"id": 8, "displayName": "Deepslate", "name": "deepslate", "stackSize": 64}, {"id": 9, "displayName": "Cobbled Deepslate", "name": "cobbled_deepslate", "stackSize": 64}, {"id": 10, "displayName": "Polished Deepslate", "name": "polished_deepslate", "stackSize": 64}, {"id": 11, "displayName": "Calcite", "name": "calcite", "stackSize": 64}, {"id": 12, "displayName": "<PERSON><PERSON>", "name": "tuff", "stackSize": 64}, {"id": 13, "displayName": "Dripstone Block", "name": "dripstone_block", "stackSize": 64}, {"id": 14, "displayName": "Grass Block", "name": "grass_block", "stackSize": 64}, {"id": 15, "displayName": "Dirt", "name": "dirt", "stackSize": 64}, {"id": 16, "displayName": "Coarse Dirt", "name": "coarse_dirt", "stackSize": 64}, {"id": 17, "displayName": "Podzol", "name": "podzol", "stackSize": 64}, {"id": 18, "displayName": "Rooted Dirt", "name": "rooted_dirt", "stackSize": 64}, {"id": 19, "displayName": "Crimson Nylium", "name": "crimson_nylium", "stackSize": 64}, {"id": 20, "displayName": "Warped Nylium", "name": "warped_nylium", "stackSize": 64}, {"id": 21, "displayName": "Cobblestone", "name": "cobblestone", "stackSize": 64}, {"id": 22, "displayName": "Oak Planks", "name": "oak_planks", "stackSize": 64}, {"id": 23, "displayName": "Spruce Planks", "name": "spruce_planks", "stackSize": 64}, {"id": 24, "displayName": "Birch Planks", "name": "birch_planks", "stackSize": 64}, {"id": 25, "displayName": "Jungle Planks", "name": "jungle_planks", "stackSize": 64}, {"id": 26, "displayName": "Acacia Planks", "name": "acacia_planks", "stackSize": 64}, {"id": 27, "displayName": "Dark Oak Planks", "name": "dark_oak_planks", "stackSize": 64}, {"id": 28, "displayName": "Crimson Planks", "name": "crimson_planks", "stackSize": 64}, {"id": 29, "displayName": "Warped Planks", "name": "warped_planks", "stackSize": 64}, {"id": 30, "displayName": "Oak Sapling", "name": "oak_sapling", "stackSize": 64}, {"id": 31, "displayName": "Spruce Sapling", "name": "spruce_sapling", "stackSize": 64}, {"id": 32, "displayName": "Birch Sapling", "name": "birch_sapling", "stackSize": 64}, {"id": 33, "displayName": "Jungle Sapling", "name": "jungle_sapling", "stackSize": 64}, {"id": 34, "displayName": "Acacia Sapling", "name": "acacia_sapling", "stackSize": 64}, {"id": 35, "displayName": "Dark Oak Sapling", "name": "dark_oak_sapling", "stackSize": 64}, {"id": 36, "displayName": "Bedrock", "name": "bedrock", "stackSize": 64}, {"id": 37, "displayName": "Sand", "name": "sand", "stackSize": 64}, {"id": 38, "displayName": "Red Sand", "name": "red_sand", "stackSize": 64}, {"id": 39, "displayName": "<PERSON>l", "name": "gravel", "stackSize": 64}, {"id": 40, "displayName": "Coal Ore", "name": "coal_ore", "stackSize": 64}, {"id": 41, "displayName": "Deepslate Coal Ore", "name": "deepslate_coal_ore", "stackSize": 64}, {"id": 42, "displayName": "Iron Ore", "name": "iron_ore", "stackSize": 64}, {"id": 43, "displayName": "Deepslate Iron Ore", "name": "deepslate_iron_ore", "stackSize": 64}, {"id": 44, "displayName": "Copper Ore", "name": "copper_ore", "stackSize": 64}, {"id": 45, "displayName": "Deepslate Copper Ore", "name": "deepslate_copper_ore", "stackSize": 64}, {"id": 46, "displayName": "Gold Ore", "name": "gold_ore", "stackSize": 64}, {"id": 47, "displayName": "Deepslate Gold Ore", "name": "deepslate_gold_ore", "stackSize": 64}, {"id": 48, "displayName": "Redstone Ore", "name": "redstone_ore", "stackSize": 64}, {"id": 49, "displayName": "Deepslate Redstone Ore", "name": "deepslate_redstone_ore", "stackSize": 64}, {"id": 50, "displayName": "Emerald Ore", "name": "emerald_ore", "stackSize": 64}, {"id": 51, "displayName": "Deepslate Emerald Ore", "name": "deepslate_emerald_ore", "stackSize": 64}, {"id": 52, "displayName": "Lapis <PERSON> Ore", "name": "lapis_ore", "stackSize": 64}, {"id": 53, "displayName": "Deepslate Lapis Lazuli Ore", "name": "deepslate_lapis_ore", "stackSize": 64}, {"id": 54, "displayName": "Diamond Ore", "name": "diamond_ore", "stackSize": 64}, {"id": 55, "displayName": "Deepslate Diamond Ore", "name": "deepslate_diamond_ore", "stackSize": 64}, {"id": 56, "displayName": "Nether Gold Ore", "name": "nether_gold_ore", "stackSize": 64}, {"id": 57, "displayName": "<PERSON><PERSON>", "name": "nether_quartz_ore", "stackSize": 64}, {"id": 58, "displayName": "Ancient Debris", "name": "ancient_debris", "stackSize": 64}, {"id": 59, "displayName": "Block of Coal", "name": "coal_block", "stackSize": 64}, {"id": 60, "displayName": "Block of Raw Iron", "name": "raw_iron_block", "stackSize": 64}, {"id": 61, "displayName": "Block of Raw Copper", "name": "raw_copper_block", "stackSize": 64}, {"id": 62, "displayName": "Block of Raw Gold", "name": "raw_gold_block", "stackSize": 64}, {"id": 63, "displayName": "Block of Amethyst", "name": "amethyst_block", "stackSize": 64}, {"id": 64, "displayName": "Budding Amethyst", "name": "budding_amethyst", "stackSize": 64}, {"id": 65, "displayName": "Block of Iron", "name": "iron_block", "stackSize": 64}, {"id": 66, "displayName": "Block of Copper", "name": "copper_block", "stackSize": 64}, {"id": 67, "displayName": "Block of Gold", "name": "gold_block", "stackSize": 64}, {"id": 68, "displayName": "Block of Diamond", "name": "diamond_block", "stackSize": 64}, {"id": 69, "displayName": "Block of Netherite", "name": "netherite_block", "stackSize": 64}, {"id": 70, "displayName": "Exposed Copper", "name": "exposed_copper", "stackSize": 64}, {"id": 71, "displayName": "Weathered Copper", "name": "weathered_copper", "stackSize": 64}, {"id": 72, "displayName": "Oxidized Copper", "name": "oxidized_copper", "stackSize": 64}, {"id": 73, "displayName": "Cut Copper", "name": "cut_copper", "stackSize": 64}, {"id": 74, "displayName": "Exposed Cut Copper", "name": "exposed_cut_copper", "stackSize": 64}, {"id": 75, "displayName": "Weathered Cut Copper", "name": "weathered_cut_copper", "stackSize": 64}, {"id": 76, "displayName": "Oxidized Cut Copper", "name": "oxidized_cut_copper", "stackSize": 64}, {"id": 77, "displayName": "Cut Copper Stairs", "name": "cut_copper_stairs", "stackSize": 64}, {"id": 78, "displayName": "Exposed Cut Copper Stairs", "name": "exposed_cut_copper_stairs", "stackSize": 64}, {"id": 79, "displayName": "Weathered Cut Copper Stairs", "name": "weathered_cut_copper_stairs", "stackSize": 64}, {"id": 80, "displayName": "Oxidized Cut Copper Stairs", "name": "oxidized_cut_copper_stairs", "stackSize": 64}, {"id": 81, "displayName": "Cut Copper Slab", "name": "cut_copper_slab", "stackSize": 64}, {"id": 82, "displayName": "Exposed Cut Copper Slab", "name": "exposed_cut_copper_slab", "stackSize": 64}, {"id": 83, "displayName": "Weathered Cut Copper Slab", "name": "weathered_cut_copper_slab", "stackSize": 64}, {"id": 84, "displayName": "Oxidized Cut Copper Slab", "name": "oxidized_cut_copper_slab", "stackSize": 64}, {"id": 85, "displayName": "Waxed Block of Copper", "name": "waxed_copper_block", "stackSize": 64}, {"id": 86, "displayName": "Waxed Exposed Copper", "name": "waxed_exposed_copper", "stackSize": 64}, {"id": 87, "displayName": "Waxed Weathered Copper", "name": "waxed_weathered_copper", "stackSize": 64}, {"id": 88, "displayName": "Waxed Oxidized Copper", "name": "waxed_oxidized_copper", "stackSize": 64}, {"id": 89, "displayName": "Waxed Cut Copper", "name": "waxed_cut_copper", "stackSize": 64}, {"id": 90, "displayName": "Waxed Exposed Cut Copper", "name": "waxed_exposed_cut_copper", "stackSize": 64}, {"id": 91, "displayName": "Waxed Weathered Cut Copper", "name": "waxed_weathered_cut_copper", "stackSize": 64}, {"id": 92, "displayName": "Waxed Oxidized Cut Copper", "name": "waxed_oxidized_cut_copper", "stackSize": 64}, {"id": 93, "displayName": "Waxed Cut Copper Stairs", "name": "waxed_cut_copper_stairs", "stackSize": 64}, {"id": 94, "displayName": "Waxed Exposed Cut Copper Stairs", "name": "waxed_exposed_cut_copper_stairs", "stackSize": 64}, {"id": 95, "displayName": "Waxed Weathered Cut Copper Stairs", "name": "waxed_weathered_cut_copper_stairs", "stackSize": 64}, {"id": 96, "displayName": "Waxed Oxidized Cut Copper Stairs", "name": "waxed_oxidized_cut_copper_stairs", "stackSize": 64}, {"id": 97, "displayName": "Waxed Cut Copper Slab", "name": "waxed_cut_copper_slab", "stackSize": 64}, {"id": 98, "displayName": "Waxed Exposed Cut Copper Slab", "name": "waxed_exposed_cut_copper_slab", "stackSize": 64}, {"id": 99, "displayName": "Waxed Weathered Cut Copper Slab", "name": "waxed_weathered_cut_copper_slab", "stackSize": 64}, {"id": 100, "displayName": "Waxed Oxidized Cut Copper Slab", "name": "waxed_oxidized_cut_copper_slab", "stackSize": 64}, {"id": 101, "displayName": "Oak Log", "name": "oak_log", "stackSize": 64}, {"id": 102, "displayName": "Spruce Log", "name": "spruce_log", "stackSize": 64}, {"id": 103, "displayName": "Birch Log", "name": "birch_log", "stackSize": 64}, {"id": 104, "displayName": "Jungle Log", "name": "jungle_log", "stackSize": 64}, {"id": 105, "displayName": "Acacia Log", "name": "acacia_log", "stackSize": 64}, {"id": 106, "displayName": "Dark Oak Log", "name": "dark_oak_log", "stackSize": 64}, {"id": 107, "displayName": "Crimson Stem", "name": "crimson_stem", "stackSize": 64}, {"id": 108, "displayName": "Warped Stem", "name": "warped_stem", "stackSize": 64}, {"id": 109, "displayName": "Stripped Oak Log", "name": "stripped_oak_log", "stackSize": 64}, {"id": 110, "displayName": "Stripped Spruce Log", "name": "stripped_spruce_log", "stackSize": 64}, {"id": 111, "displayName": "Stripped Birch Log", "name": "stripped_birch_log", "stackSize": 64}, {"id": 112, "displayName": "Stripped Jungle Log", "name": "stripped_jungle_log", "stackSize": 64}, {"id": 113, "displayName": "Stripped Acacia Log", "name": "stripped_acacia_log", "stackSize": 64}, {"id": 114, "displayName": "Stripped Dark Oak Log", "name": "stripped_dark_oak_log", "stackSize": 64}, {"id": 115, "displayName": "Stripped Crimson Stem", "name": "stripped_crimson_stem", "stackSize": 64}, {"id": 116, "displayName": "Stripped Warped Stem", "name": "stripped_warped_stem", "stackSize": 64}, {"id": 117, "displayName": "Stripped Oak Wood", "name": "stripped_oak_wood", "stackSize": 64}, {"id": 118, "displayName": "Stripped Spruce Wood", "name": "stripped_spruce_wood", "stackSize": 64}, {"id": 119, "displayName": "Stripped Birch Wood", "name": "stripped_birch_wood", "stackSize": 64}, {"id": 120, "displayName": "Stripped Jungle Wood", "name": "stripped_jungle_wood", "stackSize": 64}, {"id": 121, "displayName": "Stripped Acacia Wood", "name": "stripped_acacia_wood", "stackSize": 64}, {"id": 122, "displayName": "Stripped Dark Oak Wood", "name": "stripped_dark_oak_wood", "stackSize": 64}, {"id": 123, "displayName": "Stripped Crimson Hyphae", "name": "stripped_crimson_hyphae", "stackSize": 64}, {"id": 124, "displayName": "Stripped Warped Hyphae", "name": "stripped_warped_hyphae", "stackSize": 64}, {"id": 125, "displayName": "Oak Wood", "name": "oak_wood", "stackSize": 64}, {"id": 126, "displayName": "Spruce Wood", "name": "spruce_wood", "stackSize": 64}, {"id": 127, "displayName": "Birch Wood", "name": "birch_wood", "stackSize": 64}, {"id": 128, "displayName": "Jungle Wood", "name": "jungle_wood", "stackSize": 64}, {"id": 129, "displayName": "Acacia Wood", "name": "acacia_wood", "stackSize": 64}, {"id": 130, "displayName": "Dark Oak Wood", "name": "dark_oak_wood", "stackSize": 64}, {"id": 131, "displayName": "Crimson Hyphae", "name": "crimson_hyphae", "stackSize": 64}, {"id": 132, "displayName": "Warped Hyphae", "name": "warped_hyphae", "stackSize": 64}, {"id": 133, "displayName": "Oak Leaves", "name": "oak_leaves", "stackSize": 64}, {"id": 134, "displayName": "Spruce Leaves", "name": "spruce_leaves", "stackSize": 64}, {"id": 135, "displayName": "Birch Leaves", "name": "birch_leaves", "stackSize": 64}, {"id": 136, "displayName": "Jungle Leaves", "name": "jungle_leaves", "stackSize": 64}, {"id": 137, "displayName": "Acacia Leaves", "name": "acacia_leaves", "stackSize": 64}, {"id": 138, "displayName": "Dark Oak Leaves", "name": "dark_oak_leaves", "stackSize": 64}, {"id": 139, "displayName": "Azalea Leaves", "name": "azalea_leaves", "stackSize": 64}, {"id": 140, "displayName": "Flowering Azalea Leaves", "name": "flowering_azalea_leaves", "stackSize": 64}, {"id": 141, "displayName": "Sponge", "name": "sponge", "stackSize": 64}, {"id": 142, "displayName": "Wet Sponge", "name": "wet_sponge", "stackSize": 64}, {"id": 143, "displayName": "Glass", "name": "glass", "stackSize": 64}, {"id": 144, "displayName": "Tinted Glass", "name": "tinted_glass", "stackSize": 64}, {"id": 145, "displayName": "Block of Lapis Lazuli", "name": "lapis_block", "stackSize": 64}, {"id": 146, "displayName": "Sandstone", "name": "sandstone", "stackSize": 64}, {"id": 147, "displayName": "Chiseled Sandstone", "name": "chiseled_sandstone", "stackSize": 64}, {"id": 148, "displayName": "Cut Sandstone", "name": "cut_sandstone", "stackSize": 64}, {"id": 149, "displayName": "Cobweb", "name": "cobweb", "stackSize": 64}, {"id": 150, "displayName": "Grass", "name": "grass", "stackSize": 64}, {"id": 151, "displayName": "Fern", "name": "fern", "stackSize": 64}, {"id": 152, "displayName": "Azalea", "name": "azalea", "stackSize": 64}, {"id": 153, "displayName": "Flowering Azalea", "name": "flowering_azalea", "stackSize": 64}, {"id": 154, "displayName": "Dead Bush", "name": "dead_bush", "stackSize": 64}, {"id": 155, "displayName": "Seagrass", "name": "seagrass", "stackSize": 64}, {"id": 156, "displayName": "<PERSON>", "name": "sea_pickle", "stackSize": 64}, {"id": 157, "displayName": "White Wool", "name": "white_wool", "stackSize": 64}, {"id": 158, "displayName": "Orange Wool", "name": "orange_wool", "stackSize": 64}, {"id": 159, "displayName": "Magenta Wool", "name": "magenta_wool", "stackSize": 64}, {"id": 160, "displayName": "Light Blue Wool", "name": "light_blue_wool", "stackSize": 64}, {"id": 161, "displayName": "Yellow Wool", "name": "yellow_wool", "stackSize": 64}, {"id": 162, "displayName": "Lime Wool", "name": "lime_wool", "stackSize": 64}, {"id": 163, "displayName": "Pink Wool", "name": "pink_wool", "stackSize": 64}, {"id": 164, "displayName": "Gray <PERSON>", "name": "gray_wool", "stackSize": 64}, {"id": 165, "displayName": "Light Gray Wool", "name": "light_gray_wool", "stackSize": 64}, {"id": 166, "displayName": "<PERSON><PERSON>", "name": "cyan_wool", "stackSize": 64}, {"id": 167, "displayName": "Purple Wool", "name": "purple_wool", "stackSize": 64}, {"id": 168, "displayName": "Blue Wool", "name": "blue_wool", "stackSize": 64}, {"id": 169, "displayName": "Brown Wool", "name": "brown_wool", "stackSize": 64}, {"id": 170, "displayName": "Green Wool", "name": "green_wool", "stackSize": 64}, {"id": 171, "displayName": "Red Wool", "name": "red_wool", "stackSize": 64}, {"id": 172, "displayName": "Black Wool", "name": "black_wool", "stackSize": 64}, {"id": 173, "displayName": "Dandelion", "name": "dandelion", "stackSize": 64}, {"id": 174, "displayName": "<PERSON><PERSON>", "name": "poppy", "stackSize": 64}, {"id": 175, "displayName": "Blue Orchid", "name": "blue_orchid", "stackSize": 64}, {"id": 176, "displayName": "Allium", "name": "allium", "stackSize": 64}, {"id": 177, "displayName": "Azure Bluet", "name": "azure_bluet", "stackSize": 64}, {"id": 178, "displayName": "<PERSON>lip", "name": "red_tulip", "stackSize": 64}, {"id": 179, "displayName": "Orange Tulip", "name": "orange_tulip", "stackSize": 64}, {"id": 180, "displayName": "White Tulip", "name": "white_tulip", "stackSize": 64}, {"id": 181, "displayName": "<PERSON> Tulip", "name": "pink_tulip", "stackSize": 64}, {"id": 182, "displayName": "Oxeye Daisy", "name": "oxeye_daisy", "stackSize": 64}, {"id": 183, "displayName": "Corn<PERSON>", "name": "cornflower", "stackSize": 64}, {"id": 184, "displayName": "Lily of the Valley", "name": "lily_of_the_valley", "stackSize": 64}, {"id": 185, "displayName": "<PERSON><PERSON>", "name": "wither_rose", "stackSize": 64}, {"id": 186, "displayName": "Spore Blossom", "name": "spore_blossom", "stackSize": 64}, {"id": 187, "displayName": "Brown Mushroom", "name": "brown_mushroom", "stackSize": 64}, {"id": 188, "displayName": "Red Mushroom", "name": "red_mushroom", "stackSize": 64}, {"id": 189, "displayName": "Crimson Fungus", "name": "crimson_fungus", "stackSize": 64}, {"id": 190, "displayName": "Warped Fungus", "name": "warped_fungus", "stackSize": 64}, {"id": 191, "displayName": "Crimson Roots", "name": "crimson_roots", "stackSize": 64}, {"id": 192, "displayName": "Warped Roots", "name": "warped_roots", "stackSize": 64}, {"id": 193, "displayName": "Nether Sprouts", "name": "nether_sprouts", "stackSize": 64}, {"id": 194, "displayName": "Weeping Vines", "name": "weeping_vines", "stackSize": 64}, {"id": 195, "displayName": "Twisting Vines", "name": "twisting_vines", "stackSize": 64}, {"id": 196, "displayName": "Sugar Cane", "name": "sugar_cane", "stackSize": 64}, {"id": 197, "displayName": "<PERSON><PERSON><PERSON>", "name": "kelp", "stackSize": 64}, {"id": 198, "displayName": "Moss Carpet", "name": "moss_carpet", "stackSize": 64}, {"id": 199, "displayName": "Moss Block", "name": "moss_block", "stackSize": 64}, {"id": 200, "displayName": "Hanging Roots", "name": "hanging_roots", "stackSize": 64}, {"id": 201, "displayName": "Big Dripleaf", "name": "big_dripleaf", "stackSize": 64}, {"id": 202, "displayName": "Small Dripleaf", "name": "small_dripleaf", "stackSize": 64}, {"id": 203, "displayName": "Bamboo", "name": "bamboo", "stackSize": 64}, {"id": 204, "displayName": "Oak Slab", "name": "oak_slab", "stackSize": 64}, {"id": 205, "displayName": "Spruce Slab", "name": "spruce_slab", "stackSize": 64}, {"id": 206, "displayName": "<PERSON>", "name": "birch_slab", "stackSize": 64}, {"id": 207, "displayName": "Jungle Slab", "name": "jungle_slab", "stackSize": 64}, {"id": 208, "displayName": "Acacia <PERSON>b", "name": "acacia_slab", "stackSize": 64}, {"id": 209, "displayName": "Dark Oak Slab", "name": "dark_oak_slab", "stackSize": 64}, {"id": 210, "displayName": "Crimson Slab", "name": "crimson_slab", "stackSize": 64}, {"id": 211, "displayName": "Warped Slab", "name": "warped_slab", "stackSize": 64}, {"id": 212, "displayName": "<PERSON> Slab", "name": "stone_slab", "stackSize": 64}, {"id": 213, "displayName": "Smooth Stone Slab", "name": "smooth_stone_slab", "stackSize": 64}, {"id": 214, "displayName": "Sandstone Slab", "name": "sandstone_slab", "stackSize": 64}, {"id": 215, "displayName": "Cut Sandstone Slab", "name": "cut_sandstone_slab", "stackSize": 64}, {"id": 216, "displayName": "Petrified Oak Slab", "name": "petrified_oak_slab", "stackSize": 64}, {"id": 217, "displayName": "Cobblestone Slab", "name": "cobblestone_slab", "stackSize": 64}, {"id": 218, "displayName": "Brick Slab", "name": "brick_slab", "stackSize": 64}, {"id": 219, "displayName": "Stone Brick Slab", "name": "stone_brick_slab", "stackSize": 64}, {"id": 220, "displayName": "Nether Brick Slab", "name": "nether_brick_slab", "stackSize": 64}, {"id": 221, "displayName": "Quartz Slab", "name": "quartz_slab", "stackSize": 64}, {"id": 222, "displayName": "Red Sandstone Slab", "name": "red_sandstone_slab", "stackSize": 64}, {"id": 223, "displayName": "Cut Red Sandstone Slab", "name": "cut_red_sandstone_slab", "stackSize": 64}, {"id": 224, "displayName": "Purpur Slab", "name": "purpur_slab", "stackSize": 64}, {"id": 225, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "prismarine_slab", "stackSize": 64}, {"id": 226, "displayName": "Prismarine Brick Slab", "name": "prismarine_brick_slab", "stackSize": 64}, {"id": 227, "displayName": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "name": "dark_prismarine_slab", "stackSize": 64}, {"id": 228, "displayName": "Smooth Quartz Block", "name": "smooth_quartz", "stackSize": 64}, {"id": 229, "displayName": "Smooth Red Sandstone", "name": "smooth_red_sandstone", "stackSize": 64}, {"id": 230, "displayName": "Smooth Sandstone", "name": "smooth_sandstone", "stackSize": 64}, {"id": 231, "displayName": "Smooth Stone", "name": "smooth_stone", "stackSize": 64}, {"id": 232, "displayName": "Bricks", "name": "bricks", "stackSize": 64}, {"id": 233, "displayName": "Bookshelf", "name": "bookshelf", "stackSize": 64}, {"id": 234, "displayName": "<PERSON><PERSON>", "name": "mossy_cobblestone", "stackSize": 64}, {"id": 235, "displayName": "Obsidian", "name": "obsidian", "stackSize": 64}, {"id": 236, "displayName": "<PERSON>ch", "name": "torch", "stackSize": 64}, {"id": 237, "displayName": "End Rod", "name": "end_rod", "stackSize": 64}, {"id": 238, "displayName": "Chorus Plant", "name": "chorus_plant", "stackSize": 64}, {"id": 239, "displayName": "Chorus Flower", "name": "chorus_flower", "stackSize": 64}, {"id": 240, "displayName": "Purpur Block", "name": "purpur_block", "stackSize": 64}, {"id": 241, "displayName": "Purpur Pillar", "name": "purpur_pillar", "stackSize": 64}, {"id": 242, "displayName": "Purpur Stairs", "name": "purpur_stairs", "stackSize": 64}, {"id": 243, "displayName": "Spawner", "name": "spawner", "stackSize": 64}, {"id": 244, "displayName": "Oak Stairs", "name": "oak_stairs", "stackSize": 64}, {"id": 245, "displayName": "Chest", "name": "chest", "stackSize": 64}, {"id": 246, "displayName": "Crafting Table", "name": "crafting_table", "stackSize": 64}, {"id": 247, "displayName": "Farmland", "name": "farmland", "stackSize": 64}, {"id": 248, "displayName": "Furnace", "name": "furnace", "stackSize": 64}, {"id": 249, "displayName": "Ladder", "name": "ladder", "stackSize": 64}, {"id": 250, "displayName": "Cobblestone Stairs", "name": "cobblestone_stairs", "stackSize": 64}, {"id": 251, "displayName": "Snow", "name": "snow", "stackSize": 64}, {"id": 252, "displayName": "Ice", "name": "ice", "stackSize": 64}, {"id": 253, "displayName": "Snow Block", "name": "snow_block", "stackSize": 64}, {"id": 254, "displayName": "Cactus", "name": "cactus", "stackSize": 64}, {"id": 255, "displayName": "<PERSON>", "name": "clay", "stackSize": 64}, {"id": 256, "displayName": "Jukebox", "name": "jukebox", "stackSize": 64}, {"id": 257, "displayName": "Oak Fence", "name": "oak_fence", "stackSize": 64}, {"id": 258, "displayName": "Spruce Fence", "name": "spruce_fence", "stackSize": 64}, {"id": 259, "displayName": "<PERSON>", "name": "birch_fence", "stackSize": 64}, {"id": 260, "displayName": "Jungle Fence", "name": "jungle_fence", "stackSize": 64}, {"id": 261, "displayName": "Acacia Fence", "name": "acacia_fence", "stackSize": 64}, {"id": 262, "displayName": "Dark Oak Fence", "name": "dark_oak_fence", "stackSize": 64}, {"id": 263, "displayName": "<PERSON> Fence", "name": "crimson_fence", "stackSize": 64}, {"id": 264, "displayName": "Warped <PERSON>", "name": "warped_fence", "stackSize": 64}, {"id": 265, "displayName": "<PERSON><PERSON><PERSON>", "name": "pumpkin", "stackSize": 64}, {"id": 266, "displayName": "<PERSON><PERSON>", "name": "carved_pumpkin", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"id": 267, "displayName": "<PERSON>'<PERSON>", "name": "jack_o_lantern", "stackSize": 64}, {"id": 268, "displayName": "Netherrack", "name": "netherrack", "stackSize": 64}, {"id": 269, "displayName": "Soul Sand", "name": "soul_sand", "stackSize": 64}, {"id": 270, "displayName": "Soul Soil", "name": "soul_soil", "stackSize": 64}, {"id": 271, "displayName": "Basalt", "name": "basalt", "stackSize": 64}, {"id": 272, "displayName": "Polished Ba<PERSON>t", "name": "polished_basalt", "stackSize": 64}, {"id": 273, "displayName": "Smooth Basalt", "name": "smooth_basalt", "stackSize": 64}, {"id": 274, "displayName": "Soul Torch", "name": "soul_torch", "stackSize": 64}, {"id": 275, "displayName": "Glowstone", "name": "glowstone", "stackSize": 64}, {"id": 276, "displayName": "Infested Stone", "name": "infested_stone", "stackSize": 64}, {"id": 277, "displayName": "Infested Cobblestone", "name": "infested_cobblestone", "stackSize": 64}, {"id": 278, "displayName": "Infested Stone Bricks", "name": "infested_stone_bricks", "stackSize": 64}, {"id": 279, "displayName": "Infested Mossy Stone Bricks", "name": "infested_mossy_stone_bricks", "stackSize": 64}, {"id": 280, "displayName": "Infested Cracked Stone Bricks", "name": "infested_cracked_stone_bricks", "stackSize": 64}, {"id": 281, "displayName": "Infested Chiseled Stone Bricks", "name": "infested_chiseled_stone_bricks", "stackSize": 64}, {"id": 282, "displayName": "Infested Deepslate", "name": "infested_deepslate", "stackSize": 64}, {"id": 283, "displayName": "Stone Bricks", "name": "stone_bricks", "stackSize": 64}, {"id": 284, "displayName": "Mossy Stone Bricks", "name": "mossy_stone_bricks", "stackSize": 64}, {"id": 285, "displayName": "Cracked Stone Bricks", "name": "cracked_stone_bricks", "stackSize": 64}, {"id": 286, "displayName": "Chiseled Stone Bricks", "name": "chiseled_stone_bricks", "stackSize": 64}, {"id": 287, "displayName": "Deepslate Bricks", "name": "deepslate_bricks", "stackSize": 64}, {"id": 288, "displayName": "Cracked Deepslate Bricks", "name": "cracked_deepslate_bricks", "stackSize": 64}, {"id": 289, "displayName": "Deepslate Tiles", "name": "deepslate_tiles", "stackSize": 64}, {"id": 290, "displayName": "Cracked Deepslate Tiles", "name": "cracked_deepslate_tiles", "stackSize": 64}, {"id": 291, "displayName": "Chiseled Deepslate", "name": "chiseled_deepslate", "stackSize": 64}, {"id": 292, "displayName": "Brown Mushroom Block", "name": "brown_mushroom_block", "stackSize": 64}, {"id": 293, "displayName": "Red Mushroom Block", "name": "red_mushroom_block", "stackSize": 64}, {"id": 294, "displayName": "Mushroom Stem", "name": "mushroom_stem", "stackSize": 64}, {"id": 295, "displayName": "Iron Bars", "name": "iron_bars", "stackSize": 64}, {"id": 296, "displayName": "Chain", "name": "chain", "stackSize": 64}, {"id": 297, "displayName": "Glass Pane", "name": "glass_pane", "stackSize": 64}, {"id": 298, "displayName": "Melon", "name": "melon", "stackSize": 64}, {"id": 299, "displayName": "Vines", "name": "vine", "stackSize": 64}, {"id": 300, "displayName": "Glow Lichen", "name": "glow_lichen", "stackSize": 64}, {"id": 301, "displayName": "Brick Stairs", "name": "brick_stairs", "stackSize": 64}, {"id": 302, "displayName": "Stone Brick Stairs", "name": "stone_brick_stairs", "stackSize": 64}, {"id": 303, "displayName": "Mycelium", "name": "mycelium", "stackSize": 64}, {"id": 304, "displayName": "<PERSON>", "name": "lily_pad", "stackSize": 64}, {"id": 305, "displayName": "Nether Bricks", "name": "nether_bricks", "stackSize": 64}, {"id": 306, "displayName": "Cracked Nether Bricks", "name": "cracked_nether_bricks", "stackSize": 64}, {"id": 307, "displayName": "Chiseled Nether Bricks", "name": "chiseled_nether_bricks", "stackSize": 64}, {"id": 308, "displayName": "Nether Brick Fence", "name": "nether_brick_fence", "stackSize": 64}, {"id": 309, "displayName": "Nether Brick Stairs", "name": "nether_brick_stairs", "stackSize": 64}, {"id": 310, "displayName": "Enchanting Table", "name": "enchanting_table", "stackSize": 64}, {"id": 311, "displayName": "End Portal Frame", "name": "end_portal_frame", "stackSize": 64}, {"id": 312, "displayName": "End Stone", "name": "end_stone", "stackSize": 64}, {"id": 313, "displayName": "End Stone Bricks", "name": "end_stone_bricks", "stackSize": 64}, {"id": 314, "displayName": "Dragon Egg", "name": "dragon_egg", "stackSize": 64}, {"id": 315, "displayName": "Sandstone Stairs", "name": "sandstone_stairs", "stackSize": 64}, {"id": 316, "displayName": "<PERSON><PERSON> Chest", "name": "ender_chest", "stackSize": 64}, {"id": 317, "displayName": "Block of Emerald", "name": "emerald_block", "stackSize": 64}, {"id": 318, "displayName": "Spruce Stairs", "name": "spruce_stairs", "stackSize": 64}, {"id": 319, "displayName": "<PERSON> Stairs", "name": "birch_stairs", "stackSize": 64}, {"id": 320, "displayName": "Jungle Stairs", "name": "jungle_stairs", "stackSize": 64}, {"id": 321, "displayName": "Crimson Stairs", "name": "crimson_stairs", "stackSize": 64}, {"id": 322, "displayName": "Warped Stairs", "name": "warped_stairs", "stackSize": 64}, {"id": 323, "displayName": "Command Block", "name": "command_block", "stackSize": 64}, {"id": 324, "displayName": "Beacon", "name": "beacon", "stackSize": 64}, {"id": 325, "displayName": "Cobblestone Wall", "name": "cobblestone_wall", "stackSize": 64}, {"id": 326, "displayName": "<PERSON><PERSON>", "name": "mossy_cobblestone_wall", "stackSize": 64}, {"id": 327, "displayName": "Brick Wall", "name": "brick_wall", "stackSize": 64}, {"id": 328, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "prismarine_wall", "stackSize": 64}, {"id": 329, "displayName": "Red Sandstone Wall", "name": "red_sandstone_wall", "stackSize": 64}, {"id": 330, "displayName": "Mossy Stone Brick Wall", "name": "mossy_stone_brick_wall", "stackSize": 64}, {"id": 331, "displayName": "Granite Wall", "name": "granite_wall", "stackSize": 64}, {"id": 332, "displayName": "Stone Brick Wall", "name": "stone_brick_wall", "stackSize": 64}, {"id": 333, "displayName": "Nether Brick Wall", "name": "nether_brick_wall", "stackSize": 64}, {"id": 334, "displayName": "Andesite Wall", "name": "andesite_wall", "stackSize": 64}, {"id": 335, "displayName": "Red Nether Brick Wall", "name": "red_nether_brick_wall", "stackSize": 64}, {"id": 336, "displayName": "Sandstone Wall", "name": "sandstone_wall", "stackSize": 64}, {"id": 337, "displayName": "End Stone Brick Wall", "name": "end_stone_brick_wall", "stackSize": 64}, {"id": 338, "displayName": "Diorite Wall", "name": "diorite_wall", "stackSize": 64}, {"id": 339, "displayName": "Blackstone Wall", "name": "blackstone_wall", "stackSize": 64}, {"id": 340, "displayName": "Polished Blackstone Wall", "name": "polished_blackstone_wall", "stackSize": 64}, {"id": 341, "displayName": "Polished Blackstone Brick Wall", "name": "polished_blackstone_brick_wall", "stackSize": 64}, {"id": 342, "displayName": "Cobbled Deepslate Wall", "name": "cobbled_deepslate_wall", "stackSize": 64}, {"id": 343, "displayName": "Polished Deepslate Wall", "name": "polished_deepslate_wall", "stackSize": 64}, {"id": 344, "displayName": "Deepslate Brick Wall", "name": "deepslate_brick_wall", "stackSize": 64}, {"id": 345, "displayName": "Deepslate Tile Wall", "name": "deepslate_tile_wall", "stackSize": 64}, {"id": 346, "displayName": "An<PERSON>", "name": "anvil", "stackSize": 64}, {"id": 347, "displayName": "Chipped Anvil", "name": "chipped_anvil", "stackSize": 64}, {"id": 348, "displayName": "Damaged Anvil", "name": "damaged_anvil", "stackSize": 64}, {"id": 349, "displayName": "Chiseled Quartz Block", "name": "chiseled_quartz_block", "stackSize": 64}, {"id": 350, "displayName": "Block of Quartz", "name": "quartz_block", "stackSize": 64}, {"id": 351, "displayName": "Quartz Bricks", "name": "quartz_bricks", "stackSize": 64}, {"id": 352, "displayName": "Quartz <PERSON>", "name": "quartz_pillar", "stackSize": 64}, {"id": 353, "displayName": "Quartz Stairs", "name": "quartz_stairs", "stackSize": 64}, {"id": 354, "displayName": "White Terracotta", "name": "white_terracotta", "stackSize": 64}, {"id": 355, "displayName": "Orange Terracotta", "name": "orange_terracotta", "stackSize": 64}, {"id": 356, "displayName": "Magenta Terracotta", "name": "magenta_terracotta", "stackSize": 64}, {"id": 357, "displayName": "Light Blue Terracotta", "name": "light_blue_terracotta", "stackSize": 64}, {"id": 358, "displayName": "Yellow Terracotta", "name": "yellow_terracotta", "stackSize": 64}, {"id": 359, "displayName": "Lime Terracotta", "name": "lime_terracotta", "stackSize": 64}, {"id": 360, "displayName": "Pink Terracotta", "name": "pink_terracotta", "stackSize": 64}, {"id": 361, "displayName": "Gray <PERSON>", "name": "gray_terracotta", "stackSize": 64}, {"id": 362, "displayName": "Light Gray Terracotta", "name": "light_gray_terracotta", "stackSize": 64}, {"id": 363, "displayName": "<PERSON><PERSON>", "name": "cyan_terracotta", "stackSize": 64}, {"id": 364, "displayName": "Purple Terracotta", "name": "purple_terracotta", "stackSize": 64}, {"id": 365, "displayName": "Blue Terracotta", "name": "blue_terracotta", "stackSize": 64}, {"id": 366, "displayName": "Brown Terracotta", "name": "brown_terracotta", "stackSize": 64}, {"id": 367, "displayName": "Green Terracotta", "name": "green_terracotta", "stackSize": 64}, {"id": 368, "displayName": "Red Terracotta", "name": "red_terracotta", "stackSize": 64}, {"id": 369, "displayName": "Black Terracotta", "name": "black_terracotta", "stackSize": 64}, {"id": 370, "displayName": "Barrier", "name": "barrier", "stackSize": 64}, {"id": 371, "displayName": "Light", "name": "light", "stackSize": 64}, {"id": 372, "displayName": "<PERSON>", "name": "hay_block", "stackSize": 64}, {"id": 373, "displayName": "White Carpet", "name": "white_carpet", "stackSize": 64}, {"id": 374, "displayName": "Orange Carpet", "name": "orange_carpet", "stackSize": 64}, {"id": 375, "displayName": "Magenta Carpet", "name": "magenta_carpet", "stackSize": 64}, {"id": 376, "displayName": "Light Blue Carpet", "name": "light_blue_carpet", "stackSize": 64}, {"id": 377, "displayName": "Yellow Carpet", "name": "yellow_carpet", "stackSize": 64}, {"id": 378, "displayName": "Lime Carpet", "name": "lime_carpet", "stackSize": 64}, {"id": 379, "displayName": "Pink Carpet", "name": "pink_carpet", "stackSize": 64}, {"id": 380, "displayName": "<PERSON> Carpet", "name": "gray_carpet", "stackSize": 64}, {"id": 381, "displayName": "Light Gray Carpet", "name": "light_gray_carpet", "stackSize": 64}, {"id": 382, "displayName": "<PERSON><PERSON>", "name": "cyan_carpet", "stackSize": 64}, {"id": 383, "displayName": "Purple Carpet", "name": "purple_carpet", "stackSize": 64}, {"id": 384, "displayName": "Blue Carpet", "name": "blue_carpet", "stackSize": 64}, {"id": 385, "displayName": "Brown Carpet", "name": "brown_carpet", "stackSize": 64}, {"id": 386, "displayName": "Green Carpet", "name": "green_carpet", "stackSize": 64}, {"id": 387, "displayName": "Red Carpet", "name": "red_carpet", "stackSize": 64}, {"id": 388, "displayName": "Black Carpet", "name": "black_carpet", "stackSize": 64}, {"id": 389, "displayName": "Terracotta", "name": "terracotta", "stackSize": 64}, {"id": 390, "displayName": "Packed Ice", "name": "packed_ice", "stackSize": 64}, {"id": 391, "displayName": "Acacia Stairs", "name": "acacia_stairs", "stackSize": 64}, {"id": 392, "displayName": "Dark Oak Stairs", "name": "dark_oak_stairs", "stackSize": 64}, {"id": 393, "displayName": "Dirt Path", "name": "dirt_path", "stackSize": 64}, {"id": 394, "displayName": "Sunflower", "name": "sunflower", "stackSize": 64}, {"id": 395, "displayName": "Lilac", "name": "lilac", "stackSize": 64}, {"id": 396, "displayName": "<PERSON>", "name": "rose_bush", "stackSize": 64}, {"id": 397, "displayName": "Peony", "name": "peony", "stackSize": 64}, {"id": 398, "displayName": "Tall Grass", "name": "tall_grass", "stackSize": 64}, {"id": 399, "displayName": "Large Fern", "name": "large_fern", "stackSize": 64}, {"id": 400, "displayName": "White Stained Glass", "name": "white_stained_glass", "stackSize": 64}, {"id": 401, "displayName": "Orange Stained Glass", "name": "orange_stained_glass", "stackSize": 64}, {"id": 402, "displayName": "Magenta Stained Glass", "name": "magenta_stained_glass", "stackSize": 64}, {"id": 403, "displayName": "Light Blue Stained Glass", "name": "light_blue_stained_glass", "stackSize": 64}, {"id": 404, "displayName": "Yellow Stained Glass", "name": "yellow_stained_glass", "stackSize": 64}, {"id": 405, "displayName": "Lime Stained Glass", "name": "lime_stained_glass", "stackSize": 64}, {"id": 406, "displayName": "Pink Stained Glass", "name": "pink_stained_glass", "stackSize": 64}, {"id": 407, "displayName": "<PERSON> Stained Glass", "name": "gray_stained_glass", "stackSize": 64}, {"id": 408, "displayName": "Light Gray Stained Glass", "name": "light_gray_stained_glass", "stackSize": 64}, {"id": 409, "displayName": "<PERSON><PERSON>", "name": "cyan_stained_glass", "stackSize": 64}, {"id": 410, "displayName": "Purple Stained Glass", "name": "purple_stained_glass", "stackSize": 64}, {"id": 411, "displayName": "Blue Stained Glass", "name": "blue_stained_glass", "stackSize": 64}, {"id": 412, "displayName": "<PERSON> Stained Glass", "name": "brown_stained_glass", "stackSize": 64}, {"id": 413, "displayName": "Green Stained Glass", "name": "green_stained_glass", "stackSize": 64}, {"id": 414, "displayName": "Red Stained Glass", "name": "red_stained_glass", "stackSize": 64}, {"id": 415, "displayName": "Black Stained Glass", "name": "black_stained_glass", "stackSize": 64}, {"id": 416, "displayName": "White Stained Glass Pane", "name": "white_stained_glass_pane", "stackSize": 64}, {"id": 417, "displayName": "Orange Stained Glass Pane", "name": "orange_stained_glass_pane", "stackSize": 64}, {"id": 418, "displayName": "Magenta Stained Glass Pane", "name": "magenta_stained_glass_pane", "stackSize": 64}, {"id": 419, "displayName": "Light Blue Stained Glass Pane", "name": "light_blue_stained_glass_pane", "stackSize": 64}, {"id": 420, "displayName": "Yellow Stained Glass Pane", "name": "yellow_stained_glass_pane", "stackSize": 64}, {"id": 421, "displayName": "Lime Stained Glass Pane", "name": "lime_stained_glass_pane", "stackSize": 64}, {"id": 422, "displayName": "Pink Stained Glass Pane", "name": "pink_stained_glass_pane", "stackSize": 64}, {"id": 423, "displayName": "Gray Stained Glass Pane", "name": "gray_stained_glass_pane", "stackSize": 64}, {"id": 424, "displayName": "Light Gray Stained Glass Pane", "name": "light_gray_stained_glass_pane", "stackSize": 64}, {"id": 425, "displayName": "<PERSON><PERSON> Stained Glass Pane", "name": "cyan_stained_glass_pane", "stackSize": 64}, {"id": 426, "displayName": "Purple Stained Glass Pane", "name": "purple_stained_glass_pane", "stackSize": 64}, {"id": 427, "displayName": "Blue Stained Glass Pane", "name": "blue_stained_glass_pane", "stackSize": 64}, {"id": 428, "displayName": "<PERSON> Stained Glass Pane", "name": "brown_stained_glass_pane", "stackSize": 64}, {"id": 429, "displayName": "Green Stained Glass Pane", "name": "green_stained_glass_pane", "stackSize": 64}, {"id": 430, "displayName": "Red Stained Glass Pane", "name": "red_stained_glass_pane", "stackSize": 64}, {"id": 431, "displayName": "Black Stained Glass Pane", "name": "black_stained_glass_pane", "stackSize": 64}, {"id": 432, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "prismarine", "stackSize": 64}, {"id": 433, "displayName": "Prismarine <PERSON>s", "name": "prismarine_bricks", "stackSize": 64}, {"id": 434, "displayName": "<PERSON>", "name": "dark_prismarine", "stackSize": 64}, {"id": 435, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "prismarine_stairs", "stackSize": 64}, {"id": 436, "displayName": "Prismarine Brick Stairs", "name": "prismarine_brick_stairs", "stackSize": 64}, {"id": 437, "displayName": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "name": "dark_prismarine_stairs", "stackSize": 64}, {"id": 438, "displayName": "Sea Lantern", "name": "sea_lantern", "stackSize": 64}, {"id": 439, "displayName": "Red Sandstone", "name": "red_sandstone", "stackSize": 64}, {"id": 440, "displayName": "Chiseled Red Sandstone", "name": "chiseled_red_sandstone", "stackSize": 64}, {"id": 441, "displayName": "Cut Red Sandstone", "name": "cut_red_sandstone", "stackSize": 64}, {"id": 442, "displayName": "Red Sandstone Stairs", "name": "red_sandstone_stairs", "stackSize": 64}, {"id": 443, "displayName": "Repeating Command Block", "name": "repeating_command_block", "stackSize": 64}, {"id": 444, "displayName": "Chain Command Block", "name": "chain_command_block", "stackSize": 64}, {"id": 445, "displayName": "Magma Block", "name": "magma_block", "stackSize": 64}, {"id": 446, "displayName": "Nether Wart Block", "name": "nether_wart_block", "stackSize": 64}, {"id": 447, "displayName": "Warped Wart Block", "name": "warped_wart_block", "stackSize": 64}, {"id": 448, "displayName": "Red Nether Bricks", "name": "red_nether_bricks", "stackSize": 64}, {"id": 449, "displayName": "Bone Block", "name": "bone_block", "stackSize": 64}, {"id": 450, "displayName": "Structure Void", "name": "structure_void", "stackSize": 64}, {"id": 451, "displayName": "Shulker Box", "name": "shulker_box", "stackSize": 1}, {"id": 452, "displayName": "White Shulker Box", "name": "white_shulker_box", "stackSize": 1}, {"id": 453, "displayName": "Orange Shulker Box", "name": "orange_shulker_box", "stackSize": 1}, {"id": 454, "displayName": "<PERSON><PERSON>a <PERSON>er Box", "name": "magenta_shulker_box", "stackSize": 1}, {"id": 455, "displayName": "Light Blue Shulker Box", "name": "light_blue_shulker_box", "stackSize": 1}, {"id": 456, "displayName": "Yellow Shulker Box", "name": "yellow_shulker_box", "stackSize": 1}, {"id": 457, "displayName": "<PERSON>e <PERSON>er Box", "name": "lime_shulker_box", "stackSize": 1}, {"id": 458, "displayName": "Pink Shulker Box", "name": "pink_shulker_box", "stackSize": 1}, {"id": 459, "displayName": "<PERSON>", "name": "gray_shulker_box", "stackSize": 1}, {"id": 460, "displayName": "Light Gray Shulker Box", "name": "light_gray_shulker_box", "stackSize": 1}, {"id": 461, "displayName": "<PERSON><PERSON>", "name": "cyan_shulker_box", "stackSize": 1}, {"id": 462, "displayName": "Purple Shulker Box", "name": "purple_shulker_box", "stackSize": 1}, {"id": 463, "displayName": "Blue Shulker Box", "name": "blue_shulker_box", "stackSize": 1}, {"id": 464, "displayName": "<PERSON> Shulker Box", "name": "brown_shulker_box", "stackSize": 1}, {"id": 465, "displayName": "Green Shulker Box", "name": "green_shulker_box", "stackSize": 1}, {"id": 466, "displayName": "Red Shulker Box", "name": "red_shulker_box", "stackSize": 1}, {"id": 467, "displayName": "Black Shulker Box", "name": "black_shulker_box", "stackSize": 1}, {"id": 468, "displayName": "White Glazed Terracotta", "name": "white_glazed_terracotta", "stackSize": 64}, {"id": 469, "displayName": "Orange Glazed Terracotta", "name": "orange_glazed_terracotta", "stackSize": 64}, {"id": 470, "displayName": "Magenta Glazed Terracotta", "name": "magenta_glazed_terracotta", "stackSize": 64}, {"id": 471, "displayName": "Light Blue Glazed Terracotta", "name": "light_blue_glazed_terracotta", "stackSize": 64}, {"id": 472, "displayName": "Yellow Glazed Terracotta", "name": "yellow_glazed_terracotta", "stackSize": 64}, {"id": 473, "displayName": "Lime Glazed Terracotta", "name": "lime_glazed_terracotta", "stackSize": 64}, {"id": 474, "displayName": "Pink Glazed Terracotta", "name": "pink_glazed_terracotta", "stackSize": 64}, {"id": 475, "displayName": "Gray Glazed Terracotta", "name": "gray_glazed_terracotta", "stackSize": 64}, {"id": 476, "displayName": "Light Gray Glazed Terracotta", "name": "light_gray_glazed_terracotta", "stackSize": 64}, {"id": 477, "displayName": "<PERSON><PERSON>zed Terracotta", "name": "cyan_glazed_terracotta", "stackSize": 64}, {"id": 478, "displayName": "Purple Glazed Terracotta", "name": "purple_glazed_terracotta", "stackSize": 64}, {"id": 479, "displayName": "Blue Glazed Terracotta", "name": "blue_glazed_terracotta", "stackSize": 64}, {"id": 480, "displayName": "Brown Glazed Terracotta", "name": "brown_glazed_terracotta", "stackSize": 64}, {"id": 481, "displayName": "Green Glazed Terracotta", "name": "green_glazed_terracotta", "stackSize": 64}, {"id": 482, "displayName": "Red Glazed Terracotta", "name": "red_glazed_terracotta", "stackSize": 64}, {"id": 483, "displayName": "Black Glazed Terracotta", "name": "black_glazed_terracotta", "stackSize": 64}, {"id": 484, "displayName": "White Concrete", "name": "white_concrete", "stackSize": 64}, {"id": 485, "displayName": "Orange Concrete", "name": "orange_concrete", "stackSize": 64}, {"id": 486, "displayName": "Magenta Concrete", "name": "magenta_concrete", "stackSize": 64}, {"id": 487, "displayName": "Light Blue Concrete", "name": "light_blue_concrete", "stackSize": 64}, {"id": 488, "displayName": "Yellow Concrete", "name": "yellow_concrete", "stackSize": 64}, {"id": 489, "displayName": "Lime Concrete", "name": "lime_concrete", "stackSize": 64}, {"id": 490, "displayName": "Pink Concrete", "name": "pink_concrete", "stackSize": 64}, {"id": 491, "displayName": "<PERSON>", "name": "gray_concrete", "stackSize": 64}, {"id": 492, "displayName": "Light Gray Concrete", "name": "light_gray_concrete", "stackSize": 64}, {"id": 493, "displayName": "<PERSON><PERSON>", "name": "cyan_concrete", "stackSize": 64}, {"id": 494, "displayName": "Purple Concrete", "name": "purple_concrete", "stackSize": 64}, {"id": 495, "displayName": "Blue Concrete", "name": "blue_concrete", "stackSize": 64}, {"id": 496, "displayName": "<PERSON> Concrete", "name": "brown_concrete", "stackSize": 64}, {"id": 497, "displayName": "Green Concrete", "name": "green_concrete", "stackSize": 64}, {"id": 498, "displayName": "Red Concrete", "name": "red_concrete", "stackSize": 64}, {"id": 499, "displayName": "Black Concrete", "name": "black_concrete", "stackSize": 64}, {"id": 500, "displayName": "White Concrete Powder", "name": "white_concrete_powder", "stackSize": 64}, {"id": 501, "displayName": "Orange Concrete Powder", "name": "orange_concrete_powder", "stackSize": 64}, {"id": 502, "displayName": "Magenta Concrete Powder", "name": "magenta_concrete_powder", "stackSize": 64}, {"id": 503, "displayName": "Light Blue Concrete Powder", "name": "light_blue_concrete_powder", "stackSize": 64}, {"id": 504, "displayName": "Yellow Concrete Powder", "name": "yellow_concrete_powder", "stackSize": 64}, {"id": 505, "displayName": "Lime Concrete <PERSON>", "name": "lime_concrete_powder", "stackSize": 64}, {"id": 506, "displayName": "Pink Concrete Powder", "name": "pink_concrete_powder", "stackSize": 64}, {"id": 507, "displayName": "<PERSON> Concre<PERSON>", "name": "gray_concrete_powder", "stackSize": 64}, {"id": 508, "displayName": "Light Gray Concrete Powder", "name": "light_gray_concrete_powder", "stackSize": 64}, {"id": 509, "displayName": "<PERSON><PERSON>", "name": "cyan_concrete_powder", "stackSize": 64}, {"id": 510, "displayName": "Purple Concrete Powder", "name": "purple_concrete_powder", "stackSize": 64}, {"id": 511, "displayName": "Blue Concrete Powder", "name": "blue_concrete_powder", "stackSize": 64}, {"id": 512, "displayName": "<PERSON> Concrete <PERSON>", "name": "brown_concrete_powder", "stackSize": 64}, {"id": 513, "displayName": "Green Concrete Powder", "name": "green_concrete_powder", "stackSize": 64}, {"id": 514, "displayName": "Red Concrete Powder", "name": "red_concrete_powder", "stackSize": 64}, {"id": 515, "displayName": "Black Concrete Powder", "name": "black_concrete_powder", "stackSize": 64}, {"id": 516, "displayName": "Turtle Egg", "name": "turtle_egg", "stackSize": 64}, {"id": 517, "displayName": "Dead Tube Coral Block", "name": "dead_tube_coral_block", "stackSize": 64}, {"id": 518, "displayName": "Dead Brain Coral Block", "name": "dead_brain_coral_block", "stackSize": 64}, {"id": 519, "displayName": "Dead Bubble Coral Block", "name": "dead_bubble_coral_block", "stackSize": 64}, {"id": 520, "displayName": "Dead Fire Coral Block", "name": "dead_fire_coral_block", "stackSize": 64}, {"id": 521, "displayName": "Dead Horn Coral Block", "name": "dead_horn_coral_block", "stackSize": 64}, {"id": 522, "displayName": "Tube Coral Block", "name": "tube_coral_block", "stackSize": 64}, {"id": 523, "displayName": "Brain <PERSON>", "name": "brain_coral_block", "stackSize": 64}, {"id": 524, "displayName": "Bubble Coral Block", "name": "bubble_coral_block", "stackSize": 64}, {"id": 525, "displayName": "Fire Coral Block", "name": "fire_coral_block", "stackSize": 64}, {"id": 526, "displayName": "Horn Coral Block", "name": "horn_coral_block", "stackSize": 64}, {"id": 527, "displayName": "Tube Coral", "name": "tube_coral", "stackSize": 64}, {"id": 528, "displayName": "Brain Coral", "name": "brain_coral", "stackSize": 64}, {"id": 529, "displayName": "Bubble Coral", "name": "bubble_coral", "stackSize": 64}, {"id": 530, "displayName": "Fire Coral", "name": "fire_coral", "stackSize": 64}, {"id": 531, "displayName": "Horn Coral", "name": "horn_coral", "stackSize": 64}, {"id": 532, "displayName": "Dead Brain Coral", "name": "dead_brain_coral", "stackSize": 64}, {"id": 533, "displayName": "Dead Bubble Coral", "name": "dead_bubble_coral", "stackSize": 64}, {"id": 534, "displayName": "Dead Fire Coral", "name": "dead_fire_coral", "stackSize": 64}, {"id": 535, "displayName": "Dead Horn Coral", "name": "dead_horn_coral", "stackSize": 64}, {"id": 536, "displayName": "Dead Tube Coral", "name": "dead_tube_coral", "stackSize": 64}, {"id": 537, "displayName": "Tube Coral Fan", "name": "tube_coral_fan", "stackSize": 64}, {"id": 538, "displayName": "Brain Coral Fan", "name": "brain_coral_fan", "stackSize": 64}, {"id": 539, "displayName": "Bubble Coral Fan", "name": "bubble_coral_fan", "stackSize": 64}, {"id": 540, "displayName": "Fire Coral Fan", "name": "fire_coral_fan", "stackSize": 64}, {"id": 541, "displayName": "Horn Coral Fan", "name": "horn_coral_fan", "stackSize": 64}, {"id": 542, "displayName": "Dead Tube Coral Fan", "name": "dead_tube_coral_fan", "stackSize": 64}, {"id": 543, "displayName": "Dead Brain Coral Fan", "name": "dead_brain_coral_fan", "stackSize": 64}, {"id": 544, "displayName": "Dead Bubble Coral Fan", "name": "dead_bubble_coral_fan", "stackSize": 64}, {"id": 545, "displayName": "Dead Fire Coral Fan", "name": "dead_fire_coral_fan", "stackSize": 64}, {"id": 546, "displayName": "Dead Horn Coral Fan", "name": "dead_horn_coral_fan", "stackSize": 64}, {"id": 547, "displayName": "Blue Ice", "name": "blue_ice", "stackSize": 64}, {"id": 548, "displayName": "Conduit", "name": "conduit", "stackSize": 64}, {"id": 549, "displayName": "Polished Granite Stairs", "name": "polished_granite_stairs", "stackSize": 64}, {"id": 550, "displayName": "Smooth Red Sandstone Stairs", "name": "smooth_red_sandstone_stairs", "stackSize": 64}, {"id": 551, "displayName": "Mossy Stone Brick Stairs", "name": "mossy_stone_brick_stairs", "stackSize": 64}, {"id": 552, "displayName": "Polished Diorite Stairs", "name": "polished_diorite_stairs", "stackSize": 64}, {"id": 553, "displayName": "Mossy Cobblestone Stairs", "name": "mossy_cobblestone_stairs", "stackSize": 64}, {"id": 554, "displayName": "End Stone Brick Stairs", "name": "end_stone_brick_stairs", "stackSize": 64}, {"id": 555, "displayName": "Stone Stairs", "name": "stone_stairs", "stackSize": 64}, {"id": 556, "displayName": "Smooth Sandstone Stairs", "name": "smooth_sandstone_stairs", "stackSize": 64}, {"id": 557, "displayName": "Smooth Quartz Stairs", "name": "smooth_quartz_stairs", "stackSize": 64}, {"id": 558, "displayName": "Granite Stairs", "name": "granite_stairs", "stackSize": 64}, {"id": 559, "displayName": "Andesite Stairs", "name": "andesite_stairs", "stackSize": 64}, {"id": 560, "displayName": "Red Nether Brick Stairs", "name": "red_nether_brick_stairs", "stackSize": 64}, {"id": 561, "displayName": "Polished Andesite Stairs", "name": "polished_andesite_stairs", "stackSize": 64}, {"id": 562, "displayName": "Diorite Stairs", "name": "diorite_stairs", "stackSize": 64}, {"id": 563, "displayName": "Cobbled Deepslate Stairs", "name": "cobbled_deepslate_stairs", "stackSize": 64}, {"id": 564, "displayName": "Polished Deepslate Stairs", "name": "polished_deepslate_stairs", "stackSize": 64}, {"id": 565, "displayName": "Deepslate Brick Stairs", "name": "deepslate_brick_stairs", "stackSize": 64}, {"id": 566, "displayName": "Deepslate Tile Stairs", "name": "deepslate_tile_stairs", "stackSize": 64}, {"id": 567, "displayName": "Polished Granite Slab", "name": "polished_granite_slab", "stackSize": 64}, {"id": 568, "displayName": "Smooth Red Sandstone Slab", "name": "smooth_red_sandstone_slab", "stackSize": 64}, {"id": 569, "displayName": "Mossy Stone Brick Slab", "name": "mossy_stone_brick_slab", "stackSize": 64}, {"id": 570, "displayName": "Polished Diorite S<PERSON>b", "name": "polished_diorite_slab", "stackSize": 64}, {"id": 571, "displayName": "<PERSON><PERSON> Slab", "name": "mossy_cobblestone_slab", "stackSize": 64}, {"id": 572, "displayName": "End Stone Brick Slab", "name": "end_stone_brick_slab", "stackSize": 64}, {"id": 573, "displayName": "Smooth Sandstone Slab", "name": "smooth_sandstone_slab", "stackSize": 64}, {"id": 574, "displayName": "Smooth Quartz Slab", "name": "smooth_quartz_slab", "stackSize": 64}, {"id": 575, "displayName": "Granite Slab", "name": "granite_slab", "stackSize": 64}, {"id": 576, "displayName": "Andesite Slab", "name": "andesite_slab", "stackSize": 64}, {"id": 577, "displayName": "Red Nether Brick Slab", "name": "red_nether_brick_slab", "stackSize": 64}, {"id": 578, "displayName": "Polished Andesite Slab", "name": "polished_andesite_slab", "stackSize": 64}, {"id": 579, "displayName": "Diorite Slab", "name": "diorite_slab", "stackSize": 64}, {"id": 580, "displayName": "Cobbled Deepslate Slab", "name": "cobbled_deepslate_slab", "stackSize": 64}, {"id": 581, "displayName": "Polished Deepslate Slab", "name": "polished_deepslate_slab", "stackSize": 64}, {"id": 582, "displayName": "Deepslate Brick Slab", "name": "deepslate_brick_slab", "stackSize": 64}, {"id": 583, "displayName": "Deepslate Tile Slab", "name": "deepslate_tile_slab", "stackSize": 64}, {"id": 584, "displayName": "Scaffolding", "name": "scaffolding", "stackSize": 64}, {"id": 585, "displayName": "Redstone Dust", "name": "redstone", "stackSize": 64}, {"id": 586, "displayName": "Redstone Torch", "name": "redstone_torch", "stackSize": 64}, {"id": 587, "displayName": "Block of Redstone", "name": "redstone_block", "stackSize": 64}, {"id": 588, "displayName": "Redstone Repeater", "name": "repeater", "stackSize": 64}, {"id": 589, "displayName": "Redstone Comparator", "name": "comparator", "stackSize": 64}, {"id": 590, "displayName": "<PERSON><PERSON>", "name": "piston", "stackSize": 64}, {"id": 591, "displayName": "<PERSON><PERSON>", "name": "sticky_piston", "stackSize": 64}, {"id": 592, "displayName": "Slime Block", "name": "slime_block", "stackSize": 64}, {"id": 593, "displayName": "Honey Block", "name": "honey_block", "stackSize": 64}, {"id": 594, "displayName": "Observer", "name": "observer", "stackSize": 64}, {"id": 595, "displayName": "<PERSON>", "name": "hopper", "stackSize": 64}, {"id": 596, "displayName": "Dispenser", "name": "dispenser", "stackSize": 64}, {"id": 597, "displayName": "Dropper", "name": "dropper", "stackSize": 64}, {"id": 598, "displayName": "Lectern", "name": "lectern", "stackSize": 64}, {"id": 599, "displayName": "Target", "name": "target", "stackSize": 64}, {"id": 600, "displayName": "Lever", "name": "lever", "stackSize": 64}, {"id": 601, "displayName": "Lightning Rod", "name": "lightning_rod", "stackSize": 64}, {"id": 602, "displayName": "Daylight Detector", "name": "daylight_detector", "stackSize": 64}, {"id": 603, "displayName": "Sculk Sensor", "name": "sculk_sensor", "stackSize": 64}, {"id": 604, "displayName": "Tripwire Hook", "name": "tripwire_hook", "stackSize": 64}, {"id": 605, "displayName": "Trapped Chest", "name": "trapped_chest", "stackSize": 64}, {"id": 606, "displayName": "TNT", "name": "tnt", "stackSize": 64}, {"id": 607, "displayName": "Redstone Lamp", "name": "redstone_lamp", "stackSize": 64}, {"id": 608, "displayName": "Note Block", "name": "note_block", "stackSize": 64}, {"id": 609, "displayName": "<PERSON>", "name": "stone_button", "stackSize": 64}, {"id": 610, "displayName": "Polished Blackstone Button", "name": "polished_blackstone_button", "stackSize": 64}, {"id": 611, "displayName": "Oak Button", "name": "oak_button", "stackSize": 64}, {"id": 612, "displayName": "Spruce Button", "name": "spruce_button", "stackSize": 64}, {"id": 613, "displayName": "<PERSON>", "name": "birch_button", "stackSize": 64}, {"id": 614, "displayName": "<PERSON>ton", "name": "jungle_button", "stackSize": 64}, {"id": 615, "displayName": "Acacia <PERSON>", "name": "acacia_button", "stackSize": 64}, {"id": 616, "displayName": "Dark Oak Button", "name": "dark_oak_button", "stackSize": 64}, {"id": 617, "displayName": "<PERSON>", "name": "crimson_button", "stackSize": 64}, {"id": 618, "displayName": "Warped <PERSON>", "name": "warped_button", "stackSize": 64}, {"id": 619, "displayName": "Stone Pressure Plate", "name": "stone_pressure_plate", "stackSize": 64}, {"id": 620, "displayName": "Polished Blackstone Pressure Plate", "name": "polished_blackstone_pressure_plate", "stackSize": 64}, {"id": 621, "displayName": "Light Weighted Pressure Plate", "name": "light_weighted_pressure_plate", "stackSize": 64}, {"id": 622, "displayName": "Heavy Weighted Pressure Plate", "name": "heavy_weighted_pressure_plate", "stackSize": 64}, {"id": 623, "displayName": "Oak Pressure Plate", "name": "oak_pressure_plate", "stackSize": 64}, {"id": 624, "displayName": "Spruce Pressure Plate", "name": "spruce_pressure_plate", "stackSize": 64}, {"id": 625, "displayName": "Birch Pressure Plate", "name": "birch_pressure_plate", "stackSize": 64}, {"id": 626, "displayName": "Jungle Pressure Plate", "name": "jungle_pressure_plate", "stackSize": 64}, {"id": 627, "displayName": "Acacia Pressure Plate", "name": "acacia_pressure_plate", "stackSize": 64}, {"id": 628, "displayName": "Dark Oak Pressure Plate", "name": "dark_oak_pressure_plate", "stackSize": 64}, {"id": 629, "displayName": "Crimson Pressure Plate", "name": "crimson_pressure_plate", "stackSize": 64}, {"id": 630, "displayName": "Warped Pressure Plate", "name": "warped_pressure_plate", "stackSize": 64}, {"id": 631, "displayName": "Iron Door", "name": "iron_door", "stackSize": 64}, {"id": 632, "displayName": "Oak Door", "name": "oak_door", "stackSize": 64}, {"id": 633, "displayName": "Spruce Door", "name": "spruce_door", "stackSize": 64}, {"id": 634, "displayName": "<PERSON>", "name": "birch_door", "stackSize": 64}, {"id": 635, "displayName": "Jungle Door", "name": "jungle_door", "stackSize": 64}, {"id": 636, "displayName": "Acacia Door", "name": "acacia_door", "stackSize": 64}, {"id": 637, "displayName": "Dark Oak Door", "name": "dark_oak_door", "stackSize": 64}, {"id": 638, "displayName": "Crimson Door", "name": "crimson_door", "stackSize": 64}, {"id": 639, "displayName": "Warped Door", "name": "warped_door", "stackSize": 64}, {"id": 640, "displayName": "Iron Trapdoor", "name": "iron_trapdoor", "stackSize": 64}, {"id": 641, "displayName": "Oak Trapdoor", "name": "oak_trapdoor", "stackSize": 64}, {"id": 642, "displayName": "Spruce Trapdoor", "name": "spruce_trapdoor", "stackSize": 64}, {"id": 643, "displayName": "<PERSON>", "name": "birch_trapdoor", "stackSize": 64}, {"id": 644, "displayName": "Jungle Trapdoor", "name": "jungle_trapdoor", "stackSize": 64}, {"id": 645, "displayName": "Acacia T<PERSON>door", "name": "acacia_trapdoor", "stackSize": 64}, {"id": 646, "displayName": "Dark Oak Trapdoor", "name": "dark_oak_trapdoor", "stackSize": 64}, {"id": 647, "displayName": "Crimson Trapdoor", "name": "crimson_trapdoor", "stackSize": 64}, {"id": 648, "displayName": "Warped Trapdoor", "name": "warped_trapdoor", "stackSize": 64}, {"id": 649, "displayName": "Oak Fence Gate", "name": "oak_fence_gate", "stackSize": 64}, {"id": 650, "displayName": "Spruce Fence Gate", "name": "spruce_fence_gate", "stackSize": 64}, {"id": 651, "displayName": "Birch Fence Gate", "name": "birch_fence_gate", "stackSize": 64}, {"id": 652, "displayName": "Jungle Fence Gate", "name": "jungle_fence_gate", "stackSize": 64}, {"id": 653, "displayName": "Acacia Fence Gate", "name": "acacia_fence_gate", "stackSize": 64}, {"id": 654, "displayName": "Dark Oak Fence Gate", "name": "dark_oak_fence_gate", "stackSize": 64}, {"id": 655, "displayName": "Crimson Fence Gate", "name": "crimson_fence_gate", "stackSize": 64}, {"id": 656, "displayName": "Warped Fence Gate", "name": "warped_fence_gate", "stackSize": 64}, {"id": 657, "displayName": "Powered Rail", "name": "powered_rail", "stackSize": 64}, {"id": 658, "displayName": "Detector Rail", "name": "detector_rail", "stackSize": 64}, {"id": 659, "displayName": "Rail", "name": "rail", "stackSize": 64}, {"id": 660, "displayName": "Activator Rail", "name": "activator_rail", "stackSize": 64}, {"id": 661, "displayName": "Saddle", "name": "saddle", "stackSize": 1}, {"id": 662, "displayName": "Minecart", "name": "minecart", "stackSize": 1}, {"id": 663, "displayName": "Minecart with Chest", "name": "chest_minecart", "stackSize": 1}, {"id": 664, "displayName": "Minecart with Furnace", "name": "furnace_minecart", "stackSize": 1}, {"id": 665, "displayName": "Minecart with TNT", "name": "tnt_minecart", "stackSize": 1}, {"id": 666, "displayName": "Minecart with <PERSON>", "name": "hopper_minecart", "stackSize": 1}, {"id": 667, "displayName": "Carrot on a Stick", "name": "carrot_on_a_stick", "stackSize": 1, "maxDurability": 25, "enchantCategories": ["breakable", "vanishable"]}, {"id": 668, "displayName": "Warped Fungus on a Stick", "name": "warped_fungus_on_a_stick", "stackSize": 64, "maxDurability": 100, "enchantCategories": ["breakable", "vanishable"]}, {"id": 669, "displayName": "Elytra", "name": "elytra", "stackSize": 1, "maxDurability": 432, "enchantCategories": ["breakable", "wearable", "vanishable"], "repairWith": ["phantom_membrane"]}, {"id": 670, "displayName": "Oak Boat", "name": "oak_boat", "stackSize": 1}, {"id": 671, "displayName": "Spruce Boat", "name": "spruce_boat", "stackSize": 1}, {"id": 672, "displayName": "<PERSON> Boat", "name": "birch_boat", "stackSize": 1}, {"id": 673, "displayName": "Jungle Boat", "name": "jungle_boat", "stackSize": 1}, {"id": 674, "displayName": "Acacia Boat", "name": "acacia_boat", "stackSize": 1}, {"id": 675, "displayName": "Dark Oak Boat", "name": "dark_oak_boat", "stackSize": 1}, {"id": 676, "displayName": "Structure Block", "name": "structure_block", "stackSize": 64}, {"id": 677, "displayName": "Jigsaw Block", "name": "jigsaw", "stackSize": 64}, {"id": 678, "displayName": "Turtle Shell", "name": "turtle_helmet", "stackSize": 1, "maxDurability": 275, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["scute"]}, {"id": 679, "displayName": "<PERSON><PERSON>", "name": "scute", "stackSize": 64}, {"id": 680, "displayName": "Flint and Steel", "name": "flint_and_steel", "stackSize": 1, "maxDurability": 64, "enchantCategories": ["breakable", "vanishable"]}, {"id": 681, "displayName": "Apple", "name": "apple", "stackSize": 64}, {"id": 682, "displayName": "Bow", "name": "bow", "stackSize": 1, "maxDurability": 384, "enchantCategories": ["breakable", "bow", "vanishable"]}, {"id": 683, "displayName": "Arrow", "name": "arrow", "stackSize": 64}, {"id": 684, "displayName": "Coal", "name": "coal", "stackSize": 64}, {"id": 685, "displayName": "Charc<PERSON>l", "name": "charcoal", "stackSize": 64}, {"id": 686, "displayName": "Diamond", "name": "diamond", "stackSize": 64}, {"id": 687, "displayName": "Emerald", "name": "emerald", "stackSize": 64}, {"id": 688, "displayName": "<PERSON><PERSON>", "name": "lapis_lazuli", "stackSize": 64}, {"id": 689, "displayName": "<PERSON><PERSON>", "name": "quartz", "stackSize": 64}, {"id": 690, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "amethyst_shard", "stackSize": 64}, {"id": 691, "displayName": "Raw Iron", "name": "raw_iron", "stackSize": 64}, {"id": 692, "displayName": "Iron Ingot", "name": "iron_ingot", "stackSize": 64}, {"id": 693, "displayName": "Raw Copper", "name": "raw_copper", "stackSize": 64}, {"id": 694, "displayName": "Copper Ingot", "name": "copper_ingot", "stackSize": 64}, {"id": 695, "displayName": "Raw Gold", "name": "raw_gold", "stackSize": 64}, {"id": 696, "displayName": "Gold Ingot", "name": "gold_ingot", "stackSize": 64}, {"id": 697, "displayName": "Netherite Ingot", "name": "netherite_ingot", "stackSize": 64}, {"id": 698, "displayName": "Netherite Scrap", "name": "netherite_scrap", "stackSize": 64}, {"id": 699, "displayName": "Wooden Sword", "name": "wooden_sword", "stackSize": 1, "maxDurability": 59, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"]}, {"id": 700, "displayName": "<PERSON><PERSON>", "name": "wooden_shovel", "stackSize": 1, "maxDurability": 59, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"]}, {"id": 701, "displayName": "<PERSON><PERSON> Pick<PERSON>e", "name": "wooden_pickaxe", "stackSize": 1, "maxDurability": 59, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"]}, {"id": 702, "displayName": "Wooden Axe", "name": "wooden_axe", "stackSize": 1, "maxDurability": 59, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"]}, {"id": 703, "displayName": "<PERSON><PERSON>e", "name": "wooden_hoe", "stackSize": 1, "maxDurability": 59, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"]}, {"id": 704, "displayName": "Stone Sword", "name": "stone_sword", "stackSize": 1, "maxDurability": 131, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["cobblestone", "blackstone"]}, {"id": 705, "displayName": "<PERSON>el", "name": "stone_shovel", "stackSize": 1, "maxDurability": 131, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["cobblestone", "blackstone"]}, {"id": 706, "displayName": "<PERSON>", "name": "stone_pickaxe", "stackSize": 1, "maxDurability": 131, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["cobblestone", "blackstone"]}, {"id": 707, "displayName": "Stone Axe", "name": "stone_axe", "stackSize": 1, "maxDurability": 131, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["cobblestone", "blackstone"]}, {"id": 708, "displayName": "Stone Hoe", "name": "stone_hoe", "stackSize": 1, "maxDurability": 131, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["cobblestone", "blackstone"]}, {"id": 709, "displayName": "Golden Sword", "name": "golden_sword", "stackSize": 1, "maxDurability": 32, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 710, "displayName": "Golden Shovel", "name": "golden_shovel", "stackSize": 1, "maxDurability": 32, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 711, "displayName": "Golden Pickaxe", "name": "golden_pickaxe", "stackSize": 1, "maxDurability": 32, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 712, "displayName": "Golden Axe", "name": "golden_axe", "stackSize": 1, "maxDurability": 32, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 713, "displayName": "Golden Hoe", "name": "golden_hoe", "stackSize": 1, "maxDurability": 32, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 714, "displayName": "Iron Sword", "name": "iron_sword", "stackSize": 1, "maxDurability": 250, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 715, "displayName": "Iron Shovel", "name": "iron_shovel", "stackSize": 1, "maxDurability": 250, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 716, "displayName": "Iron Pickaxe", "name": "iron_pickaxe", "stackSize": 1, "maxDurability": 250, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 717, "displayName": "Iron Axe", "name": "iron_axe", "stackSize": 1, "maxDurability": 250, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 718, "displayName": "Iron Hoe", "name": "iron_hoe", "stackSize": 1, "maxDurability": 250, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 719, "displayName": "Diamond Sword", "name": "diamond_sword", "stackSize": 1, "maxDurability": 1561, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["diamond"]}, {"id": 720, "displayName": "Diamond Shovel", "name": "diamond_shovel", "stackSize": 1, "maxDurability": 1561, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["diamond"]}, {"id": 721, "displayName": "Diamond Pickaxe", "name": "diamond_pickaxe", "stackSize": 1, "maxDurability": 1561, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["diamond"]}, {"id": 722, "displayName": "Diamond Axe", "name": "diamond_axe", "stackSize": 1, "maxDurability": 1561, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["diamond"]}, {"id": 723, "displayName": "Diamond Hoe", "name": "diamond_hoe", "stackSize": 1, "maxDurability": 1561, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["diamond"]}, {"id": 724, "displayName": "Netherite Sword", "name": "netherite_sword", "stackSize": 1, "maxDurability": 2031, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["netherite_ingot"]}, {"id": 725, "displayName": "<PERSON><PERSON><PERSON>", "name": "netherite_shovel", "stackSize": 1, "maxDurability": 2031, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["netherite_ingot"]}, {"id": 726, "displayName": "Netherite Pickaxe", "name": "netherite_pickaxe", "stackSize": 1, "maxDurability": 2031, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["netherite_ingot"]}, {"id": 727, "displayName": "Netherite Axe", "name": "netherite_axe", "stackSize": 1, "maxDurability": 2031, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["netherite_ingot"]}, {"id": 728, "displayName": "Netherite Hoe", "name": "netherite_hoe", "stackSize": 1, "maxDurability": 2031, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["netherite_ingot"]}, {"id": 729, "displayName": "Stick", "name": "stick", "stackSize": 64}, {"id": 730, "displayName": "Bowl", "name": "bowl", "stackSize": 64}, {"id": 731, "displayName": "Mushroom Stew", "name": "mushroom_stew", "stackSize": 1}, {"id": 732, "displayName": "String", "name": "string", "stackSize": 64}, {"id": 733, "displayName": "<PERSON><PERSON>", "name": "feather", "stackSize": 64}, {"id": 734, "displayName": "Gunpowder", "name": "gunpowder", "stackSize": 64}, {"id": 735, "displayName": "Wheat Seeds", "name": "wheat_seeds", "stackSize": 64}, {"id": 736, "displayName": "Wheat", "name": "wheat", "stackSize": 64}, {"id": 737, "displayName": "Bread", "name": "bread", "stackSize": 64}, {"id": 738, "displayName": "Leather Cap", "name": "leather_helmet", "stackSize": 1, "maxDurability": 55, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["leather"]}, {"id": 739, "displayName": "<PERSON><PERSON>", "name": "leather_chestplate", "stackSize": 1, "maxDurability": 80, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["leather"]}, {"id": 740, "displayName": "<PERSON><PERSON>", "name": "leather_leggings", "stackSize": 1, "maxDurability": 75, "enchantCategories": ["armor", "breakable", "wearable", "vanishable"], "repairWith": ["leather"]}, {"id": 741, "displayName": "<PERSON><PERSON>", "name": "leather_boots", "stackSize": 1, "maxDurability": 65, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["leather"]}, {"id": 742, "displayName": "Chainmail Helmet", "name": "chainmail_helmet", "stackSize": 1, "maxDurability": 165, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 743, "displayName": "Chainmail Chestplate", "name": "chainmail_chestplate", "stackSize": 1, "maxDurability": 240, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 744, "displayName": "Chainmail Leggings", "name": "chainmail_leggings", "stackSize": 1, "maxDurability": 225, "enchantCategories": ["armor", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 745, "displayName": "Chainmail Boots", "name": "chainmail_boots", "stackSize": 1, "maxDurability": 195, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 746, "displayName": "Iron Helmet", "name": "iron_helmet", "stackSize": 1, "maxDurability": 165, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 747, "displayName": "Iron Chestplate", "name": "iron_chestplate", "stackSize": 1, "maxDurability": 240, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 748, "displayName": "Iron Leggings", "name": "iron_leggings", "stackSize": 1, "maxDurability": 225, "enchantCategories": ["armor", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 749, "displayName": "Iron Boots", "name": "iron_boots", "stackSize": 1, "maxDurability": 195, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 750, "displayName": "Diamond Helmet", "name": "diamond_helmet", "stackSize": 1, "maxDurability": 363, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["diamond"]}, {"id": 751, "displayName": "Diamond Chestplate", "name": "diamond_chestplate", "stackSize": 1, "maxDurability": 528, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["diamond"]}, {"id": 752, "displayName": "Diamond Leggings", "name": "diamond_leggings", "stackSize": 1, "maxDurability": 495, "enchantCategories": ["armor", "breakable", "wearable", "vanishable"], "repairWith": ["diamond"]}, {"id": 753, "displayName": "Diamond Boots", "name": "diamond_boots", "stackSize": 1, "maxDurability": 429, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["diamond"]}, {"id": 754, "displayName": "Golden Helmet", "name": "golden_helmet", "stackSize": 1, "maxDurability": 77, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 755, "displayName": "Golden Chestplate", "name": "golden_chestplate", "stackSize": 1, "maxDurability": 112, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 756, "displayName": "Golden Leggings", "name": "golden_leggings", "stackSize": 1, "maxDurability": 105, "enchantCategories": ["armor", "breakable", "wearable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 757, "displayName": "Golden Boots", "name": "golden_boots", "stackSize": 1, "maxDurability": 91, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 758, "displayName": "Netherite Helmet", "name": "netherite_helmet", "stackSize": 1, "maxDurability": 407, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["netherite_ingot"]}, {"id": 759, "displayName": "Netherite Chestplate", "name": "netherite_chestplate", "stackSize": 1, "maxDurability": 592, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["netherite_ingot"]}, {"id": 760, "displayName": "Netherite Leggings", "name": "netherite_leggings", "stackSize": 1, "maxDurability": 555, "enchantCategories": ["armor", "breakable", "wearable", "vanishable"], "repairWith": ["netherite_ingot"]}, {"id": 761, "displayName": "Netherite Boots", "name": "netherite_boots", "stackSize": 1, "maxDurability": 481, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["netherite_ingot"]}, {"id": 762, "displayName": "Flint", "name": "flint", "stackSize": 64}, {"id": 763, "displayName": "Raw Porkchop", "name": "porkchop", "stackSize": 64}, {"id": 764, "displayName": "Cooked Porkchop", "name": "cooked_porkchop", "stackSize": 64}, {"id": 765, "displayName": "Painting", "name": "painting", "stackSize": 64}, {"id": 766, "displayName": "Golden Apple", "name": "golden_apple", "stackSize": 64}, {"id": 767, "displayName": "Enchanted Golden Apple", "name": "enchanted_golden_apple", "stackSize": 64}, {"id": 768, "displayName": "Oak Sign", "name": "oak_sign", "stackSize": 16}, {"id": 769, "displayName": "Spruce Sign", "name": "spruce_sign", "stackSize": 16}, {"id": 770, "displayName": "Birch Sign", "name": "birch_sign", "stackSize": 16}, {"id": 771, "displayName": "Jungle Sign", "name": "jungle_sign", "stackSize": 16}, {"id": 772, "displayName": "Acacia Sign", "name": "acacia_sign", "stackSize": 16}, {"id": 773, "displayName": "Dark Oak Sign", "name": "dark_oak_sign", "stackSize": 16}, {"id": 774, "displayName": "Crimson Sign", "name": "crimson_sign", "stackSize": 16}, {"id": 775, "displayName": "Warped Sign", "name": "warped_sign", "stackSize": 16}, {"id": 776, "displayName": "Bucket", "name": "bucket", "stackSize": 16}, {"id": 777, "displayName": "Water Bucket", "name": "water_bucket", "stackSize": 1}, {"id": 778, "displayName": "<PERSON><PERSON>et", "name": "lava_bucket", "stackSize": 1}, {"id": 779, "displayName": "Powder Snow Bucket", "name": "powder_snow_bucket", "stackSize": 1}, {"id": 780, "displayName": "Snowball", "name": "snowball", "stackSize": 16}, {"id": 781, "displayName": "Leather", "name": "leather", "stackSize": 64}, {"id": 782, "displayName": "Milk Bucket", "name": "milk_bucket", "stackSize": 1}, {"id": 783, "displayName": "Bucket of Pufferfish", "name": "pufferfish_bucket", "stackSize": 1}, {"id": 784, "displayName": "Bucket of Salmon", "name": "salmon_bucket", "stackSize": 1}, {"id": 785, "displayName": "Bucket of Cod", "name": "cod_bucket", "stackSize": 1}, {"id": 786, "displayName": "Bucket of Tropical Fish", "name": "tropical_fish_bucket", "stackSize": 1}, {"id": 787, "displayName": "Bucket of Axolotl", "name": "axolotl_bucket", "stackSize": 1}, {"id": 788, "displayName": "Brick", "name": "brick", "stackSize": 64}, {"id": 789, "displayName": "<PERSON>", "name": "clay_ball", "stackSize": 64}, {"id": 790, "displayName": "Dried Kelp Block", "name": "dried_kelp_block", "stackSize": 64}, {"id": 791, "displayName": "Paper", "name": "paper", "stackSize": 64}, {"id": 792, "displayName": "Book", "name": "book", "stackSize": 64}, {"id": 793, "displayName": "Slimeball", "name": "slime_ball", "stackSize": 64}, {"id": 794, "displayName": "Egg", "name": "egg", "stackSize": 16}, {"id": 795, "displayName": "<PERSON>mp<PERSON>", "name": "compass", "stackSize": 64}, {"id": 796, "displayName": "Bundle", "name": "bundle", "stackSize": 1}, {"id": 797, "displayName": "Fishing Rod", "name": "fishing_rod", "stackSize": 1, "maxDurability": 64, "enchantCategories": ["breakable", "fishing_rod", "vanishable"]}, {"id": 798, "displayName": "Clock", "name": "clock", "stackSize": 64}, {"id": 799, "displayName": "Spyglass", "name": "spyglass", "stackSize": 1}, {"id": 800, "displayName": "Glowstone Dust", "name": "glowstone_dust", "stackSize": 64}, {"id": 801, "displayName": "Raw Cod", "name": "cod", "stackSize": 64}, {"id": 802, "displayName": "Raw Salmon", "name": "salmon", "stackSize": 64}, {"id": 803, "displayName": "Tropical Fish", "name": "tropical_fish", "stackSize": 64}, {"id": 804, "displayName": "Pufferfish", "name": "pufferfish", "stackSize": 64}, {"id": 805, "displayName": "Cooked Cod", "name": "cooked_cod", "stackSize": 64}, {"id": 806, "displayName": "Cooked Salmon", "name": "cooked_salmon", "stackSize": 64}, {"id": 807, "displayName": "Ink Sac", "name": "ink_sac", "stackSize": 64}, {"id": 808, "displayName": "Glow Ink Sac", "name": "glow_ink_sac", "stackSize": 64}, {"id": 809, "displayName": "Cocoa Beans", "name": "cocoa_beans", "stackSize": 64}, {"id": 810, "displayName": "White Dye", "name": "white_dye", "stackSize": 64}, {"id": 811, "displayName": "Orange Dye", "name": "orange_dye", "stackSize": 64}, {"id": 812, "displayName": "<PERSON><PERSON><PERSON>", "name": "magenta_dye", "stackSize": 64}, {"id": 813, "displayName": "Light Blue Dye", "name": "light_blue_dye", "stackSize": 64}, {"id": 814, "displayName": "Yellow Dye", "name": "yellow_dye", "stackSize": 64}, {"id": 815, "displayName": "Lime Dye", "name": "lime_dye", "stackSize": 64}, {"id": 816, "displayName": "Pink Dye", "name": "pink_dye", "stackSize": 64}, {"id": 817, "displayName": "<PERSON>", "name": "gray_dye", "stackSize": 64}, {"id": 818, "displayName": "Light Gray D<PERSON>", "name": "light_gray_dye", "stackSize": 64}, {"id": 819, "displayName": "<PERSON><PERSON>", "name": "cyan_dye", "stackSize": 64}, {"id": 820, "displayName": "Purple Dye", "name": "purple_dye", "stackSize": 64}, {"id": 821, "displayName": "Blue Dye", "name": "blue_dye", "stackSize": 64}, {"id": 822, "displayName": "<PERSON>", "name": "brown_dye", "stackSize": 64}, {"id": 823, "displayName": "Green Dye", "name": "green_dye", "stackSize": 64}, {"id": 824, "displayName": "Red Dye", "name": "red_dye", "stackSize": 64}, {"id": 825, "displayName": "Black Dye", "name": "black_dye", "stackSize": 64}, {"id": 826, "displayName": "<PERSON>", "name": "bone_meal", "stackSize": 64}, {"id": 827, "displayName": "Bone", "name": "bone", "stackSize": 64}, {"id": 828, "displayName": "Sugar", "name": "sugar", "stackSize": 64}, {"id": 829, "displayName": "Cake", "name": "cake", "stackSize": 1}, {"id": 830, "displayName": "White Bed", "name": "white_bed", "stackSize": 1}, {"id": 831, "displayName": "Orange Bed", "name": "orange_bed", "stackSize": 1}, {"id": 832, "displayName": "Magenta Bed", "name": "magenta_bed", "stackSize": 1}, {"id": 833, "displayName": "Light Blue Bed", "name": "light_blue_bed", "stackSize": 1}, {"id": 834, "displayName": "Yellow Bed", "name": "yellow_bed", "stackSize": 1}, {"id": 835, "displayName": "Lime Bed", "name": "lime_bed", "stackSize": 1}, {"id": 836, "displayName": "Pink Bed", "name": "pink_bed", "stackSize": 1}, {"id": 837, "displayName": "Gray Bed", "name": "gray_bed", "stackSize": 1}, {"id": 838, "displayName": "Light Gray Bed", "name": "light_gray_bed", "stackSize": 1}, {"id": 839, "displayName": "<PERSON><PERSON>", "name": "cyan_bed", "stackSize": 1}, {"id": 840, "displayName": "Purple Bed", "name": "purple_bed", "stackSize": 1}, {"id": 841, "displayName": "Blue Bed", "name": "blue_bed", "stackSize": 1}, {"id": 842, "displayName": "Brown Bed", "name": "brown_bed", "stackSize": 1}, {"id": 843, "displayName": "Green Bed", "name": "green_bed", "stackSize": 1}, {"id": 844, "displayName": "Red Bed", "name": "red_bed", "stackSize": 1}, {"id": 845, "displayName": "Black Bed", "name": "black_bed", "stackSize": 1}, {"id": 846, "displayName": "<PERSON><PERSON>", "name": "cookie", "stackSize": 64}, {"id": 847, "displayName": "Map", "name": "filled_map", "stackSize": 64}, {"id": 848, "displayName": "Shears", "name": "shears", "stackSize": 1, "maxDurability": 238, "enchantCategories": ["breakable", "vanishable"]}, {"id": 849, "displayName": "<PERSON><PERSON>", "name": "melon_slice", "stackSize": 64}, {"id": 850, "displayName": "<PERSON><PERSON>", "name": "dried_kelp", "stackSize": 64}, {"id": 851, "displayName": "<PERSON><PERSON><PERSON> Seeds", "name": "pumpkin_seeds", "stackSize": 64}, {"id": 852, "displayName": "<PERSON>on Seeds", "name": "melon_seeds", "stackSize": 64}, {"id": 853, "displayName": "Raw Beef", "name": "beef", "stackSize": 64}, {"id": 854, "displayName": "Steak", "name": "cooked_beef", "stackSize": 64}, {"id": 855, "displayName": "Raw Chicken", "name": "chicken", "stackSize": 64}, {"id": 856, "displayName": "Cooked Chicken", "name": "cooked_chicken", "stackSize": 64}, {"id": 857, "displayName": "Rotten Flesh", "name": "rotten_flesh", "stackSize": 64}, {"id": 858, "displayName": "<PERSON><PERSON>", "name": "ender_pearl", "stackSize": 16}, {"id": 859, "displayName": "<PERSON>", "name": "blaze_rod", "stackSize": 64}, {"id": 860, "displayName": "Ghast Tear", "name": "ghast_tear", "stackSize": 64}, {"id": 861, "displayName": "Gold Nugget", "name": "gold_nugget", "stackSize": 64}, {"id": 862, "displayName": "Nether Wart", "name": "nether_wart", "stackSize": 64}, {"id": 863, "displayName": "Potion", "name": "potion", "stackSize": 1}, {"id": 864, "displayName": "Glass Bottle", "name": "glass_bottle", "stackSize": 64}, {"id": 865, "displayName": "Spider Eye", "name": "spider_eye", "stackSize": 64}, {"id": 866, "displayName": "Fermented Spider Eye", "name": "fermented_spider_eye", "stackSize": 64}, {"id": 867, "displayName": "<PERSON>", "name": "blaze_powder", "stackSize": 64}, {"id": 868, "displayName": "Magma Cream", "name": "magma_cream", "stackSize": 64}, {"id": 869, "displayName": "Brewing Stand", "name": "brewing_stand", "stackSize": 64}, {"id": 870, "displayName": "<PERSON><PERSON><PERSON>", "name": "cauldron", "stackSize": 64}, {"id": 871, "displayName": "Eye of <PERSON>er", "name": "ender_eye", "stackSize": 64}, {"id": 872, "displayName": "Glistering <PERSON><PERSON>", "name": "glistering_melon_slice", "stackSize": 64}, {"id": 873, "displayName": "Axolotl Spawn Egg", "name": "axolotl_spawn_egg", "stackSize": 64}, {"id": 874, "displayName": "Bat Spawn Egg", "name": "bat_spawn_egg", "stackSize": 64}, {"id": 875, "displayName": "Bee Spawn Egg", "name": "bee_spawn_egg", "stackSize": 64}, {"id": 876, "displayName": "Blaze Spawn Egg", "name": "blaze_spawn_egg", "stackSize": 64}, {"id": 877, "displayName": "Cat Spawn Egg", "name": "cat_spawn_egg", "stackSize": 64}, {"id": 878, "displayName": "Cave Spider Spawn Egg", "name": "cave_spider_spawn_egg", "stackSize": 64}, {"id": 879, "displayName": "Chicken Spawn Egg", "name": "chicken_spawn_egg", "stackSize": 64}, {"id": 880, "displayName": "Cod Spawn Egg", "name": "cod_spawn_egg", "stackSize": 64}, {"id": 881, "displayName": "Cow Spawn Egg", "name": "cow_spawn_egg", "stackSize": 64}, {"id": 882, "displayName": "Creeper Spawn Egg", "name": "creeper_spawn_egg", "stackSize": 64}, {"id": 883, "displayName": "Dolphin Spawn Egg", "name": "dolphin_spawn_egg", "stackSize": 64}, {"id": 884, "displayName": "Donkey Spawn Egg", "name": "donkey_spawn_egg", "stackSize": 64}, {"id": 885, "displayName": "Drowned Spawn Egg", "name": "drowned_spawn_egg", "stackSize": 64}, {"id": 886, "displayName": "Elder Guardian Spawn Egg", "name": "elder_guardian_spawn_egg", "stackSize": 64}, {"id": 887, "displayName": "Enderman Spawn Egg", "name": "enderman_spawn_egg", "stackSize": 64}, {"id": 888, "displayName": "Endermite Spawn Egg", "name": "endermite_spawn_egg", "stackSize": 64}, {"id": 889, "displayName": "Evoker Spawn Egg", "name": "evoker_spawn_egg", "stackSize": 64}, {"id": 890, "displayName": "Fox Spawn Egg", "name": "fox_spawn_egg", "stackSize": 64}, {"id": 891, "displayName": "Ghast Spawn Egg", "name": "ghast_spawn_egg", "stackSize": 64}, {"id": 892, "displayName": "Glow Squid Spawn Egg", "name": "glow_squid_spawn_egg", "stackSize": 64}, {"id": 893, "displayName": "Goat Spawn Egg", "name": "goat_spawn_egg", "stackSize": 64}, {"id": 894, "displayName": "Guardian Spawn Egg", "name": "guardian_spawn_egg", "stackSize": 64}, {"id": 895, "displayName": "Hoglin Spawn Egg", "name": "hoglin_spawn_egg", "stackSize": 64}, {"id": 896, "displayName": "Horse Spawn Egg", "name": "horse_spawn_egg", "stackSize": 64}, {"id": 897, "displayName": "Husk Spawn Egg", "name": "husk_spawn_egg", "stackSize": 64}, {"id": 898, "displayName": "Llama Spawn Egg", "name": "llama_spawn_egg", "stackSize": 64}, {"id": 899, "displayName": "Magma Cube Spawn Egg", "name": "magma_cube_spawn_egg", "stackSize": 64}, {"id": 900, "displayName": "Mooshroom Spawn Egg", "name": "mooshroom_spawn_egg", "stackSize": 64}, {"id": 901, "displayName": "Mule Spawn Egg", "name": "mule_spawn_egg", "stackSize": 64}, {"id": 902, "displayName": "Ocelot Spawn Egg", "name": "ocelot_spawn_egg", "stackSize": 64}, {"id": 903, "displayName": "Panda Spawn Egg", "name": "panda_spawn_egg", "stackSize": 64}, {"id": 904, "displayName": "Parrot Spawn Egg", "name": "parrot_spawn_egg", "stackSize": 64}, {"id": 905, "displayName": "Phantom Spawn Egg", "name": "phantom_spawn_egg", "stackSize": 64}, {"id": 906, "displayName": "Pig Spawn Egg", "name": "pig_spawn_egg", "stackSize": 64}, {"id": 907, "displayName": "Piglin Spawn Egg", "name": "piglin_spawn_egg", "stackSize": 64}, {"id": 908, "displayName": "Piglin Brute Spawn Egg", "name": "piglin_brute_spawn_egg", "stackSize": 64}, {"id": 909, "displayName": "Pillager Spawn Egg", "name": "pillager_spawn_egg", "stackSize": 64}, {"id": 910, "displayName": "Polar Bear Spawn Egg", "name": "polar_bear_spawn_egg", "stackSize": 64}, {"id": 911, "displayName": "Pufferfish Spawn Egg", "name": "pufferfish_spawn_egg", "stackSize": 64}, {"id": 912, "displayName": "Rabbit Spawn Egg", "name": "rabbit_spawn_egg", "stackSize": 64}, {"id": 913, "displayName": "Ravager Spawn Egg", "name": "ravager_spawn_egg", "stackSize": 64}, {"id": 914, "displayName": "Salmon Spawn Egg", "name": "salmon_spawn_egg", "stackSize": 64}, {"id": 915, "displayName": "Sheep Spawn Egg", "name": "sheep_spawn_egg", "stackSize": 64}, {"id": 916, "displayName": "Shulker Spawn Egg", "name": "shulker_spawn_egg", "stackSize": 64}, {"id": 917, "displayName": "Silverfish Spawn Egg", "name": "silverfish_spawn_egg", "stackSize": 64}, {"id": 918, "displayName": "Skeleton Spawn Egg", "name": "skeleton_spawn_egg", "stackSize": 64}, {"id": 919, "displayName": "Skeleton Horse Spawn Egg", "name": "skeleton_horse_spawn_egg", "stackSize": 64}, {"id": 920, "displayName": "Slime Spawn Egg", "name": "slime_spawn_egg", "stackSize": 64}, {"id": 921, "displayName": "Spider Spawn Egg", "name": "spider_spawn_egg", "stackSize": 64}, {"id": 922, "displayName": "Squid Spawn Egg", "name": "squid_spawn_egg", "stackSize": 64}, {"id": 923, "displayName": "Stray Spawn Egg", "name": "stray_spawn_egg", "stackSize": 64}, {"id": 924, "displayName": "Strider Spawn Egg", "name": "strider_spawn_egg", "stackSize": 64}, {"id": 925, "displayName": "Trader <PERSON>lama Spawn Egg", "name": "trader_llama_spawn_egg", "stackSize": 64}, {"id": 926, "displayName": "Tropical Fish Spawn Egg", "name": "tropical_fish_spawn_egg", "stackSize": 64}, {"id": 927, "displayName": "Turtle Spawn Egg", "name": "turtle_spawn_egg", "stackSize": 64}, {"id": 928, "displayName": "Vex Spawn Egg", "name": "vex_spawn_egg", "stackSize": 64}, {"id": 929, "displayName": "Villager Spawn Egg", "name": "villager_spawn_egg", "stackSize": 64}, {"id": 930, "displayName": "Vindicator Spawn Egg", "name": "vindicator_spawn_egg", "stackSize": 64}, {"id": 931, "displayName": "Wandering Trader Spawn Egg", "name": "wandering_trader_spawn_egg", "stackSize": 64}, {"id": 932, "displayName": "Witch Spawn Egg", "name": "witch_spawn_egg", "stackSize": 64}, {"id": 933, "displayName": "Wither Skeleton Spawn Egg", "name": "wither_skeleton_spawn_egg", "stackSize": 64}, {"id": 934, "displayName": "Wolf Spawn Egg", "name": "wolf_spawn_egg", "stackSize": 64}, {"id": 935, "displayName": "Zoglin Spawn Egg", "name": "zoglin_spawn_egg", "stackSize": 64}, {"id": 936, "displayName": "Zombie Spawn Egg", "name": "zombie_spawn_egg", "stackSize": 64}, {"id": 937, "displayName": "Zombie Horse Spawn Egg", "name": "zombie_horse_spawn_egg", "stackSize": 64}, {"id": 938, "displayName": "Zombie Villager Spawn Egg", "name": "zombie_villager_spawn_egg", "stackSize": 64}, {"id": 939, "displayName": "Zombified Piglin Spawn Egg", "name": "zombified_piglin_spawn_egg", "stackSize": 64}, {"id": 940, "displayName": "Bottle o' Enchanting", "name": "experience_bottle", "stackSize": 64}, {"id": 941, "displayName": "Fire Charge", "name": "fire_charge", "stackSize": 64}, {"id": 942, "displayName": "Book and Quill", "name": "writable_book", "stackSize": 1}, {"id": 943, "displayName": "Written Book", "name": "written_book", "stackSize": 16}, {"id": 944, "displayName": "<PERSON><PERSON>", "name": "item_frame", "stackSize": 64}, {"id": 945, "displayName": "G<PERSON> Item <PERSON>", "name": "glow_item_frame", "stackSize": 64}, {"id": 946, "displayName": "Flower Pot", "name": "flower_pot", "stackSize": 64}, {"id": 947, "displayName": "Carrot", "name": "carrot", "stackSize": 64}, {"id": 948, "displayName": "Potato", "name": "potato", "stackSize": 64}, {"id": 949, "displayName": "Baked Potato", "name": "baked_potato", "stackSize": 64}, {"id": 950, "displayName": "Poisonous Potato", "name": "poisonous_potato", "stackSize": 64}, {"id": 951, "displayName": "Empty Map", "name": "map", "stackSize": 64}, {"id": 952, "displayName": "Golden Carrot", "name": "golden_carrot", "stackSize": 64}, {"id": 953, "displayName": "Skeleton Skull", "name": "skeleton_skull", "stackSize": 64}, {"id": 954, "displayName": "Wither Skeleton Skull", "name": "wither_skeleton_skull", "stackSize": 64}, {"id": 955, "displayName": "Player Head", "name": "player_head", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"id": 956, "displayName": "Zombie Head", "name": "zombie_head", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"id": 957, "displayName": "Creeper Head", "name": "creeper_head", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"id": 958, "displayName": "Dragon Head", "name": "dragon_head", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"id": 959, "displayName": "Nether Star", "name": "nether_star", "stackSize": 64}, {"id": 960, "displayName": "Pumpkin Pie", "name": "pumpkin_pie", "stackSize": 64}, {"id": 961, "displayName": "Firework Rocket", "name": "firework_rocket", "stackSize": 64}, {"id": 962, "displayName": "Firework Star", "name": "firework_star", "stackSize": 64}, {"id": 963, "displayName": "Enchanted Book", "name": "enchanted_book", "stackSize": 1}, {"id": 964, "displayName": "Nether Brick", "name": "nether_brick", "stackSize": 64}, {"id": 965, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "prismarine_shard", "stackSize": 64}, {"id": 966, "displayName": "Prismarine Crystals", "name": "prismarine_crystals", "stackSize": 64}, {"id": 967, "displayName": "Raw Rabbit", "name": "rabbit", "stackSize": 64}, {"id": 968, "displayName": "Cooked Rabbit", "name": "cooked_rabbit", "stackSize": 64}, {"id": 969, "displayName": "Rabbit Stew", "name": "rabbit_stew", "stackSize": 1}, {"id": 970, "displayName": "<PERSON>'s Foot", "name": "rabbit_foot", "stackSize": 64}, {"id": 971, "displayName": "<PERSON>", "name": "rabbit_hide", "stackSize": 64}, {"id": 972, "displayName": "Armor Stand", "name": "armor_stand", "stackSize": 16}, {"id": 973, "displayName": "Iron Horse Armor", "name": "iron_horse_armor", "stackSize": 1}, {"id": 974, "displayName": "Golden Horse Armor", "name": "golden_horse_armor", "stackSize": 1}, {"id": 975, "displayName": "Diamond Horse Armor", "name": "diamond_horse_armor", "stackSize": 1}, {"id": 976, "displayName": "Leather Horse Armor", "name": "leather_horse_armor", "stackSize": 1}, {"id": 977, "displayName": "Lead", "name": "lead", "stackSize": 64}, {"id": 978, "displayName": "Name Tag", "name": "name_tag", "stackSize": 64}, {"id": 979, "displayName": "Minecart with Command Block", "name": "command_block_minecart", "stackSize": 1}, {"id": 980, "displayName": "<PERSON>", "name": "mutton", "stackSize": 64}, {"id": 981, "displayName": "Cooked <PERSON>tton", "name": "cooked_mutton", "stackSize": 64}, {"id": 982, "displayName": "White Banner", "name": "white_banner", "stackSize": 16}, {"id": 983, "displayName": "Orange Banner", "name": "orange_banner", "stackSize": 16}, {"id": 984, "displayName": "Magenta Banner", "name": "magenta_banner", "stackSize": 16}, {"id": 985, "displayName": "Light Blue Banner", "name": "light_blue_banner", "stackSize": 16}, {"id": 986, "displayName": "Yellow Banner", "name": "yellow_banner", "stackSize": 16}, {"id": 987, "displayName": "Lime Banner", "name": "lime_banner", "stackSize": 16}, {"id": 988, "displayName": "Pink Banner", "name": "pink_banner", "stackSize": 16}, {"id": 989, "displayName": "<PERSON>", "name": "gray_banner", "stackSize": 16}, {"id": 990, "displayName": "<PERSON> Gray Banner", "name": "light_gray_banner", "stackSize": 16}, {"id": 991, "displayName": "<PERSON><PERSON>", "name": "cyan_banner", "stackSize": 16}, {"id": 992, "displayName": "<PERSON> Banner", "name": "purple_banner", "stackSize": 16}, {"id": 993, "displayName": "Blue Banner", "name": "blue_banner", "stackSize": 16}, {"id": 994, "displayName": "<PERSON>", "name": "brown_banner", "stackSize": 16}, {"id": 995, "displayName": "<PERSON> Banner", "name": "green_banner", "stackSize": 16}, {"id": 996, "displayName": "Red Banner", "name": "red_banner", "stackSize": 16}, {"id": 997, "displayName": "Black Banner", "name": "black_banner", "stackSize": 16}, {"id": 998, "displayName": "End Crystal", "name": "end_crystal", "stackSize": 64}, {"id": 999, "displayName": "Chorus Fruit", "name": "chorus_fruit", "stackSize": 64}, {"id": 1000, "displayName": "Popped Chorus Fruit", "name": "popped_chorus_fruit", "stackSize": 64}, {"id": 1001, "displayName": "Beetroot", "name": "beetroot", "stackSize": 64}, {"id": 1002, "displayName": "Beetroot Seeds", "name": "beetroot_seeds", "stackSize": 64}, {"id": 1003, "displayName": "Beetroot Soup", "name": "beetroot_soup", "stackSize": 1}, {"id": 1004, "displayName": "Dragon's Breath", "name": "dragon_breath", "stackSize": 64}, {"id": 1005, "displayName": "Splash Potion", "name": "splash_potion", "stackSize": 1}, {"id": 1006, "displayName": "Spectral Arrow", "name": "spectral_arrow", "stackSize": 64}, {"id": 1007, "displayName": "Tipped Arrow", "name": "tipped_arrow", "stackSize": 64}, {"id": 1008, "displayName": "Lingering Potion", "name": "lingering_potion", "stackSize": 1}, {"id": 1009, "displayName": "Shield", "name": "shield", "stackSize": 1, "maxDurability": 336, "enchantCategories": ["breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"]}, {"id": 1010, "displayName": "Totem of Undying", "name": "totem_of_undying", "stackSize": 1}, {"id": 1011, "displayName": "Shulker Shell", "name": "shulker_shell", "stackSize": 64}, {"id": 1012, "displayName": "Iron Nugget", "name": "iron_nugget", "stackSize": 64}, {"id": 1013, "displayName": "Knowledge Book", "name": "knowledge_book", "stackSize": 1}, {"id": 1014, "displayName": "Debug Stick", "name": "debug_stick", "stackSize": 1}, {"id": 1015, "displayName": "13 Disc", "name": "music_disc_13", "stackSize": 1}, {"id": 1016, "displayName": "Cat Disc", "name": "music_disc_cat", "stackSize": 1}, {"id": 1017, "displayName": "Blocks Disc", "name": "music_disc_blocks", "stackSize": 1}, {"id": 1018, "displayName": "Chirp Disc", "name": "music_disc_chirp", "stackSize": 1}, {"id": 1019, "displayName": "Far Disc", "name": "music_disc_far", "stackSize": 1}, {"id": 1020, "displayName": "Mall Disc", "name": "music_disc_mall", "stackSize": 1}, {"id": 1021, "displayName": "Mellohi Disc", "name": "music_disc_mellohi", "stackSize": 1}, {"id": 1022, "displayName": "Stal Disc", "name": "music_disc_stal", "stackSize": 1}, {"id": 1023, "displayName": "Strad Disc", "name": "music_disc_strad", "stackSize": 1}, {"id": 1024, "displayName": "Ward Disc", "name": "music_disc_ward", "stackSize": 1}, {"id": 1025, "displayName": "11 Disc", "name": "music_disc_11", "stackSize": 1}, {"id": 1026, "displayName": "Wait Disc", "name": "music_disc_wait", "stackSize": 1}, {"id": 1027, "displayName": "Music Disc", "name": "music_disc_pigstep", "stackSize": 1}, {"id": 1028, "displayName": "Trident", "name": "trident", "stackSize": 1, "maxDurability": 250, "enchantCategories": ["breakable", "vanishable", "trident"]}, {"id": 1029, "displayName": "Phantom Membrane", "name": "phantom_membrane", "stackSize": 64}, {"id": 1030, "displayName": "Nautilus Shell", "name": "nautilus_shell", "stackSize": 64}, {"id": 1031, "displayName": "Heart of the Sea", "name": "heart_of_the_sea", "stackSize": 64}, {"id": 1032, "displayName": "Crossbow", "name": "crossbow", "stackSize": 1, "maxDurability": 326, "enchantCategories": ["breakable", "vanishable", "crossbow"]}, {"id": 1033, "displayName": "Suspicious Stew", "name": "suspicious_stew", "stackSize": 1}, {"id": 1034, "displayName": "Loom", "name": "loom", "stackSize": 64}, {"id": 1035, "displayName": "<PERSON>", "name": "flower_banner_pattern", "stackSize": 1}, {"id": 1036, "displayName": "<PERSON>", "name": "creeper_banner_pattern", "stackSize": 1}, {"id": 1037, "displayName": "<PERSON>", "name": "skull_banner_pattern", "stackSize": 1}, {"id": 1038, "displayName": "<PERSON>", "name": "mojang_banner_pattern", "stackSize": 1}, {"id": 1039, "displayName": "<PERSON>", "name": "globe_banner_pattern", "stackSize": 1}, {"id": 1040, "displayName": "<PERSON>", "name": "piglin_banner_pattern", "stackSize": 1}, {"id": 1041, "displayName": "Composter", "name": "composter", "stackSize": 64}, {"id": 1042, "displayName": "Barrel", "name": "barrel", "stackSize": 64}, {"id": 1043, "displayName": "Smoker", "name": "smoker", "stackSize": 64}, {"id": 1044, "displayName": "Blast Furnace", "name": "blast_furnace", "stackSize": 64}, {"id": 1045, "displayName": "Cartography Table", "name": "cartography_table", "stackSize": 64}, {"id": 1046, "displayName": "Fletching Table", "name": "fletching_table", "stackSize": 64}, {"id": 1047, "displayName": "Grindstone", "name": "grindstone", "stackSize": 64}, {"id": 1048, "displayName": "Smithing Table", "name": "smithing_table", "stackSize": 64}, {"id": 1049, "displayName": "<PERSON><PERSON><PERSON>", "name": "stonecutter", "stackSize": 64}, {"id": 1050, "displayName": "Bell", "name": "bell", "stackSize": 64}, {"id": 1051, "displayName": "Lantern", "name": "lantern", "stackSize": 64}, {"id": 1052, "displayName": "Soul Lantern", "name": "soul_lantern", "stackSize": 64}, {"id": 1053, "displayName": "Sweet Berries", "name": "sweet_berries", "stackSize": 64}, {"id": 1054, "displayName": "Glow Berries", "name": "glow_berries", "stackSize": 64}, {"id": 1055, "displayName": "Campfire", "name": "campfire", "stackSize": 64}, {"id": 1056, "displayName": "Soul Campfire", "name": "soul_campfire", "stackSize": 64}, {"id": 1057, "displayName": "Shroomlight", "name": "shroomlight", "stackSize": 64}, {"id": 1058, "displayName": "Honeycomb", "name": "honeycomb", "stackSize": 64}, {"id": 1059, "displayName": "Bee Nest", "name": "bee_nest", "stackSize": 64}, {"id": 1060, "displayName": "Beehive", "name": "beehive", "stackSize": 64}, {"id": 1061, "displayName": "<PERSON>", "name": "honey_bottle", "stackSize": 16}, {"id": 1062, "displayName": "Honeycomb Block", "name": "honeycomb_block", "stackSize": 64}, {"id": 1063, "displayName": "Lodestone", "name": "lodestone", "stackSize": 64}, {"id": 1064, "displayName": "Crying Obsidian", "name": "crying_obsidian", "stackSize": 64}, {"id": 1065, "displayName": "Blackstone", "name": "blackstone", "stackSize": 64}, {"id": 1066, "displayName": "Blackstone Slab", "name": "blackstone_slab", "stackSize": 64}, {"id": 1067, "displayName": "Blackstone Stairs", "name": "blackstone_stairs", "stackSize": 64}, {"id": 1068, "displayName": "Gilded Blackstone", "name": "gilded_blackstone", "stackSize": 64}, {"id": 1069, "displayName": "Polished Blackstone", "name": "polished_blackstone", "stackSize": 64}, {"id": 1070, "displayName": "Polished Blackstone Slab", "name": "polished_blackstone_slab", "stackSize": 64}, {"id": 1071, "displayName": "Polished Blackstone Stairs", "name": "polished_blackstone_stairs", "stackSize": 64}, {"id": 1072, "displayName": "Chiseled Polished Blackstone", "name": "chiseled_polished_blackstone", "stackSize": 64}, {"id": 1073, "displayName": "Polished Blackstone Bricks", "name": "polished_blackstone_bricks", "stackSize": 64}, {"id": 1074, "displayName": "Polished Blackstone Brick Slab", "name": "polished_blackstone_brick_slab", "stackSize": 64}, {"id": 1075, "displayName": "Polished Blackstone Brick Stairs", "name": "polished_blackstone_brick_stairs", "stackSize": 64}, {"id": 1076, "displayName": "Cracked Polished Blackstone Bricks", "name": "cracked_polished_blackstone_bricks", "stackSize": 64}, {"id": 1077, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "respawn_anchor", "stackSize": 64}, {"id": 1078, "displayName": "Candle", "name": "candle", "stackSize": 64}, {"id": 1079, "displayName": "White Candle", "name": "white_candle", "stackSize": 64}, {"id": 1080, "displayName": "Orange Candle", "name": "orange_candle", "stackSize": 64}, {"id": 1081, "displayName": "<PERSON><PERSON><PERSON>", "name": "magenta_candle", "stackSize": 64}, {"id": 1082, "displayName": "Light Blue Candle", "name": "light_blue_candle", "stackSize": 64}, {"id": 1083, "displayName": "Yellow Candle", "name": "yellow_candle", "stackSize": 64}, {"id": 1084, "displayName": "<PERSON><PERSON>", "name": "lime_candle", "stackSize": 64}, {"id": 1085, "displayName": "Pink Candle", "name": "pink_candle", "stackSize": 64}, {"id": 1086, "displayName": "<PERSON>", "name": "gray_candle", "stackSize": 64}, {"id": 1087, "displayName": "Light Gray Candle", "name": "light_gray_candle", "stackSize": 64}, {"id": 1088, "displayName": "<PERSON><PERSON>", "name": "cyan_candle", "stackSize": 64}, {"id": 1089, "displayName": "Purple Candle", "name": "purple_candle", "stackSize": 64}, {"id": 1090, "displayName": "Blue Candle", "name": "blue_candle", "stackSize": 64}, {"id": 1091, "displayName": "<PERSON> Candle", "name": "brown_candle", "stackSize": 64}, {"id": 1092, "displayName": "Green Candle", "name": "green_candle", "stackSize": 64}, {"id": 1093, "displayName": "<PERSON> Candle", "name": "red_candle", "stackSize": 64}, {"id": 1094, "displayName": "Black Candle", "name": "black_candle", "stackSize": 64}, {"id": 1095, "displayName": "Small Amethyst Bud", "name": "small_amethyst_bud", "stackSize": 64}, {"id": 1096, "displayName": "Medium Amethyst Bud", "name": "medium_amethyst_bud", "stackSize": 64}, {"id": 1097, "displayName": "Large Amethyst Bud", "name": "large_amethyst_bud", "stackSize": 64}, {"id": 1098, "displayName": "Amethyst Cluster", "name": "amethyst_cluster", "stackSize": 64}, {"id": 1099, "displayName": "Pointed Dripstone", "name": "pointed_dripstone", "stackSize": 64}]