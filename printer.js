const mineflayer = require('mineflayer');
const config = require('./config.json');
const { performance } = require('perf_hooks');
const fs = require('fs');
const Vec3 = require('vec3').Vec3;
const { pathfinder, Movements } = require('mineflayer-pathfinder');
const { GoalBlock } = require('mineflayer-pathfinder').goals;
const schem = require('./data.json');
const mcData = require('minecraft-data')(1.8);
const Item = require('prismarine-item')('1.8');

let queue = false;
let inventoryReady = true;

let bot = mineflayer.createBot({
    host: config.ServerIP,
    username: config.Username,
    port: config.Port || 25565,
    viewDistance: 'tiny'
});

bot.loadPlugin(pathfinder);

bot.on('login', () => {
    console.log(`${config.Username} logged in to ${config.ServerIP}!`);
});

bot.on('chat', async function (username, message) {
    if (username === bot.username) return;

    if (message === 'tp') {
        bot.chat('/tp Xiovz');
    }

    if (message === 'print') {
        const startX = Math.floor(bot.player.entity.position.x);
        const startY = Math.floor(bot.player.entity.position.y);
        const startZ = Math.floor(bot.player.entity.position.z);

        let allblocks = [];
        let currentlayer = [];
        let keys = Object.keys(schem);

        for (const key of keys) {
            allblocks.push(...schem[key]);
        }

        const startTime = performance.now();
        await runLayer();
        const time = (performance.now() - startTime).toFixed(2);
        console.log(`\n[✅] Finished printing in ${time}ms`);
        console.log(`[🎉] ALL DONE! Bot finished the full print task.`);

        async function runLayer() {
            if (keys.length === 0) return;
            const layerKey = keys.shift();
            const layer = schem[layerKey];
            if (!layer || layer.length === 0) {
                return await runLayer();
            }
            currentlayer = [...layer];

            // ✅ Fixed: Zigzag pattern along Z-axis with 3-block segments
            const rowMap = {};
            for (const block of currentlayer) {
                const z = block.coords.z;
                if (!rowMap[z]) rowMap[z] = [];
                rowMap[z].push(block);
            }

            const sortedZ = Object.keys(rowMap).map(Number).sort((a, b) => a - b);
            let organized = [];

            for (const z of sortedZ) {
                const row = rowMap[z];
                row.sort((a, b) => a.coords.x - b.coords.x);
                for (let i = 0; i < row.length; i += 3) {
                    const segment = row.slice(i, i + 3);
                    if (z % 2 === 0) {
                        organized.push(...segment);
                    } else {
                        organized.push(...segment.reverse());
                    }
                }
            }

            currentlayer = organized;
            await runQueue();
        }

        function nearbyBlocksLayer(position) {
            return currentlayer.filter(element => {
                return Math.abs(element.coords.x + startX - position.x) < config.Range - 0.5 &&
                    Math.abs(element.coords.y + startY - position.y) < config.Range - 0.5 &&
                    Math.abs(element.coords.z + startZ - position.z) < config.Range - 0.5;
            });
        }

        async function runQueue() {
            const nearbylayer = nearbyBlocksLayer(bot.player.entity.position);

            if (nearbylayer.length === 0) {
                const defaultMove = new Movements(bot, mcData);
                bot.pathfinder.setMovements(defaultMove);
                if (currentlayer.length < 1) return await runLayer();

                const goalPos = new Vec3(
                    currentlayer[0].coords.x + startX - 1,
                    currentlayer[0].coords.y + startY + 1,
                    currentlayer[0].coords.z + startZ
                );

                bot.pathfinder.setGoal(new GoalBlock(goalPos.x, goalPos.y, goalPos.z));

                await new Promise(resolve => {
                    let attempts = 0;
                    const maxAttempts = 20;

                    const checkInterval = setInterval(async () => {
                        const dist = bot.entity.position.distanceTo(goalPos);
                        if (dist < 2) {
                            clearInterval(checkInterval);
                            resolve(await runQueue());
                        }
                        attempts++;
                        if (attempts >= maxAttempts) {
                            console.log(`[⚠] Movement timeout to ${goalPos}, continuing...`);
                            clearInterval(checkInterval);
                            resolve(await runQueue());
                        }
                    }, 500);
                });

            } else {
                let index = 0;
                await new Promise(resolve => {
                    const timer = setInterval(async () => {
                        if (currentlayer.length < 1) {
                            clearInterval(timer);
                            resolve(await runLayer());
                        } else if (index >= nearbylayer.length) {
                            clearInterval(timer);
                            resolve(await runQueue());
                        } else if (!queue && inventoryReady) {
                            const block = nearbylayer[index];
                            const pos = new Vec3(
                                block.coords.x + startX,
                                block.coords.y + startY,
                                block.coords.z + startZ
                            );

                            const botPos = bot.entity.position.floored();
                            if (botPos.equals(pos)) {
                                bot.setControlState('jump', true);
                                setTimeout(() => bot.setControlState('jump', false), 200);
                                index++;
                                return;
                            }

                            inventoryReady = false;
                            queue = true;
                            await runBlock(block);
                            queue = false;
                            index++;
                            inventoryReady = true;
                        }
                    }, config.blockDelay);
                });
            }
        }

        async function runBlock(block) {
            const pos = new Vec3(
                block.coords.x + startX,
                block.coords.y + startY,
                block.coords.z + startZ
            );

            const existing = bot.blockAt(pos);
            if (existing && (existing.type !== block.type || existing.metadata !== block.metadata)) {
                try {
                    await bot.dig(existing, true);
                    console.log(`[🔨] Fixed wrong block at ${pos}`);
                } catch (e) {
                    console.log(`[⚠] Couldn't dig block at ${pos}: ${e.message}`);
                    return;
                }
            } else if (existing && existing.type === block.type && existing.metadata === block.metadata) {
                currentlayer = currentlayer.filter(b => b !== block);
                allblocks = allblocks.filter(b => b !== block);
                return;
            }

            try {
                const item = new Item(block.type, 64, block.metadata);
                for (let i = 36; i <= 44; i++) {
                    await bot.creative.setInventorySlot(i, item);
                }
            } catch (err) {
                console.log(`[⚠] Inventory set failed: ${err.message}`);
                return;
            }

            const faces = [
                { offset: new Vec3(0, 1, 0), side: bot.blockAt(pos.offset(0, -1, 0)) },
                { offset: new Vec3(0, -1, 0), side: bot.blockAt(pos.offset(0, 1, 0)) },
                { offset: new Vec3(0, 0, -1), side: bot.blockAt(pos.offset(0, 0, 1)) },
                { offset: new Vec3(0, 0, 1), side: bot.blockAt(pos.offset(0, 0, -1)) },
                { offset: new Vec3(1, 0, 0), side: bot.blockAt(pos.offset(-1, 0, 0)) },
                { offset: new Vec3(-1, 0, 0), side: bot.blockAt(pos.offset(1, 0, 0)) },
            ];

            for (let face of faces) {
                if (face.side && face.side.name !== 'air') {
                    try {
                        await bot.placeBlock(face.side, face.offset);
                        console.log(`[✔] Placed at ${pos}`);
                        currentlayer = currentlayer.filter(b => b !== block);
                        allblocks = allblocks.filter(b => b !== block);
                    } catch (err) {
                        console.log(`[✘] Failed at ${pos}: ${err.message}`);
                    }
                    return;
                }
            }

            console.log(`[⚠] Skipped block at ${pos} (no face)`);
        }
    }
});
