const mineflayer = require('mineflayer');
const fs = require('fs');
const Vec3 = require('vec3');

// Load configuration
let config;
try {
    config = JSON.parse(fs.readFileSync('config.json', 'utf8'));
} catch (error) {
    console.error('❌ Error loading config.json:', error.message);
    process.exit(1);
}

class SchematicPrinter {
    constructor() {
        this.bot = null;
        this.schematicData = null;
        this.isPrinting = false;
        this.printedBlocks = 0;
        this.skippedBlocks = 0;
        this.startTime = null;
        this.buildPosition = null;
        this.currentBlockName = null;
        this.maxRange = 5;
    }

    connect() {
        console.log('🔗 Connecting to Minecraft server...');
        this.bot = mineflayer.createBot(config.server);

        this.bot.on('login', () => {
            console.log(`✅ Bot logged in as ${this.bot.username}`);
        });

        this.bot.on('spawn', () => {
            console.log('🌍 Bot spawned in the world');
            console.log(`📍 Bot position: (${this.bot.entity.position.x.toFixed(1)}, ${this.bot.entity.position.y.toFixed(1)}, ${this.bot.entity.position.z.toFixed(1)})`);

            this.setupPathfinder();
            this.loadSchematicData();
            this.setupCommands();
        });

        this.bot.on('error', (err) => {
            console.error('❌ Bot error:', err);
        });

        this.bot.on('end', () => {
            console.log('🔌 Bot disconnected');
        });
    }

    setupPathfinder() {
        try {
            const pathfinder = require('mineflayer-pathfinder');
            this.bot.loadPlugin(pathfinder.pathfinder);

            // Configure pathfinder with jumping enabled
            const movements = new pathfinder.Movements(this.bot);
            movements.canDig = false; // Don't dig blocks
            movements.allow1by1towers = true; // Allow building towers
            movements.allowFreeMotion = true; // Allow free motion
            movements.allowParkour = true; // Allow parkour moves

            this.bot.pathfinder.setMovements(movements);
            console.log('🧭 Pathfinder loaded with smart movement');
        } catch (error) {
            console.log('⚠️ Pathfinder not available, using basic movement');
        }
    }

    loadSchematicData() {
        try {
            const data = fs.readFileSync(config.settings.outputFile, 'utf8');
            this.schematicData = JSON.parse(data);
            console.log(`📦 Loaded schematic: ${this.schematicData.blocks.length} blocks`);
            console.log(`📏 Dimensions: ${this.schematicData.metadata.dimensions.width}x${this.schematicData.metadata.dimensions.height}x${this.schematicData.metadata.dimensions.depth}`);
        } catch (error) {
            console.error('❌ Error loading schematic data:', error.message);
            console.log('💡 Run saver.js first to create the schematic data!');
        }
    }

    setupCommands() {
        const target = config.settings.teleportTarget || 'Xiovz';
        console.log('\n📝 Available commands:');
        console.log('  print - Start printing the schematic in front of bot');
        console.log('  clear - Clear the build area');
        console.log('  info - Show schematic information');
        console.log(`  tp - Teleport to ${target}`);
        console.log('  help - Show this help message');

        // Handle console input
        process.stdin.on('data', (data) => {
            const command = data.toString().trim().toLowerCase();
            this.handleCommand(command);
        });
    }

    async handleCommand(command) {
        switch (command) {
            case 'print':
                if (!this.schematicData) {
                    console.log('❌ No schematic data loaded!');
                    return;
                }
                if (this.isPrinting) {
                    console.log('⚠️ Already printing!');
                    return;
                }
                await this.startPrinting();
                break;

            case 'clear':
                await this.clearBuildArea();
                break;

            case 'info':
                this.showSchematicInfo();
                break;

            case 'tp':
                await this.teleportToXiovz();
                break;

            case 'help':
                this.setupCommands();
                break;

            default:
                console.log('❓ Unknown command. Type "help" for available commands.');
        }
    }

    calculateBuildPosition() {
        const botPos = this.bot.entity.position;
        const botYaw = this.bot.entity.yaw;
        
        // Calculate position 1 block in front of bot
        const frontX = botPos.x + Math.cos(botYaw + Math.PI) * config.settings.buildOffset;
        const frontZ = botPos.z + Math.sin(botYaw + Math.PI) * config.settings.buildOffset;
        
        this.buildPosition = {
            x: Math.floor(frontX),
            y: Math.floor(botPos.y),
            z: Math.floor(frontZ)
        };

        console.log(`🎯 Build position: (${this.buildPosition.x}, ${this.buildPosition.y}, ${this.buildPosition.z})`);
    }

    async startPrinting() {
        console.log('\n🏗️ Starting to print schematic...');
        this.calculateBuildPosition();
        
        this.isPrinting = true;
        this.printedBlocks = 0;
        this.skippedBlocks = 0;
        this.startTime = Date.now();

        try {
            await this.printLayerByLayer();
            console.log('✅ Printing completed!');
        } catch (error) {
            console.error('❌ Error during printing:', error.message);
        } finally {
            this.isPrinting = false;
            this.showPrintResults();
        }
    }

    async printLayerByLayer() {
        // Group blocks by Y level
        const blocksByLayer = {};
        
        for (const block of this.schematicData.blocks) {
            const y = block.relativePosition.y;
            if (!blocksByLayer[y]) {
                blocksByLayer[y] = [];
            }
            blocksByLayer[y].push(block);
        }

        // Sort layers by Y level (bottom to top)
        const sortedLayers = Object.keys(blocksByLayer).sort((a, b) => parseInt(a) - parseInt(b));

        console.log(`📋 Printing ${sortedLayers.length} layers...`);

        // Print each layer
        for (const layerY of sortedLayers) {
            const layer = blocksByLayer[layerY];
            console.log(`🔨 Building layer Y+${layerY} (${layer.length} blocks)`);
            
            await this.printLayer(layer, parseInt(layerY));
            
            // Small delay between layers
            await this.sleep(100);
        }
    }

    async printLayer(blocks, layerOffset) {
        // Sort blocks in zig-zag pattern for efficient building
        const sortedBlocks = this.sortBlocksZigZag(blocks);

        for (const block of sortedBlocks) {
            if (!this.isPrinting) break; // Allow stopping mid-print

            try {
                await this.placeBlock(block, layerOffset);
                this.printedBlocks++;
                
                // Show progress every 50 blocks
                if (this.printedBlocks % 50 === 0) {
                    const progress = (this.printedBlocks / this.schematicData.blocks.length * 100).toFixed(1);
                    console.log(`⏳ Progress: ${this.printedBlocks}/${this.schematicData.blocks.length} (${progress}%)`);
                }
                
                await this.sleep(config.settings.blockPlaceDelay);
            } catch (error) {
                console.log(`⚠️ Failed to place ${block.name} at (${block.relativePosition.x}, ${block.relativePosition.y}, ${block.relativePosition.z}): ${error.message}`);
                this.skippedBlocks++;
            }
        }
    }

    sortBlocksZigZag(blocks) {
        // Sort blocks in zig-zag pattern (X direction alternates each Z row)
        const sortedByZ = {};
        
        // Group by Z coordinate
        for (const block of blocks) {
            const z = block.relativePosition.z;
            if (!sortedByZ[z]) {
                sortedByZ[z] = [];
            }
            sortedByZ[z].push(block);
        }

        const result = [];
        const zKeys = Object.keys(sortedByZ).sort((a, b) => parseInt(a) - parseInt(b));
        
        for (let i = 0; i < zKeys.length; i++) {
            const zKey = zKeys[i];
            const blocksInRow = sortedByZ[zKey];
            
            // Sort by X coordinate
            blocksInRow.sort((a, b) => a.relativePosition.x - b.relativePosition.x);
            
            // Reverse every other row for zig-zag pattern
            if (i % 2 === 1) {
                blocksInRow.reverse();
            }
            
            result.push(...blocksInRow);
        }

        return result;
    }

    async placeBlock(blockData, layerOffset) {
        const worldPos = {
            x: this.buildPosition.x + blockData.relativePosition.x,
            y: this.buildPosition.y + layerOffset,
            z: this.buildPosition.z + blockData.relativePosition.z
        };

        console.log(`🎯 Attempting to place ${blockData.name} at (${worldPos.x}, ${worldPos.y}, ${worldPos.z})`);

        // Try to get the block using /give command
        await this.ensureBlockInInventory(blockData.name);

        // Simple positioning: move if out of range, jump if standing on target
        await this.simplePositioning(worldPos);

        // Try to place the block
        await this.simplePlaceBlock(worldPos, blockData.name);
    }

    async simplePositioning(targetPos) {
        const botPos = this.bot.entity.position;
        const distance = Math.sqrt((targetPos.x - botPos.x) ** 2 + (targetPos.z - botPos.z) ** 2);
        const maxRange = 5; // 5 block range as requested

        console.log(`📍 Bot position: (${botPos.x.toFixed(1)}, ${botPos.y.toFixed(1)}, ${botPos.z.toFixed(1)})`);
        console.log(`🎯 Target position: (${targetPos.x}, ${targetPos.y}, ${targetPos.z})`);
        console.log(`📏 Distance: ${distance.toFixed(1)} blocks`);

        // Check if bot is standing exactly where block needs to be placed
        const botFloorX = Math.floor(botPos.x);
        const botFloorY = Math.floor(botPos.y);
        const botFloorZ = Math.floor(botPos.z);

        if (botFloorX === targetPos.x && botFloorY === targetPos.y && botFloorZ === targetPos.z) {
            console.log(`🦘 Bot is standing on target location, jumping!`);
            this.bot.setControlState('jump', true);
            await this.sleep(500);
            this.bot.setControlState('jump', false);
            await this.sleep(200);
            return;
        }

        // Check if bot is out of range
        if (distance > maxRange) {
            console.log(`🚶 Target out of range (${distance.toFixed(1)} > ${maxRange}), moving forward`);

            // Look at target
            await this.bot.lookAt(new Vec3(targetPos.x, targetPos.y, targetPos.z));

            // Move forward until in range
            this.bot.setControlState('forward', true);

            // Keep moving until in range
            while (true) {
                await this.sleep(100);
                const currentPos = this.bot.entity.position;
                const currentDistance = Math.sqrt((targetPos.x - currentPos.x) ** 2 + (targetPos.z - currentPos.z) ** 2);

                if (currentDistance <= maxRange) {
                    console.log(`✅ Now in range! Distance: ${currentDistance.toFixed(1)}`);
                    break;
                }

                // Safety check - don't move forever
                if (currentDistance > distance + 10) {
                    console.log(`⚠️ Moved too far, stopping`);
                    break;
                }
            }

            this.bot.setControlState('forward', false);
            await this.sleep(200);
        } else {
            console.log(`✅ Already in range (${distance.toFixed(1)} blocks)`);
        }
    }

    async simplePlaceBlock(worldPos, blockName) {
        const targetPos = new Vec3(worldPos.x, worldPos.y, worldPos.z);

        // Check if target position is already occupied
        const existingBlock = this.bot.blockAt(targetPos);
        if (existingBlock && existingBlock.name !== 'air') {
            console.log(`⚠️ Position already occupied by ${existingBlock.name}, skipping`);
            return;
        }

        // Try to place on block below first
        const blockBelow = this.bot.blockAt(new Vec3(worldPos.x, worldPos.y - 1, worldPos.z));

        if (blockBelow && blockBelow.name !== 'air') {
            try {
                console.log(`🔨 Placing ${blockName} on ${blockBelow.name} below`);
                await this.bot.placeBlock(blockBelow, new Vec3(0, 1, 0));
                console.log(`✅ Successfully placed ${blockName} at (${worldPos.x}, ${worldPos.y}, ${worldPos.z})`);
                return;
            } catch (error) {
                console.log(`⚠️ Failed to place on block below: ${error.message}`);
            }
        }

        // Try to place against adjacent blocks
        const adjacentBlocks = [
            { pos: new Vec3(worldPos.x + 1, worldPos.y, worldPos.z), face: new Vec3(-1, 0, 0) },
            { pos: new Vec3(worldPos.x - 1, worldPos.y, worldPos.z), face: new Vec3(1, 0, 0) },
            { pos: new Vec3(worldPos.x, worldPos.y, worldPos.z + 1), face: new Vec3(0, 0, -1) },
            { pos: new Vec3(worldPos.x, worldPos.y, worldPos.z - 1), face: new Vec3(0, 0, 1) }
        ];

        for (const { pos, face } of adjacentBlocks) {
            const adjBlock = this.bot.blockAt(pos);
            if (adjBlock && adjBlock.name !== 'air') {
                try {
                    console.log(`🔨 Placing ${blockName} against ${adjBlock.name} at (${pos.x}, ${pos.y}, ${pos.z})`);
                    await this.bot.placeBlock(adjBlock, face);
                    console.log(`✅ Successfully placed ${blockName} at (${worldPos.x}, ${worldPos.y}, ${worldPos.z})`);
                    return;
                } catch (error) {
                    console.log(`⚠️ Failed to place against adjacent block: ${error.message}`);
                    continue;
                }
            }
        }

        console.log(`❌ Could not find suitable surface to place ${blockName} at (${worldPos.x}, ${worldPos.y}, ${worldPos.z})`);
        throw new Error(`No suitable placement surface found for ${blockName}`);
    }

    async ensureBotNotBlockingPlacement(targetPos) {
        const botPos = this.bot.entity.position;

        // Check all positions the bot might be blocking
        const blockingPositions = [
            // Exact position
            { x: Math.floor(targetPos.x), y: Math.floor(targetPos.y), z: Math.floor(targetPos.z) },
            // Position below (bot standing on target)
            { x: Math.floor(targetPos.x), y: Math.floor(targetPos.y) - 1, z: Math.floor(targetPos.z) },
            // Position above (bot floating above target)
            { x: Math.floor(targetPos.x), y: Math.floor(targetPos.y) + 1, z: Math.floor(targetPos.z) }
        ];

        const botFloorPos = {
            x: Math.floor(botPos.x),
            y: Math.floor(botPos.y),
            z: Math.floor(botPos.z)
        };

        for (const blockingPos of blockingPositions) {
            if (botFloorPos.x === blockingPos.x &&
                botFloorPos.y === blockingPos.y &&
                botFloorPos.z === blockingPos.z) {

                console.log(`🚨 CRITICAL: Bot is blocking placement at (${blockingPos.x}, ${blockingPos.y}, ${blockingPos.z})!`);
                console.log(`🏃 EMERGENCY MOVE: Getting out of the way...`);

                await this.emergencyMoveAwayFromTarget(targetPos);

                // Verify we actually moved
                const newBotPos = this.bot.entity.position;
                const newBotFloorPos = {
                    x: Math.floor(newBotPos.x),
                    y: Math.floor(newBotPos.y),
                    z: Math.floor(newBotPos.z)
                };

                if (newBotFloorPos.x === blockingPos.x &&
                    newBotFloorPos.y === blockingPos.y &&
                    newBotFloorPos.z === blockingPos.z) {
                    console.log(`🚨 STILL BLOCKING! Trying more aggressive movement...`);
                    await this.aggressiveMoveAway(targetPos);
                }

                break;
            }
        }
    }

    async emergencyMoveAwayFromTarget(targetPos) {
        const botPos = this.bot.entity.position;

        // Calculate the best escape direction (away from target)
        let dx = botPos.x - targetPos.x;
        let dz = botPos.z - targetPos.z;

        // If too close, pick a random direction
        if (Math.abs(dx) < 0.5 && Math.abs(dz) < 0.5) {
            const directions = [
                { x: 3, z: 0 }, { x: -3, z: 0 }, { x: 0, z: 3 }, { x: 0, z: -3 },
                { x: 2, z: 2 }, { x: -2, z: -2 }, { x: 2, z: -2 }, { x: -2, z: 2 }
            ];
            const randomDir = directions[Math.floor(Math.random() * directions.length)];
            dx = randomDir.x;
            dz = randomDir.z;
        }

        // Normalize and amplify the escape direction
        const distance = Math.sqrt(dx * dx + dz * dz);
        const escapeDistance = 4; // Move 4 blocks away

        const escapeX = targetPos.x + (dx / distance) * escapeDistance;
        const escapeZ = targetPos.z + (dz / distance) * escapeDistance;
        const escapeY = Math.floor(botPos.y);

        console.log(`🏃 Emergency escape to (${escapeX.toFixed(1)}, ${escapeY}, ${escapeZ.toFixed(1)})`);

        // Look away from target
        await this.bot.lookAt(new Vec3(escapeX, escapeY, escapeZ));

        // Jump and move away aggressively
        this.bot.setControlState('jump', true);
        this.bot.setControlState('forward', true);
        await this.sleep(1500); // Move for 1.5 seconds
        this.bot.setControlState('forward', false);
        this.bot.setControlState('jump', false);

        // Additional side movement
        this.bot.setControlState('left', true);
        await this.sleep(500);
        this.bot.setControlState('left', false);

        await this.sleep(200);
    }

    async aggressiveMoveAway(targetPos) {
        console.log(`🚨 AGGRESSIVE MOVE: Using all movement options...`);

        // Try multiple movement combinations
        const movements = [
            { controls: ['back', 'jump'], duration: 1000 },
            { controls: ['left', 'jump'], duration: 800 },
            { controls: ['right', 'jump'], duration: 800 },
            { controls: ['forward', 'jump'], duration: 600 }
        ];

        for (const movement of movements) {
            // Activate all controls
            movement.controls.forEach(control => {
                this.bot.setControlState(control, true);
            });

            await this.sleep(movement.duration);

            // Deactivate all controls
            movement.controls.forEach(control => {
                this.bot.setControlState(control, false);
            });

            // Check if we moved away
            const botPos = this.bot.entity.position;
            const distance = Math.sqrt(
                (botPos.x - targetPos.x) ** 2 +
                (botPos.z - targetPos.z) ** 2
            );

            if (distance > 2) {
                console.log(`✅ Successfully moved away! Distance: ${distance.toFixed(1)}`);
                break;
            }

            await this.sleep(200);
        }
    }

    async moveToOptimalPosition(targetPos) {
        const botPos = this.bot.entity.position;
        const distance = botPos.distanceTo(new Vec3(targetPos.x, targetPos.y, targetPos.z));
        const maxReach = config.settings.maxReach || 4.5;

        // If already in range, just check if we need to jump
        if (distance <= maxReach) {
            await this.handleStuckSituation();
            return;
        }

        console.log(`🚶 Moving to position for block at (${targetPos.x}, ${targetPos.y}, ${targetPos.z})`);

        try {
            if (this.bot.pathfinder) {
                // Use pathfinder for smart movement
                const pathfinder = require('mineflayer-pathfinder');
                const { GoalNear } = pathfinder.goals;

                const goal = new GoalNear(targetPos.x, targetPos.y, targetPos.z, 3);

                await Promise.race([
                    this.bot.pathfinder.goto(goal),
                    this.sleep(config.settings.moveTimeout || 5000)
                ]);
            } else {
                // Fallback to manual movement
                await this.manualMovement(targetPos);
            }
        } catch (error) {
            console.log(`⚠️ Movement failed, trying manual approach: ${error.message}`);
            await this.manualMovement(targetPos);
        }
    }

    async manualMovement(targetPos) {
        const botPos = this.bot.entity.position;

        // Look at target
        await this.bot.lookAt(new Vec3(targetPos.x, targetPos.y, targetPos.z));

        // Check if there are blocks in the way and jump if needed
        const blockInPath = this.bot.blockAt(new Vec3(
            Math.floor(botPos.x + (targetPos.x - botPos.x) * 0.5),
            Math.floor(botPos.y),
            Math.floor(botPos.z + (targetPos.z - botPos.z) * 0.5)
        ));

        if (blockInPath && blockInPath.name !== 'air') {
            console.log('🦘 Jumping over obstacle');
            this.bot.setControlState('jump', true);
        }

        // Move forward
        this.bot.setControlState('forward', true);
        await this.sleep(1000);
        this.bot.setControlState('forward', false);
        this.bot.setControlState('jump', false);
    }

    async handleStuckSituation() {
        const botPos = this.bot.entity.position;

        // Check if bot is inside a block or on a block that might cause issues
        const blockAt = this.bot.blockAt(new Vec3(Math.floor(botPos.x), Math.floor(botPos.y), Math.floor(botPos.z)));
        const blockBelow = this.bot.blockAt(new Vec3(Math.floor(botPos.x), Math.floor(botPos.y) - 1, Math.floor(botPos.z)));

        if (blockAt && blockAt.name !== 'air') {
            console.log('🆙 Bot inside block, jumping up');
            this.bot.setControlState('jump', true);
            await this.sleep(300);
            this.bot.setControlState('jump', false);
        }

        // If standing on a recently placed block, might need to adjust
        if (blockBelow && (blockBelow.name.includes('planks') || blockBelow.name.includes('stone') || blockBelow.name.includes('wool'))) {
            // Small jump to ensure good positioning
            this.bot.setControlState('jump', true);
            await this.sleep(100);
            this.bot.setControlState('jump', false);
        }
    }

    async smartPlaceBlock(worldPos, blockName) {
        const targetPos = new Vec3(worldPos.x, worldPos.y, worldPos.z);

        // FINAL CRITICAL CHECK: Make absolutely sure bot isn't blocking
        console.log(`🔍 Final blocking check for (${worldPos.x}, ${worldPos.y}, ${worldPos.z})`);
        await this.ensureBotNotBlockingPlacement(worldPos);

        // Check if target position is already occupied
        const existingBlock = this.bot.blockAt(targetPos);
        if (existingBlock && existingBlock.name !== 'air') {
            console.log(`⚠️ Position already occupied by ${existingBlock.name}, skipping`);
            return;
        }

        // Verify bot position one more time
        const botPos = this.bot.entity.position;
        const distance = botPos.distanceTo(targetPos);
        const minDistance = config.settings.minDistance || 1.5;

        if (distance < minDistance) {
            console.log(`🚨 CRITICAL: Bot still too close (${distance.toFixed(1)} blocks)! Emergency move!`);
            await this.emergencyMoveAwayFromTarget(worldPos);
        }

        // Try different placement strategies
        const strategies = [
            () => this.placeOnBlockBelow(worldPos),
            () => this.placeAgainstAdjacent(worldPos),
            () => this.placeWithScaffolding(worldPos)
        ];

        for (const strategy of strategies) {
            try {
                // One more blocking check right before each placement attempt
                await this.ensureBotNotBlockingPlacement(worldPos);

                await strategy();
                console.log(`✅ Successfully placed ${blockName} at (${worldPos.x}, ${worldPos.y}, ${worldPos.z})`);

                // Verify the block was actually placed
                await this.sleep(100);
                const placedBlock = this.bot.blockAt(targetPos);
                if (placedBlock && placedBlock.name !== 'air') {
                    console.log(`✅ Verified: ${placedBlock.name} is now at (${worldPos.x}, ${worldPos.y}, ${worldPos.z})`);
                } else {
                    console.log(`⚠️ Warning: Block verification failed at (${worldPos.x}, ${worldPos.y}, ${worldPos.z})`);
                }

                return;
            } catch (error) {
                console.log(`⚠️ Placement strategy failed: ${error.message}`);
                continue;
            }
        }

        throw new Error(`All placement strategies failed for ${blockName}`);
    }

    async placeOnBlockBelow(worldPos) {
        const blockBelow = this.bot.blockAt(new Vec3(worldPos.x, worldPos.y - 1, worldPos.z));

        if (!blockBelow || blockBelow.name === 'air') {
            throw new Error('No block below to place on');
        }

        // Use timeout to prevent hanging
        await Promise.race([
            this.bot.placeBlock(blockBelow, new Vec3(0, 1, 0)),
            this.sleep(config.settings.placeTimeout || 3000).then(() => {
                throw new Error('Block placement timeout');
            })
        ]);
    }

    async placeAgainstAdjacent(worldPos) {
        const adjacentPositions = [
            { pos: new Vec3(worldPos.x + 1, worldPos.y, worldPos.z), face: new Vec3(-1, 0, 0) },
            { pos: new Vec3(worldPos.x - 1, worldPos.y, worldPos.z), face: new Vec3(1, 0, 0) },
            { pos: new Vec3(worldPos.x, worldPos.y, worldPos.z + 1), face: new Vec3(0, 0, -1) },
            { pos: new Vec3(worldPos.x, worldPos.y, worldPos.z - 1), face: new Vec3(0, 0, 1) },
            { pos: new Vec3(worldPos.x, worldPos.y + 1, worldPos.z), face: new Vec3(0, -1, 0) }
        ];

        for (const { pos, face } of adjacentPositions) {
            const adjBlock = this.bot.blockAt(pos);
            if (adjBlock && adjBlock.name !== 'air') {
                await Promise.race([
                    this.bot.placeBlock(adjBlock, face),
                    this.sleep(config.settings.placeTimeout || 3000).then(() => {
                        throw new Error('Block placement timeout');
                    })
                ]);
                return;
            }
        }

        throw new Error('No adjacent blocks to place against');
    }

    async placeWithScaffolding(worldPos) {
        // Try to place a temporary block below first
        const belowPos = new Vec3(worldPos.x, worldPos.y - 1, worldPos.z);
        const blockBelow = this.bot.blockAt(belowPos);

        if (blockBelow && blockBelow.name === 'air') {
            // Try to place scaffolding block first
            const scaffoldMaterial = this.bot.inventory.items().find(item =>
                item.name.includes('dirt') || item.name.includes('stone') || item.name.includes('planks')
            );

            if (scaffoldMaterial) {
                await this.bot.equip(scaffoldMaterial, 'hand');
                await this.placeAgainstAdjacent({ x: belowPos.x, y: belowPos.y, z: belowPos.z });

                // Now try to place the original block
                await this.ensureBlockInInventory(this.currentBlockName);
                await this.placeOnBlockBelow(worldPos);
                return;
            }
        }

        throw new Error('Cannot create scaffolding');
    }

    async ensureBlockInInventory(blockName) {
        // Check if bot already has the block
        const existingItem = this.bot.inventory.items().find(item => item.name === blockName);
        
        if (existingItem && existingItem.count > 0) {
            // Equip the block
            await this.bot.equip(existingItem, 'hand');
            return;
        }

        // Try to give the block using command
        try {
            await this.bot.chat(`/give ${this.bot.username} ${blockName} 64`);
            await this.sleep(config.settings.giveCommandDelay);
            
            // Try to equip the newly given block
            const newItem = this.bot.inventory.items().find(item => item.name === blockName);
            if (newItem) {
                await this.bot.equip(newItem, 'hand');
            } else {
                throw new Error(`Failed to obtain ${blockName}`);
            }
        } catch (error) {
            // Send message in chat about unavailable block
            await this.bot.chat(`❌ Cannot get block: ${blockName}`);
            throw error;
        }
    }

    async teleportToXiovz() {
        const target = config.settings.teleportTarget || 'Xiovz';
        console.log(`🚀 Teleporting to ${target}...`);

        try {
            this.bot.chat(`/tp ${target}`);
            console.log(`✅ Teleport command sent: /tp ${target}`);
        } catch (error) {
            console.error('❌ Error sending teleport command:', error.message);
        }
    }

    async clearBuildArea() {
        if (!this.schematicData) {
            console.log('❌ No schematic data loaded!');
            return;
        }

        console.log('🧹 Clearing build area...');
        this.calculateBuildPosition();

        const dimensions = this.schematicData.metadata.dimensions;
        const clearCommand = `/fill ${this.buildPosition.x} ${this.buildPosition.y} ${this.buildPosition.z} ${this.buildPosition.x + dimensions.width - 1} ${this.buildPosition.y + dimensions.height - 1} ${this.buildPosition.z + dimensions.depth - 1} air`;

        try {
            this.bot.chat(clearCommand);
            console.log('✅ Build area cleared!');
        } catch (error) {
            console.error('❌ Error clearing area:', error.message);
        }
    }

    showSchematicInfo() {
        if (!this.schematicData) {
            console.log('❌ No schematic data loaded!');
            return;
        }

        const meta = this.schematicData.metadata;
        console.log('\n📊 Schematic Information:');
        console.log(`📦 Total blocks: ${meta.totalBlocks}`);
        console.log(`📏 Dimensions: ${meta.dimensions.width}x${meta.dimensions.height}x${meta.dimensions.depth}`);
        console.log(`📅 Created: ${new Date(meta.created).toLocaleString()}`);
        console.log(`⏱️ Scan time: ${(meta.scanTime / 1000).toFixed(1)} seconds`);
        console.log(`🎯 Original area: (${meta.scanArea.start.x}, ${meta.scanArea.start.y}, ${meta.scanArea.start.z}) to (${meta.scanArea.end.x}, ${meta.scanArea.end.y}, ${meta.scanArea.end.z})`);

        // Show block types summary
        const blockTypes = {};
        for (const block of this.schematicData.blocks) {
            blockTypes[block.name] = (blockTypes[block.name] || 0) + 1;
        }

        console.log('\n🧱 Block types:');
        const sortedTypes = Object.entries(blockTypes).sort((a, b) => b[1] - a[1]);
        for (const [blockName, count] of sortedTypes.slice(0, 10)) {
            console.log(`  ${blockName}: ${count}`);
        }
        if (sortedTypes.length > 10) {
            console.log(`  ... and ${sortedTypes.length - 10} more types`);
        }
    }

    showPrintResults() {
        const totalTime = Date.now() - this.startTime;
        console.log('\n📊 Print Results:');
        console.log(`✅ Blocks placed: ${this.printedBlocks}`);
        console.log(`⚠️ Blocks skipped: ${this.skippedBlocks}`);
        console.log(`⏱️ Total time: ${(totalTime / 1000).toFixed(1)} seconds`);

        if (this.printedBlocks > 0) {
            const blocksPerSecond = (this.printedBlocks / (totalTime / 1000)).toFixed(1);
            console.log(`⚡ Speed: ${blocksPerSecond} blocks/second`);
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Create and start the printer
const printer = new SchematicPrinter();
printer.connect();

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down printer...');
    if (printer.bot) {
        printer.isPrinting = false;
        printer.bot.quit();
    }
    process.exit(0);
});
