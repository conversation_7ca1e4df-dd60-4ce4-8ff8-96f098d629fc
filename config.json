{"server": {"host": "localhost", "port": 59126, "username": "SchematicBot"}, "area": {"start": {"x": -3, "y": -60, "z": 1}, "end": {"x": 10, "y": 70, "z": -10}}, "settings": {"outputFile": "data.json", "ignoreAir": true, "layerByLayer": true, "zigzagPattern": true, "buildOffset": 1, "giveCommandDelay": 100, "blockPlaceDelay": 200, "maxReach": 4.5, "moveTimeout": 5000, "enableJumping": true, "placeTimeout": 3000, "minDistance": 1.5, "smartPositioning": true, "teleportTarget": "Xiovz"}}