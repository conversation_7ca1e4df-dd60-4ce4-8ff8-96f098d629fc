[{"id": 0, "displayName": "Air", "name": "air", "stackSize": 64}, {"id": 1, "displayName": "Stone", "name": "stone", "stackSize": 64}, {"id": 2, "displayName": "Grass Block", "name": "grass_block", "stackSize": 64}, {"id": 3, "displayName": "Dirt", "name": "dirt", "stackSize": 64}, {"id": 4, "displayName": "Cobblestone", "name": "cobblestone", "stackSize": 64}, {"id": 5, "displayName": "Wood Planks", "name": "wood_planks", "stackSize": 64}, {"id": 6, "displayName": "Sapling", "name": "sapling", "stackSize": 64}, {"id": 7, "displayName": "Bedrock", "name": "bedrock", "stackSize": 64}, {"id": 8, "displayName": "Water", "name": "water", "stackSize": 64}, {"id": 10, "displayName": "<PERSON><PERSON>", "name": "lava", "stackSize": 64}, {"id": 12, "displayName": "Sand", "name": "sand", "stackSize": 64}, {"id": 13, "displayName": "<PERSON>l", "name": "gravel", "stackSize": 64}, {"id": 14, "displayName": "Gold Ore", "name": "gold_ore", "stackSize": 64}, {"id": 15, "displayName": "Iron Ore", "name": "iron_ore", "stackSize": 64}, {"id": 16, "displayName": "Coal Ore", "name": "coal_ore", "stackSize": 64}, {"id": 17, "displayName": "<PERSON>", "name": "wood", "stackSize": 64}, {"id": 18, "displayName": "Leaves", "name": "leaves", "stackSize": 64}, {"id": 19, "displayName": "Sponge", "name": "sponge", "stackSize": 64}, {"id": 20, "displayName": "Glass", "name": "glass", "stackSize": 64}, {"id": 21, "displayName": "Lapis <PERSON> Ore", "name": "lapis_lazuli_ore", "stackSize": 64}, {"id": 22, "displayName": "Lapis <PERSON>", "name": "lapis_lazuli_block", "stackSize": 64}, {"id": 23, "displayName": "Dispenser", "name": "dispenser", "stackSize": 64}, {"id": 24, "displayName": "Sandstone", "name": "sandstone", "stackSize": 64}, {"id": 25, "displayName": "Note Block", "name": "note_block", "stackSize": 64}, {"id": 27, "displayName": "Powered Rail", "name": "powered_rail", "stackSize": 64}, {"id": 28, "displayName": "Detector Rail", "name": "detector_rail", "stackSize": 64}, {"id": 29, "displayName": "<PERSON><PERSON>", "name": "sticky_piston", "stackSize": 64}, {"id": 30, "displayName": "Cobweb", "name": "cobweb", "stackSize": 64}, {"id": 31, "displayName": "Tall Grass", "name": "tall_grass", "stackSize": 64}, {"id": 32, "displayName": "Dead Bush", "name": "dead_bush", "stackSize": 64}, {"id": 33, "displayName": "<PERSON><PERSON>", "name": "piston", "stackSize": 64}, {"id": 34, "displayName": "Piston Head", "name": "piston_head", "stackSize": 64}, {"id": 35, "displayName": "Wool", "name": "wool", "stackSize": 64}, {"id": 37, "displayName": "Dandelion", "name": "dandelion", "stackSize": 64}, {"id": 38, "displayName": "Flower", "name": "flower", "stackSize": 64}, {"id": 39, "displayName": "Brown Mushroom", "name": "brown_mushroom", "stackSize": 64}, {"id": 40, "displayName": "Red Mushroom", "name": "red_mushroom", "stackSize": 64}, {"id": 41, "displayName": "Block of Gold", "name": "block_of_gold", "stackSize": 64}, {"id": 42, "displayName": "Block of Iron", "name": "block_of_iron", "stackSize": 64}, {"id": 43, "displayName": "Double Stone Slab", "name": "double_stone_slab", "stackSize": 64}, {"id": 44, "displayName": "<PERSON> Slab", "name": "stone_slab", "stackSize": 64}, {"id": 45, "displayName": "Bricks", "name": "bricks", "stackSize": 64}, {"id": 46, "displayName": "TNT", "name": "tnt", "stackSize": 64}, {"id": 47, "displayName": "Bookshelf", "name": "bookshelf", "stackSize": 64}, {"id": 48, "displayName": "<PERSON>", "name": "moss_stone", "stackSize": 64}, {"id": 49, "displayName": "Obsidian", "name": "obsidian", "stackSize": 64}, {"id": 50, "displayName": "<PERSON>ch", "name": "torch", "stackSize": 64}, {"id": 51, "displayName": "Fire", "name": "fire", "stackSize": 64}, {"id": 52, "displayName": "Monster Spawner", "name": "monster_spawner", "stackSize": 64}, {"id": 53, "displayName": "Oak Wood Stairs", "name": "oak_wood_stairs", "stackSize": 64}, {"id": 54, "displayName": "Chest", "name": "chest", "stackSize": 64}, {"id": 55, "displayName": "Redstone Wire", "name": "redstone_wire", "stackSize": 64}, {"id": 56, "displayName": "Diamond Ore", "name": "diamond_ore", "stackSize": 64}, {"id": 57, "displayName": "Block of Diamond", "name": "block_of_diamond", "stackSize": 64}, {"id": 58, "displayName": "Crafting Table", "name": "crafting_table", "stackSize": 64}, {"id": 59, "displayName": "Crops", "name": "crops", "stackSize": 64}, {"id": 60, "displayName": "Farmland", "name": "farmland", "stackSize": 64}, {"id": 61, "displayName": "Furnace", "name": "furnace", "stackSize": 64}, {"id": 65, "displayName": "Ladder", "name": "ladder", "stackSize": 64}, {"id": 66, "displayName": "Rail", "name": "rail", "stackSize": 64}, {"id": 67, "displayName": "Cobblestone Stairs", "name": "cobblestone_stairs", "stackSize": 64}, {"id": 69, "displayName": "Lever", "name": "lever", "stackSize": 64}, {"id": 70, "displayName": "Stone Pressure Plate", "name": "stone_pressure_plate", "stackSize": 64}, {"id": 72, "displayName": "Wooden Pressure Plate", "name": "wooden_pressure_plate", "stackSize": 64}, {"id": 73, "displayName": "Redstone Ore", "name": "redstone_ore", "stackSize": 64}, {"id": 75, "displayName": "Redstone Torch (inactive)", "name": "redstone_torch", "stackSize": 64}, {"id": 77, "displayName": "<PERSON>", "name": "stone_button", "stackSize": 64}, {"id": 78, "displayName": "Top Snow", "name": "top_snow", "stackSize": 64}, {"id": 79, "displayName": "Ice", "name": "ice", "stackSize": 64}, {"id": 80, "displayName": "Snow", "name": "snow", "stackSize": 64}, {"id": 81, "displayName": "Cactus", "name": "cactus", "stackSize": 64}, {"id": 85, "displayName": "<PERSON><PERSON>", "name": "fence", "stackSize": 64}, {"id": 86, "displayName": "<PERSON><PERSON><PERSON>", "name": "pumpkin", "stackSize": 64}, {"id": 87, "displayName": "Netherrack", "name": "netherrack", "stackSize": 64}, {"id": 88, "displayName": "Soul Sand", "name": "soul_sand", "stackSize": 64}, {"id": 89, "displayName": "Glowstone", "name": "glowstone", "stackSize": 64}, {"id": 90, "displayName": "Portal", "name": "portal", "stackSize": 64}, {"id": 91, "displayName": "<PERSON>'<PERSON>", "name": "jack_o'lantern", "stackSize": 64}, {"id": 95, "displayName": "Invisible Bedrock", "name": "invisible_bedrock", "stackSize": 64}, {"id": 96, "displayName": "Trapdoor", "name": "trapdoor", "stackSize": 64}, {"id": 97, "displayName": "Monster Egg", "name": "monster_egg", "stackSize": 64}, {"id": 98, "displayName": "Stone Brick", "name": "stone_brick", "stackSize": 64}, {"id": 101, "displayName": "Iron Bars", "name": "iron_bars", "stackSize": 64}, {"id": 102, "displayName": "Glass Pane", "name": "glass_pane", "stackSize": 64}, {"id": 104, "displayName": "<PERSON><PERSON><PERSON>", "name": "pumpkin_stem", "stackSize": 64}, {"id": 105, "displayName": "Melon Stem", "name": "melon_stem", "stackSize": 64}, {"id": 106, "displayName": "Vines", "name": "vines", "stackSize": 64}, {"id": 107, "displayName": "Fence Gate", "name": "fence_gate", "stackSize": 64}, {"id": 108, "displayName": "Brick Stairs", "name": "brick_stairs", "stackSize": 64}, {"id": 109, "displayName": "Stone Brick Stairs", "name": "stone_brick_stairs", "stackSize": 64}, {"id": 110, "displayName": "Mycelium", "name": "mycelium", "stackSize": 64}, {"id": 111, "displayName": "<PERSON>", "name": "lily_pad", "stackSize": 64}, {"id": 113, "displayName": "Nether Brick Fence", "name": "nether_brick_fence", "stackSize": 64}, {"id": 114, "displayName": "Nether Brick Stairs", "name": "nether_brick_stairs", "stackSize": 64}, {"id": 116, "displayName": "Enchantment Table", "name": "enchantment_table", "stackSize": 64}, {"id": 120, "displayName": "End Portal Frame", "name": "end_portal_frame", "stackSize": 64}, {"id": 121, "displayName": "End Stone", "name": "end_stone", "stackSize": 64}, {"id": 123, "displayName": "Redstone Lamp (inactive)", "name": "redstone_lamp", "stackSize": 64}, {"id": 125, "displayName": "Dropper", "name": "dropper", "stackSize": 64}, {"id": 126, "displayName": "Activator Rail", "name": "activator_rail", "stackSize": 64}, {"id": 127, "displayName": "Cocoa", "name": "cocoa", "stackSize": 64}, {"id": 128, "displayName": "Sandstone Stairs", "name": "sandstone_stairs", "stackSize": 64}, {"id": 129, "displayName": "Emerald Ore", "name": "emerald_ore", "stackSize": 64}, {"id": 131, "displayName": "Tripwire Hook", "name": "tripwire_hook", "stackSize": 64}, {"id": 132, "displayName": "Tripwire", "name": "tripwire", "stackSize": 64}, {"id": 133, "displayName": "Block of Emerald", "name": "block_of_emerald", "stackSize": 64}, {"id": 134, "displayName": "Spruce Wood Stairs", "name": "spruce_wood_stairs", "stackSize": 64}, {"id": 135, "displayName": "Birch Wood Stairs", "name": "birch_wood_stairs", "stackSize": 64}, {"id": 136, "displayName": "Jungle Wood Stairs", "name": "jungle_wood_stairs", "stackSize": 64}, {"id": 139, "displayName": "Cobblestone Wall", "name": "cobblestone_wall", "stackSize": 64}, {"id": 141, "displayName": "Carrots", "name": "carrots", "stackSize": 64}, {"id": 143, "displayName": "<PERSON><PERSON>", "name": "wooden_button", "stackSize": 64}, {"id": 145, "displayName": "An<PERSON>", "name": "anvil", "stackSize": 64}, {"id": 146, "displayName": "Trapped Chest", "name": "trapped_chest", "stackSize": 64}, {"id": 147, "displayName": "Weighted Pressure Plate (Light)", "name": "weighted_pressure_plate", "stackSize": 64}, {"id": 149, "displayName": "Redstone Comparator (unpowered)", "name": "redstone_comparator", "stackSize": 64}, {"id": 151, "displayName": "Daylight Sensor", "name": "daylight_sensor", "stackSize": 64}, {"id": 152, "displayName": "Block of Redstone", "name": "block_of_redstone", "stackSize": 64}, {"id": 153, "displayName": "<PERSON><PERSON>", "name": "nether_quartz_ore", "stackSize": 64}, {"id": 155, "displayName": "Block of Quartz", "name": "block_of_quartz", "stackSize": 64}, {"id": 156, "displayName": "Quartz Stairs", "name": "quartz_stairs", "stackSize": 64}, {"id": 157, "displayName": "Wooden Double Slab", "name": "wooden_double_slab", "stackSize": 64}, {"id": 158, "displayName": "<PERSON><PERSON>", "name": "wooden_slab", "stackSize": 64}, {"id": 159, "displayName": "Stained Clay", "name": "stained_clay", "stackSize": 64}, {"id": 161, "displayName": "Acacia Leaves", "name": "acacia_leaves", "stackSize": 64}, {"id": 162, "displayName": "Acacia Wood", "name": "acacia_wood", "stackSize": 64}, {"id": 163, "displayName": "Acacia Wood Stairs", "name": "acacia_wood_stairs", "stackSize": 64}, {"id": 164, "displayName": "Dark Oak Wood Stairs", "name": "dark_oak_wood_stairs", "stackSize": 64}, {"id": 165, "displayName": "Slime Block", "name": "slime_block", "stackSize": 64}, {"id": 167, "displayName": "Iron Trapdoor", "name": "iron_trapdoor", "stackSize": 64}, {"id": 170, "displayName": "<PERSON>", "name": "hay_bale", "stackSize": 64}, {"id": 171, "displayName": "Carpet", "name": "carpet", "stackSize": 64}, {"id": 172, "displayName": "Hardened Clay", "name": "hardened_clay", "stackSize": 64}, {"id": 173, "displayName": "Block of Coal", "name": "block_of_coal", "stackSize": 64}, {"id": 174, "displayName": "Packed Ice", "name": "packed_ice", "stackSize": 64}, {"id": 175, "displayName": "Sunflower", "name": "sunflower", "stackSize": 64}, {"id": 178, "displayName": "Inverted Daylight Sensor", "name": "inverted_daylight_sensor", "stackSize": 64}, {"id": 179, "displayName": "Red Sandstone", "name": "red_sandstone", "stackSize": 64}, {"id": 180, "displayName": "Red Sandstone Stairs", "name": "red_sandstone_stairs", "stackSize": 64}, {"id": 181, "displayName": "Double Red Sandstone Slab", "name": "double_red_sandstone_slab", "stackSize": 64}, {"id": 182, "displayName": "Red Sandstone Slab", "name": "red_sandstone_slab", "stackSize": 64}, {"id": 183, "displayName": "Spruce Fence Gate", "name": "spruce_fence_gate", "stackSize": 64}, {"id": 184, "displayName": "Birch Fence Gate", "name": "birch_fence_gate", "stackSize": 64}, {"id": 185, "displayName": "Jungle Fence Gate", "name": "jungle_fence_gate", "stackSize": 64}, {"id": 186, "displayName": "Dark Oak Fence Gate", "name": "dark_oak_fence_gate", "stackSize": 64}, {"id": 187, "displayName": "Acacia Fence Gate", "name": "acacia_fence_gate", "stackSize": 64}, {"id": 198, "displayName": "Grass Path", "name": "grass_path", "stackSize": 64}, {"id": 243, "displayName": "Podzol", "name": "podzol", "stackSize": 64}, {"id": 245, "displayName": "<PERSON><PERSON><PERSON>", "name": "stonecutter", "stackSize": 64}, {"id": 246, "displayName": "Glowing Obsidian", "name": "glowing_obsidian", "stackSize": 64}, {"id": 247, "displayName": "Nether Reactor Core", "name": "nether_reactor_core", "stackSize": 64}, {"id": 248, "displayName": "Update Game Block (update!)", "name": "update_game_block", "stackSize": 64}, {"id": 250, "displayName": "Block moved by <PERSON><PERSON>", "name": "block_moved_by_piston", "stackSize": 64}, {"id": 251, "displayName": "Observer", "name": "observer", "stackSize": 64}, {"id": 255, "displayName": "info_reserved6", "name": "info_reserved6", "stackSize": 64}, {"id": 256, "displayName": "Iron Shovel", "stackSize": 1, "name": "iron_shovel"}, {"id": 257, "displayName": "Iron Pickaxe", "stackSize": 1, "name": "iron_pickaxe"}, {"id": 258, "displayName": "Iron Axe", "stackSize": 1, "name": "iron_axe"}, {"id": 259, "displayName": "Flint and Steel", "stackSize": 1, "name": "flint_and_steel"}, {"id": 260, "displayName": "Apple", "stackSize": 64, "name": "apple"}, {"id": 261, "displayName": "Bow", "stackSize": 1, "name": "bow"}, {"id": 262, "displayName": "Arrow", "stackSize": 64, "name": "arrow"}, {"id": 263, "displayName": "Coal", "stackSize": 64, "name": "coal", "variations": [{"metadata": 0, "displayName": "Coal"}, {"metadata": 1, "displayName": "Charc<PERSON>l"}]}, {"id": 264, "displayName": "Diamond", "stackSize": 64, "name": "diamond"}, {"id": 265, "displayName": "Iron Ingot", "stackSize": 64, "name": "iron_ingot"}, {"id": 266, "displayName": "Gold Ingot", "stackSize": 64, "name": "gold_ingot"}, {"id": 267, "displayName": "Iron Sword", "stackSize": 1, "name": "iron_sword"}, {"id": 268, "displayName": "Wooden Sword", "stackSize": 1, "name": "wooden_sword"}, {"id": 269, "displayName": "<PERSON><PERSON>", "stackSize": 1, "name": "wooden_shovel"}, {"id": 270, "displayName": "<PERSON><PERSON> Pick<PERSON>e", "stackSize": 1, "name": "wooden_pickaxe"}, {"id": 271, "displayName": "Wooden Axe", "stackSize": 1, "name": "wooden_axe"}, {"id": 272, "displayName": "Stone Sword", "stackSize": 1, "name": "stone_sword"}, {"id": 273, "displayName": "<PERSON>el", "stackSize": 1, "name": "stone_shovel"}, {"id": 274, "displayName": "<PERSON>", "stackSize": 1, "name": "stone_pickaxe"}, {"id": 275, "displayName": "Stone Axe", "stackSize": 1, "name": "stone_axe"}, {"id": 276, "displayName": "Diamond Sword", "stackSize": 1, "name": "diamond_sword"}, {"id": 277, "displayName": "Diamond Shovel", "stackSize": 1, "name": "diamond_shovel"}, {"id": 278, "displayName": "Diamond Pickaxe", "stackSize": 1, "name": "diamond_pickaxe"}, {"id": 279, "displayName": "Diamond Axe", "stackSize": 1, "name": "diamond_axe"}, {"id": 280, "displayName": "Stick", "stackSize": 64, "name": "stick"}, {"id": 281, "displayName": "Bowl", "stackSize": 64, "name": "bowl"}, {"id": 282, "displayName": "Mushroom Stew", "stackSize": 1, "name": "mushroom_stew"}, {"id": 283, "displayName": "Golden Sword", "stackSize": 1, "name": "golden_sword"}, {"id": 284, "displayName": "Golden Shovel", "stackSize": 1, "name": "golden_shovel"}, {"id": 285, "displayName": "Golden Pickaxe", "stackSize": 1, "name": "golden_pickaxe"}, {"id": 286, "displayName": "Golden Axe", "stackSize": 1, "name": "golden_axe"}, {"id": 287, "displayName": "String", "stackSize": 64, "name": "string"}, {"id": 288, "displayName": "<PERSON><PERSON>", "stackSize": 64, "name": "feather"}, {"id": 289, "displayName": "Gunpowder", "stackSize": 64, "name": "gunpowder"}, {"id": 290, "displayName": "<PERSON><PERSON>e", "stackSize": 1, "name": "wooden_hoe"}, {"id": 291, "displayName": "Stone Hoe", "stackSize": 1, "name": "stone_hoe"}, {"id": 292, "displayName": "Iron Hoe", "stackSize": 1, "name": "iron_hoe"}, {"id": 293, "displayName": "Diamond Hoe", "stackSize": 1, "name": "diamond_hoe"}, {"id": 294, "displayName": "Golden Hoe", "stackSize": 1, "name": "golden_hoe"}, {"id": 295, "displayName": "Seeds", "stackSize": 64, "name": "seeds"}, {"id": 296, "displayName": "Wheat", "stackSize": 64, "name": "wheat"}, {"id": 297, "displayName": "Bread", "stackSize": 64, "name": "bread"}, {"id": 298, "displayName": "Leather Cap", "stackSize": 1, "name": "leather_cap"}, {"id": 299, "displayName": "<PERSON><PERSON>", "stackSize": 1, "name": "leather_tunic"}, {"id": 300, "displayName": "<PERSON><PERSON>", "stackSize": 1, "name": "leather_pants"}, {"id": 301, "displayName": "<PERSON><PERSON>", "stackSize": 1, "name": "leather_boots"}, {"id": 302, "displayName": "Chain Helmet", "stackSize": 1, "name": "chain_helmet"}, {"id": 303, "displayName": "Chain Chestplate", "stackSize": 1, "name": "chain_chestplate"}, {"id": 304, "displayName": "Chain Leggings", "stackSize": 1, "name": "chain_leggings"}, {"id": 305, "displayName": "Chain Boots", "stackSize": 1, "name": "chain_boots"}, {"id": 306, "displayName": "Iron Helmet", "stackSize": 1, "name": "iron_helmet"}, {"id": 307, "displayName": "Iron Chestplate", "stackSize": 1, "name": "iron_chestplate"}, {"id": 308, "displayName": "Iron Leggings", "stackSize": 1, "name": "iron_leggings"}, {"id": 309, "displayName": "Iron Boots", "stackSize": 1, "name": "iron_boots"}, {"id": 310, "displayName": "Diamond Helmet", "stackSize": 1, "name": "diamond_helmet"}, {"id": 311, "displayName": "Diamond Chestplate", "stackSize": 1, "name": "diamond_chestplate"}, {"id": 312, "displayName": "Diamond Leggings", "stackSize": 1, "name": "diamond_leggings"}, {"id": 313, "displayName": "Diamond Boots", "stackSize": 1, "name": "diamond_boots"}, {"id": 314, "displayName": "Golden Helmet", "stackSize": 1, "name": "golden_helmet"}, {"id": 315, "displayName": "Golden Chestplate", "stackSize": 1, "name": "golden_chestplate"}, {"id": 316, "displayName": "Golden Leggings", "stackSize": 1, "name": "golden_leggings"}, {"id": 317, "displayName": "Golden Boots", "stackSize": 1, "name": "golden_boots"}, {"id": 318, "displayName": "Flint", "stackSize": 64, "name": "flint"}, {"id": 319, "displayName": "Raw Porkchop", "stackSize": 64, "name": "raw_porkchop"}, {"id": 320, "displayName": "Cooked Porkchop", "stackSize": 64, "name": "cooked_porkchop"}, {"id": 321, "displayName": "Painting", "stackSize": 64, "name": "painting"}, {"id": 322, "displayName": "Golden Apple", "stackSize": 64, "name": "golden_apple", "variations": [{"metadata": 0, "displayName": "Golden Apple"}, {"metadata": 1, "displayName": "Enchanted Golden Apple"}]}, {"id": 323, "displayName": "Sign", "stackSize": 16, "name": "sign"}, {"id": 324, "displayName": "Wooden Door", "stackSize": 64, "name": "wooden_door"}, {"id": 325, "displayName": "Bucket", "stackSize": 16, "name": "bucket"}, {"id": 328, "displayName": "Minecart", "stackSize": 1, "name": "minecart"}, {"id": 329, "displayName": "Saddle", "stackSize": 1, "name": "saddle"}, {"id": 330, "displayName": "Iron Door", "stackSize": 64, "name": "iron_door"}, {"id": 331, "displayName": "Redstone", "stackSize": 64, "name": "redstone"}, {"id": 332, "displayName": "Snowball", "stackSize": 16, "name": "snowball"}, {"id": 333, "displayName": "Boat", "stackSize": 1, "name": "boat"}, {"id": 334, "displayName": "Leather", "stackSize": 64, "name": "leather"}, {"id": 336, "displayName": "Brick", "stackSize": 64, "name": "brick"}, {"id": 337, "displayName": "<PERSON>", "stackSize": 64, "name": "clay"}, {"id": 338, "displayName": "Sugar Cane", "stackSize": 64, "name": "sugar_cane"}, {"id": 339, "displayName": "Paper", "stackSize": 64, "name": "paper"}, {"id": 340, "displayName": "Book", "stackSize": 64, "name": "book"}, {"id": 341, "displayName": "Slimeball", "stackSize": 64, "name": "slimeball"}, {"id": 342, "displayName": "Minecart with Chest", "stackSize": 1, "name": "minecart_with_chest"}, {"id": 344, "displayName": "Egg", "stackSize": 16, "name": "egg"}, {"id": 345, "displayName": "<PERSON>mp<PERSON>", "stackSize": 64, "name": "compass"}, {"id": 346, "displayName": "Fishing Rod", "stackSize": 1, "name": "fishing_rod"}, {"id": 347, "displayName": "Clock", "stackSize": 64, "name": "clock"}, {"id": 348, "displayName": "Glowstone Dust", "stackSize": 64, "name": "glowstone_dust"}, {"id": 349, "displayName": "Raw Fish", "stackSize": 64, "name": "raw_fish", "variations": [{"metadata": 0, "displayName": "Raw Fish"}, {"metadata": 1, "displayName": "Raw Salmon"}, {"metadata": 2, "displayName": "Clownfish"}, {"metadata": 3, "displayName": "Pufferfish"}]}, {"id": 350, "displayName": "Cooked Fish", "stackSize": 64, "name": "cooked_fish"}, {"id": 351, "displayName": "Dye", "stackSize": 64, "name": "dye", "variations": [{"metadata": 0, "displayName": "Ink Sac"}, {"metadata": 1, "displayName": "Rose Red"}, {"metadata": 2, "displayName": "Cactus <PERSON>"}, {"metadata": 3, "displayName": "Cocoa Beans"}, {"metadata": 4, "displayName": "<PERSON><PERSON>"}, {"metadata": 5, "displayName": "Purple Dye"}, {"metadata": 6, "displayName": "<PERSON><PERSON>"}, {"metadata": 7, "displayName": "Light Gray D<PERSON>"}, {"metadata": 8, "displayName": "<PERSON>"}, {"metadata": 9, "displayName": "Pink Dye"}, {"metadata": 10, "displayName": "Lime Dye"}, {"metadata": 11, "displayName": "Dandelion Yellow"}, {"metadata": 12, "displayName": "Light Blue Dye"}, {"metadata": 13, "displayName": "<PERSON><PERSON><PERSON>"}, {"metadata": 14, "displayName": "Orange Dye"}, {"metadata": 15, "displayName": "<PERSON>"}]}, {"id": 352, "displayName": "Bone", "stackSize": 64, "name": "bone"}, {"id": 353, "displayName": "Sugar", "stackSize": 64, "name": "sugar"}, {"id": 354, "displayName": "Cake", "stackSize": 1, "name": "cake"}, {"id": 355, "displayName": "Bed", "stackSize": 1, "name": "bed"}, {"id": 356, "displayName": "Redstone Repeater", "stackSize": 64, "name": "redstone_repeater"}, {"id": 357, "displayName": "<PERSON><PERSON>", "stackSize": 64, "name": "cookie"}, {"id": 358, "displayName": "Filled Map", "stackSize": 64, "name": "filled_map"}, {"id": 359, "displayName": "Shears", "stackSize": 1, "name": "shears"}, {"id": 360, "displayName": "Melon", "stackSize": 64, "name": "melon"}, {"id": 361, "displayName": "<PERSON><PERSON><PERSON> Seeds", "stackSize": 64, "name": "pumpkin_seeds"}, {"id": 362, "displayName": "<PERSON>on Seeds", "stackSize": 64, "name": "melon_seeds"}, {"id": 363, "displayName": "Raw Beef", "stackSize": 64, "name": "raw_beef"}, {"id": 364, "displayName": "Steak", "stackSize": 64, "name": "steak"}, {"id": 365, "displayName": "Raw Chicken", "stackSize": 64, "name": "raw_chicken"}, {"id": 366, "displayName": "Cooked Chicken", "stackSize": 64, "name": "cooked_chicken"}, {"id": 367, "displayName": "Rotten Flesh", "stackSize": 64, "name": "rotten_flesh"}, {"id": 369, "displayName": "<PERSON>", "stackSize": 64, "name": "blaze_rod"}, {"id": 370, "displayName": "Ghast Tear", "stackSize": 64, "name": "ghast_tear"}, {"id": 371, "displayName": "Gold Nugget", "stackSize": 64, "name": "gold_nugget"}, {"id": 372, "displayName": "Nether Wart", "stackSize": 64, "name": "nether_wart"}, {"id": 373, "displayName": "Potion", "stackSize": 1, "name": "potion"}, {"id": 374, "displayName": "Glass Bottle", "stackSize": 64, "name": "glass_bottle"}, {"id": 375, "displayName": "Spider Eye", "stackSize": 64, "name": "spider_eye"}, {"id": 376, "displayName": "Fermented Spider Eye", "stackSize": 64, "name": "fermented_spider_eye"}, {"id": 377, "displayName": "<PERSON>", "stackSize": 64, "name": "blaze_powder"}, {"id": 378, "displayName": "Magma Cream", "stackSize": 64, "name": "magma_cream"}, {"id": 379, "displayName": "Brewing Stand", "stackSize": 64, "name": "brewing_stand"}, {"id": 380, "displayName": "<PERSON><PERSON><PERSON>", "stackSize": 64, "name": "cauldron"}, {"id": 382, "displayName": "Glistering <PERSON><PERSON>", "stackSize": 64, "name": "glistering_melon"}, {"id": 383, "displayName": "Spawn Egg", "stackSize": 64, "name": "spawn_egg"}, {"id": 384, "displayName": "Bottle o' Enchanting", "stackSize": 64, "name": "bottle_o'_enchanting"}, {"id": 385, "displayName": "Fire Charge", "stackSize": 64, "name": "fire_charge"}, {"id": 388, "displayName": "Emerald", "stackSize": 64, "name": "emerald"}, {"id": 389, "displayName": "<PERSON><PERSON>", "stackSize": 64, "name": "item_frame"}, {"id": 390, "displayName": "Flower Pot", "stackSize": 64, "name": "flower_pot", "variations": [{"metadata": 0, "displayName": "Empty Flower Pot"}, {"metadata": 1, "displayName": "Poppy Flower Pot"}, {"metadata": 2, "displayName": "Dandelion Flower Pot"}, {"metadata": 3, "displayName": "Oak sapling Flower Pot"}, {"metadata": 4, "displayName": "Spruce sapling Flower Pot"}, {"metadata": 5, "displayName": "Birch sapling Flower Pot"}, {"metadata": 6, "displayName": "Jungle sapling Flower Pot"}, {"metadata": 7, "displayName": "Red mushroom Flower Pot"}, {"metadata": 8, "displayName": "Brown mushroom Flower Pot"}, {"metadata": 9, "displayName": "Cactus Flower Pot"}, {"metadata": 10, "displayName": "Dead bush Flower Pot"}, {"metadata": 11, "displayName": "<PERSON><PERSON>"}, {"metadata": 12, "displayName": "Acacia sapling Flower Pot"}, {"metadata": 13, "displayName": "Dark oak sapling Flower Pot"}]}, {"id": 391, "displayName": "Carrot", "stackSize": 64, "name": "carrot"}, {"id": 392, "displayName": "Potato", "stackSize": 64, "name": "potato"}, {"id": 393, "displayName": "Baked Potato", "stackSize": 64, "name": "baked_potato"}, {"id": 394, "displayName": "Poisonous Potato", "stackSize": 64, "name": "poisonous_potato"}, {"id": 395, "displayName": "Empty Map", "stackSize": 64, "name": "map"}, {"id": 396, "displayName": "Golden Carrot", "stackSize": 64, "name": "golden_carrot"}, {"id": 397, "displayName": "Mob head", "stackSize": 64, "name": "mob_head", "variations": [{"metadata": 0, "displayName": "Skeleton Skull"}, {"metadata": 1, "displayName": "Wither Skeleton Skull"}, {"metadata": 2, "displayName": "Zombie Head"}, {"metadata": 3, "displayName": "Head"}, {"metadata": 4, "displayName": "Creeper Head"}]}, {"id": 398, "displayName": "Carrot on a Stick", "stackSize": 1, "name": "carrot_on_a_stick"}, {"id": 400, "displayName": "Pumpkin Pie", "stackSize": 64, "name": "pumpkin_pie"}, {"id": 403, "displayName": "Enchanted Book", "stackSize": 1, "name": "enchanted_book"}, {"id": 404, "displayName": "Redstone Comparator", "stackSize": 64, "name": "comparator"}, {"id": 405, "displayName": "Nether Brick", "stackSize": 64, "name": "nether_brick"}, {"id": 406, "displayName": "<PERSON><PERSON>", "stackSize": 64, "name": "nether_quartz"}, {"id": 407, "displayName": "Minecart with TNT", "stackSize": 1, "name": "minecart_with_tnt"}, {"id": 408, "displayName": "Minecart with <PERSON>", "stackSize": 1, "name": "minecart_with_hopper"}, {"id": 410, "displayName": "<PERSON>", "stackSize": 64, "name": "hopper"}, {"id": 411, "displayName": "Raw Rabbit", "stackSize": 64, "name": "raw_rabbit"}, {"id": 412, "displayName": "Cooked Rabbit", "stackSize": 64, "name": "cooked_rabbit"}, {"id": 413, "displayName": "Rabbit Stew", "stackSize": 1, "name": "rabbit_stew"}, {"id": 414, "displayName": "<PERSON>'s Foot", "stackSize": 64, "name": "rabbit's_foot"}, {"id": 415, "displayName": "<PERSON>", "stackSize": 64, "name": "rabbit_hide"}, {"id": 416, "displayName": "Leather Horse Armor", "stackSize": 1, "name": "leather_horse_armor"}, {"id": 417, "displayName": "Iron Horse Armor", "stackSize": 1, "name": "iron_horse_armor"}, {"id": 418, "displayName": "Golden Horse Armor", "stackSize": 1, "name": "golden_horse_armor"}, {"id": 419, "displayName": "Diamond Horse Armor", "stackSize": 1, "name": "diamond_horse_armor"}, {"id": 420, "displayName": "Lead", "stackSize": 64, "name": "lead"}, {"id": 421, "displayName": "Name Tag", "stackSize": 64, "name": "name_tag"}, {"id": 423, "displayName": "<PERSON>", "stackSize": 64, "name": "mutton"}, {"id": 424, "displayName": "Cooked <PERSON>tton", "stackSize": 64, "name": "cooked_mutton"}, {"id": 427, "displayName": "Spruce Door", "stackSize": 64, "name": "spruce_door"}, {"id": 428, "displayName": "<PERSON>", "stackSize": 64, "name": "birch_door"}, {"id": 429, "displayName": "Jungle Door", "stackSize": 64, "name": "jungle_door"}, {"id": 430, "displayName": "Acacia Door", "stackSize": 64, "name": "acacia_door"}, {"id": 431, "displayName": "Dark Oak Door", "stackSize": 64, "name": "dark_oak_door"}, {"id": 438, "displayName": "Splash Potion", "stackSize": 1, "name": "splash_potion"}, {"id": 457, "displayName": "Beetroot", "stackSize": 64, "name": "beetroot"}, {"id": 458, "displayName": "Beetroot Seeds", "stackSize": 64, "name": "beetroot_seeds"}, {"id": 459, "displayName": "Beetroot Soup", "stackSize": 1, "name": "beetroot_soup"}, {"id": 460, "displayName": "Raw Salmon", "stackSize": 64, "name": "raw_salmon"}, {"id": 461, "displayName": "Clownfish", "stackSize": 64, "name": "clownfish"}, {"id": 462, "displayName": "Pufferfish", "stackSize": 64, "name": "pufferfish"}, {"id": 463, "displayName": "Cooked Salmon", "stackSize": 64, "name": "cooked_salmon"}, {"id": 466, "displayName": "Enchanted Golden Apple", "stackSize": 64, "name": "enchanted_golden_apple", "variations": [{"metadata": 0, "displayName": "Golden Apple"}, {"metadata": 1, "displayName": "Enchanted Golden Apple"}]}, {"id": 498, "displayName": "Camera", "stackSize": 64, "name": "camera"}]