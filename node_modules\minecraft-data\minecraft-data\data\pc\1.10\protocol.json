{"types": {"varint": "native", "varlong": "native", "pstring": "native", "u16": "native", "u8": "native", "i64": "native", "buffer": "native", "i32": "native", "i8": "native", "bool": "native", "i16": "native", "f32": "native", "f64": "native", "UUID": "native", "option": "native", "entityMetadataLoop": "native", "bitfield": "native", "container": "native", "switch": "native", "void": "native", "array": "native", "restBuffer": "native", "nbt": "native", "optionalNbt": "native", "string": ["pstring", {"countType": "varint"}], "slot": ["container", [{"name": "blockId", "type": "i16"}, {"anon": true, "type": ["switch", {"compareTo": "blockId", "fields": {"-1": "void"}, "default": ["container", [{"name": "itemCount", "type": "i8"}, {"name": "itemDamage", "type": "i16"}, {"name": "nbtData", "type": "optionalNbt"}]]}]}]], "position": ["bitfield", [{"name": "x", "size": 26, "signed": true}, {"name": "y", "size": 12, "signed": true}, {"name": "z", "size": 26, "signed": true}]], "entityMetadataItem": ["switch", {"compareTo": "$compareTo", "fields": {"0": "i8", "1": "varint", "2": "f32", "3": "string", "4": "string", "5": "slot", "6": "bool", "7": ["container", [{"name": "pitch", "type": "f32"}, {"name": "yaw", "type": "f32"}, {"name": "roll", "type": "f32"}]], "8": "position", "9": ["option", "position"], "10": "varint", "11": ["option", "UUID"], "12": "varint"}}], "entityMetadata": ["entityMetadataLoop", {"endVal": 255, "type": ["container", [{"anon": true, "type": ["container", [{"name": "key", "type": "u8"}, {"name": "type", "type": "i8"}]]}, {"name": "value", "type": ["entityMetadataItem", {"compareTo": "type"}]}]]}]}, "handshaking": {"toClient": {"types": {"packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {}}]}]]}}, "toServer": {"types": {"packet_set_protocol": ["container", [{"name": "protocolVersion", "type": "varint"}, {"name": "serverHost", "type": "string"}, {"name": "serverPort", "type": "u16"}, {"name": "nextState", "type": "varint"}]], "packet_legacy_server_list_ping": ["container", [{"name": "payload", "type": "u8"}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "set_protocol", "0xfe": "legacy_server_list_ping"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"set_protocol": "packet_set_protocol", "legacy_server_list_ping": "packet_legacy_server_list_ping"}}]}]]}}}, "status": {"toClient": {"types": {"packet_server_info": ["container", [{"name": "response", "type": "string"}]], "packet_ping": ["container", [{"name": "time", "type": "i64"}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "server_info", "0x01": "ping"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"server_info": "packet_server_info", "ping": "packet_ping"}}]}]]}}, "toServer": {"types": {"packet_ping_start": ["container", []], "packet_ping": ["container", [{"name": "time", "type": "i64"}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "ping_start", "0x01": "ping"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"ping_start": "packet_ping_start", "ping": "packet_ping"}}]}]]}}}, "login": {"toClient": {"types": {"packet_disconnect": ["container", [{"name": "reason", "type": "string"}]], "packet_encryption_begin": ["container", [{"name": "serverId", "type": "string"}, {"name": "public<PERSON>ey", "type": ["buffer", {"countType": "varint"}]}, {"name": "verifyToken", "type": ["buffer", {"countType": "varint"}]}]], "packet_success": ["container", [{"name": "uuid", "type": "string"}, {"name": "username", "type": "string"}]], "packet_compress": ["container", [{"name": "threshold", "type": "varint"}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "disconnect", "0x01": "encryption_begin", "0x02": "success", "0x03": "compress"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"disconnect": "packet_disconnect", "encryption_begin": "packet_encryption_begin", "success": "packet_success", "compress": "packet_compress"}}]}]]}}, "toServer": {"types": {"packet_login_start": ["container", [{"name": "username", "type": "string"}]], "packet_encryption_begin": ["container", [{"name": "sharedSecret", "type": ["buffer", {"countType": "varint"}]}, {"name": "verifyToken", "type": ["buffer", {"countType": "varint"}]}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "login_start", "0x01": "encryption_begin"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"login_start": "packet_login_start", "encryption_begin": "packet_encryption_begin"}}]}]]}}}, "play": {"toClient": {"types": {"packet_spawn_entity": ["container", [{"name": "entityId", "type": "varint"}, {"name": "objectUUID", "type": "UUID"}, {"name": "type", "type": "i8"}, {"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "pitch", "type": "i8"}, {"name": "yaw", "type": "i8"}, {"name": "objectData", "type": "i32"}, {"name": "velocityX", "type": "i16"}, {"name": "velocityY", "type": "i16"}, {"name": "velocityZ", "type": "i16"}]], "packet_spawn_entity_experience_orb": ["container", [{"name": "entityId", "type": "varint"}, {"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "count", "type": "i16"}]], "packet_spawn_entity_weather": ["container", [{"name": "entityId", "type": "varint"}, {"name": "type", "type": "i8"}, {"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}]], "packet_spawn_entity_living": ["container", [{"name": "entityId", "type": "varint"}, {"name": "entityUUID", "type": "UUID"}, {"name": "type", "type": "u8"}, {"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "yaw", "type": "i8"}, {"name": "pitch", "type": "i8"}, {"name": "head<PERSON><PERSON>", "type": "i8"}, {"name": "velocityX", "type": "i16"}, {"name": "velocityY", "type": "i16"}, {"name": "velocityZ", "type": "i16"}, {"name": "metadata", "type": "entityMetadata"}]], "packet_spawn_entity_painting": ["container", [{"name": "entityId", "type": "varint"}, {"name": "entityUUID", "type": "UUID"}, {"name": "title", "type": "string"}, {"name": "location", "type": "position"}, {"name": "direction", "type": "u8"}]], "packet_named_entity_spawn": ["container", [{"name": "entityId", "type": "varint"}, {"name": "playerUUID", "type": "UUID"}, {"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "yaw", "type": "i8"}, {"name": "pitch", "type": "i8"}, {"name": "metadata", "type": "entityMetadata"}]], "packet_animation": ["container", [{"name": "entityId", "type": "varint"}, {"name": "animation", "type": "u8"}]], "packet_statistics": ["container", [{"name": "entries", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "name", "type": "string"}, {"name": "value", "type": "varint"}]]}]}]], "packet_block_break_animation": ["container", [{"name": "entityId", "type": "varint"}, {"name": "location", "type": "position"}, {"name": "destroyStage", "type": "i8"}]], "packet_tile_entity_data": ["container", [{"name": "location", "type": "position"}, {"name": "action", "type": "u8"}, {"name": "nbtData", "type": "optionalNbt"}]], "packet_block_action": ["container", [{"name": "location", "type": "position"}, {"name": "byte1", "type": "u8"}, {"name": "byte2", "type": "u8"}, {"name": "blockId", "type": "varint"}]], "packet_block_change": ["container", [{"name": "location", "type": "position"}, {"name": "type", "type": "varint"}]], "packet_boss_bar": ["container", [{"name": "entityUUID", "type": "UUID"}, {"name": "action", "type": "varint"}, {"name": "title", "type": ["switch", {"compareTo": "action", "fields": {"0": "string", "3": "string"}, "default": "void"}]}, {"name": "health", "type": ["switch", {"compareTo": "action", "fields": {"0": "f32", "2": "f32"}, "default": "void"}]}, {"name": "color", "type": ["switch", {"compareTo": "action", "fields": {"0": "varint", "4": "varint"}, "default": "void"}]}, {"name": "dividers", "type": ["switch", {"compareTo": "action", "fields": {"0": "varint", "4": "varint"}, "default": "void"}]}, {"name": "flags", "type": ["switch", {"compareTo": "action", "fields": {"0": "u8", "5": "u8"}, "default": "void"}]}]], "packet_difficulty": ["container", [{"name": "difficulty", "type": "u8"}]], "packet_tab_complete": ["container", [{"name": "matches", "type": ["array", {"countType": "varint", "type": "string"}]}]], "packet_chat": ["container", [{"name": "message", "type": "string"}, {"name": "position", "type": "i8"}]], "packet_multi_block_change": ["container", [{"name": "chunkX", "type": "i32"}, {"name": "chunkZ", "type": "i32"}, {"name": "records", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "horizontalPos", "type": "u8"}, {"name": "y", "type": "u8"}, {"name": "blockId", "type": "varint"}]]}]}]], "packet_transaction": ["container", [{"name": "windowId", "type": "i8"}, {"name": "action", "type": "i16"}, {"name": "accepted", "type": "bool"}]], "packet_close_window": ["container", [{"name": "windowId", "type": "u8"}]], "packet_open_window": ["container", [{"name": "windowId", "type": "u8"}, {"name": "inventoryType", "type": "string"}, {"name": "windowTitle", "type": "string"}, {"name": "slotCount", "type": "u8"}, {"name": "entityId", "type": ["switch", {"compareTo": "inventoryType", "fields": {"EntityHorse": "i32"}, "default": "void"}]}]], "packet_window_items": ["container", [{"name": "windowId", "type": "u8"}, {"name": "items", "type": ["array", {"countType": "i16", "type": "slot"}]}]], "packet_craft_progress_bar": ["container", [{"name": "windowId", "type": "u8"}, {"name": "property", "type": "i16"}, {"name": "value", "type": "i16"}]], "packet_set_slot": ["container", [{"name": "windowId", "type": "i8"}, {"name": "slot", "type": "i16"}, {"name": "item", "type": "slot"}]], "packet_set_cooldown": ["container", [{"name": "itemID", "type": "varint"}, {"name": "cooldownTicks", "type": "varint"}]], "packet_custom_payload": ["container", [{"name": "channel", "type": "string"}, {"name": "data", "type": "restBuffer"}]], "packet_named_sound_effect": ["container", [{"name": "soundName", "type": "string"}, {"name": "soundCategory", "type": "varint"}, {"name": "x", "type": "i32"}, {"name": "y", "type": "i32"}, {"name": "z", "type": "i32"}, {"name": "volume", "type": "f32"}, {"name": "pitch", "type": "f32"}]], "packet_kick_disconnect": ["container", [{"name": "reason", "type": "string"}]], "packet_entity_status": ["container", [{"name": "entityId", "type": "i32"}, {"name": "entityStatus", "type": "i8"}]], "packet_explosion": ["container", [{"name": "x", "type": "f32"}, {"name": "y", "type": "f32"}, {"name": "z", "type": "f32"}, {"name": "radius", "type": "f32"}, {"name": "affectedBlockOffsets", "type": ["array", {"countType": "i32", "type": ["container", [{"name": "x", "type": "i8"}, {"name": "y", "type": "i8"}, {"name": "z", "type": "i8"}]]}]}, {"name": "playerMotionX", "type": "f32"}, {"name": "playerMotionY", "type": "f32"}, {"name": "playerMotionZ", "type": "f32"}]], "packet_unload_chunk": ["container", [{"name": "chunkX", "type": "i32"}, {"name": "chunkZ", "type": "i32"}]], "packet_game_state_change": ["container", [{"name": "reason", "type": "u8"}, {"name": "gameMode", "type": "f32"}]], "packet_keep_alive": ["container", [{"name": "keepAliveId", "type": "varint"}]], "packet_map_chunk": ["container", [{"name": "x", "type": "i32"}, {"name": "z", "type": "i32"}, {"name": "groundUp", "type": "bool"}, {"name": "bitMap", "type": "varint"}, {"name": "chunkData", "type": ["buffer", {"countType": "varint"}]}, {"name": "blockEntities", "type": ["array", {"countType": "varint", "type": "nbt"}]}]], "packet_world_event": ["container", [{"name": "effectId", "type": "i32"}, {"name": "location", "type": "position"}, {"name": "data", "type": "i32"}, {"name": "global", "type": "bool"}]], "packet_world_particles": ["container", [{"name": "particleId", "type": "i32"}, {"name": "longDistance", "type": "bool"}, {"name": "x", "type": "f32"}, {"name": "y", "type": "f32"}, {"name": "z", "type": "f32"}, {"name": "offsetX", "type": "f32"}, {"name": "offsetY", "type": "f32"}, {"name": "offsetZ", "type": "f32"}, {"name": "particleData", "type": "f32"}, {"name": "particles", "type": "i32"}, {"name": "data", "type": ["switch", {"compareTo": "particleId", "fields": {"36": ["array", {"count": 2, "type": "varint"}], "37": ["array", {"count": 1, "type": "varint"}], "38": ["array", {"count": 1, "type": "varint"}]}, "default": "void"}]}]], "packet_login": ["container", [{"name": "entityId", "type": "i32"}, {"name": "gameMode", "type": "u8"}, {"name": "dimension", "type": "i32"}, {"name": "difficulty", "type": "u8"}, {"name": "maxPlayers", "type": "u8"}, {"name": "levelType", "type": "string"}, {"name": "reducedDebugInfo", "type": "bool"}]], "packet_map": ["container", [{"name": "itemDamage", "type": "varint"}, {"name": "scale", "type": "i8"}, {"name": "trackingPosition", "type": "bool"}, {"name": "icons", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "directionAndType", "type": "i8"}, {"name": "x", "type": "i8"}, {"name": "z", "type": "i8"}]]}]}, {"name": "columns", "type": "i8"}, {"name": "rows", "type": ["switch", {"compareTo": "columns", "fields": {"0": "void"}, "default": "i8"}]}, {"name": "x", "type": ["switch", {"compareTo": "columns", "fields": {"0": "void"}, "default": "i8"}]}, {"name": "y", "type": ["switch", {"compareTo": "columns", "fields": {"0": "void"}, "default": "i8"}]}, {"name": "data", "type": ["switch", {"compareTo": "columns", "fields": {"0": "void"}, "default": ["buffer", {"countType": "varint"}]}]}]], "packet_rel_entity_move": ["container", [{"name": "entityId", "type": "varint"}, {"name": "dX", "type": "i16"}, {"name": "dY", "type": "i16"}, {"name": "dZ", "type": "i16"}, {"name": "onGround", "type": "bool"}]], "packet_entity_move_look": ["container", [{"name": "entityId", "type": "varint"}, {"name": "dX", "type": "i16"}, {"name": "dY", "type": "i16"}, {"name": "dZ", "type": "i16"}, {"name": "yaw", "type": "i8"}, {"name": "pitch", "type": "i8"}, {"name": "onGround", "type": "bool"}]], "packet_entity_look": ["container", [{"name": "entityId", "type": "varint"}, {"name": "yaw", "type": "i8"}, {"name": "pitch", "type": "i8"}, {"name": "onGround", "type": "bool"}]], "packet_entity": ["container", [{"name": "entityId", "type": "varint"}]], "packet_vehicle_move": ["container", [{"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "yaw", "type": "f32"}, {"name": "pitch", "type": "f32"}]], "packet_open_sign_entity": ["container", [{"name": "location", "type": "position"}]], "packet_abilities": ["container", [{"name": "flags", "type": "i8"}, {"name": "flyingSpeed", "type": "f32"}, {"name": "walkingSpeed", "type": "f32"}]], "packet_combat_event": ["container", [{"name": "event", "type": "varint"}, {"name": "duration", "type": ["switch", {"compareTo": "event", "fields": {"1": "varint"}, "default": "void"}]}, {"name": "playerId", "type": ["switch", {"compareTo": "event", "fields": {"2": "varint"}, "default": "void"}]}, {"name": "entityId", "type": ["switch", {"compareTo": "event", "fields": {"1": "i32", "2": "i32"}, "default": "void"}]}, {"name": "message", "type": ["switch", {"compareTo": "event", "fields": {"2": "string"}, "default": "void"}]}]], "packet_player_info": ["container", [{"name": "action", "type": "varint"}, {"name": "data", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "UUID", "type": "UUID"}, {"name": "name", "type": ["switch", {"compareTo": "../action", "fields": {"0": "string"}, "default": "void"}]}, {"name": "properties", "type": ["switch", {"compareTo": "../action", "fields": {"0": ["array", {"countType": "varint", "type": ["container", [{"name": "name", "type": "string"}, {"name": "value", "type": "string"}, {"name": "signature", "type": ["option", "string"]}]]}]}, "default": "void"}]}, {"name": "gamemode", "type": ["switch", {"compareTo": "../action", "fields": {"0": "varint", "1": "varint"}, "default": "void"}]}, {"name": "ping", "type": ["switch", {"compareTo": "../action", "fields": {"0": "varint", "2": "varint"}, "default": "void"}]}, {"name": "displayName", "type": ["switch", {"compareTo": "../action", "fields": {"0": ["option", "string"], "3": ["option", "string"]}, "default": "void"}]}]]}]}]], "packet_position": ["container", [{"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "yaw", "type": "f32"}, {"name": "pitch", "type": "f32"}, {"name": "flags", "type": "i8"}, {"name": "teleportId", "type": "varint"}]], "packet_bed": ["container", [{"name": "entityId", "type": "varint"}, {"name": "location", "type": "position"}]], "packet_entity_destroy": ["container", [{"name": "entityIds", "type": ["array", {"countType": "varint", "type": "varint"}]}]], "packet_remove_entity_effect": ["container", [{"name": "entityId", "type": "varint"}, {"name": "effectId", "type": "i8"}]], "packet_resource_pack_send": ["container", [{"name": "url", "type": "string"}, {"name": "hash", "type": "string"}]], "packet_respawn": ["container", [{"name": "dimension", "type": "i32"}, {"name": "difficulty", "type": "u8"}, {"name": "gamemode", "type": "u8"}, {"name": "levelType", "type": "string"}]], "packet_entity_head_rotation": ["container", [{"name": "entityId", "type": "varint"}, {"name": "headYaw", "type": "i8"}]], "packet_world_border": ["container", [{"name": "action", "type": "varint"}, {"name": "radius", "type": ["switch", {"compareTo": "action", "fields": {"0": "f64"}, "default": "void"}]}, {"name": "x", "type": ["switch", {"compareTo": "action", "fields": {"2": "f64", "3": "f64"}, "default": "void"}]}, {"name": "z", "type": ["switch", {"compareTo": "action", "fields": {"2": "f64", "3": "f64"}, "default": "void"}]}, {"name": "old_radius", "type": ["switch", {"compareTo": "action", "fields": {"1": "f64", "3": "f64"}, "default": "void"}]}, {"name": "new_radius", "type": ["switch", {"compareTo": "action", "fields": {"1": "f64", "3": "f64"}, "default": "void"}]}, {"name": "speed", "type": ["switch", {"compareTo": "action", "fields": {"1": "varlong", "3": "varlong"}, "default": "void"}]}, {"name": "portalBoundary", "type": ["switch", {"compareTo": "action", "fields": {"3": "varint"}, "default": "void"}]}, {"name": "warning_time", "type": ["switch", {"compareTo": "action", "fields": {"3": "varint", "4": "varint"}, "default": "void"}]}, {"name": "warning_blocks", "type": ["switch", {"compareTo": "action", "fields": {"3": "varint", "5": "varint"}, "default": "void"}]}]], "packet_camera": ["container", [{"name": "cameraId", "type": "varint"}]], "packet_held_item_slot": ["container", [{"name": "slot", "type": "i8"}]], "packet_scoreboard_display_objective": ["container", [{"name": "position", "type": "i8"}, {"name": "name", "type": "string"}]], "packet_entity_metadata": ["container", [{"name": "entityId", "type": "varint"}, {"name": "metadata", "type": "entityMetadata"}]], "packet_attach_entity": ["container", [{"name": "entityId", "type": "i32"}, {"name": "vehicleId", "type": "i32"}]], "packet_entity_velocity": ["container", [{"name": "entityId", "type": "varint"}, {"name": "velocityX", "type": "i16"}, {"name": "velocityY", "type": "i16"}, {"name": "velocityZ", "type": "i16"}]], "packet_entity_equipment": ["container", [{"name": "entityId", "type": "varint"}, {"name": "slot", "type": "varint"}, {"name": "item", "type": "slot"}]], "packet_experience": ["container", [{"name": "experienceBar", "type": "f32"}, {"name": "level", "type": "varint"}, {"name": "totalExperience", "type": "varint"}]], "packet_update_health": ["container", [{"name": "health", "type": "f32"}, {"name": "food", "type": "varint"}, {"name": "foodSaturation", "type": "f32"}]], "packet_scoreboard_objective": ["container", [{"name": "name", "type": "string"}, {"name": "action", "type": "i8"}, {"name": "displayText", "type": ["switch", {"compareTo": "action", "fields": {"0": "string", "2": "string"}, "default": "void"}]}, {"name": "type", "type": ["switch", {"compareTo": "action", "fields": {"0": "string", "2": "string"}, "default": "void"}]}]], "packet_set_passengers": ["container", [{"name": "entityId", "type": "varint"}, {"name": "passengers", "type": ["array", {"countType": "varint", "type": "varint"}]}]], "packet_teams": ["container", [{"name": "team", "type": "string"}, {"name": "mode", "type": "i8"}, {"name": "name", "type": ["switch", {"compareTo": "mode", "fields": {"0": "string", "2": "string"}, "default": "void"}]}, {"name": "prefix", "type": ["switch", {"compareTo": "mode", "fields": {"0": "string", "2": "string"}, "default": "void"}]}, {"name": "suffix", "type": ["switch", {"compareTo": "mode", "fields": {"0": "string", "2": "string"}, "default": "void"}]}, {"name": "friendlyFire", "type": ["switch", {"compareTo": "mode", "fields": {"0": "i8", "2": "i8"}, "default": "void"}]}, {"name": "nameTagVisibility", "type": ["switch", {"compareTo": "mode", "fields": {"0": "string", "2": "string"}, "default": "void"}]}, {"name": "collisionRule", "type": ["switch", {"compareTo": "mode", "fields": {"0": "string", "2": "string"}, "default": "void"}]}, {"name": "color", "type": ["switch", {"compareTo": "mode", "fields": {"0": "i8", "2": "i8"}, "default": "void"}]}, {"name": "players", "type": ["switch", {"compareTo": "mode", "fields": {"0": ["array", {"countType": "varint", "type": "string"}], "3": ["array", {"countType": "varint", "type": "string"}], "4": ["array", {"countType": "varint", "type": "string"}]}, "default": "void"}]}]], "packet_scoreboard_score": ["container", [{"name": "itemName", "type": "string"}, {"name": "action", "type": "varint"}, {"name": "scoreName", "type": "string"}, {"name": "value", "type": ["switch", {"compareTo": "action", "fields": {"1": "void"}, "default": "varint"}]}]], "packet_spawn_position": ["container", [{"name": "location", "type": "position"}]], "packet_update_time": ["container", [{"name": "age", "type": "i64"}, {"name": "time", "type": "i64"}]], "packet_title": ["container", [{"name": "action", "type": "varint"}, {"name": "text", "type": ["switch", {"compareTo": "action", "fields": {"0": "string", "1": "string"}, "default": "void"}]}, {"name": "fadeIn", "type": ["switch", {"compareTo": "action", "fields": {"2": "i32"}, "default": "void"}]}, {"name": "stay", "type": ["switch", {"compareTo": "action", "fields": {"2": "i32"}, "default": "void"}]}, {"name": "fadeOut", "type": ["switch", {"compareTo": "action", "fields": {"2": "i32"}, "default": "void"}]}]], "packet_sound_effect": ["container", [{"name": "soundId", "type": "varint"}, {"name": "soundCategory", "type": "varint"}, {"name": "x", "type": "i32"}, {"name": "y", "type": "i32"}, {"name": "z", "type": "i32"}, {"name": "volume", "type": "f32"}, {"name": "pitch", "type": "f32"}]], "packet_playerlist_header": ["container", [{"name": "header", "type": "string"}, {"name": "footer", "type": "string"}]], "packet_collect": ["container", [{"name": "collectedEntityId", "type": "varint"}, {"name": "collectorEntityId", "type": "varint"}]], "packet_entity_teleport": ["container", [{"name": "entityId", "type": "varint"}, {"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "yaw", "type": "i8"}, {"name": "pitch", "type": "i8"}, {"name": "onGround", "type": "bool"}]], "packet_entity_update_attributes": ["container", [{"name": "entityId", "type": "varint"}, {"name": "properties", "type": ["array", {"countType": "i32", "type": ["container", [{"name": "key", "type": "string"}, {"name": "value", "type": "f64"}, {"name": "modifiers", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "uuid", "type": "UUID"}, {"name": "amount", "type": "f64"}, {"name": "operation", "type": "i8"}]]}]}]]}]}]], "packet_entity_effect": ["container", [{"name": "entityId", "type": "varint"}, {"name": "effectId", "type": "i8"}, {"name": "amplifier", "type": "i8"}, {"name": "duration", "type": "varint"}, {"name": "hideParticles", "type": "i8"}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "spawn_entity", "0x01": "spawn_entity_experience_orb", "0x02": "spawn_entity_weather", "0x03": "spawn_entity_living", "0x04": "spawn_entity_painting", "0x05": "named_entity_spawn", "0x06": "animation", "0x07": "statistics", "0x08": "block_break_animation", "0x09": "tile_entity_data", "0x0a": "block_action", "0x0b": "block_change", "0x0c": "boss_bar", "0x0d": "difficulty", "0x0e": "tab_complete", "0x0f": "chat", "0x10": "multi_block_change", "0x11": "transaction", "0x12": "close_window", "0x13": "open_window", "0x14": "window_items", "0x15": "craft_progress_bar", "0x16": "set_slot", "0x17": "set_cooldown", "0x18": "custom_payload", "0x19": "named_sound_effect", "0x1a": "kick_disconnect", "0x1b": "entity_status", "0x1c": "explosion", "0x1d": "unload_chunk", "0x1e": "game_state_change", "0x1f": "keep_alive", "0x20": "map_chunk", "0x21": "world_event", "0x22": "world_particles", "0x23": "login", "0x24": "map", "0x25": "rel_entity_move", "0x26": "entity_move_look", "0x27": "entity_look", "0x28": "entity", "0x29": "vehicle_move", "0x2a": "open_sign_entity", "0x2b": "abilities", "0x2c": "combat_event", "0x2d": "player_info", "0x2e": "position", "0x2f": "bed", "0x30": "entity_destroy", "0x31": "remove_entity_effect", "0x32": "resource_pack_send", "0x33": "respawn", "0x34": "entity_head_rotation", "0x35": "world_border", "0x36": "camera", "0x37": "held_item_slot", "0x38": "scoreboard_display_objective", "0x39": "entity_metadata", "0x3a": "attach_entity", "0x3b": "entity_velocity", "0x3c": "entity_equipment", "0x3d": "experience", "0x3e": "update_health", "0x3f": "scoreboard_objective", "0x40": "set_passengers", "0x41": "teams", "0x42": "scoreboard_score", "0x43": "spawn_position", "0x44": "update_time", "0x45": "title", "0x46": "sound_effect", "0x47": "playerlist_header", "0x48": "collect", "0x49": "entity_teleport", "0x4a": "entity_update_attributes", "0x4b": "entity_effect"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"spawn_entity": "packet_spawn_entity", "spawn_entity_experience_orb": "packet_spawn_entity_experience_orb", "spawn_entity_weather": "packet_spawn_entity_weather", "spawn_entity_living": "packet_spawn_entity_living", "spawn_entity_painting": "packet_spawn_entity_painting", "named_entity_spawn": "packet_named_entity_spawn", "animation": "packet_animation", "statistics": "packet_statistics", "block_break_animation": "packet_block_break_animation", "tile_entity_data": "packet_tile_entity_data", "block_action": "packet_block_action", "block_change": "packet_block_change", "boss_bar": "packet_boss_bar", "difficulty": "packet_difficulty", "tab_complete": "packet_tab_complete", "chat": "packet_chat", "multi_block_change": "packet_multi_block_change", "transaction": "packet_transaction", "close_window": "packet_close_window", "open_window": "packet_open_window", "window_items": "packet_window_items", "craft_progress_bar": "packet_craft_progress_bar", "set_slot": "packet_set_slot", "set_cooldown": "packet_set_cooldown", "custom_payload": "packet_custom_payload", "named_sound_effect": "packet_named_sound_effect", "kick_disconnect": "packet_kick_disconnect", "entity_status": "packet_entity_status", "explosion": "packet_explosion", "unload_chunk": "packet_unload_chunk", "game_state_change": "packet_game_state_change", "keep_alive": "packet_keep_alive", "map_chunk": "packet_map_chunk", "world_event": "packet_world_event", "world_particles": "packet_world_particles", "login": "packet_login", "map": "packet_map", "rel_entity_move": "packet_rel_entity_move", "entity_move_look": "packet_entity_move_look", "entity_look": "packet_entity_look", "entity": "packet_entity", "vehicle_move": "packet_vehicle_move", "open_sign_entity": "packet_open_sign_entity", "abilities": "packet_abilities", "combat_event": "packet_combat_event", "player_info": "packet_player_info", "position": "packet_position", "bed": "packet_bed", "entity_destroy": "packet_entity_destroy", "remove_entity_effect": "packet_remove_entity_effect", "resource_pack_send": "packet_resource_pack_send", "respawn": "packet_respawn", "entity_update_attributes": "packet_entity_update_attributes", "world_border": "packet_world_border", "camera": "packet_camera", "held_item_slot": "packet_held_item_slot", "scoreboard_display_objective": "packet_scoreboard_display_objective", "entity_metadata": "packet_entity_metadata", "attach_entity": "packet_attach_entity", "entity_velocity": "packet_entity_velocity", "entity_equipment": "packet_entity_equipment", "experience": "packet_experience", "update_health": "packet_update_health", "scoreboard_objective": "packet_scoreboard_objective", "set_passengers": "packet_set_passengers", "teams": "packet_teams", "scoreboard_score": "packet_scoreboard_score", "spawn_position": "packet_spawn_position", "update_time": "packet_update_time", "title": "packet_title", "sound_effect": "packet_sound_effect", "playerlist_header": "packet_playerlist_header", "collect": "packet_collect", "entity_teleport": "packet_entity_teleport", "entity_head_rotation": "packet_entity_head_rotation", "entity_effect": "packet_entity_effect"}}]}]]}}, "toServer": {"types": {"packet_teleport_confirm": ["container", [{"name": "teleportId", "type": "varint"}]], "packet_tab_complete": ["container", [{"name": "text", "type": "string"}, {"name": "assumeCommand", "type": "bool"}, {"name": "lookedAtBlock", "type": ["option", "position"]}]], "packet_chat": ["container", [{"name": "message", "type": "string"}]], "packet_client_command": ["container", [{"name": "actionId", "type": "varint"}]], "packet_settings": ["container", [{"name": "locale", "type": "string"}, {"name": "viewDistance", "type": "i8"}, {"name": "chatFlags", "type": "varint"}, {"name": "chatColors", "type": "bool"}, {"name": "skinParts", "type": "u8"}, {"name": "mainHand", "type": "varint"}]], "packet_transaction": ["container", [{"name": "windowId", "type": "i8"}, {"name": "action", "type": "i16"}, {"name": "accepted", "type": "bool"}]], "packet_enchant_item": ["container", [{"name": "windowId", "type": "i8"}, {"name": "enchantment", "type": "i8"}]], "packet_window_click": ["container", [{"name": "windowId", "type": "u8"}, {"name": "slot", "type": "i16"}, {"name": "mouseButton", "type": "i8"}, {"name": "action", "type": "i16"}, {"name": "mode", "type": "i8"}, {"name": "item", "type": "slot"}]], "packet_close_window": ["container", [{"name": "windowId", "type": "u8"}]], "packet_custom_payload": ["container", [{"name": "channel", "type": "string"}, {"name": "data", "type": "restBuffer"}]], "packet_use_entity": ["container", [{"name": "target", "type": "varint"}, {"name": "mouse", "type": "varint"}, {"name": "x", "type": ["switch", {"compareTo": "mouse", "fields": {"2": "f32"}, "default": "void"}]}, {"name": "y", "type": ["switch", {"compareTo": "mouse", "fields": {"2": "f32"}, "default": "void"}]}, {"name": "z", "type": ["switch", {"compareTo": "mouse", "fields": {"2": "f32"}, "default": "void"}]}, {"name": "hand", "type": ["switch", {"compareTo": "mouse", "fields": {"0": "varint", "2": "varint"}, "default": "void"}]}]], "packet_keep_alive": ["container", [{"name": "keepAliveId", "type": "varint"}]], "packet_position": ["container", [{"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "onGround", "type": "bool"}]], "packet_position_look": ["container", [{"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "yaw", "type": "f32"}, {"name": "pitch", "type": "f32"}, {"name": "onGround", "type": "bool"}]], "packet_look": ["container", [{"name": "yaw", "type": "f32"}, {"name": "pitch", "type": "f32"}, {"name": "onGround", "type": "bool"}]], "packet_flying": ["container", [{"name": "onGround", "type": "bool"}]], "packet_vehicle_move": ["container", [{"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "yaw", "type": "f32"}, {"name": "pitch", "type": "f32"}]], "packet_steer_boat": ["container", [{"name": "leftPaddle", "type": "bool"}, {"name": "rightPaddle", "type": "bool"}]], "packet_abilities": ["container", [{"name": "flags", "type": "i8"}, {"name": "flyingSpeed", "type": "f32"}, {"name": "walkingSpeed", "type": "f32"}]], "packet_block_dig": ["container", [{"name": "status", "type": "varint"}, {"name": "location", "type": "position"}, {"name": "face", "type": "i8"}]], "packet_entity_action": ["container", [{"name": "entityId", "type": "varint"}, {"name": "actionId", "type": "varint"}, {"name": "jumpBoost", "type": "varint"}]], "packet_steer_vehicle": ["container", [{"name": "sideways", "type": "f32"}, {"name": "forward", "type": "f32"}, {"name": "jump", "type": "u8"}]], "packet_resource_pack_receive": ["container", [{"name": "result", "type": "varint"}]], "packet_held_item_slot": ["container", [{"name": "slotId", "type": "i16"}]], "packet_set_creative_slot": ["container", [{"name": "slot", "type": "i16"}, {"name": "item", "type": "slot"}]], "packet_update_sign": ["container", [{"name": "location", "type": "position"}, {"name": "text1", "type": "string"}, {"name": "text2", "type": "string"}, {"name": "text3", "type": "string"}, {"name": "text4", "type": "string"}]], "packet_arm_animation": ["container", [{"name": "hand", "type": "varint"}]], "packet_spectate": ["container", [{"name": "target", "type": "UUID"}]], "packet_block_place": ["container", [{"name": "location", "type": "position"}, {"name": "direction", "type": "varint"}, {"name": "hand", "type": "varint"}, {"name": "cursorX", "type": "i8"}, {"name": "cursorY", "type": "i8"}, {"name": "cursorZ", "type": "i8"}]], "packet_use_item": ["container", [{"name": "hand", "type": "varint"}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "teleport_confirm", "0x01": "tab_complete", "0x02": "chat", "0x03": "client_command", "0x04": "settings", "0x05": "transaction", "0x06": "enchant_item", "0x07": "window_click", "0x08": "close_window", "0x09": "custom_payload", "0x0a": "use_entity", "0x0b": "keep_alive", "0x0c": "position", "0x0d": "position_look", "0x0e": "look", "0x0f": "flying", "0x10": "vehicle_move", "0x11": "steer_boat", "0x12": "abilities", "0x13": "block_dig", "0x14": "entity_action", "0x15": "steer_vehicle", "0x16": "resource_pack_receive", "0x17": "held_item_slot", "0x18": "set_creative_slot", "0x19": "update_sign", "0x1a": "arm_animation", "0x1b": "spectate", "0x1c": "block_place", "0x1d": "use_item"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"teleport_confirm": "packet_teleport_confirm", "tab_complete": "packet_tab_complete", "chat": "packet_chat", "client_command": "packet_client_command", "settings": "packet_settings", "transaction": "packet_transaction", "enchant_item": "packet_enchant_item", "window_click": "packet_window_click", "close_window": "packet_close_window", "custom_payload": "packet_custom_payload", "use_entity": "packet_use_entity", "keep_alive": "packet_keep_alive", "position": "packet_position", "position_look": "packet_position_look", "look": "packet_look", "flying": "packet_flying", "vehicle_move": "packet_vehicle_move", "steer_boat": "packet_steer_boat", "abilities": "packet_abilities", "block_dig": "packet_block_dig", "entity_action": "packet_entity_action", "steer_vehicle": "packet_steer_vehicle", "resource_pack_receive": "packet_resource_pack_receive", "held_item_slot": "packet_held_item_slot", "set_creative_slot": "packet_set_creative_slot", "update_sign": "packet_update_sign", "arm_animation": "packet_arm_animation", "spectate": "packet_spectate", "block_place": "packet_block_place", "use_item": "packet_use_item"}}]}]]}}}}