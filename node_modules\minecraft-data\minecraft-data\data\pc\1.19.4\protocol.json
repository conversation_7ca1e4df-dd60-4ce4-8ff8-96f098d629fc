{"types": {"varint": "native", "varlong": "native", "optvarint": "varint", "pstring": "native", "buffer": "native", "u8": "native", "u16": "native", "u32": "native", "u64": "native", "i8": "native", "i16": "native", "i32": "native", "i64": "native", "bool": "native", "f32": "native", "f64": "native", "UUID": "native", "option": "native", "entityMetadataLoop": "native", "topBitSetTerminatedArray": "native", "bitfield": "native", "container": "native", "switch": "native", "void": "native", "array": "native", "restBuffer": "native", "nbt": "native", "optionalNbt": "native", "string": ["pstring", {"countType": "varint"}], "vec3f": ["container", [{"name": "x", "type": "f32"}, {"name": "y", "type": "f32"}, {"name": "z", "type": "f32"}]], "vec4f": ["container", [{"name": "x", "type": "f32"}, {"name": "y", "type": "f32"}, {"name": "z", "type": "f32"}, {"name": "w", "type": "f32"}]], "vec3f64": ["container", [{"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}]], "slot": ["container", [{"name": "present", "type": "bool"}, {"anon": true, "type": ["switch", {"compareTo": "present", "fields": {"false": "void", "true": ["container", [{"name": "itemId", "type": "varint"}, {"name": "itemCount", "type": "i8"}, {"name": "nbtData", "type": "optionalNbt"}]]}}]}]], "particle": ["container", [{"name": "particleId", "type": "varint"}, {"name": "data", "type": ["particleData", {"compareTo": "particleId"}]}]], "particleData": ["switch", {"compareTo": "$compareTo", "fields": {"2": ["container", [{"name": "blockState", "type": "varint"}]], "3": ["container", [{"name": "blockState", "type": "varint"}]], "14": ["container", [{"name": "red", "type": "f32"}, {"name": "green", "type": "f32"}, {"name": "blue", "type": "f32"}, {"name": "scale", "type": "f32"}]], "15": ["container", [{"name": "fromRed", "type": "f32"}, {"name": "fromGreen", "type": "f32"}, {"name": "fromBlue", "type": "f32"}, {"name": "scale", "type": "f32"}, {"name": "toRed", "type": "f32"}, {"name": "<PERSON><PERSON><PERSON>", "type": "f32"}, {"name": "toBlue", "type": "f32"}]], "25": ["container", [{"name": "blockState", "type": "varint"}]], "33": ["container", [{"name": "rotation", "type": "f32"}]], "42": ["container", [{"name": "item", "type": "slot"}]], "43": ["container", [{"name": "positionType", "type": "string"}, {"name": "entityId", "type": ["switch", {"compareTo": "positionType", "fields": {"minecraft:entity": "varint"}, "default": "void"}]}, {"name": "entityEyeHeight", "type": ["switch", {"compareTo": "positionType", "fields": {"minecraft:entity": "varint"}, "default": "void"}]}, {"name": "destination", "type": ["switch", {"compareTo": "positionType", "fields": {"minecraft:block": "position", "minecraft:entity": "varint"}}]}, {"name": "ticks", "type": "varint"}]], "95": ["container", [{"name": "delayInTicksBeforeShown", "type": "varint"}]]}, "default": "void"}], "ingredient": ["array", {"countType": "varint", "type": "slot"}], "position": ["bitfield", [{"name": "x", "size": 26, "signed": true}, {"name": "z", "size": 26, "signed": true}, {"name": "y", "size": 12, "signed": true}]], "packedChunkPos": ["container", [{"name": "z", "type": "i32"}, {"name": "x", "type": "i32"}]], "soundSource": ["mapper", {"type": "varint", "mappings": {"0": "master", "1": "music", "2": "record", "3": "weather", "4": "block", "5": "hostile", "6": "neutral", "7": "player", "8": "ambient", "9": "voice"}}], "previousMessages": ["array", {"countType": "varint", "type": ["container", [{"name": "id", "type": "varint"}, {"name": "signature", "type": ["switch", {"compareTo": "id", "fields": {"0": ["buffer", {"count": 256}]}, "default": "void"}]}]]}], "entityMetadataItem": ["switch", {"compareTo": "$compareTo", "fields": {"byte": "i8", "int": "varint", "long": "varlong", "float": "f32", "string": "string", "component": "string", "optional_component": ["option", "string"], "item_stack": "slot", "boolean": "bool", "rotations": ["container", [{"name": "pitch", "type": "f32"}, {"name": "yaw", "type": "f32"}, {"name": "roll", "type": "f32"}]], "block_pos": "position", "optional_block_pos": ["option", "position"], "direction": "varint", "optional_uuid": ["option", "UUID"], "block_state": "varint", "optional_block_state": "optvarint", "compound_tag": "nbt", "particle": "particle", "villager_data": ["container", [{"name": "villagerType", "type": "varint"}, {"name": "villagerProfession", "type": "varint"}, {"name": "level", "type": "varint"}]], "optional_unsigned_int": "optvarint", "pose": "varint", "cat_variant": "varint", "frog_variant": "varint", "optional_global_pos": ["option", "string"], "painting_variant": "varint", "sniffer_state": "varint", "vector3": "vec3f", "quaternion": "vec4f"}}], "entityMetadata": ["entityMetadataLoop", {"endVal": 255, "type": ["container", [{"name": "key", "type": "u8"}, {"name": "type", "type": ["mapper", {"type": "varint", "mappings": {"0": "byte", "1": "int", "2": "long", "3": "float", "4": "string", "5": "component", "6": "optional_component", "7": "item_stack", "8": "boolean", "9": "rotations", "10": "block_pos", "11": "optional_block_pos", "12": "direction", "13": "optional_uuid", "14": "block_state", "15": "optional_block_state", "16": "compound_tag", "17": "particle", "18": "villager_data", "19": "optional_unsigned_int", "20": "pose", "21": "cat_variant", "22": "frog_variant", "23": "optional_global_pos", "24": "painting_variant", "25": "sniffer_state", "26": "vector3", "27": "quaternion"}}]}, {"name": "value", "type": ["entityMetadataItem", {"compareTo": "type"}]}]]}], "minecraft_simple_recipe_format": ["container", [{"name": "category", "type": "varint"}]], "minecraft_smelting_format": ["container", [{"name": "group", "type": "string"}, {"name": "category", "type": "varint"}, {"name": "ingredient", "type": "ingredient"}, {"name": "result", "type": "slot"}, {"name": "experience", "type": "f32"}, {"name": "cookTime", "type": "varint"}]], "tags": ["array", {"countType": "varint", "type": ["container", [{"name": "tagName", "type": "string"}, {"name": "entries", "type": ["array", {"countType": "varint", "type": "varint"}]}]]}], "chunkBlockEntity": ["container", [{"anon": true, "type": ["bitfield", [{"name": "x", "size": 4, "signed": false}, {"name": "z", "size": 4, "signed": false}]]}, {"name": "y", "type": "i16"}, {"name": "type", "type": "varint"}, {"name": "nbtData", "type": "optionalNbt"}]], "chat_session": ["option", ["container", [{"name": "uuid", "type": "UUID"}, {"name": "public<PERSON>ey", "type": ["container", [{"name": "expireTime", "type": "i64"}, {"name": "keyBytes", "type": ["buffer", {"countType": "varint"}]}, {"name": "keySignature", "type": ["buffer", {"countType": "varint"}]}]]}]]], "game_profile": ["container", [{"name": "name", "type": "string"}, {"name": "properties", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "key", "type": "string"}, {"name": "value", "type": "string"}, {"name": "signature", "type": ["option", "string"]}]]}]}]], "command_node": ["container", [{"name": "flags", "type": ["bitfield", [{"name": "unused", "size": 3, "signed": false}, {"name": "has_custom_suggestions", "size": 1, "signed": false}, {"name": "has_redirect_node", "size": 1, "signed": false}, {"name": "has_command", "size": 1, "signed": false}, {"name": "command_node_type", "size": 2, "signed": false}]]}, {"name": "children", "type": ["array", {"countType": "varint", "type": "varint"}]}, {"name": "redirectNode", "type": ["switch", {"compareTo": "flags/has_redirect_node", "fields": {"1": "varint"}, "default": "void"}]}, {"name": "extraNodeData", "type": ["switch", {"compareTo": "flags/command_node_type", "fields": {"0": "void", "1": ["container", [{"name": "name", "type": "string"}]], "2": ["container", [{"name": "name", "type": "string"}, {"name": "parser", "type": ["mapper", {"type": "varint", "mappings": {"0": "brigadier:bool", "1": "brigadier:float", "2": "brigadier:double", "3": "brigadier:integer", "4": "brigadier:long", "5": "brigadier:string", "6": "minecraft:entity", "7": "minecraft:game_profile", "8": "minecraft:block_pos", "9": "minecraft:column_pos", "10": "minecraft:vec3", "11": "minecraft:vec2", "12": "minecraft:block_state", "13": "minecraft:block_predicate", "14": "minecraft:item_stack", "15": "minecraft:item_predicate", "16": "minecraft:color", "17": "minecraft:component", "18": "minecraft:message", "19": "minecraft:nbt", "20": "minecraft:nbt_tag", "21": "minecraft:nbt_path", "22": "minecraft:objective", "23": "minecraft:objective_criteria", "24": "minecraft:operation", "25": "minecraft:particle", "26": "minecraft:angle", "27": "minecraft:rotation", "28": "minecraft:scoreboard_slot", "29": "minecraft:score_holder", "30": "minecraft:swizzle", "31": "minecraft:team", "32": "minecraft:item_slot", "33": "minecraft:resource_location", "34": "minecraft:function", "35": "minecraft:entity_anchor", "36": "minecraft:int_range", "37": "minecraft:float_range", "38": "minecraft:dimension", "39": "minecraft:gamemode", "40": "minecraft:time", "41": "minecraft:resource_or_tag", "42": "minecraft:resource_or_tag_key", "43": "minecraft:resource", "44": "minecraft:resource_key", "45": "minecraft:template_mirror", "46": "minecraft:template_rotation", "47": "minecraft:heightmap", "48": "minecraft:uuid"}}]}, {"name": "properties", "type": ["switch", {"compareTo": "parser", "fields": {"brigadier:bool": "void", "brigadier:float": ["container", [{"name": "flags", "type": ["bitfield", [{"name": "unused", "size": 6, "signed": false}, {"name": "max_present", "size": 1, "signed": false}, {"name": "min_present", "size": 1, "signed": false}]]}, {"name": "min", "type": ["switch", {"compareTo": "flags/min_present", "fields": {"1": "f32"}, "default": "void"}]}, {"name": "max", "type": ["switch", {"compareTo": "flags/max_present", "fields": {"1": "f32"}, "default": "void"}]}]], "brigadier:double": ["container", [{"name": "flags", "type": ["bitfield", [{"name": "unused", "size": 6, "signed": false}, {"name": "max_present", "size": 1, "signed": false}, {"name": "min_present", "size": 1, "signed": false}]]}, {"name": "min", "type": ["switch", {"compareTo": "flags/min_present", "fields": {"1": "f64"}, "default": "void"}]}, {"name": "max", "type": ["switch", {"compareTo": "flags/max_present", "fields": {"1": "f64"}, "default": "void"}]}]], "brigadier:integer": ["container", [{"name": "flags", "type": ["bitfield", [{"name": "unused", "size": 6, "signed": false}, {"name": "max_present", "size": 1, "signed": false}, {"name": "min_present", "size": 1, "signed": false}]]}, {"name": "min", "type": ["switch", {"compareTo": "flags/min_present", "fields": {"1": "i32"}, "default": "void"}]}, {"name": "max", "type": ["switch", {"compareTo": "flags/max_present", "fields": {"1": "i32"}, "default": "void"}]}]], "brigadier:long": ["container", [{"name": "flags", "type": ["bitfield", [{"name": "unused", "size": 6, "signed": false}, {"name": "max_present", "size": 1, "signed": false}, {"name": "min_present", "size": 1, "signed": false}]]}, {"name": "min", "type": ["switch", {"compareTo": "flags/min_present", "fields": {"1": "i64"}, "default": "void"}]}, {"name": "max", "type": ["switch", {"compareTo": "flags/max_present", "fields": {"1": "i64"}, "default": "void"}]}]], "brigadier:string": ["mapper", {"type": "varint", "mappings": {"0": "SINGLE_WORD", "1": "QUOTABLE_PHRASE", "2": "GREEDY_PHRASE"}}], "minecraft:entity": ["bitfield", [{"name": "unused", "size": 6, "signed": false}, {"name": "onlyAllowPlayers", "size": 1, "signed": false}, {"name": "onlyAllowEntities", "size": 1, "signed": false}]], "minecraft:game_profile": "void", "minecraft:block_pos": "void", "minecraft:column_pos": "void", "minecraft:vec3": "void", "minecraft:vec2": "void", "minecraft:block_state": "void", "minecraft:block_predicate": "void", "minecraft:item_stack": "void", "minecraft:item_predicate": "void", "minecraft:color": "void", "minecraft:component": "void", "minecraft:message": "void", "minecraft:nbt": "void", "minecraft:nbt_path": "void", "minecraft:objective": "void", "minecraft:objective_criteria": "void", "minecraft:operation": "void", "minecraft:particle": "void", "minecraft:angle": "void", "minecraft:rotation": "void", "minecraft:scoreboard_slot": "void", "minecraft:score_holder": ["bitfield", [{"name": "unused", "size": 7, "signed": false}, {"name": "allowMultiple", "size": 1, "signed": false}]], "minecraft:swizzle": "void", "minecraft:team": "void", "minecraft:item_slot": "void", "minecraft:resource_location": "void", "minecraft:function": "void", "minecraft:entity_anchor": "void", "minecraft:int_range": "void", "minecraft:float_range": "void", "minecraft:dimension": "void", "minecraft:gamemode": "void", "minecraft:time": ["container", [{"name": "min", "type": "i32"}]], "minecraft:resource_or_tag": ["container", [{"name": "registry", "type": "string"}]], "minecraft:resource_or_tag_key": ["container", [{"name": "registry", "type": "string"}]], "minecraft:resource": ["container", [{"name": "registry", "type": "string"}]], "minecraft:resource_key": ["container", [{"name": "registry", "type": "string"}]], "minecraft:template_mirror": "void", "minecraft:template_rotation": "void", "minecraft:heightmap": "void", "minecraft:uuid": "void"}}]}, {"name": "suggestionType", "type": ["switch", {"compareTo": "../flags/has_custom_suggestions", "fields": {"1": "string"}, "default": "void"}]}]]}}]}]]}, "handshaking": {"toClient": {"types": {"packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {}}]}]]}}, "toServer": {"types": {"packet_set_protocol": ["container", [{"name": "protocolVersion", "type": "varint"}, {"name": "serverHost", "type": "string"}, {"name": "serverPort", "type": "u16"}, {"name": "nextState", "type": "varint"}]], "packet_legacy_server_list_ping": ["container", [{"name": "payload", "type": "u8"}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "set_protocol", "0xfe": "legacy_server_list_ping"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"set_protocol": "packet_set_protocol", "legacy_server_list_ping": "packet_legacy_server_list_ping"}}]}]]}}}, "status": {"toClient": {"types": {"packet_server_info": ["container", [{"name": "response", "type": "string"}]], "packet_ping": ["container", [{"name": "time", "type": "i64"}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "server_info", "0x01": "ping"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"server_info": "packet_server_info", "ping": "packet_ping"}}]}]]}}, "toServer": {"types": {"packet_ping_start": ["container", []], "packet_ping": ["container", [{"name": "time", "type": "i64"}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "ping_start", "0x01": "ping"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"ping_start": "packet_ping_start", "ping": "packet_ping"}}]}]]}}}, "login": {"toClient": {"types": {"packet_disconnect": ["container", [{"name": "reason", "type": "string"}]], "packet_encryption_begin": ["container", [{"name": "serverId", "type": "string"}, {"name": "public<PERSON>ey", "type": ["buffer", {"countType": "varint"}]}, {"name": "verifyToken", "type": ["buffer", {"countType": "varint"}]}]], "packet_success": ["container", [{"name": "uuid", "type": "UUID"}, {"name": "username", "type": "string"}, {"name": "properties", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "name", "type": "string"}, {"name": "value", "type": "string"}, {"name": "signature", "type": ["option", "string"]}]]}]}]], "packet_compress": ["container", [{"name": "threshold", "type": "varint"}]], "packet_login_plugin_request": ["container", [{"name": "messageId", "type": "varint"}, {"name": "channel", "type": "string"}, {"name": "data", "type": "restBuffer"}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "disconnect", "0x01": "encryption_begin", "0x02": "success", "0x03": "compress", "0x04": "login_plugin_request"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"disconnect": "packet_disconnect", "encryption_begin": "packet_encryption_begin", "success": "packet_success", "compress": "packet_compress", "login_plugin_request": "packet_login_plugin_request"}}]}]]}}, "toServer": {"types": {"packet_login_start": ["container", [{"name": "username", "type": "string"}, {"name": "playerUUID", "type": ["option", "UUID"]}]], "packet_encryption_begin": ["container", [{"name": "sharedSecret", "type": ["buffer", {"countType": "varint"}]}, {"name": "verifyToken", "type": ["buffer", {"countType": "varint"}]}]], "packet_login_plugin_response": ["container", [{"name": "messageId", "type": "varint"}, {"name": "data", "type": ["option", "restBuffer"]}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "login_start", "0x01": "encryption_begin", "0x02": "login_plugin_response"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"login_start": "packet_login_start", "encryption_begin": "packet_encryption_begin", "login_plugin_response": "packet_login_plugin_response"}}]}]]}}}, "play": {"toClient": {"types": {"packet_spawn_entity": ["container", [{"name": "entityId", "type": "varint"}, {"name": "objectUUID", "type": "UUID"}, {"name": "type", "type": "varint"}, {"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "pitch", "type": "i8"}, {"name": "yaw", "type": "i8"}, {"name": "head<PERSON><PERSON>", "type": "i8"}, {"name": "objectData", "type": "varint"}, {"name": "velocityX", "type": "i16"}, {"name": "velocityY", "type": "i16"}, {"name": "velocityZ", "type": "i16"}]], "packet_spawn_entity_experience_orb": ["container", [{"name": "entityId", "type": "varint"}, {"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "count", "type": "i16"}]], "packet_named_entity_spawn": ["container", [{"name": "entityId", "type": "varint"}, {"name": "playerUUID", "type": "UUID"}, {"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "yaw", "type": "i8"}, {"name": "pitch", "type": "i8"}]], "packet_animation": ["container", [{"name": "entityId", "type": "varint"}, {"name": "animation", "type": "u8"}]], "packet_statistics": ["container", [{"name": "entries", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "categoryId", "type": "varint"}, {"name": "statisticId", "type": "varint"}, {"name": "value", "type": "varint"}]]}]}]], "packet_advancements": ["container", [{"name": "reset", "type": "bool"}, {"name": "advancementMapping", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "key", "type": "string"}, {"name": "value", "type": ["container", [{"name": "parentId", "type": ["option", "string"]}, {"name": "displayData", "type": ["option", ["container", [{"name": "title", "type": "string"}, {"name": "description", "type": "string"}, {"name": "icon", "type": "slot"}, {"name": "frameType", "type": "varint"}, {"name": "flags", "type": ["bitfield", [{"name": "_unused", "size": 29, "signed": false}, {"name": "hidden", "size": 1, "signed": false}, {"name": "show_toast", "size": 1, "signed": false}, {"name": "has_background_texture", "size": 1, "signed": false}]]}, {"name": "backgroundTexture", "type": ["switch", {"compareTo": "flags/has_background_texture", "fields": {"1": "string"}, "default": "void"}]}, {"name": "xCord", "type": "f32"}, {"name": "yCord", "type": "f32"}]]]}, {"name": "criteria", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "key", "type": "string"}, {"name": "value", "type": "void"}]]}]}, {"name": "requirements", "type": ["array", {"countType": "varint", "type": ["array", {"countType": "varint", "type": "string"}]}]}]]}]]}]}, {"name": "identifiers", "type": ["array", {"countType": "varint", "type": "string"}]}, {"name": "progressMapping", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "key", "type": "string"}, {"name": "value", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "criterionIdentifier", "type": "string"}, {"name": "criterionProgress", "type": ["option", "i64"]}]]}]}]]}]}]], "packet_block_break_animation": ["container", [{"name": "entityId", "type": "varint"}, {"name": "location", "type": "position"}, {"name": "destroyStage", "type": "i8"}]], "packet_tile_entity_data": ["container", [{"name": "location", "type": "position"}, {"name": "action", "type": "varint"}, {"name": "nbtData", "type": "optionalNbt"}]], "packet_block_action": ["container", [{"name": "location", "type": "position"}, {"name": "byte1", "type": "u8"}, {"name": "byte2", "type": "u8"}, {"name": "blockId", "type": "varint"}]], "packet_block_change": ["container", [{"name": "location", "type": "position"}, {"name": "type", "type": "varint"}]], "packet_boss_bar": ["container", [{"name": "entityUUID", "type": "UUID"}, {"name": "action", "type": "varint"}, {"name": "title", "type": ["switch", {"compareTo": "action", "fields": {"0": "string", "3": "string"}, "default": "void"}]}, {"name": "health", "type": ["switch", {"compareTo": "action", "fields": {"0": "f32", "2": "f32"}, "default": "void"}]}, {"name": "color", "type": ["switch", {"compareTo": "action", "fields": {"0": "varint", "4": "varint"}, "default": "void"}]}, {"name": "dividers", "type": ["switch", {"compareTo": "action", "fields": {"0": "varint", "4": "varint"}, "default": "void"}]}, {"name": "flags", "type": ["switch", {"compareTo": "action", "fields": {"0": "u8", "5": "u8"}, "default": "void"}]}]], "packet_difficulty": ["container", [{"name": "difficulty", "type": "u8"}, {"name": "difficultyLocked", "type": "bool"}]], "packet_tab_complete": ["container", [{"name": "transactionId", "type": "varint"}, {"name": "start", "type": "varint"}, {"name": "length", "type": "varint"}, {"name": "matches", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "match", "type": "string"}, {"name": "tooltip", "type": ["option", "string"]}]]}]}]], "packet_declare_commands": ["container", [{"name": "nodes", "type": ["array", {"countType": "varint", "type": "command_node"}]}, {"name": "rootIndex", "type": "varint"}]], "packet_face_player": ["container", [{"name": "feet_eyes", "type": "varint"}, {"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "isEntity", "type": "bool"}, {"name": "entityId", "type": ["switch", {"compareTo": "isEntity", "fields": {"true": "varint"}, "default": "void"}]}, {"name": "entity_feet_eyes", "type": ["switch", {"compareTo": "isEntity", "fields": {"true": "string"}, "default": "void"}]}]], "packet_nbt_query_response": ["container", [{"name": "transactionId", "type": "varint"}, {"name": "nbt", "type": "optionalNbt"}]], "packet_multi_block_change": ["container", [{"name": "chunkCoordinates", "type": ["bitfield", [{"name": "x", "size": 22, "signed": true}, {"name": "z", "size": 22, "signed": true}, {"name": "y", "size": 20, "signed": true}]]}, {"name": "suppressLightUpdates", "type": "bool"}, {"name": "records", "type": ["array", {"countType": "varint", "type": "varint"}]}]], "packet_close_window": ["container", [{"name": "windowId", "type": "u8"}]], "packet_open_window": ["container", [{"name": "windowId", "type": "varint"}, {"name": "inventoryType", "type": "varint"}, {"name": "windowTitle", "type": "string"}]], "packet_window_items": ["container", [{"name": "windowId", "type": "u8"}, {"name": "stateId", "type": "varint"}, {"name": "items", "type": ["array", {"countType": "varint", "type": "slot"}]}, {"name": "carriedItem", "type": "slot"}]], "packet_craft_progress_bar": ["container", [{"name": "windowId", "type": "u8"}, {"name": "property", "type": "i16"}, {"name": "value", "type": "i16"}]], "packet_set_slot": ["container", [{"name": "windowId", "type": "i8"}, {"name": "stateId", "type": "varint"}, {"name": "slot", "type": "i16"}, {"name": "item", "type": "slot"}]], "packet_set_cooldown": ["container", [{"name": "itemID", "type": "varint"}, {"name": "cooldownTicks", "type": "varint"}]], "packet_chat_suggestions": ["container", [{"name": "action", "type": "varint"}, {"name": "entries", "type": ["array", {"countType": "varint", "type": "string"}]}]], "packet_custom_payload": ["container", [{"name": "channel", "type": "string"}, {"name": "data", "type": "restBuffer"}]], "packet_hide_message": ["container", [{"name": "id", "type": "varint"}, {"name": "signature", "type": ["switch", {"compareTo": "id", "fields": {"0": ["buffer", {"count": 256}]}, "default": "void"}]}]], "packet_kick_disconnect": ["container", [{"name": "reason", "type": "string"}]], "packet_profileless_chat": ["container", [{"name": "message", "type": "string"}, {"name": "type", "type": "varint"}, {"name": "name", "type": "string"}, {"name": "target", "type": ["option", "string"]}]], "packet_entity_status": ["container", [{"name": "entityId", "type": "i32"}, {"name": "entityStatus", "type": "i8"}]], "packet_explosion": ["container", [{"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "radius", "type": "f32"}, {"name": "affectedBlockOffsets", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "x", "type": "i8"}, {"name": "y", "type": "i8"}, {"name": "z", "type": "i8"}]]}]}, {"name": "playerMotionX", "type": "f32"}, {"name": "playerMotionY", "type": "f32"}, {"name": "playerMotionZ", "type": "f32"}]], "packet_unload_chunk": ["container", [{"name": "chunkX", "type": "i32"}, {"name": "chunkZ", "type": "i32"}]], "packet_game_state_change": ["container", [{"name": "reason", "type": "u8"}, {"name": "gameMode", "type": "f32"}]], "packet_open_horse_window": ["container", [{"name": "windowId", "type": "u8"}, {"name": "nbSlots", "type": "varint"}, {"name": "entityId", "type": "i32"}]], "packet_keep_alive": ["container", [{"name": "keepAliveId", "type": "i64"}]], "packet_map_chunk": ["container", [{"name": "x", "type": "i32"}, {"name": "z", "type": "i32"}, {"name": "heightmaps", "type": "nbt"}, {"name": "chunkData", "type": ["buffer", {"countType": "varint"}]}, {"name": "blockEntities", "type": ["array", {"countType": "varint", "type": "chunkBlockEntity"}]}, {"name": "trustEdges", "type": "bool"}, {"name": "skyLightMask", "type": ["array", {"countType": "varint", "type": "i64"}]}, {"name": "blockLightMask", "type": ["array", {"countType": "varint", "type": "i64"}]}, {"name": "emptySkyLightMask", "type": ["array", {"countType": "varint", "type": "i64"}]}, {"name": "emptyBlockLightMask", "type": ["array", {"countType": "varint", "type": "i64"}]}, {"name": "skyLight", "type": ["array", {"countType": "varint", "type": ["array", {"countType": "varint", "type": "u8"}]}]}, {"name": "blockLight", "type": ["array", {"countType": "varint", "type": ["array", {"countType": "varint", "type": "u8"}]}]}]], "packet_world_event": ["container", [{"name": "effectId", "type": "i32"}, {"name": "location", "type": "position"}, {"name": "data", "type": "i32"}, {"name": "global", "type": "bool"}]], "packet_world_particles": ["container", [{"name": "particleId", "type": "varint"}, {"name": "longDistance", "type": "bool"}, {"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "offsetX", "type": "f32"}, {"name": "offsetY", "type": "f32"}, {"name": "offsetZ", "type": "f32"}, {"name": "particleData", "type": "f32"}, {"name": "particles", "type": "i32"}, {"name": "data", "type": ["particleData", {"compareTo": "particleId"}]}]], "packet_update_light": ["container", [{"name": "chunkX", "type": "varint"}, {"name": "chunkZ", "type": "varint"}, {"name": "trustEdges", "type": "bool"}, {"name": "skyLightMask", "type": ["array", {"countType": "varint", "type": "i64"}]}, {"name": "blockLightMask", "type": ["array", {"countType": "varint", "type": "i64"}]}, {"name": "emptySkyLightMask", "type": ["array", {"countType": "varint", "type": "i64"}]}, {"name": "emptyBlockLightMask", "type": ["array", {"countType": "varint", "type": "i64"}]}, {"name": "skyLight", "type": ["array", {"countType": "varint", "type": ["array", {"countType": "varint", "type": "u8"}]}]}, {"name": "blockLight", "type": ["array", {"countType": "varint", "type": ["array", {"countType": "varint", "type": "u8"}]}]}]], "packet_login": ["container", [{"name": "entityId", "type": "i32"}, {"name": "isHardcore", "type": "bool"}, {"name": "gameMode", "type": "u8"}, {"name": "previousGameMode", "type": "i8"}, {"name": "worldNames", "type": ["array", {"countType": "varint", "type": "string"}]}, {"name": "dimensionCodec", "type": "nbt"}, {"name": "worldType", "type": "string"}, {"name": "worldName", "type": "string"}, {"name": "hashedSeed", "type": "i64"}, {"name": "maxPlayers", "type": "varint"}, {"name": "viewDistance", "type": "varint"}, {"name": "simulationDistance", "type": "varint"}, {"name": "reducedDebugInfo", "type": "bool"}, {"name": "enableRespawnScreen", "type": "bool"}, {"name": "isDebug", "type": "bool"}, {"name": "is<PERSON><PERSON>", "type": "bool"}, {"name": "death", "type": ["option", ["container", [{"name": "dimensionName", "type": "string"}, {"name": "location", "type": "position"}]]]}]], "packet_map": ["container", [{"name": "itemDamage", "type": "varint"}, {"name": "scale", "type": "i8"}, {"name": "locked", "type": "bool"}, {"name": "icons", "type": ["option", ["array", {"countType": "varint", "type": ["container", [{"name": "type", "type": "varint"}, {"name": "x", "type": "i8"}, {"name": "z", "type": "i8"}, {"name": "direction", "type": "u8"}, {"name": "displayName", "type": ["option", "string"]}]]}]]}, {"name": "columns", "type": "u8"}, {"name": "rows", "type": ["switch", {"compareTo": "columns", "fields": {"0": "void"}, "default": "u8"}]}, {"name": "x", "type": ["switch", {"compareTo": "columns", "fields": {"0": "void"}, "default": "u8"}]}, {"name": "y", "type": ["switch", {"compareTo": "columns", "fields": {"0": "void"}, "default": "u8"}]}, {"name": "data", "type": ["switch", {"compareTo": "columns", "fields": {"0": "void"}, "default": ["buffer", {"countType": "varint"}]}]}]], "packet_trade_list": ["container", [{"name": "windowId", "type": "varint"}, {"name": "trades", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "inputItem1", "type": "slot"}, {"name": "outputItem", "type": "slot"}, {"name": "inputItem2", "type": "slot"}, {"name": "tradeDisabled", "type": "bool"}, {"name": "nbTradeUses", "type": "i32"}, {"name": "maximumNbTradeUses", "type": "i32"}, {"name": "xp", "type": "i32"}, {"name": "specialPrice", "type": "i32"}, {"name": "priceMultiplier", "type": "f32"}, {"name": "demand", "type": "i32"}]]}]}, {"name": "villagerLevel", "type": "varint"}, {"name": "experience", "type": "varint"}, {"name": "isRegularVillager", "type": "bool"}, {"name": "canRestock", "type": "bool"}]], "packet_rel_entity_move": ["container", [{"name": "entityId", "type": "varint"}, {"name": "dX", "type": "i16"}, {"name": "dY", "type": "i16"}, {"name": "dZ", "type": "i16"}, {"name": "onGround", "type": "bool"}]], "packet_entity_move_look": ["container", [{"name": "entityId", "type": "varint"}, {"name": "dX", "type": "i16"}, {"name": "dY", "type": "i16"}, {"name": "dZ", "type": "i16"}, {"name": "yaw", "type": "i8"}, {"name": "pitch", "type": "i8"}, {"name": "onGround", "type": "bool"}]], "packet_entity_look": ["container", [{"name": "entityId", "type": "varint"}, {"name": "yaw", "type": "i8"}, {"name": "pitch", "type": "i8"}, {"name": "onGround", "type": "bool"}]], "packet_vehicle_move": ["container", [{"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "yaw", "type": "f32"}, {"name": "pitch", "type": "f32"}]], "packet_open_book": ["container", [{"name": "hand", "type": "varint"}]], "packet_open_sign_entity": ["container", [{"name": "location", "type": "position"}]], "packet_craft_recipe_response": ["container", [{"name": "windowId", "type": "i8"}, {"name": "recipe", "type": "string"}]], "packet_abilities": ["container", [{"name": "flags", "type": "i8"}, {"name": "flyingSpeed", "type": "f32"}, {"name": "walkingSpeed", "type": "f32"}]], "packet_player_chat": ["container", [{"name": "senderUuid", "type": "UUID"}, {"name": "index", "type": "varint"}, {"name": "signature", "type": ["option", ["buffer", {"count": 256}]]}, {"name": "plainMessage", "type": "string"}, {"name": "timestamp", "type": "i64"}, {"name": "salt", "type": "i64"}, {"name": "previousMessages", "type": "previousMessages"}, {"name": "unsignedChatContent", "type": ["option", "string"]}, {"name": "filterType", "type": "varint"}, {"name": "filterTypeMask", "type": ["switch", {"compareTo": "filterType", "fields": {"2": ["array", {"countType": "varint", "type": "i64"}]}, "default": "void"}]}, {"name": "type", "type": "varint"}, {"name": "networkName", "type": "string"}, {"name": "networkTargetName", "type": ["option", "string"]}]], "packet_end_combat_event": ["container", [{"name": "duration", "type": "varint"}, {"name": "entityId", "type": "i32"}]], "packet_enter_combat_event": ["container", []], "packet_death_combat_event": ["container", [{"name": "playerId", "type": "varint"}, {"name": "entityId", "type": "i32"}, {"name": "message", "type": "string"}]], "packet_player_remove": ["container", [{"name": "players", "type": ["array", {"countType": "varint", "type": "UUID"}]}]], "packet_player_info": ["container", [{"name": "action", "type": "i8"}, {"name": "data", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "uuid", "type": "UUID"}, {"name": "player", "type": ["switch", {"compareTo": "../action", "fields": {"1": "game_profile", "3": "game_profile", "5": "game_profile", "7": "game_profile", "9": "game_profile", "11": "game_profile", "13": "game_profile", "15": "game_profile", "17": "game_profile", "19": "game_profile", "21": "game_profile", "23": "game_profile", "25": "game_profile", "27": "game_profile", "29": "game_profile", "31": "game_profile", "33": "game_profile", "35": "game_profile", "37": "game_profile", "39": "game_profile", "41": "game_profile", "43": "game_profile", "45": "game_profile", "47": "game_profile", "49": "game_profile", "51": "game_profile", "53": "game_profile", "55": "game_profile", "57": "game_profile", "59": "game_profile", "61": "game_profile", "63": "game_profile"}, "default": "void"}]}, {"name": "chatSession", "type": ["switch", {"compareTo": "../action", "fields": {"2": "chat_session", "3": "chat_session", "6": "chat_session", "7": "chat_session", "10": "chat_session", "11": "chat_session", "14": "chat_session", "15": "chat_session", "18": "chat_session", "19": "chat_session", "22": "chat_session", "23": "chat_session", "26": "chat_session", "27": "chat_session", "30": "chat_session", "31": "chat_session", "34": "chat_session", "35": "chat_session", "38": "chat_session", "39": "chat_session", "42": "chat_session", "43": "chat_session", "46": "chat_session", "47": "chat_session", "50": "chat_session", "51": "chat_session", "54": "chat_session", "55": "chat_session", "58": "chat_session", "59": "chat_session", "62": "chat_session", "63": "chat_session"}, "default": "void"}]}, {"name": "gamemode", "type": ["switch", {"compareTo": "../action", "fields": {"4": "varint", "5": "varint", "6": "varint", "7": "varint", "12": "varint", "13": "varint", "14": "varint", "15": "varint", "20": "varint", "21": "varint", "22": "varint", "23": "varint", "28": "varint", "29": "varint", "30": "varint", "31": "varint", "36": "varint", "37": "varint", "38": "varint", "39": "varint", "44": "varint", "45": "varint", "46": "varint", "47": "varint", "52": "varint", "53": "varint", "54": "varint", "55": "varint", "60": "varint", "61": "varint", "62": "varint", "63": "varint"}, "default": "void"}]}, {"name": "listed", "type": ["switch", {"compareTo": "../action", "fields": {"8": "bool", "9": "bool", "10": "bool", "11": "bool", "12": "bool", "13": "bool", "14": "bool", "15": "bool", "24": "bool", "25": "bool", "26": "bool", "27": "bool", "28": "bool", "29": "bool", "30": "bool", "31": "bool", "40": "bool", "41": "bool", "42": "bool", "43": "bool", "44": "bool", "45": "bool", "46": "bool", "47": "bool", "56": "bool", "57": "bool", "58": "bool", "59": "bool", "60": "bool", "61": "bool", "62": "bool", "63": "bool"}, "default": "void"}]}, {"name": "latency", "type": ["switch", {"compareTo": "../action", "fields": {"16": "varint", "17": "varint", "18": "varint", "19": "varint", "20": "varint", "21": "varint", "22": "varint", "23": "varint", "24": "varint", "25": "varint", "26": "varint", "27": "varint", "28": "varint", "29": "varint", "30": "varint", "31": "varint", "48": "varint", "49": "varint", "50": "varint", "51": "varint", "52": "varint", "53": "varint", "54": "varint", "55": "varint", "56": "varint", "57": "varint", "58": "varint", "59": "varint", "60": "varint", "61": "varint", "62": "varint", "63": "varint"}, "default": "void"}]}, {"name": "displayName", "type": ["switch", {"compareTo": "../action", "fields": {"32": ["option", "string"], "33": ["option", "string"], "34": ["option", "string"], "35": ["option", "string"], "36": ["option", "string"], "37": ["option", "string"], "38": ["option", "string"], "39": ["option", "string"], "40": ["option", "string"], "41": ["option", "string"], "42": ["option", "string"], "43": ["option", "string"], "44": ["option", "string"], "45": ["option", "string"], "46": ["option", "string"], "47": ["option", "string"], "48": ["option", "string"], "49": ["option", "string"], "50": ["option", "string"], "51": ["option", "string"], "52": ["option", "string"], "53": ["option", "string"], "54": ["option", "string"], "55": ["option", "string"], "56": ["option", "string"], "57": ["option", "string"], "58": ["option", "string"], "59": ["option", "string"], "60": ["option", "string"], "61": ["option", "string"], "62": ["option", "string"], "63": ["option", "string"]}, "default": "void"}]}]]}]}]], "packet_position": ["container", [{"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "yaw", "type": "f32"}, {"name": "pitch", "type": "f32"}, {"name": "flags", "type": "i8"}, {"name": "teleportId", "type": "varint"}]], "packet_unlock_recipes": ["container", [{"name": "action", "type": "varint"}, {"name": "craftingBookOpen", "type": "bool"}, {"name": "filteringCraftable", "type": "bool"}, {"name": "smeltingBookOpen", "type": "bool"}, {"name": "filteringSmeltable", "type": "bool"}, {"name": "blastFurnaceOpen", "type": "bool"}, {"name": "filteringBlastFurnace", "type": "bool"}, {"name": "smokerBookOpen", "type": "bool"}, {"name": "filteringSmoker", "type": "bool"}, {"name": "recipes1", "type": ["array", {"countType": "varint", "type": "string"}]}, {"name": "recipes2", "type": ["switch", {"compareTo": "action", "fields": {"0": ["array", {"countType": "varint", "type": "string"}]}, "default": "void"}]}]], "packet_entity_destroy": ["container", [{"name": "entityIds", "type": ["array", {"countType": "varint", "type": "varint"}]}]], "packet_remove_entity_effect": ["container", [{"name": "entityId", "type": "varint"}, {"name": "effectId", "type": "varint"}]], "packet_resource_pack_send": ["container", [{"name": "url", "type": "string"}, {"name": "hash", "type": "string"}, {"name": "forced", "type": "bool"}, {"name": "promptMessage", "type": ["option", "string"]}]], "packet_respawn": ["container", [{"name": "dimension", "type": "string"}, {"name": "worldName", "type": "string"}, {"name": "hashedSeed", "type": "i64"}, {"name": "gamemode", "type": "i8"}, {"name": "previousGamemode", "type": "u8"}, {"name": "isDebug", "type": "bool"}, {"name": "is<PERSON><PERSON>", "type": "bool"}, {"name": "copyMetadata", "type": "bool"}, {"name": "death", "type": ["option", ["container", [{"name": "dimensionName", "type": "string"}, {"name": "location", "type": "position"}]]]}]], "packet_entity_head_rotation": ["container", [{"name": "entityId", "type": "varint"}, {"name": "headYaw", "type": "i8"}]], "packet_camera": ["container", [{"name": "cameraId", "type": "varint"}]], "packet_held_item_slot": ["container", [{"name": "slot", "type": "i8"}]], "packet_update_view_position": ["container", [{"name": "chunkX", "type": "varint"}, {"name": "chunkZ", "type": "varint"}]], "packet_update_view_distance": ["container", [{"name": "viewDistance", "type": "varint"}]], "packet_scoreboard_display_objective": ["container", [{"name": "position", "type": "i8"}, {"name": "name", "type": "string"}]], "packet_entity_metadata": ["container", [{"name": "entityId", "type": "varint"}, {"name": "metadata", "type": "entityMetadata"}]], "packet_attach_entity": ["container", [{"name": "entityId", "type": "i32"}, {"name": "vehicleId", "type": "i32"}]], "packet_entity_velocity": ["container", [{"name": "entityId", "type": "varint"}, {"name": "velocityX", "type": "i16"}, {"name": "velocityY", "type": "i16"}, {"name": "velocityZ", "type": "i16"}]], "packet_entity_equipment": ["container", [{"name": "entityId", "type": "varint"}, {"name": "equipments", "type": ["topBitSetTerminatedArray", {"type": ["container", [{"name": "slot", "type": "i8"}, {"name": "item", "type": "slot"}]]}]}]], "packet_experience": ["container", [{"name": "experienceBar", "type": "f32"}, {"name": "level", "type": "varint"}, {"name": "totalExperience", "type": "varint"}]], "packet_update_health": ["container", [{"name": "health", "type": "f32"}, {"name": "food", "type": "varint"}, {"name": "foodSaturation", "type": "f32"}]], "packet_scoreboard_objective": ["container", [{"name": "name", "type": "string"}, {"name": "action", "type": "i8"}, {"name": "displayText", "type": ["switch", {"compareTo": "action", "fields": {"0": "string", "2": "string"}, "default": "void"}]}, {"name": "type", "type": ["switch", {"compareTo": "action", "fields": {"0": "varint", "2": "varint"}, "default": "void"}]}]], "packet_set_passengers": ["container", [{"name": "entityId", "type": "varint"}, {"name": "passengers", "type": ["array", {"countType": "varint", "type": "varint"}]}]], "packet_teams": ["container", [{"name": "team", "type": "string"}, {"name": "mode", "type": "i8"}, {"name": "name", "type": ["switch", {"compareTo": "mode", "fields": {"0": "string", "2": "string"}, "default": "void"}]}, {"name": "friendlyFire", "type": ["switch", {"compareTo": "mode", "fields": {"0": "i8", "2": "i8"}, "default": "void"}]}, {"name": "nameTagVisibility", "type": ["switch", {"compareTo": "mode", "fields": {"0": "string", "2": "string"}, "default": "void"}]}, {"name": "collisionRule", "type": ["switch", {"compareTo": "mode", "fields": {"0": "string", "2": "string"}, "default": "void"}]}, {"name": "formatting", "type": ["switch", {"compareTo": "mode", "fields": {"0": "varint", "2": "varint"}, "default": "void"}]}, {"name": "prefix", "type": ["switch", {"compareTo": "mode", "fields": {"0": "string", "2": "string"}, "default": "void"}]}, {"name": "suffix", "type": ["switch", {"compareTo": "mode", "fields": {"0": "string", "2": "string"}, "default": "void"}]}, {"name": "players", "type": ["switch", {"compareTo": "mode", "fields": {"0": ["array", {"countType": "varint", "type": "string"}], "3": ["array", {"countType": "varint", "type": "string"}], "4": ["array", {"countType": "varint", "type": "string"}]}, "default": "void"}]}]], "packet_scoreboard_score": ["container", [{"name": "itemName", "type": "string"}, {"name": "action", "type": "varint"}, {"name": "scoreName", "type": "string"}, {"name": "value", "type": ["switch", {"compareTo": "action", "fields": {"1": "void"}, "default": "varint"}]}]], "packet_spawn_position": ["container", [{"name": "location", "type": "position"}, {"name": "angle", "type": "f32"}]], "packet_update_time": ["container", [{"name": "age", "type": "i64"}, {"name": "time", "type": "i64"}]], "packet_entity_sound_effect": ["container", [{"name": "soundId", "type": "varint"}, {"name": "soundEvent", "type": ["switch", {"compareTo": "soundId", "fields": {"0": ["container", [{"name": "resource", "type": "string"}, {"name": "range", "type": ["option", "f32"]}]]}, "default": "void"}]}, {"name": "soundCategory", "type": "soundSource"}, {"name": "entityId", "type": "varint"}, {"name": "volume", "type": "f32"}, {"name": "pitch", "type": "f32"}, {"name": "seed", "type": "i64"}]], "packet_stop_sound": ["container", [{"name": "flags", "type": "i8"}, {"name": "source", "type": ["switch", {"compareTo": "flags", "fields": {"1": "varint", "3": "varint"}, "default": "void"}]}, {"name": "sound", "type": ["switch", {"compareTo": "flags", "fields": {"2": "string", "3": "string"}, "default": "void"}]}]], "packet_sound_effect": ["container", [{"name": "soundId", "type": "varint"}, {"name": "soundEvent", "type": ["switch", {"compareTo": "soundId", "fields": {"0": ["container", [{"name": "resource", "type": "string"}, {"name": "range", "type": ["option", "f32"]}]]}, "default": "void"}]}, {"name": "soundCategory", "type": "soundSource"}, {"name": "x", "type": "i32"}, {"name": "y", "type": "i32"}, {"name": "z", "type": "i32"}, {"name": "volume", "type": "f32"}, {"name": "pitch", "type": "f32"}, {"name": "seed", "type": "i64"}]], "packet_system_chat": ["container", [{"name": "content", "type": "string"}, {"name": "isActionBar", "type": "bool"}]], "packet_playerlist_header": ["container", [{"name": "header", "type": "string"}, {"name": "footer", "type": "string"}]], "packet_collect": ["container", [{"name": "collectedEntityId", "type": "varint"}, {"name": "collectorEntityId", "type": "varint"}, {"name": "pickupItemCount", "type": "varint"}]], "packet_entity_teleport": ["container", [{"name": "entityId", "type": "varint"}, {"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "yaw", "type": "i8"}, {"name": "pitch", "type": "i8"}, {"name": "onGround", "type": "bool"}]], "packet_entity_update_attributes": ["container", [{"name": "entityId", "type": "varint"}, {"name": "properties", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "key", "type": "string"}, {"name": "value", "type": "f64"}, {"name": "modifiers", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "uuid", "type": "UUID"}, {"name": "amount", "type": "f64"}, {"name": "operation", "type": "i8"}]]}]}]]}]}]], "packet_feature_flags": ["container", [{"name": "features", "type": ["array", {"countType": "varint", "type": "string"}]}]], "packet_entity_effect": ["container", [{"name": "entityId", "type": "varint"}, {"name": "effectId", "type": "varint"}, {"name": "amplifier", "type": "i8"}, {"name": "duration", "type": "varint"}, {"name": "hideParticles", "type": "i8"}, {"name": "factorCodec", "type": ["option", "nbt"]}]], "packet_select_advancement_tab": ["container", [{"name": "id", "type": ["option", "string"]}]], "packet_server_data": ["container", [{"name": "motd", "type": "string"}, {"name": "iconBytes", "type": ["option", ["buffer", {"countType": "varint"}]]}, {"name": "enforcesSecureChat", "type": "bool"}]], "packet_declare_recipes": ["container", [{"name": "recipes", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "type", "type": "string"}, {"name": "recipeId", "type": "string"}, {"name": "data", "type": ["switch", {"compareTo": "type", "fields": {"minecraft:crafting_shapeless": ["container", [{"name": "group", "type": "string"}, {"name": "category", "type": "varint"}, {"name": "ingredients", "type": ["array", {"countType": "varint", "type": "ingredient"}]}, {"name": "result", "type": "slot"}]], "minecraft:crafting_shaped": ["container", [{"name": "width", "type": "varint"}, {"name": "height", "type": "varint"}, {"name": "group", "type": "string"}, {"name": "category", "type": "varint"}, {"name": "ingredients", "type": ["array", {"count": "width", "type": ["array", {"count": "height", "type": "ingredient"}]}]}, {"name": "result", "type": "slot"}, {"name": "showNotification", "type": "bool"}]], "minecraft:crafting_special_armordye": "minecraft_simple_recipe_format", "minecraft:crafting_special_bookcloning": "minecraft_simple_recipe_format", "minecraft:crafting_special_mapcloning": "minecraft_simple_recipe_format", "minecraft:crafting_special_mapextending": "minecraft_simple_recipe_format", "minecraft:crafting_special_firework_rocket": "minecraft_simple_recipe_format", "minecraft:crafting_special_firework_star": "minecraft_simple_recipe_format", "minecraft:crafting_special_firework_star_fade": "minecraft_simple_recipe_format", "minecraft:crafting_special_repairitem": "minecraft_simple_recipe_format", "minecraft:crafting_special_tippedarrow": "minecraft_simple_recipe_format", "minecraft:crafting_special_bannerduplicate": "minecraft_simple_recipe_format", "minecraft:crafting_special_banneraddpattern": "minecraft_simple_recipe_format", "minecraft:crafting_special_shielddecoration": "minecraft_simple_recipe_format", "minecraft:crafting_special_shulkerboxcoloring": "minecraft_simple_recipe_format", "minecraft:crafting_special_suspiciousstew": "minecraft_simple_recipe_format", "minecraft:smelting": "minecraft_smelting_format", "minecraft:blasting": "minecraft_smelting_format", "minecraft:smoking": "minecraft_smelting_format", "minecraft:campfire_cooking": "minecraft_smelting_format", "minecraft:stonecutting": ["container", [{"name": "group", "type": "string"}, {"name": "ingredient", "type": "ingredient"}, {"name": "result", "type": "slot"}]], "minecraft:smithing": ["container", [{"name": "base", "type": "ingredient"}, {"name": "addition", "type": "ingredient"}, {"name": "result", "type": "slot"}]], "minecraft:smithing_transform": ["container", [{"name": "template", "type": "ingredient"}, {"name": "base", "type": "ingredient"}, {"name": "addition", "type": "ingredient"}, {"name": "result", "type": "slot"}]], "minecraft:smithing_trim": ["container", [{"name": "template", "type": "ingredient"}, {"name": "base", "type": "ingredient"}, {"name": "addition", "type": "ingredient"}]], "minecraft:crafting_decorated_pot": "minecraft_simple_recipe_format"}}]}]]}]}]], "packet_tags": ["container", [{"name": "tags", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "tagType", "type": "string"}, {"name": "tags", "type": "tags"}]]}]}]], "packet_acknowledge_player_digging": ["container", [{"name": "sequenceId", "type": "varint"}]], "packet_clear_titles": ["container", [{"name": "reset", "type": "bool"}]], "packet_initialize_world_border": ["container", [{"name": "x", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "oldDiam<PERSON>", "type": "f64"}, {"name": "newDiameter", "type": "f64"}, {"name": "speed", "type": "varint"}, {"name": "portalTeleportBoundary", "type": "varint"}, {"name": "warningBlocks", "type": "varint"}, {"name": "warningTime", "type": "varint"}]], "packet_action_bar": ["container", [{"name": "text", "type": "string"}]], "packet_world_border_center": ["container", [{"name": "x", "type": "f64"}, {"name": "z", "type": "f64"}]], "packet_world_border_lerp_size": ["container", [{"name": "oldDiam<PERSON>", "type": "f64"}, {"name": "newDiameter", "type": "f64"}, {"name": "speed", "type": "varint"}]], "packet_world_border_size": ["container", [{"name": "diameter", "type": "f64"}]], "packet_world_border_warning_delay": ["container", [{"name": "warningTime", "type": "varint"}]], "packet_world_border_warning_reach": ["container", [{"name": "warningBlocks", "type": "varint"}]], "packet_ping": ["container", [{"name": "id", "type": "i32"}]], "packet_set_title_subtitle": ["container", [{"name": "text", "type": "string"}]], "packet_set_title_text": ["container", [{"name": "text", "type": "string"}]], "packet_set_title_time": ["container", [{"name": "fadeIn", "type": "i32"}, {"name": "stay", "type": "i32"}, {"name": "fadeOut", "type": "i32"}]], "packet_simulation_distance": ["container", [{"name": "distance", "type": "varint"}]], "packet_chunk_biomes": ["container", [{"name": "biomes", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "position", "type": "packedChunkPos"}, {"name": "data", "type": ["buffer", {"countType": "varint"}]}]]}]}]], "packet_damage_event": ["container", [{"name": "entityId", "type": "varint"}, {"name": "sourceTypeId", "type": "varint"}, {"name": "sourceCauseId", "type": "varint"}, {"name": "sourceDirectId", "type": "varint"}, {"name": "sourcePosition", "type": ["option", "vec3f64"]}]], "packet_hurt_animation": ["container", [{"name": "entityId", "type": "varint"}, {"name": "yaw", "type": "f32"}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "bundle_delimiter", "0x01": "spawn_entity", "0x02": "spawn_entity_experience_orb", "0x03": "named_entity_spawn", "0x04": "animation", "0x05": "statistics", "0x06": "acknowledge_player_digging", "0x07": "block_break_animation", "0x08": "tile_entity_data", "0x09": "block_action", "0x0a": "block_change", "0x0b": "boss_bar", "0x0c": "difficulty", "0x0d": "chunk_biomes", "0x0e": "clear_titles", "0x0f": "tab_complete", "0x10": "declare_commands", "0x11": "close_window", "0x12": "window_items", "0x13": "craft_progress_bar", "0x14": "set_slot", "0x15": "set_cooldown", "0x16": "chat_suggestions", "0x17": "custom_payload", "0x18": "damage_event", "0x19": "hide_message", "0x1a": "kick_disconnect", "0x1b": "profileless_chat", "0x1c": "entity_status", "0x1d": "explosion", "0x1e": "unload_chunk", "0x1f": "game_state_change", "0x20": "open_horse_window", "0x21": "hurt_animation", "0x22": "initialize_world_border", "0x23": "keep_alive", "0x24": "map_chunk", "0x25": "world_event", "0x26": "world_particles", "0x27": "update_light", "0x28": "login", "0x29": "map", "0x2a": "trade_list", "0x2b": "rel_entity_move", "0x2c": "entity_move_look", "0x2d": "entity_look", "0x2e": "vehicle_move", "0x2f": "open_book", "0x30": "open_window", "0x31": "open_sign_entity", "0x32": "ping", "0x33": "craft_recipe_response", "0x34": "abilities", "0x35": "player_chat", "0x36": "end_combat_event", "0x37": "enter_combat_event", "0x38": "death_combat_event", "0x39": "player_remove", "0x3a": "player_info", "0x3b": "face_player", "0x3c": "position", "0x3d": "unlock_recipes", "0x3e": "entity_destroy", "0x3f": "remove_entity_effect", "0x40": "resource_pack_send", "0x41": "respawn", "0x42": "entity_head_rotation", "0x43": "multi_block_change", "0x44": "select_advancement_tab", "0x45": "server_data", "0x46": "action_bar", "0x47": "world_border_center", "0x48": "world_border_lerp_size", "0x49": "world_border_size", "0x4a": "world_border_warning_delay", "0x4b": "world_border_warning_reach", "0x4c": "camera", "0x4d": "held_item_slot", "0x4e": "update_view_position", "0x4f": "update_view_distance", "0x50": "spawn_position", "0x51": "scoreboard_display_objective", "0x52": "entity_metadata", "0x53": "attach_entity", "0x54": "entity_velocity", "0x55": "entity_equipment", "0x56": "experience", "0x57": "update_health", "0x58": "scoreboard_objective", "0x59": "set_passengers", "0x5a": "teams", "0x5b": "scoreboard_score", "0x5c": "simulation_distance", "0x5d": "set_title_subtitle", "0x5e": "update_time", "0x5f": "set_title_text", "0x60": "set_title_time", "0x61": "entity_sound_effect", "0x62": "sound_effect", "0x63": "stop_sound", "0x64": "system_chat", "0x65": "playerlist_header", "0x66": "nbt_query_response", "0x67": "collect", "0x68": "entity_teleport", "0x69": "advancements", "0x6a": "entity_update_attributes", "0x6b": "feature_flags", "0x6c": "entity_effect", "0x6d": "declare_recipes", "0x6e": "tags"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"bundle_delimiter": "void", "spawn_entity": "packet_spawn_entity", "spawn_entity_experience_orb": "packet_spawn_entity_experience_orb", "named_entity_spawn": "packet_named_entity_spawn", "animation": "packet_animation", "statistics": "packet_statistics", "acknowledge_player_digging": "packet_acknowledge_player_digging", "block_break_animation": "packet_block_break_animation", "tile_entity_data": "packet_tile_entity_data", "block_action": "packet_block_action", "block_change": "packet_block_change", "boss_bar": "packet_boss_bar", "difficulty": "packet_difficulty", "chunk_biomes": "packet_chunk_biomes", "clear_titles": "packet_clear_titles", "tab_complete": "packet_tab_complete", "declare_commands": "packet_declare_commands", "close_window": "packet_close_window", "window_items": "packet_window_items", "craft_progress_bar": "packet_craft_progress_bar", "set_slot": "packet_set_slot", "set_cooldown": "packet_set_cooldown", "chat_suggestions": "packet_chat_suggestions", "custom_payload": "packet_custom_payload", "damage_event": "packet_damage_event", "hide_message": "packet_hide_message", "kick_disconnect": "packet_kick_disconnect", "profileless_chat": "packet_profileless_chat", "entity_status": "packet_entity_status", "explosion": "packet_explosion", "unload_chunk": "packet_unload_chunk", "game_state_change": "packet_game_state_change", "open_horse_window": "packet_open_horse_window", "hurt_animation": "packet_hurt_animation", "initialize_world_border": "packet_initialize_world_border", "keep_alive": "packet_keep_alive", "map_chunk": "packet_map_chunk", "world_event": "packet_world_event", "world_particles": "packet_world_particles", "update_light": "packet_update_light", "login": "packet_login", "map": "packet_map", "trade_list": "packet_trade_list", "rel_entity_move": "packet_rel_entity_move", "entity_move_look": "packet_entity_move_look", "entity_look": "packet_entity_look", "vehicle_move": "packet_vehicle_move", "open_book": "packet_open_book", "open_window": "packet_open_window", "open_sign_entity": "packet_open_sign_entity", "ping": "packet_ping", "craft_recipe_response": "packet_craft_recipe_response", "abilities": "packet_abilities", "player_chat": "packet_player_chat", "end_combat_event": "packet_end_combat_event", "enter_combat_event": "packet_enter_combat_event", "death_combat_event": "packet_death_combat_event", "player_remove": "packet_player_remove", "player_info": "packet_player_info", "face_player": "packet_face_player", "position": "packet_position", "unlock_recipes": "packet_unlock_recipes", "entity_destroy": "packet_entity_destroy", "remove_entity_effect": "packet_remove_entity_effect", "resource_pack_send": "packet_resource_pack_send", "respawn": "packet_respawn", "entity_head_rotation": "packet_entity_head_rotation", "multi_block_change": "packet_multi_block_change", "select_advancement_tab": "packet_select_advancement_tab", "server_data": "packet_server_data", "action_bar": "packet_action_bar", "world_border_center": "packet_world_border_center", "world_border_lerp_size": "packet_world_border_lerp_size", "world_border_size": "packet_world_border_size", "world_border_warning_delay": "packet_world_border_warning_delay", "world_border_warning_reach": "packet_world_border_warning_reach", "camera": "packet_camera", "held_item_slot": "packet_held_item_slot", "update_view_position": "packet_update_view_position", "update_view_distance": "packet_update_view_distance", "spawn_position": "packet_spawn_position", "scoreboard_display_objective": "packet_scoreboard_display_objective", "entity_metadata": "packet_entity_metadata", "attach_entity": "packet_attach_entity", "entity_velocity": "packet_entity_velocity", "entity_equipment": "packet_entity_equipment", "experience": "packet_experience", "update_health": "packet_update_health", "scoreboard_objective": "packet_scoreboard_objective", "set_passengers": "packet_set_passengers", "teams": "packet_teams", "scoreboard_score": "packet_scoreboard_score", "simulation_distance": "packet_simulation_distance", "set_title_subtitle": "packet_set_title_subtitle", "update_time": "packet_update_time", "set_title_text": "packet_set_title_text", "set_title_time": "packet_set_title_time", "entity_sound_effect": "packet_entity_sound_effect", "sound_effect": "packet_sound_effect", "stop_sound": "packet_stop_sound", "system_chat": "packet_system_chat", "playerlist_header": "packet_playerlist_header", "nbt_query_response": "packet_nbt_query_response", "collect": "packet_collect", "entity_teleport": "packet_entity_teleport", "advancements": "packet_advancements", "entity_update_attributes": "packet_entity_update_attributes", "feature_flags": "packet_feature_flags", "entity_effect": "packet_entity_effect", "declare_recipes": "packet_declare_recipes", "tags": "packet_tags"}}]}]]}}, "toServer": {"types": {"packet_teleport_confirm": ["container", [{"name": "teleportId", "type": "varint"}]], "packet_query_block_nbt": ["container", [{"name": "transactionId", "type": "varint"}, {"name": "location", "type": "position"}]], "packet_chat_command": ["container", [{"name": "command", "type": "string"}, {"name": "timestamp", "type": "i64"}, {"name": "salt", "type": "i64"}, {"name": "argumentSignatures", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "argumentName", "type": "string"}, {"name": "signature", "type": ["buffer", {"count": 256}]}]]}]}, {"name": "messageCount", "type": "varint"}, {"name": "acknowledged", "type": ["buffer", {"count": 3}]}]], "packet_chat_message": ["container", [{"name": "message", "type": "string"}, {"name": "timestamp", "type": "i64"}, {"name": "salt", "type": "i64"}, {"name": "signature", "type": ["option", ["buffer", {"count": 256}]]}, {"name": "offset", "type": "varint"}, {"name": "acknowledged", "type": ["buffer", {"count": 3}]}]], "packet_set_difficulty": ["container", [{"name": "new<PERSON>iff<PERSON><PERSON><PERSON>", "type": "u8"}]], "packet_message_acknowledgement": ["container", [{"name": "count", "type": "varint"}]], "packet_edit_book": ["container", [{"name": "hand", "type": "varint"}, {"name": "pages", "type": ["array", {"countType": "varint", "type": "string"}]}, {"name": "title", "type": ["option", "string"]}]], "packet_query_entity_nbt": ["container", [{"name": "transactionId", "type": "varint"}, {"name": "entityId", "type": "varint"}]], "packet_pick_item": ["container", [{"name": "slot", "type": "varint"}]], "packet_name_item": ["container", [{"name": "name", "type": "string"}]], "packet_select_trade": ["container", [{"name": "slot", "type": "varint"}]], "packet_set_beacon_effect": ["container", [{"name": "primary_effect", "type": ["option", "varint"]}, {"name": "secondary_effect", "type": ["option", "varint"]}]], "packet_update_command_block": ["container", [{"name": "location", "type": "position"}, {"name": "command", "type": "string"}, {"name": "mode", "type": "varint"}, {"name": "flags", "type": "u8"}]], "packet_update_command_block_minecart": ["container", [{"name": "entityId", "type": "varint"}, {"name": "command", "type": "string"}, {"name": "track_output", "type": "bool"}]], "packet_update_structure_block": ["container", [{"name": "location", "type": "position"}, {"name": "action", "type": "varint"}, {"name": "mode", "type": "varint"}, {"name": "name", "type": "string"}, {"name": "offset_x", "type": "i8"}, {"name": "offset_y", "type": "i8"}, {"name": "offset_z", "type": "i8"}, {"name": "size_x", "type": "i8"}, {"name": "size_y", "type": "i8"}, {"name": "size_z", "type": "i8"}, {"name": "mirror", "type": "varint"}, {"name": "rotation", "type": "varint"}, {"name": "metadata", "type": "string"}, {"name": "integrity", "type": "f32"}, {"name": "seed", "type": "varint"}, {"name": "flags", "type": "u8"}]], "packet_tab_complete": ["container", [{"name": "transactionId", "type": "varint"}, {"name": "text", "type": "string"}]], "packet_client_command": ["container", [{"name": "actionId", "type": "varint"}]], "packet_settings": ["container", [{"name": "locale", "type": "string"}, {"name": "viewDistance", "type": "i8"}, {"name": "chatFlags", "type": "varint"}, {"name": "chatColors", "type": "bool"}, {"name": "skinParts", "type": "u8"}, {"name": "mainHand", "type": "varint"}, {"name": "enableTextFiltering", "type": "bool"}, {"name": "enableServerListing", "type": "bool"}]], "packet_enchant_item": ["container", [{"name": "windowId", "type": "i8"}, {"name": "enchantment", "type": "i8"}]], "packet_window_click": ["container", [{"name": "windowId", "type": "u8"}, {"name": "stateId", "type": "varint"}, {"name": "slot", "type": "i16"}, {"name": "mouseButton", "type": "i8"}, {"name": "mode", "type": "varint"}, {"name": "changedSlots", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "location", "type": "i16"}, {"name": "item", "type": "slot"}]]}]}, {"name": "cursorItem", "type": "slot"}]], "packet_close_window": ["container", [{"name": "windowId", "type": "u8"}]], "packet_custom_payload": ["container", [{"name": "channel", "type": "string"}, {"name": "data", "type": "restBuffer"}]], "packet_use_entity": ["container", [{"name": "target", "type": "varint"}, {"name": "mouse", "type": "varint"}, {"name": "x", "type": ["switch", {"compareTo": "mouse", "fields": {"2": "f32"}, "default": "void"}]}, {"name": "y", "type": ["switch", {"compareTo": "mouse", "fields": {"2": "f32"}, "default": "void"}]}, {"name": "z", "type": ["switch", {"compareTo": "mouse", "fields": {"2": "f32"}, "default": "void"}]}, {"name": "hand", "type": ["switch", {"compareTo": "mouse", "fields": {"0": "varint", "2": "varint"}, "default": "void"}]}, {"name": "sneaking", "type": "bool"}]], "packet_generate_structure": ["container", [{"name": "location", "type": "position"}, {"name": "levels", "type": "varint"}, {"name": "keepJigsaws", "type": "bool"}]], "packet_keep_alive": ["container", [{"name": "keepAliveId", "type": "i64"}]], "packet_lock_difficulty": ["container", [{"name": "locked", "type": "bool"}]], "packet_position": ["container", [{"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "onGround", "type": "bool"}]], "packet_position_look": ["container", [{"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "yaw", "type": "f32"}, {"name": "pitch", "type": "f32"}, {"name": "onGround", "type": "bool"}]], "packet_look": ["container", [{"name": "yaw", "type": "f32"}, {"name": "pitch", "type": "f32"}, {"name": "onGround", "type": "bool"}]], "packet_flying": ["container", [{"name": "onGround", "type": "bool"}]], "packet_vehicle_move": ["container", [{"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "yaw", "type": "f32"}, {"name": "pitch", "type": "f32"}]], "packet_steer_boat": ["container", [{"name": "leftPaddle", "type": "bool"}, {"name": "rightPaddle", "type": "bool"}]], "packet_craft_recipe_request": ["container", [{"name": "windowId", "type": "i8"}, {"name": "recipe", "type": "string"}, {"name": "makeAll", "type": "bool"}]], "packet_abilities": ["container", [{"name": "flags", "type": "i8"}]], "packet_block_dig": ["container", [{"name": "status", "type": "varint"}, {"name": "location", "type": "position"}, {"name": "face", "type": "i8"}, {"name": "sequence", "type": "varint"}]], "packet_entity_action": ["container", [{"name": "entityId", "type": "varint"}, {"name": "actionId", "type": "varint"}, {"name": "jumpBoost", "type": "varint"}]], "packet_steer_vehicle": ["container", [{"name": "sideways", "type": "f32"}, {"name": "forward", "type": "f32"}, {"name": "jump", "type": "u8"}]], "packet_displayed_recipe": ["container", [{"name": "recipeId", "type": "string"}]], "packet_recipe_book": ["container", [{"name": "bookId", "type": "varint"}, {"name": "bookOpen", "type": "bool"}, {"name": "filterActive", "type": "bool"}]], "packet_resource_pack_receive": ["container", [{"name": "result", "type": "varint"}]], "packet_held_item_slot": ["container", [{"name": "slotId", "type": "i16"}]], "packet_set_creative_slot": ["container", [{"name": "slot", "type": "i16"}, {"name": "item", "type": "slot"}]], "packet_update_jigsaw_block": ["container", [{"name": "location", "type": "position"}, {"name": "name", "type": "string"}, {"name": "target", "type": "string"}, {"name": "pool", "type": "string"}, {"name": "finalState", "type": "string"}, {"name": "jointType", "type": "string"}]], "packet_update_sign": ["container", [{"name": "location", "type": "position"}, {"name": "text1", "type": "string"}, {"name": "text2", "type": "string"}, {"name": "text3", "type": "string"}, {"name": "text4", "type": "string"}]], "packet_arm_animation": ["container", [{"name": "hand", "type": "varint"}]], "packet_spectate": ["container", [{"name": "target", "type": "UUID"}]], "packet_block_place": ["container", [{"name": "hand", "type": "varint"}, {"name": "location", "type": "position"}, {"name": "direction", "type": "varint"}, {"name": "cursorX", "type": "f32"}, {"name": "cursorY", "type": "f32"}, {"name": "cursorZ", "type": "f32"}, {"name": "insideBlock", "type": "bool"}, {"name": "sequence", "type": "varint"}]], "packet_use_item": ["container", [{"name": "hand", "type": "varint"}, {"name": "sequence", "type": "varint"}]], "packet_advancement_tab": ["container", [{"name": "action", "type": "varint"}, {"name": "tabId", "type": ["switch", {"compareTo": "action", "fields": {"0": "string", "1": "void"}}]}]], "packet_pong": ["container", [{"name": "id", "type": "i32"}]], "packet_chat_session_update": ["container", [{"name": "sessionUUID", "type": "UUID"}, {"name": "expireTime", "type": "i64"}, {"name": "public<PERSON>ey", "type": ["buffer", {"countType": "varint"}]}, {"name": "signature", "type": ["buffer", {"countType": "varint"}]}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "teleport_confirm", "0x01": "query_block_nbt", "0x02": "set_difficulty", "0x03": "message_acknowledgement", "0x04": "chat_command", "0x05": "chat_message", "0x06": "chat_session_update", "0x07": "client_command", "0x08": "settings", "0x09": "tab_complete", "0x0a": "enchant_item", "0x0b": "window_click", "0x0c": "close_window", "0x0d": "custom_payload", "0x0e": "edit_book", "0x0f": "query_entity_nbt", "0x10": "use_entity", "0x11": "generate_structure", "0x12": "keep_alive", "0x13": "lock_difficulty", "0x14": "position", "0x15": "position_look", "0x16": "look", "0x17": "flying", "0x18": "vehicle_move", "0x19": "steer_boat", "0x1a": "pick_item", "0x1b": "craft_recipe_request", "0x1c": "abilities", "0x1d": "block_dig", "0x1e": "entity_action", "0x1f": "steer_vehicle", "0x20": "pong", "0x21": "recipe_book", "0x22": "displayed_recipe", "0x23": "name_item", "0x24": "resource_pack_receive", "0x25": "advancement_tab", "0x26": "select_trade", "0x27": "set_beacon_effect", "0x28": "held_item_slot", "0x29": "update_command_block", "0x2a": "update_command_block_minecart", "0x2b": "set_creative_slot", "0x2c": "update_jigsaw_block", "0x2d": "update_structure_block", "0x2e": "update_sign", "0x2f": "arm_animation", "0x30": "spectate", "0x31": "block_place", "0x32": "use_item"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"teleport_confirm": "packet_teleport_confirm", "query_block_nbt": "packet_query_block_nbt", "set_difficulty": "packet_set_difficulty", "message_acknowledgement": "packet_message_acknowledgement", "chat_command": "packet_chat_command", "chat_message": "packet_chat_message", "client_command": "packet_client_command", "settings": "packet_settings", "tab_complete": "packet_tab_complete", "enchant_item": "packet_enchant_item", "window_click": "packet_window_click", "close_window": "packet_close_window", "custom_payload": "packet_custom_payload", "edit_book": "packet_edit_book", "query_entity_nbt": "packet_query_entity_nbt", "use_entity": "packet_use_entity", "generate_structure": "packet_generate_structure", "keep_alive": "packet_keep_alive", "lock_difficulty": "packet_lock_difficulty", "position": "packet_position", "position_look": "packet_position_look", "look": "packet_look", "flying": "packet_flying", "vehicle_move": "packet_vehicle_move", "steer_boat": "packet_steer_boat", "pick_item": "packet_pick_item", "craft_recipe_request": "packet_craft_recipe_request", "abilities": "packet_abilities", "block_dig": "packet_block_dig", "entity_action": "packet_entity_action", "steer_vehicle": "packet_steer_vehicle", "pong": "packet_pong", "chat_session_update": "packet_chat_session_update", "recipe_book": "packet_recipe_book", "displayed_recipe": "packet_displayed_recipe", "name_item": "packet_name_item", "resource_pack_receive": "packet_resource_pack_receive", "advancement_tab": "packet_advancement_tab", "select_trade": "packet_select_trade", "set_beacon_effect": "packet_set_beacon_effect", "held_item_slot": "packet_held_item_slot", "update_command_block": "packet_update_command_block", "update_command_block_minecart": "packet_update_command_block_minecart", "set_creative_slot": "packet_set_creative_slot", "update_jigsaw_block": "packet_update_jigsaw_block", "update_structure_block": "packet_update_structure_block", "update_sign": "packet_update_sign", "arm_animation": "packet_arm_animation", "spectate": "packet_spectate", "block_place": "packet_block_place", "use_item": "packet_use_item"}}]}]]}}}}