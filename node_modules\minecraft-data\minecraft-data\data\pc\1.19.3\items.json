[{"id": 0, "name": "air", "displayName": "Air", "stackSize": 64}, {"id": 1, "name": "stone", "displayName": "Stone", "stackSize": 64}, {"id": 2, "name": "granite", "displayName": "Granite", "stackSize": 64}, {"id": 3, "name": "polished_granite", "displayName": "Polished Granite", "stackSize": 64}, {"id": 4, "name": "diorite", "displayName": "Diorite", "stackSize": 64}, {"id": 5, "name": "polished_diorite", "displayName": "Polished Diorite", "stackSize": 64}, {"id": 6, "name": "andesite", "displayName": "Andesite", "stackSize": 64}, {"id": 7, "name": "polished_andesite", "displayName": "Polished Andesite", "stackSize": 64}, {"id": 8, "name": "deepslate", "displayName": "Deepslate", "stackSize": 64}, {"id": 9, "name": "cobbled_deepslate", "displayName": "Cobbled Deepslate", "stackSize": 64}, {"id": 10, "name": "polished_deepslate", "displayName": "Polished Deepslate", "stackSize": 64}, {"id": 11, "name": "calcite", "displayName": "Calcite", "stackSize": 64}, {"id": 12, "name": "tuff", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 13, "name": "dripstone_block", "displayName": "Dripstone Block", "stackSize": 64}, {"id": 14, "name": "grass_block", "displayName": "Grass Block", "stackSize": 64}, {"id": 15, "name": "dirt", "displayName": "Dirt", "stackSize": 64}, {"id": 16, "name": "coarse_dirt", "displayName": "Coarse Dirt", "stackSize": 64}, {"id": 17, "name": "podzol", "displayName": "Podzol", "stackSize": 64}, {"id": 18, "name": "rooted_dirt", "displayName": "Rooted Dirt", "stackSize": 64}, {"id": 19, "name": "mud", "displayName": "Mud", "stackSize": 64}, {"id": 20, "name": "crimson_nylium", "displayName": "Crimson Nylium", "stackSize": 64}, {"id": 21, "name": "warped_nylium", "displayName": "Warped Nylium", "stackSize": 64}, {"id": 22, "name": "cobblestone", "displayName": "Cobblestone", "stackSize": 64}, {"id": 23, "name": "oak_planks", "displayName": "Oak Planks", "stackSize": 64}, {"id": 24, "name": "spruce_planks", "displayName": "Spruce Planks", "stackSize": 64}, {"id": 25, "name": "birch_planks", "displayName": "Birch Planks", "stackSize": 64}, {"id": 26, "name": "jungle_planks", "displayName": "Jungle Planks", "stackSize": 64}, {"id": 27, "name": "acacia_planks", "displayName": "Acacia Planks", "stackSize": 64}, {"id": 28, "name": "dark_oak_planks", "displayName": "Dark Oak Planks", "stackSize": 64}, {"id": 29, "name": "mangrove_planks", "displayName": "Mangrove Planks", "stackSize": 64}, {"id": 30, "name": "bamboo_planks", "displayName": "Bamboo Planks", "stackSize": 64}, {"id": 31, "name": "crimson_planks", "displayName": "Crimson Planks", "stackSize": 64}, {"id": 32, "name": "warped_planks", "displayName": "Warped Planks", "stackSize": 64}, {"id": 33, "name": "bamboo_mosaic", "displayName": "Bamboo Mosaic", "stackSize": 64}, {"id": 34, "name": "oak_sapling", "displayName": "Oak Sapling", "stackSize": 64}, {"id": 35, "name": "spruce_sapling", "displayName": "Spruce Sapling", "stackSize": 64}, {"id": 36, "name": "birch_sapling", "displayName": "Birch Sapling", "stackSize": 64}, {"id": 37, "name": "jungle_sapling", "displayName": "Jungle Sapling", "stackSize": 64}, {"id": 38, "name": "acacia_sapling", "displayName": "Acacia Sapling", "stackSize": 64}, {"id": 39, "name": "dark_oak_sapling", "displayName": "Dark Oak Sapling", "stackSize": 64}, {"id": 40, "name": "mangrove_propagule", "displayName": "Mangrove Propagule", "stackSize": 64}, {"id": 41, "name": "bedrock", "displayName": "Bedrock", "stackSize": 64}, {"id": 42, "name": "sand", "displayName": "Sand", "stackSize": 64}, {"id": 43, "name": "red_sand", "displayName": "Red Sand", "stackSize": 64}, {"id": 44, "name": "gravel", "displayName": "<PERSON>l", "stackSize": 64}, {"id": 45, "name": "coal_ore", "displayName": "Coal Ore", "stackSize": 64}, {"id": 46, "name": "deepslate_coal_ore", "displayName": "Deepslate Coal Ore", "stackSize": 64}, {"id": 47, "name": "iron_ore", "displayName": "Iron Ore", "stackSize": 64}, {"id": 48, "name": "deepslate_iron_ore", "displayName": "Deepslate Iron Ore", "stackSize": 64}, {"id": 49, "name": "copper_ore", "displayName": "Copper Ore", "stackSize": 64}, {"id": 50, "name": "deepslate_copper_ore", "displayName": "Deepslate Copper Ore", "stackSize": 64}, {"id": 51, "name": "gold_ore", "displayName": "Gold Ore", "stackSize": 64}, {"id": 52, "name": "deepslate_gold_ore", "displayName": "Deepslate Gold Ore", "stackSize": 64}, {"id": 53, "name": "redstone_ore", "displayName": "Redstone Ore", "stackSize": 64}, {"id": 54, "name": "deepslate_redstone_ore", "displayName": "Deepslate Redstone Ore", "stackSize": 64}, {"id": 55, "name": "emerald_ore", "displayName": "Emerald Ore", "stackSize": 64}, {"id": 56, "name": "deepslate_emerald_ore", "displayName": "Deepslate Emerald Ore", "stackSize": 64}, {"id": 57, "name": "lapis_ore", "displayName": "Lapis <PERSON> Ore", "stackSize": 64}, {"id": 58, "name": "deepslate_lapis_ore", "displayName": "Deepslate Lapis Lazuli Ore", "stackSize": 64}, {"id": 59, "name": "diamond_ore", "displayName": "Diamond Ore", "stackSize": 64}, {"id": 60, "name": "deepslate_diamond_ore", "displayName": "Deepslate Diamond Ore", "stackSize": 64}, {"id": 61, "name": "nether_gold_ore", "displayName": "Nether Gold Ore", "stackSize": 64}, {"id": 62, "name": "nether_quartz_ore", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 63, "name": "ancient_debris", "displayName": "Ancient Debris", "stackSize": 64}, {"id": 64, "name": "coal_block", "displayName": "Block of Coal", "stackSize": 64}, {"id": 65, "name": "raw_iron_block", "displayName": "Block of Raw Iron", "stackSize": 64}, {"id": 66, "name": "raw_copper_block", "displayName": "Block of Raw Copper", "stackSize": 64}, {"id": 67, "name": "raw_gold_block", "displayName": "Block of Raw Gold", "stackSize": 64}, {"id": 68, "name": "amethyst_block", "displayName": "Block of Amethyst", "stackSize": 64}, {"id": 69, "name": "budding_amethyst", "displayName": "Budding Amethyst", "stackSize": 64}, {"id": 70, "name": "iron_block", "displayName": "Block of Iron", "stackSize": 64}, {"id": 71, "name": "copper_block", "displayName": "Block of Copper", "stackSize": 64}, {"id": 72, "name": "gold_block", "displayName": "Block of Gold", "stackSize": 64}, {"id": 73, "name": "diamond_block", "displayName": "Block of Diamond", "stackSize": 64}, {"id": 74, "name": "netherite_block", "displayName": "Block of Netherite", "stackSize": 64}, {"id": 75, "name": "exposed_copper", "displayName": "Exposed Copper", "stackSize": 64}, {"id": 76, "name": "weathered_copper", "displayName": "Weathered Copper", "stackSize": 64}, {"id": 77, "name": "oxidized_copper", "displayName": "Oxidized Copper", "stackSize": 64}, {"id": 78, "name": "cut_copper", "displayName": "Cut Copper", "stackSize": 64}, {"id": 79, "name": "exposed_cut_copper", "displayName": "Exposed Cut Copper", "stackSize": 64}, {"id": 80, "name": "weathered_cut_copper", "displayName": "Weathered Cut Copper", "stackSize": 64}, {"id": 81, "name": "oxidized_cut_copper", "displayName": "Oxidized Cut Copper", "stackSize": 64}, {"id": 82, "name": "cut_copper_stairs", "displayName": "Cut Copper Stairs", "stackSize": 64}, {"id": 83, "name": "exposed_cut_copper_stairs", "displayName": "Exposed Cut Copper Stairs", "stackSize": 64}, {"id": 84, "name": "weathered_cut_copper_stairs", "displayName": "Weathered Cut Copper Stairs", "stackSize": 64}, {"id": 85, "name": "oxidized_cut_copper_stairs", "displayName": "Oxidized Cut Copper Stairs", "stackSize": 64}, {"id": 86, "name": "cut_copper_slab", "displayName": "Cut Copper Slab", "stackSize": 64}, {"id": 87, "name": "exposed_cut_copper_slab", "displayName": "Exposed Cut Copper Slab", "stackSize": 64}, {"id": 88, "name": "weathered_cut_copper_slab", "displayName": "Weathered Cut Copper Slab", "stackSize": 64}, {"id": 89, "name": "oxidized_cut_copper_slab", "displayName": "Oxidized Cut Copper Slab", "stackSize": 64}, {"id": 90, "name": "waxed_copper_block", "displayName": "Waxed Block of Copper", "stackSize": 64}, {"id": 91, "name": "waxed_exposed_copper", "displayName": "Waxed Exposed Copper", "stackSize": 64}, {"id": 92, "name": "waxed_weathered_copper", "displayName": "Waxed Weathered Copper", "stackSize": 64}, {"id": 93, "name": "waxed_oxidized_copper", "displayName": "Waxed Oxidized Copper", "stackSize": 64}, {"id": 94, "name": "waxed_cut_copper", "displayName": "Waxed Cut Copper", "stackSize": 64}, {"id": 95, "name": "waxed_exposed_cut_copper", "displayName": "Waxed Exposed Cut Copper", "stackSize": 64}, {"id": 96, "name": "waxed_weathered_cut_copper", "displayName": "Waxed Weathered Cut Copper", "stackSize": 64}, {"id": 97, "name": "waxed_oxidized_cut_copper", "displayName": "Waxed Oxidized Cut Copper", "stackSize": 64}, {"id": 98, "name": "waxed_cut_copper_stairs", "displayName": "Waxed Cut Copper Stairs", "stackSize": 64}, {"id": 99, "name": "waxed_exposed_cut_copper_stairs", "displayName": "Waxed Exposed Cut Copper Stairs", "stackSize": 64}, {"id": 100, "name": "waxed_weathered_cut_copper_stairs", "displayName": "Waxed Weathered Cut Copper Stairs", "stackSize": 64}, {"id": 101, "name": "waxed_oxidized_cut_copper_stairs", "displayName": "Waxed Oxidized Cut Copper Stairs", "stackSize": 64}, {"id": 102, "name": "waxed_cut_copper_slab", "displayName": "Waxed Cut Copper Slab", "stackSize": 64}, {"id": 103, "name": "waxed_exposed_cut_copper_slab", "displayName": "Waxed Exposed Cut Copper Slab", "stackSize": 64}, {"id": 104, "name": "waxed_weathered_cut_copper_slab", "displayName": "Waxed Weathered Cut Copper Slab", "stackSize": 64}, {"id": 105, "name": "waxed_oxidized_cut_copper_slab", "displayName": "Waxed Oxidized Cut Copper Slab", "stackSize": 64}, {"id": 106, "name": "oak_log", "displayName": "Oak Log", "stackSize": 64}, {"id": 107, "name": "spruce_log", "displayName": "Spruce Log", "stackSize": 64}, {"id": 108, "name": "birch_log", "displayName": "Birch Log", "stackSize": 64}, {"id": 109, "name": "jungle_log", "displayName": "Jungle Log", "stackSize": 64}, {"id": 110, "name": "acacia_log", "displayName": "Acacia Log", "stackSize": 64}, {"id": 111, "name": "dark_oak_log", "displayName": "Dark Oak Log", "stackSize": 64}, {"id": 112, "name": "mangrove_log", "displayName": "Mangrove Log", "stackSize": 64}, {"id": 113, "name": "mangrove_roots", "displayName": "Mangrove Roots", "stackSize": 64}, {"id": 114, "name": "muddy_mangrove_roots", "displayName": "Muddy Mangrove Roots", "stackSize": 64}, {"id": 115, "name": "crimson_stem", "displayName": "Crimson Stem", "stackSize": 64}, {"id": 116, "name": "warped_stem", "displayName": "Warped Stem", "stackSize": 64}, {"id": 117, "name": "bamboo_block", "displayName": "Block of Bamboo", "stackSize": 64}, {"id": 118, "name": "stripped_oak_log", "displayName": "Stripped Oak Log", "stackSize": 64}, {"id": 119, "name": "stripped_spruce_log", "displayName": "Stripped Spruce Log", "stackSize": 64}, {"id": 120, "name": "stripped_birch_log", "displayName": "Stripped Birch Log", "stackSize": 64}, {"id": 121, "name": "stripped_jungle_log", "displayName": "Stripped Jungle Log", "stackSize": 64}, {"id": 122, "name": "stripped_acacia_log", "displayName": "Stripped Acacia Log", "stackSize": 64}, {"id": 123, "name": "stripped_dark_oak_log", "displayName": "Stripped Dark Oak Log", "stackSize": 64}, {"id": 124, "name": "stripped_mangrove_log", "displayName": "Stripped Mangrove Log", "stackSize": 64}, {"id": 125, "name": "stripped_crimson_stem", "displayName": "Stripped Crimson Stem", "stackSize": 64}, {"id": 126, "name": "stripped_warped_stem", "displayName": "Stripped Warped Stem", "stackSize": 64}, {"id": 127, "name": "stripped_oak_wood", "displayName": "Stripped Oak Wood", "stackSize": 64}, {"id": 128, "name": "stripped_spruce_wood", "displayName": "Stripped Spruce Wood", "stackSize": 64}, {"id": 129, "name": "stripped_birch_wood", "displayName": "Stripped Birch Wood", "stackSize": 64}, {"id": 130, "name": "stripped_jungle_wood", "displayName": "Stripped Jungle Wood", "stackSize": 64}, {"id": 131, "name": "stripped_acacia_wood", "displayName": "Stripped Acacia Wood", "stackSize": 64}, {"id": 132, "name": "stripped_dark_oak_wood", "displayName": "Stripped Dark Oak Wood", "stackSize": 64}, {"id": 133, "name": "stripped_mangrove_wood", "displayName": "Stripped Mangrove Wood", "stackSize": 64}, {"id": 134, "name": "stripped_crimson_hyphae", "displayName": "Stripped Crimson Hyphae", "stackSize": 64}, {"id": 135, "name": "stripped_warped_hyphae", "displayName": "Stripped Warped Hyphae", "stackSize": 64}, {"id": 136, "name": "stripped_bamboo_block", "displayName": "Block of Stripped Bamboo", "stackSize": 64}, {"id": 137, "name": "oak_wood", "displayName": "Oak Wood", "stackSize": 64}, {"id": 138, "name": "spruce_wood", "displayName": "Spruce Wood", "stackSize": 64}, {"id": 139, "name": "birch_wood", "displayName": "Birch Wood", "stackSize": 64}, {"id": 140, "name": "jungle_wood", "displayName": "Jungle Wood", "stackSize": 64}, {"id": 141, "name": "acacia_wood", "displayName": "Acacia Wood", "stackSize": 64}, {"id": 142, "name": "dark_oak_wood", "displayName": "Dark Oak Wood", "stackSize": 64}, {"id": 143, "name": "mangrove_wood", "displayName": "Mangrove Wood", "stackSize": 64}, {"id": 144, "name": "crimson_hyphae", "displayName": "Crimson Hyphae", "stackSize": 64}, {"id": 145, "name": "warped_hyphae", "displayName": "Warped Hyphae", "stackSize": 64}, {"id": 146, "name": "oak_leaves", "displayName": "Oak Leaves", "stackSize": 64}, {"id": 147, "name": "spruce_leaves", "displayName": "Spruce Leaves", "stackSize": 64}, {"id": 148, "name": "birch_leaves", "displayName": "Birch Leaves", "stackSize": 64}, {"id": 149, "name": "jungle_leaves", "displayName": "Jungle Leaves", "stackSize": 64}, {"id": 150, "name": "acacia_leaves", "displayName": "Acacia Leaves", "stackSize": 64}, {"id": 151, "name": "dark_oak_leaves", "displayName": "Dark Oak Leaves", "stackSize": 64}, {"id": 152, "name": "mangrove_leaves", "displayName": "Mangrove Leaves", "stackSize": 64}, {"id": 153, "name": "azalea_leaves", "displayName": "Azalea Leaves", "stackSize": 64}, {"id": 154, "name": "flowering_azalea_leaves", "displayName": "Flowering Azalea Leaves", "stackSize": 64}, {"id": 155, "name": "sponge", "displayName": "Sponge", "stackSize": 64}, {"id": 156, "name": "wet_sponge", "displayName": "Wet Sponge", "stackSize": 64}, {"id": 157, "name": "glass", "displayName": "Glass", "stackSize": 64}, {"id": 158, "name": "tinted_glass", "displayName": "Tinted Glass", "stackSize": 64}, {"id": 159, "name": "lapis_block", "displayName": "Block of Lapis Lazuli", "stackSize": 64}, {"id": 160, "name": "sandstone", "displayName": "Sandstone", "stackSize": 64}, {"id": 161, "name": "chiseled_sandstone", "displayName": "Chiseled Sandstone", "stackSize": 64}, {"id": 162, "name": "cut_sandstone", "displayName": "Cut Sandstone", "stackSize": 64}, {"id": 163, "name": "cobweb", "displayName": "Cobweb", "stackSize": 64}, {"id": 164, "name": "grass", "displayName": "Grass", "stackSize": 64}, {"id": 165, "name": "fern", "displayName": "Fern", "stackSize": 64}, {"id": 166, "name": "azalea", "displayName": "Azalea", "stackSize": 64}, {"id": 167, "name": "flowering_azalea", "displayName": "Flowering Azalea", "stackSize": 64}, {"id": 168, "name": "dead_bush", "displayName": "Dead Bush", "stackSize": 64}, {"id": 169, "name": "seagrass", "displayName": "Seagrass", "stackSize": 64}, {"id": 170, "name": "sea_pickle", "displayName": "<PERSON>", "stackSize": 64}, {"id": 171, "name": "white_wool", "displayName": "White Wool", "stackSize": 64}, {"id": 172, "name": "orange_wool", "displayName": "Orange Wool", "stackSize": 64}, {"id": 173, "name": "magenta_wool", "displayName": "Magenta Wool", "stackSize": 64}, {"id": 174, "name": "light_blue_wool", "displayName": "Light Blue Wool", "stackSize": 64}, {"id": 175, "name": "yellow_wool", "displayName": "Yellow Wool", "stackSize": 64}, {"id": 176, "name": "lime_wool", "displayName": "Lime Wool", "stackSize": 64}, {"id": 177, "name": "pink_wool", "displayName": "Pink Wool", "stackSize": 64}, {"id": 178, "name": "gray_wool", "displayName": "Gray <PERSON>", "stackSize": 64}, {"id": 179, "name": "light_gray_wool", "displayName": "Light Gray Wool", "stackSize": 64}, {"id": 180, "name": "cyan_wool", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 181, "name": "purple_wool", "displayName": "Purple Wool", "stackSize": 64}, {"id": 182, "name": "blue_wool", "displayName": "Blue Wool", "stackSize": 64}, {"id": 183, "name": "brown_wool", "displayName": "Brown Wool", "stackSize": 64}, {"id": 184, "name": "green_wool", "displayName": "Green Wool", "stackSize": 64}, {"id": 185, "name": "red_wool", "displayName": "Red Wool", "stackSize": 64}, {"id": 186, "name": "black_wool", "displayName": "Black Wool", "stackSize": 64}, {"id": 187, "name": "dandelion", "displayName": "Dandelion", "stackSize": 64}, {"id": 188, "name": "poppy", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 189, "name": "blue_orchid", "displayName": "Blue Orchid", "stackSize": 64}, {"id": 190, "name": "allium", "displayName": "Allium", "stackSize": 64}, {"id": 191, "name": "azure_bluet", "displayName": "Azure Bluet", "stackSize": 64}, {"id": 192, "name": "red_tulip", "displayName": "<PERSON>lip", "stackSize": 64}, {"id": 193, "name": "orange_tulip", "displayName": "Orange Tulip", "stackSize": 64}, {"id": 194, "name": "white_tulip", "displayName": "White Tulip", "stackSize": 64}, {"id": 195, "name": "pink_tulip", "displayName": "<PERSON> Tulip", "stackSize": 64}, {"id": 196, "name": "oxeye_daisy", "displayName": "Oxeye Daisy", "stackSize": 64}, {"id": 197, "name": "cornflower", "displayName": "Corn<PERSON>", "stackSize": 64}, {"id": 198, "name": "lily_of_the_valley", "displayName": "Lily of the Valley", "stackSize": 64}, {"id": 199, "name": "wither_rose", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 200, "name": "spore_blossom", "displayName": "Spore Blossom", "stackSize": 64}, {"id": 201, "name": "brown_mushroom", "displayName": "Brown Mushroom", "stackSize": 64}, {"id": 202, "name": "red_mushroom", "displayName": "Red Mushroom", "stackSize": 64}, {"id": 203, "name": "crimson_fungus", "displayName": "Crimson Fungus", "stackSize": 64}, {"id": 204, "name": "warped_fungus", "displayName": "Warped Fungus", "stackSize": 64}, {"id": 205, "name": "crimson_roots", "displayName": "Crimson Roots", "stackSize": 64}, {"id": 206, "name": "warped_roots", "displayName": "Warped Roots", "stackSize": 64}, {"id": 207, "name": "nether_sprouts", "displayName": "Nether Sprouts", "stackSize": 64}, {"id": 208, "name": "weeping_vines", "displayName": "Weeping Vines", "stackSize": 64}, {"id": 209, "name": "twisting_vines", "displayName": "Twisting Vines", "stackSize": 64}, {"id": 210, "name": "sugar_cane", "displayName": "Sugar Cane", "stackSize": 64}, {"id": 211, "name": "kelp", "displayName": "<PERSON><PERSON><PERSON>", "stackSize": 64}, {"id": 212, "name": "moss_carpet", "displayName": "Moss Carpet", "stackSize": 64}, {"id": 213, "name": "moss_block", "displayName": "Moss Block", "stackSize": 64}, {"id": 214, "name": "hanging_roots", "displayName": "Hanging Roots", "stackSize": 64}, {"id": 215, "name": "big_dripleaf", "displayName": "Big Dripleaf", "stackSize": 64}, {"id": 216, "name": "small_dripleaf", "displayName": "Small Dripleaf", "stackSize": 64}, {"id": 217, "name": "bamboo", "displayName": "Bamboo", "stackSize": 64}, {"id": 218, "name": "oak_slab", "displayName": "Oak Slab", "stackSize": 64}, {"id": 219, "name": "spruce_slab", "displayName": "Spruce Slab", "stackSize": 64}, {"id": 220, "name": "birch_slab", "displayName": "<PERSON>", "stackSize": 64}, {"id": 221, "name": "jungle_slab", "displayName": "Jungle Slab", "stackSize": 64}, {"id": 222, "name": "acacia_slab", "displayName": "Acacia <PERSON>b", "stackSize": 64}, {"id": 223, "name": "dark_oak_slab", "displayName": "Dark Oak Slab", "stackSize": 64}, {"id": 224, "name": "mangrove_slab", "displayName": "Mangrove Slab", "stackSize": 64}, {"id": 225, "name": "bamboo_slab", "displayName": "Bamboo Slab", "stackSize": 64}, {"id": 226, "name": "bamboo_mosaic_slab", "displayName": "Bamboo Mosaic Slab", "stackSize": 64}, {"id": 227, "name": "crimson_slab", "displayName": "Crimson Slab", "stackSize": 64}, {"id": 228, "name": "warped_slab", "displayName": "Warped Slab", "stackSize": 64}, {"id": 229, "name": "stone_slab", "displayName": "<PERSON> Slab", "stackSize": 64}, {"id": 230, "name": "smooth_stone_slab", "displayName": "Smooth Stone Slab", "stackSize": 64}, {"id": 231, "name": "sandstone_slab", "displayName": "Sandstone Slab", "stackSize": 64}, {"id": 232, "name": "cut_sandstone_slab", "displayName": "Cut Sandstone Slab", "stackSize": 64}, {"id": 233, "name": "petrified_oak_slab", "displayName": "Petrified Oak Slab", "stackSize": 64}, {"id": 234, "name": "cobblestone_slab", "displayName": "Cobblestone Slab", "stackSize": 64}, {"id": 235, "name": "brick_slab", "displayName": "Brick Slab", "stackSize": 64}, {"id": 236, "name": "stone_brick_slab", "displayName": "Stone Brick Slab", "stackSize": 64}, {"id": 237, "name": "mud_brick_slab", "displayName": "Mud <PERSON> Slab", "stackSize": 64}, {"id": 238, "name": "nether_brick_slab", "displayName": "Nether Brick Slab", "stackSize": 64}, {"id": 239, "name": "quartz_slab", "displayName": "Quartz Slab", "stackSize": 64}, {"id": 240, "name": "red_sandstone_slab", "displayName": "Red Sandstone Slab", "stackSize": 64}, {"id": 241, "name": "cut_red_sandstone_slab", "displayName": "Cut Red Sandstone Slab", "stackSize": 64}, {"id": 242, "name": "purpur_slab", "displayName": "Purpur Slab", "stackSize": 64}, {"id": 243, "name": "prismarine_slab", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "stackSize": 64}, {"id": 244, "name": "prismarine_brick_slab", "displayName": "Prismarine Brick Slab", "stackSize": 64}, {"id": 245, "name": "dark_prismarine_slab", "displayName": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "stackSize": 64}, {"id": 246, "name": "smooth_quartz", "displayName": "Smooth Quartz Block", "stackSize": 64}, {"id": 247, "name": "smooth_red_sandstone", "displayName": "Smooth Red Sandstone", "stackSize": 64}, {"id": 248, "name": "smooth_sandstone", "displayName": "Smooth Sandstone", "stackSize": 64}, {"id": 249, "name": "smooth_stone", "displayName": "Smooth Stone", "stackSize": 64}, {"id": 250, "name": "bricks", "displayName": "Bricks", "stackSize": 64}, {"id": 251, "name": "bookshelf", "displayName": "Bookshelf", "stackSize": 64}, {"id": 252, "name": "chiseled_bookshelf", "displayName": "Chiseled Bookshelf", "stackSize": 64}, {"id": 253, "name": "mossy_cobblestone", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 254, "name": "obsidian", "displayName": "Obsidian", "stackSize": 64}, {"id": 255, "name": "torch", "displayName": "<PERSON>ch", "stackSize": 64}, {"id": 256, "name": "end_rod", "displayName": "End Rod", "stackSize": 64}, {"id": 257, "name": "chorus_plant", "displayName": "Chorus Plant", "stackSize": 64}, {"id": 258, "name": "chorus_flower", "displayName": "Chorus Flower", "stackSize": 64}, {"id": 259, "name": "purpur_block", "displayName": "Purpur Block", "stackSize": 64}, {"id": 260, "name": "purpur_pillar", "displayName": "Purpur Pillar", "stackSize": 64}, {"id": 261, "name": "purpur_stairs", "displayName": "Purpur Stairs", "stackSize": 64}, {"id": 262, "name": "spawner", "displayName": "Monster Spawner", "stackSize": 64}, {"id": 263, "name": "chest", "displayName": "Chest", "stackSize": 64}, {"id": 264, "name": "crafting_table", "displayName": "Crafting Table", "stackSize": 64}, {"id": 265, "name": "farmland", "displayName": "Farmland", "stackSize": 64}, {"id": 266, "name": "furnace", "displayName": "Furnace", "stackSize": 64}, {"id": 267, "name": "ladder", "displayName": "Ladder", "stackSize": 64}, {"id": 268, "name": "cobblestone_stairs", "displayName": "Cobblestone Stairs", "stackSize": 64}, {"id": 269, "name": "snow", "displayName": "Snow", "stackSize": 64}, {"id": 270, "name": "ice", "displayName": "Ice", "stackSize": 64}, {"id": 271, "name": "snow_block", "displayName": "Snow Block", "stackSize": 64}, {"id": 272, "name": "cactus", "displayName": "Cactus", "stackSize": 64}, {"id": 273, "name": "clay", "displayName": "<PERSON>", "stackSize": 64}, {"id": 274, "name": "jukebox", "displayName": "Jukebox", "stackSize": 64}, {"id": 275, "name": "oak_fence", "displayName": "Oak Fence", "stackSize": 64}, {"id": 276, "name": "spruce_fence", "displayName": "Spruce Fence", "stackSize": 64}, {"id": 277, "name": "birch_fence", "displayName": "<PERSON>", "stackSize": 64}, {"id": 278, "name": "jungle_fence", "displayName": "Jungle Fence", "stackSize": 64}, {"id": 279, "name": "acacia_fence", "displayName": "Acacia Fence", "stackSize": 64}, {"id": 280, "name": "dark_oak_fence", "displayName": "Dark Oak Fence", "stackSize": 64}, {"id": 281, "name": "mangrove_fence", "displayName": "Mangrove Fence", "stackSize": 64}, {"id": 282, "name": "bamboo_fence", "displayName": "Bamboo Fence", "stackSize": 64}, {"id": 283, "name": "crimson_fence", "displayName": "<PERSON> Fence", "stackSize": 64}, {"id": 284, "name": "warped_fence", "displayName": "Warped <PERSON>", "stackSize": 64}, {"id": 285, "name": "pumpkin", "displayName": "<PERSON><PERSON><PERSON>", "stackSize": 64}, {"id": 286, "name": "carved_pumpkin", "displayName": "<PERSON><PERSON>", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"id": 287, "name": "jack_o_lantern", "displayName": "<PERSON>'<PERSON>", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"id": 288, "name": "netherrack", "displayName": "Netherrack", "stackSize": 64}, {"id": 289, "name": "soul_sand", "displayName": "Soul Sand", "stackSize": 64}, {"id": 290, "name": "soul_soil", "displayName": "Soul Soil", "stackSize": 64}, {"id": 291, "name": "basalt", "displayName": "Basalt", "stackSize": 64}, {"id": 292, "name": "polished_basalt", "displayName": "Polished Ba<PERSON>t", "stackSize": 64}, {"id": 293, "name": "smooth_basalt", "displayName": "Smooth Basalt", "stackSize": 64}, {"id": 294, "name": "soul_torch", "displayName": "Soul Torch", "stackSize": 64}, {"id": 295, "name": "glowstone", "displayName": "Glowstone", "stackSize": 64}, {"id": 296, "name": "infested_stone", "displayName": "Infested Stone", "stackSize": 64}, {"id": 297, "name": "infested_cobblestone", "displayName": "Infested Cobblestone", "stackSize": 64}, {"id": 298, "name": "infested_stone_bricks", "displayName": "Infested Stone Bricks", "stackSize": 64}, {"id": 299, "name": "infested_mossy_stone_bricks", "displayName": "Infested Mossy Stone Bricks", "stackSize": 64}, {"id": 300, "name": "infested_cracked_stone_bricks", "displayName": "Infested Cracked Stone Bricks", "stackSize": 64}, {"id": 301, "name": "infested_chiseled_stone_bricks", "displayName": "Infested Chiseled Stone Bricks", "stackSize": 64}, {"id": 302, "name": "infested_deepslate", "displayName": "Infested Deepslate", "stackSize": 64}, {"id": 303, "name": "stone_bricks", "displayName": "Stone Bricks", "stackSize": 64}, {"id": 304, "name": "mossy_stone_bricks", "displayName": "Mossy Stone Bricks", "stackSize": 64}, {"id": 305, "name": "cracked_stone_bricks", "displayName": "Cracked Stone Bricks", "stackSize": 64}, {"id": 306, "name": "chiseled_stone_bricks", "displayName": "Chiseled Stone Bricks", "stackSize": 64}, {"id": 307, "name": "packed_mud", "displayName": "Packed Mud", "stackSize": 64}, {"id": 308, "name": "mud_bricks", "displayName": "Mud Bricks", "stackSize": 64}, {"id": 309, "name": "deepslate_bricks", "displayName": "Deepslate Bricks", "stackSize": 64}, {"id": 310, "name": "cracked_deepslate_bricks", "displayName": "Cracked Deepslate Bricks", "stackSize": 64}, {"id": 311, "name": "deepslate_tiles", "displayName": "Deepslate Tiles", "stackSize": 64}, {"id": 312, "name": "cracked_deepslate_tiles", "displayName": "Cracked Deepslate Tiles", "stackSize": 64}, {"id": 313, "name": "chiseled_deepslate", "displayName": "Chiseled Deepslate", "stackSize": 64}, {"id": 314, "name": "reinforced_deepslate", "displayName": "Reinforced Deepslate", "stackSize": 64}, {"id": 315, "name": "brown_mushroom_block", "displayName": "Brown Mushroom Block", "stackSize": 64}, {"id": 316, "name": "red_mushroom_block", "displayName": "Red Mushroom Block", "stackSize": 64}, {"id": 317, "name": "mushroom_stem", "displayName": "Mushroom Stem", "stackSize": 64}, {"id": 318, "name": "iron_bars", "displayName": "Iron Bars", "stackSize": 64}, {"id": 319, "name": "chain", "displayName": "Chain", "stackSize": 64}, {"id": 320, "name": "glass_pane", "displayName": "Glass Pane", "stackSize": 64}, {"id": 321, "name": "melon", "displayName": "Melon", "stackSize": 64}, {"id": 322, "name": "vine", "displayName": "Vines", "stackSize": 64}, {"id": 323, "name": "glow_lichen", "displayName": "Glow Lichen", "stackSize": 64}, {"id": 324, "name": "brick_stairs", "displayName": "Brick Stairs", "stackSize": 64}, {"id": 325, "name": "stone_brick_stairs", "displayName": "Stone Brick Stairs", "stackSize": 64}, {"id": 326, "name": "mud_brick_stairs", "displayName": "Mud Brick Stairs", "stackSize": 64}, {"id": 327, "name": "mycelium", "displayName": "Mycelium", "stackSize": 64}, {"id": 328, "name": "lily_pad", "displayName": "<PERSON>", "stackSize": 64}, {"id": 329, "name": "nether_bricks", "displayName": "Nether Bricks", "stackSize": 64}, {"id": 330, "name": "cracked_nether_bricks", "displayName": "Cracked Nether Bricks", "stackSize": 64}, {"id": 331, "name": "chiseled_nether_bricks", "displayName": "Chiseled Nether Bricks", "stackSize": 64}, {"id": 332, "name": "nether_brick_fence", "displayName": "Nether Brick Fence", "stackSize": 64}, {"id": 333, "name": "nether_brick_stairs", "displayName": "Nether Brick Stairs", "stackSize": 64}, {"id": 334, "name": "sculk", "displayName": "Sculk", "stackSize": 64}, {"id": 335, "name": "sculk_vein", "displayName": "Sculk Vein", "stackSize": 64}, {"id": 336, "name": "sculk_catalyst", "displayName": "Sculk Catalyst", "stackSize": 64}, {"id": 337, "name": "sculk_shrieker", "displayName": "<PERSON><PERSON><PERSON>", "stackSize": 64}, {"id": 338, "name": "enchanting_table", "displayName": "Enchanting Table", "stackSize": 64}, {"id": 339, "name": "end_portal_frame", "displayName": "End Portal Frame", "stackSize": 64}, {"id": 340, "name": "end_stone", "displayName": "End Stone", "stackSize": 64}, {"id": 341, "name": "end_stone_bricks", "displayName": "End Stone Bricks", "stackSize": 64}, {"id": 342, "name": "dragon_egg", "displayName": "Dragon Egg", "stackSize": 64}, {"id": 343, "name": "sandstone_stairs", "displayName": "Sandstone Stairs", "stackSize": 64}, {"id": 344, "name": "ender_chest", "displayName": "<PERSON><PERSON> Chest", "stackSize": 64}, {"id": 345, "name": "emerald_block", "displayName": "Block of Emerald", "stackSize": 64}, {"id": 346, "name": "oak_stairs", "displayName": "Oak Stairs", "stackSize": 64}, {"id": 347, "name": "spruce_stairs", "displayName": "Spruce Stairs", "stackSize": 64}, {"id": 348, "name": "birch_stairs", "displayName": "<PERSON> Stairs", "stackSize": 64}, {"id": 349, "name": "jungle_stairs", "displayName": "Jungle Stairs", "stackSize": 64}, {"id": 350, "name": "acacia_stairs", "displayName": "Acacia Stairs", "stackSize": 64}, {"id": 351, "name": "dark_oak_stairs", "displayName": "Dark Oak Stairs", "stackSize": 64}, {"id": 352, "name": "mangrove_stairs", "displayName": "Mangrove Stairs", "stackSize": 64}, {"id": 353, "name": "bamboo_stairs", "displayName": "Bamboo Stairs", "stackSize": 64}, {"id": 354, "name": "bamboo_mosaic_stairs", "displayName": "Bamboo Mosaic Stairs", "stackSize": 64}, {"id": 355, "name": "crimson_stairs", "displayName": "Crimson Stairs", "stackSize": 64}, {"id": 356, "name": "warped_stairs", "displayName": "Warped Stairs", "stackSize": 64}, {"id": 357, "name": "command_block", "displayName": "Command Block", "stackSize": 64}, {"id": 358, "name": "beacon", "displayName": "Beacon", "stackSize": 64}, {"id": 359, "name": "cobblestone_wall", "displayName": "Cobblestone Wall", "stackSize": 64}, {"id": 360, "name": "mossy_cobblestone_wall", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 361, "name": "brick_wall", "displayName": "Brick Wall", "stackSize": 64}, {"id": 362, "name": "prismarine_wall", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "stackSize": 64}, {"id": 363, "name": "red_sandstone_wall", "displayName": "Red Sandstone Wall", "stackSize": 64}, {"id": 364, "name": "mossy_stone_brick_wall", "displayName": "Mossy Stone Brick Wall", "stackSize": 64}, {"id": 365, "name": "granite_wall", "displayName": "Granite Wall", "stackSize": 64}, {"id": 366, "name": "stone_brick_wall", "displayName": "Stone Brick Wall", "stackSize": 64}, {"id": 367, "name": "mud_brick_wall", "displayName": "Mud Brick Wall", "stackSize": 64}, {"id": 368, "name": "nether_brick_wall", "displayName": "Nether Brick Wall", "stackSize": 64}, {"id": 369, "name": "andesite_wall", "displayName": "Andesite Wall", "stackSize": 64}, {"id": 370, "name": "red_nether_brick_wall", "displayName": "Red Nether Brick Wall", "stackSize": 64}, {"id": 371, "name": "sandstone_wall", "displayName": "Sandstone Wall", "stackSize": 64}, {"id": 372, "name": "end_stone_brick_wall", "displayName": "End Stone Brick Wall", "stackSize": 64}, {"id": 373, "name": "diorite_wall", "displayName": "Diorite Wall", "stackSize": 64}, {"id": 374, "name": "blackstone_wall", "displayName": "Blackstone Wall", "stackSize": 64}, {"id": 375, "name": "polished_blackstone_wall", "displayName": "Polished Blackstone Wall", "stackSize": 64}, {"id": 376, "name": "polished_blackstone_brick_wall", "displayName": "Polished Blackstone Brick Wall", "stackSize": 64}, {"id": 377, "name": "cobbled_deepslate_wall", "displayName": "Cobbled Deepslate Wall", "stackSize": 64}, {"id": 378, "name": "polished_deepslate_wall", "displayName": "Polished Deepslate Wall", "stackSize": 64}, {"id": 379, "name": "deepslate_brick_wall", "displayName": "Deepslate Brick Wall", "stackSize": 64}, {"id": 380, "name": "deepslate_tile_wall", "displayName": "Deepslate Tile Wall", "stackSize": 64}, {"id": 381, "name": "anvil", "displayName": "An<PERSON>", "stackSize": 64}, {"id": 382, "name": "chipped_anvil", "displayName": "Chipped Anvil", "stackSize": 64}, {"id": 383, "name": "damaged_anvil", "displayName": "Damaged Anvil", "stackSize": 64}, {"id": 384, "name": "chiseled_quartz_block", "displayName": "Chiseled Quartz Block", "stackSize": 64}, {"id": 385, "name": "quartz_block", "displayName": "Block of Quartz", "stackSize": 64}, {"id": 386, "name": "quartz_bricks", "displayName": "Quartz Bricks", "stackSize": 64}, {"id": 387, "name": "quartz_pillar", "displayName": "Quartz <PERSON>", "stackSize": 64}, {"id": 388, "name": "quartz_stairs", "displayName": "Quartz Stairs", "stackSize": 64}, {"id": 389, "name": "white_terracotta", "displayName": "White Terracotta", "stackSize": 64}, {"id": 390, "name": "orange_terracotta", "displayName": "Orange Terracotta", "stackSize": 64}, {"id": 391, "name": "magenta_terracotta", "displayName": "Magenta Terracotta", "stackSize": 64}, {"id": 392, "name": "light_blue_terracotta", "displayName": "Light Blue Terracotta", "stackSize": 64}, {"id": 393, "name": "yellow_terracotta", "displayName": "Yellow Terracotta", "stackSize": 64}, {"id": 394, "name": "lime_terracotta", "displayName": "Lime Terracotta", "stackSize": 64}, {"id": 395, "name": "pink_terracotta", "displayName": "Pink Terracotta", "stackSize": 64}, {"id": 396, "name": "gray_terracotta", "displayName": "Gray <PERSON>", "stackSize": 64}, {"id": 397, "name": "light_gray_terracotta", "displayName": "Light Gray Terracotta", "stackSize": 64}, {"id": 398, "name": "cyan_terracotta", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 399, "name": "purple_terracotta", "displayName": "Purple Terracotta", "stackSize": 64}, {"id": 400, "name": "blue_terracotta", "displayName": "Blue Terracotta", "stackSize": 64}, {"id": 401, "name": "brown_terracotta", "displayName": "Brown Terracotta", "stackSize": 64}, {"id": 402, "name": "green_terracotta", "displayName": "Green Terracotta", "stackSize": 64}, {"id": 403, "name": "red_terracotta", "displayName": "Red Terracotta", "stackSize": 64}, {"id": 404, "name": "black_terracotta", "displayName": "Black Terracotta", "stackSize": 64}, {"id": 405, "name": "barrier", "displayName": "Barrier", "stackSize": 64}, {"id": 406, "name": "light", "displayName": "Light", "stackSize": 64}, {"id": 407, "name": "hay_block", "displayName": "<PERSON>", "stackSize": 64}, {"id": 408, "name": "white_carpet", "displayName": "White Carpet", "stackSize": 64}, {"id": 409, "name": "orange_carpet", "displayName": "Orange Carpet", "stackSize": 64}, {"id": 410, "name": "magenta_carpet", "displayName": "Magenta Carpet", "stackSize": 64}, {"id": 411, "name": "light_blue_carpet", "displayName": "Light Blue Carpet", "stackSize": 64}, {"id": 412, "name": "yellow_carpet", "displayName": "Yellow Carpet", "stackSize": 64}, {"id": 413, "name": "lime_carpet", "displayName": "Lime Carpet", "stackSize": 64}, {"id": 414, "name": "pink_carpet", "displayName": "Pink Carpet", "stackSize": 64}, {"id": 415, "name": "gray_carpet", "displayName": "<PERSON> Carpet", "stackSize": 64}, {"id": 416, "name": "light_gray_carpet", "displayName": "Light Gray Carpet", "stackSize": 64}, {"id": 417, "name": "cyan_carpet", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 418, "name": "purple_carpet", "displayName": "Purple Carpet", "stackSize": 64}, {"id": 419, "name": "blue_carpet", "displayName": "Blue Carpet", "stackSize": 64}, {"id": 420, "name": "brown_carpet", "displayName": "Brown Carpet", "stackSize": 64}, {"id": 421, "name": "green_carpet", "displayName": "Green Carpet", "stackSize": 64}, {"id": 422, "name": "red_carpet", "displayName": "Red Carpet", "stackSize": 64}, {"id": 423, "name": "black_carpet", "displayName": "Black Carpet", "stackSize": 64}, {"id": 424, "name": "terracotta", "displayName": "Terracotta", "stackSize": 64}, {"id": 425, "name": "packed_ice", "displayName": "Packed Ice", "stackSize": 64}, {"id": 426, "name": "dirt_path", "displayName": "Dirt Path", "stackSize": 64}, {"id": 427, "name": "sunflower", "displayName": "Sunflower", "stackSize": 64}, {"id": 428, "name": "lilac", "displayName": "Lilac", "stackSize": 64}, {"id": 429, "name": "rose_bush", "displayName": "<PERSON>", "stackSize": 64}, {"id": 430, "name": "peony", "displayName": "Peony", "stackSize": 64}, {"id": 431, "name": "tall_grass", "displayName": "Tall Grass", "stackSize": 64}, {"id": 432, "name": "large_fern", "displayName": "Large Fern", "stackSize": 64}, {"id": 433, "name": "white_stained_glass", "displayName": "White Stained Glass", "stackSize": 64}, {"id": 434, "name": "orange_stained_glass", "displayName": "Orange Stained Glass", "stackSize": 64}, {"id": 435, "name": "magenta_stained_glass", "displayName": "Magenta Stained Glass", "stackSize": 64}, {"id": 436, "name": "light_blue_stained_glass", "displayName": "Light Blue Stained Glass", "stackSize": 64}, {"id": 437, "name": "yellow_stained_glass", "displayName": "Yellow Stained Glass", "stackSize": 64}, {"id": 438, "name": "lime_stained_glass", "displayName": "Lime Stained Glass", "stackSize": 64}, {"id": 439, "name": "pink_stained_glass", "displayName": "Pink Stained Glass", "stackSize": 64}, {"id": 440, "name": "gray_stained_glass", "displayName": "<PERSON> Stained Glass", "stackSize": 64}, {"id": 441, "name": "light_gray_stained_glass", "displayName": "Light Gray Stained Glass", "stackSize": 64}, {"id": 442, "name": "cyan_stained_glass", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 443, "name": "purple_stained_glass", "displayName": "Purple Stained Glass", "stackSize": 64}, {"id": 444, "name": "blue_stained_glass", "displayName": "Blue Stained Glass", "stackSize": 64}, {"id": 445, "name": "brown_stained_glass", "displayName": "<PERSON> Stained Glass", "stackSize": 64}, {"id": 446, "name": "green_stained_glass", "displayName": "Green Stained Glass", "stackSize": 64}, {"id": 447, "name": "red_stained_glass", "displayName": "Red Stained Glass", "stackSize": 64}, {"id": 448, "name": "black_stained_glass", "displayName": "Black Stained Glass", "stackSize": 64}, {"id": 449, "name": "white_stained_glass_pane", "displayName": "White Stained Glass Pane", "stackSize": 64}, {"id": 450, "name": "orange_stained_glass_pane", "displayName": "Orange Stained Glass Pane", "stackSize": 64}, {"id": 451, "name": "magenta_stained_glass_pane", "displayName": "Magenta Stained Glass Pane", "stackSize": 64}, {"id": 452, "name": "light_blue_stained_glass_pane", "displayName": "Light Blue Stained Glass Pane", "stackSize": 64}, {"id": 453, "name": "yellow_stained_glass_pane", "displayName": "Yellow Stained Glass Pane", "stackSize": 64}, {"id": 454, "name": "lime_stained_glass_pane", "displayName": "Lime Stained Glass Pane", "stackSize": 64}, {"id": 455, "name": "pink_stained_glass_pane", "displayName": "Pink Stained Glass Pane", "stackSize": 64}, {"id": 456, "name": "gray_stained_glass_pane", "displayName": "Gray Stained Glass Pane", "stackSize": 64}, {"id": 457, "name": "light_gray_stained_glass_pane", "displayName": "Light Gray Stained Glass Pane", "stackSize": 64}, {"id": 458, "name": "cyan_stained_glass_pane", "displayName": "<PERSON><PERSON> Stained Glass Pane", "stackSize": 64}, {"id": 459, "name": "purple_stained_glass_pane", "displayName": "Purple Stained Glass Pane", "stackSize": 64}, {"id": 460, "name": "blue_stained_glass_pane", "displayName": "Blue Stained Glass Pane", "stackSize": 64}, {"id": 461, "name": "brown_stained_glass_pane", "displayName": "<PERSON> Stained Glass Pane", "stackSize": 64}, {"id": 462, "name": "green_stained_glass_pane", "displayName": "Green Stained Glass Pane", "stackSize": 64}, {"id": 463, "name": "red_stained_glass_pane", "displayName": "Red Stained Glass Pane", "stackSize": 64}, {"id": 464, "name": "black_stained_glass_pane", "displayName": "Black Stained Glass Pane", "stackSize": 64}, {"id": 465, "name": "prismarine", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "stackSize": 64}, {"id": 466, "name": "prismarine_bricks", "displayName": "Prismarine <PERSON>s", "stackSize": 64}, {"id": 467, "name": "dark_prismarine", "displayName": "<PERSON>", "stackSize": 64}, {"id": 468, "name": "prismarine_stairs", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "stackSize": 64}, {"id": 469, "name": "prismarine_brick_stairs", "displayName": "Prismarine Brick Stairs", "stackSize": 64}, {"id": 470, "name": "dark_prismarine_stairs", "displayName": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "stackSize": 64}, {"id": 471, "name": "sea_lantern", "displayName": "Sea Lantern", "stackSize": 64}, {"id": 472, "name": "red_sandstone", "displayName": "Red Sandstone", "stackSize": 64}, {"id": 473, "name": "chiseled_red_sandstone", "displayName": "Chiseled Red Sandstone", "stackSize": 64}, {"id": 474, "name": "cut_red_sandstone", "displayName": "Cut Red Sandstone", "stackSize": 64}, {"id": 475, "name": "red_sandstone_stairs", "displayName": "Red Sandstone Stairs", "stackSize": 64}, {"id": 476, "name": "repeating_command_block", "displayName": "Repeating Command Block", "stackSize": 64}, {"id": 477, "name": "chain_command_block", "displayName": "Chain Command Block", "stackSize": 64}, {"id": 478, "name": "magma_block", "displayName": "Magma Block", "stackSize": 64}, {"id": 479, "name": "nether_wart_block", "displayName": "Nether Wart Block", "stackSize": 64}, {"id": 480, "name": "warped_wart_block", "displayName": "Warped Wart Block", "stackSize": 64}, {"id": 481, "name": "red_nether_bricks", "displayName": "Red Nether Bricks", "stackSize": 64}, {"id": 482, "name": "bone_block", "displayName": "Bone Block", "stackSize": 64}, {"id": 483, "name": "structure_void", "displayName": "Structure Void", "stackSize": 64}, {"id": 484, "name": "shulker_box", "displayName": "Shulker Box", "stackSize": 1}, {"id": 485, "name": "white_shulker_box", "displayName": "White Shulker Box", "stackSize": 1}, {"id": 486, "name": "orange_shulker_box", "displayName": "Orange Shulker Box", "stackSize": 1}, {"id": 487, "name": "magenta_shulker_box", "displayName": "<PERSON><PERSON>a <PERSON>er Box", "stackSize": 1}, {"id": 488, "name": "light_blue_shulker_box", "displayName": "Light Blue Shulker Box", "stackSize": 1}, {"id": 489, "name": "yellow_shulker_box", "displayName": "Yellow Shulker Box", "stackSize": 1}, {"id": 490, "name": "lime_shulker_box", "displayName": "<PERSON>e <PERSON>er Box", "stackSize": 1}, {"id": 491, "name": "pink_shulker_box", "displayName": "Pink Shulker Box", "stackSize": 1}, {"id": 492, "name": "gray_shulker_box", "displayName": "<PERSON>", "stackSize": 1}, {"id": 493, "name": "light_gray_shulker_box", "displayName": "Light Gray Shulker Box", "stackSize": 1}, {"id": 494, "name": "cyan_shulker_box", "displayName": "<PERSON><PERSON>", "stackSize": 1}, {"id": 495, "name": "purple_shulker_box", "displayName": "Purple Shulker Box", "stackSize": 1}, {"id": 496, "name": "blue_shulker_box", "displayName": "Blue Shulker Box", "stackSize": 1}, {"id": 497, "name": "brown_shulker_box", "displayName": "<PERSON> Shulker Box", "stackSize": 1}, {"id": 498, "name": "green_shulker_box", "displayName": "Green Shulker Box", "stackSize": 1}, {"id": 499, "name": "red_shulker_box", "displayName": "Red Shulker Box", "stackSize": 1}, {"id": 500, "name": "black_shulker_box", "displayName": "Black Shulker Box", "stackSize": 1}, {"id": 501, "name": "white_glazed_terracotta", "displayName": "White Glazed Terracotta", "stackSize": 64}, {"id": 502, "name": "orange_glazed_terracotta", "displayName": "Orange Glazed Terracotta", "stackSize": 64}, {"id": 503, "name": "magenta_glazed_terracotta", "displayName": "Magenta Glazed Terracotta", "stackSize": 64}, {"id": 504, "name": "light_blue_glazed_terracotta", "displayName": "Light Blue Glazed Terracotta", "stackSize": 64}, {"id": 505, "name": "yellow_glazed_terracotta", "displayName": "Yellow Glazed Terracotta", "stackSize": 64}, {"id": 506, "name": "lime_glazed_terracotta", "displayName": "Lime Glazed Terracotta", "stackSize": 64}, {"id": 507, "name": "pink_glazed_terracotta", "displayName": "Pink Glazed Terracotta", "stackSize": 64}, {"id": 508, "name": "gray_glazed_terracotta", "displayName": "Gray Glazed Terracotta", "stackSize": 64}, {"id": 509, "name": "light_gray_glazed_terracotta", "displayName": "Light Gray Glazed Terracotta", "stackSize": 64}, {"id": 510, "name": "cyan_glazed_terracotta", "displayName": "<PERSON><PERSON>zed Terracotta", "stackSize": 64}, {"id": 511, "name": "purple_glazed_terracotta", "displayName": "Purple Glazed Terracotta", "stackSize": 64}, {"id": 512, "name": "blue_glazed_terracotta", "displayName": "Blue Glazed Terracotta", "stackSize": 64}, {"id": 513, "name": "brown_glazed_terracotta", "displayName": "Brown Glazed Terracotta", "stackSize": 64}, {"id": 514, "name": "green_glazed_terracotta", "displayName": "Green Glazed Terracotta", "stackSize": 64}, {"id": 515, "name": "red_glazed_terracotta", "displayName": "Red Glazed Terracotta", "stackSize": 64}, {"id": 516, "name": "black_glazed_terracotta", "displayName": "Black Glazed Terracotta", "stackSize": 64}, {"id": 517, "name": "white_concrete", "displayName": "White Concrete", "stackSize": 64}, {"id": 518, "name": "orange_concrete", "displayName": "Orange Concrete", "stackSize": 64}, {"id": 519, "name": "magenta_concrete", "displayName": "Magenta Concrete", "stackSize": 64}, {"id": 520, "name": "light_blue_concrete", "displayName": "Light Blue Concrete", "stackSize": 64}, {"id": 521, "name": "yellow_concrete", "displayName": "Yellow Concrete", "stackSize": 64}, {"id": 522, "name": "lime_concrete", "displayName": "Lime Concrete", "stackSize": 64}, {"id": 523, "name": "pink_concrete", "displayName": "Pink Concrete", "stackSize": 64}, {"id": 524, "name": "gray_concrete", "displayName": "<PERSON>", "stackSize": 64}, {"id": 525, "name": "light_gray_concrete", "displayName": "Light Gray Concrete", "stackSize": 64}, {"id": 526, "name": "cyan_concrete", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 527, "name": "purple_concrete", "displayName": "Purple Concrete", "stackSize": 64}, {"id": 528, "name": "blue_concrete", "displayName": "Blue Concrete", "stackSize": 64}, {"id": 529, "name": "brown_concrete", "displayName": "<PERSON> Concrete", "stackSize": 64}, {"id": 530, "name": "green_concrete", "displayName": "Green Concrete", "stackSize": 64}, {"id": 531, "name": "red_concrete", "displayName": "Red Concrete", "stackSize": 64}, {"id": 532, "name": "black_concrete", "displayName": "Black Concrete", "stackSize": 64}, {"id": 533, "name": "white_concrete_powder", "displayName": "White Concrete Powder", "stackSize": 64}, {"id": 534, "name": "orange_concrete_powder", "displayName": "Orange Concrete Powder", "stackSize": 64}, {"id": 535, "name": "magenta_concrete_powder", "displayName": "Magenta Concrete Powder", "stackSize": 64}, {"id": 536, "name": "light_blue_concrete_powder", "displayName": "Light Blue Concrete Powder", "stackSize": 64}, {"id": 537, "name": "yellow_concrete_powder", "displayName": "Yellow Concrete Powder", "stackSize": 64}, {"id": 538, "name": "lime_concrete_powder", "displayName": "Lime Concrete <PERSON>", "stackSize": 64}, {"id": 539, "name": "pink_concrete_powder", "displayName": "Pink Concrete Powder", "stackSize": 64}, {"id": 540, "name": "gray_concrete_powder", "displayName": "<PERSON> Concre<PERSON>", "stackSize": 64}, {"id": 541, "name": "light_gray_concrete_powder", "displayName": "Light Gray Concrete Powder", "stackSize": 64}, {"id": 542, "name": "cyan_concrete_powder", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 543, "name": "purple_concrete_powder", "displayName": "Purple Concrete Powder", "stackSize": 64}, {"id": 544, "name": "blue_concrete_powder", "displayName": "Blue Concrete Powder", "stackSize": 64}, {"id": 545, "name": "brown_concrete_powder", "displayName": "<PERSON> Concrete <PERSON>", "stackSize": 64}, {"id": 546, "name": "green_concrete_powder", "displayName": "Green Concrete Powder", "stackSize": 64}, {"id": 547, "name": "red_concrete_powder", "displayName": "Red Concrete Powder", "stackSize": 64}, {"id": 548, "name": "black_concrete_powder", "displayName": "Black Concrete Powder", "stackSize": 64}, {"id": 549, "name": "turtle_egg", "displayName": "Turtle Egg", "stackSize": 64}, {"id": 550, "name": "dead_tube_coral_block", "displayName": "Dead Tube Coral Block", "stackSize": 64}, {"id": 551, "name": "dead_brain_coral_block", "displayName": "Dead Brain Coral Block", "stackSize": 64}, {"id": 552, "name": "dead_bubble_coral_block", "displayName": "Dead Bubble Coral Block", "stackSize": 64}, {"id": 553, "name": "dead_fire_coral_block", "displayName": "Dead Fire Coral Block", "stackSize": 64}, {"id": 554, "name": "dead_horn_coral_block", "displayName": "Dead Horn Coral Block", "stackSize": 64}, {"id": 555, "name": "tube_coral_block", "displayName": "Tube Coral Block", "stackSize": 64}, {"id": 556, "name": "brain_coral_block", "displayName": "Brain <PERSON>", "stackSize": 64}, {"id": 557, "name": "bubble_coral_block", "displayName": "Bubble Coral Block", "stackSize": 64}, {"id": 558, "name": "fire_coral_block", "displayName": "Fire Coral Block", "stackSize": 64}, {"id": 559, "name": "horn_coral_block", "displayName": "Horn Coral Block", "stackSize": 64}, {"id": 560, "name": "tube_coral", "displayName": "Tube Coral", "stackSize": 64}, {"id": 561, "name": "brain_coral", "displayName": "Brain Coral", "stackSize": 64}, {"id": 562, "name": "bubble_coral", "displayName": "Bubble Coral", "stackSize": 64}, {"id": 563, "name": "fire_coral", "displayName": "Fire Coral", "stackSize": 64}, {"id": 564, "name": "horn_coral", "displayName": "Horn Coral", "stackSize": 64}, {"id": 565, "name": "dead_brain_coral", "displayName": "Dead Brain Coral", "stackSize": 64}, {"id": 566, "name": "dead_bubble_coral", "displayName": "Dead Bubble Coral", "stackSize": 64}, {"id": 567, "name": "dead_fire_coral", "displayName": "Dead Fire Coral", "stackSize": 64}, {"id": 568, "name": "dead_horn_coral", "displayName": "Dead Horn Coral", "stackSize": 64}, {"id": 569, "name": "dead_tube_coral", "displayName": "Dead Tube Coral", "stackSize": 64}, {"id": 570, "name": "tube_coral_fan", "displayName": "Tube Coral Fan", "stackSize": 64}, {"id": 571, "name": "brain_coral_fan", "displayName": "Brain Coral Fan", "stackSize": 64}, {"id": 572, "name": "bubble_coral_fan", "displayName": "Bubble Coral Fan", "stackSize": 64}, {"id": 573, "name": "fire_coral_fan", "displayName": "Fire Coral Fan", "stackSize": 64}, {"id": 574, "name": "horn_coral_fan", "displayName": "Horn Coral Fan", "stackSize": 64}, {"id": 575, "name": "dead_tube_coral_fan", "displayName": "Dead Tube Coral Fan", "stackSize": 64}, {"id": 576, "name": "dead_brain_coral_fan", "displayName": "Dead Brain Coral Fan", "stackSize": 64}, {"id": 577, "name": "dead_bubble_coral_fan", "displayName": "Dead Bubble Coral Fan", "stackSize": 64}, {"id": 578, "name": "dead_fire_coral_fan", "displayName": "Dead Fire Coral Fan", "stackSize": 64}, {"id": 579, "name": "dead_horn_coral_fan", "displayName": "Dead Horn Coral Fan", "stackSize": 64}, {"id": 580, "name": "blue_ice", "displayName": "Blue Ice", "stackSize": 64}, {"id": 581, "name": "conduit", "displayName": "Conduit", "stackSize": 64}, {"id": 582, "name": "polished_granite_stairs", "displayName": "Polished Granite Stairs", "stackSize": 64}, {"id": 583, "name": "smooth_red_sandstone_stairs", "displayName": "Smooth Red Sandstone Stairs", "stackSize": 64}, {"id": 584, "name": "mossy_stone_brick_stairs", "displayName": "Mossy Stone Brick Stairs", "stackSize": 64}, {"id": 585, "name": "polished_diorite_stairs", "displayName": "Polished Diorite Stairs", "stackSize": 64}, {"id": 586, "name": "mossy_cobblestone_stairs", "displayName": "Mossy Cobblestone Stairs", "stackSize": 64}, {"id": 587, "name": "end_stone_brick_stairs", "displayName": "End Stone Brick Stairs", "stackSize": 64}, {"id": 588, "name": "stone_stairs", "displayName": "Stone Stairs", "stackSize": 64}, {"id": 589, "name": "smooth_sandstone_stairs", "displayName": "Smooth Sandstone Stairs", "stackSize": 64}, {"id": 590, "name": "smooth_quartz_stairs", "displayName": "Smooth Quartz Stairs", "stackSize": 64}, {"id": 591, "name": "granite_stairs", "displayName": "Granite Stairs", "stackSize": 64}, {"id": 592, "name": "andesite_stairs", "displayName": "Andesite Stairs", "stackSize": 64}, {"id": 593, "name": "red_nether_brick_stairs", "displayName": "Red Nether Brick Stairs", "stackSize": 64}, {"id": 594, "name": "polished_andesite_stairs", "displayName": "Polished Andesite Stairs", "stackSize": 64}, {"id": 595, "name": "diorite_stairs", "displayName": "Diorite Stairs", "stackSize": 64}, {"id": 596, "name": "cobbled_deepslate_stairs", "displayName": "Cobbled Deepslate Stairs", "stackSize": 64}, {"id": 597, "name": "polished_deepslate_stairs", "displayName": "Polished Deepslate Stairs", "stackSize": 64}, {"id": 598, "name": "deepslate_brick_stairs", "displayName": "Deepslate Brick Stairs", "stackSize": 64}, {"id": 599, "name": "deepslate_tile_stairs", "displayName": "Deepslate Tile Stairs", "stackSize": 64}, {"id": 600, "name": "polished_granite_slab", "displayName": "Polished Granite Slab", "stackSize": 64}, {"id": 601, "name": "smooth_red_sandstone_slab", "displayName": "Smooth Red Sandstone Slab", "stackSize": 64}, {"id": 602, "name": "mossy_stone_brick_slab", "displayName": "Mossy Stone Brick Slab", "stackSize": 64}, {"id": 603, "name": "polished_diorite_slab", "displayName": "Polished Diorite S<PERSON>b", "stackSize": 64}, {"id": 604, "name": "mossy_cobblestone_slab", "displayName": "<PERSON><PERSON> Slab", "stackSize": 64}, {"id": 605, "name": "end_stone_brick_slab", "displayName": "End Stone Brick Slab", "stackSize": 64}, {"id": 606, "name": "smooth_sandstone_slab", "displayName": "Smooth Sandstone Slab", "stackSize": 64}, {"id": 607, "name": "smooth_quartz_slab", "displayName": "Smooth Quartz Slab", "stackSize": 64}, {"id": 608, "name": "granite_slab", "displayName": "Granite Slab", "stackSize": 64}, {"id": 609, "name": "andesite_slab", "displayName": "Andesite Slab", "stackSize": 64}, {"id": 610, "name": "red_nether_brick_slab", "displayName": "Red Nether Brick Slab", "stackSize": 64}, {"id": 611, "name": "polished_andesite_slab", "displayName": "Polished Andesite Slab", "stackSize": 64}, {"id": 612, "name": "diorite_slab", "displayName": "Diorite Slab", "stackSize": 64}, {"id": 613, "name": "cobbled_deepslate_slab", "displayName": "Cobbled Deepslate Slab", "stackSize": 64}, {"id": 614, "name": "polished_deepslate_slab", "displayName": "Polished Deepslate Slab", "stackSize": 64}, {"id": 615, "name": "deepslate_brick_slab", "displayName": "Deepslate Brick Slab", "stackSize": 64}, {"id": 616, "name": "deepslate_tile_slab", "displayName": "Deepslate Tile Slab", "stackSize": 64}, {"id": 617, "name": "scaffolding", "displayName": "Scaffolding", "stackSize": 64}, {"id": 618, "name": "redstone", "displayName": "Redstone Dust", "stackSize": 64}, {"id": 619, "name": "redstone_torch", "displayName": "Redstone Torch", "stackSize": 64}, {"id": 620, "name": "redstone_block", "displayName": "Block of Redstone", "stackSize": 64}, {"id": 621, "name": "repeater", "displayName": "Redstone Repeater", "stackSize": 64}, {"id": 622, "name": "comparator", "displayName": "Redstone Comparator", "stackSize": 64}, {"id": 623, "name": "piston", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 624, "name": "sticky_piston", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 625, "name": "slime_block", "displayName": "Slime Block", "stackSize": 64}, {"id": 626, "name": "honey_block", "displayName": "Honey Block", "stackSize": 64}, {"id": 627, "name": "observer", "displayName": "Observer", "stackSize": 64}, {"id": 628, "name": "hopper", "displayName": "<PERSON>", "stackSize": 64}, {"id": 629, "name": "dispenser", "displayName": "Dispenser", "stackSize": 64}, {"id": 630, "name": "dropper", "displayName": "Dropper", "stackSize": 64}, {"id": 631, "name": "lectern", "displayName": "Lectern", "stackSize": 64}, {"id": 632, "name": "target", "displayName": "Target", "stackSize": 64}, {"id": 633, "name": "lever", "displayName": "Lever", "stackSize": 64}, {"id": 634, "name": "lightning_rod", "displayName": "Lightning Rod", "stackSize": 64}, {"id": 635, "name": "daylight_detector", "displayName": "Daylight Detector", "stackSize": 64}, {"id": 636, "name": "sculk_sensor", "displayName": "Sculk Sensor", "stackSize": 64}, {"id": 637, "name": "tripwire_hook", "displayName": "Tripwire Hook", "stackSize": 64}, {"id": 638, "name": "trapped_chest", "displayName": "Trapped Chest", "stackSize": 64}, {"id": 639, "name": "tnt", "displayName": "TNT", "stackSize": 64}, {"id": 640, "name": "redstone_lamp", "displayName": "Redstone Lamp", "stackSize": 64}, {"id": 641, "name": "note_block", "displayName": "Note Block", "stackSize": 64}, {"id": 642, "name": "stone_button", "displayName": "<PERSON>", "stackSize": 64}, {"id": 643, "name": "polished_blackstone_button", "displayName": "Polished Blackstone Button", "stackSize": 64}, {"id": 644, "name": "oak_button", "displayName": "Oak Button", "stackSize": 64}, {"id": 645, "name": "spruce_button", "displayName": "Spruce Button", "stackSize": 64}, {"id": 646, "name": "birch_button", "displayName": "<PERSON>", "stackSize": 64}, {"id": 647, "name": "jungle_button", "displayName": "<PERSON>ton", "stackSize": 64}, {"id": 648, "name": "acacia_button", "displayName": "Acacia <PERSON>", "stackSize": 64}, {"id": 649, "name": "dark_oak_button", "displayName": "Dark Oak Button", "stackSize": 64}, {"id": 650, "name": "mangrove_button", "displayName": "Mangrove Button", "stackSize": 64}, {"id": 651, "name": "bamboo_button", "displayName": "Bamboo Button", "stackSize": 64}, {"id": 652, "name": "crimson_button", "displayName": "<PERSON>", "stackSize": 64}, {"id": 653, "name": "warped_button", "displayName": "Warped <PERSON>", "stackSize": 64}, {"id": 654, "name": "stone_pressure_plate", "displayName": "Stone Pressure Plate", "stackSize": 64}, {"id": 655, "name": "polished_blackstone_pressure_plate", "displayName": "Polished Blackstone Pressure Plate", "stackSize": 64}, {"id": 656, "name": "light_weighted_pressure_plate", "displayName": "Light Weighted Pressure Plate", "stackSize": 64}, {"id": 657, "name": "heavy_weighted_pressure_plate", "displayName": "Heavy Weighted Pressure Plate", "stackSize": 64}, {"id": 658, "name": "oak_pressure_plate", "displayName": "Oak Pressure Plate", "stackSize": 64}, {"id": 659, "name": "spruce_pressure_plate", "displayName": "Spruce Pressure Plate", "stackSize": 64}, {"id": 660, "name": "birch_pressure_plate", "displayName": "Birch Pressure Plate", "stackSize": 64}, {"id": 661, "name": "jungle_pressure_plate", "displayName": "Jungle Pressure Plate", "stackSize": 64}, {"id": 662, "name": "acacia_pressure_plate", "displayName": "Acacia Pressure Plate", "stackSize": 64}, {"id": 663, "name": "dark_oak_pressure_plate", "displayName": "Dark Oak Pressure Plate", "stackSize": 64}, {"id": 664, "name": "mangrove_pressure_plate", "displayName": "Mangrove Pressure Plate", "stackSize": 64}, {"id": 665, "name": "bamboo_pressure_plate", "displayName": "Bamboo Pressure Plate", "stackSize": 64}, {"id": 666, "name": "crimson_pressure_plate", "displayName": "Crimson Pressure Plate", "stackSize": 64}, {"id": 667, "name": "warped_pressure_plate", "displayName": "Warped Pressure Plate", "stackSize": 64}, {"id": 668, "name": "iron_door", "displayName": "Iron Door", "stackSize": 64}, {"id": 669, "name": "oak_door", "displayName": "Oak Door", "stackSize": 64}, {"id": 670, "name": "spruce_door", "displayName": "Spruce Door", "stackSize": 64}, {"id": 671, "name": "birch_door", "displayName": "<PERSON>", "stackSize": 64}, {"id": 672, "name": "jungle_door", "displayName": "Jungle Door", "stackSize": 64}, {"id": 673, "name": "acacia_door", "displayName": "Acacia Door", "stackSize": 64}, {"id": 674, "name": "dark_oak_door", "displayName": "Dark Oak Door", "stackSize": 64}, {"id": 675, "name": "mangrove_door", "displayName": "Mangrove Door", "stackSize": 64}, {"id": 676, "name": "bamboo_door", "displayName": "Bamboo Door", "stackSize": 64}, {"id": 677, "name": "crimson_door", "displayName": "Crimson Door", "stackSize": 64}, {"id": 678, "name": "warped_door", "displayName": "Warped Door", "stackSize": 64}, {"id": 679, "name": "iron_trapdoor", "displayName": "Iron Trapdoor", "stackSize": 64}, {"id": 680, "name": "oak_trapdoor", "displayName": "Oak Trapdoor", "stackSize": 64}, {"id": 681, "name": "spruce_trapdoor", "displayName": "Spruce Trapdoor", "stackSize": 64}, {"id": 682, "name": "birch_trapdoor", "displayName": "<PERSON>", "stackSize": 64}, {"id": 683, "name": "jungle_trapdoor", "displayName": "Jungle Trapdoor", "stackSize": 64}, {"id": 684, "name": "acacia_trapdoor", "displayName": "Acacia T<PERSON>door", "stackSize": 64}, {"id": 685, "name": "dark_oak_trapdoor", "displayName": "Dark Oak Trapdoor", "stackSize": 64}, {"id": 686, "name": "mangrove_trapdoor", "displayName": "Mangrove Trapdoor", "stackSize": 64}, {"id": 687, "name": "bamboo_trapdoor", "displayName": "Bamboo Trapdoor", "stackSize": 64}, {"id": 688, "name": "crimson_trapdoor", "displayName": "Crimson Trapdoor", "stackSize": 64}, {"id": 689, "name": "warped_trapdoor", "displayName": "Warped Trapdoor", "stackSize": 64}, {"id": 690, "name": "oak_fence_gate", "displayName": "Oak Fence Gate", "stackSize": 64}, {"id": 691, "name": "spruce_fence_gate", "displayName": "Spruce Fence Gate", "stackSize": 64}, {"id": 692, "name": "birch_fence_gate", "displayName": "Birch Fence Gate", "stackSize": 64}, {"id": 693, "name": "jungle_fence_gate", "displayName": "Jungle Fence Gate", "stackSize": 64}, {"id": 694, "name": "acacia_fence_gate", "displayName": "Acacia Fence Gate", "stackSize": 64}, {"id": 695, "name": "dark_oak_fence_gate", "displayName": "Dark Oak Fence Gate", "stackSize": 64}, {"id": 696, "name": "mangrove_fence_gate", "displayName": "Mangrove Fence Gate", "stackSize": 64}, {"id": 697, "name": "bamboo_fence_gate", "displayName": "Bamboo Fence Gate", "stackSize": 64}, {"id": 698, "name": "crimson_fence_gate", "displayName": "Crimson Fence Gate", "stackSize": 64}, {"id": 699, "name": "warped_fence_gate", "displayName": "Warped Fence Gate", "stackSize": 64}, {"id": 700, "name": "powered_rail", "displayName": "Powered Rail", "stackSize": 64}, {"id": 701, "name": "detector_rail", "displayName": "Detector Rail", "stackSize": 64}, {"id": 702, "name": "rail", "displayName": "Rail", "stackSize": 64}, {"id": 703, "name": "activator_rail", "displayName": "Activator Rail", "stackSize": 64}, {"id": 704, "name": "saddle", "displayName": "Saddle", "stackSize": 1}, {"id": 705, "name": "minecart", "displayName": "Minecart", "stackSize": 1}, {"id": 706, "name": "chest_minecart", "displayName": "Minecart with Chest", "stackSize": 1}, {"id": 707, "name": "furnace_minecart", "displayName": "Minecart with Furnace", "stackSize": 1}, {"id": 708, "name": "tnt_minecart", "displayName": "Minecart with TNT", "stackSize": 1}, {"id": 709, "name": "hopper_minecart", "displayName": "Minecart with <PERSON>", "stackSize": 1}, {"id": 710, "name": "carrot_on_a_stick", "displayName": "Carrot on a Stick", "stackSize": 1, "enchantCategories": ["breakable", "vanishable"], "maxDurability": 25}, {"id": 711, "name": "warped_fungus_on_a_stick", "displayName": "Warped Fungus on a Stick", "stackSize": 1, "enchantCategories": ["breakable", "vanishable"], "maxDurability": 100}, {"id": 712, "name": "elytra", "displayName": "Elytra", "stackSize": 1, "enchantCategories": ["breakable", "wearable", "vanishable"], "repairWith": ["phantom_membrane"], "maxDurability": 432}, {"id": 713, "name": "oak_boat", "displayName": "Oak Boat", "stackSize": 1}, {"id": 714, "name": "oak_chest_boat", "displayName": "Oak Boat with Chest", "stackSize": 1}, {"id": 715, "name": "spruce_boat", "displayName": "Spruce Boat", "stackSize": 1}, {"id": 716, "name": "spruce_chest_boat", "displayName": "Spruce Boat with Chest", "stackSize": 1}, {"id": 717, "name": "birch_boat", "displayName": "<PERSON> Boat", "stackSize": 1}, {"id": 718, "name": "birch_chest_boat", "displayName": "<PERSON> Boat with Chest", "stackSize": 1}, {"id": 719, "name": "jungle_boat", "displayName": "Jungle Boat", "stackSize": 1}, {"id": 720, "name": "jungle_chest_boat", "displayName": "Jungle Boat with Chest", "stackSize": 1}, {"id": 721, "name": "acacia_boat", "displayName": "Acacia Boat", "stackSize": 1}, {"id": 722, "name": "acacia_chest_boat", "displayName": "Acacia Boat with Chest", "stackSize": 1}, {"id": 723, "name": "dark_oak_boat", "displayName": "Dark Oak Boat", "stackSize": 1}, {"id": 724, "name": "dark_oak_chest_boat", "displayName": "Dark Oak Boat with Chest", "stackSize": 1}, {"id": 725, "name": "mangrove_boat", "displayName": "Mangrove Boat", "stackSize": 1}, {"id": 726, "name": "mangrove_chest_boat", "displayName": "Mangrove Boat with Chest", "stackSize": 1}, {"id": 727, "name": "bamboo_raft", "displayName": "Bamboo Raft", "stackSize": 1}, {"id": 728, "name": "bamboo_chest_raft", "displayName": "Bamboo Raft with Chest", "stackSize": 1}, {"id": 729, "name": "structure_block", "displayName": "Structure Block", "stackSize": 64}, {"id": 730, "name": "jigsaw", "displayName": "Jigsaw Block", "stackSize": 64}, {"id": 731, "name": "turtle_helmet", "displayName": "Turtle Shell", "stackSize": 1, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["scute"], "maxDurability": 275}, {"id": 732, "name": "scute", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 733, "name": "flint_and_steel", "displayName": "Flint and Steel", "stackSize": 1, "enchantCategories": ["breakable", "vanishable"], "maxDurability": 64}, {"id": 734, "name": "apple", "displayName": "Apple", "stackSize": 64}, {"id": 735, "name": "bow", "displayName": "Bow", "stackSize": 1, "enchantCategories": ["breakable", "bow", "vanishable"], "maxDurability": 384}, {"id": 736, "name": "arrow", "displayName": "Arrow", "stackSize": 64}, {"id": 737, "name": "coal", "displayName": "Coal", "stackSize": 64}, {"id": 738, "name": "charcoal", "displayName": "Charc<PERSON>l", "stackSize": 64}, {"id": 739, "name": "diamond", "displayName": "Diamond", "stackSize": 64}, {"id": 740, "name": "emerald", "displayName": "Emerald", "stackSize": 64}, {"id": 741, "name": "lapis_lazuli", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 742, "name": "quartz", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 743, "name": "amethyst_shard", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "stackSize": 64}, {"id": 744, "name": "raw_iron", "displayName": "Raw Iron", "stackSize": 64}, {"id": 745, "name": "iron_ingot", "displayName": "Iron Ingot", "stackSize": 64}, {"id": 746, "name": "raw_copper", "displayName": "Raw Copper", "stackSize": 64}, {"id": 747, "name": "copper_ingot", "displayName": "Copper Ingot", "stackSize": 64}, {"id": 748, "name": "raw_gold", "displayName": "Raw Gold", "stackSize": 64}, {"id": 749, "name": "gold_ingot", "displayName": "Gold Ingot", "stackSize": 64}, {"id": 750, "name": "netherite_ingot", "displayName": "Netherite Ingot", "stackSize": 64}, {"id": 751, "name": "netherite_scrap", "displayName": "Netherite Scrap", "stackSize": 64}, {"id": 752, "name": "wooden_sword", "displayName": "Wooden Sword", "stackSize": 1, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "mangrove_planks", "crimson_planks", "warped_planks"], "maxDurability": 59}, {"id": 753, "name": "wooden_shovel", "displayName": "<PERSON><PERSON>", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "mangrove_planks", "crimson_planks", "warped_planks"], "maxDurability": 59}, {"id": 754, "name": "wooden_pickaxe", "displayName": "<PERSON><PERSON> Pick<PERSON>e", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "mangrove_planks", "crimson_planks", "warped_planks"], "maxDurability": 59}, {"id": 755, "name": "wooden_axe", "displayName": "Wooden Axe", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "mangrove_planks", "crimson_planks", "warped_planks"], "maxDurability": 59}, {"id": 756, "name": "wooden_hoe", "displayName": "<PERSON><PERSON>e", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "mangrove_planks", "crimson_planks", "warped_planks"], "maxDurability": 59}, {"id": 757, "name": "stone_sword", "displayName": "Stone Sword", "stackSize": 1, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["cobbled_deepslate", "cobblestone", "blackstone"], "maxDurability": 131}, {"id": 758, "name": "stone_shovel", "displayName": "<PERSON>el", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["cobbled_deepslate", "cobblestone", "blackstone"], "maxDurability": 131}, {"id": 759, "name": "stone_pickaxe", "displayName": "<PERSON>", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["cobbled_deepslate", "cobblestone", "blackstone"], "maxDurability": 131}, {"id": 760, "name": "stone_axe", "displayName": "Stone Axe", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["cobbled_deepslate", "cobblestone", "blackstone"], "maxDurability": 131}, {"id": 761, "name": "stone_hoe", "displayName": "Stone Hoe", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["cobbled_deepslate", "cobblestone", "blackstone"], "maxDurability": 131}, {"id": 762, "name": "golden_sword", "displayName": "Golden Sword", "stackSize": 1, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["gold_ingot"], "maxDurability": 32}, {"id": 763, "name": "golden_shovel", "displayName": "Golden Shovel", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["gold_ingot"], "maxDurability": 32}, {"id": 764, "name": "golden_pickaxe", "displayName": "Golden Pickaxe", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["gold_ingot"], "maxDurability": 32}, {"id": 765, "name": "golden_axe", "displayName": "Golden Axe", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["gold_ingot"], "maxDurability": 32}, {"id": 766, "name": "golden_hoe", "displayName": "Golden Hoe", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["gold_ingot"], "maxDurability": 32}, {"id": 767, "name": "iron_sword", "displayName": "Iron Sword", "stackSize": 1, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 250}, {"id": 768, "name": "iron_shovel", "displayName": "Iron Shovel", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 250}, {"id": 769, "name": "iron_pickaxe", "displayName": "Iron Pickaxe", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 250}, {"id": 770, "name": "iron_axe", "displayName": "Iron Axe", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 250}, {"id": 771, "name": "iron_hoe", "displayName": "Iron Hoe", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 250}, {"id": 772, "name": "diamond_sword", "displayName": "Diamond Sword", "stackSize": 1, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["diamond"], "maxDurability": 1561}, {"id": 773, "name": "diamond_shovel", "displayName": "Diamond Shovel", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["diamond"], "maxDurability": 1561}, {"id": 774, "name": "diamond_pickaxe", "displayName": "Diamond Pickaxe", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["diamond"], "maxDurability": 1561}, {"id": 775, "name": "diamond_axe", "displayName": "Diamond Axe", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["diamond"], "maxDurability": 1561}, {"id": 776, "name": "diamond_hoe", "displayName": "Diamond Hoe", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["diamond"], "maxDurability": 1561}, {"id": 777, "name": "netherite_sword", "displayName": "Netherite Sword", "stackSize": 1, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["netherite_ingot"], "maxDurability": 2031}, {"id": 778, "name": "netherite_shovel", "displayName": "<PERSON><PERSON><PERSON>", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["netherite_ingot"], "maxDurability": 2031}, {"id": 779, "name": "netherite_pickaxe", "displayName": "Netherite Pickaxe", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["netherite_ingot"], "maxDurability": 2031}, {"id": 780, "name": "netherite_axe", "displayName": "Netherite Axe", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["netherite_ingot"], "maxDurability": 2031}, {"id": 781, "name": "netherite_hoe", "displayName": "Netherite Hoe", "stackSize": 1, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["netherite_ingot"], "maxDurability": 2031}, {"id": 782, "name": "stick", "displayName": "Stick", "stackSize": 64}, {"id": 783, "name": "bowl", "displayName": "Bowl", "stackSize": 64}, {"id": 784, "name": "mushroom_stew", "displayName": "Mushroom Stew", "stackSize": 1}, {"id": 785, "name": "string", "displayName": "String", "stackSize": 64}, {"id": 786, "name": "feather", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 787, "name": "gunpowder", "displayName": "Gunpowder", "stackSize": 64}, {"id": 788, "name": "wheat_seeds", "displayName": "Wheat Seeds", "stackSize": 64}, {"id": 789, "name": "wheat", "displayName": "Wheat", "stackSize": 64}, {"id": 790, "name": "bread", "displayName": "Bread", "stackSize": 64}, {"id": 791, "name": "leather_helmet", "displayName": "Leather Cap", "stackSize": 1, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["leather"], "maxDurability": 55}, {"id": 792, "name": "leather_chestplate", "displayName": "<PERSON><PERSON>", "stackSize": 1, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["leather"], "maxDurability": 80}, {"id": 793, "name": "leather_leggings", "displayName": "<PERSON><PERSON>", "stackSize": 1, "enchantCategories": ["armor", "armor_legs", "breakable", "wearable", "vanishable"], "repairWith": ["leather"], "maxDurability": 75}, {"id": 794, "name": "leather_boots", "displayName": "<PERSON><PERSON>", "stackSize": 1, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["leather"], "maxDurability": 65}, {"id": 795, "name": "chainmail_helmet", "displayName": "Chainmail Helmet", "stackSize": 1, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 165}, {"id": 796, "name": "chainmail_chestplate", "displayName": "Chainmail Chestplate", "stackSize": 1, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 240}, {"id": 797, "name": "chainmail_leggings", "displayName": "Chainmail Leggings", "stackSize": 1, "enchantCategories": ["armor", "armor_legs", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 225}, {"id": 798, "name": "chainmail_boots", "displayName": "Chainmail Boots", "stackSize": 1, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 195}, {"id": 799, "name": "iron_helmet", "displayName": "Iron Helmet", "stackSize": 1, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 165}, {"id": 800, "name": "iron_chestplate", "displayName": "Iron Chestplate", "stackSize": 1, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 240}, {"id": 801, "name": "iron_leggings", "displayName": "Iron Leggings", "stackSize": 1, "enchantCategories": ["armor", "armor_legs", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 225}, {"id": 802, "name": "iron_boots", "displayName": "Iron Boots", "stackSize": 1, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"], "maxDurability": 195}, {"id": 803, "name": "diamond_helmet", "displayName": "Diamond Helmet", "stackSize": 1, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["diamond"], "maxDurability": 363}, {"id": 804, "name": "diamond_chestplate", "displayName": "Diamond Chestplate", "stackSize": 1, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["diamond"], "maxDurability": 528}, {"id": 805, "name": "diamond_leggings", "displayName": "Diamond Leggings", "stackSize": 1, "enchantCategories": ["armor", "armor_legs", "breakable", "wearable", "vanishable"], "repairWith": ["diamond"], "maxDurability": 495}, {"id": 806, "name": "diamond_boots", "displayName": "Diamond Boots", "stackSize": 1, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["diamond"], "maxDurability": 429}, {"id": 807, "name": "golden_helmet", "displayName": "Golden Helmet", "stackSize": 1, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["gold_ingot"], "maxDurability": 77}, {"id": 808, "name": "golden_chestplate", "displayName": "Golden Chestplate", "stackSize": 1, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["gold_ingot"], "maxDurability": 112}, {"id": 809, "name": "golden_leggings", "displayName": "Golden Leggings", "stackSize": 1, "enchantCategories": ["armor", "armor_legs", "breakable", "wearable", "vanishable"], "repairWith": ["gold_ingot"], "maxDurability": 105}, {"id": 810, "name": "golden_boots", "displayName": "Golden Boots", "stackSize": 1, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["gold_ingot"], "maxDurability": 91}, {"id": 811, "name": "netherite_helmet", "displayName": "Netherite Helmet", "stackSize": 1, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["netherite_ingot"], "maxDurability": 407}, {"id": 812, "name": "netherite_chestplate", "displayName": "Netherite Chestplate", "stackSize": 1, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["netherite_ingot"], "maxDurability": 592}, {"id": 813, "name": "netherite_leggings", "displayName": "Netherite Leggings", "stackSize": 1, "enchantCategories": ["armor", "armor_legs", "breakable", "wearable", "vanishable"], "repairWith": ["netherite_ingot"], "maxDurability": 555}, {"id": 814, "name": "netherite_boots", "displayName": "Netherite Boots", "stackSize": 1, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["netherite_ingot"], "maxDurability": 481}, {"id": 815, "name": "flint", "displayName": "Flint", "stackSize": 64}, {"id": 816, "name": "porkchop", "displayName": "Raw Porkchop", "stackSize": 64}, {"id": 817, "name": "cooked_porkchop", "displayName": "Cooked Porkchop", "stackSize": 64}, {"id": 818, "name": "painting", "displayName": "Painting", "stackSize": 64}, {"id": 819, "name": "golden_apple", "displayName": "Golden Apple", "stackSize": 64}, {"id": 820, "name": "enchanted_golden_apple", "displayName": "Enchanted Golden Apple", "stackSize": 64}, {"id": 821, "name": "oak_sign", "displayName": "Oak Sign", "stackSize": 16}, {"id": 822, "name": "spruce_sign", "displayName": "Spruce Sign", "stackSize": 16}, {"id": 823, "name": "birch_sign", "displayName": "Birch Sign", "stackSize": 16}, {"id": 824, "name": "jungle_sign", "displayName": "Jungle Sign", "stackSize": 16}, {"id": 825, "name": "acacia_sign", "displayName": "Acacia Sign", "stackSize": 16}, {"id": 826, "name": "dark_oak_sign", "displayName": "Dark Oak Sign", "stackSize": 16}, {"id": 827, "name": "mangrove_sign", "displayName": "Mangrove Sign", "stackSize": 16}, {"id": 828, "name": "bamboo_sign", "displayName": "Bamboo Sign", "stackSize": 16}, {"id": 829, "name": "crimson_sign", "displayName": "Crimson Sign", "stackSize": 16}, {"id": 830, "name": "warped_sign", "displayName": "Warped Sign", "stackSize": 16}, {"id": 831, "name": "oak_hanging_sign", "displayName": "Oak Hanging Sign", "stackSize": 16}, {"id": 832, "name": "spruce_hanging_sign", "displayName": "Spruce Hanging Sign", "stackSize": 16}, {"id": 833, "name": "birch_hanging_sign", "displayName": "<PERSON> Hanging Sign", "stackSize": 16}, {"id": 834, "name": "jungle_hanging_sign", "displayName": "Jungle Hanging Sign", "stackSize": 16}, {"id": 835, "name": "acacia_hanging_sign", "displayName": "Acacia Hanging Sign", "stackSize": 16}, {"id": 836, "name": "dark_oak_hanging_sign", "displayName": "Dark Oak Hanging Sign", "stackSize": 16}, {"id": 837, "name": "mangrove_hanging_sign", "displayName": "Mangrove Hanging Sign", "stackSize": 16}, {"id": 838, "name": "bamboo_hanging_sign", "displayName": "Bamboo Hanging Sign", "stackSize": 16}, {"id": 839, "name": "crimson_hanging_sign", "displayName": "Crimson Hanging Sign", "stackSize": 16}, {"id": 840, "name": "warped_hanging_sign", "displayName": "Warped Hanging Sign", "stackSize": 16}, {"id": 841, "name": "bucket", "displayName": "Bucket", "stackSize": 16}, {"id": 842, "name": "water_bucket", "displayName": "Water Bucket", "stackSize": 1}, {"id": 843, "name": "lava_bucket", "displayName": "<PERSON><PERSON>et", "stackSize": 1}, {"id": 844, "name": "powder_snow_bucket", "displayName": "Powder Snow Bucket", "stackSize": 1}, {"id": 845, "name": "snowball", "displayName": "Snowball", "stackSize": 16}, {"id": 846, "name": "leather", "displayName": "Leather", "stackSize": 64}, {"id": 847, "name": "milk_bucket", "displayName": "Milk Bucket", "stackSize": 1}, {"id": 848, "name": "pufferfish_bucket", "displayName": "Bucket of Pufferfish", "stackSize": 1}, {"id": 849, "name": "salmon_bucket", "displayName": "Bucket of Salmon", "stackSize": 1}, {"id": 850, "name": "cod_bucket", "displayName": "Bucket of Cod", "stackSize": 1}, {"id": 851, "name": "tropical_fish_bucket", "displayName": "Bucket of Tropical Fish", "stackSize": 1}, {"id": 852, "name": "axolotl_bucket", "displayName": "Bucket of Axolotl", "stackSize": 1}, {"id": 853, "name": "tadpole_bucket", "displayName": "Bucket of Tadpole", "stackSize": 1}, {"id": 854, "name": "brick", "displayName": "Brick", "stackSize": 64}, {"id": 855, "name": "clay_ball", "displayName": "<PERSON>", "stackSize": 64}, {"id": 856, "name": "dried_kelp_block", "displayName": "Dried Kelp Block", "stackSize": 64}, {"id": 857, "name": "paper", "displayName": "Paper", "stackSize": 64}, {"id": 858, "name": "book", "displayName": "Book", "stackSize": 64}, {"id": 859, "name": "slime_ball", "displayName": "Slimeball", "stackSize": 64}, {"id": 860, "name": "egg", "displayName": "Egg", "stackSize": 16}, {"id": 861, "name": "compass", "displayName": "<PERSON>mp<PERSON>", "stackSize": 64, "enchantCategories": ["vanishable"]}, {"id": 862, "name": "recovery_compass", "displayName": "Recovery Compass", "stackSize": 64}, {"id": 863, "name": "bundle", "displayName": "Bundle", "stackSize": 1}, {"id": 864, "name": "fishing_rod", "displayName": "Fishing Rod", "stackSize": 1, "enchantCategories": ["fishing_rod", "breakable", "vanishable"], "maxDurability": 64}, {"id": 865, "name": "clock", "displayName": "Clock", "stackSize": 64}, {"id": 866, "name": "spyglass", "displayName": "Spyglass", "stackSize": 1}, {"id": 867, "name": "glowstone_dust", "displayName": "Glowstone Dust", "stackSize": 64}, {"id": 868, "name": "cod", "displayName": "Raw Cod", "stackSize": 64}, {"id": 869, "name": "salmon", "displayName": "Raw Salmon", "stackSize": 64}, {"id": 870, "name": "tropical_fish", "displayName": "Tropical Fish", "stackSize": 64}, {"id": 871, "name": "pufferfish", "displayName": "Pufferfish", "stackSize": 64}, {"id": 872, "name": "cooked_cod", "displayName": "Cooked Cod", "stackSize": 64}, {"id": 873, "name": "cooked_salmon", "displayName": "Cooked Salmon", "stackSize": 64}, {"id": 874, "name": "ink_sac", "displayName": "Ink Sac", "stackSize": 64}, {"id": 875, "name": "glow_ink_sac", "displayName": "Glow Ink Sac", "stackSize": 64}, {"id": 876, "name": "cocoa_beans", "displayName": "Cocoa Beans", "stackSize": 64}, {"id": 877, "name": "white_dye", "displayName": "White Dye", "stackSize": 64}, {"id": 878, "name": "orange_dye", "displayName": "Orange Dye", "stackSize": 64}, {"id": 879, "name": "magenta_dye", "displayName": "<PERSON><PERSON><PERSON>", "stackSize": 64}, {"id": 880, "name": "light_blue_dye", "displayName": "Light Blue Dye", "stackSize": 64}, {"id": 881, "name": "yellow_dye", "displayName": "Yellow Dye", "stackSize": 64}, {"id": 882, "name": "lime_dye", "displayName": "Lime Dye", "stackSize": 64}, {"id": 883, "name": "pink_dye", "displayName": "Pink Dye", "stackSize": 64}, {"id": 884, "name": "gray_dye", "displayName": "<PERSON>", "stackSize": 64}, {"id": 885, "name": "light_gray_dye", "displayName": "Light Gray D<PERSON>", "stackSize": 64}, {"id": 886, "name": "cyan_dye", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 887, "name": "purple_dye", "displayName": "Purple Dye", "stackSize": 64}, {"id": 888, "name": "blue_dye", "displayName": "Blue Dye", "stackSize": 64}, {"id": 889, "name": "brown_dye", "displayName": "<PERSON>", "stackSize": 64}, {"id": 890, "name": "green_dye", "displayName": "Green Dye", "stackSize": 64}, {"id": 891, "name": "red_dye", "displayName": "Red Dye", "stackSize": 64}, {"id": 892, "name": "black_dye", "displayName": "Black Dye", "stackSize": 64}, {"id": 893, "name": "bone_meal", "displayName": "<PERSON>", "stackSize": 64}, {"id": 894, "name": "bone", "displayName": "Bone", "stackSize": 64}, {"id": 895, "name": "sugar", "displayName": "Sugar", "stackSize": 64}, {"id": 896, "name": "cake", "displayName": "Cake", "stackSize": 1}, {"id": 897, "name": "white_bed", "displayName": "White Bed", "stackSize": 1}, {"id": 898, "name": "orange_bed", "displayName": "Orange Bed", "stackSize": 1}, {"id": 899, "name": "magenta_bed", "displayName": "Magenta Bed", "stackSize": 1}, {"id": 900, "name": "light_blue_bed", "displayName": "Light Blue Bed", "stackSize": 1}, {"id": 901, "name": "yellow_bed", "displayName": "Yellow Bed", "stackSize": 1}, {"id": 902, "name": "lime_bed", "displayName": "Lime Bed", "stackSize": 1}, {"id": 903, "name": "pink_bed", "displayName": "Pink Bed", "stackSize": 1}, {"id": 904, "name": "gray_bed", "displayName": "Gray Bed", "stackSize": 1}, {"id": 905, "name": "light_gray_bed", "displayName": "Light Gray Bed", "stackSize": 1}, {"id": 906, "name": "cyan_bed", "displayName": "<PERSON><PERSON>", "stackSize": 1}, {"id": 907, "name": "purple_bed", "displayName": "Purple Bed", "stackSize": 1}, {"id": 908, "name": "blue_bed", "displayName": "Blue Bed", "stackSize": 1}, {"id": 909, "name": "brown_bed", "displayName": "Brown Bed", "stackSize": 1}, {"id": 910, "name": "green_bed", "displayName": "Green Bed", "stackSize": 1}, {"id": 911, "name": "red_bed", "displayName": "Red Bed", "stackSize": 1}, {"id": 912, "name": "black_bed", "displayName": "Black Bed", "stackSize": 1}, {"id": 913, "name": "cookie", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 914, "name": "filled_map", "displayName": "Map", "stackSize": 64}, {"id": 915, "name": "shears", "displayName": "Shears", "stackSize": 1, "enchantCategories": ["breakable", "vanishable"], "maxDurability": 238}, {"id": 916, "name": "melon_slice", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 917, "name": "dried_kelp", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 918, "name": "pumpkin_seeds", "displayName": "<PERSON><PERSON><PERSON> Seeds", "stackSize": 64}, {"id": 919, "name": "melon_seeds", "displayName": "<PERSON>on Seeds", "stackSize": 64}, {"id": 920, "name": "beef", "displayName": "Raw Beef", "stackSize": 64}, {"id": 921, "name": "cooked_beef", "displayName": "Steak", "stackSize": 64}, {"id": 922, "name": "chicken", "displayName": "Raw Chicken", "stackSize": 64}, {"id": 923, "name": "cooked_chicken", "displayName": "Cooked Chicken", "stackSize": 64}, {"id": 924, "name": "rotten_flesh", "displayName": "Rotten Flesh", "stackSize": 64}, {"id": 925, "name": "ender_pearl", "displayName": "<PERSON><PERSON>", "stackSize": 16}, {"id": 926, "name": "blaze_rod", "displayName": "<PERSON>", "stackSize": 64}, {"id": 927, "name": "ghast_tear", "displayName": "Ghast Tear", "stackSize": 64}, {"id": 928, "name": "gold_nugget", "displayName": "Gold Nugget", "stackSize": 64}, {"id": 929, "name": "nether_wart", "displayName": "Nether Wart", "stackSize": 64}, {"id": 930, "name": "potion", "displayName": "Potion", "stackSize": 1}, {"id": 931, "name": "glass_bottle", "displayName": "Glass Bottle", "stackSize": 64}, {"id": 932, "name": "spider_eye", "displayName": "Spider Eye", "stackSize": 64}, {"id": 933, "name": "fermented_spider_eye", "displayName": "Fermented Spider Eye", "stackSize": 64}, {"id": 934, "name": "blaze_powder", "displayName": "<PERSON>", "stackSize": 64}, {"id": 935, "name": "magma_cream", "displayName": "Magma Cream", "stackSize": 64}, {"id": 936, "name": "brewing_stand", "displayName": "Brewing Stand", "stackSize": 64}, {"id": 937, "name": "cauldron", "displayName": "<PERSON><PERSON><PERSON>", "stackSize": 64}, {"id": 938, "name": "ender_eye", "displayName": "Eye of <PERSON>er", "stackSize": 64}, {"id": 939, "name": "glistering_melon_slice", "displayName": "Glistering <PERSON><PERSON>", "stackSize": 64}, {"id": 940, "name": "allay_spawn_egg", "displayName": "Allay Spawn Egg", "stackSize": 64}, {"id": 941, "name": "axolotl_spawn_egg", "displayName": "Axolotl Spawn Egg", "stackSize": 64}, {"id": 942, "name": "bat_spawn_egg", "displayName": "Bat Spawn Egg", "stackSize": 64}, {"id": 943, "name": "bee_spawn_egg", "displayName": "Bee Spawn Egg", "stackSize": 64}, {"id": 944, "name": "blaze_spawn_egg", "displayName": "Blaze Spawn Egg", "stackSize": 64}, {"id": 945, "name": "cat_spawn_egg", "displayName": "Cat Spawn Egg", "stackSize": 64}, {"id": 946, "name": "camel_spawn_egg", "displayName": "Camel Spawn Egg", "stackSize": 64}, {"id": 947, "name": "cave_spider_spawn_egg", "displayName": "Cave Spider Spawn Egg", "stackSize": 64}, {"id": 948, "name": "chicken_spawn_egg", "displayName": "Chicken Spawn Egg", "stackSize": 64}, {"id": 949, "name": "cod_spawn_egg", "displayName": "Cod Spawn Egg", "stackSize": 64}, {"id": 950, "name": "cow_spawn_egg", "displayName": "Cow Spawn Egg", "stackSize": 64}, {"id": 951, "name": "creeper_spawn_egg", "displayName": "Creeper Spawn Egg", "stackSize": 64}, {"id": 952, "name": "dolphin_spawn_egg", "displayName": "Dolphin Spawn Egg", "stackSize": 64}, {"id": 953, "name": "donkey_spawn_egg", "displayName": "Donkey Spawn Egg", "stackSize": 64}, {"id": 954, "name": "drowned_spawn_egg", "displayName": "Drowned Spawn Egg", "stackSize": 64}, {"id": 955, "name": "elder_guardian_spawn_egg", "displayName": "Elder Guardian Spawn Egg", "stackSize": 64}, {"id": 956, "name": "ender_dragon_spawn_egg", "displayName": "Ender Dragon Spawn Egg", "stackSize": 64}, {"id": 957, "name": "enderman_spawn_egg", "displayName": "Enderman Spawn Egg", "stackSize": 64}, {"id": 958, "name": "endermite_spawn_egg", "displayName": "Endermite Spawn Egg", "stackSize": 64}, {"id": 959, "name": "evoker_spawn_egg", "displayName": "Evoker Spawn Egg", "stackSize": 64}, {"id": 960, "name": "fox_spawn_egg", "displayName": "Fox Spawn Egg", "stackSize": 64}, {"id": 961, "name": "frog_spawn_egg", "displayName": "Frog Spawn Egg", "stackSize": 64}, {"id": 962, "name": "ghast_spawn_egg", "displayName": "Ghast Spawn Egg", "stackSize": 64}, {"id": 963, "name": "glow_squid_spawn_egg", "displayName": "Glow Squid Spawn Egg", "stackSize": 64}, {"id": 964, "name": "goat_spawn_egg", "displayName": "Goat Spawn Egg", "stackSize": 64}, {"id": 965, "name": "guardian_spawn_egg", "displayName": "Guardian Spawn Egg", "stackSize": 64}, {"id": 966, "name": "hoglin_spawn_egg", "displayName": "Hoglin Spawn Egg", "stackSize": 64}, {"id": 967, "name": "horse_spawn_egg", "displayName": "Horse Spawn Egg", "stackSize": 64}, {"id": 968, "name": "husk_spawn_egg", "displayName": "Husk Spawn Egg", "stackSize": 64}, {"id": 969, "name": "iron_golem_spawn_egg", "displayName": "Iron Golem Spawn Egg", "stackSize": 64}, {"id": 970, "name": "llama_spawn_egg", "displayName": "Llama Spawn Egg", "stackSize": 64}, {"id": 971, "name": "magma_cube_spawn_egg", "displayName": "Magma Cube Spawn Egg", "stackSize": 64}, {"id": 972, "name": "mooshroom_spawn_egg", "displayName": "Mooshroom Spawn Egg", "stackSize": 64}, {"id": 973, "name": "mule_spawn_egg", "displayName": "Mule Spawn Egg", "stackSize": 64}, {"id": 974, "name": "ocelot_spawn_egg", "displayName": "Ocelot Spawn Egg", "stackSize": 64}, {"id": 975, "name": "panda_spawn_egg", "displayName": "Panda Spawn Egg", "stackSize": 64}, {"id": 976, "name": "parrot_spawn_egg", "displayName": "Parrot Spawn Egg", "stackSize": 64}, {"id": 977, "name": "phantom_spawn_egg", "displayName": "Phantom Spawn Egg", "stackSize": 64}, {"id": 978, "name": "pig_spawn_egg", "displayName": "Pig Spawn Egg", "stackSize": 64}, {"id": 979, "name": "piglin_spawn_egg", "displayName": "Piglin Spawn Egg", "stackSize": 64}, {"id": 980, "name": "piglin_brute_spawn_egg", "displayName": "Piglin Brute Spawn Egg", "stackSize": 64}, {"id": 981, "name": "pillager_spawn_egg", "displayName": "Pillager Spawn Egg", "stackSize": 64}, {"id": 982, "name": "polar_bear_spawn_egg", "displayName": "Polar Bear Spawn Egg", "stackSize": 64}, {"id": 983, "name": "pufferfish_spawn_egg", "displayName": "Pufferfish Spawn Egg", "stackSize": 64}, {"id": 984, "name": "rabbit_spawn_egg", "displayName": "Rabbit Spawn Egg", "stackSize": 64}, {"id": 985, "name": "ravager_spawn_egg", "displayName": "Ravager Spawn Egg", "stackSize": 64}, {"id": 986, "name": "salmon_spawn_egg", "displayName": "Salmon Spawn Egg", "stackSize": 64}, {"id": 987, "name": "sheep_spawn_egg", "displayName": "Sheep Spawn Egg", "stackSize": 64}, {"id": 988, "name": "shulker_spawn_egg", "displayName": "Shulker Spawn Egg", "stackSize": 64}, {"id": 989, "name": "silverfish_spawn_egg", "displayName": "Silverfish Spawn Egg", "stackSize": 64}, {"id": 990, "name": "skeleton_spawn_egg", "displayName": "Skeleton Spawn Egg", "stackSize": 64}, {"id": 991, "name": "skeleton_horse_spawn_egg", "displayName": "Skeleton Horse Spawn Egg", "stackSize": 64}, {"id": 992, "name": "slime_spawn_egg", "displayName": "Slime Spawn Egg", "stackSize": 64}, {"id": 993, "name": "snow_golem_spawn_egg", "displayName": "Snow Golem Spawn Egg", "stackSize": 64}, {"id": 994, "name": "spider_spawn_egg", "displayName": "Spider Spawn Egg", "stackSize": 64}, {"id": 995, "name": "squid_spawn_egg", "displayName": "Squid Spawn Egg", "stackSize": 64}, {"id": 996, "name": "stray_spawn_egg", "displayName": "Stray Spawn Egg", "stackSize": 64}, {"id": 997, "name": "strider_spawn_egg", "displayName": "Strider Spawn Egg", "stackSize": 64}, {"id": 998, "name": "tadpole_spawn_egg", "displayName": "Tadpole Spawn Egg", "stackSize": 64}, {"id": 999, "name": "trader_llama_spawn_egg", "displayName": "Trader <PERSON>lama Spawn Egg", "stackSize": 64}, {"id": 1000, "name": "tropical_fish_spawn_egg", "displayName": "Tropical Fish Spawn Egg", "stackSize": 64}, {"id": 1001, "name": "turtle_spawn_egg", "displayName": "Turtle Spawn Egg", "stackSize": 64}, {"id": 1002, "name": "vex_spawn_egg", "displayName": "Vex Spawn Egg", "stackSize": 64}, {"id": 1003, "name": "villager_spawn_egg", "displayName": "Villager Spawn Egg", "stackSize": 64}, {"id": 1004, "name": "vindicator_spawn_egg", "displayName": "Vindicator Spawn Egg", "stackSize": 64}, {"id": 1005, "name": "wandering_trader_spawn_egg", "displayName": "Wandering Trader Spawn Egg", "stackSize": 64}, {"id": 1006, "name": "warden_spawn_egg", "displayName": "Warden Spawn Egg", "stackSize": 64}, {"id": 1007, "name": "witch_spawn_egg", "displayName": "Witch Spawn Egg", "stackSize": 64}, {"id": 1008, "name": "wither_spawn_egg", "displayName": "Wither Spawn Egg", "stackSize": 64}, {"id": 1009, "name": "wither_skeleton_spawn_egg", "displayName": "Wither Skeleton Spawn Egg", "stackSize": 64}, {"id": 1010, "name": "wolf_spawn_egg", "displayName": "Wolf Spawn Egg", "stackSize": 64}, {"id": 1011, "name": "zoglin_spawn_egg", "displayName": "Zoglin Spawn Egg", "stackSize": 64}, {"id": 1012, "name": "zombie_spawn_egg", "displayName": "Zombie Spawn Egg", "stackSize": 64}, {"id": 1013, "name": "zombie_horse_spawn_egg", "displayName": "Zombie Horse Spawn Egg", "stackSize": 64}, {"id": 1014, "name": "zombie_villager_spawn_egg", "displayName": "Zombie Villager Spawn Egg", "stackSize": 64}, {"id": 1015, "name": "zombified_piglin_spawn_egg", "displayName": "Zombified Piglin Spawn Egg", "stackSize": 64}, {"id": 1016, "name": "experience_bottle", "displayName": "Bottle o' Enchanting", "stackSize": 64}, {"id": 1017, "name": "fire_charge", "displayName": "Fire Charge", "stackSize": 64}, {"id": 1018, "name": "writable_book", "displayName": "Book and Quill", "stackSize": 1}, {"id": 1019, "name": "written_book", "displayName": "Written Book", "stackSize": 16}, {"id": 1020, "name": "item_frame", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 1021, "name": "glow_item_frame", "displayName": "G<PERSON> Item <PERSON>", "stackSize": 64}, {"id": 1022, "name": "flower_pot", "displayName": "Flower Pot", "stackSize": 64}, {"id": 1023, "name": "carrot", "displayName": "Carrot", "stackSize": 64}, {"id": 1024, "name": "potato", "displayName": "Potato", "stackSize": 64}, {"id": 1025, "name": "baked_potato", "displayName": "Baked Potato", "stackSize": 64}, {"id": 1026, "name": "poisonous_potato", "displayName": "Poisonous Potato", "stackSize": 64}, {"id": 1027, "name": "map", "displayName": "Empty Map", "stackSize": 64}, {"id": 1028, "name": "golden_carrot", "displayName": "Golden Carrot", "stackSize": 64}, {"id": 1029, "name": "skeleton_skull", "displayName": "Skeleton Skull", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"id": 1030, "name": "wither_skeleton_skull", "displayName": "Wither Skeleton Skull", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"id": 1031, "name": "player_head", "displayName": "Player Head", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"id": 1032, "name": "zombie_head", "displayName": "Zombie Head", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"id": 1033, "name": "creeper_head", "displayName": "Creeper Head", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"id": 1034, "name": "dragon_head", "displayName": "Dragon Head", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"id": 1035, "name": "piglin_head", "displayName": "<PERSON><PERSON>", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"id": 1036, "name": "nether_star", "displayName": "Nether Star", "stackSize": 64}, {"id": 1037, "name": "pumpkin_pie", "displayName": "Pumpkin Pie", "stackSize": 64}, {"id": 1038, "name": "firework_rocket", "displayName": "Firework Rocket", "stackSize": 64}, {"id": 1039, "name": "firework_star", "displayName": "Firework Star", "stackSize": 64}, {"id": 1040, "name": "enchanted_book", "displayName": "Enchanted Book", "stackSize": 1}, {"id": 1041, "name": "nether_brick", "displayName": "Nether Brick", "stackSize": 64}, {"id": 1042, "name": "prismarine_shard", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "stackSize": 64}, {"id": 1043, "name": "prismarine_crystals", "displayName": "Prismarine Crystals", "stackSize": 64}, {"id": 1044, "name": "rabbit", "displayName": "Raw Rabbit", "stackSize": 64}, {"id": 1045, "name": "cooked_rabbit", "displayName": "Cooked Rabbit", "stackSize": 64}, {"id": 1046, "name": "rabbit_stew", "displayName": "Rabbit Stew", "stackSize": 1}, {"id": 1047, "name": "rabbit_foot", "displayName": "<PERSON>'s Foot", "stackSize": 64}, {"id": 1048, "name": "rabbit_hide", "displayName": "<PERSON>", "stackSize": 64}, {"id": 1049, "name": "armor_stand", "displayName": "Armor Stand", "stackSize": 16}, {"id": 1050, "name": "iron_horse_armor", "displayName": "Iron Horse Armor", "stackSize": 1}, {"id": 1051, "name": "golden_horse_armor", "displayName": "Golden Horse Armor", "stackSize": 1}, {"id": 1052, "name": "diamond_horse_armor", "displayName": "Diamond Horse Armor", "stackSize": 1}, {"id": 1053, "name": "leather_horse_armor", "displayName": "Leather Horse Armor", "stackSize": 1}, {"id": 1054, "name": "lead", "displayName": "Lead", "stackSize": 64}, {"id": 1055, "name": "name_tag", "displayName": "Name Tag", "stackSize": 64}, {"id": 1056, "name": "command_block_minecart", "displayName": "Minecart with Command Block", "stackSize": 1}, {"id": 1057, "name": "mutton", "displayName": "<PERSON>", "stackSize": 64}, {"id": 1058, "name": "cooked_mutton", "displayName": "Cooked <PERSON>tton", "stackSize": 64}, {"id": 1059, "name": "white_banner", "displayName": "White Banner", "stackSize": 16}, {"id": 1060, "name": "orange_banner", "displayName": "Orange Banner", "stackSize": 16}, {"id": 1061, "name": "magenta_banner", "displayName": "Magenta Banner", "stackSize": 16}, {"id": 1062, "name": "light_blue_banner", "displayName": "Light Blue Banner", "stackSize": 16}, {"id": 1063, "name": "yellow_banner", "displayName": "Yellow Banner", "stackSize": 16}, {"id": 1064, "name": "lime_banner", "displayName": "Lime Banner", "stackSize": 16}, {"id": 1065, "name": "pink_banner", "displayName": "Pink Banner", "stackSize": 16}, {"id": 1066, "name": "gray_banner", "displayName": "<PERSON>", "stackSize": 16}, {"id": 1067, "name": "light_gray_banner", "displayName": "<PERSON> Gray Banner", "stackSize": 16}, {"id": 1068, "name": "cyan_banner", "displayName": "<PERSON><PERSON>", "stackSize": 16}, {"id": 1069, "name": "purple_banner", "displayName": "<PERSON> Banner", "stackSize": 16}, {"id": 1070, "name": "blue_banner", "displayName": "Blue Banner", "stackSize": 16}, {"id": 1071, "name": "brown_banner", "displayName": "<PERSON>", "stackSize": 16}, {"id": 1072, "name": "green_banner", "displayName": "<PERSON> Banner", "stackSize": 16}, {"id": 1073, "name": "red_banner", "displayName": "Red Banner", "stackSize": 16}, {"id": 1074, "name": "black_banner", "displayName": "Black Banner", "stackSize": 16}, {"id": 1075, "name": "end_crystal", "displayName": "End Crystal", "stackSize": 64}, {"id": 1076, "name": "chorus_fruit", "displayName": "Chorus Fruit", "stackSize": 64}, {"id": 1077, "name": "popped_chorus_fruit", "displayName": "Popped Chorus Fruit", "stackSize": 64}, {"id": 1078, "name": "beetroot", "displayName": "Beetroot", "stackSize": 64}, {"id": 1079, "name": "beetroot_seeds", "displayName": "Beetroot Seeds", "stackSize": 64}, {"id": 1080, "name": "beetroot_soup", "displayName": "Beetroot Soup", "stackSize": 1}, {"id": 1081, "name": "dragon_breath", "displayName": "Dragon's Breath", "stackSize": 64}, {"id": 1082, "name": "splash_potion", "displayName": "Splash Potion", "stackSize": 1}, {"id": 1083, "name": "spectral_arrow", "displayName": "Spectral Arrow", "stackSize": 64}, {"id": 1084, "name": "tipped_arrow", "displayName": "Tipped Arrow", "stackSize": 64}, {"id": 1085, "name": "lingering_potion", "displayName": "Lingering Potion", "stackSize": 1}, {"id": 1086, "name": "shield", "displayName": "Shield", "stackSize": 1, "enchantCategories": ["breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "mangrove_planks", "crimson_planks", "warped_planks"], "maxDurability": 336}, {"id": 1087, "name": "totem_of_undying", "displayName": "Totem of Undying", "stackSize": 1}, {"id": 1088, "name": "shulker_shell", "displayName": "Shulker Shell", "stackSize": 64}, {"id": 1089, "name": "iron_nugget", "displayName": "Iron Nugget", "stackSize": 64}, {"id": 1090, "name": "knowledge_book", "displayName": "Knowledge Book", "stackSize": 1}, {"id": 1091, "name": "debug_stick", "displayName": "Debug Stick", "stackSize": 1}, {"id": 1092, "name": "music_disc_13", "displayName": "Music Disc", "stackSize": 1}, {"id": 1093, "name": "music_disc_cat", "displayName": "Music Disc", "stackSize": 1}, {"id": 1094, "name": "music_disc_blocks", "displayName": "Music Disc", "stackSize": 1}, {"id": 1095, "name": "music_disc_chirp", "displayName": "Music Disc", "stackSize": 1}, {"id": 1096, "name": "music_disc_far", "displayName": "Music Disc", "stackSize": 1}, {"id": 1097, "name": "music_disc_mall", "displayName": "Music Disc", "stackSize": 1}, {"id": 1098, "name": "music_disc_mellohi", "displayName": "Music Disc", "stackSize": 1}, {"id": 1099, "name": "music_disc_stal", "displayName": "Music Disc", "stackSize": 1}, {"id": 1100, "name": "music_disc_strad", "displayName": "Music Disc", "stackSize": 1}, {"id": 1101, "name": "music_disc_ward", "displayName": "Music Disc", "stackSize": 1}, {"id": 1102, "name": "music_disc_11", "displayName": "Music Disc", "stackSize": 1}, {"id": 1103, "name": "music_disc_wait", "displayName": "Music Disc", "stackSize": 1}, {"id": 1104, "name": "music_disc_otherside", "displayName": "Music Disc", "stackSize": 1}, {"id": 1105, "name": "music_disc_5", "displayName": "Music Disc", "stackSize": 1}, {"id": 1106, "name": "music_disc_pigstep", "displayName": "Music Disc", "stackSize": 1}, {"id": 1107, "name": "disc_fragment_5", "displayName": "Disc Fragment", "stackSize": 64}, {"id": 1108, "name": "trident", "displayName": "Trident", "stackSize": 1, "enchantCategories": ["trident", "breakable", "vanishable"], "maxDurability": 250}, {"id": 1109, "name": "phantom_membrane", "displayName": "Phantom Membrane", "stackSize": 64}, {"id": 1110, "name": "nautilus_shell", "displayName": "Nautilus Shell", "stackSize": 64}, {"id": 1111, "name": "heart_of_the_sea", "displayName": "Heart of the Sea", "stackSize": 64}, {"id": 1112, "name": "crossbow", "displayName": "Crossbow", "stackSize": 1, "enchantCategories": ["breakable", "crossbow", "vanishable"], "maxDurability": 465}, {"id": 1113, "name": "suspicious_stew", "displayName": "Suspicious Stew", "stackSize": 1}, {"id": 1114, "name": "loom", "displayName": "Loom", "stackSize": 64}, {"id": 1115, "name": "flower_banner_pattern", "displayName": "<PERSON>", "stackSize": 1}, {"id": 1116, "name": "creeper_banner_pattern", "displayName": "<PERSON>", "stackSize": 1}, {"id": 1117, "name": "skull_banner_pattern", "displayName": "<PERSON>", "stackSize": 1}, {"id": 1118, "name": "mojang_banner_pattern", "displayName": "<PERSON>", "stackSize": 1}, {"id": 1119, "name": "globe_banner_pattern", "displayName": "<PERSON>", "stackSize": 1}, {"id": 1120, "name": "piglin_banner_pattern", "displayName": "<PERSON>", "stackSize": 1}, {"id": 1121, "name": "goat_horn", "displayName": "<PERSON><PERSON>", "stackSize": 1}, {"id": 1122, "name": "composter", "displayName": "Composter", "stackSize": 64}, {"id": 1123, "name": "barrel", "displayName": "Barrel", "stackSize": 64}, {"id": 1124, "name": "smoker", "displayName": "Smoker", "stackSize": 64}, {"id": 1125, "name": "blast_furnace", "displayName": "Blast Furnace", "stackSize": 64}, {"id": 1126, "name": "cartography_table", "displayName": "Cartography Table", "stackSize": 64}, {"id": 1127, "name": "fletching_table", "displayName": "Fletching Table", "stackSize": 64}, {"id": 1128, "name": "grindstone", "displayName": "Grindstone", "stackSize": 64}, {"id": 1129, "name": "smithing_table", "displayName": "Smithing Table", "stackSize": 64}, {"id": 1130, "name": "stonecutter", "displayName": "<PERSON><PERSON><PERSON>", "stackSize": 64}, {"id": 1131, "name": "bell", "displayName": "Bell", "stackSize": 64}, {"id": 1132, "name": "lantern", "displayName": "Lantern", "stackSize": 64}, {"id": 1133, "name": "soul_lantern", "displayName": "Soul Lantern", "stackSize": 64}, {"id": 1134, "name": "sweet_berries", "displayName": "Sweet Berries", "stackSize": 64}, {"id": 1135, "name": "glow_berries", "displayName": "Glow Berries", "stackSize": 64}, {"id": 1136, "name": "campfire", "displayName": "Campfire", "stackSize": 64}, {"id": 1137, "name": "soul_campfire", "displayName": "Soul Campfire", "stackSize": 64}, {"id": 1138, "name": "shroomlight", "displayName": "Shroomlight", "stackSize": 64}, {"id": 1139, "name": "honeycomb", "displayName": "Honeycomb", "stackSize": 64}, {"id": 1140, "name": "bee_nest", "displayName": "Bee Nest", "stackSize": 64}, {"id": 1141, "name": "beehive", "displayName": "Beehive", "stackSize": 64}, {"id": 1142, "name": "honey_bottle", "displayName": "<PERSON>", "stackSize": 16}, {"id": 1143, "name": "honeycomb_block", "displayName": "Honeycomb Block", "stackSize": 64}, {"id": 1144, "name": "lodestone", "displayName": "Lodestone", "stackSize": 64}, {"id": 1145, "name": "crying_obsidian", "displayName": "Crying Obsidian", "stackSize": 64}, {"id": 1146, "name": "blackstone", "displayName": "Blackstone", "stackSize": 64}, {"id": 1147, "name": "blackstone_slab", "displayName": "Blackstone Slab", "stackSize": 64}, {"id": 1148, "name": "blackstone_stairs", "displayName": "Blackstone Stairs", "stackSize": 64}, {"id": 1149, "name": "gilded_blackstone", "displayName": "Gilded Blackstone", "stackSize": 64}, {"id": 1150, "name": "polished_blackstone", "displayName": "Polished Blackstone", "stackSize": 64}, {"id": 1151, "name": "polished_blackstone_slab", "displayName": "Polished Blackstone Slab", "stackSize": 64}, {"id": 1152, "name": "polished_blackstone_stairs", "displayName": "Polished Blackstone Stairs", "stackSize": 64}, {"id": 1153, "name": "chiseled_polished_blackstone", "displayName": "Chiseled Polished Blackstone", "stackSize": 64}, {"id": 1154, "name": "polished_blackstone_bricks", "displayName": "Polished Blackstone Bricks", "stackSize": 64}, {"id": 1155, "name": "polished_blackstone_brick_slab", "displayName": "Polished Blackstone Brick Slab", "stackSize": 64}, {"id": 1156, "name": "polished_blackstone_brick_stairs", "displayName": "Polished Blackstone Brick Stairs", "stackSize": 64}, {"id": 1157, "name": "cracked_polished_blackstone_bricks", "displayName": "Cracked Polished Blackstone Bricks", "stackSize": 64}, {"id": 1158, "name": "respawn_anchor", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "stackSize": 64}, {"id": 1159, "name": "candle", "displayName": "Candle", "stackSize": 64}, {"id": 1160, "name": "white_candle", "displayName": "White Candle", "stackSize": 64}, {"id": 1161, "name": "orange_candle", "displayName": "Orange Candle", "stackSize": 64}, {"id": 1162, "name": "magenta_candle", "displayName": "<PERSON><PERSON><PERSON>", "stackSize": 64}, {"id": 1163, "name": "light_blue_candle", "displayName": "Light Blue Candle", "stackSize": 64}, {"id": 1164, "name": "yellow_candle", "displayName": "Yellow Candle", "stackSize": 64}, {"id": 1165, "name": "lime_candle", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 1166, "name": "pink_candle", "displayName": "Pink Candle", "stackSize": 64}, {"id": 1167, "name": "gray_candle", "displayName": "<PERSON>", "stackSize": 64}, {"id": 1168, "name": "light_gray_candle", "displayName": "Light Gray Candle", "stackSize": 64}, {"id": 1169, "name": "cyan_candle", "displayName": "<PERSON><PERSON>", "stackSize": 64}, {"id": 1170, "name": "purple_candle", "displayName": "Purple Candle", "stackSize": 64}, {"id": 1171, "name": "blue_candle", "displayName": "Blue Candle", "stackSize": 64}, {"id": 1172, "name": "brown_candle", "displayName": "<PERSON> Candle", "stackSize": 64}, {"id": 1173, "name": "green_candle", "displayName": "Green Candle", "stackSize": 64}, {"id": 1174, "name": "red_candle", "displayName": "<PERSON> Candle", "stackSize": 64}, {"id": 1175, "name": "black_candle", "displayName": "Black Candle", "stackSize": 64}, {"id": 1176, "name": "small_amethyst_bud", "displayName": "Small Amethyst Bud", "stackSize": 64}, {"id": 1177, "name": "medium_amethyst_bud", "displayName": "Medium Amethyst Bud", "stackSize": 64}, {"id": 1178, "name": "large_amethyst_bud", "displayName": "Large Amethyst Bud", "stackSize": 64}, {"id": 1179, "name": "amethyst_cluster", "displayName": "Amethyst Cluster", "stackSize": 64}, {"id": 1180, "name": "pointed_dripstone", "displayName": "Pointed Dripstone", "stackSize": 64}, {"id": 1181, "name": "ochre_froglight", "displayName": "Ochre Froglight", "stackSize": 64}, {"id": 1182, "name": "verdant_froglight", "displayName": "<PERSON><PERSON><PERSON>", "stackSize": 64}, {"id": 1183, "name": "pearlescent_froglight", "displayName": "Pearlescent Froglight", "stackSize": 64}, {"id": 1184, "name": "frogspawn", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "stackSize": 64}, {"id": 1185, "name": "echo_shard", "displayName": "Echo Shard", "stackSize": 64}]