# Mineflayer MapArt Maker

A Minecraft bot that can fill a 16x16 area with white wool blocks, building 2 blocks above the starting position.

## Features

- Connects to Minecraft server using Mineflayer
- Fills a 16x16 area with white wool
- Builds 2 blocks up from starting position
- Automatically builds scaffolding when needed to place blocks in the air
- **Automatic inventory monitoring** - checks wool count every 5 seconds
- **Real-time wool tracking** - shows remaining wool count with each placement
- **Manual inventory checking** - type 'check' or 'inventory' in console
- Cleans up scaffolding after completion
- Detailed progress tracking and console logging with emojis

## Prerequisites

- Node.js installed on your system
- A Minecraft server (can be local or remote)
- White wool in the bot's inventory

## Installation

1. Install dependencies:
```bash
npm install
```

## Configuration

Edit the `BOT_CONFIG` object in `bot.js` to match your server settings:

```javascript
const BOT_CONFIG = {
    host: 'localhost',     // Your server IP
    port: 25565,          // Your server port
    username: 'MapArtBot', // Bot username
    // password: 'your_password', // Uncomment if needed
};
```

## Usage

1. Make sure your Minecraft server is running
2. Give the bot white wool in its inventory (you can do this by having the bot join the server first)
3. Run the bot:
```bash
npm start
```

## How it works

1. **Connection**: Bot connects to the specified Minecraft server
2. **Inventory Check**: Checks for white wool in inventory
3. **Area Building**: Systematically fills a 16x16 area starting from the bot's spawn position
4. **Height Management**: Places blocks 2 blocks above the starting position
5. **Scaffolding**: Automatically builds temporary scaffolding when needed to reach higher positions
6. **Progress Tracking**: Shows progress percentage in console
7. **Cleanup**: Removes scaffolding blocks after completion

## Console Output

The bot provides detailed console output including:
- Connection status
- Starting position
- **Automatic inventory updates** every 5 seconds showing wool count
- Building progress (percentage completed) with remaining wool count
- Real-time placement confirmations with coordinates
- White wool availability status with visual indicators (✅❌🧶)
- Error messages if any issues occur

## Manual Commands

While the bot is running, you can type these commands in the console:
- `check` or `inventory` - Manually check current wool inventory
- `help` - Show available commands

## Inventory Monitoring

The bot automatically monitors your inventory:
- **Check interval**: Every 5 seconds (configurable in config.json)
- **Real-time updates**: Shows wool count changes immediately
- **Build-time tracking**: Displays remaining wool with each block placed
- **Low wool warning**: Alerts when no wool is found

## Troubleshooting

- **"No white wool found in inventory!"**: Make sure the bot has white wool items in its inventory
- **Connection errors**: Check server IP, port, and make sure the server is running
- **Placement errors**: The bot will try to build scaffolding or find alternative placement methods

## Customization

You can modify these constants in `bot.js`:
- `AREA_SIZE`: Change the size of the area (default: 16x16)
- `BUILD_HEIGHT`: Change how many blocks up to build (default: 2)

## Dependencies

- `mineflayer`: Main library for creating Minecraft bots
- `mineflayer-pathfinder`: For bot navigation (loaded automatically when needed)
