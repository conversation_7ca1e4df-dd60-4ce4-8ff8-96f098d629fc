# Minecraft Schematic System

A Minecraft bot system for saving and printing block structures using Mineflayer.

## 🚀 Features

### **Saver.js**
- 📦 Scans any area defined by start/end coordinates
- 💾 Saves block type, name, states, metadata, and coordinates
- 🚫 Ignores air blocks (configurable)
- 📋 Scans layer by layer for organized data
- 📊 Shows real-time progress and statistics

### **Printer.js**
- 🏗️ Builds structures 1 block in front of bot
- 🔄 Layer-by-layer building with zig-zag pattern
- 🎁 Uses `/give` commands to obtain blocks
- 💬 Reports unavailable blocks in chat
- 🧹 Can clear build areas with `/fill` command
- ⚡ Real-time progress tracking

## 📋 Setup

1. **Install dependencies:**
```bash
npm install
```

2. **Configure your area in `config.json`:**
```json
{
  "server": {
    "host": "localhost",
    "port": 25565,
    "username": "SchematicBot"
  },
  "area": {
    "start": { "x": 0, "y": 64, "z": 0 },
    "end": { "x": 10, "y": 70, "z": 10 }
  }
}
```

## 🎮 Usage

### **Step 1: Save a Structure**
```bash
npm run save
# or
node saver.js
```

The bot will:
- Connect to your server
- Scan the area defined in config
- Save all block data to `data.json`
- Show progress and completion stats

### **Step 2: Print the Structure**
```bash
npm run print
# or
node printer.js
```

Then use these commands in the console:
- `print` - Start building the structure in front of bot
- `clear` - Clear the build area
- `info` - Show schematic information
- `tp` - Teleport to specified player (default: Xiovz)
- `help` - Show available commands

## 📊 How It Works

### **Saver Process:**
1. 🔍 Scans area from start to end coordinates
2. 📦 Collects block data (type, name, metadata, states, position)
3. 🚫 Skips air blocks (saves space)
4. 📋 Processes layer by layer (Y-level by Y-level)
5. 💾 Saves everything to `data.json` with metadata

### **Printer Process:**
1. 📖 Loads schematic data from `data.json`
2. 📍 Calculates build position (1 block in front of bot)
3. 🔨 Builds layer by layer from bottom to top
4. 🔄 Uses zig-zag pattern for efficient placement
5. 🎁 Uses `/give` commands to get required blocks
6. 💬 Reports missing blocks in chat

## ⚙️ Configuration

### **Area Settings:**
```json
"area": {
  "start": { "x": 0, "y": 64, "z": 0 },  // Starting corner
  "end": { "x": 10, "y": 70, "z": 10 }    // Ending corner
}
```

### **Build Settings:**
```json
"settings": {
  "ignoreAir": true,           // Skip air blocks when saving
  "layerByLayer": true,        // Process layer by layer
  "zigzagPattern": true,       // Use zig-zag building pattern
  "buildOffset": 1,            // Distance in front of bot
  "giveCommandDelay": 100,     // Delay after /give commands (ms)
  "blockPlaceDelay": 200,      // Delay between block placements (ms)
  "teleportTarget": "Xiovz"    // Player to teleport to with 'tp' command
}
```

## 📁 Output Format

The `data.json` file contains:

```json
{
  "metadata": {
    "version": "1.0.0",
    "created": "2024-01-01T12:00:00.000Z",
    "scanArea": { "start": {...}, "end": {...} },
    "dimensions": { "width": 11, "height": 7, "depth": 11 },
    "totalBlocks": 847,
    "scanTime": 5420
  },
  "blocks": [
    {
      "position": { "x": 0, "y": 64, "z": 0 },
      "relativePosition": { "x": 0, "y": 0, "z": 0 },
      "type": 1,
      "name": "stone",
      "metadata": 0,
      "stateId": 1,
      "properties": null,
      "displayName": "Stone"
    }
  ]
}
```

## 🎯 Example Workflow

1. **Position your bot** near the structure you want to copy
2. **Update config.json** with the exact coordinates of your structure
3. **Run saver:** `node saver.js` - scans and saves the structure
4. **Move bot** to where you want to build
5. **Run printer:** `node printer.js` - loads the schematic
6. **Type `print`** - bot builds the structure in front of it

## 🔧 Troubleshooting

- **"No schematic data loaded"** - Run saver.js first
- **"Cannot get block"** - Bot doesn't have creative permissions or block doesn't exist
- **"No suitable placement surface"** - Area might need to be cleared first
- **Bot gets stuck** - Use `clear` command to clear the build area

## 📝 Requirements

- Minecraft server with creative mode access
- Bot needs permission to use `/give` and `/fill` commands
- Node.js and npm installed
