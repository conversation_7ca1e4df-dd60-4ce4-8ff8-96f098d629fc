# Smart Mineflayer MapArt Maker

An intelligent Minecraft bot that creates perfect 16x16 white wool areas with advanced movement and placement algorithms.

## 🚀 Smart Features

- **Exact 16x16 placement** - Guarantees precise area coverage
- **Intelligent movement** - Automatically moves to optimal positions for block placement
- **Smart pathfinding** - Uses mineflayer-pathfinder for efficient navigation
- **Range management** - Calculates and maintains optimal reach distance (4.5 blocks)
- **Multi-attempt building** - Retries failed placements up to 3 times
- **Position optimization** - Sorts build order by distance for efficient movement
- **Placement verification** - Confirms each block was placed successfully
- **Fallback positioning** - Uses adjacent positions if exact placement fails
- **Real-time inventory monitoring** - Tracks wool count every 5 seconds
- **Detailed progress reporting** - Shows completion percentage and failed positions
- **Manual inventory checking** - Type 'check' or 'inventory' in console

## Prerequisites

- Node.js installed on your system
- A Minecraft server (can be local or remote)
- White wool in the bot's inventory

## Installation

1. Install dependencies:
```bash
npm install
```

## Configuration

Edit the `BOT_CONFIG` object in `bot.js` to match your server settings:

```javascript
const BOT_CONFIG = {
    host: 'localhost',     // Your server IP
    port: 25565,          // Your server port
    username: 'MapArtBot', // Bot username
    // password: 'your_password', // Uncomment if needed
};
```

## Usage

1. Make sure your Minecraft server is running
2. Give the bot white wool in its inventory (you can do this by having the bot join the server first)
3. Run the bot:
```bash
npm start
```

## 🧠 How the Smart System Works

1. **Grid Initialization**: Creates a precise 16x16 grid from bot's starting position
2. **Smart Sorting**: Orders placement by distance from bot for optimal movement
3. **Intelligent Movement**:
   - Calculates optimal position within 4.5 block reach
   - Uses pathfinder for precise navigation
   - Falls back to manual movement if pathfinding fails
4. **Exact Placement**:
   - Tries exact target coordinates first
   - Falls back to adjacent positions if needed
   - Verifies each placement was successful
5. **Multi-Attempt System**: Retries failed placements up to 3 times
6. **Progress Tracking**: Real-time updates with detailed statistics

## Console Output

The bot provides detailed console output including:
- Connection status
- Starting position
- **Automatic inventory updates** every 5 seconds showing wool count
- Building progress (percentage completed) with remaining wool count
- Real-time placement confirmations with coordinates
- White wool availability status with visual indicators (✅❌🧶)
- Error messages if any issues occur

## Manual Commands

While the bot is running, you can type these commands in the console:
- `check` or `inventory` - Manually check current wool inventory
- `help` - Show available commands

## Inventory Monitoring

The bot automatically monitors your inventory:
- **Check interval**: Every 5 seconds (configurable in config.json)
- **Real-time updates**: Shows wool count changes immediately
- **Build-time tracking**: Displays remaining wool with each block placed
- **Low wool warning**: Alerts when no wool is found

## Troubleshooting

- **"No white wool found in inventory!"**: Make sure the bot has white wool items in its inventory
- **Connection errors**: Check server IP, port, and make sure the server is running
- **Placement errors**: The bot will try to build scaffolding or find alternative placement methods

## Customization

You can modify these settings in `config.json`:
- `areaSize`: Change the size of the area (default: 16x16)
- `blockType`: Change the block type to place (default: "white_wool")
- `inventoryCheck`: Change how often to check inventory (default: 5000ms)

## Dependencies

- `mineflayer`: Main library for creating Minecraft bots
- `mineflayer-pathfinder`: For bot navigation (loaded automatically when needed)
