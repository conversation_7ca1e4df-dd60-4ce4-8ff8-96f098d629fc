{"version": 3, "file": "RefreshTokenClient.mjs", "sources": ["../../src/client/RefreshTokenClient.ts"], "sourcesContent": [null], "names": ["TimeUtils.nowSeconds", "ClientConfigurationErrorCodes.tokenRequestEmpty", "ClientAuthErrorCodes.noAccountInSilentRequest", "InteractionRequiredAuthErrorCodes.noTokensFound", "TimeUtils.isTokenExpired", "InteractionRequiredAuthErrorCodes.refreshTokenExpired", "InteractionRequiredAuthErrorCodes.badToken", "AADServerParamKeys.CLIENT_ID", "ClientConfigurationErrorCodes.missingSshJwk"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAGG;AAkDH,MAAM,+CAA+C,GAAG,GAAG,CAAC;AAE5D;;;AAGG;AACG,MAAO,kBAAmB,SAAQ,UAAU,CAAA;IAC9C,WACI,CAAA,aAAkC,EAClC,iBAAsC,EAAA;AAEtC,QAAA,KAAK,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;KAC3C;IACM,MAAM,YAAY,CACrB,OAAkC,EAAA;AAElC,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,8BAA8B,EAChD,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,QAAA,MAAM,YAAY,GAAGA,UAAoB,EAAE,CAAC;AAC5C,QAAA,MAAM,QAAQ,GAAG,MAAM,WAAW,CAC9B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EACnC,iBAAiB,CAAC,qCAAqC,EACvD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;;QAG3B,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,GAAG,WAAW,CAAC,eAAe,CAAC,CAAC;AAClE,QAAA,MAAM,eAAe,GAAG,IAAI,eAAe,CACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAC7B,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAChC,CAAC;AACF,QAAA,eAAe,CAAC,qBAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAErD,OAAO,WAAW,CACd,eAAe,CAAC,yBAAyB,CAAC,IAAI,CAAC,eAAe,CAAC,EAC/D,iBAAiB,CAAC,yBAAyB,EAC3C,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CACG,QAAQ,CAAC,IAAI,EACb,IAAI,CAAC,SAAS,EACd,YAAY,EACZ,OAAO,EACP,SAAS,EACT,SAAS,EACT,IAAI,EACJ,OAAO,CAAC,UAAU,EAClB,SAAS,CACZ,CAAC;KACL;AAED;;;AAGG;IACI,MAAM,0BAA0B,CACnC,OAAgC,EAAA;;QAGhC,IAAI,CAAC,OAAO,EAAE;AACV,YAAA,MAAM,8BAA8B,CAChCC,iBAA+C,CAClD,CAAC;AACL,SAAA;AAED,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,4CAA4C,EAC9D,OAAO,CAAC,aAAa,CACxB,CAAC;;AAGF,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AAClB,YAAA,MAAM,qBAAqB,CACvBC,wBAA6C,CAChD,CAAC;AACL,SAAA;;AAGD,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAC9C,OAAO,CAAC,OAAO,CAAC,WAAW,CAC9B,CAAC;;AAGF,QAAA,IAAI,MAAM,EAAE;YACR,IAAI;AACA,gBAAA,OAAO,MAAM,WAAW,CACpB,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,IAAI,CAAC,EAClD,iBAAiB,CAAC,oDAAoD,EACtE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACpB,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACR,gBAAA,MAAM,iBAAiB,GACnB,CAAC,YAAY,4BAA4B;AACzC,oBAAA,CAAC,CAAC,SAAS;wBACPC,aAA+C,CAAC;AACxD,gBAAA,MAAM,+BAA+B,GACjC,CAAC,YAAY,WAAW;AACxB,oBAAA,CAAC,CAAC,SAAS,KAAK,MAAM,CAAC,mBAAmB;AAC1C,oBAAA,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,qBAAqB,CAAC;;gBAGhD,IAAI,iBAAiB,IAAI,+BAA+B,EAAE;AACtD,oBAAA,OAAO,WAAW,CACd,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,IAAI,CAAC,EAClD,iBAAiB,CAAC,oDAAoD,EACtE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;;AAErB,iBAAA;AAAM,qBAAA;AACH,oBAAA,MAAM,CAAC,CAAC;AACX,iBAAA;AACJ,aAAA;AACJ,SAAA;;AAED,QAAA,OAAO,WAAW,CACd,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,IAAI,CAAC,EAClD,iBAAiB,CAAC,oDAAoD,EACtE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;KACrB;AAED;;;AAGG;AACK,IAAA,MAAM,kCAAkC,CAC5C,OAAgC,EAChC,IAAa,EAAA;AAEb,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,oDAAoD,EACtE,OAAO,CAAC,aAAa,CACxB,CAAC;;QAGF,MAAM,YAAY,GAAG,MAAM,CACvB,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EACzD,iBAAiB,CAAC,2BAA2B,EAC7C,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CACG,OAAO,CAAC,OAAO,EACf,IAAI,EACJ,SAAS,EACT,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC;QAEF,IAAI,CAAC,YAAY,EAAE;AACf,YAAA,MAAM,kCAAkC,CACpCA,aAA+C,CAClD,CAAC;AACL,SAAA;QAED,IACI,YAAY,CAAC,SAAS;YACtBC,cAAwB,CACpB,YAAY,CAAC,SAAS,EACtB,OAAO,CAAC,mCAAmC;AACvC,gBAAA,+CAA+C,CACtD,EACH;AACE,YAAA,MAAM,kCAAkC,CACpCC,mBAAqD,CACxD,CAAC;AACL,SAAA;;AAGD,QAAA,MAAM,mBAAmB,GAA8B;AACnD,YAAA,GAAG,OAAO;YACV,YAAY,EAAE,YAAY,CAAC,MAAM;AACjC,YAAA,oBAAoB,EAChB,OAAO,CAAC,oBAAoB,IAAI,oBAAoB,CAAC,MAAM;AAC/D,YAAA,aAAa,EAAE;AACX,gBAAA,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,aAAa;gBACzC,IAAI,EAAE,iBAAiB,CAAC,eAAe;AAC1C,aAAA;SACJ,CAAC;QAEF,IAAI;AACA,YAAA,OAAO,MAAM,WAAW,CACpB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAC5B,iBAAiB,CAAC,8BAA8B,EAChD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,mBAAmB,CAAC,CAAC;AAC1B,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IACI,CAAC,YAAY,4BAA4B;AACzC,gBAAA,CAAC,CAAC,QAAQ,KAAKC,QAA0C,EAC3D;;AAEE,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,sEAAsE,CACzE,CAAC;AACF,gBAAA,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,YAAY,CAAC,CAAC;AAC/D,gBAAA,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;AAC5D,aAAA;AAED,YAAA,MAAM,CAAC,CAAC;AACX,SAAA;KACJ;AAED;;;;AAIG;AACK,IAAA,MAAM,mBAAmB,CAC7B,OAAkC,EAClC,SAAoB,EAAA;AAEpB,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,qCAAqC,EACvD,OAAO,CAAC,aAAa,CACxB,CAAC;QAEF,MAAM,qBAAqB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;AACvE,QAAA,MAAM,QAAQ,GAAG,SAAS,CAAC,iBAAiB,CACxC,SAAS,CAAC,aAAa,EACvB,qBAAqB,CACxB,CAAC;AAEF,QAAA,MAAM,WAAW,GAAG,MAAM,WAAW,CACjC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,EACtC,iBAAiB,CAAC,wCAAwC,EAC1D,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,OAAO,CAAC,CAAC;QACX,MAAM,OAAO,GAA2B,IAAI,CAAC,yBAAyB,CAClE,OAAO,CAAC,aAAa,CACxB,CAAC;AACF,QAAA,MAAM,UAAU,GAAsB;AAClC,YAAA,QAAQ,EACJ,OAAO,CAAC,mBAAmB,EAAE,QAAQ;AACrC,gBAAA,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ;YACpC,SAAS,EAAE,SAAS,CAAC,kBAAkB;YACvC,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;YAClD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;YACpD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;YAC9C,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;SACzB,CAAC;QAEF,OAAO,WAAW,CACd,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC,EAC1C,iBAAiB,CAAC,4CAA4C,EAC9D,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CACG,QAAQ,EACR,WAAW,EACX,OAAO,EACP,UAAU,EACV,OAAO,CAAC,aAAa,EACrB,iBAAiB,CAAC,4CAA4C,CACjE,CAAC;KACL;AAED;;;AAGG;IACK,MAAM,sBAAsB,CAChC,OAAkC,EAAA;AAElC,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,wCAAwC,EAC1D,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,QAAA,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAC5C,MAAM,gBAAgB,GAAG,IAAI,uBAAuB,CAChD,aAAa,EACb,IAAI,CAAC,iBAAiB,CACzB,CAAC;AAEF,QAAA,gBAAgB,CAAC,WAAW,CACxB,OAAO,CAAC,gBAAgB;AACpB,YAAA,OAAO,CAAC,mBAAmB,GAAGC,SAA4B,CAAC;AAC3D,YAAA,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CACvC,CAAC;QAEF,IAAI,OAAO,CAAC,WAAW,EAAE;AACrB,YAAA,gBAAgB,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AACxD,SAAA;QAED,gBAAgB,CAAC,SAAS,CACtB,OAAO,CAAC,MAAM,EACd,IAAI,EACJ,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,EAAE,aAAa,CACvE,CAAC;AAEF,QAAA,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QAE7D,gBAAgB,CAAC,aAAa,EAAE,CAAC;QAEjC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QACzD,gBAAgB,CAAC,uBAAuB,CACpC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CACpC,CAAC;QACF,gBAAgB,CAAC,aAAa,EAAE,CAAC;QAEjC,IAAI,IAAI,CAAC,sBAAsB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AACjE,YAAA,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;AACpE,SAAA;AAED,QAAA,gBAAgB,CAAC,eAAe,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AAEvD,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,EAAE;YAC5C,gBAAgB,CAAC,eAAe,CAC5B,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAC7C,CAAC;AACL,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,eAAe,EAAE;YAC/C,MAAM,eAAe,GACjB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,eAAe,CAAC;YAElD,gBAAgB,CAAC,kBAAkB,CAC/B,MAAM,kBAAkB,CACpB,eAAe,CAAC,SAAS,EACzB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,OAAO,CAAC,kBAAkB,CAC7B,CACJ,CAAC;AACF,YAAA,gBAAgB,CAAC,sBAAsB,CACnC,eAAe,CAAC,aAAa,CAChC,CAAC;AACL,SAAA;AAED,QAAA,IAAI,OAAO,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,GAAG,EAAE;AAC3D,YAAA,MAAM,iBAAiB,GAAG,IAAI,iBAAiB,CAC3C,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,iBAAiB,CACzB,CAAC;AAEF,YAAA,IAAI,UAAU,CAAC;AACf,YAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AACjB,gBAAA,MAAM,mBAAmB,GAAG,MAAM,WAAW,CACzC,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,EACrD,iBAAiB,CAAC,mBAAmB,EACrC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AAExB,gBAAA,UAAU,GAAG,mBAAmB,CAAC,YAAY,CAAC;AACjD,aAAA;AAAM,iBAAA;gBACH,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC3D,aAAA;;AAGD,YAAA,gBAAgB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AAC5C,SAAA;AAAM,aAAA,IAAI,OAAO,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,GAAG,EAAE;YAClE,IAAI,OAAO,CAAC,MAAM,EAAE;AAChB,gBAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC9C,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,8BAA8B,CAChCC,aAA2C,CAC9C,CAAC;AACL,aAAA;AACJ,SAAA;QAED,IACI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;AACvC,aAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB;gBACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,EAC5D;AACE,YAAA,gBAAgB,CAAC,SAAS,CACtB,OAAO,CAAC,MAAM,EACd,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAC7C,CAAC;AACL,SAAA;AAED,QAAA,IACI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,oBAAoB;YAC9C,OAAO,CAAC,aAAa,EACvB;AACE,YAAA,QAAQ,OAAO,CAAC,aAAa,CAAC,IAAI;gBAC9B,KAAK,iBAAiB,CAAC,eAAe;oBAClC,IAAI;wBACA,MAAM,UAAU,GAAG,gCAAgC,CAC/C,OAAO,CAAC,aAAa,CAAC,UAAU,CACnC,CAAC;AACF,wBAAA,gBAAgB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AAC1C,qBAAA;AAAC,oBAAA,OAAO,CAAC,EAAE;AACR,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,kDAAkD;AAC9C,4BAAA,CAAC,CACR,CAAC;AACL,qBAAA;oBACD,MAAM;gBACV,KAAK,iBAAiB,CAAC,GAAG;oBACtB,gBAAgB,CAAC,SAAS,CACtB,OAAO,CAAC,aAAa,CAAC,UAAU,CACnC,CAAC;oBACF,MAAM;AACb,aAAA;AACJ,SAAA;QAED,IAAI,OAAO,CAAC,gBAAgB,EAAE;YAC1B,gBAAgB,CAAC,mBAAmB,CAAC;AACjC,gBAAA,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ;AAChD,gBAAA,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW;AACzD,aAAA,CAAC,CAAC;AACN,SAAA;QAED,IAAI,OAAO,CAAC,mBAAmB,EAAE;AAC7B,YAAA,gBAAgB,CAAC,uBAAuB,CACpC,OAAO,CAAC,mBAAmB,CAC9B,CAAC;AACL,SAAA;AAED,QAAA,OAAO,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;KAC/C;AACJ;;;;"}