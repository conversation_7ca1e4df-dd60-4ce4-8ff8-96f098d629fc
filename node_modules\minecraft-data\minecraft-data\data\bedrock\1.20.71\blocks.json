[{"id": 0, "name": "air", "displayName": "Air", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": false, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11892, "minStateId": 11892, "maxStateId": 11892, "drops": [], "boundingBox": "empty"}, {"id": 1, "name": "stone", "displayName": "Stone", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1791, "minStateId": 1791, "maxStateId": 1791, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [35], "boundingBox": "block"}, {"id": 2, "name": "granite", "displayName": "Granite", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 86, "minStateId": 86, "maxStateId": 86, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [2], "boundingBox": "block"}, {"id": 3, "name": "polished_granite", "displayName": "Polished Granite", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1164, "minStateId": 1164, "maxStateId": 1164, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [3], "boundingBox": "block"}, {"id": 4, "name": "diorite", "displayName": "Diorite", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 152, "minStateId": 152, "maxStateId": 152, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [4], "boundingBox": "block"}, {"id": 5, "name": "polished_diorite", "displayName": "Polished Diorite", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 10045, "minStateId": 10045, "maxStateId": 10045, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [5], "boundingBox": "block"}, {"id": 6, "name": "andesite", "displayName": "Andesite", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1789, "minStateId": 1789, "maxStateId": 1789, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [6], "boundingBox": "block"}, {"id": 7, "name": "polished_andesite", "displayName": "Polished Andesite", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13249, "minStateId": 13249, "maxStateId": 13249, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [7], "boundingBox": "block"}, {"id": 8, "name": "grass_block", "displayName": "Grass Block", "hardness": 0.6, "resistance": 0.6, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 10617, "minStateId": 10617, "maxStateId": 10617, "drops": [28], "boundingBox": "block"}, {"id": 9, "name": "dirt", "displayName": "Dirt", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9578, "minStateId": 9578, "maxStateId": 9579, "drops": [28], "boundingBox": "block"}, {"id": 11, "name": "podzol", "displayName": "Podzol", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7966, "minStateId": 7966, "maxStateId": 7966, "drops": [28], "boundingBox": "block"}, {"id": 12, "name": "cobblestone", "displayName": "Cobblestone", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 5997, "minStateId": 5997, "maxStateId": 5997, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [35], "boundingBox": "block"}, {"id": 13, "name": "oak_planks", "displayName": "Oak Planks", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13245, "minStateId": 13245, "maxStateId": 13245, "drops": [36], "boundingBox": "block"}, {"id": 14, "name": "spruce_planks", "displayName": "Spruce Planks", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13607, "minStateId": 13607, "maxStateId": 13607, "drops": [37], "boundingBox": "block"}, {"id": 15, "name": "birch_planks", "displayName": "Birch Planks", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9112, "minStateId": 9112, "maxStateId": 9112, "drops": [38], "boundingBox": "block"}, {"id": 16, "name": "jungle_planks", "displayName": "Jungle Planks", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11827, "minStateId": 11827, "maxStateId": 11827, "drops": [39], "boundingBox": "block"}, {"id": 17, "name": "acacia_planks", "displayName": "Acacia Planks", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7303, "minStateId": 7303, "maxStateId": 7303, "drops": [40], "boundingBox": "block"}, {"id": 18, "name": "cherry_planks", "displayName": "Cherry Planks", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13475, "minStateId": 13475, "maxStateId": 13475, "drops": [41], "boundingBox": "block"}, {"id": 19, "name": "dark_oak_planks", "displayName": "Dark Oak Planks", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 5933, "minStateId": 5933, "maxStateId": 5933, "drops": [42], "boundingBox": "block"}, {"id": 20, "name": "mangrove_planks", "displayName": "Mangrove Planks", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 2432, "minStateId": 2432, "maxStateId": 2432, "drops": [43], "boundingBox": "block"}, {"id": 21, "name": "bamboo_planks", "displayName": "Bamboo Planks", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9443, "minStateId": 9443, "maxStateId": 9443, "drops": [44], "boundingBox": "block"}, {"id": 22, "name": "bamboo_mosaic", "displayName": "Bamboo Mosaic", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 14084, "minStateId": 14084, "maxStateId": 14084, "drops": [47], "boundingBox": "block"}, {"id": 23, "name": "sapling", "displayName": "Oak Sapling", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 2171, "minStateId": 2171, "maxStateId": 2182, "drops": [48], "boundingBox": "empty"}, {"id": 28, "name": "cherry_sapling", "displayName": "Cherry Sapling", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12947, "minStateId": 12947, "maxStateId": 12948, "drops": [53], "boundingBox": "empty"}, {"id": 30, "name": "mangrove_propagule", "displayName": "Mangrove Propagule", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12198, "minStateId": 12198, "maxStateId": 12207, "drops": [], "boundingBox": "empty"}, {"id": 31, "name": "bedrock", "displayName": "Bedrock", "hardness": -1, "resistance": 3600000, "stackSize": 64, "diggable": false, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12243, "minStateId": 12243, "maxStateId": 12244, "drops": [], "boundingBox": "block"}, {"id": 32, "name": "flowing_water", "displayName": "Water", "hardness": 100, "resistance": 100, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 8105, "minStateId": 8105, "maxStateId": 8120, "drops": [], "boundingBox": "empty"}, {"id": 8000, "name": "water", "displayName": "Water", "hardness": 100, "resistance": 100, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 9330, "minStateId": 9330, "maxStateId": 9345, "drops": [], "boundingBox": "empty"}, {"id": 33, "name": "lava", "displayName": "<PERSON><PERSON>", "hardness": 100, "resistance": 100, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 15, "filterLight": 1, "defaultState": 6209, "minStateId": 6209, "maxStateId": 6224, "drops": [], "boundingBox": "empty"}, {"id": 8001, "name": "flowing_lava", "displayName": "<PERSON><PERSON>", "hardness": 100, "resistance": 100, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 15, "filterLight": 1, "defaultState": 12894, "minStateId": 12894, "maxStateId": 12909, "drops": [], "boundingBox": "empty"}, {"id": 34, "name": "sand", "displayName": "Sand", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6998, "minStateId": 6998, "maxStateId": 6999, "drops": [57], "boundingBox": "block"}, {"id": 35, "name": "suspicious_sand", "displayName": "Suspicious Sand", "hardness": 0.25, "resistance": 0.25, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 2621, "minStateId": 2621, "maxStateId": 2628, "drops": [], "boundingBox": "block"}, {"id": 37, "name": "gravel", "displayName": "<PERSON>l", "hardness": 0.6, "resistance": 0.6, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 14115, "minStateId": 14115, "maxStateId": 14115, "drops": [61], "boundingBox": "block"}, {"id": 38, "name": "suspicious_gravel", "displayName": "Suspicious Gravel", "hardness": 0.25, "resistance": 0.25, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 8081, "minStateId": 8081, "maxStateId": 8088, "drops": [], "boundingBox": "block"}, {"id": 39, "name": "gold_ore", "displayName": "Gold Ore", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 2395, "minStateId": 2395, "maxStateId": 2395, "harvestTools": {"831": true, "836": true, "841": true}, "drops": [810], "boundingBox": "block"}, {"id": 40, "name": "deepslate_gold_ore", "displayName": "Deepslate Gold Ore", "hardness": 4.5, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11256, "minStateId": 11256, "maxStateId": 11256, "harvestTools": {"831": true, "836": true, "841": true}, "drops": [810], "boundingBox": "block"}, {"id": 41, "name": "iron_ore", "displayName": "Iron Ore", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 8006, "minStateId": 8006, "maxStateId": 8006, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [806], "boundingBox": "block"}, {"id": 42, "name": "deepslate_iron_ore", "displayName": "Deepslate Iron Ore", "hardness": 4.5, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12973, "minStateId": 12973, "maxStateId": 12973, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [806], "boundingBox": "block"}, {"id": 43, "name": "coal_ore", "displayName": "Coal Ore", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7093, "minStateId": 7093, "maxStateId": 7093, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [799], "boundingBox": "block"}, {"id": 44, "name": "deepslate_coal_ore", "displayName": "Deepslate Coal Ore", "hardness": 4.5, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12859, "minStateId": 12859, "maxStateId": 12859, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [799], "boundingBox": "block"}, {"id": 45, "name": "nether_gold_ore", "displayName": "Nether Gold Ore", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 32, "minStateId": 32, "maxStateId": 32, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [993], "boundingBox": "block"}, {"id": 46, "name": "oak_log", "displayName": "Oak Log", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 737, "minStateId": 737, "maxStateId": 739, "drops": [131], "boundingBox": "block"}, {"id": 47, "name": "spruce_log", "displayName": "Spruce Log", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7073, "minStateId": 7073, "maxStateId": 7075, "drops": [132], "boundingBox": "block"}, {"id": 48, "name": "birch_log", "displayName": "Birch Log", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1792, "minStateId": 1792, "maxStateId": 1794, "drops": [133], "boundingBox": "block"}, {"id": 49, "name": "jungle_log", "displayName": "Jungle Log", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 642, "minStateId": 642, "maxStateId": 644, "drops": [134], "boundingBox": "block"}, {"id": 50, "name": "acacia_log", "displayName": "Acacia Log", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7180, "minStateId": 7180, "maxStateId": 7182, "drops": [135], "boundingBox": "block"}, {"id": 51, "name": "cherry_log", "displayName": "Cherry Log", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12949, "minStateId": 12949, "maxStateId": 12951, "drops": [136], "boundingBox": "block"}, {"id": 52, "name": "dark_oak_log", "displayName": "Dark Oak Log", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 4937, "minStateId": 4937, "maxStateId": 4939, "drops": [137], "boundingBox": "block"}, {"id": 53, "name": "mangrove_log", "displayName": "Mangrove Log", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1104, "minStateId": 1104, "maxStateId": 1106, "drops": [138], "boundingBox": "block"}, {"id": 54, "name": "mangrove_roots", "displayName": "Mangrove Roots", "hardness": 0.7, "resistance": 0.7, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 11342, "minStateId": 11342, "maxStateId": 11342, "drops": [139], "boundingBox": "block"}, {"id": 55, "name": "muddy_mangrove_roots", "displayName": "Muddy Mangrove Roots", "hardness": 0.7, "resistance": 0.7, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1096, "minStateId": 1096, "maxStateId": 1098, "drops": [140], "boundingBox": "block"}, {"id": 56, "name": "bamboo_block", "displayName": "Block of Bamboo", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 66, "minStateId": 66, "maxStateId": 68, "drops": [143], "boundingBox": "block"}, {"id": 57, "name": "stripped_spruce_log", "displayName": "Stripped Spruce Log", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11434, "minStateId": 11434, "maxStateId": 11436, "drops": [145], "boundingBox": "block"}, {"id": 58, "name": "stripped_birch_log", "displayName": "Stripped Birch Log", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 10709, "minStateId": 10709, "maxStateId": 10711, "drops": [146], "boundingBox": "block"}, {"id": 59, "name": "stripped_jungle_log", "displayName": "Stripped Jungle Log", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1778, "minStateId": 1778, "maxStateId": 1780, "drops": [147], "boundingBox": "block"}, {"id": 60, "name": "stripped_acacia_log", "displayName": "Stripped Acacia Log", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 10098, "minStateId": 10098, "maxStateId": 10100, "drops": [148], "boundingBox": "block"}, {"id": 61, "name": "stripped_cherry_log", "displayName": "Stripped Cherry Log", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7288, "minStateId": 7288, "maxStateId": 7290, "drops": [149], "boundingBox": "block"}, {"id": 62, "name": "stripped_dark_oak_log", "displayName": "Stripped Dark Oak Log", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 638, "minStateId": 638, "maxStateId": 640, "drops": [150], "boundingBox": "block"}, {"id": 63, "name": "stripped_oak_log", "displayName": "Stripped Oak Log", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13246, "minStateId": 13246, "maxStateId": 13248, "drops": [144], "boundingBox": "block"}, {"id": 64, "name": "stripped_mangrove_log", "displayName": "Stripped Mangrove Log", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 14112, "minStateId": 14112, "maxStateId": 14114, "drops": [151], "boundingBox": "block"}, {"id": 65, "name": "stripped_bamboo_block", "displayName": "Block of Stripped Bamboo", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 5976, "minStateId": 5976, "maxStateId": 5978, "drops": [164], "boundingBox": "block"}, {"id": 66, "name": "oak_wood", "displayName": "Oak Wood", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 8619, "minStateId": 8619, "maxStateId": 8621, "drops": [165], "boundingBox": "block"}, {"id": 67, "name": "spruce_wood", "displayName": "Spruce Wood", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13296, "minStateId": 13296, "maxStateId": 13298, "drops": [166], "boundingBox": "block"}, {"id": 68, "name": "birch_wood", "displayName": "Birch Wood", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1982, "minStateId": 1982, "maxStateId": 1984, "drops": [167], "boundingBox": "block"}, {"id": 69, "name": "jungle_wood", "displayName": "Jungle Wood", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1997, "minStateId": 1997, "maxStateId": 1999, "drops": [168], "boundingBox": "block"}, {"id": 70, "name": "acacia_wood", "displayName": "Acacia Wood", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12113, "minStateId": 12113, "maxStateId": 12115, "drops": [169], "boundingBox": "block"}, {"id": 71, "name": "cherry_wood", "displayName": "<PERSON>", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12385, "minStateId": 12385, "maxStateId": 12390, "drops": [170], "boundingBox": "block"}, {"id": 72, "name": "dark_oak_wood", "displayName": "Dark Oak Wood", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 10, "minStateId": 10, "maxStateId": 12, "drops": [171], "boundingBox": "block"}, {"id": 73, "name": "mangrove_wood", "displayName": "Mangrove Wood", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6955, "minStateId": 6955, "maxStateId": 6960, "drops": [172], "boundingBox": "block"}, {"id": 74, "name": "stripped_oak_wood", "displayName": "Stripped Oak Wood", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 8176, "minStateId": 8176, "maxStateId": 8178, "drops": [154], "boundingBox": "block"}, {"id": 75, "name": "stripped_spruce_wood", "displayName": "Stripped Spruce Wood", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1959, "minStateId": 1959, "maxStateId": 1961, "drops": [155], "boundingBox": "block"}, {"id": 76, "name": "stripped_birch_wood", "displayName": "Stripped Birch Wood", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7415, "minStateId": 7415, "maxStateId": 7417, "drops": [156], "boundingBox": "block"}, {"id": 77, "name": "stripped_jungle_wood", "displayName": "Stripped Jungle Wood", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1099, "minStateId": 1099, "maxStateId": 1101, "drops": [157], "boundingBox": "block"}, {"id": 78, "name": "stripped_acacia_wood", "displayName": "Stripped Acacia Wood", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 724, "minStateId": 724, "maxStateId": 726, "drops": [158], "boundingBox": "block"}, {"id": 79, "name": "stripped_cherry_wood", "displayName": "Stripped Cherry Wood", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 8646, "minStateId": 8646, "maxStateId": 8648, "drops": [159], "boundingBox": "block"}, {"id": 80, "name": "stripped_dark_oak_wood", "displayName": "Stripped Dark Oak Wood", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 8614, "minStateId": 8614, "maxStateId": 8616, "drops": [160], "boundingBox": "block"}, {"id": 81, "name": "stripped_mangrove_wood", "displayName": "Stripped Mangrove Wood", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7032, "minStateId": 7032, "maxStateId": 7034, "drops": [161], "boundingBox": "block"}, {"id": 82, "name": "oak_leaves", "displayName": "Oak Leaves", "hardness": 0.2, "resistance": 0.2, "stackSize": 64, "diggable": true, "material": "leaves;mineable/hoe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 2003, "minStateId": 2003, "maxStateId": 2006, "drops": [], "boundingBox": "block"}, {"id": 83, "name": "spruce_leaves", "displayName": "Spruce Leaves", "hardness": 0.2, "resistance": 0.2, "stackSize": 64, "diggable": true, "material": "leaves;mineable/hoe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 7250, "minStateId": 7250, "maxStateId": 7253, "drops": [], "boundingBox": "block"}, {"id": 84, "name": "birch_leaves", "displayName": "Birch Leaves", "hardness": 0.2, "resistance": 0.2, "stackSize": 64, "diggable": true, "material": "leaves;mineable/hoe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 6945, "minStateId": 6945, "maxStateId": 6948, "drops": [], "boundingBox": "block"}, {"id": 85, "name": "jungle_leaves", "displayName": "Jungle Leaves", "hardness": 0.2, "resistance": 0.2, "stackSize": 64, "diggable": true, "material": "leaves;mineable/hoe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 9132, "minStateId": 9132, "maxStateId": 9135, "drops": [], "boundingBox": "block"}, {"id": 86, "name": "acacia_leaves", "displayName": "Acacia Leaves", "hardness": 0.2, "resistance": 0.2, "stackSize": 64, "diggable": true, "material": "leaves;mineable/hoe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 2652, "minStateId": 2652, "maxStateId": 2655, "drops": [], "boundingBox": "block"}, {"id": 87, "name": "cherry_leaves", "displayName": "Cherry Leaves", "hardness": 0.2, "resistance": 0.2, "stackSize": 64, "diggable": true, "material": "leaves;mineable/hoe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 10048, "minStateId": 10048, "maxStateId": 10051, "drops": [], "boundingBox": "block"}, {"id": 88, "name": "dark_oak_leaves", "displayName": "Dark Oak Leaves", "hardness": 0.2, "resistance": 0.2, "stackSize": 64, "diggable": true, "material": "leaves;mineable/hoe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 11189, "minStateId": 11189, "maxStateId": 11192, "drops": [], "boundingBox": "block"}, {"id": 89, "name": "mangrove_leaves", "displayName": "Mangrove Leaves", "hardness": 0.2, "resistance": 0.2, "stackSize": 64, "diggable": true, "material": "leaves;mineable/hoe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 11880, "minStateId": 11880, "maxStateId": 11883, "drops": [], "boundingBox": "block"}, {"id": 90, "name": "azalea_leaves", "displayName": "Azalea Leaves", "hardness": 0.2, "resistance": 0.2, "stackSize": 64, "diggable": true, "material": "leaves;mineable/hoe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 13424, "minStateId": 13424, "maxStateId": 13427, "drops": [], "boundingBox": "block"}, {"id": 91, "name": "azalea_leaves_flowered", "displayName": "Flowering Azalea Leaves", "hardness": 0.2, "resistance": 0.2, "stackSize": 64, "diggable": true, "material": "leaves;mineable/hoe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 11489, "minStateId": 11489, "maxStateId": 11492, "drops": [], "boundingBox": "block"}, {"id": 92, "name": "sponge", "displayName": "Sponge", "hardness": 0.6, "resistance": 0.6, "stackSize": 64, "diggable": true, "material": "mineable/hoe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1380, "minStateId": 1380, "maxStateId": 1381, "drops": [185], "boundingBox": "block"}, {"id": 94, "name": "glass", "displayName": "Glass", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11331, "minStateId": 11331, "maxStateId": 11331, "drops": [], "boundingBox": "block"}, {"id": 95, "name": "lapis_ore", "displayName": "Lapis <PERSON> Ore", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13411, "minStateId": 13411, "maxStateId": 13411, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [803], "boundingBox": "block"}, {"id": 96, "name": "deepslate_lapis_ore", "displayName": "Deepslate Lapis Lazuli Ore", "hardness": 4.5, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12945, "minStateId": 12945, "maxStateId": 12945, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [803], "boundingBox": "block"}, {"id": 97, "name": "lapis_block", "displayName": "Block of Lapis Lazuli", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7092, "minStateId": 7092, "maxStateId": 7092, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [189], "boundingBox": "block"}, {"id": 98, "name": "dispenser", "displayName": "Dispenser", "hardness": 3.5, "resistance": 3.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13818, "minStateId": 13818, "maxStateId": 13829, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [667], "boundingBox": "block"}, {"id": 99, "name": "sandstone", "displayName": "Sandstone", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6041, "minStateId": 6041, "maxStateId": 6044, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [190], "boundingBox": "block"}, {"id": 102, "name": "noteblock", "displayName": "Note Block", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1102, "minStateId": 1102, "maxStateId": 1102, "drops": [680], "boundingBox": "block"}, {"id": 118, "name": "bed", "displayName": "Black Bed", "hardness": 0.2, "resistance": 0.2, "stackSize": 1, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11893, "minStateId": 11893, "maxStateId": 11908, "drops": [], "boundingBox": "block"}, {"id": 119, "name": "golden_rail", "displayName": "Powered Rail", "hardness": 0.7, "resistance": 0.7, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9113, "minStateId": 9113, "maxStateId": 9124, "drops": [760], "boundingBox": "empty"}, {"id": 120, "name": "detector_rail", "displayName": "Detector Rail", "hardness": 0.7, "resistance": 0.7, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 6918, "minStateId": 6918, "maxStateId": 6929, "drops": [761], "boundingBox": "empty"}, {"id": 121, "name": "sticky_piston", "displayName": "<PERSON><PERSON>", "hardness": 1.5, "resistance": 1.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7210, "minStateId": 7210, "maxStateId": 7215, "drops": [662], "boundingBox": "block"}, {"id": 122, "name": "web", "displayName": "Cobweb", "hardness": 4, "resistance": 4, "stackSize": 64, "diggable": true, "material": "<PERSON><PERSON>", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 11916, "minStateId": 11916, "maxStateId": 11916, "harvestTools": {"814": true, "819": true, "824": true, "829": true, "834": true, "839": true, "980": true}, "drops": [847], "boundingBox": "empty"}, {"id": 123, "name": "tallgrass", "displayName": "Short Grass", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 2411, "minStateId": 2411, "maxStateId": 2414, "drops": [], "boundingBox": "empty"}, {"id": 125, "name": "deadbush", "displayName": "Dead Bush", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7993, "minStateId": 7993, "maxStateId": 7993, "drops": [844], "boundingBox": "empty"}, {"id": 126, "name": "seagrass", "displayName": "Seagrass", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 677, "minStateId": 677, "maxStateId": 679, "drops": [], "boundingBox": "empty"}, {"id": 128, "name": "piston", "displayName": "<PERSON><PERSON>", "hardness": 1.5, "resistance": 1.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 2404, "minStateId": 2404, "maxStateId": 2409, "drops": [661], "boundingBox": "block"}, {"id": 129, "name": "piston_arm_collision", "displayName": "Piston Head", "hardness": 1.5, "resistance": 1.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 69, "minStateId": 69, "maxStateId": 74, "drops": [], "boundingBox": "block"}, {"id": 8002, "name": "sticky_piston_arm_collision", "displayName": "Piston Head", "hardness": 1.5, "resistance": 1.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11494, "minStateId": 11494, "maxStateId": 11499, "drops": [], "boundingBox": "block"}, {"id": 130, "name": "white_wool", "displayName": "White Wool", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "wool", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9189, "minStateId": 9189, "maxStateId": 9189, "drops": [201], "boundingBox": "block"}, {"id": 131, "name": "orange_wool", "displayName": "Orange Wool", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "wool", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1962, "minStateId": 1962, "maxStateId": 1962, "drops": [202], "boundingBox": "block"}, {"id": 132, "name": "magenta_wool", "displayName": "Magenta Wool", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "wool", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 2439, "minStateId": 2439, "maxStateId": 2439, "drops": [203], "boundingBox": "block"}, {"id": 133, "name": "light_blue_wool", "displayName": "Light Blue Wool", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "wool", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12346, "minStateId": 12346, "maxStateId": 12346, "drops": [204], "boundingBox": "block"}, {"id": 134, "name": "yellow_wool", "displayName": "Yellow Wool", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "wool", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 557, "minStateId": 557, "maxStateId": 557, "drops": [205], "boundingBox": "block"}, {"id": 135, "name": "lime_wool", "displayName": "Lime Wool", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "wool", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11129, "minStateId": 11129, "maxStateId": 11129, "drops": [206], "boundingBox": "block"}, {"id": 136, "name": "pink_wool", "displayName": "Pink Wool", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "wool", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6174, "minStateId": 6174, "maxStateId": 6174, "drops": [207], "boundingBox": "block"}, {"id": 137, "name": "gray_wool", "displayName": "Gray <PERSON>", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "wool", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 650, "minStateId": 650, "maxStateId": 650, "drops": [208], "boundingBox": "block"}, {"id": 138, "name": "light_gray_wool", "displayName": "Light Gray Wool", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "wool", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13848, "minStateId": 13848, "maxStateId": 13848, "drops": [209], "boundingBox": "block"}, {"id": 139, "name": "cyan_wool", "displayName": "<PERSON><PERSON>", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "wool", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9125, "minStateId": 9125, "maxStateId": 9125, "drops": [210], "boundingBox": "block"}, {"id": 140, "name": "purple_wool", "displayName": "Purple Wool", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "wool", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 14088, "minStateId": 14088, "maxStateId": 14088, "drops": [211], "boundingBox": "block"}, {"id": 141, "name": "blue_wool", "displayName": "Blue Wool", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "wool", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9302, "minStateId": 9302, "maxStateId": 9302, "drops": [212], "boundingBox": "block"}, {"id": 142, "name": "brown_wool", "displayName": "Brown Wool", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "wool", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 704, "minStateId": 704, "maxStateId": 704, "drops": [213], "boundingBox": "block"}, {"id": 143, "name": "green_wool", "displayName": "Green Wool", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "wool", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6121, "minStateId": 6121, "maxStateId": 6121, "drops": [214], "boundingBox": "block"}, {"id": 144, "name": "red_wool", "displayName": "Red Wool", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "wool", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 130, "minStateId": 130, "maxStateId": 130, "drops": [215], "boundingBox": "block"}, {"id": 145, "name": "black_wool", "displayName": "Black Wool", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "wool", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1120, "minStateId": 1120, "maxStateId": 1120, "drops": [216], "boundingBox": "block"}, {"id": 146, "name": "moving_block", "displayName": "Moving <PERSON>ston", "hardness": -1, "resistance": 0, "stackSize": 64, "diggable": false, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9406, "minStateId": 9406, "maxStateId": 9406, "drops": [], "boundingBox": "empty"}, {"id": 147, "name": "yellow_flower", "displayName": "Dandelion", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 1051, "minStateId": 1051, "maxStateId": 1051, "drops": [217], "boundingBox": "empty"}, {"id": 148, "name": "torchflower", "displayName": "Torch<PERSON>", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11209, "minStateId": 11209, "maxStateId": 11209, "drops": [230], "boundingBox": "empty"}, {"id": 149, "name": "red_flower", "displayName": "<PERSON><PERSON>", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 6001, "minStateId": 6001, "maxStateId": 6011, "drops": [218], "boundingBox": "empty"}, {"id": 159, "name": "wither_rose", "displayName": "<PERSON><PERSON>", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11332, "minStateId": 11332, "maxStateId": 11332, "drops": [229], "boundingBox": "empty"}, {"id": 161, "name": "brown_mushroom", "displayName": "Brown Mushroom", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 1, "filterLight": 0, "defaultState": 5907, "minStateId": 5907, "maxStateId": 5907, "drops": [233], "boundingBox": "empty"}, {"id": 162, "name": "red_mushroom", "displayName": "Red Mushroom", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7471, "minStateId": 7471, "maxStateId": 7471, "drops": [234], "boundingBox": "empty"}, {"id": 163, "name": "gold_block", "displayName": "Block of Gold", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 784, "minStateId": 784, "maxStateId": 784, "harvestTools": {"831": true, "836": true, "841": true}, "drops": [89], "boundingBox": "block"}, {"id": 164, "name": "iron_block", "displayName": "Block of Iron", "hardness": 5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 14089, "minStateId": 14089, "maxStateId": 14089, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [87], "boundingBox": "block"}, {"id": 165, "name": "brick_block", "displayName": "Bricks", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 8121, "minStateId": 8121, "maxStateId": 8121, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [284], "boundingBox": "block"}, {"id": 166, "name": "tnt", "displayName": "TNT", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11910, "minStateId": 11910, "maxStateId": 11913, "drops": [678], "boundingBox": "block"}, {"id": 167, "name": "bookshelf", "displayName": "Bookshelf", "hardness": 1.5, "resistance": 1.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11885, "minStateId": 11885, "maxStateId": 11885, "drops": [922], "boundingBox": "block"}, {"id": 168, "name": "chiseled_bookshelf", "displayName": "Chiseled Bookshelf", "hardness": 1.5, "resistance": 1.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 787, "minStateId": 787, "maxStateId": 1042, "drops": [], "boundingBox": "block"}, {"id": 169, "name": "mossy_cobblestone", "displayName": "<PERSON><PERSON>", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 683, "minStateId": 683, "maxStateId": 683, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [288], "boundingBox": "block"}, {"id": 170, "name": "obsidian", "displayName": "Obsidian", "hardness": 50, "resistance": 1200, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1192, "minStateId": 1192, "maxStateId": 1192, "harvestTools": {"836": true, "841": true}, "drops": [289], "boundingBox": "block"}, {"id": 171, "name": "torch", "displayName": "<PERSON>ch", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 14, "filterLight": 0, "defaultState": 2191, "minStateId": 2191, "maxStateId": 2196, "drops": [290], "boundingBox": "empty"}, {"id": 173, "name": "fire", "displayName": "Fire", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 15, "filterLight": 0, "defaultState": 11193, "minStateId": 11193, "maxStateId": 11208, "drops": [], "boundingBox": "empty"}, {"id": 174, "name": "soul_fire", "displayName": "Soul Fire", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 10, "filterLight": 0, "defaultState": 1165, "minStateId": 1165, "maxStateId": 1180, "drops": [], "boundingBox": "empty"}, {"id": 175, "name": "mob_spawner", "displayName": "Monster Spawner", "hardness": 5, "resistance": 5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 1163, "minStateId": 1163, "maxStateId": 1163, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [], "boundingBox": "block"}, {"id": 176, "name": "oak_stairs", "displayName": "Oak Stairs", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 728, "minStateId": 728, "maxStateId": 735, "drops": [382], "boundingBox": "block"}, {"id": 177, "name": "chest", "displayName": "Chest", "hardness": 2.5, "resistance": 2.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 12381, "minStateId": 12381, "maxStateId": 12384, "drops": [298], "boundingBox": "block"}, {"id": 178, "name": "redstone_wire", "displayName": "Redstone Wire", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 6192, "minStateId": 6192, "maxStateId": 6207, "drops": [656], "boundingBox": "empty"}, {"id": 179, "name": "diamond_ore", "displayName": "Diamond Ore", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7207, "minStateId": 7207, "maxStateId": 7207, "harvestTools": {"831": true, "836": true, "841": true}, "drops": [801], "boundingBox": "block"}, {"id": 180, "name": "deepslate_diamond_ore", "displayName": "Deepslate Diamond Ore", "hardness": 4.5, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13831, "minStateId": 13831, "maxStateId": 13831, "harvestTools": {"831": true, "836": true, "841": true}, "drops": [801], "boundingBox": "block"}, {"id": 181, "name": "diamond_block", "displayName": "Block of Diamond", "hardness": 5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 727, "minStateId": 727, "maxStateId": 727, "harvestTools": {"831": true, "836": true, "841": true}, "drops": [90], "boundingBox": "block"}, {"id": 182, "name": "crafting_table", "displayName": "Crafting Table", "hardness": 2.5, "resistance": 2.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 10104, "minStateId": 10104, "maxStateId": 10104, "drops": [299], "boundingBox": "block"}, {"id": 183, "name": "wheat", "displayName": "Wheat Crops", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12985, "minStateId": 12985, "maxStateId": 12992, "drops": [850], "boundingBox": "empty"}, {"id": 184, "name": "farmland", "displayName": "Farmland", "hardness": 0.6, "resistance": 0.6, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6697, "minStateId": 6697, "maxStateId": 6704, "drops": [28], "boundingBox": "block"}, {"id": 185, "name": "lit_furnace", "displayName": "Furnace", "hardness": 3.5, "resistance": 3.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12926, "minStateId": 12926, "maxStateId": 12929, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [301], "boundingBox": "block"}, {"id": 8003, "name": "furnace", "displayName": "Furnace", "hardness": 3.5, "resistance": 3.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13608, "minStateId": 13608, "maxStateId": 13611, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [301], "boundingBox": "block"}, {"id": 186, "name": "standing_sign", "displayName": "Oak Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9427, "minStateId": 9427, "maxStateId": 9442, "drops": [883], "boundingBox": "empty"}, {"id": 187, "name": "spruce_standing_sign", "displayName": "Spruce Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12277, "minStateId": 12277, "maxStateId": 12292, "drops": [884], "boundingBox": "empty"}, {"id": 188, "name": "birch_standing_sign", "displayName": "Birch Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13, "minStateId": 13, "maxStateId": 28, "drops": [885], "boundingBox": "empty"}, {"id": 189, "name": "acacia_standing_sign", "displayName": "Acacia Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11396, "minStateId": 11396, "maxStateId": 11411, "drops": [887], "boundingBox": "empty"}, {"id": 190, "name": "cherry_standing_sign", "displayName": "Cherry Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 10113, "minStateId": 10113, "maxStateId": 10128, "drops": [888], "boundingBox": "empty"}, {"id": 191, "name": "jungle_standing_sign", "displayName": "Jungle Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11169, "minStateId": 11169, "maxStateId": 11184, "drops": [886], "boundingBox": "empty"}, {"id": 192, "name": "darkoak_standing_sign", "displayName": "Dark Oak Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13029, "minStateId": 13029, "maxStateId": 13044, "drops": [889], "boundingBox": "empty"}, {"id": 193, "name": "mangrove_standing_sign", "displayName": "Mangrove Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7016, "minStateId": 7016, "maxStateId": 7031, "drops": [890], "boundingBox": "empty"}, {"id": 194, "name": "bamboo_standing_sign", "displayName": "Bamboo Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12878, "minStateId": 12878, "maxStateId": 12893, "drops": [891], "boundingBox": "empty"}, {"id": 195, "name": "wooden_door", "displayName": "Oak Door", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 6123, "minStateId": 6123, "maxStateId": 6154, "drops": [710], "boundingBox": "block"}, {"id": 196, "name": "ladder", "displayName": "Ladder", "hardness": 0.4, "resistance": 0.4, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 14090, "minStateId": 14090, "maxStateId": 14095, "drops": [302], "boundingBox": "block"}, {"id": 197, "name": "rail", "displayName": "Rail", "hardness": 0.7, "resistance": 0.7, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 6705, "minStateId": 6705, "maxStateId": 6714, "drops": [762], "boundingBox": "empty"}, {"id": 198, "name": "stone_stairs", "displayName": "Cobblestone Stairs", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6096, "minStateId": 6096, "maxStateId": 6103, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [303], "boundingBox": "block"}, {"id": 199, "name": "wall_sign", "displayName": "Oak Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 8639, "minStateId": 8639, "maxStateId": 8644, "drops": [883], "boundingBox": "empty"}, {"id": 200, "name": "spruce_wall_sign", "displayName": "Spruce Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13091, "minStateId": 13091, "maxStateId": 13096, "drops": [884], "boundingBox": "empty"}, {"id": 201, "name": "birch_wall_sign", "displayName": "Birch Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12101, "minStateId": 12101, "maxStateId": 12106, "drops": [885], "boundingBox": "empty"}, {"id": 202, "name": "acacia_wall_sign", "displayName": "Acacia Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 6047, "minStateId": 6047, "maxStateId": 6052, "drops": [887], "boundingBox": "empty"}, {"id": 203, "name": "cherry_wall_sign", "displayName": "Cherry Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13440, "minStateId": 13440, "maxStateId": 13445, "drops": [888], "boundingBox": "empty"}, {"id": 204, "name": "jungle_wall_sign", "displayName": "Jungle Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7229, "minStateId": 7229, "maxStateId": 7234, "drops": [886], "boundingBox": "empty"}, {"id": 205, "name": "darkoak_wall_sign", "displayName": "Dark Oak Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9126, "minStateId": 9126, "maxStateId": 9131, "drops": [889], "boundingBox": "empty"}, {"id": 206, "name": "mangrove_wall_sign", "displayName": "Mangrove Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9071, "minStateId": 9071, "maxStateId": 9076, "drops": [890], "boundingBox": "empty"}, {"id": 207, "name": "bamboo_wall_sign", "displayName": "Bamboo Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12107, "minStateId": 12107, "maxStateId": 12112, "drops": [891], "boundingBox": "empty"}, {"id": 208, "name": "oak_hanging_sign", "displayName": "Oak Hanging Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 8180, "minStateId": 8180, "maxStateId": 8563, "drops": [894], "boundingBox": "empty"}, {"id": 209, "name": "spruce_hanging_sign", "displayName": "Spruce Hanging Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9661, "minStateId": 9661, "maxStateId": 10044, "drops": [895], "boundingBox": "empty"}, {"id": 210, "name": "birch_hanging_sign", "displayName": "<PERSON> Hanging Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 1394, "minStateId": 1394, "maxStateId": 1777, "drops": [896], "boundingBox": "empty"}, {"id": 211, "name": "acacia_hanging_sign", "displayName": "Acacia Hanging Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 4940, "minStateId": 4940, "maxStateId": 5323, "drops": [898], "boundingBox": "empty"}, {"id": 212, "name": "cherry_hanging_sign", "displayName": "Cherry Hanging Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 173, "minStateId": 173, "maxStateId": 556, "drops": [899], "boundingBox": "empty"}, {"id": 213, "name": "jungle_hanging_sign", "displayName": "Jungle Hanging Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 6225, "minStateId": 6225, "maxStateId": 6608, "drops": [897], "boundingBox": "empty"}, {"id": 214, "name": "dark_oak_hanging_sign", "displayName": "Dark Oak Hanging Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 5522, "minStateId": 5522, "maxStateId": 5905, "drops": [900], "boundingBox": "empty"}, {"id": 215, "name": "crimson_hanging_sign", "displayName": "Crimson Hanging Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12410, "minStateId": 12410, "maxStateId": 12793, "drops": [903], "boundingBox": "empty"}, {"id": 216, "name": "warped_hanging_sign", "displayName": "Warped Hanging Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 10745, "minStateId": 10745, "maxStateId": 11128, "drops": [904], "boundingBox": "empty"}, {"id": 217, "name": "mangrove_hanging_sign", "displayName": "Mangrove Hanging Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 10130, "minStateId": 10130, "maxStateId": 10513, "drops": [901], "boundingBox": "empty"}, {"id": 218, "name": "bamboo_hanging_sign", "displayName": "Bamboo Hanging Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7573, "minStateId": 7573, "maxStateId": 7956, "drops": [902], "boundingBox": "empty"}, {"id": 230, "name": "lever", "displayName": "Lever", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11704, "minStateId": 11704, "maxStateId": 11719, "drops": [671], "boundingBox": "empty"}, {"id": 231, "name": "stone_pressure_plate", "displayName": "Stone Pressure Plate", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 6670, "minStateId": 6670, "maxStateId": 6685, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [694], "boundingBox": "empty"}, {"id": 232, "name": "iron_door", "displayName": "Iron Door", "hardness": 5, "resistance": 5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7305, "minStateId": 7305, "maxStateId": 7336, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [709], "boundingBox": "block"}, {"id": 233, "name": "wooden_pressure_plate", "displayName": "Oak Pressure Plate", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13857, "minStateId": 13857, "maxStateId": 13872, "drops": [698], "boundingBox": "empty"}, {"id": 234, "name": "spruce_pressure_plate", "displayName": "Spruce Pressure Plate", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 6157, "minStateId": 6157, "maxStateId": 6172, "drops": [699], "boundingBox": "empty"}, {"id": 235, "name": "birch_pressure_plate", "displayName": "Birch Pressure Plate", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 5934, "minStateId": 5934, "maxStateId": 5949, "drops": [700], "boundingBox": "empty"}, {"id": 236, "name": "jungle_pressure_plate", "displayName": "Jungle Pressure Plate", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 6022, "minStateId": 6022, "maxStateId": 6037, "drops": [701], "boundingBox": "empty"}, {"id": 237, "name": "acacia_pressure_plate", "displayName": "Acacia Pressure Plate", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9038, "minStateId": 9038, "maxStateId": 9053, "drops": [702], "boundingBox": "empty"}, {"id": 238, "name": "cherry_pressure_plate", "displayName": "Cherry Pressure Plate", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 157, "minStateId": 157, "maxStateId": 172, "drops": [703], "boundingBox": "empty"}, {"id": 239, "name": "dark_oak_pressure_plate", "displayName": "Dark Oak Pressure Plate", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 10660, "minStateId": 10660, "maxStateId": 10675, "drops": [704], "boundingBox": "empty"}, {"id": 240, "name": "mangrove_pressure_plate", "displayName": "Mangrove Pressure Plate", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 6650, "minStateId": 6650, "maxStateId": 6665, "drops": [705], "boundingBox": "empty"}, {"id": 241, "name": "bamboo_pressure_plate", "displayName": "Bamboo Pressure Plate", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11218, "minStateId": 11218, "maxStateId": 11233, "drops": [706], "boundingBox": "empty"}, {"id": 242, "name": "redstone_ore", "displayName": "Redstone Ore", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7095, "minStateId": 7095, "maxStateId": 7095, "harvestTools": {"831": true, "836": true, "841": true}, "drops": [656], "boundingBox": "block"}, {"id": 8004, "name": "lit_redstone_ore", "displayName": "Redstone Ore", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7968, "minStateId": 7968, "maxStateId": 7968, "harvestTools": {"831": true, "836": true, "841": true}, "drops": [656], "boundingBox": "block"}, {"id": 243, "name": "deepslate_redstone_ore", "displayName": "Deepslate Redstone Ore", "hardness": 4.5, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11828, "minStateId": 11828, "maxStateId": 11828, "harvestTools": {"831": true, "836": true, "841": true}, "drops": [656], "boundingBox": "block"}, {"id": 8005, "name": "lit_deepslate_redstone_ore", "displayName": "Deepslate Redstone Ore", "hardness": 4.5, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13316, "minStateId": 13316, "maxStateId": 13316, "harvestTools": {"831": true, "836": true, "841": true}, "drops": [656], "boundingBox": "block"}, {"id": 244, "name": "redstone_torch", "displayName": "Redstone Torch", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 7, "filterLight": 0, "defaultState": 5501, "minStateId": 5501, "maxStateId": 5506, "drops": [657], "boundingBox": "empty"}, {"id": 8006, "name": "unlit_redstone_torch", "displayName": "Redstone Torch", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 7, "filterLight": 0, "defaultState": 14054, "minStateId": 14054, "maxStateId": 14059, "drops": [657], "boundingBox": "empty"}, {"id": 246, "name": "stone_button", "displayName": "<PERSON>", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 1364, "minStateId": 1364, "maxStateId": 1375, "drops": [681], "boundingBox": "empty"}, {"id": 247, "name": "snow_layer", "displayName": "Snow", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 576, "minStateId": 576, "maxStateId": 591, "harvestTools": {"815": true, "820": true, "825": true, "830": true, "835": true, "840": true}, "drops": [], "boundingBox": "empty"}, {"id": 248, "name": "ice", "displayName": "Ice", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 11891, "minStateId": 11891, "maxStateId": 11891, "drops": [], "boundingBox": "block"}, {"id": 249, "name": "snow", "displayName": "Snow Block", "hardness": 0.2, "resistance": 0.2, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6997, "minStateId": 6997, "maxStateId": 6997, "harvestTools": {"815": true, "820": true, "825": true, "830": true, "835": true, "840": true}, "drops": [909], "boundingBox": "block"}, {"id": 250, "name": "cactus", "displayName": "Cactus", "hardness": 0.4, "resistance": 0.4, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 12208, "minStateId": 12208, "maxStateId": 12223, "drops": [307], "boundingBox": "block"}, {"id": 251, "name": "clay", "displayName": "<PERSON>", "hardness": 0.6, "resistance": 0.6, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12394, "minStateId": 12394, "maxStateId": 12394, "drops": [919], "boundingBox": "block"}, {"id": 252, "name": "reeds", "displayName": "Sugar Cane", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11142, "minStateId": 11142, "maxStateId": 11157, "drops": [242], "boundingBox": "empty"}, {"id": 253, "name": "jukebox", "displayName": "Jukebox", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 8645, "minStateId": 8645, "maxStateId": 8645, "drops": [309], "boundingBox": "block"}, {"id": 254, "name": "oak_fence", "displayName": "Oak Fence", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 9575, "minStateId": 9575, "maxStateId": 9575, "drops": [310], "boundingBox": "block"}, {"id": 255, "name": "netherrack", "displayName": "Netherrack", "hardness": 0.4, "resistance": 0.4, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12268, "minStateId": 12268, "maxStateId": 12268, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [324], "boundingBox": "block"}, {"id": 256, "name": "soul_sand", "displayName": "Soul Sand", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9660, "minStateId": 9660, "maxStateId": 9660, "drops": [325], "boundingBox": "block"}, {"id": 257, "name": "soul_soil", "displayName": "Soul Soil", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9659, "minStateId": 9659, "maxStateId": 9659, "drops": [326], "boundingBox": "block"}, {"id": 258, "name": "basalt", "displayName": "Basalt", "hardness": 1.25, "resistance": 4.2, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7199, "minStateId": 7199, "maxStateId": 7201, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [327], "boundingBox": "block"}, {"id": 259, "name": "polished_basalt", "displayName": "Polished Ba<PERSON>t", "hardness": 1.25, "resistance": 4.2, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 29, "minStateId": 29, "maxStateId": 31, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [328], "boundingBox": "block"}, {"id": 260, "name": "soul_torch", "displayName": "Soul Torch", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 10, "filterLight": 0, "defaultState": 7960, "minStateId": 7960, "maxStateId": 7965, "drops": [330], "boundingBox": "empty"}, {"id": 262, "name": "glowstone", "displayName": "Glowstone", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 15, "filterLight": 15, "defaultState": 6669, "minStateId": 6669, "maxStateId": 6669, "drops": [931], "boundingBox": "block"}, {"id": 263, "name": "portal", "displayName": "Nether Portal", "hardness": -1, "resistance": 0, "stackSize": 64, "diggable": false, "material": "default", "transparent": true, "emitLight": 11, "filterLight": 0, "defaultState": 13520, "minStateId": 13520, "maxStateId": 13522, "drops": [], "boundingBox": "empty"}, {"id": 264, "name": "carved_pumpkin", "displayName": "<PERSON><PERSON>", "hardness": 1, "resistance": 1, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13075, "minStateId": 13075, "maxStateId": 13078, "drops": [322], "boundingBox": "block"}, {"id": 265, "name": "lit_pumpkin", "displayName": "<PERSON>'<PERSON>", "hardness": 1, "resistance": 1, "stackSize": 64, "diggable": true, "material": "gourd;mineable/axe", "transparent": false, "emitLight": 15, "filterLight": 15, "defaultState": 11887, "minStateId": 11887, "maxStateId": 11890, "drops": [323], "boundingBox": "block"}, {"id": 266, "name": "cake", "displayName": "Cake", "hardness": 0.5, "resistance": 0.5, "stackSize": 1, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 12403, "minStateId": 12403, "maxStateId": 12409, "drops": [], "boundingBox": "block"}, {"id": 267, "name": "powered_repeater", "displayName": "Redstone Repeater", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 7419, "minStateId": 7419, "maxStateId": 7434, "drops": [659], "boundingBox": "block"}, {"id": 8007, "name": "unpowered_repeater", "displayName": "Redstone Repeater", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 9354, "minStateId": 9354, "maxStateId": 9369, "drops": [659], "boundingBox": "block"}, {"id": 268, "name": "white_stained_glass", "displayName": "White Stained Glass", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 8618, "minStateId": 8618, "maxStateId": 8618, "drops": [], "boundingBox": "block"}, {"id": 269, "name": "orange_stained_glass", "displayName": "Orange Stained Glass", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7413, "minStateId": 7413, "maxStateId": 7413, "drops": [], "boundingBox": "block"}, {"id": 270, "name": "magenta_stained_glass", "displayName": "Magenta Stained Glass", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12911, "minStateId": 12911, "maxStateId": 12911, "drops": [], "boundingBox": "block"}, {"id": 271, "name": "light_blue_stained_glass", "displayName": "Light Blue Stained Glass", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9616, "minStateId": 9616, "maxStateId": 9616, "drops": [], "boundingBox": "block"}, {"id": 272, "name": "yellow_stained_glass", "displayName": "Yellow Stained Glass", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13293, "minStateId": 13293, "maxStateId": 13293, "drops": [], "boundingBox": "block"}, {"id": 273, "name": "lime_stained_glass", "displayName": "Lime Stained Glass", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 129, "minStateId": 129, "maxStateId": 129, "drops": [], "boundingBox": "block"}, {"id": 274, "name": "pink_stained_glass", "displayName": "Pink Stained Glass", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7166, "minStateId": 7166, "maxStateId": 7166, "drops": [], "boundingBox": "block"}, {"id": 275, "name": "gray_stained_glass", "displayName": "<PERSON> Stained Glass", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 6105, "minStateId": 6105, "maxStateId": 6105, "drops": [], "boundingBox": "block"}, {"id": 276, "name": "light_gray_stained_glass", "displayName": "Light Gray Stained Glass", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 1191, "minStateId": 1191, "maxStateId": 1191, "drops": [], "boundingBox": "block"}, {"id": 277, "name": "cyan_stained_glass", "displayName": "<PERSON><PERSON>", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 10582, "minStateId": 10582, "maxStateId": 10582, "drops": [], "boundingBox": "block"}, {"id": 278, "name": "purple_stained_glass", "displayName": "Purple Stained Glass", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 2645, "minStateId": 2645, "maxStateId": 2645, "drops": [], "boundingBox": "block"}, {"id": 279, "name": "blue_stained_glass", "displayName": "Blue Stained Glass", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9580, "minStateId": 9580, "maxStateId": 9580, "drops": [], "boundingBox": "block"}, {"id": 280, "name": "brown_stained_glass", "displayName": "<PERSON> Stained Glass", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 1788, "minStateId": 1788, "maxStateId": 1788, "drops": [], "boundingBox": "block"}, {"id": 281, "name": "green_stained_glass", "displayName": "Green Stained Glass", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7098, "minStateId": 7098, "maxStateId": 7098, "drops": [], "boundingBox": "block"}, {"id": 282, "name": "red_stained_glass", "displayName": "Red Stained Glass", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9037, "minStateId": 9037, "maxStateId": 9037, "drops": [], "boundingBox": "block"}, {"id": 283, "name": "black_stained_glass", "displayName": "Black Stained Glass", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 10583, "minStateId": 10583, "maxStateId": 10583, "drops": [], "boundingBox": "block"}, {"id": 284, "name": "trapdoor", "displayName": "Oak Trapdoor", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 660, "minStateId": 660, "maxStateId": 675, "drops": [730], "boundingBox": "block"}, {"id": 285, "name": "spruce_trapdoor", "displayName": "Spruce Trapdoor", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11762, "minStateId": 11762, "maxStateId": 11777, "drops": [731], "boundingBox": "block"}, {"id": 286, "name": "birch_trapdoor", "displayName": "<PERSON>", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11863, "minStateId": 11863, "maxStateId": 11878, "drops": [732], "boundingBox": "block"}, {"id": 287, "name": "jungle_trapdoor", "displayName": "Jungle Trapdoor", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9171, "minStateId": 9171, "maxStateId": 9186, "drops": [733], "boundingBox": "block"}, {"id": 288, "name": "acacia_trapdoor", "displayName": "Acacia T<PERSON>door", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9411, "minStateId": 9411, "maxStateId": 9426, "drops": [734], "boundingBox": "block"}, {"id": 289, "name": "cherry_trapdoor", "displayName": "Cherry Trapdoor", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 2377, "minStateId": 2377, "maxStateId": 2392, "drops": [735], "boundingBox": "block"}, {"id": 290, "name": "dark_oak_trapdoor", "displayName": "Dark Oak Trapdoor", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13202, "minStateId": 13202, "maxStateId": 13217, "drops": [736], "boundingBox": "block"}, {"id": 291, "name": "mangrove_trapdoor", "displayName": "Mangrove Trapdoor", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7346, "minStateId": 7346, "maxStateId": 7361, "drops": [737], "boundingBox": "block"}, {"id": 292, "name": "bamboo_trapdoor", "displayName": "Bamboo Trapdoor", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9054, "minStateId": 9054, "maxStateId": 9069, "drops": [738], "boundingBox": "block"}, {"id": 293, "name": "stonebrick", "displayName": "Stone Bricks", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11757, "minStateId": 11757, "maxStateId": 11761, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [339], "boundingBox": "block"}, {"id": 297, "name": "packed_mud", "displayName": "Packed Mud", "hardness": 1, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 744, "minStateId": 744, "maxStateId": 744, "drops": [343], "boundingBox": "block"}, {"id": 298, "name": "mud_bricks", "displayName": "Mud Bricks", "hardness": 1.5, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12100, "minStateId": 12100, "maxStateId": 12100, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [344], "boundingBox": "block"}, {"id": 299, "name": "monster_egg", "displayName": "Infested Stone", "hardness": 0.75, "resistance": 0.75, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6930, "minStateId": 6930, "maxStateId": 6935, "drops": [], "boundingBox": "block"}, {"id": 305, "name": "brown_mushroom_block", "displayName": "Brown Mushroom Block", "hardness": 0.2, "resistance": 0.2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13048, "minStateId": 13048, "maxStateId": 13063, "drops": [233], "boundingBox": "block"}, {"id": 306, "name": "red_mushroom_block", "displayName": "Red Mushroom Block", "hardness": 0.2, "resistance": 0.2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 5979, "minStateId": 5979, "maxStateId": 5994, "drops": [0], "boundingBox": "block"}, {"id": 308, "name": "iron_bars", "displayName": "Iron Bars", "hardness": 5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 8174, "minStateId": 8174, "maxStateId": 8174, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [354], "boundingBox": "block"}, {"id": 309, "name": "chain", "displayName": "Chain", "hardness": 5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12391, "minStateId": 12391, "maxStateId": 12393, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [355], "boundingBox": "block"}, {"id": 310, "name": "glass_pane", "displayName": "Glass Pane", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9023, "minStateId": 9023, "maxStateId": 9023, "drops": [], "boundingBox": "block"}, {"id": 311, "name": "pumpkin", "displayName": "<PERSON><PERSON><PERSON>", "hardness": 1, "resistance": 1, "stackSize": 64, "diggable": true, "material": "gourd;mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7444, "minStateId": 7444, "maxStateId": 7447, "drops": [321], "boundingBox": "block"}, {"id": 312, "name": "melon_block", "displayName": "Melon", "hardness": 1, "resistance": 1, "stackSize": 64, "diggable": true, "material": "gourd;mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1153, "minStateId": 1153, "maxStateId": 1153, "drops": [981], "boundingBox": "block"}, {"id": 313, "name": "pumpkin_stem", "displayName": "Attached P<PERSON><PERSON> Stem", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11441, "minStateId": 11441, "maxStateId": 11488, "drops": [983], "boundingBox": "empty"}, {"id": 314, "name": "melon_stem", "displayName": "Attached Melon Stem", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 8565, "minStateId": 8565, "maxStateId": 8612, "drops": [984], "boundingBox": "empty"}, {"id": 317, "name": "vine", "displayName": "Vines", "hardness": 0.2, "resistance": 0.2, "stackSize": 64, "diggable": true, "material": "vine_or_glow_lichen;plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 2361, "minStateId": 2361, "maxStateId": 2376, "drops": [], "boundingBox": "empty"}, {"id": 318, "name": "glow_lichen", "displayName": "Glow Lichen", "hardness": 0.2, "resistance": 0.2, "stackSize": 64, "diggable": true, "material": "vine_or_glow_lichen;plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9444, "minStateId": 9444, "maxStateId": 9507, "drops": [], "boundingBox": "empty"}, {"id": 319, "name": "fence_gate", "displayName": "Oak Fence Gate", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 88, "minStateId": 88, "maxStateId": 103, "drops": [749], "boundingBox": "block"}, {"id": 320, "name": "brick_stairs", "displayName": "Brick Stairs", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11723, "minStateId": 11723, "maxStateId": 11730, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [360], "boundingBox": "block"}, {"id": 321, "name": "stone_brick_stairs", "displayName": "Stone Brick Stairs", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 2415, "minStateId": 2415, "maxStateId": 2422, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [361], "boundingBox": "block"}, {"id": 322, "name": "mud_brick_stairs", "displayName": "Mud Brick Stairs", "hardness": 1.5, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 9346, "minStateId": 9346, "maxStateId": 9353, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [362], "boundingBox": "block"}, {"id": 323, "name": "mycelium", "displayName": "Mycelium", "hardness": 0.6, "resistance": 0.6, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6071, "minStateId": 6071, "maxStateId": 6071, "drops": [28], "boundingBox": "block"}, {"id": 324, "name": "waterlily", "displayName": "<PERSON>", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 2618, "minStateId": 2618, "maxStateId": 2618, "drops": [364], "boundingBox": "block"}, {"id": 325, "name": "nether_brick", "displayName": "Nether Bricks", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12972, "minStateId": 12972, "maxStateId": 12972, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [365], "boundingBox": "block"}, {"id": 326, "name": "nether_brick_fence", "displayName": "Nether Brick Fence", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 7099, "minStateId": 7099, "maxStateId": 7099, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [368], "boundingBox": "block"}, {"id": 327, "name": "nether_brick_stairs", "displayName": "Nether Brick Stairs", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 118, "minStateId": 118, "maxStateId": 125, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [369], "boundingBox": "block"}, {"id": 328, "name": "nether_wart", "displayName": "Nether Wart", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13467, "minStateId": 13467, "maxStateId": 13470, "drops": [994], "boundingBox": "empty"}, {"id": 329, "name": "enchanting_table", "displayName": "Enchanting Table", "hardness": 5, "resistance": 1200, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 7, "filterLight": 0, "defaultState": 11933, "minStateId": 11933, "maxStateId": 11933, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [374], "boundingBox": "block"}, {"id": 330, "name": "brewing_stand", "displayName": "Brewing Stand", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 1, "filterLight": 0, "defaultState": 13251, "minStateId": 13251, "maxStateId": 13258, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1001], "boundingBox": "block"}, {"id": 331, "name": "cauldron", "displayName": "<PERSON><PERSON><PERSON>", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13155, "minStateId": 13155, "maxStateId": 13175, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1002], "boundingBox": "block"}, {"id": 335, "name": "end_portal", "displayName": "End Portal", "hardness": -1, "resistance": 3600000, "stackSize": 64, "diggable": false, "material": "default", "transparent": true, "emitLight": 15, "filterLight": 0, "defaultState": 13292, "minStateId": 13292, "maxStateId": 13292, "drops": [], "boundingBox": "empty"}, {"id": 336, "name": "end_portal_frame", "displayName": "End Portal Frame", "hardness": -1, "resistance": 3600000, "stackSize": 64, "diggable": false, "material": "default", "transparent": false, "emitLight": 1, "filterLight": 0, "defaultState": 11210, "minStateId": 11210, "maxStateId": 11217, "drops": [], "boundingBox": "block"}, {"id": 337, "name": "end_stone", "displayName": "End Stone", "hardness": 3, "resistance": 9, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6615, "minStateId": 6615, "maxStateId": 6615, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [376], "boundingBox": "block"}, {"id": 338, "name": "dragon_egg", "displayName": "Dragon Egg", "hardness": 3, "resistance": 9, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 1, "filterLight": 0, "defaultState": 12970, "minStateId": 12970, "maxStateId": 12970, "drops": [378], "boundingBox": "block"}, {"id": 339, "name": "redstone_lamp", "displayName": "Redstone Lamp", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 682, "minStateId": 682, "maxStateId": 682, "drops": [679], "boundingBox": "block"}, {"id": 8008, "name": "lit_redstone_lamp", "displayName": "Redstone Lamp", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7204, "minStateId": 7204, "maxStateId": 7204, "drops": [679], "boundingBox": "block"}, {"id": 340, "name": "cocoa", "displayName": "Cocoa", "hardness": 0.2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11692, "minStateId": 11692, "maxStateId": 11703, "drops": [940], "boundingBox": "block"}, {"id": 341, "name": "sandstone_stairs", "displayName": "Sandstone Stairs", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 5966, "minStateId": 5966, "maxStateId": 5973, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [379], "boundingBox": "block"}, {"id": 342, "name": "emerald_ore", "displayName": "Emerald Ore", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13047, "minStateId": 13047, "maxStateId": 13047, "harvestTools": {"831": true, "836": true, "841": true}, "drops": [802], "boundingBox": "block"}, {"id": 343, "name": "deepslate_emerald_ore", "displayName": "Deepslate Emerald Ore", "hardness": 4.5, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11501, "minStateId": 11501, "maxStateId": 11501, "harvestTools": {"831": true, "836": true, "841": true}, "drops": [802], "boundingBox": "block"}, {"id": 344, "name": "ender_chest", "displayName": "<PERSON><PERSON> Chest", "hardness": 22.5, "resistance": 600, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 7, "filterLight": 0, "defaultState": 7216, "minStateId": 7216, "maxStateId": 7219, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [289], "boundingBox": "block"}, {"id": 345, "name": "tripwire_hook", "displayName": "Tripwire Hook", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 10618, "minStateId": 10618, "maxStateId": 10633, "drops": [676], "boundingBox": "empty"}, {"id": 346, "name": "trip_wire", "displayName": "Tripwire", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13139, "minStateId": 13139, "maxStateId": 13154, "drops": [847], "boundingBox": "empty"}, {"id": 347, "name": "emerald_block", "displayName": "Block of Emerald", "hardness": 5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 2620, "minStateId": 2620, "maxStateId": 2620, "harvestTools": {"831": true, "836": true, "841": true}, "drops": [381], "boundingBox": "block"}, {"id": 348, "name": "spruce_stairs", "displayName": "Spruce Stairs", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 143, "minStateId": 143, "maxStateId": 150, "drops": [383], "boundingBox": "block"}, {"id": 349, "name": "birch_stairs", "displayName": "<PERSON> Stairs", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 12228, "minStateId": 12228, "maxStateId": 12235, "drops": [384], "boundingBox": "block"}, {"id": 350, "name": "jungle_stairs", "displayName": "Jungle Stairs", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 12190, "minStateId": 12190, "maxStateId": 12197, "drops": [385], "boundingBox": "block"}, {"id": 351, "name": "command_block", "displayName": "Command Block", "hardness": -1, "resistance": 3600000, "stackSize": 64, "diggable": false, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13535, "minStateId": 13535, "maxStateId": 13546, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 352, "name": "beacon", "displayName": "Beacon", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 15, "filterLight": 1, "defaultState": 566, "minStateId": 566, "maxStateId": 566, "drops": [395], "boundingBox": "block"}, {"id": 353, "name": "cobblestone_wall", "displayName": "Cobblestone Wall", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 2657, "minStateId": 2657, "maxStateId": 4924, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [396], "boundingBox": "block"}, {"id": 383, "name": "carrots", "displayName": "Carrots", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 10565, "minStateId": 10565, "maxStateId": 10572, "drops": [1090], "boundingBox": "empty"}, {"id": 384, "name": "potatoes", "displayName": "Potatoes", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 1088, "minStateId": 1088, "maxStateId": 1095, "drops": [1091], "boundingBox": "empty"}, {"id": 385, "name": "wooden_button", "displayName": "Oak Button", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11543, "minStateId": 11543, "maxStateId": 11554, "drops": [683], "boundingBox": "empty"}, {"id": 386, "name": "spruce_button", "displayName": "Spruce Button", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7168, "minStateId": 7168, "maxStateId": 7179, "drops": [684], "boundingBox": "empty"}, {"id": 387, "name": "birch_button", "displayName": "<PERSON>", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13523, "minStateId": 13523, "maxStateId": 13534, "drops": [685], "boundingBox": "empty"}, {"id": 388, "name": "jungle_button", "displayName": "<PERSON>ton", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 131, "minStateId": 131, "maxStateId": 142, "drops": [686], "boundingBox": "empty"}, {"id": 389, "name": "acacia_button", "displayName": "Acacia <PERSON>", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12913, "minStateId": 12913, "maxStateId": 12924, "drops": [687], "boundingBox": "empty"}, {"id": 390, "name": "cherry_button", "displayName": "<PERSON>", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7511, "minStateId": 7511, "maxStateId": 7522, "drops": [688], "boundingBox": "empty"}, {"id": 391, "name": "dark_oak_button", "displayName": "Dark Oak Button", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 105, "minStateId": 105, "maxStateId": 116, "drops": [689], "boundingBox": "empty"}, {"id": 392, "name": "mangrove_button", "displayName": "Mangrove Button", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12293, "minStateId": 12293, "maxStateId": 12304, "drops": [690], "boundingBox": "empty"}, {"id": 393, "name": "bamboo_button", "displayName": "Bamboo Button", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11665, "minStateId": 11665, "maxStateId": 11676, "drops": [691], "boundingBox": "empty"}, {"id": 406, "name": "skull", "displayName": "<PERSON><PERSON>", "hardness": 1, "resistance": 1, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 9088, "minStateId": 9088, "maxStateId": 9093, "drops": [1102], "boundingBox": "block"}, {"id": 408, "name": "anvil", "displayName": "An<PERSON>", "hardness": 5, "resistance": 1200, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11847, "minStateId": 11847, "maxStateId": 11862, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [418], "boundingBox": "block"}, {"id": 411, "name": "trapped_chest", "displayName": "Trapped Chest", "hardness": 2.5, "resistance": 2.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 9407, "minStateId": 9407, "maxStateId": 9410, "drops": [677], "boundingBox": "block"}, {"id": 412, "name": "light_weighted_pressure_plate", "displayName": "Light Weighted Pressure Plate", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 6053, "minStateId": 6053, "maxStateId": 6068, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [696], "boundingBox": "empty"}, {"id": 413, "name": "heavy_weighted_pressure_plate", "displayName": "Heavy Weighted Pressure Plate", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 2629, "minStateId": 2629, "maxStateId": 2644, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [697], "boundingBox": "empty"}, {"id": 414, "name": "powered_comparator", "displayName": "Redstone Comparator", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 1129, "minStateId": 1129, "maxStateId": 1144, "drops": [660], "boundingBox": "block"}, {"id": 8009, "name": "unpowered_comparator", "displayName": "Redstone Comparator", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11503, "minStateId": 11503, "maxStateId": 11518, "drops": [660], "boundingBox": "block"}, {"id": 415, "name": "daylight_detector", "displayName": "Daylight Detector", "hardness": 0.2, "resistance": 0.2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 7000, "minStateId": 7000, "maxStateId": 7015, "drops": [673], "boundingBox": "block"}, {"id": 8010, "name": "daylight_detector_inverted", "displayName": "Daylight Detector", "hardness": 0.2, "resistance": 0.2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 7363, "minStateId": 7363, "maxStateId": 7378, "drops": [673], "boundingBox": "block"}, {"id": 416, "name": "redstone_block", "displayName": "Block of Redstone", "hardness": 5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6175, "minStateId": 6175, "maxStateId": 6175, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [658], "boundingBox": "block"}, {"id": 417, "name": "quartz_ore", "displayName": "<PERSON><PERSON>", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7362, "minStateId": 7362, "maxStateId": 7362, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [804], "boundingBox": "block"}, {"id": 418, "name": "hopper", "displayName": "<PERSON>", "hardness": 3, "resistance": 4.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12117, "minStateId": 12117, "maxStateId": 12128, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [666], "boundingBox": "block"}, {"id": 419, "name": "quartz_block", "displayName": "Block of Quartz", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6084, "minStateId": 6084, "maxStateId": 6095, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [422], "boundingBox": "block"}, {"id": 422, "name": "quartz_stairs", "displayName": "Quartz Stairs", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 8139, "minStateId": 8139, "maxStateId": 8146, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [425], "boundingBox": "block"}, {"id": 423, "name": "activator_rail", "displayName": "Activator Rail", "hardness": 0.7, "resistance": 0.7, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 1060, "minStateId": 1060, "maxStateId": 1071, "drops": [763], "boundingBox": "empty"}, {"id": 424, "name": "dropper", "displayName": "Dropper", "hardness": 3.5, "resistance": 3.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13079, "minStateId": 13079, "maxStateId": 13090, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [668], "boundingBox": "block"}, {"id": 425, "name": "white_terracotta", "displayName": "White Terracotta", "hardness": 1.25, "resistance": 4.2, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 8175, "minStateId": 8175, "maxStateId": 8175, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [426], "boundingBox": "block"}, {"id": 426, "name": "orange_terracotta", "displayName": "Orange Terracotta", "hardness": 1.25, "resistance": 4.2, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13243, "minStateId": 13243, "maxStateId": 13243, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [427], "boundingBox": "block"}, {"id": 427, "name": "magenta_terracotta", "displayName": "Magenta Terracotta", "hardness": 1.25, "resistance": 4.2, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7474, "minStateId": 7474, "maxStateId": 7474, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [428], "boundingBox": "block"}, {"id": 428, "name": "light_blue_terracotta", "displayName": "Light Blue Terracotta", "hardness": 1.25, "resistance": 4.2, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7203, "minStateId": 7203, "maxStateId": 7203, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [429], "boundingBox": "block"}, {"id": 429, "name": "yellow_terracotta", "displayName": "Yellow Terracotta", "hardness": 1.25, "resistance": 4.2, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6996, "minStateId": 6996, "maxStateId": 6996, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [430], "boundingBox": "block"}, {"id": 430, "name": "lime_terracotta", "displayName": "Lime Terracotta", "hardness": 1.25, "resistance": 4.2, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 14060, "minStateId": 14060, "maxStateId": 14060, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [431], "boundingBox": "block"}, {"id": 431, "name": "pink_terracotta", "displayName": "Pink Terracotta", "hardness": 1.25, "resistance": 4.2, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6949, "minStateId": 6949, "maxStateId": 6949, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [432], "boundingBox": "block"}, {"id": 432, "name": "gray_terracotta", "displayName": "Gray <PERSON>", "hardness": 1.25, "resistance": 4.2, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7451, "minStateId": 7451, "maxStateId": 7451, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [433], "boundingBox": "block"}, {"id": 433, "name": "light_gray_terracotta", "displayName": "Light Gray Terracotta", "hardness": 1.25, "resistance": 4.2, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1783, "minStateId": 1783, "maxStateId": 1783, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [434], "boundingBox": "block"}, {"id": 434, "name": "cyan_terracotta", "displayName": "<PERSON><PERSON>", "hardness": 1.25, "resistance": 4.2, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 0, "minStateId": 0, "maxStateId": 0, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [435], "boundingBox": "block"}, {"id": 435, "name": "purple_terracotta", "displayName": "Purple Terracotta", "hardness": 1.25, "resistance": 4.2, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11541, "minStateId": 11541, "maxStateId": 11541, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [436], "boundingBox": "block"}, {"id": 436, "name": "blue_terracotta", "displayName": "Blue Terracotta", "hardness": 1.25, "resistance": 4.2, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6040, "minStateId": 6040, "maxStateId": 6040, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [437], "boundingBox": "block"}, {"id": 437, "name": "brown_terracotta", "displayName": "Brown Terracotta", "hardness": 1.25, "resistance": 4.2, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13830, "minStateId": 13830, "maxStateId": 13830, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [438], "boundingBox": "block"}, {"id": 438, "name": "green_terracotta", "displayName": "Green Terracotta", "hardness": 1.25, "resistance": 4.2, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6106, "minStateId": 6106, "maxStateId": 6106, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [439], "boundingBox": "block"}, {"id": 439, "name": "red_terracotta", "displayName": "Red Terracotta", "hardness": 1.25, "resistance": 4.2, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 2434, "minStateId": 2434, "maxStateId": 2434, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [440], "boundingBox": "block"}, {"id": 440, "name": "black_terracotta", "displayName": "Black Terracotta", "hardness": 1.25, "resistance": 4.2, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11255, "minStateId": 11255, "maxStateId": 11255, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [441], "boundingBox": "block"}, {"id": 441, "name": "white_stained_glass_pane", "displayName": "White Stained Glass Pane", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7414, "minStateId": 7414, "maxStateId": 7414, "drops": [], "boundingBox": "block"}, {"id": 442, "name": "orange_stained_glass_pane", "displayName": "Orange Stained Glass Pane", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 651, "minStateId": 651, "maxStateId": 651, "drops": [], "boundingBox": "block"}, {"id": 443, "name": "magenta_stained_glass_pane", "displayName": "Magenta Stained Glass Pane", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 8173, "minStateId": 8173, "maxStateId": 8173, "drops": [], "boundingBox": "block"}, {"id": 444, "name": "light_blue_stained_glass_pane", "displayName": "Light Blue Stained Glass Pane", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 6668, "minStateId": 6668, "maxStateId": 6668, "drops": [], "boundingBox": "block"}, {"id": 445, "name": "yellow_stained_glass_pane", "displayName": "Yellow Stained Glass Pane", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 564, "minStateId": 564, "maxStateId": 564, "drops": [], "boundingBox": "block"}, {"id": 446, "name": "lime_stained_glass_pane", "displayName": "Lime Stained Glass Pane", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13291, "minStateId": 13291, "maxStateId": 13291, "drops": [], "boundingBox": "block"}, {"id": 447, "name": "pink_stained_glass_pane", "displayName": "Pink Stained Glass Pane", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12345, "minStateId": 12345, "maxStateId": 12345, "drops": [], "boundingBox": "block"}, {"id": 448, "name": "gray_stained_glass_pane", "displayName": "Gray Stained Glass Pane", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12116, "minStateId": 12116, "maxStateId": 12116, "drops": [], "boundingBox": "block"}, {"id": 449, "name": "light_gray_stained_glass_pane", "displayName": "Light Gray Stained Glass Pane", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 1193, "minStateId": 1193, "maxStateId": 1193, "drops": [], "boundingBox": "block"}, {"id": 450, "name": "cyan_stained_glass_pane", "displayName": "<PERSON><PERSON> Stained Glass Pane", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11678, "minStateId": 11678, "maxStateId": 11678, "drops": [], "boundingBox": "block"}, {"id": 451, "name": "purple_stained_glass_pane", "displayName": "Purple Stained Glass Pane", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 8622, "minStateId": 8622, "maxStateId": 8622, "drops": [], "boundingBox": "block"}, {"id": 452, "name": "blue_stained_glass_pane", "displayName": "Blue Stained Glass Pane", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 87, "minStateId": 87, "maxStateId": 87, "drops": [], "boundingBox": "block"}, {"id": 453, "name": "brown_stained_glass_pane", "displayName": "<PERSON> Stained Glass Pane", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 740, "minStateId": 740, "maxStateId": 740, "drops": [], "boundingBox": "block"}, {"id": 454, "name": "green_stained_glass_pane", "displayName": "Green Stained Glass Pane", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 6936, "minStateId": 6936, "maxStateId": 6936, "drops": [], "boundingBox": "block"}, {"id": 455, "name": "red_stained_glass_pane", "displayName": "Red Stained Glass Pane", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12340, "minStateId": 12340, "maxStateId": 12340, "drops": [], "boundingBox": "block"}, {"id": 456, "name": "black_stained_glass_pane", "displayName": "Black Stained Glass Pane", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 2656, "minStateId": 2656, "maxStateId": 2656, "drops": [], "boundingBox": "block"}, {"id": 457, "name": "acacia_stairs", "displayName": "Acacia Stairs", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11351, "minStateId": 11351, "maxStateId": 11358, "drops": [386], "boundingBox": "block"}, {"id": 458, "name": "cherry_stairs", "displayName": "<PERSON> Stairs", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 12395, "minStateId": 12395, "maxStateId": 12402, "drops": [387], "boundingBox": "block"}, {"id": 459, "name": "dark_oak_stairs", "displayName": "Dark Oak Stairs", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 8853, "minStateId": 8853, "maxStateId": 8860, "drops": [388], "boundingBox": "block"}, {"id": 460, "name": "mangrove_stairs", "displayName": "Mangrove Stairs", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 7483, "minStateId": 7483, "maxStateId": 7490, "drops": [389], "boundingBox": "block"}, {"id": 461, "name": "bamboo_stairs", "displayName": "Bamboo Stairs", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 2183, "minStateId": 2183, "maxStateId": 2190, "drops": [390], "boundingBox": "block"}, {"id": 462, "name": "bamboo_mosaic_stairs", "displayName": "Bamboo Mosaic Stairs", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11359, "minStateId": 11359, "maxStateId": 11366, "drops": [391], "boundingBox": "block"}, {"id": 463, "name": "slime", "displayName": "Slime Block", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 7036, "minStateId": 7036, "maxStateId": 7036, "drops": [663], "boundingBox": "block"}, {"id": 464, "name": "barrier", "displayName": "Barrier", "hardness": -1, "resistance": 3600000.8, "stackSize": 64, "diggable": false, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11159, "minStateId": 11159, "maxStateId": 11159, "drops": [], "boundingBox": "block"}, {"id": 465, "name": "light_block", "displayName": "Light", "hardness": -1, "resistance": 3600000.8, "stackSize": 64, "diggable": false, "material": "default", "transparent": true, "emitLight": 15, "filterLight": 0, "defaultState": 13785, "minStateId": 13785, "maxStateId": 13800, "drops": [], "boundingBox": "empty"}, {"id": 466, "name": "iron_trapdoor", "displayName": "Iron Trapdoor", "hardness": 5, "resistance": 5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 1072, "minStateId": 1072, "maxStateId": 1087, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [729], "boundingBox": "block"}, {"id": 467, "name": "prismarine", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11234, "minStateId": 11234, "maxStateId": 11236, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [502], "boundingBox": "block"}, {"id": 470, "name": "prismarine_stairs", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 12952, "minStateId": 12952, "maxStateId": 12959, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [505], "boundingBox": "block"}, {"id": 471, "name": "prismarine_bricks_stairs", "displayName": "Prismarine Brick Stairs", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 628, "minStateId": 628, "maxStateId": 635, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [506], "boundingBox": "block"}, {"id": 472, "name": "dark_prismarine_stairs", "displayName": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13128, "minStateId": 13128, "maxStateId": 13135, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [507], "boundingBox": "block"}, {"id": 473, "name": "stone_block_slab2", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 9617, "minStateId": 9617, "maxStateId": 9632, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [277], "boundingBox": "block"}, {"id": 8011, "name": "double_stone_block_slab2", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11623, "minStateId": 11623, "maxStateId": 11638, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [277], "boundingBox": "block"}, {"id": 476, "name": "sea_lantern", "displayName": "Sea Lantern", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 15, "filterLight": 15, "defaultState": 13250, "minStateId": 13250, "maxStateId": 13250, "drops": [1110], "boundingBox": "block"}, {"id": 477, "name": "hay_block", "displayName": "<PERSON>", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/hoe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1985, "minStateId": 1985, "maxStateId": 1996, "drops": [444], "boundingBox": "block"}, {"id": 478, "name": "white_carpet", "displayName": "White Carpet", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 12960, "minStateId": 12960, "maxStateId": 12960, "drops": [445], "boundingBox": "block"}, {"id": 479, "name": "orange_carpet", "displayName": "Orange Carpet", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 12305, "minStateId": 12305, "maxStateId": 12305, "drops": [446], "boundingBox": "block"}, {"id": 480, "name": "magenta_carpet", "displayName": "Magenta Carpet", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 687, "minStateId": 687, "maxStateId": 687, "drops": [447], "boundingBox": "block"}, {"id": 481, "name": "light_blue_carpet", "displayName": "Light Blue Carpet", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 8179, "minStateId": 8179, "maxStateId": 8179, "drops": [448], "boundingBox": "block"}, {"id": 482, "name": "yellow_carpet", "displayName": "Yellow Carpet", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 10581, "minStateId": 10581, "maxStateId": 10581, "drops": [449], "boundingBox": "block"}, {"id": 483, "name": "lime_carpet", "displayName": "Lime Carpet", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11928, "minStateId": 11928, "maxStateId": 11928, "drops": [450], "boundingBox": "block"}, {"id": 484, "name": "pink_carpet", "displayName": "Pink Carpet", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13413, "minStateId": 13413, "maxStateId": 13413, "drops": [451], "boundingBox": "block"}, {"id": 485, "name": "gray_carpet", "displayName": "<PERSON> Carpet", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 653, "minStateId": 653, "maxStateId": 653, "drops": [452], "boundingBox": "block"}, {"id": 486, "name": "light_gray_carpet", "displayName": "Light Gray Carpet", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 14087, "minStateId": 14087, "maxStateId": 14087, "drops": [453], "boundingBox": "block"}, {"id": 487, "name": "cyan_carpet", "displayName": "<PERSON><PERSON>", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6000, "minStateId": 6000, "maxStateId": 6000, "drops": [454], "boundingBox": "block"}, {"id": 488, "name": "purple_carpet", "displayName": "Purple Carpet", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13471, "minStateId": 13471, "maxStateId": 13471, "drops": [455], "boundingBox": "block"}, {"id": 489, "name": "blue_carpet", "displayName": "Blue Carpet", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 600, "minStateId": 600, "maxStateId": 600, "drops": [456], "boundingBox": "block"}, {"id": 490, "name": "brown_carpet", "displayName": "Brown Carpet", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 2410, "minStateId": 2410, "maxStateId": 2410, "drops": [457], "boundingBox": "block"}, {"id": 491, "name": "green_carpet", "displayName": "Green Carpet", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6122, "minStateId": 6122, "maxStateId": 6122, "drops": [458], "boundingBox": "block"}, {"id": 492, "name": "red_carpet", "displayName": "Red Carpet", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13118, "minStateId": 13118, "maxStateId": 13118, "drops": [459], "boundingBox": "block"}, {"id": 493, "name": "black_carpet", "displayName": "Black Carpet", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11168, "minStateId": 11168, "maxStateId": 11168, "drops": [460], "boundingBox": "block"}, {"id": 494, "name": "hardened_clay", "displayName": "Terracotta", "hardness": 1.25, "resistance": 4.2, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1393, "minStateId": 1393, "maxStateId": 1393, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [461], "boundingBox": "block"}, {"id": 495, "name": "coal_block", "displayName": "Block of Coal", "hardness": 5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9188, "minStateId": 9188, "maxStateId": 9188, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [81], "boundingBox": "block"}, {"id": 496, "name": "packed_ice", "displayName": "Packed Ice", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 743, "minStateId": 743, "maxStateId": 743, "drops": [], "boundingBox": "block"}, {"id": 497, "name": "double_plant", "displayName": "Sunflower", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9243, "minStateId": 9243, "maxStateId": 9254, "drops": [464], "boundingBox": "empty"}, {"id": 518, "name": "standing_banner", "displayName": "Black Banner", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12173, "minStateId": 12173, "maxStateId": 12188, "drops": [1141], "boundingBox": "empty"}, {"id": 534, "name": "wall_banner", "displayName": "Black Banner", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9508, "minStateId": 9508, "maxStateId": 9513, "drops": [1141], "boundingBox": "empty"}, {"id": 535, "name": "red_sandstone", "displayName": "Red Sandstone", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11790, "minStateId": 11790, "maxStateId": 11793, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [509], "boundingBox": "block"}, {"id": 538, "name": "red_sandstone_stairs", "displayName": "Red Sandstone Stairs", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 9137, "minStateId": 9137, "maxStateId": 9144, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [512], "boundingBox": "block"}, {"id": 539, "name": "oak_double_slab", "displayName": "Oak Slab", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6012, "minStateId": 6012, "maxStateId": 6013, "drops": [251], "boundingBox": "block"}, {"id": 8012, "name": "oak_slab", "displayName": "Oak Slab", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 7100, "minStateId": 7100, "maxStateId": 7101, "drops": [251], "boundingBox": "block"}, {"id": 540, "name": "spruce_double_slab", "displayName": "Spruce Slab", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 4933, "minStateId": 4933, "maxStateId": 4934, "drops": [252], "boundingBox": "block"}, {"id": 8013, "name": "spruce_slab", "displayName": "Spruce Slab", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 12245, "minStateId": 12245, "maxStateId": 12246, "drops": [252], "boundingBox": "block"}, {"id": 541, "name": "birch_slab", "displayName": "<PERSON>", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6609, "minStateId": 6609, "maxStateId": 6610, "drops": [253], "boundingBox": "block"}, {"id": 8014, "name": "birch_double_slab", "displayName": "<PERSON>", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13877, "minStateId": 13877, "maxStateId": 13878, "drops": [253], "boundingBox": "block"}, {"id": 542, "name": "jungle_slab", "displayName": "Jungle Slab", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6666, "minStateId": 6666, "maxStateId": 6667, "drops": [254], "boundingBox": "block"}, {"id": 8015, "name": "jungle_double_slab", "displayName": "Jungle Slab", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13294, "minStateId": 13294, "maxStateId": 13295, "drops": [254], "boundingBox": "block"}, {"id": 543, "name": "acacia_double_slab", "displayName": "Acacia <PERSON>b", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 9541, "minStateId": 9541, "maxStateId": 9542, "drops": [255], "boundingBox": "block"}, {"id": 8016, "name": "acacia_slab", "displayName": "Acacia <PERSON>b", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13280, "minStateId": 13280, "maxStateId": 13281, "drops": [255], "boundingBox": "block"}, {"id": 544, "name": "cherry_double_slab", "displayName": "Cherry Slab", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11185, "minStateId": 11185, "maxStateId": 11186, "drops": [256], "boundingBox": "block"}, {"id": 8017, "name": "cherry_slab", "displayName": "Cherry Slab", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11368, "minStateId": 11368, "maxStateId": 11369, "drops": [256], "boundingBox": "block"}, {"id": 545, "name": "dark_oak_slab", "displayName": "Dark Oak Slab", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 1194, "minStateId": 1194, "maxStateId": 1195, "drops": [257], "boundingBox": "block"}, {"id": 8018, "name": "dark_oak_double_slab", "displayName": "Dark Oak Slab", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6950, "minStateId": 6950, "maxStateId": 6951, "drops": [257], "boundingBox": "block"}, {"id": 546, "name": "mangrove_double_slab", "displayName": "Mangrove Slab", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 1189, "minStateId": 1189, "maxStateId": 1190, "drops": [258], "boundingBox": "block"}, {"id": 8019, "name": "mangrove_slab", "displayName": "Mangrove Slab", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 2608, "minStateId": 2608, "maxStateId": 2609, "drops": [258], "boundingBox": "block"}, {"id": 547, "name": "bamboo_double_slab", "displayName": "Bamboo Slab", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 7096, "minStateId": 7096, "maxStateId": 7097, "drops": [259], "boundingBox": "block"}, {"id": 8020, "name": "bamboo_slab", "displayName": "Bamboo Slab", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11720, "minStateId": 11720, "maxStateId": 11721, "drops": [259], "boundingBox": "block"}, {"id": 548, "name": "bamboo_mosaic_slab", "displayName": "Bamboo Mosaic Slab", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 4935, "minStateId": 4935, "maxStateId": 4936, "drops": [260], "boundingBox": "block"}, {"id": 8021, "name": "bamboo_mosaic_double_slab", "displayName": "Bamboo Mosaic Slab", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6983, "minStateId": 6983, "maxStateId": 6984, "drops": [260], "boundingBox": "block"}, {"id": 549, "name": "stone_block_slab4", "displayName": "<PERSON> Slab", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 9649, "minStateId": 9649, "maxStateId": 9658, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [263], "boundingBox": "block"}, {"id": 8022, "name": "double_stone_block_slab4", "displayName": "<PERSON> Slab", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11655, "minStateId": 11655, "maxStateId": 11664, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [263], "boundingBox": "block"}, {"id": 550, "name": "stone_block_slab", "displayName": "Smooth Stone Slab", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 7076, "minStateId": 7076, "maxStateId": 7091, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [264], "boundingBox": "block"}, {"id": 8023, "name": "double_stone_block_slab", "displayName": "Smooth Stone Slab", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11829, "minStateId": 11829, "maxStateId": 11844, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [264], "boundingBox": "block"}, {"id": 557, "name": "mud_brick_double_slab", "displayName": "Mud <PERSON> Slab", "hardness": 1.5, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 625, "minStateId": 625, "maxStateId": 626, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [271], "boundingBox": "block"}, {"id": 8024, "name": "mud_brick_slab", "displayName": "Mud <PERSON> Slab", "hardness": 1.5, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6695, "minStateId": 6695, "maxStateId": 6696, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [271], "boundingBox": "block"}, {"id": 563, "name": "smooth_stone", "displayName": "Smooth Stone", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7449, "minStateId": 7449, "maxStateId": 7449, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [283], "boundingBox": "block"}, {"id": 567, "name": "spruce_fence_gate", "displayName": "Spruce Fence Gate", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11795, "minStateId": 11795, "maxStateId": 11810, "drops": [750], "boundingBox": "block"}, {"id": 568, "name": "birch_fence_gate", "displayName": "Birch Fence Gate", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6176, "minStateId": 6176, "maxStateId": 6191, "drops": [751], "boundingBox": "block"}, {"id": 569, "name": "jungle_fence_gate", "displayName": "Jungle Fence Gate", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 9152, "minStateId": 9152, "maxStateId": 9167, "drops": [752], "boundingBox": "block"}, {"id": 570, "name": "acacia_fence_gate", "displayName": "Acacia Fence Gate", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13300, "minStateId": 13300, "maxStateId": 13315, "drops": [753], "boundingBox": "block"}, {"id": 571, "name": "cherry_fence_gate", "displayName": "Cherry Fence Gate", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 14061, "minStateId": 14061, "maxStateId": 14076, "drops": [754], "boundingBox": "block"}, {"id": 572, "name": "dark_oak_fence_gate", "displayName": "Dark Oak Fence Gate", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6967, "minStateId": 6967, "maxStateId": 6982, "drops": [755], "boundingBox": "block"}, {"id": 573, "name": "mangrove_fence_gate", "displayName": "Mangrove Fence Gate", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 7525, "minStateId": 7525, "maxStateId": 7540, "drops": [756], "boundingBox": "block"}, {"id": 574, "name": "bamboo_fence_gate", "displayName": "Bamboo Fence Gate", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 8836, "minStateId": 8836, "maxStateId": 8851, "drops": [757], "boundingBox": "block"}, {"id": 575, "name": "spruce_fence", "displayName": "Spruce Fence", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 1152, "minStateId": 1152, "maxStateId": 1152, "drops": [311], "boundingBox": "block"}, {"id": 576, "name": "birch_fence", "displayName": "<PERSON>", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13873, "minStateId": 13873, "maxStateId": 13873, "drops": [312], "boundingBox": "block"}, {"id": 577, "name": "jungle_fence", "displayName": "Jungle Fence", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 1145, "minStateId": 1145, "maxStateId": 1145, "drops": [313], "boundingBox": "block"}, {"id": 578, "name": "acacia_fence", "displayName": "Acacia Fence", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13886, "minStateId": 13886, "maxStateId": 13886, "drops": [314], "boundingBox": "block"}, {"id": 579, "name": "cherry_fence", "displayName": "<PERSON>", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 2431, "minStateId": 2431, "maxStateId": 2431, "drops": [315], "boundingBox": "block"}, {"id": 580, "name": "dark_oak_fence", "displayName": "Dark Oak Fence", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 12348, "minStateId": 12348, "maxStateId": 12348, "drops": [316], "boundingBox": "block"}, {"id": 581, "name": "mangrove_fence", "displayName": "Mangrove Fence", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11845, "minStateId": 11845, "maxStateId": 11845, "drops": [317], "boundingBox": "block"}, {"id": 582, "name": "bamboo_fence", "displayName": "Bamboo Fence", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 1384, "minStateId": 1384, "maxStateId": 1384, "drops": [318], "boundingBox": "block"}, {"id": 583, "name": "spruce_door", "displayName": "Spruce Door", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 8007, "minStateId": 8007, "maxStateId": 8038, "drops": [711], "boundingBox": "block"}, {"id": 584, "name": "birch_door", "displayName": "<PERSON>", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12349, "minStateId": 12349, "maxStateId": 12380, "drops": [712], "boundingBox": "block"}, {"id": 585, "name": "jungle_door", "displayName": "Jungle Door", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11299, "minStateId": 11299, "maxStateId": 11330, "drops": [713], "boundingBox": "block"}, {"id": 586, "name": "acacia_door", "displayName": "Acacia Door", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7255, "minStateId": 7255, "maxStateId": 7286, "drops": [714], "boundingBox": "block"}, {"id": 587, "name": "cherry_door", "displayName": "Cherry Door", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7107, "minStateId": 7107, "maxStateId": 7138, "drops": [715], "boundingBox": "block"}, {"id": 588, "name": "dark_oak_door", "displayName": "Dark Oak Door", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9543, "minStateId": 9543, "maxStateId": 9574, "drops": [716], "boundingBox": "block"}, {"id": 589, "name": "mangrove_door", "displayName": "Mangrove Door", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11555, "minStateId": 11555, "maxStateId": 11586, "drops": [717], "boundingBox": "block"}, {"id": 590, "name": "bamboo_door", "displayName": "Bamboo Door", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 751, "minStateId": 751, "maxStateId": 782, "drops": [718], "boundingBox": "block"}, {"id": 591, "name": "end_rod", "displayName": "End Rod", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 14, "filterLight": 0, "defaultState": 10546, "minStateId": 10546, "maxStateId": 10551, "drops": [291], "boundingBox": "block"}, {"id": 592, "name": "chorus_plant", "displayName": "Chorus Plant", "hardness": 0.4, "resistance": 0.4, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 9329, "minStateId": 9329, "maxStateId": 9329, "drops": [1143], "boundingBox": "block"}, {"id": 593, "name": "chorus_flower", "displayName": "Chorus Flower", "hardness": 0.4, "resistance": 0.4, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 7391, "minStateId": 7391, "maxStateId": 7396, "drops": [], "boundingBox": "block"}, {"id": 594, "name": "purpur_block", "displayName": "Purpur Block", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13428, "minStateId": 13428, "maxStateId": 13439, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [294], "boundingBox": "block"}, {"id": 596, "name": "purpur_stairs", "displayName": "Purpur Stairs", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13479, "minStateId": 13479, "maxStateId": 13486, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [296], "boundingBox": "block"}, {"id": 597, "name": "end_bricks", "displayName": "End Stone Bricks", "hardness": 3, "resistance": 9, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 741, "minStateId": 741, "maxStateId": 741, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [377], "boundingBox": "block"}, {"id": 598, "name": "torchflower_crop", "displayName": "Torchflower <PERSON>", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11160, "minStateId": 11160, "maxStateId": 11167, "drops": [1145], "boundingBox": "empty"}, {"id": 599, "name": "pitcher_crop", "displayName": "Pitcher C<PERSON>", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 688, "minStateId": 688, "maxStateId": 703, "drops": [1146], "boundingBox": "block"}, {"id": 600, "name": "pitcher_plant", "displayName": "Pitcher Plant", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 6155, "minStateId": 6155, "maxStateId": 6156, "drops": [231], "boundingBox": "empty"}, {"id": 601, "name": "beetroot", "displayName": "Beetroots", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9080, "minStateId": 9080, "maxStateId": 9087, "drops": [1148], "boundingBox": "empty"}, {"id": 602, "name": "grass_path", "displayName": "Dirt Path", "hardness": 0.65, "resistance": 0.65, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13887, "minStateId": 13887, "maxStateId": 13887, "drops": [28], "boundingBox": "block"}, {"id": 603, "name": "end_gateway", "displayName": "End Gateway", "hardness": -1, "resistance": 3600000, "stackSize": 64, "diggable": false, "material": "default", "transparent": true, "emitLight": 15, "filterLight": 1, "defaultState": 565, "minStateId": 565, "maxStateId": 565, "drops": [], "boundingBox": "empty"}, {"id": 604, "name": "repeating_command_block", "displayName": "Repeating Command Block", "hardness": -1, "resistance": 3600000, "stackSize": 64, "diggable": false, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13455, "minStateId": 13455, "maxStateId": 13466, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 605, "name": "chain_command_block", "displayName": "Chain Command Block", "hardness": -1, "resistance": 3600000, "stackSize": 64, "diggable": false, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11778, "minStateId": 11778, "maxStateId": 11789, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 606, "name": "frosted_ice", "displayName": "Frosted Ice", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 8039, "minStateId": 8039, "maxStateId": 8042, "drops": [], "boundingBox": "block"}, {"id": 607, "name": "magma", "displayName": "Magma Block", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 3, "filterLight": 15, "defaultState": 13817, "minStateId": 13817, "maxStateId": 13817, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [515], "boundingBox": "block"}, {"id": 608, "name": "nether_wart_block", "displayName": "Nether Wart Block", "hardness": 1, "resistance": 1, "stackSize": 64, "diggable": true, "material": "mineable/hoe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7106, "minStateId": 7106, "maxStateId": 7106, "drops": [516], "boundingBox": "block"}, {"id": 609, "name": "red_nether_brick", "displayName": "Red Nether Bricks", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 567, "minStateId": 567, "maxStateId": 567, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [518], "boundingBox": "block"}, {"id": 610, "name": "bone_block", "displayName": "Bone Block", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7037, "minStateId": 7037, "maxStateId": 7048, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [519], "boundingBox": "block"}, {"id": 611, "name": "structure_void", "displayName": "Structure Void", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 6986, "minStateId": 6986, "maxStateId": 6987, "drops": [], "boundingBox": "empty"}, {"id": 612, "name": "observer", "displayName": "Observer", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 5489, "minStateId": 5489, "maxStateId": 5500, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [665], "boundingBox": "block"}, {"id": 613, "name": "undyed_shulker_box", "displayName": "Shulker Box", "hardness": 2, "resistance": 2, "stackSize": 1, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 6069, "minStateId": 6069, "maxStateId": 6069, "drops": [521], "boundingBox": "block"}, {"id": 614, "name": "white_shulker_box", "displayName": "White Shulker Box", "hardness": 2, "resistance": 2, "stackSize": 1, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 1118, "minStateId": 1118, "maxStateId": 1118, "drops": [522], "boundingBox": "block"}, {"id": 615, "name": "orange_shulker_box", "displayName": "Orange Shulker Box", "hardness": 2, "resistance": 2, "stackSize": 1, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 11371, "minStateId": 11371, "maxStateId": 11371, "drops": [523], "boundingBox": "block"}, {"id": 616, "name": "magenta_shulker_box", "displayName": "<PERSON><PERSON>a <PERSON>er Box", "hardness": 2, "resistance": 2, "stackSize": 1, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 742, "minStateId": 742, "maxStateId": 742, "drops": [524], "boundingBox": "block"}, {"id": 617, "name": "light_blue_shulker_box", "displayName": "Light Blue Shulker Box", "hardness": 2, "resistance": 2, "stackSize": 1, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 12189, "minStateId": 12189, "maxStateId": 12189, "drops": [525], "boundingBox": "block"}, {"id": 618, "name": "yellow_shulker_box", "displayName": "Yellow Shulker Box", "hardness": 2, "resistance": 2, "stackSize": 1, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 126, "minStateId": 126, "maxStateId": 126, "drops": [526], "boundingBox": "block"}, {"id": 619, "name": "lime_shulker_box", "displayName": "<PERSON>e <PERSON>er Box", "hardness": 2, "resistance": 2, "stackSize": 1, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 1052, "minStateId": 1052, "maxStateId": 1052, "drops": [527], "boundingBox": "block"}, {"id": 620, "name": "pink_shulker_box", "displayName": "Pink Shulker Box", "hardness": 2, "resistance": 2, "stackSize": 1, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 7226, "minStateId": 7226, "maxStateId": 7226, "drops": [528], "boundingBox": "block"}, {"id": 621, "name": "gray_shulker_box", "displayName": "<PERSON>", "hardness": 2, "resistance": 2, "stackSize": 1, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 9136, "minStateId": 9136, "maxStateId": 9136, "drops": [529], "boundingBox": "block"}, {"id": 622, "name": "light_gray_shulker_box", "displayName": "Light Gray Shulker Box", "hardness": 2, "resistance": 2, "stackSize": 1, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 10564, "minStateId": 10564, "maxStateId": 10564, "drops": [530], "boundingBox": "block"}, {"id": 623, "name": "cyan_shulker_box", "displayName": "<PERSON><PERSON>", "hardness": 2, "resistance": 2, "stackSize": 1, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 12256, "minStateId": 12256, "maxStateId": 12256, "drops": [531], "boundingBox": "block"}, {"id": 624, "name": "purple_shulker_box", "displayName": "Purple Shulker Box", "hardness": 2, "resistance": 2, "stackSize": 1, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 13074, "minStateId": 13074, "maxStateId": 13074, "drops": [532], "boundingBox": "block"}, {"id": 625, "name": "blue_shulker_box", "displayName": "Blue Shulker Box", "hardness": 2, "resistance": 2, "stackSize": 1, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 11420, "minStateId": 11420, "maxStateId": 11420, "drops": [533], "boundingBox": "block"}, {"id": 626, "name": "brown_shulker_box", "displayName": "<PERSON> Shulker Box", "hardness": 2, "resistance": 2, "stackSize": 1, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 12098, "minStateId": 12098, "maxStateId": 12098, "drops": [534], "boundingBox": "block"}, {"id": 627, "name": "green_shulker_box", "displayName": "Green Shulker Box", "hardness": 2, "resistance": 2, "stackSize": 1, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 11688, "minStateId": 11688, "maxStateId": 11688, "drops": [535], "boundingBox": "block"}, {"id": 628, "name": "red_shulker_box", "displayName": "Red Shulker Box", "hardness": 2, "resistance": 2, "stackSize": 1, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 7287, "minStateId": 7287, "maxStateId": 7287, "drops": [536], "boundingBox": "block"}, {"id": 629, "name": "black_shulker_box", "displayName": "Black Shulker Box", "hardness": 2, "resistance": 2, "stackSize": 1, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 11158, "minStateId": 11158, "maxStateId": 11158, "drops": [537], "boundingBox": "block"}, {"id": 630, "name": "white_glazed_terracotta", "displayName": "White Glazed Terracotta", "hardness": 1.4, "resistance": 1.4, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9397, "minStateId": 9397, "maxStateId": 9402, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [538], "boundingBox": "block"}, {"id": 631, "name": "orange_glazed_terracotta", "displayName": "Orange Glazed Terracotta", "hardness": 1.4, "resistance": 1.4, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 2610, "minStateId": 2610, "maxStateId": 2615, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [539], "boundingBox": "block"}, {"id": 632, "name": "magenta_glazed_terracotta", "displayName": "Magenta Glazed Terracotta", "hardness": 1.4, "resistance": 1.4, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 2440, "minStateId": 2440, "maxStateId": 2445, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [540], "boundingBox": "block"}, {"id": 633, "name": "light_blue_glazed_terracotta", "displayName": "Light Blue Glazed Terracotta", "hardness": 1.4, "resistance": 1.4, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9294, "minStateId": 9294, "maxStateId": 9299, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [541], "boundingBox": "block"}, {"id": 634, "name": "yellow_glazed_terracotta", "displayName": "Yellow Glazed Terracotta", "hardness": 1.4, "resistance": 1.4, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 2396, "minStateId": 2396, "maxStateId": 2401, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [542], "boundingBox": "block"}, {"id": 635, "name": "lime_glazed_terracotta", "displayName": "Lime Glazed Terracotta", "hardness": 1.4, "resistance": 1.4, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 654, "minStateId": 654, "maxStateId": 659, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [543], "boundingBox": "block"}, {"id": 636, "name": "pink_glazed_terracotta", "displayName": "Pink Glazed Terracotta", "hardness": 1.4, "resistance": 1.4, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11749, "minStateId": 11749, "maxStateId": 11754, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [544], "boundingBox": "block"}, {"id": 637, "name": "gray_glazed_terracotta", "displayName": "Gray Glazed Terracotta", "hardness": 1.4, "resistance": 1.4, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 14077, "minStateId": 14077, "maxStateId": 14082, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [545], "boundingBox": "block"}, {"id": 638, "name": "silver_glazed_terracotta", "displayName": "Light Gray Glazed Terracotta", "hardness": 1.4, "resistance": 1.4, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 5507, "minStateId": 5507, "maxStateId": 5512, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [546], "boundingBox": "block"}, {"id": 639, "name": "cyan_glazed_terracotta", "displayName": "<PERSON><PERSON>zed Terracotta", "hardness": 1.4, "resistance": 1.4, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9145, "minStateId": 9145, "maxStateId": 9150, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [547], "boundingBox": "block"}, {"id": 640, "name": "purple_glazed_terracotta", "displayName": "Purple Glazed Terracotta", "hardness": 1.4, "resistance": 1.4, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12236, "minStateId": 12236, "maxStateId": 12241, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [548], "boundingBox": "block"}, {"id": 641, "name": "blue_glazed_terracotta", "displayName": "Blue Glazed Terracotta", "hardness": 1.4, "resistance": 1.4, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9288, "minStateId": 9288, "maxStateId": 9293, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [549], "boundingBox": "block"}, {"id": 642, "name": "brown_glazed_terracotta", "displayName": "Brown Glazed Terracotta", "hardness": 1.4, "resistance": 1.4, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 5909, "minStateId": 5909, "maxStateId": 5914, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [550], "boundingBox": "block"}, {"id": 643, "name": "green_glazed_terracotta", "displayName": "Green Glazed Terracotta", "hardness": 1.4, "resistance": 1.4, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11821, "minStateId": 11821, "maxStateId": 11826, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [551], "boundingBox": "block"}, {"id": 644, "name": "red_glazed_terracotta", "displayName": "Red Glazed Terracotta", "hardness": 1.4, "resistance": 1.4, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6961, "minStateId": 6961, "maxStateId": 6966, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [552], "boundingBox": "block"}, {"id": 645, "name": "black_glazed_terracotta", "displayName": "Black Glazed Terracotta", "hardness": 1.4, "resistance": 1.4, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 10052, "minStateId": 10052, "maxStateId": 10057, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [553], "boundingBox": "block"}, {"id": 646, "name": "white_concrete", "displayName": "White Concrete", "hardness": 1.8, "resistance": 1.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13879, "minStateId": 13879, "maxStateId": 13879, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [554], "boundingBox": "block"}, {"id": 647, "name": "orange_concrete", "displayName": "Orange Concrete", "hardness": 1.8, "resistance": 1.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11926, "minStateId": 11926, "maxStateId": 11926, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [555], "boundingBox": "block"}, {"id": 648, "name": "magenta_concrete", "displayName": "Magenta Concrete", "hardness": 1.8, "resistance": 1.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7209, "minStateId": 7209, "maxStateId": 7209, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [556], "boundingBox": "block"}, {"id": 649, "name": "light_blue_concrete", "displayName": "Light Blue Concrete", "hardness": 1.8, "resistance": 1.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13137, "minStateId": 13137, "maxStateId": 13137, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [557], "boundingBox": "block"}, {"id": 650, "name": "yellow_concrete", "displayName": "Yellow Concrete", "hardness": 1.8, "resistance": 1.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 5999, "minStateId": 5999, "maxStateId": 5999, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [558], "boundingBox": "block"}, {"id": 651, "name": "lime_concrete", "displayName": "Lime Concrete", "hardness": 1.8, "resistance": 1.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7468, "minStateId": 7468, "maxStateId": 7468, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [559], "boundingBox": "block"}, {"id": 652, "name": "pink_concrete", "displayName": "Pink Concrete", "hardness": 1.8, "resistance": 1.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 5521, "minStateId": 5521, "maxStateId": 5521, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [560], "boundingBox": "block"}, {"id": 653, "name": "gray_concrete", "displayName": "<PERSON>", "hardness": 1.8, "resistance": 1.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13065, "minStateId": 13065, "maxStateId": 13065, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [561], "boundingBox": "block"}, {"id": 654, "name": "light_gray_concrete", "displayName": "Light Gray Concrete", "hardness": 1.8, "resistance": 1.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1972, "minStateId": 1972, "maxStateId": 1972, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [562], "boundingBox": "block"}, {"id": 655, "name": "cyan_concrete", "displayName": "<PERSON><PERSON>", "hardness": 1.8, "resistance": 1.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12961, "minStateId": 12961, "maxStateId": 12961, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [563], "boundingBox": "block"}, {"id": 656, "name": "purple_concrete", "displayName": "Purple Concrete", "hardness": 1.8, "resistance": 1.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6988, "minStateId": 6988, "maxStateId": 6988, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [564], "boundingBox": "block"}, {"id": 657, "name": "blue_concrete", "displayName": "Blue Concrete", "hardness": 1.8, "resistance": 1.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12971, "minStateId": 12971, "maxStateId": 12971, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [565], "boundingBox": "block"}, {"id": 658, "name": "brown_concrete", "displayName": "<PERSON> Concrete", "hardness": 1.8, "resistance": 1.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11367, "minStateId": 11367, "maxStateId": 11367, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [566], "boundingBox": "block"}, {"id": 659, "name": "green_concrete", "displayName": "Green Concrete", "hardness": 1.8, "resistance": 1.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 10555, "minStateId": 10555, "maxStateId": 10555, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [567], "boundingBox": "block"}, {"id": 660, "name": "red_concrete", "displayName": "Red Concrete", "hardness": 1.8, "resistance": 1.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13412, "minStateId": 13412, "maxStateId": 13412, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [568], "boundingBox": "block"}, {"id": 661, "name": "black_concrete", "displayName": "Black Concrete", "hardness": 1.8, "resistance": 1.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11909, "minStateId": 11909, "maxStateId": 11909, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [569], "boundingBox": "block"}, {"id": 662, "name": "white_concrete_powder", "displayName": "White Concrete Powder", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 8564, "minStateId": 8564, "maxStateId": 8564, "drops": [570], "boundingBox": "block"}, {"id": 663, "name": "orange_concrete_powder", "displayName": "Orange Concrete Powder", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 14050, "minStateId": 14050, "maxStateId": 14050, "drops": [571], "boundingBox": "block"}, {"id": 664, "name": "magenta_concrete_powder", "displayName": "Magenta Concrete Powder", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7304, "minStateId": 7304, "maxStateId": 7304, "drops": [572], "boundingBox": "block"}, {"id": 665, "name": "light_blue_concrete_powder", "displayName": "Light Blue Concrete Powder", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 65, "minStateId": 65, "maxStateId": 65, "drops": [573], "boundingBox": "block"}, {"id": 666, "name": "yellow_concrete_powder", "displayName": "Yellow Concrete Powder", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13286, "minStateId": 13286, "maxStateId": 13286, "drops": [574], "boundingBox": "block"}, {"id": 667, "name": "lime_concrete_powder", "displayName": "Lime Concrete <PERSON>", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13804, "minStateId": 13804, "maxStateId": 13804, "drops": [575], "boundingBox": "block"}, {"id": 668, "name": "pink_concrete_powder", "displayName": "Pink Concrete Powder", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7104, "minStateId": 7104, "maxStateId": 7104, "drops": [576], "boundingBox": "block"}, {"id": 669, "name": "gray_concrete_powder", "displayName": "<PERSON> Concre<PERSON>", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13064, "minStateId": 13064, "maxStateId": 13064, "drops": [577], "boundingBox": "block"}, {"id": 670, "name": "light_gray_concrete_powder", "displayName": "Light Gray Concrete Powder", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12944, "minStateId": 12944, "maxStateId": 12944, "drops": [578], "boundingBox": "block"}, {"id": 671, "name": "cyan_concrete_powder", "displayName": "<PERSON><PERSON>", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 5908, "minStateId": 5908, "maxStateId": 5908, "drops": [579], "boundingBox": "block"}, {"id": 672, "name": "purple_concrete_powder", "displayName": "Purple Concrete Powder", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11748, "minStateId": 11748, "maxStateId": 11748, "drops": [580], "boundingBox": "block"}, {"id": 673, "name": "blue_concrete_powder", "displayName": "Blue Concrete Powder", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11925, "minStateId": 11925, "maxStateId": 11925, "drops": [581], "boundingBox": "block"}, {"id": 674, "name": "brown_concrete_powder", "displayName": "<PERSON> Concrete <PERSON>", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 10129, "minStateId": 10129, "maxStateId": 10129, "drops": [582], "boundingBox": "block"}, {"id": 675, "name": "green_concrete_powder", "displayName": "Green Concrete Powder", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12242, "minStateId": 12242, "maxStateId": 12242, "drops": [583], "boundingBox": "block"}, {"id": 676, "name": "red_concrete_powder", "displayName": "Red Concrete Powder", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12943, "minStateId": 12943, "maxStateId": 12943, "drops": [584], "boundingBox": "block"}, {"id": 677, "name": "black_concrete_powder", "displayName": "Black Concrete Powder", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1154, "minStateId": 1154, "maxStateId": 1154, "drops": [585], "boundingBox": "block"}, {"id": 679, "name": "kelp", "displayName": "Kelp Plant", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 9217, "minStateId": 9217, "maxStateId": 9242, "drops": [243], "boundingBox": "empty"}, {"id": 680, "name": "dried_kelp_block", "displayName": "Dried Kelp Block", "hardness": 0.5, "resistance": 2.5, "stackSize": 64, "diggable": true, "material": "mineable/hoe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13784, "minStateId": 13784, "maxStateId": 13784, "drops": [920], "boundingBox": "block"}, {"id": 681, "name": "turtle_egg", "displayName": "Turtle Egg", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13805, "minStateId": 13805, "maxStateId": 13816, "drops": [], "boundingBox": "block"}, {"id": 682, "name": "sniffer_egg", "displayName": "Sniffer Egg", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12225, "minStateId": 12225, "maxStateId": 12227, "drops": [587], "boundingBox": "block"}, {"id": 683, "name": "coral_block", "displayName": "Dead Tube Coral Block", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9027, "minStateId": 9027, "maxStateId": 9036, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [588], "boundingBox": "block"}, {"id": 693, "name": "dead_tube_coral", "displayName": "Dead Tube Coral", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 7105, "minStateId": 7105, "maxStateId": 7105, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [], "boundingBox": "empty"}, {"id": 694, "name": "dead_brain_coral", "displayName": "Dead Brain Coral", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 12858, "minStateId": 12858, "maxStateId": 12858, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [], "boundingBox": "empty"}, {"id": 695, "name": "dead_bubble_coral", "displayName": "Dead Bubble Coral", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 12946, "minStateId": 12946, "maxStateId": 12946, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [], "boundingBox": "empty"}, {"id": 696, "name": "dead_fire_coral", "displayName": "Dead Fire Coral", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 11425, "minStateId": 11425, "maxStateId": 11425, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [], "boundingBox": "empty"}, {"id": 697, "name": "dead_horn_coral", "displayName": "Dead Horn Coral", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 10616, "minStateId": 10616, "maxStateId": 10616, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [], "boundingBox": "empty"}, {"id": 698, "name": "tube_coral", "displayName": "Tube Coral", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 13487, "minStateId": 13487, "maxStateId": 13487, "drops": [], "boundingBox": "empty"}, {"id": 699, "name": "brain_coral", "displayName": "Brain Coral", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 1958, "minStateId": 1958, "maxStateId": 1958, "drops": [], "boundingBox": "empty"}, {"id": 700, "name": "bubble_coral", "displayName": "Bubble Coral", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 11370, "minStateId": 11370, "maxStateId": 11370, "drops": [], "boundingBox": "empty"}, {"id": 701, "name": "fire_coral", "displayName": "Fire Coral", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 1790, "minStateId": 1790, "maxStateId": 1790, "drops": [], "boundingBox": "empty"}, {"id": 702, "name": "horn_coral", "displayName": "Horn Coral", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 5998, "minStateId": 5998, "maxStateId": 5998, "drops": [], "boundingBox": "empty"}, {"id": 703, "name": "coral_fan_dead", "displayName": "Dead Tube Coral Fan", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 76, "minStateId": 76, "maxStateId": 85, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [], "boundingBox": "empty"}, {"id": 708, "name": "coral_fan", "displayName": "Tube Coral Fan", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 7501, "minStateId": 7501, "maxStateId": 7510, "drops": [], "boundingBox": "empty"}, {"id": 713, "name": "coral_fan_hang", "displayName": "Dead Tube Coral Wall Fan", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 7397, "minStateId": 7397, "maxStateId": 7412, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [], "boundingBox": "empty"}, {"id": 715, "name": "coral_fan_hang2", "displayName": "Dead Bubble Coral Wall Fan", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 6885, "minStateId": 6885, "maxStateId": 6900, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [], "boundingBox": "empty"}, {"id": 717, "name": "coral_fan_hang3", "displayName": "Dead Horn Coral Wall Fan", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 6901, "minStateId": 6901, "maxStateId": 6916, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [], "boundingBox": "empty"}, {"id": 723, "name": "sea_pickle", "displayName": "<PERSON>", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 6, "filterLight": 1, "defaultState": 10105, "minStateId": 10105, "maxStateId": 10112, "drops": [200], "boundingBox": "block"}, {"id": 724, "name": "blue_ice", "displayName": "Blue Ice", "hardness": 2.8, "resistance": 2.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12255, "minStateId": 12255, "maxStateId": 12255, "drops": [], "boundingBox": "block"}, {"id": 725, "name": "conduit", "displayName": "Conduit", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 15, "filterLight": 1, "defaultState": 7035, "minStateId": 7035, "maxStateId": 7035, "drops": [619], "boundingBox": "block"}, {"id": 726, "name": "bamboo_sapling", "displayName": "Bamboo Shoot", "hardness": 1, "resistance": 1, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13259, "minStateId": 13259, "maxStateId": 13270, "drops": [250], "boundingBox": "empty"}, {"id": 727, "name": "bamboo", "displayName": "Bamboo", "hardness": 1, "resistance": 1, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 6072, "minStateId": 6072, "maxStateId": 6083, "drops": [250], "boundingBox": "block"}, {"id": 731, "name": "bubble_column", "displayName": "Bubble Column", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 9606, "minStateId": 9606, "maxStateId": 9607, "drops": [], "boundingBox": "empty"}, {"id": 732, "name": "polished_granite_stairs", "displayName": "Polished Granite Stairs", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6937, "minStateId": 6937, "maxStateId": 6944, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [620], "boundingBox": "block"}, {"id": 733, "name": "smooth_red_sandstone_stairs", "displayName": "Smooth Red Sandstone Stairs", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 9370, "minStateId": 9370, "maxStateId": 9377, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [621], "boundingBox": "block"}, {"id": 734, "name": "mossy_stone_brick_stairs", "displayName": "Mossy Stone Brick Stairs", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 10538, "minStateId": 10538, "maxStateId": 10545, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [622], "boundingBox": "block"}, {"id": 735, "name": "polished_diorite_stairs", "displayName": "Polished Diorite Stairs", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11917, "minStateId": 11917, "maxStateId": 11924, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [623], "boundingBox": "block"}, {"id": 736, "name": "mossy_cobblestone_stairs", "displayName": "Mossy Cobblestone Stairs", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6877, "minStateId": 6877, "maxStateId": 6884, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [624], "boundingBox": "block"}, {"id": 737, "name": "end_brick_stairs", "displayName": "End Stone Brick Stairs", "hardness": 3, "resistance": 9, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11533, "minStateId": 11533, "maxStateId": 11540, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [625], "boundingBox": "block"}, {"id": 738, "name": "normal_stone_stairs", "displayName": "Stone Stairs", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 1385, "minStateId": 1385, "maxStateId": 1392, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [626], "boundingBox": "block"}, {"id": 739, "name": "smooth_sandstone_stairs", "displayName": "Smooth Sandstone Stairs", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6014, "minStateId": 6014, "maxStateId": 6021, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [627], "boundingBox": "block"}, {"id": 740, "name": "smooth_quartz_stairs", "displayName": "Smooth Quartz Stairs", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13414, "minStateId": 13414, "maxStateId": 13421, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [628], "boundingBox": "block"}, {"id": 741, "name": "granite_stairs", "displayName": "Granite Stairs", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 5513, "minStateId": 5513, "maxStateId": 5520, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [629], "boundingBox": "block"}, {"id": 742, "name": "andesite_stairs", "displayName": "Andesite Stairs", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 9104, "minStateId": 9104, "maxStateId": 9111, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [630], "boundingBox": "block"}, {"id": 743, "name": "red_nether_brick_stairs", "displayName": "Red Nether Brick Stairs", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11813, "minStateId": 11813, "maxStateId": 11820, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [631], "boundingBox": "block"}, {"id": 744, "name": "polished_andesite_stairs", "displayName": "Polished Andesite Stairs", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 12258, "minStateId": 12258, "maxStateId": 12265, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [632], "boundingBox": "block"}, {"id": 745, "name": "diorite_stairs", "displayName": "Diorite Stairs", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 7242, "minStateId": 7242, "maxStateId": 7249, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [633], "boundingBox": "block"}, {"id": 746, "name": "stone_block_slab3", "displayName": "Polished Granite Slab", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 9633, "minStateId": 9633, "maxStateId": 9648, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [638], "boundingBox": "block"}, {"id": 8025, "name": "double_stone_block_slab3", "displayName": "Polished Granite Slab", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11639, "minStateId": 11639, "maxStateId": 11654, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [638], "boundingBox": "block"}, {"id": 765, "name": "mud_brick_wall", "displayName": "Mud Brick Wall", "hardness": 1.5, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 2197, "minStateId": 2197, "maxStateId": 2358, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [404], "boundingBox": "block"}, {"id": 772, "name": "scaffolding", "displayName": "Scaffolding", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 5950, "minStateId": 5950, "maxStateId": 5965, "drops": [655], "boundingBox": "block"}, {"id": 773, "name": "loom", "displayName": "Loom", "hardness": 2.5, "resistance": 2.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6611, "minStateId": 6611, "maxStateId": 6614, "drops": [1184], "boundingBox": "block"}, {"id": 774, "name": "barrel", "displayName": "Barrel", "hardness": 2.5, "resistance": 2.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7379, "minStateId": 7379, "maxStateId": 7390, "drops": [1193], "boundingBox": "block"}, {"id": 775, "name": "smoker", "displayName": "Smoker", "hardness": 3.5, "resistance": 3.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1784, "minStateId": 1784, "maxStateId": 1787, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1194], "boundingBox": "block"}, {"id": 8026, "name": "lit_smoker", "displayName": "Smoker", "hardness": 3.5, "resistance": 3.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13407, "minStateId": 13407, "maxStateId": 13410, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1194], "boundingBox": "block"}, {"id": 776, "name": "lit_blast_furnace", "displayName": "Blast Furnace", "hardness": 3.5, "resistance": 3.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12341, "minStateId": 12341, "maxStateId": 12344, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1195], "boundingBox": "block"}, {"id": 8027, "name": "blast_furnace", "displayName": "Blast Furnace", "hardness": 3.5, "resistance": 3.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13275, "minStateId": 13275, "maxStateId": 13278, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1195], "boundingBox": "block"}, {"id": 777, "name": "cartography_table", "displayName": "Cartography Table", "hardness": 2.5, "resistance": 2.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 14116, "minStateId": 14116, "maxStateId": 14116, "drops": [1196], "boundingBox": "block"}, {"id": 778, "name": "fletching_table", "displayName": "Fletching Table", "hardness": 2.5, "resistance": 2.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 10047, "minStateId": 10047, "maxStateId": 10047, "drops": [1197], "boundingBox": "block"}, {"id": 779, "name": "grindstone", "displayName": "Grindstone", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13832, "minStateId": 13832, "maxStateId": 13847, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1198], "boundingBox": "block"}, {"id": 780, "name": "lectern", "displayName": "Lectern", "hardness": 2.5, "resistance": 2.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 12162, "minStateId": 12162, "maxStateId": 12169, "drops": [669], "boundingBox": "block"}, {"id": 781, "name": "smithing_table", "displayName": "Smithing Table", "hardness": 2.5, "resistance": 2.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6117, "minStateId": 6117, "maxStateId": 6117, "drops": [1199], "boundingBox": "block"}, {"id": 782, "name": "stonecutter_block", "displayName": "<PERSON><PERSON><PERSON>", "hardness": 3.5, "resistance": 3.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13282, "minStateId": 13282, "maxStateId": 13285, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1200], "boundingBox": "block"}, {"id": 783, "name": "bell", "displayName": "Bell", "hardness": 5, "resistance": 5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 12130, "minStateId": 12130, "maxStateId": 12161, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1201], "boundingBox": "block"}, {"id": 784, "name": "lantern", "displayName": "Lantern", "hardness": 3.5, "resistance": 3.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 15, "filterLight": 0, "defaultState": 12306, "minStateId": 12306, "maxStateId": 12307, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1202], "boundingBox": "block"}, {"id": 785, "name": "soul_lantern", "displayName": "Soul Lantern", "hardness": 3.5, "resistance": 3.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 10, "filterLight": 0, "defaultState": 9576, "minStateId": 9576, "maxStateId": 9577, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1203], "boundingBox": "block"}, {"id": 786, "name": "campfire", "displayName": "Campfire", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 15, "filterLight": 0, "defaultState": 9608, "minStateId": 9608, "maxStateId": 9615, "drops": [800], "boundingBox": "block"}, {"id": 787, "name": "soul_campfire", "displayName": "Soul Campfire", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 10, "filterLight": 0, "defaultState": 13849, "minStateId": 13849, "maxStateId": 13856, "drops": [326], "boundingBox": "block"}, {"id": 788, "name": "sweet_berry_bush", "displayName": "Sweet <PERSON>", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11132, "minStateId": 11132, "maxStateId": 11139, "drops": [], "boundingBox": "empty"}, {"id": 789, "name": "warped_stem", "displayName": "Warped Stem", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11685, "minStateId": 11685, "maxStateId": 11687, "drops": [142], "boundingBox": "block"}, {"id": 790, "name": "stripped_warped_stem", "displayName": "Stripped Warped Stem", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13097, "minStateId": 13097, "maxStateId": 13099, "drops": [153], "boundingBox": "block"}, {"id": 791, "name": "warped_hyphae", "displayName": "Warped Hyphae", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 10560, "minStateId": 10560, "maxStateId": 10562, "drops": [174], "boundingBox": "block"}, {"id": 792, "name": "stripped_warped_hyphae", "displayName": "Stripped Warped Hyphae", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9403, "minStateId": 9403, "maxStateId": 9405, "drops": [163], "boundingBox": "block"}, {"id": 793, "name": "warped_nylium", "displayName": "Warped Nylium", "hardness": 0.4, "resistance": 0.4, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11500, "minStateId": 11500, "maxStateId": 11500, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [324], "boundingBox": "block"}, {"id": 794, "name": "warped_fungus", "displayName": "Warped Fungus", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 748, "minStateId": 748, "maxStateId": 748, "drops": [236], "boundingBox": "empty"}, {"id": 795, "name": "warped_wart_block", "displayName": "Warped Wart Block", "hardness": 1, "resistance": 1, "stackSize": 64, "diggable": true, "material": "mineable/hoe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 10563, "minStateId": 10563, "maxStateId": 10563, "drops": [517], "boundingBox": "block"}, {"id": 796, "name": "warped_roots", "displayName": "Warped Roots", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7208, "minStateId": 7208, "maxStateId": 7208, "drops": [238], "boundingBox": "empty"}, {"id": 797, "name": "nether_sprouts", "displayName": "Nether Sprouts", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11677, "minStateId": 11677, "maxStateId": 11677, "drops": [], "boundingBox": "empty"}, {"id": 798, "name": "crimson_stem", "displayName": "Crimson Stem", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 10552, "minStateId": 10552, "maxStateId": 10554, "drops": [141], "boundingBox": "block"}, {"id": 799, "name": "stripped_crimson_stem", "displayName": "Stripped Crimson Stem", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12170, "minStateId": 12170, "maxStateId": 12172, "drops": [152], "boundingBox": "block"}, {"id": 800, "name": "crimson_hyphae", "displayName": "Crimson Hyphae", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7139, "minStateId": 7139, "maxStateId": 7141, "drops": [173], "boundingBox": "block"}, {"id": 801, "name": "stripped_crimson_hyphae", "displayName": "Stripped Crimson Hyphae", "hardness": 2, "resistance": 2, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11689, "minStateId": 11689, "maxStateId": 11691, "drops": [162], "boundingBox": "block"}, {"id": 802, "name": "crimson_nylium", "displayName": "Crimson Nylium", "hardness": 0.4, "resistance": 0.4, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6985, "minStateId": 6985, "maxStateId": 6985, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [324], "boundingBox": "block"}, {"id": 803, "name": "crimson_fungus", "displayName": "Crimson Fungus", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13474, "minStateId": 13474, "maxStateId": 13474, "drops": [235], "boundingBox": "empty"}, {"id": 804, "name": "shroomlight", "displayName": "Shroomlight", "hardness": 1, "resistance": 1, "stackSize": 64, "diggable": true, "material": "mineable/hoe", "transparent": false, "emitLight": 15, "filterLight": 15, "defaultState": 8835, "minStateId": 8835, "maxStateId": 8835, "drops": [1208], "boundingBox": "block"}, {"id": 806, "name": "weeping_vines", "displayName": "Weeping Vines Plant", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9303, "minStateId": 9303, "maxStateId": 9328, "drops": [], "boundingBox": "empty"}, {"id": 808, "name": "twisting_vines", "displayName": "Twisting Vines Plant", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9514, "minStateId": 9514, "maxStateId": 9539, "drops": [], "boundingBox": "empty"}, {"id": 809, "name": "crimson_roots", "displayName": "Crimson Roots", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13279, "minStateId": 13279, "maxStateId": 13279, "drops": [237], "boundingBox": "empty"}, {"id": 810, "name": "crimson_planks", "displayName": "Crimson Planks", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 8613, "minStateId": 8613, "maxStateId": 8613, "drops": [45], "boundingBox": "block"}, {"id": 811, "name": "warped_planks", "displayName": "Warped Planks", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 2403, "minStateId": 2403, "maxStateId": 2403, "drops": [46], "boundingBox": "block"}, {"id": 812, "name": "crimson_double_slab", "displayName": "Crimson Slab", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 1965, "minStateId": 1965, "maxStateId": 1966, "drops": [261], "boundingBox": "block"}, {"id": 8028, "name": "crimson_slab", "displayName": "Crimson Slab", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 10558, "minStateId": 10558, "maxStateId": 10559, "drops": [261], "boundingBox": "block"}, {"id": 813, "name": "warped_double_slab", "displayName": "Warped Slab", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 7227, "minStateId": 7227, "maxStateId": 7228, "drops": [262], "boundingBox": "block"}, {"id": 8029, "name": "warped_slab", "displayName": "Warped Slab", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11683, "minStateId": 11683, "maxStateId": 11684, "drops": [262], "boundingBox": "block"}, {"id": 814, "name": "crimson_pressure_plate", "displayName": "Crimson Pressure Plate", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 14096, "minStateId": 14096, "maxStateId": 14111, "drops": [707], "boundingBox": "empty"}, {"id": 815, "name": "warped_pressure_plate", "displayName": "Warped Pressure Plate", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 708, "minStateId": 708, "maxStateId": 723, "drops": [708], "boundingBox": "empty"}, {"id": 816, "name": "crimson_fence", "displayName": "<PERSON> Fence", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13802, "minStateId": 13802, "maxStateId": 13802, "drops": [319], "boundingBox": "block"}, {"id": 817, "name": "warped_fence", "displayName": "Warped <PERSON>", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 10103, "minStateId": 10103, "maxStateId": 10103, "drops": [320], "boundingBox": "block"}, {"id": 818, "name": "crimson_trapdoor", "displayName": "Crimson Trapdoor", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7183, "minStateId": 7183, "maxStateId": 7198, "drops": [739], "boundingBox": "block"}, {"id": 819, "name": "warped_trapdoor", "displayName": "Warped Trapdoor", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 8089, "minStateId": 8089, "maxStateId": 8104, "drops": [740], "boundingBox": "block"}, {"id": 820, "name": "crimson_fence_gate", "displayName": "Crimson Fence Gate", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 7977, "minStateId": 7977, "maxStateId": 7992, "drops": [758], "boundingBox": "block"}, {"id": 821, "name": "warped_fence_gate", "displayName": "Warped Fence Gate", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 9190, "minStateId": 9190, "maxStateId": 9205, "drops": [759], "boundingBox": "block"}, {"id": 822, "name": "crimson_stairs", "displayName": "Crimson Stairs", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11426, "minStateId": 11426, "maxStateId": 11433, "drops": [392], "boundingBox": "block"}, {"id": 823, "name": "warped_stairs", "displayName": "Warped Stairs", "hardness": 2, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6109, "minStateId": 6109, "maxStateId": 6116, "drops": [393], "boundingBox": "block"}, {"id": 824, "name": "crimson_button", "displayName": "<PERSON>", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7291, "minStateId": 7291, "maxStateId": 7302, "drops": [692], "boundingBox": "empty"}, {"id": 825, "name": "warped_button", "displayName": "Warped <PERSON>", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12931, "minStateId": 12931, "maxStateId": 12942, "drops": [693], "boundingBox": "empty"}, {"id": 826, "name": "crimson_door", "displayName": "Crimson Door", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 6618, "minStateId": 6618, "maxStateId": 6649, "drops": [719], "boundingBox": "block"}, {"id": 827, "name": "warped_door", "displayName": "Warped Door", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 33, "minStateId": 33, "maxStateId": 64, "drops": [720], "boundingBox": "block"}, {"id": 828, "name": "crimson_standing_sign", "displayName": "Crimson Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13013, "minStateId": 13013, "maxStateId": 13028, "drops": [892], "boundingBox": "empty"}, {"id": 829, "name": "warped_standing_sign", "displayName": "Warped Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12861, "minStateId": 12861, "maxStateId": 12876, "drops": [893], "boundingBox": "empty"}, {"id": 830, "name": "crimson_wall_sign", "displayName": "Crimson Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 558, "minStateId": 558, "maxStateId": 563, "drops": [892], "boundingBox": "empty"}, {"id": 831, "name": "warped_wall_sign", "displayName": "Warped Sign", "hardness": 1, "resistance": 1, "stackSize": 16, "diggable": true, "material": "mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 1146, "minStateId": 1146, "maxStateId": 1151, "drops": [893], "boundingBox": "empty"}, {"id": 832, "name": "structure_block", "displayName": "Structure Block", "hardness": -1, "resistance": 3600000, "stackSize": 64, "diggable": false, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11527, "minStateId": 11527, "maxStateId": 11532, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 833, "name": "jigsaw", "displayName": "Jigsaw Block", "hardness": -1, "resistance": 3600000, "stackSize": 64, "diggable": false, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 8649, "minStateId": 8649, "maxStateId": 8672, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 834, "name": "composter", "displayName": "Composter", "hardness": 0.6, "resistance": 0.6, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 9206, "minStateId": 9206, "maxStateId": 9214, "drops": [1192], "boundingBox": "block"}, {"id": 835, "name": "target", "displayName": "Target", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/hoe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11542, "minStateId": 11542, "maxStateId": 11542, "drops": [670], "boundingBox": "block"}, {"id": 836, "name": "bee_nest", "displayName": "Bee Nest", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9582, "minStateId": 9582, "maxStateId": 9605, "drops": [], "boundingBox": "block"}, {"id": 837, "name": "beehive", "displayName": "Beehive", "hardness": 0.6, "resistance": 0.6, "stackSize": 64, "diggable": true, "material": "mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11274, "minStateId": 11274, "maxStateId": 11297, "drops": [1211], "boundingBox": "block"}, {"id": 838, "name": "honey_block", "displayName": "Honey Block", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 2359, "minStateId": 2359, "maxStateId": 2359, "drops": [664], "boundingBox": "block"}, {"id": 839, "name": "honeycomb_block", "displayName": "Honeycomb Block", "hardness": 0.6, "resistance": 0.6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7337, "minStateId": 7337, "maxStateId": 7337, "drops": [1213], "boundingBox": "block"}, {"id": 840, "name": "netherite_block", "displayName": "Block of Netherite", "hardness": 50, "resistance": 1200, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6173, "minStateId": 6173, "maxStateId": 6173, "harvestTools": {"836": true, "841": true}, "drops": [91], "boundingBox": "block"}, {"id": 841, "name": "ancient_debris", "displayName": "Ancient Debris", "hardness": 30, "resistance": 1200, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11257, "minStateId": 11257, "maxStateId": 11257, "harvestTools": {"836": true, "841": true}, "drops": [80], "boundingBox": "block"}, {"id": 842, "name": "crying_obsidian", "displayName": "Crying Obsidian", "hardness": 50, "resistance": 1200, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 10, "filterLight": 15, "defaultState": 11927, "minStateId": 11927, "maxStateId": 11927, "harvestTools": {"836": true, "841": true}, "drops": [1215], "boundingBox": "block"}, {"id": 843, "name": "respawn_anchor", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "hardness": 50, "resistance": 1200, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1967, "minStateId": 1967, "maxStateId": 1971, "harvestTools": {"836": true, "841": true}, "drops": [1228], "boundingBox": "block"}, {"id": 848, "name": "lodestone", "displayName": "Lodestone", "hardness": 3.5, "resistance": 3.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 14083, "minStateId": 14083, "maxStateId": 14083, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1214], "boundingBox": "block"}, {"id": 849, "name": "blackstone", "displayName": "Blackstone", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13299, "minStateId": 13299, "maxStateId": 13299, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1216], "boundingBox": "block"}, {"id": 850, "name": "blackstone_stairs", "displayName": "Blackstone Stairs", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 12247, "minStateId": 12247, "maxStateId": 12254, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1218], "boundingBox": "block"}, {"id": 851, "name": "blackstone_wall", "displayName": "Blackstone Wall", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6715, "minStateId": 6715, "maxStateId": 6876, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [411], "boundingBox": "block"}, {"id": 852, "name": "blackstone_double_slab", "displayName": "Blackstone Slab", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 127, "minStateId": 127, "maxStateId": 128, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1217], "boundingBox": "block"}, {"id": 8030, "name": "blackstone_slab", "displayName": "Blackstone Slab", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 2393, "minStateId": 2393, "maxStateId": 2394, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1217], "boundingBox": "block"}, {"id": 853, "name": "polished_blackstone", "displayName": "Polished Blackstone", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6070, "minStateId": 6070, "maxStateId": 6070, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1220], "boundingBox": "block"}, {"id": 854, "name": "polished_blackstone_bricks", "displayName": "Polished Blackstone Bricks", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7996, "minStateId": 7996, "maxStateId": 7996, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1224], "boundingBox": "block"}, {"id": 855, "name": "cracked_polished_blackstone_bricks", "displayName": "Cracked Polished Blackstone Bricks", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12877, "minStateId": 12877, "maxStateId": 12877, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1227], "boundingBox": "block"}, {"id": 856, "name": "chiseled_polished_blackstone", "displayName": "Chiseled Polished Blackstone", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 8852, "minStateId": 8852, "maxStateId": 8852, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1223], "boundingBox": "block"}, {"id": 857, "name": "polished_blackstone_brick_double_slab", "displayName": "Polished Blackstone Brick Slab", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 1963, "minStateId": 1963, "maxStateId": 1964, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1225], "boundingBox": "block"}, {"id": 8031, "name": "polished_blackstone_brick_slab", "displayName": "Polished Blackstone Brick Slab", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6993, "minStateId": 6993, "maxStateId": 6994, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1225], "boundingBox": "block"}, {"id": 858, "name": "polished_blackstone_brick_stairs", "displayName": "Polished Blackstone Brick Stairs", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 7338, "minStateId": 7338, "maxStateId": 7345, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1226], "boundingBox": "block"}, {"id": 859, "name": "polished_blackstone_brick_wall", "displayName": "Polished Blackstone Brick Wall", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 2446, "minStateId": 2446, "maxStateId": 2607, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [413], "boundingBox": "block"}, {"id": 860, "name": "gilded_blackstone", "displayName": "Gilded Blackstone", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7472, "minStateId": 7472, "maxStateId": 7472, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1219], "boundingBox": "block"}, {"id": 861, "name": "polished_blackstone_stairs", "displayName": "Polished Blackstone Stairs", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 7142, "minStateId": 7142, "maxStateId": 7149, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1222], "boundingBox": "block"}, {"id": 862, "name": "polished_blackstone_double_slab", "displayName": "Polished Blackstone Slab", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 1781, "minStateId": 1781, "maxStateId": 1782, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1221], "boundingBox": "block"}, {"id": 8032, "name": "polished_blackstone_slab", "displayName": "Polished Blackstone Slab", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11140, "minStateId": 11140, "maxStateId": 11141, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [1221], "boundingBox": "block"}, {"id": 863, "name": "polished_blackstone_pressure_plate", "displayName": "Polished Blackstone Pressure Plate", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11380, "minStateId": 11380, "maxStateId": 11395, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [695], "boundingBox": "empty"}, {"id": 864, "name": "polished_blackstone_button", "displayName": "Polished Blackstone Button", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13547, "minStateId": 13547, "maxStateId": 13558, "drops": [682], "boundingBox": "empty"}, {"id": 865, "name": "polished_blackstone_wall", "displayName": "Polished Blackstone Wall", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11934, "minStateId": 11934, "maxStateId": 12095, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [412], "boundingBox": "block"}, {"id": 866, "name": "chiseled_nether_bricks", "displayName": "Chiseled Nether Bricks", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12930, "minStateId": 12930, "maxStateId": 12930, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [367], "boundingBox": "block"}, {"id": 867, "name": "cracked_nether_bricks", "displayName": "Cracked Nether Bricks", "hardness": 2, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7418, "minStateId": 7418, "maxStateId": 7418, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [366], "boundingBox": "block"}, {"id": 868, "name": "quartz_bricks", "displayName": "Quartz Bricks", "hardness": 0.8, "resistance": 0.8, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11502, "minStateId": 11502, "maxStateId": 11502, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [423], "boundingBox": "block"}, {"id": 869, "name": "candle", "displayName": "Candle", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13100, "minStateId": 13100, "maxStateId": 13107, "drops": [1229], "boundingBox": "block"}, {"id": 870, "name": "white_candle", "displayName": "White Candle", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9096, "minStateId": 9096, "maxStateId": 9103, "drops": [1230], "boundingBox": "block"}, {"id": 871, "name": "orange_candle", "displayName": "Orange Candle", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 1121, "minStateId": 1121, "maxStateId": 1128, "drops": [1231], "boundingBox": "block"}, {"id": 872, "name": "magenta_candle", "displayName": "<PERSON><PERSON><PERSON>", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 1181, "minStateId": 1181, "maxStateId": 1188, "drops": [1232], "boundingBox": "block"}, {"id": 873, "name": "light_blue_candle", "displayName": "Light Blue Candle", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7435, "minStateId": 7435, "maxStateId": 7442, "drops": [1233], "boundingBox": "block"}, {"id": 874, "name": "yellow_candle", "displayName": "Yellow Candle", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11343, "minStateId": 11343, "maxStateId": 11350, "drops": [1234], "boundingBox": "block"}, {"id": 875, "name": "lime_candle", "displayName": "<PERSON><PERSON>", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11519, "minStateId": 11519, "maxStateId": 11526, "drops": [1235], "boundingBox": "block"}, {"id": 876, "name": "pink_candle", "displayName": "Pink Candle", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13066, "minStateId": 13066, "maxStateId": 13073, "drops": [1236], "boundingBox": "block"}, {"id": 877, "name": "gray_candle", "displayName": "<PERSON>", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 2423, "minStateId": 2423, "maxStateId": 2430, "drops": [1237], "boundingBox": "block"}, {"id": 878, "name": "light_gray_candle", "displayName": "Light Gray Candle", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11372, "minStateId": 11372, "maxStateId": 11379, "drops": [1238], "boundingBox": "block"}, {"id": 879, "name": "cyan_candle", "displayName": "<PERSON><PERSON>", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13446, "minStateId": 13446, "maxStateId": 13453, "drops": [1239], "boundingBox": "block"}, {"id": 880, "name": "purple_candle", "displayName": "Purple Candle", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12269, "minStateId": 12269, "maxStateId": 12276, "drops": [1240], "boundingBox": "block"}, {"id": 881, "name": "blue_candle", "displayName": "Blue Candle", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 2, "minStateId": 2, "maxStateId": 9, "drops": [1241], "boundingBox": "block"}, {"id": 882, "name": "brown_candle", "displayName": "<PERSON> Candle", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 10530, "minStateId": 10530, "maxStateId": 10537, "drops": [1242], "boundingBox": "block"}, {"id": 883, "name": "green_candle", "displayName": "Green Candle", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 1973, "minStateId": 1973, "maxStateId": 1980, "drops": [1243], "boundingBox": "block"}, {"id": 884, "name": "red_candle", "displayName": "<PERSON> Candle", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7997, "minStateId": 7997, "maxStateId": 8004, "drops": [1244], "boundingBox": "block"}, {"id": 885, "name": "black_candle", "displayName": "Black Candle", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 592, "minStateId": 592, "maxStateId": 599, "drops": [1245], "boundingBox": "block"}, {"id": 886, "name": "candle_cake", "displayName": "Cake with Candle", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13289, "minStateId": 13289, "maxStateId": 13290, "drops": [1229], "boundingBox": "block"}, {"id": 887, "name": "white_candle_cake", "displayName": "Cake with White Candle", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13287, "minStateId": 13287, "maxStateId": 13288, "drops": [1230], "boundingBox": "block"}, {"id": 888, "name": "orange_candle_cake", "displayName": "Cake with Orange Candle", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 14051, "minStateId": 14051, "maxStateId": 14052, "drops": [1231], "boundingBox": "block"}, {"id": 889, "name": "magenta_candle_cake", "displayName": "Cake with <PERSON><PERSON><PERSON>dle", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11237, "minStateId": 11237, "maxStateId": 11238, "drops": [1232], "boundingBox": "block"}, {"id": 890, "name": "light_blue_candle_cake", "displayName": "Cake with Light Blue Candle", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 745, "minStateId": 745, "maxStateId": 746, "drops": [1233], "boundingBox": "block"}, {"id": 891, "name": "yellow_candle_cake", "displayName": "Cake with Yellow Candle", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 7523, "minStateId": 7523, "maxStateId": 7524, "drops": [1234], "boundingBox": "block"}, {"id": 892, "name": "lime_candle_cake", "displayName": "Cake with <PERSON><PERSON> Candle", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13874, "minStateId": 13874, "maxStateId": 13875, "drops": [1235], "boundingBox": "block"}, {"id": 893, "name": "pink_candle_cake", "displayName": "Cake with <PERSON> Candle", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6952, "minStateId": 6952, "maxStateId": 6953, "drops": [1236], "boundingBox": "block"}, {"id": 894, "name": "gray_candle_cake", "displayName": "Cake with <PERSON>", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 153, "minStateId": 153, "maxStateId": 154, "drops": [1237], "boundingBox": "block"}, {"id": 895, "name": "light_gray_candle_cake", "displayName": "Cake with Light Gray Candle", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 9094, "minStateId": 9094, "maxStateId": 9095, "drops": [1238], "boundingBox": "block"}, {"id": 896, "name": "cyan_candle_cake", "displayName": "Cake with <PERSON><PERSON>", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 2007, "minStateId": 2007, "maxStateId": 2008, "drops": [1239], "boundingBox": "block"}, {"id": 897, "name": "purple_candle_cake", "displayName": "Cake with <PERSON> Candle", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11914, "minStateId": 11914, "maxStateId": 11915, "drops": [1240], "boundingBox": "block"}, {"id": 898, "name": "blue_candle_cake", "displayName": "Cake with Blue Candle", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11130, "minStateId": 11130, "maxStateId": 11131, "drops": [1241], "boundingBox": "block"}, {"id": 899, "name": "brown_candle_cake", "displayName": "Cake with <PERSON> Candle", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6045, "minStateId": 6045, "maxStateId": 6046, "drops": [1242], "boundingBox": "block"}, {"id": 900, "name": "green_candle_cake", "displayName": "Cake with Green Candle", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 5974, "minStateId": 5974, "maxStateId": 5975, "drops": [1243], "boundingBox": "block"}, {"id": 901, "name": "red_candle_cake", "displayName": "Cake with <PERSON> Candle", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13422, "minStateId": 13422, "maxStateId": 13423, "drops": [1244], "boundingBox": "block"}, {"id": 902, "name": "black_candle_cake", "displayName": "Cake with Black Candle", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 7469, "minStateId": 7469, "maxStateId": 7470, "drops": [1245], "boundingBox": "block"}, {"id": 903, "name": "amethyst_block", "displayName": "Block of Amethyst", "hardness": 1.5, "resistance": 1.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 783, "minStateId": 783, "maxStateId": 783, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [85], "boundingBox": "block"}, {"id": 904, "name": "budding_amethyst", "displayName": "Budding Amethyst", "hardness": 1.5, "resistance": 1.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12224, "minStateId": 12224, "maxStateId": 12224, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [], "boundingBox": "block"}, {"id": 905, "name": "amethyst_cluster", "displayName": "Amethyst Cluster", "hardness": 1.5, "resistance": 1.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 5, "filterLight": 0, "defaultState": 13613, "minStateId": 13613, "maxStateId": 13618, "drops": [805], "boundingBox": "block"}, {"id": 906, "name": "large_amethyst_bud", "displayName": "Large Amethyst Bud", "hardness": 1.5, "resistance": 1.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 4, "filterLight": 0, "defaultState": 8043, "minStateId": 8043, "maxStateId": 8048, "drops": [], "boundingBox": "block"}, {"id": 907, "name": "medium_amethyst_bud", "displayName": "Medium Amethyst Bud", "hardness": 1.5, "resistance": 1.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 2, "filterLight": 0, "defaultState": 7220, "minStateId": 7220, "maxStateId": 7225, "drops": [], "boundingBox": "block"}, {"id": 908, "name": "small_amethyst_bud", "displayName": "Small Amethyst Bud", "hardness": 1.5, "resistance": 1.5, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 1, "filterLight": 0, "defaultState": 1054, "minStateId": 1054, "maxStateId": 1059, "drops": [], "boundingBox": "block"}, {"id": 909, "name": "tuff", "displayName": "<PERSON><PERSON>", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1103, "minStateId": 1103, "maxStateId": 1103, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [12], "boundingBox": "block"}, {"id": 910, "name": "tuff_slab", "displayName": "<PERSON><PERSON>", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 706, "minStateId": 706, "maxStateId": 707, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 8033, "name": "tuff_double_slab", "displayName": "<PERSON><PERSON>", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13477, "minStateId": 13477, "maxStateId": 13478, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 911, "name": "tuff_stairs", "displayName": "<PERSON><PERSON> St<PERSON>s", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 10573, "minStateId": 10573, "maxStateId": 10580, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 912, "name": "tuff_wall", "displayName": "<PERSON><PERSON>", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 5327, "minStateId": 5327, "maxStateId": 5488, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 913, "name": "polished_tuff", "displayName": "Polished <PERSON>", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12910, "minStateId": 12910, "maxStateId": 12910, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 914, "name": "polished_tuff_slab", "displayName": "Polished <PERSON><PERSON>", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 155, "minStateId": 155, "maxStateId": 156, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 8034, "name": "polished_tuff_double_slab", "displayName": "Polished <PERSON><PERSON>", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6616, "minStateId": 6616, "maxStateId": 6617, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 915, "name": "polished_tuff_stairs", "displayName": "Polished <PERSON><PERSON>", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 12962, "minStateId": 12962, "maxStateId": 12969, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 916, "name": "polished_tuff_wall", "displayName": "Polished <PERSON><PERSON>", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 2009, "minStateId": 2009, "maxStateId": 2170, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 917, "name": "chiseled_tuff", "displayName": "Chiseled <PERSON>", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13803, "minStateId": 13803, "maxStateId": 13803, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 918, "name": "tuff_bricks", "displayName": "<PERSON>ff Bricks", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11879, "minStateId": 11879, "maxStateId": 11879, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 919, "name": "tuff_brick_slab", "displayName": "Tuff Brick Slab", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6119, "minStateId": 6119, "maxStateId": 6120, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 8035, "name": "tuff_brick_double_slab", "displayName": "Tuff Brick Slab", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 10556, "minStateId": 10556, "maxStateId": 10557, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 920, "name": "tuff_brick_stairs", "displayName": "Tuff Brick Stairs", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11412, "minStateId": 11412, "maxStateId": 11419, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 921, "name": "tuff_brick_wall", "displayName": "Tuff Brick Wall", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 1796, "minStateId": 1796, "maxStateId": 1957, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 922, "name": "chiseled_tuff_bricks", "displayName": "Chiseled Tuff Bricks", "hardness": 1.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13136, "minStateId": 13136, "maxStateId": 13136, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 923, "name": "calcite", "displayName": "Calcite", "hardness": 0.75, "resistance": 0.75, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 637, "minStateId": 637, "maxStateId": 637, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [11], "boundingBox": "block"}, {"id": 924, "name": "tinted_glass", "displayName": "Tinted Glass", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 15, "defaultState": 10712, "minStateId": 10712, "maxStateId": 10712, "drops": [188], "boundingBox": "block"}, {"id": 925, "name": "powder_snow", "displayName": "Powder Snow", "hardness": 0.25, "resistance": 0.25, "stackSize": 1, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 1, "defaultState": 104, "minStateId": 104, "maxStateId": 104, "drops": [], "boundingBox": "empty"}, {"id": 926, "name": "sculk_sensor", "displayName": "Sculk Sensor", "hardness": 1.5, "resistance": 1.5, "stackSize": 64, "diggable": true, "material": "mineable/hoe", "transparent": false, "emitLight": 1, "filterLight": 0, "defaultState": 7235, "minStateId": 7235, "maxStateId": 7237, "drops": [], "boundingBox": "block"}, {"id": 927, "name": "calibrated_sculk_sensor", "displayName": "Calibrated Sculk Sensor", "hardness": 1.5, "resistance": 1.5, "stackSize": 64, "diggable": true, "material": "mineable/hoe", "transparent": false, "emitLight": 1, "filterLight": 0, "defaultState": 10070, "minStateId": 10070, "maxStateId": 10081, "drops": [], "boundingBox": "block"}, {"id": 928, "name": "sculk", "displayName": "Sculk", "hardness": 0.2, "resistance": 0.2, "stackSize": 64, "diggable": true, "material": "mineable/hoe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12266, "minStateId": 12266, "maxStateId": 12266, "drops": [], "boundingBox": "block"}, {"id": 929, "name": "sculk_vein", "displayName": "Sculk Vein", "hardness": 0.2, "resistance": 0.2, "stackSize": 64, "diggable": true, "material": "mineable/hoe", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 12794, "minStateId": 12794, "maxStateId": 12857, "drops": [], "boundingBox": "empty"}, {"id": 930, "name": "sculk_catalyst", "displayName": "Sculk Catalyst", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/hoe", "transparent": false, "emitLight": 6, "filterLight": 15, "defaultState": 5995, "minStateId": 5995, "maxStateId": 5996, "drops": [], "boundingBox": "block"}, {"id": 931, "name": "sculk_shrieker", "displayName": "<PERSON><PERSON><PERSON>", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/hoe", "transparent": false, "emitLight": 0, "filterLight": 1, "defaultState": 646, "minStateId": 646, "maxStateId": 649, "drops": [], "boundingBox": "block"}, {"id": 932, "name": "copper_block", "displayName": "Block of Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7967, "minStateId": 7967, "maxStateId": 7967, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [88], "boundingBox": "block"}, {"id": 933, "name": "exposed_copper", "displayName": "Exposed Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1361, "minStateId": 1361, "maxStateId": 1361, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [92], "boundingBox": "block"}, {"id": 934, "name": "weathered_copper", "displayName": "Weathered Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 14053, "minStateId": 14053, "maxStateId": 14053, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [93], "boundingBox": "block"}, {"id": 935, "name": "oxidized_copper", "displayName": "Oxidized Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 5931, "minStateId": 5931, "maxStateId": 5931, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [94], "boundingBox": "block"}, {"id": 936, "name": "copper_ore", "displayName": "Copper Ore", "hardness": 3, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 5932, "minStateId": 5932, "maxStateId": 5932, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [808], "boundingBox": "block"}, {"id": 937, "name": "deepslate_copper_ore", "displayName": "Deepslate Copper Ore", "hardness": 4.5, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 117, "minStateId": 117, "maxStateId": 117, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [808], "boundingBox": "block"}, {"id": 938, "name": "oxidized_cut_copper", "displayName": "Oxidized Cut Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9301, "minStateId": 9301, "maxStateId": 9301, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [102], "boundingBox": "block"}, {"id": 939, "name": "weathered_cut_copper", "displayName": "Weathered Cut Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12860, "minStateId": 12860, "maxStateId": 12860, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [101], "boundingBox": "block"}, {"id": 940, "name": "exposed_cut_copper", "displayName": "Exposed Cut Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11333, "minStateId": 11333, "maxStateId": 11333, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [100], "boundingBox": "block"}, {"id": 941, "name": "cut_copper", "displayName": "Cut Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 8005, "minStateId": 8005, "maxStateId": 8005, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [99], "boundingBox": "block"}, {"id": 942, "name": "oxidized_chiseled_copper", "displayName": "Oxidized Chiseled Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9070, "minStateId": 9070, "maxStateId": 9070, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 943, "name": "weathered_chiseled_copper", "displayName": "Weathered Chiseled Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1053, "minStateId": 1053, "maxStateId": 1053, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 944, "name": "exposed_chiseled_copper", "displayName": "Exposed Chiseled Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11794, "minStateId": 11794, "maxStateId": 11794, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 945, "name": "chiseled_copper", "displayName": "Chiseled Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9540, "minStateId": 9540, "maxStateId": 9540, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 946, "name": "waxed_oxidized_chiseled_copper", "displayName": "Waxed Oxidized Chiseled Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 75, "minStateId": 75, "maxStateId": 75, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 947, "name": "waxed_weathered_chiseled_copper", "displayName": "Waxed Weathered Chiseled Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6104, "minStateId": 6104, "maxStateId": 6104, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 948, "name": "waxed_exposed_chiseled_copper", "displayName": "Waxed Exposed Chiseled Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 705, "minStateId": 705, "maxStateId": 705, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 949, "name": "waxed_chiseled_copper", "displayName": "Waxed Chiseled Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13619, "minStateId": 13619, "maxStateId": 13619, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 950, "name": "oxidized_cut_copper_stairs", "displayName": "Oxidized Cut Copper Stairs", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 1107, "minStateId": 1107, "maxStateId": 1114, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [106], "boundingBox": "block"}, {"id": 951, "name": "weathered_cut_copper_stairs", "displayName": "Weathered Cut Copper Stairs", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 7150, "minStateId": 7150, "maxStateId": 7157, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [105], "boundingBox": "block"}, {"id": 952, "name": "exposed_cut_copper_stairs", "displayName": "Exposed Cut Copper Stairs", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 7475, "minStateId": 7475, "maxStateId": 7482, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [104], "boundingBox": "block"}, {"id": 953, "name": "cut_copper_stairs", "displayName": "Cut Copper Stairs", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 7492, "minStateId": 7492, "maxStateId": 7499, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [103], "boundingBox": "block"}, {"id": 954, "name": "oxidized_double_cut_copper_slab", "displayName": "Oxidized Cut Copper Slab", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 1359, "minStateId": 1359, "maxStateId": 1360, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [110], "boundingBox": "block"}, {"id": 8036, "name": "oxidized_cut_copper_slab", "displayName": "Oxidized Cut Copper Slab", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 9078, "minStateId": 9078, "maxStateId": 9079, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [110], "boundingBox": "block"}, {"id": 955, "name": "weathered_cut_copper_slab", "displayName": "Weathered Cut Copper Slab", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11187, "minStateId": 11187, "maxStateId": 11188, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [109], "boundingBox": "block"}, {"id": 8037, "name": "weathered_double_cut_copper_slab", "displayName": "Weathered Cut Copper Slab", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13045, "minStateId": 13045, "maxStateId": 13046, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [109], "boundingBox": "block"}, {"id": 956, "name": "exposed_double_cut_copper_slab", "displayName": "Exposed Cut Copper Slab", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 1382, "minStateId": 1382, "maxStateId": 1383, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [108], "boundingBox": "block"}, {"id": 8038, "name": "exposed_cut_copper_slab", "displayName": "Exposed Cut Copper Slab", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11811, "minStateId": 11811, "maxStateId": 11812, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [108], "boundingBox": "block"}, {"id": 957, "name": "double_cut_copper_slab", "displayName": "Cut Copper Slab", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6038, "minStateId": 6038, "maxStateId": 6039, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [107], "boundingBox": "block"}, {"id": 8039, "name": "cut_copper_slab", "displayName": "Cut Copper Slab", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 9025, "minStateId": 9025, "maxStateId": 9026, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [107], "boundingBox": "block"}, {"id": 958, "name": "waxed_copper", "displayName": "Waxed Block of Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13454, "minStateId": 13454, "maxStateId": 13454, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [111], "boundingBox": "block"}, {"id": 959, "name": "waxed_weathered_copper", "displayName": "Waxed Weathered Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 2000, "minStateId": 2000, "maxStateId": 2000, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [113], "boundingBox": "block"}, {"id": 960, "name": "waxed_exposed_copper", "displayName": "Waxed Exposed Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1981, "minStateId": 1981, "maxStateId": 1981, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [112], "boundingBox": "block"}, {"id": 961, "name": "waxed_oxidized_copper", "displayName": "Waxed Oxidized Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13244, "minStateId": 13244, "maxStateId": 13244, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [114], "boundingBox": "block"}, {"id": 962, "name": "waxed_oxidized_cut_copper", "displayName": "Waxed Oxidized Cut Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 636, "minStateId": 636, "maxStateId": 636, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [122], "boundingBox": "block"}, {"id": 963, "name": "waxed_weathered_cut_copper", "displayName": "Waxed Weathered Cut Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 8617, "minStateId": 8617, "maxStateId": 8617, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [121], "boundingBox": "block"}, {"id": 964, "name": "waxed_exposed_cut_copper", "displayName": "Waxed Exposed Cut Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6208, "minStateId": 6208, "maxStateId": 6208, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [120], "boundingBox": "block"}, {"id": 965, "name": "waxed_cut_copper", "displayName": "Waxed Cut Copper", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 12993, "minStateId": 12993, "maxStateId": 12993, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [119], "boundingBox": "block"}, {"id": 966, "name": "waxed_oxidized_cut_copper_stairs", "displayName": "Waxed Oxidized Cut Copper Stairs", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 10058, "minStateId": 10058, "maxStateId": 10065, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [126], "boundingBox": "block"}, {"id": 967, "name": "waxed_weathered_cut_copper_stairs", "displayName": "Waxed Weathered Cut Copper Stairs", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11334, "minStateId": 11334, "maxStateId": 11341, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [125], "boundingBox": "block"}, {"id": 968, "name": "waxed_exposed_cut_copper_stairs", "displayName": "Waxed Exposed Cut Copper Stairs", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6686, "minStateId": 6686, "maxStateId": 6693, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [124], "boundingBox": "block"}, {"id": 969, "name": "waxed_cut_copper_stairs", "displayName": "Waxed Cut Copper Stairs", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 1155, "minStateId": 1155, "maxStateId": 1162, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [123], "boundingBox": "block"}, {"id": 970, "name": "waxed_oxidized_cut_copper_slab", "displayName": "Waxed Oxidized Cut Copper Slab", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 2001, "minStateId": 2001, "maxStateId": 2002, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [130], "boundingBox": "block"}, {"id": 8040, "name": "waxed_oxidized_double_cut_copper_slab", "displayName": "Waxed Oxidized Cut Copper Slab", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13472, "minStateId": 13472, "maxStateId": 13473, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [130], "boundingBox": "block"}, {"id": 971, "name": "waxed_weathered_double_cut_copper_slab", "displayName": "Waxed Weathered Cut Copper Slab", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 7994, "minStateId": 7994, "maxStateId": 7995, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [129], "boundingBox": "block"}, {"id": 8041, "name": "waxed_weathered_cut_copper_slab", "displayName": "Waxed Weathered Cut Copper Slab", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 11755, "minStateId": 11755, "maxStateId": 11756, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [129], "boundingBox": "block"}, {"id": 972, "name": "waxed_exposed_cut_copper_slab", "displayName": "Waxed Exposed Cut Copper Slab", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 680, "minStateId": 680, "maxStateId": 681, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [128], "boundingBox": "block"}, {"id": 8042, "name": "waxed_exposed_double_cut_copper_slab", "displayName": "Waxed Exposed Cut Copper Slab", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 12096, "minStateId": 12096, "maxStateId": 12097, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [128], "boundingBox": "block"}, {"id": 973, "name": "waxed_double_cut_copper_slab", "displayName": "Waxed Cut Copper Slab", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 9215, "minStateId": 9215, "maxStateId": 9216, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [127], "boundingBox": "block"}, {"id": 8043, "name": "waxed_cut_copper_slab", "displayName": "Waxed Cut Copper Slab", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13620, "minStateId": 13620, "maxStateId": 13621, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [127], "boundingBox": "block"}, {"id": 974, "name": "copper_door", "displayName": "Copper Door", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 10676, "minStateId": 10676, "maxStateId": 10707, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 975, "name": "exposed_copper_door", "displayName": "Exposed Copper Door", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 8049, "minStateId": 8049, "maxStateId": 8080, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 976, "name": "oxidized_copper_door", "displayName": "Oxidized Copper Door", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 14117, "minStateId": 14117, "maxStateId": 14148, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 977, "name": "weathered_copper_door", "displayName": "Weathered Copper Door", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11588, "minStateId": 11588, "maxStateId": 11619, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 978, "name": "waxed_copper_door", "displayName": "Waxed Copper Door", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13488, "minStateId": 13488, "maxStateId": 13519, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 979, "name": "waxed_exposed_copper_door", "displayName": "Waxed Exposed Copper Door", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9255, "minStateId": 9255, "maxStateId": 9286, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 980, "name": "waxed_oxidized_copper_door", "displayName": "Waxed Oxidized Copper Door", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 10584, "minStateId": 10584, "maxStateId": 10615, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 981, "name": "waxed_weathered_copper_door", "displayName": "Waxed Weathered Copper Door", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12308, "minStateId": 12308, "maxStateId": 12339, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 982, "name": "copper_trapdoor", "displayName": "Copper Trapdoor", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 10082, "minStateId": 10082, "maxStateId": 10097, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 983, "name": "exposed_copper_trapdoor", "displayName": "Exposed Copper Trapdoor", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11239, "minStateId": 11239, "maxStateId": 11254, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 984, "name": "oxidized_copper_trapdoor", "displayName": "Oxidized Copper Trapdoor", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7452, "minStateId": 7452, "maxStateId": 7467, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 985, "name": "weathered_copper_trapdoor", "displayName": "Weathered Copper Trapdoor", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11731, "minStateId": 11731, "maxStateId": 11746, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 986, "name": "waxed_copper_trapdoor", "displayName": "Waxed Copper Trapdoor", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 5915, "minStateId": 5915, "maxStateId": 5930, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 987, "name": "waxed_exposed_copper_trapdoor", "displayName": "Waxed Exposed Copper Trapdoor", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 10514, "minStateId": 10514, "maxStateId": 10529, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 988, "name": "waxed_oxidized_copper_trapdoor", "displayName": "Waxed Oxidized Copper Trapdoor", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 8623, "minStateId": 8623, "maxStateId": 8638, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 989, "name": "waxed_weathered_copper_trapdoor", "displayName": "Waxed Weathered Copper Trapdoor", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 8123, "minStateId": 8123, "maxStateId": 8138, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 990, "name": "copper_grate", "displayName": "Copper Grate", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 1119, "minStateId": 1119, "maxStateId": 1119, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 991, "name": "exposed_copper_grate", "displayName": "Exposed Copper Grate", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9168, "minStateId": 9168, "maxStateId": 9168, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 992, "name": "weathered_copper_grate", "displayName": "Weathered Copper Grate", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 6118, "minStateId": 6118, "maxStateId": 6118, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 993, "name": "oxidized_copper_grate", "displayName": "Oxidized Copper Grate", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11846, "minStateId": 11846, "maxStateId": 11846, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 994, "name": "waxed_copper_grate", "displayName": "Waxed Copper Grate", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9169, "minStateId": 9169, "maxStateId": 9169, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 995, "name": "waxed_exposed_copper_grate", "displayName": "Waxed Exposed Copper Grate", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 1358, "minStateId": 1358, "maxStateId": 1358, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 996, "name": "waxed_weathered_copper_grate", "displayName": "Waxed Weathered Copper Grate", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7167, "minStateId": 7167, "maxStateId": 7167, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 997, "name": "waxed_oxidized_copper_grate", "displayName": "Waxed Oxidized Copper Grate", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13876, "minStateId": 13876, "maxStateId": 13876, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 998, "name": "copper_bulb", "displayName": "Copper Bulb", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7238, "minStateId": 7238, "maxStateId": 7241, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 999, "name": "exposed_copper_bulb", "displayName": "Exposed Copper Bulb", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11421, "minStateId": 11421, "maxStateId": 11424, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"name": "hard_blue_stained_glass_pane", "id": 14085, "minStateId": 14085, "maxStateId": 14085, "displayName": "Hard Blue Stained Glass Pane", "defaultState": 14085, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"id": 1027, "name": "cobbled_deepslate_wall", "displayName": "Cobbled Deepslate Wall", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13888, "minStateId": 13888, "maxStateId": 14049, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [414], "boundingBox": "block"}, {"id": 1045, "name": "raw_iron_block", "displayName": "Block of Raw Iron", "hardness": 5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 14086, "minStateId": 14086, "maxStateId": 14086, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [82], "boundingBox": "block"}, {"id": 1057, "name": "trial_spawner", "displayName": "Trial Spawner", "hardness": 50, "resistance": 50, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 1, "defaultState": 13880, "minStateId": 13880, "maxStateId": 13885, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"name": "hard_light_gray_stained_glass", "id": 13801, "minStateId": 13801, "maxStateId": 13801, "displayName": "Hard Light Gray Stained Glass", "defaultState": 13801, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "info_update2", "id": 13612, "minStateId": 13612, "maxStateId": 13612, "displayName": "Info Update2", "defaultState": 13612, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"id": 1028, "name": "polished_deepslate", "displayName": "Polished Deepslate", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13476, "minStateId": 13476, "maxStateId": 13476, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [10], "boundingBox": "block"}, {"id": 1031, "name": "polished_deepslate_wall", "displayName": "Polished Deepslate Wall", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13622, "minStateId": 13622, "maxStateId": 13783, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [415], "boundingBox": "block"}, {"id": 1056, "name": "crafter", "displayName": "Crafter", "hardness": 1.5, "resistance": 3.5, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13559, "minStateId": 13559, "maxStateId": 13606, "drops": [], "boundingBox": "block"}, {"name": "element_84", "id": 13406, "minStateId": 13406, "maxStateId": 13406, "displayName": "Element 84", "defaultState": 13406, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_85", "id": 13405, "minStateId": 13405, "maxStateId": 13405, "displayName": "Element 85", "defaultState": 13405, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_86", "id": 13404, "minStateId": 13404, "maxStateId": 13404, "displayName": "Element 86", "defaultState": 13404, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_87", "id": 13403, "minStateId": 13403, "maxStateId": 13403, "displayName": "Element 87", "defaultState": 13403, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_80", "id": 13402, "minStateId": 13402, "maxStateId": 13402, "displayName": "Element 80", "defaultState": 13402, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_81", "id": 13401, "minStateId": 13401, "maxStateId": 13401, "displayName": "Element 81", "defaultState": 13401, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_82", "id": 13400, "minStateId": 13400, "maxStateId": 13400, "displayName": "Element 82", "defaultState": 13400, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_83", "id": 13399, "minStateId": 13399, "maxStateId": 13399, "displayName": "Element 83", "defaultState": 13399, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_88", "id": 13398, "minStateId": 13398, "maxStateId": 13398, "displayName": "Element 88", "defaultState": 13398, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_89", "id": 13397, "minStateId": 13397, "maxStateId": 13397, "displayName": "Element 89", "defaultState": 13397, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_97", "id": 13396, "minStateId": 13396, "maxStateId": 13396, "displayName": "Element 97", "defaultState": 13396, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_96", "id": 13395, "minStateId": 13395, "maxStateId": 13395, "displayName": "Element 96", "defaultState": 13395, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_95", "id": 13394, "minStateId": 13394, "maxStateId": 13394, "displayName": "Element 95", "defaultState": 13394, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_94", "id": 13393, "minStateId": 13393, "maxStateId": 13393, "displayName": "Element 94", "defaultState": 13393, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_93", "id": 13392, "minStateId": 13392, "maxStateId": 13392, "displayName": "Element 93", "defaultState": 13392, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_92", "id": 13391, "minStateId": 13391, "maxStateId": 13391, "displayName": "Element 92", "defaultState": 13391, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_91", "id": 13390, "minStateId": 13390, "maxStateId": 13390, "displayName": "Element 91", "defaultState": 13390, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_90", "id": 13389, "minStateId": 13389, "maxStateId": 13389, "displayName": "Element 90", "defaultState": 13389, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_99", "id": 13388, "minStateId": 13388, "maxStateId": 13388, "displayName": "Element 99", "defaultState": 13388, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_98", "id": 13387, "minStateId": 13387, "maxStateId": 13387, "displayName": "Element 98", "defaultState": 13387, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_68", "id": 13386, "minStateId": 13386, "maxStateId": 13386, "displayName": "Element 68", "defaultState": 13386, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_69", "id": 13385, "minStateId": 13385, "maxStateId": 13385, "displayName": "Element 69", "defaultState": 13385, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_62", "id": 13384, "minStateId": 13384, "maxStateId": 13384, "displayName": "Element 62", "defaultState": 13384, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_63", "id": 13383, "minStateId": 13383, "maxStateId": 13383, "displayName": "Element 63", "defaultState": 13383, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_60", "id": 13382, "minStateId": 13382, "maxStateId": 13382, "displayName": "Element 60", "defaultState": 13382, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_61", "id": 13381, "minStateId": 13381, "maxStateId": 13381, "displayName": "Element 61", "defaultState": 13381, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_66", "id": 13380, "minStateId": 13380, "maxStateId": 13380, "displayName": "Element 66", "defaultState": 13380, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_67", "id": 13379, "minStateId": 13379, "maxStateId": 13379, "displayName": "Element 67", "defaultState": 13379, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_64", "id": 13378, "minStateId": 13378, "maxStateId": 13378, "displayName": "Element 64", "defaultState": 13378, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_65", "id": 13377, "minStateId": 13377, "maxStateId": 13377, "displayName": "Element 65", "defaultState": 13377, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_79", "id": 13376, "minStateId": 13376, "maxStateId": 13376, "displayName": "Element 79", "defaultState": 13376, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_78", "id": 13375, "minStateId": 13375, "maxStateId": 13375, "displayName": "Element 78", "defaultState": 13375, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_75", "id": 13374, "minStateId": 13374, "maxStateId": 13374, "displayName": "Element 75", "defaultState": 13374, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_74", "id": 13373, "minStateId": 13373, "maxStateId": 13373, "displayName": "Element 74", "defaultState": 13373, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_77", "id": 13372, "minStateId": 13372, "maxStateId": 13372, "displayName": "Element 77", "defaultState": 13372, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_76", "id": 13371, "minStateId": 13371, "maxStateId": 13371, "displayName": "Element 76", "defaultState": 13371, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_71", "id": 13370, "minStateId": 13370, "maxStateId": 13370, "displayName": "Element 71", "defaultState": 13370, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_70", "id": 13369, "minStateId": 13369, "maxStateId": 13369, "displayName": "Element 70", "defaultState": 13369, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_73", "id": 13368, "minStateId": 13368, "maxStateId": 13368, "displayName": "Element 73", "defaultState": 13368, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_72", "id": 13367, "minStateId": 13367, "maxStateId": 13367, "displayName": "Element 72", "defaultState": 13367, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_40", "id": 13366, "minStateId": 13366, "maxStateId": 13366, "displayName": "Element 40", "defaultState": 13366, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_41", "id": 13365, "minStateId": 13365, "maxStateId": 13365, "displayName": "Element 41", "defaultState": 13365, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_42", "id": 13364, "minStateId": 13364, "maxStateId": 13364, "displayName": "Element 42", "defaultState": 13364, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_43", "id": 13363, "minStateId": 13363, "maxStateId": 13363, "displayName": "Element 43", "defaultState": 13363, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_44", "id": 13362, "minStateId": 13362, "maxStateId": 13362, "displayName": "Element 44", "defaultState": 13362, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_45", "id": 13361, "minStateId": 13361, "maxStateId": 13361, "displayName": "Element 45", "defaultState": 13361, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_46", "id": 13360, "minStateId": 13360, "maxStateId": 13360, "displayName": "Element 46", "defaultState": 13360, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_47", "id": 13359, "minStateId": 13359, "maxStateId": 13359, "displayName": "Element 47", "defaultState": 13359, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_48", "id": 13358, "minStateId": 13358, "maxStateId": 13358, "displayName": "Element 48", "defaultState": 13358, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_49", "id": 13357, "minStateId": 13357, "maxStateId": 13357, "displayName": "Element 49", "defaultState": 13357, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_53", "id": 13356, "minStateId": 13356, "maxStateId": 13356, "displayName": "Element 53", "defaultState": 13356, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_52", "id": 13355, "minStateId": 13355, "maxStateId": 13355, "displayName": "Element 52", "defaultState": 13355, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_51", "id": 13354, "minStateId": 13354, "maxStateId": 13354, "displayName": "Element 51", "defaultState": 13354, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_50", "id": 13353, "minStateId": 13353, "maxStateId": 13353, "displayName": "Element 50", "defaultState": 13353, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_57", "id": 13352, "minStateId": 13352, "maxStateId": 13352, "displayName": "Element 57", "defaultState": 13352, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_56", "id": 13351, "minStateId": 13351, "maxStateId": 13351, "displayName": "Element 56", "defaultState": 13351, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_55", "id": 13350, "minStateId": 13350, "maxStateId": 13350, "displayName": "Element 55", "defaultState": 13350, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_54", "id": 13349, "minStateId": 13349, "maxStateId": 13349, "displayName": "Element 54", "defaultState": 13349, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_59", "id": 13348, "minStateId": 13348, "maxStateId": 13348, "displayName": "Element 59", "defaultState": 13348, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_58", "id": 13347, "minStateId": 13347, "maxStateId": 13347, "displayName": "Element 58", "defaultState": 13347, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_26", "id": 13346, "minStateId": 13346, "maxStateId": 13346, "displayName": "Element 26", "defaultState": 13346, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_27", "id": 13345, "minStateId": 13345, "maxStateId": 13345, "displayName": "Element 27", "defaultState": 13345, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_24", "id": 13344, "minStateId": 13344, "maxStateId": 13344, "displayName": "Element 24", "defaultState": 13344, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_25", "id": 13343, "minStateId": 13343, "maxStateId": 13343, "displayName": "Element 25", "defaultState": 13343, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_22", "id": 13342, "minStateId": 13342, "maxStateId": 13342, "displayName": "Element 22", "defaultState": 13342, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_23", "id": 13341, "minStateId": 13341, "maxStateId": 13341, "displayName": "Element 23", "defaultState": 13341, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_20", "id": 13340, "minStateId": 13340, "maxStateId": 13340, "displayName": "Element 20", "defaultState": 13340, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_21", "id": 13339, "minStateId": 13339, "maxStateId": 13339, "displayName": "Element 21", "defaultState": 13339, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_28", "id": 13338, "minStateId": 13338, "maxStateId": 13338, "displayName": "Element 28", "defaultState": 13338, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_29", "id": 13337, "minStateId": 13337, "maxStateId": 13337, "displayName": "Element 29", "defaultState": 13337, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_39", "id": 13336, "minStateId": 13336, "maxStateId": 13336, "displayName": "Element 39", "defaultState": 13336, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_38", "id": 13335, "minStateId": 13335, "maxStateId": 13335, "displayName": "Element 38", "defaultState": 13335, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_31", "id": 13334, "minStateId": 13334, "maxStateId": 13334, "displayName": "Element 31", "defaultState": 13334, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_30", "id": 13333, "minStateId": 13333, "maxStateId": 13333, "displayName": "Element 30", "defaultState": 13333, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_33", "id": 13332, "minStateId": 13332, "maxStateId": 13332, "displayName": "Element 33", "defaultState": 13332, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_32", "id": 13331, "minStateId": 13331, "maxStateId": 13331, "displayName": "Element 32", "defaultState": 13331, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_35", "id": 13330, "minStateId": 13330, "maxStateId": 13330, "displayName": "Element 35", "defaultState": 13330, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_34", "id": 13329, "minStateId": 13329, "maxStateId": 13329, "displayName": "Element 34", "defaultState": 13329, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_37", "id": 13328, "minStateId": 13328, "maxStateId": 13328, "displayName": "Element 37", "defaultState": 13328, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_36", "id": 13327, "minStateId": 13327, "maxStateId": 13327, "displayName": "Element 36", "defaultState": 13327, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_19", "id": 13326, "minStateId": 13326, "maxStateId": 13326, "displayName": "Element 19", "defaultState": 13326, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_18", "id": 13325, "minStateId": 13325, "maxStateId": 13325, "displayName": "Element 18", "defaultState": 13325, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_17", "id": 13324, "minStateId": 13324, "maxStateId": 13324, "displayName": "Element 17", "defaultState": 13324, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_16", "id": 13323, "minStateId": 13323, "maxStateId": 13323, "displayName": "Element 16", "defaultState": 13323, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_15", "id": 13322, "minStateId": 13322, "maxStateId": 13322, "displayName": "Element 15", "defaultState": 13322, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_14", "id": 13321, "minStateId": 13321, "maxStateId": 13321, "displayName": "Element 14", "defaultState": 13321, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_13", "id": 13320, "minStateId": 13320, "maxStateId": 13320, "displayName": "Element 13", "defaultState": 13320, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_12", "id": 13319, "minStateId": 13319, "maxStateId": 13319, "displayName": "Element 12", "defaultState": 13319, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_11", "id": 13318, "minStateId": 13318, "maxStateId": 13318, "displayName": "Element 11", "defaultState": 13318, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_10", "id": 13317, "minStateId": 13317, "maxStateId": 13317, "displayName": "Element 10", "defaultState": 13317, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"id": 1000, "name": "weathered_copper_bulb", "displayName": "Weathered Copper Bulb", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 13271, "minStateId": 13271, "maxStateId": 13274, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"name": "hard_pink_stained_glass_pane", "id": 13242, "minStateId": 13242, "maxStateId": 13242, "displayName": "Hard Pink Stained Glass Pane", "defaultState": 13242, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "colored_torch_rg", "id": 13230, "minStateId": 13230, "maxStateId": 13241, "displayName": "Colored Torch Rg", "defaultState": 13230, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "colored_torch_bp", "id": 13218, "minStateId": 13218, "maxStateId": 13229, "displayName": "Colored Torch Bp", "defaultState": 13218, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "chemical_heat", "id": 13138, "minStateId": 13138, "maxStateId": 13138, "displayName": "Chemical Heat", "defaultState": 13138, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "netherreactor", "id": 13119, "minStateId": 13119, "maxStateId": 13119, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "defaultState": 13119, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"id": 1007, "name": "pointed_dripstone", "displayName": "Pointed Dripstone", "hardness": 1.5, "resistance": 3, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13108, "minStateId": 13108, "maxStateId": 13117, "drops": [1250], "boundingBox": "block"}, {"id": 1009, "name": "cave_vines_head_with_berries", "displayName": "Cave Vines", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13176, "minStateId": 13176, "maxStateId": 13201, "drops": [], "boundingBox": "empty"}, {"id": 1011, "name": "spore_blossom", "displayName": "Spore Blossom", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 13012, "minStateId": 13012, "maxStateId": 13012, "drops": [232], "boundingBox": "empty"}, {"id": 1026, "name": "cobbled_deepslate_slab", "displayName": "Cobbled Deepslate Slab", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13010, "minStateId": 13010, "maxStateId": 13011, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [651], "boundingBox": "block"}, {"id": 1037, "name": "deepslate_brick_stairs", "displayName": "Deepslate Brick Stairs", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 13120, "minStateId": 13120, "maxStateId": 13127, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [636], "boundingBox": "block"}, {"name": "chemistry_table", "id": 12994, "minStateId": 12994, "maxStateId": 13009, "displayName": "Chemistry Table", "defaultState": 12994, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "camera", "id": 12984, "minStateId": 12984, "maxStateId": 12984, "displayName": "Camera", "defaultState": 12984, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_8", "id": 12983, "minStateId": 12983, "maxStateId": 12983, "displayName": "Element 8", "defaultState": 12983, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_9", "id": 12982, "minStateId": 12982, "maxStateId": 12982, "displayName": "Element 9", "defaultState": 12982, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_6", "id": 12981, "minStateId": 12981, "maxStateId": 12981, "displayName": "Element 6", "defaultState": 12981, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_7", "id": 12980, "minStateId": 12980, "maxStateId": 12980, "displayName": "Element 7", "defaultState": 12980, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_4", "id": 12979, "minStateId": 12979, "maxStateId": 12979, "displayName": "Element 4", "defaultState": 12979, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_5", "id": 12978, "minStateId": 12978, "maxStateId": 12978, "displayName": "Element 5", "defaultState": 12978, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_2", "id": 12977, "minStateId": 12977, "maxStateId": 12977, "displayName": "Element 2", "defaultState": 12977, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_3", "id": 12976, "minStateId": 12976, "maxStateId": 12976, "displayName": "Element 3", "defaultState": 12976, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_0", "id": 12975, "minStateId": 12975, "maxStateId": 12975, "displayName": "Element 0", "defaultState": 12975, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_1", "id": 12974, "minStateId": 12974, "maxStateId": 12974, "displayName": "Element 1", "defaultState": 12974, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "hard_cyan_stained_glass_pane", "id": 12925, "minStateId": 12925, "maxStateId": 12925, "displayName": "Hard <PERSON>an Stained Glass Pane", "defaultState": 12925, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "hard_white_stained_glass_pane", "id": 12912, "minStateId": 12912, "maxStateId": 12912, "displayName": "Hard White Stained Glass Pane", "defaultState": 12912, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "allow", "id": 12347, "minStateId": 12347, "maxStateId": 12347, "displayName": "Allow", "defaultState": 12347, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "hard_purple_stained_glass_pane", "id": 12267, "minStateId": 12267, "maxStateId": 12267, "displayName": "Hard Purple Stained Glass Pane", "defaultState": 12267, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "hard_red_stained_glass_pane", "id": 12257, "minStateId": 12257, "maxStateId": 12257, "displayName": "Hard Red Stained Glass Pane", "defaultState": 12257, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "hard_red_stained_glass", "id": 12129, "minStateId": 12129, "maxStateId": 12129, "displayName": "Hard Red Stained Glass", "defaultState": 12129, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"id": 1012, "name": "azalea", "displayName": "Azalea", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 12099, "minStateId": 12099, "maxStateId": 12099, "drops": [196], "boundingBox": "block"}, {"id": 1016, "name": "moss_block", "displayName": "Moss Block", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "mineable/hoe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11747, "minStateId": 11747, "maxStateId": 11747, "drops": [246], "boundingBox": "block"}, {"id": 1022, "name": "mud", "displayName": "Mud", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11886, "minStateId": 11886, "maxStateId": 11886, "drops": [32], "boundingBox": "block"}, {"id": 1024, "name": "cobbled_deepslate", "displayName": "Cobbled Deepslate", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11884, "minStateId": 11884, "maxStateId": 11884, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [9], "boundingBox": "block"}, {"id": 1055, "name": "decorated_pot", "displayName": "Decorated Pot", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 11929, "minStateId": 11929, "maxStateId": 11932, "drops": [287], "boundingBox": "block"}, {"name": "hard_green_stained_glass", "id": 11722, "minStateId": 11722, "maxStateId": 11722, "displayName": "Hard Green Stained Glass", "defaultState": 11722, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "hard_gray_stained_glass_pane", "id": 11682, "minStateId": 11682, "maxStateId": 11682, "displayName": "Hard Gray Stained Glass Pane", "defaultState": 11682, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"id": 1051, "name": "verdant_froglight", "displayName": "<PERSON><PERSON><PERSON>", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 15, "filterLight": 15, "defaultState": 11679, "minStateId": 11679, "maxStateId": 11681, "drops": [1252], "boundingBox": "block"}, {"id": 1052, "name": "pearlescent_froglight", "displayName": "Pearlescent Froglight", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 15, "filterLight": 15, "defaultState": 11620, "minStateId": 11620, "maxStateId": 11622, "drops": [1253], "boundingBox": "block"}, {"name": "hard_lime_stained_glass", "id": 11587, "minStateId": 11587, "maxStateId": 11587, "displayName": "Hard Lime Stained Glass", "defaultState": 11587, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "hard_magenta_stained_glass_pane", "id": 11493, "minStateId": 11493, "maxStateId": 11493, "displayName": "Hard Magenta Stained Glass Pane", "defaultState": 11493, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"id": 1004, "name": "waxed_weathered_copper_bulb", "displayName": "Waxed Weathered Copper Bulb", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 10066, "minStateId": 10066, "maxStateId": 10069, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 1005, "name": "waxed_oxidized_copper_bulb", "displayName": "Waxed Oxidized Copper Bulb", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 11437, "minStateId": 11437, "maxStateId": 11440, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"name": "hard_orange_stained_glass", "id": 11298, "minStateId": 11298, "maxStateId": 11298, "displayName": "Hard Orange Stained Glass", "defaultState": 11298, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "vault", "id": 11258, "minStateId": 11258, "maxStateId": 11273, "displayName": "<PERSON><PERSON>", "defaultState": 11258, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "hard_black_stained_glass", "id": 10708, "minStateId": 10708, "maxStateId": 10708, "displayName": "Hard Black Stained Glass", "defaultState": 10708, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"id": 1010, "name": "cave_vines_body_with_berries", "displayName": "Cave Vines Plant", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 10634, "minStateId": 10634, "maxStateId": 10659, "drops": [], "boundingBox": "empty"}, {"id": 1017, "name": "big_dripleaf", "displayName": "Big Dripleaf", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 10713, "minStateId": 10713, "maxStateId": 10744, "drops": [248], "boundingBox": "block"}, {"id": 8044, "name": "cobbled_deepslate_double_slab", "displayName": "Cobbled Deepslate Slab", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 10101, "minStateId": 10101, "maxStateId": 10102, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [651], "boundingBox": "block"}, {"id": 1054, "name": "reinforced_deepslate", "displayName": "Reinforced Deepslate", "hardness": 55, "resistance": 1200, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 10046, "minStateId": 10046, "maxStateId": 10046, "drops": [], "boundingBox": "block"}, {"name": "deny", "id": 9581, "minStateId": 9581, "maxStateId": 9581, "displayName": "<PERSON><PERSON>", "defaultState": 9581, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_118", "id": 9396, "minStateId": 9396, "maxStateId": 9396, "displayName": "Element 118", "defaultState": 9396, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_114", "id": 9395, "minStateId": 9395, "maxStateId": 9395, "displayName": "Element 114", "defaultState": 9395, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_115", "id": 9394, "minStateId": 9394, "maxStateId": 9394, "displayName": "Element 115", "defaultState": 9394, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_116", "id": 9393, "minStateId": 9393, "maxStateId": 9393, "displayName": "Element 116", "defaultState": 9393, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_117", "id": 9392, "minStateId": 9392, "maxStateId": 9392, "displayName": "Element 117", "defaultState": 9392, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_110", "id": 9391, "minStateId": 9391, "maxStateId": 9391, "displayName": "Element 110", "defaultState": 9391, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_111", "id": 9390, "minStateId": 9390, "maxStateId": 9390, "displayName": "Element 111", "defaultState": 9390, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_112", "id": 9389, "minStateId": 9389, "maxStateId": 9389, "displayName": "Element 112", "defaultState": 9389, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_113", "id": 9388, "minStateId": 9388, "maxStateId": 9388, "displayName": "Element 113", "defaultState": 9388, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_109", "id": 9387, "minStateId": 9387, "maxStateId": 9387, "displayName": "Element 109", "defaultState": 9387, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_108", "id": 9386, "minStateId": 9386, "maxStateId": 9386, "displayName": "Element 108", "defaultState": 9386, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_107", "id": 9385, "minStateId": 9385, "maxStateId": 9385, "displayName": "Element 107", "defaultState": 9385, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_106", "id": 9384, "minStateId": 9384, "maxStateId": 9384, "displayName": "Element 106", "defaultState": 9384, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_105", "id": 9383, "minStateId": 9383, "maxStateId": 9383, "displayName": "Element 105", "defaultState": 9383, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_104", "id": 9382, "minStateId": 9382, "maxStateId": 9382, "displayName": "Element 104", "defaultState": 9382, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_103", "id": 9381, "minStateId": 9381, "maxStateId": 9381, "displayName": "Element 103", "defaultState": 9381, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_102", "id": 9380, "minStateId": 9380, "maxStateId": 9380, "displayName": "Element 102", "defaultState": 9380, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_101", "id": 9379, "minStateId": 9379, "maxStateId": 9379, "displayName": "Element 101", "defaultState": 9379, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "element_100", "id": 9378, "minStateId": 9378, "maxStateId": 9378, "displayName": "Element 100", "defaultState": 9378, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"id": 1013, "name": "flowering_azalea", "displayName": "Flowering Azalea", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 9300, "minStateId": 9300, "maxStateId": 9300, "drops": [197], "boundingBox": "block"}, {"id": 1014, "name": "moss_carpet", "displayName": "Moss Carpet", "hardness": 0.1, "resistance": 0.1, "stackSize": 64, "diggable": true, "material": "plant", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 747, "minStateId": 747, "maxStateId": 747, "drops": [244], "boundingBox": "block"}, {"id": 1021, "name": "dirt_with_roots", "displayName": "Rooted Dirt", "hardness": 0.5, "resistance": 0.5, "stackSize": 64, "diggable": true, "material": "mineable/shovel", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9187, "minStateId": 9187, "maxStateId": 9187, "drops": [31], "boundingBox": "block"}, {"id": 1023, "name": "deepslate", "displayName": "Deepslate", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 684, "minStateId": 684, "maxStateId": 686, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [9], "boundingBox": "block"}, {"id": 1029, "name": "polished_deepslate_stairs", "displayName": "Polished Deepslate Stairs", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 1043, "minStateId": 1043, "maxStateId": 1050, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [635], "boundingBox": "block"}, {"id": 1030, "name": "polished_deepslate_slab", "displayName": "Polished Deepslate Slab", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 749, "minStateId": 749, "maxStateId": 750, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [652], "boundingBox": "block"}, {"id": 8045, "name": "polished_deepslate_double_slab", "displayName": "Polished Deepslate Slab", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 1362, "minStateId": 1362, "maxStateId": 1363, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [652], "boundingBox": "block"}, {"id": 1034, "name": "deepslate_tile_double_slab", "displayName": "Deepslate Tile Slab", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 1115, "minStateId": 1115, "maxStateId": 1116, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [654], "boundingBox": "block"}, {"id": 1036, "name": "deepslate_bricks", "displayName": "Deepslate Bricks", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9287, "minStateId": 9287, "maxStateId": 9287, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [345], "boundingBox": "block"}, {"name": "hard_light_blue_stained_glass_pane", "id": 9170, "minStateId": 9170, "maxStateId": 9170, "displayName": "Hard Light Blue Stained Glass Pane", "defaultState": 9170, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"id": 1035, "name": "deepslate_tile_wall", "displayName": "Deepslate Tile Wall", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 8861, "minStateId": 8861, "maxStateId": 9022, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [417], "boundingBox": "block"}, {"id": 1038, "name": "deepslate_brick_slab", "displayName": "Deepslate Brick Slab", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 6107, "minStateId": 6107, "maxStateId": 6108, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [653], "boundingBox": "block"}, {"id": 1040, "name": "chiseled_deepslate", "displayName": "Chiseled Deepslate", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9024, "minStateId": 9024, "maxStateId": 9024, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [349], "boundingBox": "block"}, {"id": 1041, "name": "cracked_deepslate_bricks", "displayName": "Cracked Deepslate Bricks", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9151, "minStateId": 9151, "maxStateId": 9151, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [346], "boundingBox": "block"}, {"id": 1046, "name": "raw_copper_block", "displayName": "Block of Raw Copper", "hardness": 5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 9077, "minStateId": 9077, "maxStateId": 9077, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [83], "boundingBox": "block"}, {"name": "border_block", "id": 8673, "minStateId": 8673, "maxStateId": 8834, "displayName": "Border Block", "defaultState": 8673, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"id": 8046, "name": "cave_vines", "displayName": "Cave Vines Plant", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 8147, "minStateId": 8147, "maxStateId": 8172, "drops": [], "boundingBox": "empty"}, {"name": "hard_glass", "id": 8122, "minStateId": 8122, "maxStateId": 8122, "displayName": "Hard Glass", "defaultState": 8122, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"id": 1015, "name": "pink_petals", "displayName": "Pink Petals", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7541, "minStateId": 7541, "maxStateId": 7572, "drops": [245], "boundingBox": "empty"}, {"id": 1033, "name": "deepslate_tile_stairs", "displayName": "Deepslate Tile Stairs", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 7969, "minStateId": 7969, "maxStateId": 7976, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [637], "boundingBox": "block"}, {"id": 1043, "name": "infested_deepslate", "displayName": "Infested Deepslate", "hardness": 1.5, "resistance": 0.75, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7957, "minStateId": 7957, "maxStateId": 7959, "drops": [], "boundingBox": "block"}, {"name": "unknown", "id": 7500, "minStateId": 7500, "maxStateId": 7500, "displayName": "Unknown", "defaultState": 7500, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "reserved6", "id": 7491, "minStateId": 7491, "maxStateId": 7491, "displayName": "Reserved6", "defaultState": 7491, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "hard_yellow_stained_glass", "id": 7473, "minStateId": 7473, "maxStateId": 7473, "displayName": "Hard Yellow Stained Glass", "defaultState": 7473, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "hard_light_gray_stained_glass_pane", "id": 7450, "minStateId": 7450, "maxStateId": 7450, "displayName": "Hard Light Gray Stained Glass Pane", "defaultState": 7450, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"id": 1032, "name": "deepslate_tiles", "displayName": "Deepslate Tiles", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 7448, "minStateId": 7448, "maxStateId": 7448, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [347], "boundingBox": "block"}, {"name": "hard_lime_stained_glass_pane", "id": 7443, "minStateId": 7443, "maxStateId": 7443, "displayName": "Hard Lime Stained Glass Pane", "defaultState": 7443, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"id": 1053, "name": "frog_spawn", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7254, "minStateId": 7254, "maxStateId": 7254, "drops": [], "boundingBox": "empty"}, {"name": "hard_purple_stained_glass", "id": 7206, "minStateId": 7206, "maxStateId": 7206, "displayName": "Hard Purple Stained Glass", "defaultState": 7206, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "hard_blue_stained_glass", "id": 7205, "minStateId": 7205, "maxStateId": 7205, "displayName": "Hard Blue Stained Glass", "defaultState": 7205, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "hard_cyan_stained_glass", "id": 7202, "minStateId": 7202, "maxStateId": 7202, "displayName": "<PERSON> <PERSON><PERSON> Stained Glass", "defaultState": 7202, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"id": 1019, "name": "small_dripleaf_block", "displayName": "Small Dripleaf", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 7158, "minStateId": 7158, "maxStateId": 7165, "drops": [], "boundingBox": "empty"}, {"id": 8047, "name": "deepslate_tile_slab", "displayName": "Deepslate Tile Slab", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 7102, "minStateId": 7102, "maxStateId": 7103, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [654], "boundingBox": "block"}, {"name": "client_request_placeholder_block", "id": 7094, "minStateId": 7094, "maxStateId": 7094, "displayName": "Client Request Placeholder Block", "defaultState": 7094, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "frame", "id": 7049, "minStateId": 7049, "maxStateId": 7072, "displayName": "<PERSON>ame", "defaultState": 7049, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "hard_yellow_stained_glass_pane", "id": 6995, "minStateId": 6995, "maxStateId": 6995, "displayName": "Hard Yellow Stained Glass Pane", "defaultState": 6995, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"id": 1003, "name": "waxed_exposed_copper_bulb", "displayName": "Waxed Exposed Copper Bulb", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6989, "minStateId": 6989, "maxStateId": 6992, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 1042, "name": "cracked_deepslate_tiles", "displayName": "Cracked Deepslate Tiles", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 6954, "minStateId": 6954, "maxStateId": 6954, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [348], "boundingBox": "block"}, {"name": "hard_magenta_stained_glass", "id": 6917, "minStateId": 6917, "maxStateId": 6917, "displayName": "Hard Magenta Stained Glass", "defaultState": 6917, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "hard_white_stained_glass", "id": 6694, "minStateId": 6694, "maxStateId": 6694, "displayName": "Hard White Stained Glass", "defaultState": 6694, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "glowingobsidian", "id": 5906, "minStateId": 5906, "maxStateId": 5906, "displayName": "Glowingobsidian", "defaultState": 5906, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"id": 1039, "name": "deepslate_brick_wall", "displayName": "Deepslate Brick Wall", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 1196, "minStateId": 1196, "maxStateId": 1357, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [416], "boundingBox": "block"}, {"id": 1047, "name": "raw_gold_block", "displayName": "Block of Raw Gold", "hardness": 5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1117, "minStateId": 1117, "maxStateId": 1117, "harvestTools": {"831": true, "836": true, "841": true}, "drops": [84], "boundingBox": "block"}, {"id": 1049, "name": "flower_pot", "displayName": "Potted Flowering Azalea", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "default", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 785, "minStateId": 785, "maxStateId": 786, "drops": [1089, 197], "boundingBox": "block"}, {"id": 1050, "name": "ochre_froglight", "displayName": "Ochre Froglight", "hardness": 0.3, "resistance": 0.3, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 15, "filterLight": 15, "defaultState": 5324, "minStateId": 5324, "maxStateId": 5326, "drops": [1251], "boundingBox": "block"}, {"name": "underwater_torch", "id": 4925, "minStateId": 4925, "maxStateId": 4930, "displayName": "Underwater Torch", "defaultState": 4925, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"id": 1006, "name": "lightning_rod", "displayName": "Lightning Rod", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 2646, "minStateId": 2646, "maxStateId": 2651, "harvestTools": {"821": true, "831": true, "836": true, "841": true}, "drops": [672], "boundingBox": "block"}, {"id": 8048, "name": "deepslate_brick_double_slab", "displayName": "Deepslate Brick Slab", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 4931, "minStateId": 4931, "maxStateId": 4932, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [653], "boundingBox": "block"}, {"name": "hard_light_blue_stained_glass", "id": 2619, "minStateId": 2619, "maxStateId": 2619, "displayName": "Hard Light Blue Stained Glass", "defaultState": 2619, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "hard_brown_stained_glass_pane", "id": 2616, "minStateId": 2616, "maxStateId": 2616, "displayName": "Hard Brown Stained Glass Pane", "defaultState": 2616, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"id": 1001, "name": "oxidized_copper_bulb", "displayName": "Oxidized Copper Bulb", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 2435, "minStateId": 2435, "maxStateId": 2438, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 1044, "name": "smooth_basalt", "displayName": "Smooth Basalt", "hardness": 1.25, "resistance": 4.2, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 2617, "minStateId": 2617, "maxStateId": 2617, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [329], "boundingBox": "block"}, {"name": "invisible_bedrock", "id": 2433, "minStateId": 2433, "maxStateId": 2433, "displayName": "Invisible Bedrock", "defaultState": 2433, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "stonecutter", "id": 2402, "minStateId": 2402, "maxStateId": 2402, "displayName": "<PERSON><PERSON><PERSON>", "defaultState": 2402, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "hard_glass_pane", "id": 1795, "minStateId": 1795, "maxStateId": 1795, "displayName": "Hard Glass Pane", "defaultState": 1795, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"id": 1002, "name": "waxed_copper_bulb", "displayName": "Waxed Copper Bulb", "hardness": 3, "resistance": 6, "stackSize": 64, "diggable": true, "material": "default", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 1376, "minStateId": 1376, "maxStateId": 1379, "harvestTools": {}, "drops": [], "boundingBox": "block"}, {"id": 1008, "name": "dripstone_block", "displayName": "Dripstone Block", "hardness": 1.5, "resistance": 1, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 15, "defaultState": 2360, "minStateId": 2360, "maxStateId": 2360, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [26], "boundingBox": "block"}, {"name": "hard_gray_stained_glass", "id": 8049, "minStateId": 736, "maxStateId": 736, "displayName": "Hard Gray Stained Glass", "defaultState": 736, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "info_update", "id": 8050, "minStateId": 676, "maxStateId": 676, "displayName": "Info Update", "defaultState": 676, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "hard_black_stained_glass_pane", "id": 8051, "minStateId": 652, "maxStateId": 652, "displayName": "Hard Black Stained Glass Pane", "defaultState": 652, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "hard_brown_stained_glass", "id": 8052, "minStateId": 645, "maxStateId": 645, "displayName": "<PERSON> Brown Stained Glass", "defaultState": 645, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "hard_orange_stained_glass_pane", "id": 8053, "minStateId": 641, "maxStateId": 641, "displayName": "Hard Orange Stained Glass Pane", "defaultState": 641, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"id": 1020, "name": "hanging_roots", "displayName": "Hanging Roots", "hardness": 0, "resistance": 0, "stackSize": 64, "diggable": true, "material": "plant;mineable/axe", "transparent": true, "emitLight": 0, "filterLight": 0, "defaultState": 627, "minStateId": 627, "maxStateId": 627, "drops": [], "boundingBox": "empty"}, {"name": "glow_frame", "id": 8054, "minStateId": 601, "maxStateId": 624, "displayName": "Glow Frame", "defaultState": 601, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"id": 1025, "name": "cobbled_deepslate_stairs", "displayName": "Cobbled Deepslate Stairs", "hardness": 3.5, "resistance": 6, "stackSize": 64, "diggable": true, "material": "mineable/pickaxe", "transparent": false, "emitLight": 0, "filterLight": 0, "defaultState": 568, "minStateId": 568, "maxStateId": 575, "harvestTools": {"816": true, "821": true, "826": true, "831": true, "836": true, "841": true}, "drops": [634], "boundingBox": "block"}, {"name": "hard_green_stained_glass_pane", "id": 151, "minStateId": 151, "maxStateId": 151, "displayName": "Hard Green Stained Glass Pane", "defaultState": 151, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}, {"name": "hard_pink_stained_glass", "id": 8055, "minStateId": 1, "maxStateId": 1, "displayName": "Hard Pink Stained Glass", "defaultState": 1, "hardness": 0, "stackSize": 1, "diggable": false, "boundingBox": "block", "drops": [], "transparent": false, "emitLight": 0, "filterLight": 0}]