{"version": 3, "file": "ClientConfiguration.d.ts", "sourceRoot": "", "sources": ["../../src/config/ClientConfiguration.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,cAAc,EAAE,MAAM,8BAA8B,CAAC;AAC9D,OAAO,EAAiC,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC9E,OAAO,EAAE,eAAe,EAAU,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AAMxE,OAAO,EAAE,SAAS,EAAE,MAAM,2BAA2B,CAAC;AACtD,OAAO,EAAE,kBAAkB,EAAE,MAAM,kCAAkC,CAAC;AACtE,OAAO,EAAE,YAAY,EAAuB,MAAM,0BAA0B,CAAC;AAC7E,OAAO,EAAE,sBAAsB,EAAE,MAAM,+CAA+C,CAAC;AACvF,OAAO,EAAE,YAAY,EAAE,MAAM,oCAAoC,CAAC;AAClE,OAAO,EAAE,uBAAuB,EAAE,MAAM,+CAA+C,CAAC;AACxF,OAAO,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AAOpE;;;;;;;;;;;;;;GAcG;AACH,MAAM,MAAM,mBAAmB,GAAG;IAC9B,WAAW,EAAE,WAAW,CAAC;IACzB,aAAa,CAAC,EAAE,aAAa,CAAC;IAC9B,aAAa,CAAC,EAAE,aAAa,CAAC;IAC9B,YAAY,CAAC,EAAE,YAAY,CAAC;IAC5B,gBAAgB,CAAC,EAAE,YAAY,CAAC;IAChC,gBAAgB,CAAC,EAAE,cAAc,CAAC;IAClC,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B,iBAAiB,CAAC,EAAE,iBAAiB,CAAC;IACtC,WAAW,CAAC,EAAE,WAAW,CAAC;IAC1B,SAAS,CAAC,EAAE,gBAAgB,CAAC;IAC7B,sBAAsB,CAAC,EAAE,sBAAsB,GAAG,IAAI,CAAC;IACvD,iBAAiB,CAAC,EAAE,YAAY,GAAG,IAAI,CAAC;IACxC,iBAAiB,CAAC,EAAE,uBAAuB,GAAG,IAAI,CAAC;CACtD,CAAC;AAEF,MAAM,MAAM,yBAAyB,GAAG;IACpC,WAAW,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC;IACnC,aAAa,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;IACvC,aAAa,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;IACvC,YAAY,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAC;IACrC,gBAAgB,EAAE,YAAY,CAAC;IAC/B,gBAAgB,EAAE,cAAc,CAAC;IACjC,eAAe,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;IACnC,WAAW,EAAE,WAAW,CAAC;IACzB,SAAS,EAAE,QAAQ,CAAC,gBAAgB,CAAC,CAAC;IACtC,sBAAsB,EAAE,sBAAsB,GAAG,IAAI,CAAC;IACtD,iBAAiB,EAAE,iBAAiB,CAAC;IACrC,iBAAiB,EAAE,YAAY,GAAG,IAAI,CAAC;IACvC,iBAAiB,EAAE,uBAAuB,GAAG,IAAI,CAAC;CACrD,CAAC;AAEF;;;;;;;;;;;;;GAaG;AACH,MAAM,MAAM,WAAW,GAAG;IACtB,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,SAAS,CAAC;IACrB,WAAW,EAAE,MAAM,CAAC;IACpB,kBAAkB,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IACnC,iBAAiB,CAAC,EAAE,iBAAiB,CAAC;IACtC,0BAA0B,CAAC,EAAE,OAAO,CAAC;IACrC,aAAa,CAAC,EAAE,OAAO,CAAC;CAC3B,CAAC;AAEF;;;;GAIG;AACH,MAAM,MAAM,aAAa,GAAG;IACxB,yBAAyB,CAAC,EAAE,MAAM,CAAC;IACnC,oBAAoB,CAAC,EAAE,OAAO,CAAC;CAClC,CAAC;AAEF;;;;;;;GAOG;AACH,MAAM,MAAM,aAAa,GAAG;IACxB,cAAc,CAAC,EAAE,eAAe,CAAC;IACjC,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAC5B,QAAQ,CAAC,EAAE,QAAQ,CAAC;IACpB,aAAa,CAAC,EAAE,MAAM,CAAC;CAC1B,CAAC;AAEF;;;;GAIG;AACH,MAAM,MAAM,YAAY,GAAG;IACvB,yBAAyB,CAAC,EAAE,OAAO,CAAC;CACvC,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,WAAW,GAAG;IACtB,GAAG,EAAE,MAAM,CAAC;IACZ,OAAO,EAAE,MAAM,CAAC;IAChB,GAAG,EAAE,MAAM,CAAC;IACZ,EAAE,EAAE,MAAM,CAAC;CACd,CAAC;AAEF;;;;;GAKG;AACH,MAAM,MAAM,iBAAiB,GAAG;IAC5B,kBAAkB,EAAE,kBAAkB,CAAC;IACvC,MAAM,CAAC,EAAE,MAAM,CAAC;CACnB,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG;IAC3B,WAAW,EAAE,oBAAoB,CAAC;CACrC,CAAC;AAEF;;;;GAIG;AACH,MAAM,MAAM,oBAAoB,GAAG;IAC/B,OAAO,EAAE,MAAM,CAAC;IAChB,UAAU,EAAE,MAAM,CAAC;CACtB,CAAC;AAEF,eAAO,MAAM,sBAAsB,EAAE,QAAQ,CAAC,aAAa,CAG1D,CAAC;AAgDF;;;;;;GAMG;AACH,wBAAgB,wBAAwB,CAAC,EACrC,WAAW,EAAE,eAAe,EAC5B,aAAa,EAAE,iBAAiB,EAChC,aAAa,EAAE,gBAAgB,EAC/B,YAAY,EAAE,gBAAgB,EAC9B,gBAAgB,EAAE,qBAAqB,EACvC,gBAAgB,EAAE,qBAAqB,EACvC,eAAe,EAAE,oBAAoB,EACrC,iBAAiB,EAAE,iBAAiB,EACpC,WAAW,EAAE,WAAW,EACxB,SAAS,EAAE,SAAS,EACpB,sBAAsB,EAAE,sBAAsB,EAC9C,iBAAiB,EAAE,iBAAiB,EACpC,iBAAiB,EAAE,iBAAiB,GACvC,EAAE,mBAAmB,GAAG,yBAAyB,CA4BjD;AAgBD;;;GAGG;AACH,wBAAgB,kBAAkB,CAAC,MAAM,EAAE,mBAAmB,GAAG,OAAO,CAIvE"}