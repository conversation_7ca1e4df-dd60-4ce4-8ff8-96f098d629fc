[{"id": 0, "displayName": "Air", "name": "air", "hardness": 0, "minStateId": 0, "maxStateId": 0, "states": [], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 0, "defaultState": 0, "resistance": 0}, {"id": 1, "displayName": "Stone", "name": "stone", "hardness": 1.5, "minStateId": 1, "maxStateId": 1, "states": [], "drops": [1, 12], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 1, "resistance": 6}, {"id": 2, "displayName": "Granite", "name": "granite", "hardness": 1.5, "minStateId": 2, "maxStateId": 2, "states": [], "drops": [2], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 2, "resistance": 6}, {"id": 3, "displayName": "Polished Granite", "name": "polished_granite", "hardness": 1.5, "minStateId": 3, "maxStateId": 3, "states": [], "drops": [3], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 3, "resistance": 6}, {"id": 4, "displayName": "Diorite", "name": "diorite", "hardness": 1.5, "minStateId": 4, "maxStateId": 4, "states": [], "drops": [4], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 4, "resistance": 6}, {"id": 5, "displayName": "Polished Diorite", "name": "polished_diorite", "hardness": 1.5, "minStateId": 5, "maxStateId": 5, "states": [], "drops": [5], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 5, "resistance": 6}, {"id": 6, "displayName": "Andesite", "name": "andesite", "hardness": 1.5, "minStateId": 6, "maxStateId": 6, "states": [], "drops": [6], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6, "resistance": 6}, {"id": 7, "displayName": "Polished Andesite", "name": "polished_andesite", "hardness": 1.5, "minStateId": 7, "maxStateId": 7, "states": [], "drops": [7], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7, "resistance": 6}, {"id": 8, "displayName": "Grass Block", "name": "grass_block", "hardness": 0.6, "minStateId": 8, "maxStateId": 9, "states": [{"name": "snowy", "type": "bool", "num_values": 2}], "drops": [8, 9], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 9, "resistance": 0.6}, {"id": 9, "displayName": "Dirt", "name": "dirt", "hardness": 0.5, "minStateId": 10, "maxStateId": 10, "states": [], "drops": [9], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 10, "resistance": 0.5}, {"id": 10, "displayName": "Coarse Dirt", "name": "coarse_dirt", "hardness": 0.5, "minStateId": 11, "maxStateId": 11, "states": [], "drops": [10], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 11, "resistance": 0.5}, {"id": 11, "displayName": "Podzol", "name": "podzol", "hardness": 0.5, "minStateId": 12, "maxStateId": 13, "states": [{"name": "snowy", "type": "bool", "num_values": 2}], "drops": [11, 9], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 13, "resistance": 0.5}, {"id": 12, "displayName": "Cobblestone", "name": "cobblestone", "hardness": 2, "minStateId": 14, "maxStateId": 14, "states": [], "drops": [12], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 14, "resistance": 6}, {"id": 13, "displayName": "Oak Planks", "name": "oak_planks", "hardness": 2, "minStateId": 15, "maxStateId": 15, "states": [], "drops": [13], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 15, "resistance": 3}, {"id": 14, "displayName": "Spruce Planks", "name": "spruce_planks", "hardness": 2, "minStateId": 16, "maxStateId": 16, "states": [], "drops": [14], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 16, "resistance": 3}, {"id": 15, "displayName": "Birch Planks", "name": "birch_planks", "hardness": 2, "minStateId": 17, "maxStateId": 17, "states": [], "drops": [15], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 17, "resistance": 3}, {"id": 16, "displayName": "Jungle Planks", "name": "jungle_planks", "hardness": 2, "minStateId": 18, "maxStateId": 18, "states": [], "drops": [16], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 18, "resistance": 3}, {"id": 17, "displayName": "Acacia Planks", "name": "acacia_planks", "hardness": 2, "minStateId": 19, "maxStateId": 19, "states": [], "drops": [17], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 19, "resistance": 3}, {"id": 18, "displayName": "Dark Oak Planks", "name": "dark_oak_planks", "hardness": 2, "minStateId": 20, "maxStateId": 20, "states": [], "drops": [18], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 20, "resistance": 3}, {"id": 19, "displayName": "Oak Sapling", "name": "oak_sapling", "hardness": 0, "minStateId": 21, "maxStateId": 22, "states": [{"name": "stage", "type": "int", "num_values": 2}], "drops": [19], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 21, "resistance": 0}, {"id": 20, "displayName": "Spruce Sapling", "name": "spruce_sapling", "hardness": 0, "minStateId": 23, "maxStateId": 24, "states": [{"name": "stage", "type": "int", "num_values": 2}], "drops": [20], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 23, "resistance": 0}, {"id": 21, "displayName": "Birch Sapling", "name": "birch_sapling", "hardness": 0, "minStateId": 25, "maxStateId": 26, "states": [{"name": "stage", "type": "int", "num_values": 2}], "drops": [21], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 25, "resistance": 0}, {"id": 22, "displayName": "Jungle Sapling", "name": "jungle_sapling", "hardness": 0, "minStateId": 27, "maxStateId": 28, "states": [{"name": "stage", "type": "int", "num_values": 2}], "drops": [22], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 27, "resistance": 0}, {"id": 23, "displayName": "Acacia Sapling", "name": "acacia_sapling", "hardness": 0, "minStateId": 29, "maxStateId": 30, "states": [{"name": "stage", "type": "int", "num_values": 2}], "drops": [23], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 29, "resistance": 0}, {"id": 24, "displayName": "Dark Oak Sapling", "name": "dark_oak_sapling", "hardness": 0, "minStateId": 31, "maxStateId": 32, "states": [{"name": "stage", "type": "int", "num_values": 2}], "drops": [24], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 31, "resistance": 0}, {"id": 25, "displayName": "Bedrock", "name": "bedrock", "hardness": null, "minStateId": 33, "maxStateId": 33, "states": [], "drops": [], "diggable": false, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 33, "resistance": 3600000}, {"id": 26, "displayName": "Water", "name": "water", "hardness": 100, "minStateId": 34, "maxStateId": 49, "states": [{"name": "level", "type": "int", "num_values": 16}], "drops": [], "diggable": false, "transparent": true, "filterLight": 2, "emitLight": 0, "boundingBox": "empty", "stackSize": 0, "defaultState": 34, "resistance": 100}, {"id": 27, "displayName": "<PERSON><PERSON>", "name": "lava", "hardness": 100, "minStateId": 50, "maxStateId": 65, "states": [{"name": "level", "type": "int", "num_values": 16}], "drops": [], "diggable": false, "transparent": true, "filterLight": 0, "emitLight": 15, "boundingBox": "empty", "stackSize": 0, "defaultState": 50, "resistance": 100}, {"id": 28, "displayName": "Sand", "name": "sand", "hardness": 0.5, "minStateId": 66, "maxStateId": 66, "states": [], "drops": [26], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 0, "material": "dirt", "defaultState": 66, "resistance": 0.5}, {"id": 29, "displayName": "Red Sand", "name": "red_sand", "hardness": 0.5, "minStateId": 67, "maxStateId": 67, "states": [], "drops": [27], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 0, "material": "dirt", "defaultState": 67, "resistance": 0.5}, {"id": 30, "displayName": "<PERSON>l", "name": "gravel", "hardness": 0.6, "minStateId": 68, "maxStateId": 68, "states": [], "drops": [28, 583], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 68, "resistance": 0.6}, {"id": 31, "displayName": "Gold Ore", "name": "gold_ore", "hardness": 3, "minStateId": 69, "maxStateId": 69, "states": [], "drops": [29], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "543": true}, "defaultState": 69, "resistance": 3}, {"id": 32, "displayName": "Iron Ore", "name": "iron_ore", "hardness": 3, "minStateId": 70, "maxStateId": 70, "states": [], "drops": [30], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "539": true, "543": true}, "defaultState": 70, "resistance": 3}, {"id": 33, "displayName": "Coal Ore", "name": "coal_ore", "hardness": 3, "minStateId": 71, "maxStateId": 71, "states": [], "drops": [31, 527], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 71, "resistance": 3}, {"id": 34, "displayName": "Oak Log", "name": "oak_log", "hardness": 2, "minStateId": 72, "maxStateId": 74, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [32], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 73, "resistance": 2}, {"id": 35, "displayName": "Spruce Log", "name": "spruce_log", "hardness": 2, "minStateId": 75, "maxStateId": 77, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [33], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 76, "resistance": 2}, {"id": 36, "displayName": "Birch Log", "name": "birch_log", "hardness": 2, "minStateId": 78, "maxStateId": 80, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [34], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 79, "resistance": 2}, {"id": 37, "displayName": "Jungle Log", "name": "jungle_log", "hardness": 2, "minStateId": 81, "maxStateId": 83, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [35], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 82, "resistance": 2}, {"id": 38, "displayName": "Acacia Log", "name": "acacia_log", "hardness": 2, "minStateId": 84, "maxStateId": 86, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [36], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 85, "resistance": 2}, {"id": 39, "displayName": "Dark Oak Log", "name": "dark_oak_log", "hardness": 2, "minStateId": 87, "maxStateId": 89, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [37], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 88, "resistance": 2}, {"id": 40, "displayName": "Stripped Spruce Log", "name": "stripped_spruce_log", "hardness": 2, "minStateId": 90, "maxStateId": 92, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [39], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 91, "resistance": 2}, {"id": 41, "displayName": "Stripped Birch Log", "name": "stripped_birch_log", "hardness": 2, "minStateId": 93, "maxStateId": 95, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [40], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 94, "resistance": 2}, {"id": 42, "displayName": "Stripped Jungle Log", "name": "stripped_jungle_log", "hardness": 2, "minStateId": 96, "maxStateId": 98, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [41], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 97, "resistance": 2}, {"id": 43, "displayName": "Stripped Acacia Log", "name": "stripped_acacia_log", "hardness": 2, "minStateId": 99, "maxStateId": 101, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [42], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 100, "resistance": 2}, {"id": 44, "displayName": "Stripped Dark Oak Log", "name": "stripped_dark_oak_log", "hardness": 2, "minStateId": 102, "maxStateId": 104, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [43], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 103, "resistance": 2}, {"id": 45, "displayName": "Stripped Oak Log", "name": "stripped_oak_log", "hardness": 2, "minStateId": 105, "maxStateId": 107, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [38], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 106, "resistance": 2}, {"id": 46, "displayName": "Oak Wood", "name": "oak_wood", "hardness": 2, "minStateId": 108, "maxStateId": 110, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [50], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 109, "resistance": 2}, {"id": 47, "displayName": "Spruce Wood", "name": "spruce_wood", "hardness": 2, "minStateId": 111, "maxStateId": 113, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [51], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 112, "resistance": 2}, {"id": 48, "displayName": "Birch Wood", "name": "birch_wood", "hardness": 2, "minStateId": 114, "maxStateId": 116, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [52], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 115, "resistance": 2}, {"id": 49, "displayName": "Jungle Wood", "name": "jungle_wood", "hardness": 2, "minStateId": 117, "maxStateId": 119, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [53], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 118, "resistance": 2}, {"id": 50, "displayName": "Acacia Wood", "name": "acacia_wood", "hardness": 2, "minStateId": 120, "maxStateId": 122, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [54], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 121, "resistance": 2}, {"id": 51, "displayName": "Dark Oak Wood", "name": "dark_oak_wood", "hardness": 2, "minStateId": 123, "maxStateId": 125, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [55], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 124, "resistance": 2}, {"id": 52, "displayName": "Stripped Oak Wood", "name": "stripped_oak_wood", "hardness": 2, "minStateId": 126, "maxStateId": 128, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [44], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 127, "resistance": 2}, {"id": 53, "displayName": "Stripped Spruce Wood", "name": "stripped_spruce_wood", "hardness": 2, "minStateId": 129, "maxStateId": 131, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [45], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 130, "resistance": 2}, {"id": 54, "displayName": "Stripped Birch Wood", "name": "stripped_birch_wood", "hardness": 2, "minStateId": 132, "maxStateId": 134, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [46], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 133, "resistance": 2}, {"id": 55, "displayName": "Stripped Jungle Wood", "name": "stripped_jungle_wood", "hardness": 2, "minStateId": 135, "maxStateId": 137, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [47], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 136, "resistance": 2}, {"id": 56, "displayName": "Stripped Acacia Wood", "name": "stripped_acacia_wood", "hardness": 2, "minStateId": 138, "maxStateId": 140, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [48], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 139, "resistance": 2}, {"id": 57, "displayName": "Stripped Dark Oak Wood", "name": "stripped_dark_oak_wood", "hardness": 2, "minStateId": 141, "maxStateId": 143, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [49], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 142, "resistance": 2}, {"id": 58, "displayName": "Oak Leaves", "name": "oak_leaves", "hardness": 0.2, "minStateId": 144, "maxStateId": 157, "states": [{"name": "distance", "type": "enum", "num_values": 7, "values": ["1", "2", "3", "4", "5", "6", "7"]}, {"name": "persistent", "type": "bool", "num_values": 2}], "drops": [672, 56, 19, 545, 524], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 157, "resistance": 0.2}, {"id": 59, "displayName": "Spruce Leaves", "name": "spruce_leaves", "hardness": 0.2, "minStateId": 158, "maxStateId": 171, "states": [{"name": "distance", "type": "enum", "num_values": 7, "values": ["1", "2", "3", "4", "5", "6", "7"]}, {"name": "persistent", "type": "bool", "num_values": 2}], "drops": [672, 57, 20, 545], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 171, "resistance": 0.2}, {"id": 60, "displayName": "Birch Leaves", "name": "birch_leaves", "hardness": 0.2, "minStateId": 172, "maxStateId": 185, "states": [{"name": "distance", "type": "enum", "num_values": 7, "values": ["1", "2", "3", "4", "5", "6", "7"]}, {"name": "persistent", "type": "bool", "num_values": 2}], "drops": [672, 58, 21, 545], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 185, "resistance": 0.2}, {"id": 61, "displayName": "Jungle Leaves", "name": "jungle_leaves", "hardness": 0.2, "minStateId": 186, "maxStateId": 199, "states": [{"name": "distance", "type": "enum", "num_values": 7, "values": ["1", "2", "3", "4", "5", "6", "7"]}, {"name": "persistent", "type": "bool", "num_values": 2}], "drops": [672, 59, 22, 545], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 199, "resistance": 0.2}, {"id": 62, "displayName": "Acacia Leaves", "name": "acacia_leaves", "hardness": 0.2, "minStateId": 200, "maxStateId": 213, "states": [{"name": "distance", "type": "enum", "num_values": 7, "values": ["1", "2", "3", "4", "5", "6", "7"]}, {"name": "persistent", "type": "bool", "num_values": 2}], "drops": [672, 60, 23, 545], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 213, "resistance": 0.2}, {"id": 63, "displayName": "Dark Oak Leaves", "name": "dark_oak_leaves", "hardness": 0.2, "minStateId": 214, "maxStateId": 227, "states": [{"name": "distance", "type": "enum", "num_values": 7, "values": ["1", "2", "3", "4", "5", "6", "7"]}, {"name": "persistent", "type": "bool", "num_values": 2}], "drops": [672, 61, 24, 545, 524], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 227, "resistance": 0.2}, {"id": 64, "displayName": "Sponge", "name": "sponge", "hardness": 0.6, "minStateId": 228, "maxStateId": 228, "states": [], "drops": [62], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 228, "resistance": 0.6}, {"id": 65, "displayName": "Wet Sponge", "name": "wet_sponge", "hardness": 0.6, "minStateId": 229, "maxStateId": 229, "states": [], "drops": [63], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 229, "resistance": 0.6}, {"id": 66, "displayName": "Glass", "name": "glass", "hardness": 0.3, "minStateId": 230, "maxStateId": 230, "states": [], "drops": [64], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 230, "resistance": 0.3}, {"id": 67, "displayName": "Lapis <PERSON> Ore", "name": "lapis_ore", "hardness": 3, "minStateId": 231, "maxStateId": 231, "states": [], "drops": [65, 635], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "539": true, "543": true}, "defaultState": 231, "resistance": 3}, {"id": 68, "displayName": "Lapis <PERSON>", "name": "lapis_block", "hardness": 3, "minStateId": 232, "maxStateId": 232, "states": [], "drops": [66], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "539": true, "543": true}, "defaultState": 232, "resistance": 3}, {"id": 69, "displayName": "Dispenser", "name": "dispenser", "hardness": 3.5, "minStateId": 233, "maxStateId": 244, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}, {"name": "triggered", "type": "bool", "num_values": 2}], "drops": [67], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 234, "resistance": 3.5}, {"id": 70, "displayName": "Sandstone", "name": "sandstone", "hardness": 0.8, "minStateId": 245, "maxStateId": 245, "states": [], "drops": [68], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 245, "resistance": 0.8}, {"id": 71, "displayName": "Chiseled Sandstone", "name": "chiseled_sandstone", "hardness": 0.8, "minStateId": 246, "maxStateId": 246, "states": [], "drops": [69], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 246, "resistance": 0.8}, {"id": 72, "displayName": "Cut Sandstone", "name": "cut_sandstone", "hardness": 0.8, "minStateId": 247, "maxStateId": 247, "states": [], "drops": [70], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 247, "resistance": 0.8}, {"id": 73, "displayName": "Note Block", "name": "note_block", "hardness": 0.8, "minStateId": 248, "maxStateId": 1047, "states": [{"name": "instrument", "type": "enum", "num_values": 16, "values": ["harp", "basedrum", "snare", "hat", "bass", "flute", "bell", "guitar", "chime", "xylophone", "iron_xylophone", "cow_bell", "didger<PERSON>o", "bit", "banjo", "pling"]}, {"name": "note", "type": "int", "num_values": 25}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [71], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 249, "resistance": 0.8}, {"id": 74, "displayName": "White Bed", "name": "white_bed", "hardness": 0.2, "minStateId": 1048, "maxStateId": 1063, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [654], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 1051, "resistance": 0.2}, {"id": 75, "displayName": "Orange Bed", "name": "orange_bed", "hardness": 0.2, "minStateId": 1064, "maxStateId": 1079, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [655], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 1067, "resistance": 0.2}, {"id": 76, "displayName": "Magenta Bed", "name": "magenta_bed", "hardness": 0.2, "minStateId": 1080, "maxStateId": 1095, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [656], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 1083, "resistance": 0.2}, {"id": 77, "displayName": "Light Blue Bed", "name": "light_blue_bed", "hardness": 0.2, "minStateId": 1096, "maxStateId": 1111, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [657], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 1099, "resistance": 0.2}, {"id": 78, "displayName": "Yellow Bed", "name": "yellow_bed", "hardness": 0.2, "minStateId": 1112, "maxStateId": 1127, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [658], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 1115, "resistance": 0.2}, {"id": 79, "displayName": "Lime Bed", "name": "lime_bed", "hardness": 0.2, "minStateId": 1128, "maxStateId": 1143, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [659], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 1131, "resistance": 0.2}, {"id": 80, "displayName": "Pink Bed", "name": "pink_bed", "hardness": 0.2, "minStateId": 1144, "maxStateId": 1159, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [660], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 1147, "resistance": 0.2}, {"id": 81, "displayName": "Gray Bed", "name": "gray_bed", "hardness": 0.2, "minStateId": 1160, "maxStateId": 1175, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [661], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 1163, "resistance": 0.2}, {"id": 82, "displayName": "Light Gray Bed", "name": "light_gray_bed", "hardness": 0.2, "minStateId": 1176, "maxStateId": 1191, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [662], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 1179, "resistance": 0.2}, {"id": 83, "displayName": "<PERSON><PERSON>", "name": "cyan_bed", "hardness": 0.2, "minStateId": 1192, "maxStateId": 1207, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [663], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 1195, "resistance": 0.2}, {"id": 84, "displayName": "Purple Bed", "name": "purple_bed", "hardness": 0.2, "minStateId": 1208, "maxStateId": 1223, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [664], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 1211, "resistance": 0.2}, {"id": 85, "displayName": "Blue Bed", "name": "blue_bed", "hardness": 0.2, "minStateId": 1224, "maxStateId": 1239, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [665], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 1227, "resistance": 0.2}, {"id": 86, "displayName": "Brown Bed", "name": "brown_bed", "hardness": 0.2, "minStateId": 1240, "maxStateId": 1255, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [666], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 1243, "resistance": 0.2}, {"id": 87, "displayName": "Green Bed", "name": "green_bed", "hardness": 0.2, "minStateId": 1256, "maxStateId": 1271, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [667], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 1259, "resistance": 0.2}, {"id": 88, "displayName": "Red Bed", "name": "red_bed", "hardness": 0.2, "minStateId": 1272, "maxStateId": 1287, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [668], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 1275, "resistance": 0.2}, {"id": 89, "displayName": "Black Bed", "name": "black_bed", "hardness": 0.2, "minStateId": 1288, "maxStateId": 1303, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "occupied", "type": "bool", "num_values": 2}, {"name": "part", "type": "enum", "num_values": 2, "values": ["head", "foot"]}], "drops": [669], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 1291, "resistance": 0.2}, {"id": 90, "displayName": "Powered Rail", "name": "powered_rail", "hardness": 0.7, "minStateId": 1304, "maxStateId": 1315, "states": [{"name": "powered", "type": "bool", "num_values": 2}, {"name": "shape", "type": "enum", "num_values": 6, "values": ["north_south", "east_west", "ascending_east", "ascending_west", "ascending_north", "ascending_south"]}], "drops": [72], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "rock", "defaultState": 1310, "resistance": 0.7}, {"id": 91, "displayName": "Detector Rail", "name": "detector_rail", "hardness": 0.7, "minStateId": 1316, "maxStateId": 1327, "states": [{"name": "powered", "type": "bool", "num_values": 2}, {"name": "shape", "type": "enum", "num_values": 6, "values": ["north_south", "east_west", "ascending_east", "ascending_west", "ascending_north", "ascending_south"]}], "drops": [73], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "rock", "defaultState": 1322, "resistance": 0.7}, {"id": 92, "displayName": "<PERSON><PERSON>", "name": "sticky_piston", "hardness": 0.5, "minStateId": 1328, "maxStateId": 1339, "states": [{"name": "extended", "type": "bool", "num_values": 2}, {"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [74], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 1334, "resistance": 0.5}, {"id": 93, "displayName": "Cobweb", "name": "cobweb", "hardness": 4, "minStateId": 1340, "maxStateId": 1340, "states": [], "drops": [672, 75, 552], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "web", "harvestTools": {"532": true, "533": true, "537": true, "541": true, "548": true, "672": true}, "defaultState": 1340, "resistance": 4}, {"id": 94, "displayName": "Grass", "name": "grass", "hardness": 0, "minStateId": 1341, "maxStateId": 1341, "states": [], "drops": [672, 76, 560], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1341, "resistance": 0}, {"id": 95, "displayName": "Fern", "name": "fern", "hardness": 0, "minStateId": 1342, "maxStateId": 1342, "states": [], "drops": [672, 77, 560], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1342, "resistance": 0}, {"id": 96, "displayName": "Dead Bush", "name": "dead_bush", "hardness": 0, "minStateId": 1343, "maxStateId": 1343, "states": [], "drops": [672, 78, 545], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1343, "resistance": 0}, {"id": 97, "displayName": "Seagrass", "name": "seagrass", "hardness": 0, "minStateId": 1344, "maxStateId": 1344, "states": [], "drops": [79, 672], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1344, "resistance": 0}, {"id": 98, "displayName": "Tall Seagrass", "name": "tall_seagrass", "hardness": 0, "minStateId": 1345, "maxStateId": 1346, "states": [{"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}], "drops": [79, 672], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1346, "resistance": 0}, {"id": 99, "displayName": "<PERSON><PERSON>", "name": "piston", "hardness": 0.5, "minStateId": 1347, "maxStateId": 1358, "states": [{"name": "extended", "type": "bool", "num_values": 2}, {"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [81], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 1353, "resistance": 0.5}, {"id": 100, "displayName": "Piston Head", "name": "piston_head", "hardness": 0.5, "minStateId": 1359, "maxStateId": 1382, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}, {"name": "short", "type": "bool", "num_values": 2}, {"name": "type", "type": "enum", "num_values": 2, "values": ["normal", "sticky"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 1361, "resistance": 0.5}, {"id": 101, "displayName": "White Wool", "name": "white_wool", "hardness": 0.8, "minStateId": 1383, "maxStateId": 1383, "states": [], "drops": [82], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1383, "resistance": 0.8}, {"id": 102, "displayName": "Orange Wool", "name": "orange_wool", "hardness": 0.8, "minStateId": 1384, "maxStateId": 1384, "states": [], "drops": [83], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1384, "resistance": 0.8}, {"id": 103, "displayName": "Magenta Wool", "name": "magenta_wool", "hardness": 0.8, "minStateId": 1385, "maxStateId": 1385, "states": [], "drops": [84], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1385, "resistance": 0.8}, {"id": 104, "displayName": "Light Blue Wool", "name": "light_blue_wool", "hardness": 0.8, "minStateId": 1386, "maxStateId": 1386, "states": [], "drops": [85], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1386, "resistance": 0.8}, {"id": 105, "displayName": "Yellow Wool", "name": "yellow_wool", "hardness": 0.8, "minStateId": 1387, "maxStateId": 1387, "states": [], "drops": [86], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1387, "resistance": 0.8}, {"id": 106, "displayName": "Lime Wool", "name": "lime_wool", "hardness": 0.8, "minStateId": 1388, "maxStateId": 1388, "states": [], "drops": [87], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1388, "resistance": 0.8}, {"id": 107, "displayName": "Pink Wool", "name": "pink_wool", "hardness": 0.8, "minStateId": 1389, "maxStateId": 1389, "states": [], "drops": [88], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1389, "resistance": 0.8}, {"id": 108, "displayName": "Gray <PERSON>", "name": "gray_wool", "hardness": 0.8, "minStateId": 1390, "maxStateId": 1390, "states": [], "drops": [89], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1390, "resistance": 0.8}, {"id": 109, "displayName": "Light Gray Wool", "name": "light_gray_wool", "hardness": 0.8, "minStateId": 1391, "maxStateId": 1391, "states": [], "drops": [90], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1391, "resistance": 0.8}, {"id": 110, "displayName": "<PERSON><PERSON>", "name": "cyan_wool", "hardness": 0.8, "minStateId": 1392, "maxStateId": 1392, "states": [], "drops": [91], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1392, "resistance": 0.8}, {"id": 111, "displayName": "Purple Wool", "name": "purple_wool", "hardness": 0.8, "minStateId": 1393, "maxStateId": 1393, "states": [], "drops": [92], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1393, "resistance": 0.8}, {"id": 112, "displayName": "Blue Wool", "name": "blue_wool", "hardness": 0.8, "minStateId": 1394, "maxStateId": 1394, "states": [], "drops": [93], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1394, "resistance": 0.8}, {"id": 113, "displayName": "Brown Wool", "name": "brown_wool", "hardness": 0.8, "minStateId": 1395, "maxStateId": 1395, "states": [], "drops": [94], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1395, "resistance": 0.8}, {"id": 114, "displayName": "Green Wool", "name": "green_wool", "hardness": 0.8, "minStateId": 1396, "maxStateId": 1396, "states": [], "drops": [95], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1396, "resistance": 0.8}, {"id": 115, "displayName": "Red Wool", "name": "red_wool", "hardness": 0.8, "minStateId": 1397, "maxStateId": 1397, "states": [], "drops": [96], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1397, "resistance": 0.8}, {"id": 116, "displayName": "Black Wool", "name": "black_wool", "hardness": 0.8, "minStateId": 1398, "maxStateId": 1398, "states": [], "drops": [97], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wool", "defaultState": 1398, "resistance": 0.8}, {"id": 117, "displayName": "Moving <PERSON>ston", "name": "moving_piston", "hardness": null, "minStateId": 1399, "maxStateId": 1410, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}, {"name": "type", "type": "enum", "num_values": 2, "values": ["normal", "sticky"]}], "drops": [], "diggable": false, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 1399, "resistance": -1}, {"id": 118, "displayName": "Dandelion", "name": "dandelion", "hardness": 0, "minStateId": 1411, "maxStateId": 1411, "states": [], "drops": [98], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1411, "resistance": 0}, {"id": 119, "displayName": "<PERSON><PERSON>", "name": "poppy", "hardness": 0, "minStateId": 1412, "maxStateId": 1412, "states": [], "drops": [99], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1412, "resistance": 0}, {"id": 120, "displayName": "Blue Orchid", "name": "blue_orchid", "hardness": 0, "minStateId": 1413, "maxStateId": 1413, "states": [], "drops": [100], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1413, "resistance": 0}, {"id": 121, "displayName": "Allium", "name": "allium", "hardness": 0, "minStateId": 1414, "maxStateId": 1414, "states": [], "drops": [101], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1414, "resistance": 0}, {"id": 122, "displayName": "Azure Bluet", "name": "azure_bluet", "hardness": 0, "minStateId": 1415, "maxStateId": 1415, "states": [], "drops": [102], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1415, "resistance": 0}, {"id": 123, "displayName": "<PERSON>lip", "name": "red_tulip", "hardness": 0, "minStateId": 1416, "maxStateId": 1416, "states": [], "drops": [103], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1416, "resistance": 0}, {"id": 124, "displayName": "Orange Tulip", "name": "orange_tulip", "hardness": 0, "minStateId": 1417, "maxStateId": 1417, "states": [], "drops": [104], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1417, "resistance": 0}, {"id": 125, "displayName": "White Tulip", "name": "white_tulip", "hardness": 0, "minStateId": 1418, "maxStateId": 1418, "states": [], "drops": [105], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1418, "resistance": 0}, {"id": 126, "displayName": "<PERSON> Tulip", "name": "pink_tulip", "hardness": 0, "minStateId": 1419, "maxStateId": 1419, "states": [], "drops": [106], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1419, "resistance": 0}, {"id": 127, "displayName": "Oxeye Daisy", "name": "oxeye_daisy", "hardness": 0, "minStateId": 1420, "maxStateId": 1420, "states": [], "drops": [107], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1420, "resistance": 0}, {"id": 128, "displayName": "Corn<PERSON>", "name": "cornflower", "hardness": 0, "minStateId": 1421, "maxStateId": 1421, "states": [], "drops": [108], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1421, "resistance": 0}, {"id": 129, "displayName": "<PERSON><PERSON>", "name": "wither_rose", "hardness": 0, "minStateId": 1422, "maxStateId": 1422, "states": [], "drops": [110], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1422, "resistance": 0}, {"id": 130, "displayName": "Lily of the Valley", "name": "lily_of_the_valley", "hardness": 0, "minStateId": 1423, "maxStateId": 1423, "states": [], "drops": [109], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1423, "resistance": 0}, {"id": 131, "displayName": "Brown Mushroom", "name": "brown_mushroom", "hardness": 0, "minStateId": 1424, "maxStateId": 1424, "states": [], "drops": [111], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 1, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1424, "resistance": 0}, {"id": 132, "displayName": "Red Mushroom", "name": "red_mushroom", "hardness": 0, "minStateId": 1425, "maxStateId": 1425, "states": [], "drops": [112], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 1, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 1425, "resistance": 0}, {"id": 133, "displayName": "Block of Gold", "name": "gold_block", "hardness": 3, "minStateId": 1426, "maxStateId": 1426, "states": [], "drops": [113], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "543": true}, "defaultState": 1426, "resistance": 6}, {"id": 134, "displayName": "Block of Iron", "name": "iron_block", "hardness": 5, "minStateId": 1427, "maxStateId": 1427, "states": [], "drops": [114], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "539": true, "543": true}, "defaultState": 1427, "resistance": 6}, {"id": 135, "displayName": "Bricks", "name": "bricks", "hardness": 2, "minStateId": 1428, "maxStateId": 1428, "states": [], "drops": [141], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 1428, "resistance": 6}, {"id": 136, "displayName": "TNT", "name": "tnt", "hardness": 0, "minStateId": 1429, "maxStateId": 1430, "states": [{"name": "unstable", "type": "bool", "num_values": 2}], "drops": [142], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 1430, "resistance": 0}, {"id": 137, "displayName": "Bookshelf", "name": "bookshelf", "hardness": 1.5, "minStateId": 1431, "maxStateId": 1431, "states": [], "drops": [143, 616], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 1431, "resistance": 1.5}, {"id": 138, "displayName": "<PERSON><PERSON>", "name": "mossy_cobblestone", "hardness": 2, "minStateId": 1432, "maxStateId": 1432, "states": [], "drops": [144], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 1432, "resistance": 6}, {"id": 139, "displayName": "Obsidian", "name": "obsidian", "hardness": 50, "minStateId": 1433, "maxStateId": 1433, "states": [], "drops": [145], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"543": true}, "defaultState": 1433, "resistance": 1200}, {"id": 140, "displayName": "<PERSON>ch", "name": "torch", "hardness": 0, "minStateId": 1434, "maxStateId": 1434, "states": [], "drops": [146], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 14, "boundingBox": "empty", "stackSize": 64, "defaultState": 1434, "resistance": 0}, {"id": 141, "displayName": "<PERSON>", "name": "wall_torch", "hardness": 0, "minStateId": 1435, "maxStateId": 1438, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 14, "boundingBox": "empty", "stackSize": 64, "defaultState": 1435, "resistance": 0}, {"id": 142, "displayName": "Fire", "name": "fire", "hardness": 0, "minStateId": 1439, "maxStateId": 1950, "states": [{"name": "age", "type": "int", "num_values": 16}, {"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 15, "boundingBox": "empty", "stackSize": 0, "defaultState": 1470, "resistance": 0}, {"id": 143, "displayName": "Spawner", "name": "spawner", "hardness": 5, "minStateId": 1951, "maxStateId": 1951, "states": [], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 1951, "resistance": 5}, {"id": 144, "displayName": "Oak Stairs", "name": "oak_stairs", "hardness": 2, "minStateId": 1952, "maxStateId": 2031, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [154], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 1963, "resistance": 3}, {"id": 145, "displayName": "Chest", "name": "chest", "hardness": 2.5, "minStateId": 2032, "maxStateId": 2055, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "type", "type": "enum", "num_values": 3, "values": ["single", "left", "right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [155], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 2033, "resistance": 2.5}, {"id": 146, "displayName": "Redstone Wire", "name": "redstone_wire", "hardness": 0, "minStateId": 2056, "maxStateId": 3351, "states": [{"name": "east", "type": "enum", "num_values": 3, "values": ["up", "side", "none"]}, {"name": "north", "type": "enum", "num_values": 3, "values": ["up", "side", "none"]}, {"name": "power", "type": "int", "num_values": 16}, {"name": "south", "type": "enum", "num_values": 3, "values": ["up", "side", "none"]}, {"name": "west", "type": "enum", "num_values": 3, "values": ["up", "side", "none"]}], "drops": [600], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 3216, "resistance": 0}, {"id": 147, "displayName": "Diamond Ore", "name": "diamond_ore", "hardness": 3, "minStateId": 3352, "maxStateId": 3352, "states": [], "drops": [156, 529], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "543": true}, "defaultState": 3352, "resistance": 3}, {"id": 148, "displayName": "Block of Diamond", "name": "diamond_block", "hardness": 5, "minStateId": 3353, "maxStateId": 3353, "states": [], "drops": [157], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "543": true}, "defaultState": 3353, "resistance": 6}, {"id": 149, "displayName": "Crafting Table", "name": "crafting_table", "hardness": 2.5, "minStateId": 3354, "maxStateId": 3354, "states": [], "drops": [158], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 3354, "resistance": 2.5}, {"id": 150, "displayName": "Wheat Crops", "name": "wheat", "hardness": 0, "minStateId": 3355, "maxStateId": 3362, "states": [{"name": "age", "type": "int", "num_values": 8}], "drops": [561, 560], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 3355, "resistance": 0}, {"id": 151, "displayName": "Farmland", "name": "farmland", "hardness": 0.6, "minStateId": 3363, "maxStateId": 3370, "states": [{"name": "moisture", "type": "int", "num_values": 8}], "drops": [9], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 3363, "resistance": 0.6}, {"id": 152, "displayName": "Furnace", "name": "furnace", "hardness": 3.5, "minStateId": 3371, "maxStateId": 3378, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "lit", "type": "bool", "num_values": 2}], "drops": [160], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 13, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 3372, "resistance": 3.5}, {"id": 153, "displayName": "Oak Sign", "name": "oak_sign", "hardness": 1, "minStateId": 3379, "maxStateId": 3410, "states": [{"name": "rotation", "type": "int", "num_values": 16}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [589], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 3380, "resistance": 1}, {"id": 154, "displayName": "Spruce Sign", "name": "spruce_sign", "hardness": 1, "minStateId": 3411, "maxStateId": 3442, "states": [{"name": "rotation", "type": "int", "num_values": 16}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [590], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 3412, "resistance": 1}, {"id": 155, "displayName": "Birch Sign", "name": "birch_sign", "hardness": 1, "minStateId": 3443, "maxStateId": 3474, "states": [{"name": "rotation", "type": "int", "num_values": 16}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [591], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 3444, "resistance": 1}, {"id": 156, "displayName": "Acacia Sign", "name": "acacia_sign", "hardness": 1, "minStateId": 3475, "maxStateId": 3506, "states": [{"name": "rotation", "type": "int", "num_values": 16}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [593], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 3476, "resistance": 1}, {"id": 157, "displayName": "Jungle Sign", "name": "jungle_sign", "hardness": 1, "minStateId": 3507, "maxStateId": 3538, "states": [{"name": "rotation", "type": "int", "num_values": 16}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [592], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 3508, "resistance": 1}, {"id": 158, "displayName": "Dark Oak Sign", "name": "dark_oak_sign", "hardness": 1, "minStateId": 3539, "maxStateId": 3570, "states": [{"name": "rotation", "type": "int", "num_values": 16}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [594], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 3540, "resistance": 1}, {"id": 159, "displayName": "Oak Door", "name": "oak_door", "hardness": 3, "minStateId": 3571, "maxStateId": 3634, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}, {"name": "hinge", "type": "enum", "num_values": 2, "values": ["left", "right"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [507], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 3582, "resistance": 3}, {"id": 160, "displayName": "Ladder", "name": "ladder", "hardness": 0.4, "minStateId": 3635, "maxStateId": 3642, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [161], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 3636, "resistance": 0.4}, {"id": 161, "displayName": "Rail", "name": "rail", "hardness": 0.7, "minStateId": 3643, "maxStateId": 3652, "states": [{"name": "shape", "type": "enum", "num_values": 10, "values": ["north_south", "east_west", "ascending_east", "ascending_west", "ascending_north", "ascending_south", "south_east", "south_west", "north_west", "north_east"]}], "drops": [162], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "rock", "defaultState": 3643, "resistance": 0.7}, {"id": 162, "displayName": "Cobblestone Stairs", "name": "cobblestone_stairs", "hardness": 2, "minStateId": 3653, "maxStateId": 3732, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [163], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 3664, "resistance": 6}, {"id": 163, "displayName": "Oak Wall Sign", "name": "oak_wall_sign", "hardness": 1, "minStateId": 3733, "maxStateId": 3740, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 3734, "resistance": 1}, {"id": 164, "displayName": "Spruce Wall Sign", "name": "spruce_wall_sign", "hardness": 1, "minStateId": 3741, "maxStateId": 3748, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 3742, "resistance": 1}, {"id": 165, "displayName": "<PERSON> Sign", "name": "birch_wall_sign", "hardness": 1, "minStateId": 3749, "maxStateId": 3756, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 3750, "resistance": 1}, {"id": 166, "displayName": "Acacia Wall Sign", "name": "acacia_wall_sign", "hardness": 1, "minStateId": 3757, "maxStateId": 3764, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 3758, "resistance": 1}, {"id": 167, "displayName": "Jungle Wall Sign", "name": "jungle_wall_sign", "hardness": 1, "minStateId": 3765, "maxStateId": 3772, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 3766, "resistance": 1}, {"id": 168, "displayName": "Dark Oak Wall Sign", "name": "dark_oak_wall_sign", "hardness": 1, "minStateId": 3773, "maxStateId": 3780, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 3774, "resistance": 1}, {"id": 169, "displayName": "Lever", "name": "lever", "hardness": 0.5, "minStateId": 3781, "maxStateId": 3804, "states": [{"name": "face", "type": "enum", "num_values": 3, "values": ["floor", "wall", "ceiling"]}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [164], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 3790, "resistance": 0.5}, {"id": 170, "displayName": "Stone Pressure Plate", "name": "stone_pressure_plate", "hardness": 0.5, "minStateId": 3805, "maxStateId": 3806, "states": [{"name": "powered", "type": "bool", "num_values": 2}], "drops": [165], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 3806, "resistance": 0.5}, {"id": 171, "displayName": "Iron Door", "name": "iron_door", "hardness": 5, "minStateId": 3807, "maxStateId": 3870, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}, {"name": "hinge", "type": "enum", "num_values": 2, "values": ["left", "right"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [506], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 3818, "resistance": 5}, {"id": 172, "displayName": "Oak Pressure Plate", "name": "oak_pressure_plate", "hardness": 0.5, "minStateId": 3871, "maxStateId": 3872, "states": [{"name": "powered", "type": "bool", "num_values": 2}], "drops": [166], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 3872, "resistance": 0.5}, {"id": 173, "displayName": "Spruce Pressure Plate", "name": "spruce_pressure_plate", "hardness": 0.5, "minStateId": 3873, "maxStateId": 3874, "states": [{"name": "powered", "type": "bool", "num_values": 2}], "drops": [167], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 3874, "resistance": 0.5}, {"id": 174, "displayName": "Birch Pressure Plate", "name": "birch_pressure_plate", "hardness": 0.5, "minStateId": 3875, "maxStateId": 3876, "states": [{"name": "powered", "type": "bool", "num_values": 2}], "drops": [168], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 3876, "resistance": 0.5}, {"id": 175, "displayName": "Jungle Pressure Plate", "name": "jungle_pressure_plate", "hardness": 0.5, "minStateId": 3877, "maxStateId": 3878, "states": [{"name": "powered", "type": "bool", "num_values": 2}], "drops": [169], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 3878, "resistance": 0.5}, {"id": 176, "displayName": "Acacia Pressure Plate", "name": "acacia_pressure_plate", "hardness": 0.5, "minStateId": 3879, "maxStateId": 3880, "states": [{"name": "powered", "type": "bool", "num_values": 2}], "drops": [170], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 3880, "resistance": 0.5}, {"id": 177, "displayName": "Dark Oak Pressure Plate", "name": "dark_oak_pressure_plate", "hardness": 0.5, "minStateId": 3881, "maxStateId": 3882, "states": [{"name": "powered", "type": "bool", "num_values": 2}], "drops": [171], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 3882, "resistance": 0.5}, {"id": 178, "displayName": "Redstone Ore", "name": "redstone_ore", "hardness": 3, "minStateId": 3883, "maxStateId": 3884, "states": [{"name": "lit", "type": "bool", "num_values": 2}], "drops": [172, 600], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 9, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "543": true}, "defaultState": 3884, "resistance": 3}, {"id": 179, "displayName": "Redstone Torch", "name": "redstone_torch", "hardness": 0, "minStateId": 3885, "maxStateId": 3886, "states": [{"name": "lit", "type": "bool", "num_values": 2}], "drops": [173], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 7, "boundingBox": "empty", "stackSize": 64, "defaultState": 3885, "resistance": 0}, {"id": 180, "displayName": "Redstone Wall Torch", "name": "redstone_wall_torch", "hardness": 0, "minStateId": 3887, "maxStateId": 3894, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "lit", "type": "bool", "num_values": 2}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 7, "boundingBox": "empty", "stackSize": 64, "defaultState": 3887, "resistance": 0}, {"id": 181, "displayName": "<PERSON>", "name": "stone_button", "hardness": 0.5, "minStateId": 3895, "maxStateId": 3918, "states": [{"name": "face", "type": "enum", "num_values": 3, "values": ["floor", "wall", "ceiling"]}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [174], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "rock", "defaultState": 3904, "resistance": 0.5}, {"id": 182, "displayName": "Snow", "name": "snow", "hardness": 0.1, "minStateId": 3919, "maxStateId": 3926, "states": [{"name": "layers", "type": "enum", "num_values": 8, "values": ["1", "2", "3", "4", "5", "6", "7", "8"]}], "drops": [175, 601, 177], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "harvestTools": {"520": true, "534": true, "538": true, "542": true, "549": true}, "defaultState": 3919, "resistance": 0.1}, {"id": 183, "displayName": "Ice", "name": "ice", "hardness": 0.5, "minStateId": 3927, "maxStateId": 3927, "states": [], "drops": [176], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "defaultState": 3927, "resistance": 0.5}, {"id": 184, "displayName": "Snow Block", "name": "snow_block", "hardness": 0.2, "minStateId": 3928, "maxStateId": 3928, "states": [], "drops": [177, 601], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "harvestTools": {"520": true, "534": true, "538": true, "542": true, "549": true}, "defaultState": 3928, "resistance": 0.2}, {"id": 185, "displayName": "Cactus", "name": "cactus", "hardness": 0.4, "minStateId": 3929, "maxStateId": 3944, "states": [{"name": "age", "type": "int", "num_values": 16}], "drops": [178], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 3929, "resistance": 0.4}, {"id": 186, "displayName": "<PERSON>", "name": "clay", "hardness": 0.6, "minStateId": 3945, "maxStateId": 3945, "states": [], "drops": [179, 610], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 3945, "resistance": 0.6}, {"id": 187, "displayName": "Sugar Cane", "name": "sugar_cane", "hardness": 0, "minStateId": 3946, "maxStateId": 3961, "states": [{"name": "age", "type": "int", "num_values": 16}], "drops": [611], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 3946, "resistance": 0}, {"id": 188, "displayName": "Jukebox", "name": "jukebox", "hardness": 2, "minStateId": 3962, "maxStateId": 3963, "states": [{"name": "has_record", "type": "bool", "num_values": 2}], "drops": [180], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 3963, "resistance": 6}, {"id": 189, "displayName": "Oak Fence", "name": "oak_fence", "hardness": 2, "minStateId": 3964, "maxStateId": 3995, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [181], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 3995, "resistance": 3}, {"id": 190, "displayName": "<PERSON><PERSON><PERSON>", "name": "pumpkin", "hardness": 1, "minStateId": 3996, "maxStateId": 3996, "states": [], "drops": [187], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 3996, "resistance": 1}, {"id": 191, "displayName": "Netherrack", "name": "netherrack", "hardness": 0.4, "minStateId": 3997, "maxStateId": 3997, "states": [], "drops": [189], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 3997, "resistance": 0.4}, {"id": 192, "displayName": "Soul Sand", "name": "soul_sand", "hardness": 0.5, "minStateId": 3998, "maxStateId": 3998, "states": [], "drops": [190], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 3998, "resistance": 0.5}, {"id": 193, "displayName": "Glowstone", "name": "glowstone", "hardness": 0.3, "minStateId": 3999, "maxStateId": 3999, "states": [], "drops": [191, 624], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 15, "boundingBox": "block", "stackSize": 64, "defaultState": 3999, "resistance": 0.3}, {"id": 194, "displayName": "Nether Portal", "name": "nether_portal", "hardness": null, "minStateId": 4000, "maxStateId": 4001, "states": [{"name": "axis", "type": "enum", "num_values": 2, "values": ["x", "z"]}], "drops": [], "diggable": false, "transparent": true, "filterLight": 0, "emitLight": 11, "boundingBox": "empty", "stackSize": 0, "defaultState": 4000, "resistance": -1}, {"id": 195, "displayName": "<PERSON><PERSON>", "name": "carved_pumpkin", "hardness": 1, "minStateId": 4002, "maxStateId": 4005, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [188], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 4002, "resistance": 1}, {"id": 196, "displayName": "<PERSON>'<PERSON>", "name": "jack_o_lantern", "hardness": 1, "minStateId": 4006, "maxStateId": 4009, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [192], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 15, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 4006, "resistance": 1}, {"id": 197, "displayName": "Cake", "name": "cake", "hardness": 0.5, "minStateId": 4010, "maxStateId": 4016, "states": [{"name": "bites", "type": "int", "num_values": 7}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 4010, "resistance": 0.5}, {"id": 198, "displayName": "Redstone Repeater", "name": "repeater", "hardness": 0, "minStateId": 4017, "maxStateId": 4080, "states": [{"name": "delay", "type": "enum", "num_values": 4, "values": ["1", "2", "3", "4"]}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "locked", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [513], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 4020, "resistance": 0}, {"id": 199, "displayName": "White Stained Glass", "name": "white_stained_glass", "hardness": 0.3, "minStateId": 4081, "maxStateId": 4081, "states": [], "drops": [329], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 4081, "resistance": 0.3}, {"id": 200, "displayName": "Orange Stained Glass", "name": "orange_stained_glass", "hardness": 0.3, "minStateId": 4082, "maxStateId": 4082, "states": [], "drops": [330], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 4082, "resistance": 0.3}, {"id": 201, "displayName": "Magenta Stained Glass", "name": "magenta_stained_glass", "hardness": 0.3, "minStateId": 4083, "maxStateId": 4083, "states": [], "drops": [331], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 4083, "resistance": 0.3}, {"id": 202, "displayName": "Light Blue Stained Glass", "name": "light_blue_stained_glass", "hardness": 0.3, "minStateId": 4084, "maxStateId": 4084, "states": [], "drops": [332], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 4084, "resistance": 0.3}, {"id": 203, "displayName": "Yellow Stained Glass", "name": "yellow_stained_glass", "hardness": 0.3, "minStateId": 4085, "maxStateId": 4085, "states": [], "drops": [333], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 4085, "resistance": 0.3}, {"id": 204, "displayName": "Lime Stained Glass", "name": "lime_stained_glass", "hardness": 0.3, "minStateId": 4086, "maxStateId": 4086, "states": [], "drops": [334], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 4086, "resistance": 0.3}, {"id": 205, "displayName": "Pink Stained Glass", "name": "pink_stained_glass", "hardness": 0.3, "minStateId": 4087, "maxStateId": 4087, "states": [], "drops": [335], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 4087, "resistance": 0.3}, {"id": 206, "displayName": "<PERSON> Stained Glass", "name": "gray_stained_glass", "hardness": 0.3, "minStateId": 4088, "maxStateId": 4088, "states": [], "drops": [336], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 4088, "resistance": 0.3}, {"id": 207, "displayName": "Light Gray Stained Glass", "name": "light_gray_stained_glass", "hardness": 0.3, "minStateId": 4089, "maxStateId": 4089, "states": [], "drops": [337], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 4089, "resistance": 0.3}, {"id": 208, "displayName": "<PERSON><PERSON>", "name": "cyan_stained_glass", "hardness": 0.3, "minStateId": 4090, "maxStateId": 4090, "states": [], "drops": [338], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 4090, "resistance": 0.3}, {"id": 209, "displayName": "Purple Stained Glass", "name": "purple_stained_glass", "hardness": 0.3, "minStateId": 4091, "maxStateId": 4091, "states": [], "drops": [339], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 4091, "resistance": 0.3}, {"id": 210, "displayName": "Blue Stained Glass", "name": "blue_stained_glass", "hardness": 0.3, "minStateId": 4092, "maxStateId": 4092, "states": [], "drops": [340], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 4092, "resistance": 0.3}, {"id": 211, "displayName": "<PERSON> Stained Glass", "name": "brown_stained_glass", "hardness": 0.3, "minStateId": 4093, "maxStateId": 4093, "states": [], "drops": [341], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 4093, "resistance": 0.3}, {"id": 212, "displayName": "Green Stained Glass", "name": "green_stained_glass", "hardness": 0.3, "minStateId": 4094, "maxStateId": 4094, "states": [], "drops": [342], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 4094, "resistance": 0.3}, {"id": 213, "displayName": "Red Stained Glass", "name": "red_stained_glass", "hardness": 0.3, "minStateId": 4095, "maxStateId": 4095, "states": [], "drops": [343], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 4095, "resistance": 0.3}, {"id": 214, "displayName": "Black Stained Glass", "name": "black_stained_glass", "hardness": 0.3, "minStateId": 4096, "maxStateId": 4096, "states": [], "drops": [344], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 4096, "resistance": 0.3}, {"id": 215, "displayName": "Oak Trapdoor", "name": "oak_trapdoor", "hardness": 3, "minStateId": 4097, "maxStateId": 4160, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [193], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 4112, "resistance": 3}, {"id": 216, "displayName": "Spruce Trapdoor", "name": "spruce_trapdoor", "hardness": 3, "minStateId": 4161, "maxStateId": 4224, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [194], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 4176, "resistance": 3}, {"id": 217, "displayName": "<PERSON>", "name": "birch_trapdoor", "hardness": 3, "minStateId": 4225, "maxStateId": 4288, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [195], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 4240, "resistance": 3}, {"id": 218, "displayName": "Jungle Trapdoor", "name": "jungle_trapdoor", "hardness": 3, "minStateId": 4289, "maxStateId": 4352, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [196], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 4304, "resistance": 3}, {"id": 219, "displayName": "Acacia T<PERSON>door", "name": "acacia_trapdoor", "hardness": 3, "minStateId": 4353, "maxStateId": 4416, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [197], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 4368, "resistance": 3}, {"id": 220, "displayName": "Dark Oak Trapdoor", "name": "dark_oak_trapdoor", "hardness": 3, "minStateId": 4417, "maxStateId": 4480, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [198], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 4432, "resistance": 3}, {"id": 221, "displayName": "Stone Bricks", "name": "stone_bricks", "hardness": 1.5, "minStateId": 4481, "maxStateId": 4481, "states": [], "drops": [205], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 4481, "resistance": 6}, {"id": 222, "displayName": "Mossy Stone Bricks", "name": "mossy_stone_bricks", "hardness": 1.5, "minStateId": 4482, "maxStateId": 4482, "states": [], "drops": [206], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 4482, "resistance": 6}, {"id": 223, "displayName": "Cracked Stone Bricks", "name": "cracked_stone_bricks", "hardness": 1.5, "minStateId": 4483, "maxStateId": 4483, "states": [], "drops": [207], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 4483, "resistance": 6}, {"id": 224, "displayName": "Chiseled Stone Bricks", "name": "chiseled_stone_bricks", "hardness": 1.5, "minStateId": 4484, "maxStateId": 4484, "states": [], "drops": [208], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 4484, "resistance": 6}, {"id": 225, "displayName": "Infested Stone", "name": "infested_stone", "hardness": 0, "minStateId": 4485, "maxStateId": 4485, "states": [], "drops": [1], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 4485, "resistance": 0.75}, {"id": 226, "displayName": "Infested Cobblestone", "name": "infested_cobblestone", "hardness": 0, "minStateId": 4486, "maxStateId": 4486, "states": [], "drops": [12], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 4486, "resistance": 0.75}, {"id": 227, "displayName": "Infested Stone Bricks", "name": "infested_stone_bricks", "hardness": 0, "minStateId": 4487, "maxStateId": 4487, "states": [], "drops": [205], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 4487, "resistance": 0.75}, {"id": 228, "displayName": "Infested Mossy Stone Bricks", "name": "infested_mossy_stone_bricks", "hardness": 0, "minStateId": 4488, "maxStateId": 4488, "states": [], "drops": [206], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 4488, "resistance": 0.75}, {"id": 229, "displayName": "Infested Cracked Stone Bricks", "name": "infested_cracked_stone_bricks", "hardness": 0, "minStateId": 4489, "maxStateId": 4489, "states": [], "drops": [207], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 4489, "resistance": 0.75}, {"id": 230, "displayName": "Infested Chiseled Stone Bricks", "name": "infested_chiseled_stone_bricks", "hardness": 0, "minStateId": 4490, "maxStateId": 4490, "states": [], "drops": [208], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 4490, "resistance": 0.75}, {"id": 231, "displayName": "Brown Mushroom Block", "name": "brown_mushroom_block", "hardness": 0.2, "minStateId": 4491, "maxStateId": 4554, "states": [{"name": "down", "type": "bool", "num_values": 2}, {"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [209, 111], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 4491, "resistance": 0.2}, {"id": 232, "displayName": "Red Mushroom Block", "name": "red_mushroom_block", "hardness": 0.2, "minStateId": 4555, "maxStateId": 4618, "states": [{"name": "down", "type": "bool", "num_values": 2}, {"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [210, 112], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 4555, "resistance": 0.2}, {"id": 233, "displayName": "Mushroom Stem", "name": "mushroom_stem", "hardness": 0.2, "minStateId": 4619, "maxStateId": 4682, "states": [{"name": "down", "type": "bool", "num_values": 2}, {"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [211], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 4619, "resistance": 0.2}, {"id": 234, "displayName": "Iron Bars", "name": "iron_bars", "hardness": 5, "minStateId": 4683, "maxStateId": 4714, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [212], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 4714, "resistance": 6}, {"id": 235, "displayName": "Glass Pane", "name": "glass_pane", "hardness": 0.3, "minStateId": 4715, "maxStateId": 4746, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [213], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 4746, "resistance": 0.3}, {"id": 236, "displayName": "Melon", "name": "melon", "hardness": 1, "minStateId": 4747, "maxStateId": 4747, "states": [], "drops": [214, 673], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 4747, "resistance": 1}, {"id": 237, "displayName": "Attached P<PERSON><PERSON> Stem", "name": "attached_pumpkin_stem", "hardness": 0, "minStateId": 4748, "maxStateId": 4751, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [675], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 4748, "resistance": 0}, {"id": 238, "displayName": "Attached Melon Stem", "name": "attached_melon_stem", "hardness": 0, "minStateId": 4752, "maxStateId": 4755, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [676], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 4752, "resistance": 0}, {"id": 239, "displayName": "<PERSON><PERSON><PERSON>", "name": "pumpkin_stem", "hardness": 0, "minStateId": 4756, "maxStateId": 4763, "states": [{"name": "age", "type": "int", "num_values": 8}], "drops": [675], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 4756, "resistance": 0}, {"id": 240, "displayName": "Melon Stem", "name": "melon_stem", "hardness": 0, "minStateId": 4764, "maxStateId": 4771, "states": [{"name": "age", "type": "int", "num_values": 8}], "drops": [676], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 4764, "resistance": 0}, {"id": 241, "displayName": "Vines", "name": "vine", "hardness": 0.2, "minStateId": 4772, "maxStateId": 4803, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [215, 672], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 4803, "resistance": 0.2}, {"id": 242, "displayName": "Oak Fence Gate", "name": "oak_fence_gate", "hardness": 2, "minStateId": 4804, "maxStateId": 4835, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "in_wall", "type": "bool", "num_values": 2}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [216], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 4811, "resistance": 3}, {"id": 243, "displayName": "Brick Stairs", "name": "brick_stairs", "hardness": 2, "minStateId": 4836, "maxStateId": 4915, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [222], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 4847, "resistance": 6}, {"id": 244, "displayName": "Stone Brick Stairs", "name": "stone_brick_stairs", "hardness": 1.5, "minStateId": 4916, "maxStateId": 4995, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [223], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 4927, "resistance": 6}, {"id": 245, "displayName": "Mycelium", "name": "mycelium", "hardness": 0.6, "minStateId": 4996, "maxStateId": 4997, "states": [{"name": "snowy", "type": "bool", "num_values": 2}], "drops": [224, 9], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 4997, "resistance": 0.6}, {"id": 246, "displayName": "<PERSON>", "name": "lily_pad", "hardness": 0, "minStateId": 4998, "maxStateId": 4998, "states": [], "drops": [225], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 4998, "resistance": 0}, {"id": 247, "displayName": "Nether Bricks", "name": "nether_bricks", "hardness": 2, "minStateId": 4999, "maxStateId": 4999, "states": [], "drops": [226], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 4999, "resistance": 6}, {"id": 248, "displayName": "Nether Brick Fence", "name": "nether_brick_fence", "hardness": 2, "minStateId": 5000, "maxStateId": 5031, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [227], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 5031, "resistance": 6}, {"id": 249, "displayName": "Nether Brick Stairs", "name": "nether_brick_stairs", "hardness": 2, "minStateId": 5032, "maxStateId": 5111, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [228], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 5043, "resistance": 6}, {"id": 250, "displayName": "Nether Wart", "name": "nether_wart", "hardness": 0, "minStateId": 5112, "maxStateId": 5115, "states": [{"name": "age", "type": "int", "num_values": 4}], "drops": [686], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 5112, "resistance": 0}, {"id": 251, "displayName": "Enchanting Table", "name": "enchanting_table", "hardness": 5, "minStateId": 5116, "maxStateId": 5116, "states": [], "drops": [229], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 5116, "resistance": 1200}, {"id": 252, "displayName": "Brewing Stand", "name": "brewing_stand", "hardness": 0.5, "minStateId": 5117, "maxStateId": 5124, "states": [{"name": "has_bottle_0", "type": "bool", "num_values": 2}, {"name": "has_bottle_1", "type": "bool", "num_values": 2}, {"name": "has_bottle_2", "type": "bool", "num_values": 2}], "drops": [693], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 1, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 5124, "resistance": 0.5}, {"id": 253, "displayName": "<PERSON><PERSON><PERSON>", "name": "cauldron", "hardness": 2, "minStateId": 5125, "maxStateId": 5128, "states": [{"name": "level", "type": "int", "num_values": 4}], "drops": [694], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 5125, "resistance": 2}, {"id": 254, "displayName": "End Portal", "name": "end_portal", "hardness": null, "minStateId": 5129, "maxStateId": 5129, "states": [], "drops": [], "diggable": false, "transparent": true, "filterLight": 0, "emitLight": 15, "boundingBox": "empty", "stackSize": 0, "defaultState": 5129, "resistance": 3600000}, {"id": 255, "displayName": "End Portal Frame", "name": "end_portal_frame", "hardness": null, "minStateId": 5130, "maxStateId": 5137, "states": [{"name": "eye", "type": "bool", "num_values": 2}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": false, "transparent": true, "filterLight": 0, "emitLight": 1, "boundingBox": "block", "stackSize": 64, "defaultState": 5134, "resistance": 3600000}, {"id": 256, "displayName": "End Stone", "name": "end_stone", "hardness": 3, "minStateId": 5138, "maxStateId": 5138, "states": [], "drops": [231], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 5138, "resistance": 9}, {"id": 257, "displayName": "Dragon Egg", "name": "dragon_egg", "hardness": 3, "minStateId": 5139, "maxStateId": 5139, "states": [], "drops": [233], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5139, "resistance": 9}, {"id": 258, "displayName": "Redstone Lamp", "name": "redstone_lamp", "hardness": 0.3, "minStateId": 5140, "maxStateId": 5141, "states": [{"name": "lit", "type": "bool", "num_values": 2}], "drops": [234], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 15, "boundingBox": "block", "stackSize": 64, "defaultState": 5141, "resistance": 0.3}, {"id": 259, "displayName": "Cocoa", "name": "cocoa", "hardness": 0.2, "minStateId": 5142, "maxStateId": 5153, "states": [{"name": "age", "type": "int", "num_values": 3}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [634], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 5142, "resistance": 3}, {"id": 260, "displayName": "Sandstone Stairs", "name": "sandstone_stairs", "hardness": 0.8, "minStateId": 5154, "maxStateId": 5233, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [235], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 5165, "resistance": 0.8}, {"id": 261, "displayName": "Emerald Ore", "name": "emerald_ore", "hardness": 3, "minStateId": 5234, "maxStateId": 5234, "states": [], "drops": [236, 760], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "543": true}, "defaultState": 5234, "resistance": 3}, {"id": 262, "displayName": "<PERSON><PERSON> Chest", "name": "ender_chest", "hardness": 22.5, "minStateId": 5235, "maxStateId": 5242, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [237, 145], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 5236, "resistance": 600}, {"id": 263, "displayName": "Tripwire Hook", "name": "tripwire_hook", "hardness": 0, "minStateId": 5243, "maxStateId": 5258, "states": [{"name": "attached", "type": "bool", "num_values": 2}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [238], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 5252, "resistance": 0}, {"id": 264, "displayName": "Tripwire", "name": "tripwire", "hardness": 0, "minStateId": 5259, "maxStateId": 5386, "states": [{"name": "attached", "type": "bool", "num_values": 2}, {"name": "disarmed", "type": "bool", "num_values": 2}, {"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [552], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 5386, "resistance": 0}, {"id": 265, "displayName": "Block of Emerald", "name": "emerald_block", "hardness": 5, "minStateId": 5387, "maxStateId": 5387, "states": [], "drops": [239], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "543": true}, "defaultState": 5387, "resistance": 6}, {"id": 266, "displayName": "Spruce Stairs", "name": "spruce_stairs", "hardness": 2, "minStateId": 5388, "maxStateId": 5467, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [240], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 5399, "resistance": 3}, {"id": 267, "displayName": "<PERSON> Stairs", "name": "birch_stairs", "hardness": 2, "minStateId": 5468, "maxStateId": 5547, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [241], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 5479, "resistance": 3}, {"id": 268, "displayName": "Jungle Stairs", "name": "jungle_stairs", "hardness": 2, "minStateId": 5548, "maxStateId": 5627, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [242], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 5559, "resistance": 3}, {"id": 269, "displayName": "Command Block", "name": "command_block", "hardness": null, "minStateId": 5628, "maxStateId": 5639, "states": [{"name": "conditional", "type": "bool", "num_values": 2}, {"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [], "diggable": false, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5634, "resistance": 3600000}, {"id": 270, "displayName": "Beacon", "name": "beacon", "hardness": 3, "minStateId": 5640, "maxStateId": 5640, "states": [], "drops": [244], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 15, "boundingBox": "block", "stackSize": 64, "defaultState": 5640, "resistance": 3}, {"id": 271, "displayName": "Cobblestone Wall", "name": "cobblestone_wall", "hardness": 2, "minStateId": 5641, "maxStateId": 5704, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [245], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 5700, "resistance": 6}, {"id": 272, "displayName": "<PERSON><PERSON>", "name": "mossy_cobblestone_wall", "hardness": 2, "minStateId": 5705, "maxStateId": 5768, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [246], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 5764, "resistance": 6}, {"id": 273, "displayName": "Flower Pot", "name": "flower_pot", "hardness": 0, "minStateId": 5769, "maxStateId": 5769, "states": [], "drops": [762], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5769, "resistance": 0}, {"id": 274, "displayName": "Potted Oak Sapling", "name": "potted_oak_sapling", "hardness": 0, "minStateId": 5770, "maxStateId": 5770, "states": [], "drops": [762, 19], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5770, "resistance": 0}, {"id": 275, "displayName": "Potted Spruce Sapling", "name": "potted_spruce_sapling", "hardness": 0, "minStateId": 5771, "maxStateId": 5771, "states": [], "drops": [762, 20], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5771, "resistance": 0}, {"id": 276, "displayName": "Potted Birch Sapling", "name": "potted_birch_sapling", "hardness": 0, "minStateId": 5772, "maxStateId": 5772, "states": [], "drops": [762, 21], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5772, "resistance": 0}, {"id": 277, "displayName": "Potted Jungle Sapling", "name": "potted_jungle_sapling", "hardness": 0, "minStateId": 5773, "maxStateId": 5773, "states": [], "drops": [762, 22], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5773, "resistance": 0}, {"id": 278, "displayName": "Potted Acacia Sapling", "name": "potted_acacia_sapling", "hardness": 0, "minStateId": 5774, "maxStateId": 5774, "states": [], "drops": [762, 23], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5774, "resistance": 0}, {"id": 279, "displayName": "Potted Dark Oak Sapling", "name": "potted_dark_oak_sapling", "hardness": 0, "minStateId": 5775, "maxStateId": 5775, "states": [], "drops": [762, 24], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5775, "resistance": 0}, {"id": 280, "displayName": "Potted Fern", "name": "potted_fern", "hardness": 0, "minStateId": 5776, "maxStateId": 5776, "states": [], "drops": [762, 77], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5776, "resistance": 0}, {"id": 281, "displayName": "Potted Dandelion", "name": "potted_dandelion", "hardness": 0, "minStateId": 5777, "maxStateId": 5777, "states": [], "drops": [762, 98], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "plant", "defaultState": 5777, "resistance": 0}, {"id": 282, "displayName": "Potted Poppy", "name": "potted_poppy", "hardness": 0, "minStateId": 5778, "maxStateId": 5778, "states": [], "drops": [762, 99], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5778, "resistance": 0}, {"id": 283, "displayName": "Potted Blue Orchid", "name": "potted_blue_orchid", "hardness": 0, "minStateId": 5779, "maxStateId": 5779, "states": [], "drops": [762, 100], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5779, "resistance": 0}, {"id": 284, "displayName": "Potted Allium", "name": "potted_allium", "hardness": 0, "minStateId": 5780, "maxStateId": 5780, "states": [], "drops": [762, 101], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5780, "resistance": 0}, {"id": 285, "displayName": "Potted Azure Bluet", "name": "potted_azure_bluet", "hardness": 0, "minStateId": 5781, "maxStateId": 5781, "states": [], "drops": [762, 102], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5781, "resistance": 0}, {"id": 286, "displayName": "Potted Red Tulip", "name": "potted_red_tulip", "hardness": 0, "minStateId": 5782, "maxStateId": 5782, "states": [], "drops": [762, 103], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5782, "resistance": 0}, {"id": 287, "displayName": "Potted Orange Tulip", "name": "potted_orange_tulip", "hardness": 0, "minStateId": 5783, "maxStateId": 5783, "states": [], "drops": [762, 104], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5783, "resistance": 0}, {"id": 288, "displayName": "Potted White Tulip", "name": "potted_white_tulip", "hardness": 0, "minStateId": 5784, "maxStateId": 5784, "states": [], "drops": [762, 105], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5784, "resistance": 0}, {"id": 289, "displayName": "Potted Pink Tulip", "name": "potted_pink_tulip", "hardness": 0, "minStateId": 5785, "maxStateId": 5785, "states": [], "drops": [762, 106], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5785, "resistance": 0}, {"id": 290, "displayName": "Potted Oxeye Daisy", "name": "potted_oxeye_daisy", "hardness": 0, "minStateId": 5786, "maxStateId": 5786, "states": [], "drops": [762, 107], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5786, "resistance": 0}, {"id": 291, "displayName": "Potted Cornflower", "name": "potted_cornflower", "hardness": 0, "minStateId": 5787, "maxStateId": 5787, "states": [], "drops": [762, 108], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5787, "resistance": 0}, {"id": 292, "displayName": "Potted Lily of the Valley", "name": "potted_lily_of_the_valley", "hardness": 0, "minStateId": 5788, "maxStateId": 5788, "states": [], "drops": [762, 109], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5788, "resistance": 0}, {"id": 293, "displayName": "Potted Wither <PERSON>", "name": "potted_wither_rose", "hardness": 0, "minStateId": 5789, "maxStateId": 5789, "states": [], "drops": [762, 110], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5789, "resistance": 0}, {"id": 294, "displayName": "Potted Red Mushroom", "name": "potted_red_mushroom", "hardness": 0, "minStateId": 5790, "maxStateId": 5790, "states": [], "drops": [762, 112], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5790, "resistance": 0}, {"id": 295, "displayName": "Potted Brown Mushroom", "name": "potted_brown_mushroom", "hardness": 0, "minStateId": 5791, "maxStateId": 5791, "states": [], "drops": [762, 111], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5791, "resistance": 0}, {"id": 296, "displayName": "Potted Dead Bush", "name": "potted_dead_bush", "hardness": 0, "minStateId": 5792, "maxStateId": 5792, "states": [], "drops": [762, 78], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5792, "resistance": 0}, {"id": 297, "displayName": "Potted Cactus", "name": "potted_cactus", "hardness": 0, "minStateId": 5793, "maxStateId": 5793, "states": [], "drops": [762, 178], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5793, "resistance": 0}, {"id": 298, "displayName": "Carrots", "name": "carrots", "hardness": 0, "minStateId": 5794, "maxStateId": 5801, "states": [{"name": "age", "type": "int", "num_values": 8}], "drops": [763], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 5794, "resistance": 0}, {"id": 299, "displayName": "Potatoes", "name": "potatoes", "hardness": 0, "minStateId": 5802, "maxStateId": 5809, "states": [{"name": "age", "type": "int", "num_values": 8}], "drops": [764, 766], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 5802, "resistance": 0}, {"id": 300, "displayName": "Oak Button", "name": "oak_button", "hardness": 0.5, "minStateId": 5810, "maxStateId": 5833, "states": [{"name": "face", "type": "enum", "num_values": 3, "values": ["floor", "wall", "ceiling"]}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [259], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 5819, "resistance": 0.5}, {"id": 301, "displayName": "Spruce Button", "name": "spruce_button", "hardness": 0.5, "minStateId": 5834, "maxStateId": 5857, "states": [{"name": "face", "type": "enum", "num_values": 3, "values": ["floor", "wall", "ceiling"]}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [260], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 5843, "resistance": 0.5}, {"id": 302, "displayName": "<PERSON>", "name": "birch_button", "hardness": 0.5, "minStateId": 5858, "maxStateId": 5881, "states": [{"name": "face", "type": "enum", "num_values": 3, "values": ["floor", "wall", "ceiling"]}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [261], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 5867, "resistance": 0.5}, {"id": 303, "displayName": "<PERSON>ton", "name": "jungle_button", "hardness": 0.5, "minStateId": 5882, "maxStateId": 5905, "states": [{"name": "face", "type": "enum", "num_values": 3, "values": ["floor", "wall", "ceiling"]}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [262], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 5891, "resistance": 0.5}, {"id": 304, "displayName": "Acacia <PERSON>", "name": "acacia_button", "hardness": 0.5, "minStateId": 5906, "maxStateId": 5929, "states": [{"name": "face", "type": "enum", "num_values": 3, "values": ["floor", "wall", "ceiling"]}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [263], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 5915, "resistance": 0.5}, {"id": 305, "displayName": "Dark Oak Button", "name": "dark_oak_button", "hardness": 0.5, "minStateId": 5930, "maxStateId": 5953, "states": [{"name": "face", "type": "enum", "num_values": 3, "values": ["floor", "wall", "ceiling"]}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [264], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "wood", "defaultState": 5939, "resistance": 0.5}, {"id": 306, "displayName": "Skeleton Skull", "name": "skeleton_skull", "hardness": 1, "minStateId": 5954, "maxStateId": 5969, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [769], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5954, "resistance": 1}, {"id": 307, "displayName": "Skeleton Wall Skull", "name": "skeleton_wall_skull", "hardness": 1, "minStateId": 5970, "maxStateId": 5973, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5970, "resistance": 1}, {"id": 308, "displayName": "Wither Skeleton Skull", "name": "wither_skeleton_skull", "hardness": 1, "minStateId": 5974, "maxStateId": 5989, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [770], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5974, "resistance": 1}, {"id": 309, "displayName": "Wither Skeleton Wall Skull", "name": "wither_skeleton_wall_skull", "hardness": 1, "minStateId": 5990, "maxStateId": 5993, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5990, "resistance": 1}, {"id": 310, "displayName": "Zombie Head", "name": "zombie_head", "hardness": 1, "minStateId": 5994, "maxStateId": 6009, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [772], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 5994, "resistance": 1}, {"id": 311, "displayName": "<PERSON> Head", "name": "zombie_wall_head", "hardness": 1, "minStateId": 6010, "maxStateId": 6013, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6010, "resistance": 1}, {"id": 312, "displayName": "Player Head", "name": "player_head", "hardness": 1, "minStateId": 6014, "maxStateId": 6029, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [771], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6014, "resistance": 1}, {"id": 313, "displayName": "Player <PERSON>", "name": "player_wall_head", "hardness": 1, "minStateId": 6030, "maxStateId": 6033, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6030, "resistance": 1}, {"id": 314, "displayName": "Creeper Head", "name": "creeper_head", "hardness": 1, "minStateId": 6034, "maxStateId": 6049, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [773], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6034, "resistance": 1}, {"id": 315, "displayName": "Creeper Wall Head", "name": "creeper_wall_head", "hardness": 1, "minStateId": 6050, "maxStateId": 6053, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6050, "resistance": 1}, {"id": 316, "displayName": "Dragon Head", "name": "dragon_head", "hardness": 1, "minStateId": 6054, "maxStateId": 6069, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [774], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6054, "resistance": 1}, {"id": 317, "displayName": "Dragon Wall Head", "name": "dragon_wall_head", "hardness": 1, "minStateId": 6070, "maxStateId": 6073, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6070, "resistance": 1}, {"id": 318, "displayName": "An<PERSON>", "name": "anvil", "hardness": 5, "minStateId": 6074, "maxStateId": 6077, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [265], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6074, "resistance": 1200}, {"id": 319, "displayName": "Chipped Anvil", "name": "chipped_anvil", "hardness": 5, "minStateId": 6078, "maxStateId": 6081, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [266], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6078, "resistance": 1200}, {"id": 320, "displayName": "Damaged Anvil", "name": "damaged_anvil", "hardness": 5, "minStateId": 6082, "maxStateId": 6085, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [267], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6082, "resistance": 1200}, {"id": 321, "displayName": "Trapped Chest", "name": "trapped_chest", "hardness": 2.5, "minStateId": 6086, "maxStateId": 6109, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "type", "type": "enum", "num_values": 3, "values": ["single", "left", "right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [268], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 6087, "resistance": 2.5}, {"id": 322, "displayName": "Light Weighted Pressure Plate", "name": "light_weighted_pressure_plate", "hardness": 0.5, "minStateId": 6110, "maxStateId": 6125, "states": [{"name": "power", "type": "int", "num_values": 16}], "drops": [269], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6110, "resistance": 0.5}, {"id": 323, "displayName": "Heavy Weighted Pressure Plate", "name": "heavy_weighted_pressure_plate", "hardness": 0.5, "minStateId": 6126, "maxStateId": 6141, "states": [{"name": "power", "type": "int", "num_values": 16}], "drops": [270], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6126, "resistance": 0.5}, {"id": 324, "displayName": "Redstone Comparator", "name": "comparator", "hardness": 0, "minStateId": 6142, "maxStateId": 6157, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "mode", "type": "enum", "num_values": 2, "values": ["compare", "subtract"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [514], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6143, "resistance": 0}, {"id": 325, "displayName": "Daylight Detector", "name": "daylight_detector", "hardness": 0.2, "minStateId": 6158, "maxStateId": 6189, "states": [{"name": "inverted", "type": "bool", "num_values": 2}, {"name": "power", "type": "int", "num_values": 16}], "drops": [271], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 6174, "resistance": 0.2}, {"id": 326, "displayName": "Block of Redstone", "name": "redstone_block", "hardness": 5, "minStateId": 6190, "maxStateId": 6190, "states": [], "drops": [272], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6190, "resistance": 6}, {"id": 327, "displayName": "<PERSON><PERSON>", "name": "nether_quartz_ore", "hardness": 3, "minStateId": 6191, "maxStateId": 6191, "states": [], "drops": [273, 782], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6191, "resistance": 3}, {"id": 328, "displayName": "<PERSON>", "name": "hopper", "hardness": 3, "minStateId": 6192, "maxStateId": 6201, "states": [{"name": "enabled", "type": "bool", "num_values": 2}, {"name": "facing", "type": "enum", "num_values": 5, "values": ["down", "north", "south", "west", "east"]}], "drops": [274], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6192, "resistance": 4.8}, {"id": 329, "displayName": "Block of Quartz", "name": "quartz_block", "hardness": 0.8, "minStateId": 6202, "maxStateId": 6202, "states": [], "drops": [276], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6202, "resistance": 0.8}, {"id": 330, "displayName": "Chiseled Quartz Block", "name": "chiseled_quartz_block", "hardness": 0.8, "minStateId": 6203, "maxStateId": 6203, "states": [], "drops": [275], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6203, "resistance": 0.8}, {"id": 331, "displayName": "Quartz <PERSON>", "name": "quartz_pillar", "hardness": 0.8, "minStateId": 6204, "maxStateId": 6206, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [277], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6205, "resistance": 0.8}, {"id": 332, "displayName": "Quartz Stairs", "name": "quartz_stairs", "hardness": 0.8, "minStateId": 6207, "maxStateId": 6286, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [278], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6218, "resistance": 0.8}, {"id": 333, "displayName": "Activator Rail", "name": "activator_rail", "hardness": 0.7, "minStateId": 6287, "maxStateId": 6298, "states": [{"name": "powered", "type": "bool", "num_values": 2}, {"name": "shape", "type": "enum", "num_values": 6, "values": ["north_south", "east_west", "ascending_east", "ascending_west", "ascending_north", "ascending_south"]}], "drops": [279], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "rock", "defaultState": 6293, "resistance": 0.7}, {"id": 334, "displayName": "Dropper", "name": "dropper", "hardness": 3.5, "minStateId": 6299, "maxStateId": 6310, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}, {"name": "triggered", "type": "bool", "num_values": 2}], "drops": [280], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6300, "resistance": 3.5}, {"id": 335, "displayName": "White Terracotta", "name": "white_terracotta", "hardness": 1.25, "minStateId": 6311, "maxStateId": 6311, "states": [], "drops": [281], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6311, "resistance": 4.2}, {"id": 336, "displayName": "Orange Terracotta", "name": "orange_terracotta", "hardness": 1.25, "minStateId": 6312, "maxStateId": 6312, "states": [], "drops": [282], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6312, "resistance": 4.2}, {"id": 337, "displayName": "Magenta Terracotta", "name": "magenta_terracotta", "hardness": 1.25, "minStateId": 6313, "maxStateId": 6313, "states": [], "drops": [283], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6313, "resistance": 4.2}, {"id": 338, "displayName": "Light Blue Terracotta", "name": "light_blue_terracotta", "hardness": 1.25, "minStateId": 6314, "maxStateId": 6314, "states": [], "drops": [284], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6314, "resistance": 4.2}, {"id": 339, "displayName": "Yellow Terracotta", "name": "yellow_terracotta", "hardness": 1.25, "minStateId": 6315, "maxStateId": 6315, "states": [], "drops": [285], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6315, "resistance": 4.2}, {"id": 340, "displayName": "Lime Terracotta", "name": "lime_terracotta", "hardness": 1.25, "minStateId": 6316, "maxStateId": 6316, "states": [], "drops": [286], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6316, "resistance": 4.2}, {"id": 341, "displayName": "Pink Terracotta", "name": "pink_terracotta", "hardness": 1.25, "minStateId": 6317, "maxStateId": 6317, "states": [], "drops": [287], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6317, "resistance": 4.2}, {"id": 342, "displayName": "Gray <PERSON>", "name": "gray_terracotta", "hardness": 1.25, "minStateId": 6318, "maxStateId": 6318, "states": [], "drops": [288], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6318, "resistance": 4.2}, {"id": 343, "displayName": "Light Gray Terracotta", "name": "light_gray_terracotta", "hardness": 1.25, "minStateId": 6319, "maxStateId": 6319, "states": [], "drops": [289], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6319, "resistance": 4.2}, {"id": 344, "displayName": "<PERSON><PERSON>", "name": "cyan_terracotta", "hardness": 1.25, "minStateId": 6320, "maxStateId": 6320, "states": [], "drops": [290], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6320, "resistance": 4.2}, {"id": 345, "displayName": "Purple Terracotta", "name": "purple_terracotta", "hardness": 1.25, "minStateId": 6321, "maxStateId": 6321, "states": [], "drops": [291], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6321, "resistance": 4.2}, {"id": 346, "displayName": "Blue Terracotta", "name": "blue_terracotta", "hardness": 1.25, "minStateId": 6322, "maxStateId": 6322, "states": [], "drops": [292], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6322, "resistance": 4.2}, {"id": 347, "displayName": "Brown Terracotta", "name": "brown_terracotta", "hardness": 1.25, "minStateId": 6323, "maxStateId": 6323, "states": [], "drops": [293], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6323, "resistance": 4.2}, {"id": 348, "displayName": "Green Terracotta", "name": "green_terracotta", "hardness": 1.25, "minStateId": 6324, "maxStateId": 6324, "states": [], "drops": [294], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6324, "resistance": 4.2}, {"id": 349, "displayName": "Red Terracotta", "name": "red_terracotta", "hardness": 1.25, "minStateId": 6325, "maxStateId": 6325, "states": [], "drops": [295], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6325, "resistance": 4.2}, {"id": 350, "displayName": "Black Terracotta", "name": "black_terracotta", "hardness": 1.25, "minStateId": 6326, "maxStateId": 6326, "states": [], "drops": [296], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 6326, "resistance": 4.2}, {"id": 351, "displayName": "White Stained Glass Pane", "name": "white_stained_glass_pane", "hardness": 0.3, "minStateId": 6327, "maxStateId": 6358, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [345], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6358, "resistance": 0.3}, {"id": 352, "displayName": "Orange Stained Glass Pane", "name": "orange_stained_glass_pane", "hardness": 0.3, "minStateId": 6359, "maxStateId": 6390, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [346], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6390, "resistance": 0.3}, {"id": 353, "displayName": "Magenta Stained Glass Pane", "name": "magenta_stained_glass_pane", "hardness": 0.3, "minStateId": 6391, "maxStateId": 6422, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [347], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6422, "resistance": 0.3}, {"id": 354, "displayName": "Light Blue Stained Glass Pane", "name": "light_blue_stained_glass_pane", "hardness": 0.3, "minStateId": 6423, "maxStateId": 6454, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [348], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6454, "resistance": 0.3}, {"id": 355, "displayName": "Yellow Stained Glass Pane", "name": "yellow_stained_glass_pane", "hardness": 0.3, "minStateId": 6455, "maxStateId": 6486, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [349], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6486, "resistance": 0.3}, {"id": 356, "displayName": "Lime Stained Glass Pane", "name": "lime_stained_glass_pane", "hardness": 0.3, "minStateId": 6487, "maxStateId": 6518, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [350], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6518, "resistance": 0.3}, {"id": 357, "displayName": "Pink Stained Glass Pane", "name": "pink_stained_glass_pane", "hardness": 0.3, "minStateId": 6519, "maxStateId": 6550, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [351], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6550, "resistance": 0.3}, {"id": 358, "displayName": "Gray Stained Glass Pane", "name": "gray_stained_glass_pane", "hardness": 0.3, "minStateId": 6551, "maxStateId": 6582, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [352], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6582, "resistance": 0.3}, {"id": 359, "displayName": "Light Gray Stained Glass Pane", "name": "light_gray_stained_glass_pane", "hardness": 0.3, "minStateId": 6583, "maxStateId": 6614, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [353], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6614, "resistance": 0.3}, {"id": 360, "displayName": "<PERSON><PERSON> Stained Glass Pane", "name": "cyan_stained_glass_pane", "hardness": 0.3, "minStateId": 6615, "maxStateId": 6646, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [354], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6646, "resistance": 0.3}, {"id": 361, "displayName": "Purple Stained Glass Pane", "name": "purple_stained_glass_pane", "hardness": 0.3, "minStateId": 6647, "maxStateId": 6678, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [355], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6678, "resistance": 0.3}, {"id": 362, "displayName": "Blue Stained Glass Pane", "name": "blue_stained_glass_pane", "hardness": 0.3, "minStateId": 6679, "maxStateId": 6710, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [356], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6710, "resistance": 0.3}, {"id": 363, "displayName": "<PERSON> Stained Glass Pane", "name": "brown_stained_glass_pane", "hardness": 0.3, "minStateId": 6711, "maxStateId": 6742, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [357], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6742, "resistance": 0.3}, {"id": 364, "displayName": "Green Stained Glass Pane", "name": "green_stained_glass_pane", "hardness": 0.3, "minStateId": 6743, "maxStateId": 6774, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [358], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6774, "resistance": 0.3}, {"id": 365, "displayName": "Red Stained Glass Pane", "name": "red_stained_glass_pane", "hardness": 0.3, "minStateId": 6775, "maxStateId": 6806, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [359], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6806, "resistance": 0.3}, {"id": 366, "displayName": "Black Stained Glass Pane", "name": "black_stained_glass_pane", "hardness": 0.3, "minStateId": 6807, "maxStateId": 6838, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [360], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6838, "resistance": 0.3}, {"id": 367, "displayName": "Acacia Stairs", "name": "acacia_stairs", "hardness": 2, "minStateId": 6839, "maxStateId": 6918, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [319], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 6850, "resistance": 3}, {"id": 368, "displayName": "Dark Oak Stairs", "name": "dark_oak_stairs", "hardness": 2, "minStateId": 6919, "maxStateId": 6998, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [320], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 6930, "resistance": 3}, {"id": 369, "displayName": "Slime Block", "name": "slime_block", "hardness": 0, "minStateId": 6999, "maxStateId": 6999, "states": [], "drops": [321], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 6999, "resistance": 0}, {"id": 370, "displayName": "Barrier", "name": "barrier", "hardness": null, "minStateId": 7000, "maxStateId": 7000, "states": [], "drops": [], "diggable": false, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 7000, "resistance": 3600000.75}, {"id": 371, "displayName": "Iron Trapdoor", "name": "iron_trapdoor", "hardness": 5, "minStateId": 7001, "maxStateId": 7064, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [298], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7016, "resistance": 5}, {"id": 372, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "prismarine", "hardness": 1.5, "minStateId": 7065, "maxStateId": 7065, "states": [], "drops": [361], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7065, "resistance": 6}, {"id": 373, "displayName": "Prismarine <PERSON>s", "name": "prismarine_bricks", "hardness": 1.5, "minStateId": 7066, "maxStateId": 7066, "states": [], "drops": [362], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7066, "resistance": 6}, {"id": 374, "displayName": "<PERSON>", "name": "dark_prismarine", "hardness": 1.5, "minStateId": 7067, "maxStateId": 7067, "states": [], "drops": [363], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7067, "resistance": 6}, {"id": 375, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "prismarine_stairs", "hardness": 1.5, "minStateId": 7068, "maxStateId": 7147, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [364], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7079, "resistance": 6}, {"id": 376, "displayName": "Prismarine Brick Stairs", "name": "prismarine_brick_stairs", "hardness": 1.5, "minStateId": 7148, "maxStateId": 7227, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [365], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7159, "resistance": 6}, {"id": 377, "displayName": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "name": "dark_prismarine_stairs", "hardness": 1.5, "minStateId": 7228, "maxStateId": 7307, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [366], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7239, "resistance": 6}, {"id": 378, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "prismarine_slab", "hardness": 1.5, "minStateId": 7308, "maxStateId": 7313, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [134], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7311, "resistance": 6}, {"id": 379, "displayName": "Prismarine Brick Slab", "name": "prismarine_brick_slab", "hardness": 1.5, "minStateId": 7314, "maxStateId": 7319, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [135], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7317, "resistance": 6}, {"id": 380, "displayName": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "name": "dark_prismarine_slab", "hardness": 1.5, "minStateId": 7320, "maxStateId": 7325, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [136], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7323, "resistance": 6}, {"id": 381, "displayName": "Sea Lantern", "name": "sea_lantern", "hardness": 0.3, "minStateId": 7326, "maxStateId": 7326, "states": [], "drops": [367, 786], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 15, "boundingBox": "block", "stackSize": 64, "defaultState": 7326, "resistance": 0.3}, {"id": 382, "displayName": "<PERSON>", "name": "hay_block", "hardness": 0.5, "minStateId": 7327, "maxStateId": 7329, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [299], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 7328, "resistance": 0.5}, {"id": 383, "displayName": "White Carpet", "name": "white_carpet", "hardness": 0.1, "minStateId": 7330, "maxStateId": 7330, "states": [], "drops": [300], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 7330, "resistance": 0.1}, {"id": 384, "displayName": "Orange Carpet", "name": "orange_carpet", "hardness": 0.1, "minStateId": 7331, "maxStateId": 7331, "states": [], "drops": [301], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 7331, "resistance": 0.1}, {"id": 385, "displayName": "Magenta Carpet", "name": "magenta_carpet", "hardness": 0.1, "minStateId": 7332, "maxStateId": 7332, "states": [], "drops": [302], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 7332, "resistance": 0.1}, {"id": 386, "displayName": "Light Blue Carpet", "name": "light_blue_carpet", "hardness": 0.1, "minStateId": 7333, "maxStateId": 7333, "states": [], "drops": [303], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 7333, "resistance": 0.1}, {"id": 387, "displayName": "Yellow Carpet", "name": "yellow_carpet", "hardness": 0.1, "minStateId": 7334, "maxStateId": 7334, "states": [], "drops": [304], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 7334, "resistance": 0.1}, {"id": 388, "displayName": "Lime Carpet", "name": "lime_carpet", "hardness": 0.1, "minStateId": 7335, "maxStateId": 7335, "states": [], "drops": [305], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 7335, "resistance": 0.1}, {"id": 389, "displayName": "Pink Carpet", "name": "pink_carpet", "hardness": 0.1, "minStateId": 7336, "maxStateId": 7336, "states": [], "drops": [306], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 7336, "resistance": 0.1}, {"id": 390, "displayName": "<PERSON> Carpet", "name": "gray_carpet", "hardness": 0.1, "minStateId": 7337, "maxStateId": 7337, "states": [], "drops": [307], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 7337, "resistance": 0.1}, {"id": 391, "displayName": "Light Gray Carpet", "name": "light_gray_carpet", "hardness": 0.1, "minStateId": 7338, "maxStateId": 7338, "states": [], "drops": [308], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 7338, "resistance": 0.1}, {"id": 392, "displayName": "<PERSON><PERSON>", "name": "cyan_carpet", "hardness": 0.1, "minStateId": 7339, "maxStateId": 7339, "states": [], "drops": [309], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 7339, "resistance": 0.1}, {"id": 393, "displayName": "Purple Carpet", "name": "purple_carpet", "hardness": 0.1, "minStateId": 7340, "maxStateId": 7340, "states": [], "drops": [310], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 7340, "resistance": 0.1}, {"id": 394, "displayName": "Blue Carpet", "name": "blue_carpet", "hardness": 0.1, "minStateId": 7341, "maxStateId": 7341, "states": [], "drops": [311], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 7341, "resistance": 0.1}, {"id": 395, "displayName": "Brown Carpet", "name": "brown_carpet", "hardness": 0.1, "minStateId": 7342, "maxStateId": 7342, "states": [], "drops": [312], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 7342, "resistance": 0.1}, {"id": 396, "displayName": "Green Carpet", "name": "green_carpet", "hardness": 0.1, "minStateId": 7343, "maxStateId": 7343, "states": [], "drops": [313], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 7343, "resistance": 0.1}, {"id": 397, "displayName": "Red Carpet", "name": "red_carpet", "hardness": 0.1, "minStateId": 7344, "maxStateId": 7344, "states": [], "drops": [314], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 7344, "resistance": 0.1}, {"id": 398, "displayName": "Black Carpet", "name": "black_carpet", "hardness": 0.1, "minStateId": 7345, "maxStateId": 7345, "states": [], "drops": [315], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 7345, "resistance": 0.1}, {"id": 399, "displayName": "Terracotta", "name": "terracotta", "hardness": 1.25, "minStateId": 7346, "maxStateId": 7346, "states": [], "drops": [316], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7346, "resistance": 4.2}, {"id": 400, "displayName": "Block of Coal", "name": "coal_block", "hardness": 5, "minStateId": 7347, "maxStateId": 7347, "states": [], "drops": [317], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7347, "resistance": 6}, {"id": 401, "displayName": "Packed Ice", "name": "packed_ice", "hardness": 0.5, "minStateId": 7348, "maxStateId": 7348, "states": [], "drops": [318], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "defaultState": 7348, "resistance": 0.5}, {"id": 402, "displayName": "Sunflower", "name": "sunflower", "hardness": 0, "minStateId": 7349, "maxStateId": 7350, "states": [{"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}], "drops": [323], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 7350, "resistance": 0}, {"id": 403, "displayName": "Lilac", "name": "lilac", "hardness": 0, "minStateId": 7351, "maxStateId": 7352, "states": [{"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}], "drops": [324], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 7352, "resistance": 0}, {"id": 404, "displayName": "<PERSON>", "name": "rose_bush", "hardness": 0, "minStateId": 7353, "maxStateId": 7354, "states": [{"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}], "drops": [325], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 7354, "resistance": 0}, {"id": 405, "displayName": "Peony", "name": "peony", "hardness": 0, "minStateId": 7355, "maxStateId": 7356, "states": [{"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}], "drops": [326], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7356, "resistance": 0}, {"id": 406, "displayName": "Tall Grass", "name": "tall_grass", "hardness": 0, "minStateId": 7357, "maxStateId": 7358, "states": [{"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}], "drops": [672, 76, 327, 560], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 7358, "resistance": 0}, {"id": 407, "displayName": "Large Fern", "name": "large_fern", "hardness": 0, "minStateId": 7359, "maxStateId": 7360, "states": [{"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}], "drops": [672, 77, 328, 560], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "plant", "defaultState": 7360, "resistance": 0}, {"id": 408, "displayName": "White Banner", "name": "white_banner", "hardness": 1, "minStateId": 7361, "maxStateId": 7376, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [802], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7361, "resistance": 1}, {"id": 409, "displayName": "Orange Banner", "name": "orange_banner", "hardness": 1, "minStateId": 7377, "maxStateId": 7392, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [803], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7377, "resistance": 1}, {"id": 410, "displayName": "Magenta Banner", "name": "magenta_banner", "hardness": 1, "minStateId": 7393, "maxStateId": 7408, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [804], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7393, "resistance": 1}, {"id": 411, "displayName": "Light Blue Banner", "name": "light_blue_banner", "hardness": 1, "minStateId": 7409, "maxStateId": 7424, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [805], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7409, "resistance": 1}, {"id": 412, "displayName": "Yellow Banner", "name": "yellow_banner", "hardness": 1, "minStateId": 7425, "maxStateId": 7440, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [806], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7425, "resistance": 1}, {"id": 413, "displayName": "Lime Banner", "name": "lime_banner", "hardness": 1, "minStateId": 7441, "maxStateId": 7456, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [807], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7441, "resistance": 1}, {"id": 414, "displayName": "Pink Banner", "name": "pink_banner", "hardness": 1, "minStateId": 7457, "maxStateId": 7472, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [808], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7457, "resistance": 1}, {"id": 415, "displayName": "<PERSON>", "name": "gray_banner", "hardness": 1, "minStateId": 7473, "maxStateId": 7488, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [809], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7473, "resistance": 1}, {"id": 416, "displayName": "<PERSON> Gray Banner", "name": "light_gray_banner", "hardness": 1, "minStateId": 7489, "maxStateId": 7504, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [810], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7489, "resistance": 1}, {"id": 417, "displayName": "<PERSON><PERSON>", "name": "cyan_banner", "hardness": 1, "minStateId": 7505, "maxStateId": 7520, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [811], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7505, "resistance": 1}, {"id": 418, "displayName": "<PERSON> Banner", "name": "purple_banner", "hardness": 1, "minStateId": 7521, "maxStateId": 7536, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [812], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7521, "resistance": 1}, {"id": 419, "displayName": "Blue Banner", "name": "blue_banner", "hardness": 1, "minStateId": 7537, "maxStateId": 7552, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [813], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7537, "resistance": 1}, {"id": 420, "displayName": "<PERSON>", "name": "brown_banner", "hardness": 1, "minStateId": 7553, "maxStateId": 7568, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [814], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7553, "resistance": 1}, {"id": 421, "displayName": "<PERSON> Banner", "name": "green_banner", "hardness": 1, "minStateId": 7569, "maxStateId": 7584, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [815], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7569, "resistance": 1}, {"id": 422, "displayName": "Red Banner", "name": "red_banner", "hardness": 1, "minStateId": 7585, "maxStateId": 7600, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [816], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7585, "resistance": 1}, {"id": 423, "displayName": "Black Banner", "name": "black_banner", "hardness": 1, "minStateId": 7601, "maxStateId": 7616, "states": [{"name": "rotation", "type": "int", "num_values": 16}], "drops": [817], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7601, "resistance": 1}, {"id": 424, "displayName": "White wall banner", "name": "white_wall_banner", "hardness": 1, "minStateId": 7617, "maxStateId": 7620, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7617, "resistance": 1}, {"id": 425, "displayName": "Orange wall banner", "name": "orange_wall_banner", "hardness": 1, "minStateId": 7621, "maxStateId": 7624, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7621, "resistance": 1}, {"id": 426, "displayName": "Magenta wall banner", "name": "magenta_wall_banner", "hardness": 1, "minStateId": 7625, "maxStateId": 7628, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7625, "resistance": 1}, {"id": 427, "displayName": "Light blue wall banner", "name": "light_blue_wall_banner", "hardness": 1, "minStateId": 7629, "maxStateId": 7632, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7629, "resistance": 1}, {"id": 428, "displayName": "Yellow wall banner", "name": "yellow_wall_banner", "hardness": 1, "minStateId": 7633, "maxStateId": 7636, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7633, "resistance": 1}, {"id": 429, "displayName": "Lime wall banner", "name": "lime_wall_banner", "hardness": 1, "minStateId": 7637, "maxStateId": 7640, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7637, "resistance": 1}, {"id": 430, "displayName": "Pink wall banner", "name": "pink_wall_banner", "hardness": 1, "minStateId": 7641, "maxStateId": 7644, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7641, "resistance": 1}, {"id": 431, "displayName": "Gray wall banner", "name": "gray_wall_banner", "hardness": 1, "minStateId": 7645, "maxStateId": 7648, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7645, "resistance": 1}, {"id": 432, "displayName": "Light gray wall banner", "name": "light_gray_wall_banner", "hardness": 1, "minStateId": 7649, "maxStateId": 7652, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7649, "resistance": 1}, {"id": 433, "displayName": "<PERSON>an wall banner", "name": "cyan_wall_banner", "hardness": 1, "minStateId": 7653, "maxStateId": 7656, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7653, "resistance": 1}, {"id": 434, "displayName": "Purple wall banner", "name": "purple_wall_banner", "hardness": 1, "minStateId": 7657, "maxStateId": 7660, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7657, "resistance": 1}, {"id": 435, "displayName": "Blue wall banner", "name": "blue_wall_banner", "hardness": 1, "minStateId": 7661, "maxStateId": 7664, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7661, "resistance": 1}, {"id": 436, "displayName": "<PERSON> wall banner", "name": "brown_wall_banner", "hardness": 1, "minStateId": 7665, "maxStateId": 7668, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7665, "resistance": 1}, {"id": 437, "displayName": "Green wall banner", "name": "green_wall_banner", "hardness": 1, "minStateId": 7669, "maxStateId": 7672, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7669, "resistance": 1}, {"id": 438, "displayName": "Red wall banner", "name": "red_wall_banner", "hardness": 1, "minStateId": 7673, "maxStateId": 7676, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7673, "resistance": 1}, {"id": 439, "displayName": "Black wall banner", "name": "black_wall_banner", "hardness": 1, "minStateId": 7677, "maxStateId": 7680, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 16, "material": "wood", "defaultState": 7677, "resistance": 1}, {"id": 440, "displayName": "Red Sandstone", "name": "red_sandstone", "hardness": 0.8, "minStateId": 7681, "maxStateId": 7681, "states": [], "drops": [368], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7681, "resistance": 0.8}, {"id": 441, "displayName": "Chiseled Red Sandstone", "name": "chiseled_red_sandstone", "hardness": 0.8, "minStateId": 7682, "maxStateId": 7682, "states": [], "drops": [369], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7682, "resistance": 0.8}, {"id": 442, "displayName": "Cut Red Sandstone", "name": "cut_red_sandstone", "hardness": 0.8, "minStateId": 7683, "maxStateId": 7683, "states": [], "drops": [370], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7683, "resistance": 0.8}, {"id": 443, "displayName": "Red Sandstone Stairs", "name": "red_sandstone_stairs", "hardness": 0.8, "minStateId": 7684, "maxStateId": 7763, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [371], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7695, "resistance": 0.8}, {"id": 444, "displayName": "Oak Slab", "name": "oak_slab", "hardness": 2, "minStateId": 7764, "maxStateId": 7769, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [115], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7767, "resistance": 3}, {"id": 445, "displayName": "Spruce Slab", "name": "spruce_slab", "hardness": 2, "minStateId": 7770, "maxStateId": 7775, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [116], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7773, "resistance": 3}, {"id": 446, "displayName": "<PERSON>", "name": "birch_slab", "hardness": 2, "minStateId": 7776, "maxStateId": 7781, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [117], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7779, "resistance": 3}, {"id": 447, "displayName": "Jungle Slab", "name": "jungle_slab", "hardness": 2, "minStateId": 7782, "maxStateId": 7787, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [118], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7785, "resistance": 3}, {"id": 448, "displayName": "Acacia <PERSON>b", "name": "acacia_slab", "hardness": 2, "minStateId": 7788, "maxStateId": 7793, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [119], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7791, "resistance": 3}, {"id": 449, "displayName": "Dark Oak Slab", "name": "dark_oak_slab", "hardness": 2, "minStateId": 7794, "maxStateId": 7799, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [120], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7797, "resistance": 3}, {"id": 450, "displayName": "<PERSON> Slab", "name": "stone_slab", "hardness": 2, "minStateId": 7800, "maxStateId": 7805, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [121], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7803, "resistance": 6}, {"id": 451, "displayName": "Smooth Stone Slab", "name": "smooth_stone_slab", "hardness": 2, "minStateId": 7806, "maxStateId": 7811, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [122], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7809, "resistance": 6}, {"id": 452, "displayName": "Sandstone Slab", "name": "sandstone_slab", "hardness": 2, "minStateId": 7812, "maxStateId": 7817, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [123], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7815, "resistance": 6}, {"id": 453, "displayName": "Cut Sandstone Slab", "name": "cut_sandstone_slab", "hardness": 2, "minStateId": 7818, "maxStateId": 7823, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [124], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7821, "resistance": 6}, {"id": 454, "displayName": "Petrified Oak Slab", "name": "petrified_oak_slab", "hardness": 2, "minStateId": 7824, "maxStateId": 7829, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [125], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7827, "resistance": 6}, {"id": 455, "displayName": "Cobblestone Slab", "name": "cobblestone_slab", "hardness": 2, "minStateId": 7830, "maxStateId": 7835, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [126], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7833, "resistance": 6}, {"id": 456, "displayName": "Brick Slab", "name": "brick_slab", "hardness": 2, "minStateId": 7836, "maxStateId": 7841, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [127], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7839, "resistance": 6}, {"id": 457, "displayName": "Stone Brick Slab", "name": "stone_brick_slab", "hardness": 2, "minStateId": 7842, "maxStateId": 7847, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [128], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7845, "resistance": 6}, {"id": 458, "displayName": "Nether Brick Slab", "name": "nether_brick_slab", "hardness": 2, "minStateId": 7848, "maxStateId": 7853, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [129], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7851, "resistance": 6}, {"id": 459, "displayName": "Quartz Slab", "name": "quartz_slab", "hardness": 2, "minStateId": 7854, "maxStateId": 7859, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [130], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7857, "resistance": 6}, {"id": 460, "displayName": "Red Sandstone Slab", "name": "red_sandstone_slab", "hardness": 2, "minStateId": 7860, "maxStateId": 7865, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [131], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7863, "resistance": 6}, {"id": 461, "displayName": "Cut Red Sandstone Slab", "name": "cut_red_sandstone_slab", "hardness": 2, "minStateId": 7866, "maxStateId": 7871, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [132], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7869, "resistance": 6}, {"id": 462, "displayName": "Purpur Slab", "name": "purpur_slab", "hardness": 2, "minStateId": 7872, "maxStateId": 7877, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [133], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7875, "resistance": 6}, {"id": 463, "displayName": "Smooth Stone", "name": "smooth_stone", "hardness": 2, "minStateId": 7878, "maxStateId": 7878, "states": [], "drops": [140], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7878, "resistance": 6}, {"id": 464, "displayName": "Smooth Sandstone", "name": "smooth_sandstone", "hardness": 2, "minStateId": 7879, "maxStateId": 7879, "states": [], "drops": [139], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7879, "resistance": 6}, {"id": 465, "displayName": "Smooth Quartz", "name": "smooth_quartz", "hardness": 2, "minStateId": 7880, "maxStateId": 7880, "states": [], "drops": [137], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7880, "resistance": 6}, {"id": 466, "displayName": "Smooth Red Sandstone", "name": "smooth_red_sandstone", "hardness": 2, "minStateId": 7881, "maxStateId": 7881, "states": [], "drops": [138], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 7881, "resistance": 6}, {"id": 467, "displayName": "Spruce Fence Gate", "name": "spruce_fence_gate", "hardness": 2, "minStateId": 7882, "maxStateId": 7913, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "in_wall", "type": "bool", "num_values": 2}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [217], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 7889, "resistance": 3}, {"id": 468, "displayName": "Birch Fence Gate", "name": "birch_fence_gate", "hardness": 2, "minStateId": 7914, "maxStateId": 7945, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "in_wall", "type": "bool", "num_values": 2}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [218], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 7921, "resistance": 3}, {"id": 469, "displayName": "Jungle Fence Gate", "name": "jungle_fence_gate", "hardness": 2, "minStateId": 7946, "maxStateId": 7977, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "in_wall", "type": "bool", "num_values": 2}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [219], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 7953, "resistance": 3}, {"id": 470, "displayName": "Acacia Fence Gate", "name": "acacia_fence_gate", "hardness": 2, "minStateId": 7978, "maxStateId": 8009, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "in_wall", "type": "bool", "num_values": 2}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [220], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 7985, "resistance": 3}, {"id": 471, "displayName": "Dark Oak Fence Gate", "name": "dark_oak_fence_gate", "hardness": 2, "minStateId": 8010, "maxStateId": 8041, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "in_wall", "type": "bool", "num_values": 2}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [221], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 8017, "resistance": 3}, {"id": 472, "displayName": "Spruce Fence", "name": "spruce_fence", "hardness": 2, "minStateId": 8042, "maxStateId": 8073, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [182], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 8073, "resistance": 3}, {"id": 473, "displayName": "<PERSON>", "name": "birch_fence", "hardness": 2, "minStateId": 8074, "maxStateId": 8105, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [183], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 8105, "resistance": 3}, {"id": 474, "displayName": "Jungle Fence", "name": "jungle_fence", "hardness": 2, "minStateId": 8106, "maxStateId": 8137, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [184], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 8137, "resistance": 3}, {"id": 475, "displayName": "Acacia Fence", "name": "acacia_fence", "hardness": 2, "minStateId": 8138, "maxStateId": 8169, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [185], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 8169, "resistance": 3}, {"id": 476, "displayName": "Dark Oak Fence", "name": "dark_oak_fence", "hardness": 2, "minStateId": 8170, "maxStateId": 8201, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [186], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 8201, "resistance": 3}, {"id": 477, "displayName": "Spruce Door", "name": "spruce_door", "hardness": 3, "minStateId": 8202, "maxStateId": 8265, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}, {"name": "hinge", "type": "enum", "num_values": 2, "values": ["left", "right"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [508], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 8213, "resistance": 3}, {"id": 478, "displayName": "<PERSON>", "name": "birch_door", "hardness": 3, "minStateId": 8266, "maxStateId": 8329, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}, {"name": "hinge", "type": "enum", "num_values": 2, "values": ["left", "right"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [509], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 8277, "resistance": 3}, {"id": 479, "displayName": "Jungle Door", "name": "jungle_door", "hardness": 3, "minStateId": 8330, "maxStateId": 8393, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}, {"name": "hinge", "type": "enum", "num_values": 2, "values": ["left", "right"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [510], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 8341, "resistance": 3}, {"id": 480, "displayName": "Acacia Door", "name": "acacia_door", "hardness": 3, "minStateId": 8394, "maxStateId": 8457, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}, {"name": "hinge", "type": "enum", "num_values": 2, "values": ["left", "right"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [511], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 8405, "resistance": 3}, {"id": 481, "displayName": "Dark Oak Door", "name": "dark_oak_door", "hardness": 3, "minStateId": 8458, "maxStateId": 8521, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["upper", "lower"]}, {"name": "hinge", "type": "enum", "num_values": 2, "values": ["left", "right"]}, {"name": "open", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [512], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 8469, "resistance": 3}, {"id": 482, "displayName": "End Rod", "name": "end_rod", "hardness": 0, "minStateId": 8522, "maxStateId": 8527, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [147], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 14, "boundingBox": "block", "stackSize": 64, "defaultState": 8526, "resistance": 0}, {"id": 483, "displayName": "Chorus Plant", "name": "chorus_plant", "hardness": 0.4, "minStateId": 8528, "maxStateId": 8591, "states": [{"name": "down", "type": "bool", "num_values": 2}, {"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [819], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 8591, "resistance": 0.4}, {"id": 484, "displayName": "Chorus Flower", "name": "chorus_flower", "hardness": 0.4, "minStateId": 8592, "maxStateId": 8597, "states": [{"name": "age", "type": "int", "num_values": 6}], "drops": [149], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 8592, "resistance": 0.4}, {"id": 485, "displayName": "Purpur Block", "name": "purpur_block", "hardness": 1.5, "minStateId": 8598, "maxStateId": 8598, "states": [], "drops": [150], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8598, "resistance": 6}, {"id": 486, "displayName": "Purpur Pillar", "name": "purpur_pillar", "hardness": 1.5, "minStateId": 8599, "maxStateId": 8601, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [151], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8600, "resistance": 6}, {"id": 487, "displayName": "Purpur Stairs", "name": "purpur_stairs", "hardness": 1.5, "minStateId": 8602, "maxStateId": 8681, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [152], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8613, "resistance": 6}, {"id": 488, "displayName": "End Stone Bricks", "name": "end_stone_bricks", "hardness": 3, "minStateId": 8682, "maxStateId": 8682, "states": [], "drops": [232], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8682, "resistance": 9}, {"id": 489, "displayName": "Beetroots", "name": "beetroots", "hardness": 0, "minStateId": 8683, "maxStateId": 8686, "states": [{"name": "age", "type": "int", "num_values": 4}], "drops": [821, 822], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8683, "resistance": 0}, {"id": 490, "displayName": "Grass Path", "name": "grass_path", "hardness": 0.65, "minStateId": 8687, "maxStateId": 8687, "states": [], "drops": [9], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8687, "resistance": 0.65}, {"id": 491, "displayName": "End Gateway", "name": "end_gateway", "hardness": null, "minStateId": 8688, "maxStateId": 8688, "states": [], "drops": [], "diggable": false, "transparent": false, "filterLight": 15, "emitLight": 15, "boundingBox": "empty", "stackSize": 0, "defaultState": 8688, "resistance": 3600000}, {"id": 492, "displayName": "Repeating Command Block", "name": "repeating_command_block", "hardness": null, "minStateId": 8689, "maxStateId": 8700, "states": [{"name": "conditional", "type": "bool", "num_values": 2}, {"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [], "diggable": false, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 8695, "resistance": 3600000}, {"id": 493, "displayName": "Chain Command Block", "name": "chain_command_block", "hardness": null, "minStateId": 8701, "maxStateId": 8712, "states": [{"name": "conditional", "type": "bool", "num_values": 2}, {"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [], "diggable": false, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 8707, "resistance": 3600000}, {"id": 494, "displayName": "Frosted Ice", "name": "frosted_ice", "hardness": 0.5, "minStateId": 8713, "maxStateId": 8716, "states": [{"name": "age", "type": "int", "num_values": 4}], "drops": [], "diggable": true, "transparent": true, "filterLight": 2, "emitLight": 0, "boundingBox": "block", "stackSize": 0, "defaultState": 8713, "resistance": 0.5}, {"id": 495, "displayName": "Magma Block", "name": "magma_block", "hardness": 0.5, "minStateId": 8717, "maxStateId": 8717, "states": [], "drops": [374], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8717, "resistance": 0.5}, {"id": 496, "displayName": "Nether Wart Block", "name": "nether_wart_block", "hardness": 1, "minStateId": 8718, "maxStateId": 8718, "states": [], "drops": [375], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 8718, "resistance": 1}, {"id": 497, "displayName": "Red Nether Bricks", "name": "red_nether_bricks", "hardness": 2, "minStateId": 8719, "maxStateId": 8719, "states": [], "drops": [376], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8719, "resistance": 6}, {"id": 498, "displayName": "Bone Block", "name": "bone_block", "hardness": 2, "minStateId": 8720, "maxStateId": 8722, "states": [{"name": "axis", "type": "enum", "num_values": 3, "values": ["x", "y", "z"]}], "drops": [377], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8721, "resistance": 2}, {"id": 499, "displayName": "Structure Void", "name": "structure_void", "hardness": 0, "minStateId": 8723, "maxStateId": 8723, "states": [], "drops": [], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8723, "resistance": 0}, {"id": 500, "displayName": "Observer", "name": "observer", "hardness": 3, "minStateId": 8724, "maxStateId": 8735, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [379], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8729, "resistance": 3}, {"id": 501, "displayName": "Shulker Box", "name": "shulker_box", "hardness": 2, "minStateId": 8736, "maxStateId": 8741, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [380], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8740, "resistance": 2}, {"id": 502, "displayName": "White Shulker Box", "name": "white_shulker_box", "hardness": 2, "minStateId": 8742, "maxStateId": 8747, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [381], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8746, "resistance": 2}, {"id": 503, "displayName": "Orange Shulker Box", "name": "orange_shulker_box", "hardness": 2, "minStateId": 8748, "maxStateId": 8753, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [382], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8752, "resistance": 2}, {"id": 504, "displayName": "<PERSON><PERSON>a <PERSON>er Box", "name": "magenta_shulker_box", "hardness": 2, "minStateId": 8754, "maxStateId": 8759, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [383], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8758, "resistance": 2}, {"id": 505, "displayName": "Light Blue Shulker Box", "name": "light_blue_shulker_box", "hardness": 2, "minStateId": 8760, "maxStateId": 8765, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [384], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8764, "resistance": 2}, {"id": 506, "displayName": "Yellow Shulker Box", "name": "yellow_shulker_box", "hardness": 2, "minStateId": 8766, "maxStateId": 8771, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [385], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8770, "resistance": 2}, {"id": 507, "displayName": "<PERSON>e <PERSON>er Box", "name": "lime_shulker_box", "hardness": 2, "minStateId": 8772, "maxStateId": 8777, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [386], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8776, "resistance": 2}, {"id": 508, "displayName": "Pink Shulker Box", "name": "pink_shulker_box", "hardness": 2, "minStateId": 8778, "maxStateId": 8783, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [387], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8782, "resistance": 2}, {"id": 509, "displayName": "<PERSON>", "name": "gray_shulker_box", "hardness": 2, "minStateId": 8784, "maxStateId": 8789, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [388], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8788, "resistance": 2}, {"id": 510, "displayName": "Light Gray Shulker Box", "name": "light_gray_shulker_box", "hardness": 2, "minStateId": 8790, "maxStateId": 8795, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [389], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8794, "resistance": 2}, {"id": 511, "displayName": "<PERSON><PERSON>", "name": "cyan_shulker_box", "hardness": 2, "minStateId": 8796, "maxStateId": 8801, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [390], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8800, "resistance": 2}, {"id": 512, "displayName": "Purple Shulker Box", "name": "purple_shulker_box", "hardness": 2, "minStateId": 8802, "maxStateId": 8807, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [391], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8806, "resistance": 2}, {"id": 513, "displayName": "Blue Shulker Box", "name": "blue_shulker_box", "hardness": 2, "minStateId": 8808, "maxStateId": 8813, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [392], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8812, "resistance": 2}, {"id": 514, "displayName": "<PERSON> Shulker Box", "name": "brown_shulker_box", "hardness": 2, "minStateId": 8814, "maxStateId": 8819, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [393], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8818, "resistance": 2}, {"id": 515, "displayName": "Green Shulker Box", "name": "green_shulker_box", "hardness": 2, "minStateId": 8820, "maxStateId": 8825, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [394], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8824, "resistance": 2}, {"id": 516, "displayName": "Red Shulker Box", "name": "red_shulker_box", "hardness": 2, "minStateId": 8826, "maxStateId": 8831, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [395], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8830, "resistance": 2}, {"id": 517, "displayName": "Black Shulker Box", "name": "black_shulker_box", "hardness": 2, "minStateId": 8832, "maxStateId": 8837, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [396], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "defaultState": 8836, "resistance": 2}, {"id": 518, "displayName": "White Glazed Terracotta", "name": "white_glazed_terracotta", "hardness": 1.4, "minStateId": 8838, "maxStateId": 8841, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [397], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8838, "resistance": 1.4}, {"id": 519, "displayName": "Orange Glazed Terracotta", "name": "orange_glazed_terracotta", "hardness": 1.4, "minStateId": 8842, "maxStateId": 8845, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [398], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8842, "resistance": 1.4}, {"id": 520, "displayName": "Magenta Glazed Terracotta", "name": "magenta_glazed_terracotta", "hardness": 1.4, "minStateId": 8846, "maxStateId": 8849, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [399], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8846, "resistance": 1.4}, {"id": 521, "displayName": "Light Blue Glazed Terracotta", "name": "light_blue_glazed_terracotta", "hardness": 1.4, "minStateId": 8850, "maxStateId": 8853, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [400], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8850, "resistance": 1.4}, {"id": 522, "displayName": "Yellow Glazed Terracotta", "name": "yellow_glazed_terracotta", "hardness": 1.4, "minStateId": 8854, "maxStateId": 8857, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [401], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8854, "resistance": 1.4}, {"id": 523, "displayName": "Lime Glazed Terracotta", "name": "lime_glazed_terracotta", "hardness": 1.4, "minStateId": 8858, "maxStateId": 8861, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [402], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8858, "resistance": 1.4}, {"id": 524, "displayName": "Pink Glazed Terracotta", "name": "pink_glazed_terracotta", "hardness": 1.4, "minStateId": 8862, "maxStateId": 8865, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [403], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8862, "resistance": 1.4}, {"id": 525, "displayName": "Gray Glazed Terracotta", "name": "gray_glazed_terracotta", "hardness": 1.4, "minStateId": 8866, "maxStateId": 8869, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [404], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8866, "resistance": 1.4}, {"id": 526, "displayName": "Light Gray Glazed Terracotta", "name": "light_gray_glazed_terracotta", "hardness": 1.4, "minStateId": 8870, "maxStateId": 8873, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [405], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8870, "resistance": 1.4}, {"id": 527, "displayName": "<PERSON><PERSON>zed Terracotta", "name": "cyan_glazed_terracotta", "hardness": 1.4, "minStateId": 8874, "maxStateId": 8877, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [406], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8874, "resistance": 1.4}, {"id": 528, "displayName": "Purple Glazed Terracotta", "name": "purple_glazed_terracotta", "hardness": 1.4, "minStateId": 8878, "maxStateId": 8881, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [407], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8878, "resistance": 1.4}, {"id": 529, "displayName": "Blue Glazed Terracotta", "name": "blue_glazed_terracotta", "hardness": 1.4, "minStateId": 8882, "maxStateId": 8885, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [408], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8882, "resistance": 1.4}, {"id": 530, "displayName": "Brown Glazed Terracotta", "name": "brown_glazed_terracotta", "hardness": 1.4, "minStateId": 8886, "maxStateId": 8889, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [409], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8886, "resistance": 1.4}, {"id": 531, "displayName": "Green Glazed Terracotta", "name": "green_glazed_terracotta", "hardness": 1.4, "minStateId": 8890, "maxStateId": 8893, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [410], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8890, "resistance": 1.4}, {"id": 532, "displayName": "Red Glazed Terracotta", "name": "red_glazed_terracotta", "hardness": 1.4, "minStateId": 8894, "maxStateId": 8897, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [411], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8894, "resistance": 1.4}, {"id": 533, "displayName": "Black Glazed Terracotta", "name": "black_glazed_terracotta", "hardness": 1.4, "minStateId": 8898, "maxStateId": 8901, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [412], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8898, "resistance": 1.4}, {"id": 534, "displayName": "White Concrete", "name": "white_concrete", "hardness": 1.8, "minStateId": 8902, "maxStateId": 8902, "states": [], "drops": [413], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8902, "resistance": 1.8}, {"id": 535, "displayName": "Orange Concrete", "name": "orange_concrete", "hardness": 1.8, "minStateId": 8903, "maxStateId": 8903, "states": [], "drops": [414], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8903, "resistance": 1.8}, {"id": 536, "displayName": "Magenta Concrete", "name": "magenta_concrete", "hardness": 1.8, "minStateId": 8904, "maxStateId": 8904, "states": [], "drops": [415], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8904, "resistance": 1.8}, {"id": 537, "displayName": "Light Blue Concrete", "name": "light_blue_concrete", "hardness": 1.8, "minStateId": 8905, "maxStateId": 8905, "states": [], "drops": [416], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8905, "resistance": 1.8}, {"id": 538, "displayName": "Yellow Concrete", "name": "yellow_concrete", "hardness": 1.8, "minStateId": 8906, "maxStateId": 8906, "states": [], "drops": [417], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8906, "resistance": 1.8}, {"id": 539, "displayName": "Lime Concrete", "name": "lime_concrete", "hardness": 1.8, "minStateId": 8907, "maxStateId": 8907, "states": [], "drops": [418], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8907, "resistance": 1.8}, {"id": 540, "displayName": "Pink Concrete", "name": "pink_concrete", "hardness": 1.8, "minStateId": 8908, "maxStateId": 8908, "states": [], "drops": [419], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8908, "resistance": 1.8}, {"id": 541, "displayName": "<PERSON>", "name": "gray_concrete", "hardness": 1.8, "minStateId": 8909, "maxStateId": 8909, "states": [], "drops": [420], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8909, "resistance": 1.8}, {"id": 542, "displayName": "Light Gray Concrete", "name": "light_gray_concrete", "hardness": 1.8, "minStateId": 8910, "maxStateId": 8910, "states": [], "drops": [421], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8910, "resistance": 1.8}, {"id": 543, "displayName": "<PERSON><PERSON>", "name": "cyan_concrete", "hardness": 1.8, "minStateId": 8911, "maxStateId": 8911, "states": [], "drops": [422], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8911, "resistance": 1.8}, {"id": 544, "displayName": "Purple Concrete", "name": "purple_concrete", "hardness": 1.8, "minStateId": 8912, "maxStateId": 8912, "states": [], "drops": [423], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8912, "resistance": 1.8}, {"id": 545, "displayName": "Blue Concrete", "name": "blue_concrete", "hardness": 1.8, "minStateId": 8913, "maxStateId": 8913, "states": [], "drops": [424], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8913, "resistance": 1.8}, {"id": 546, "displayName": "<PERSON> Concrete", "name": "brown_concrete", "hardness": 1.8, "minStateId": 8914, "maxStateId": 8914, "states": [], "drops": [425], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8914, "resistance": 1.8}, {"id": 547, "displayName": "Green Concrete", "name": "green_concrete", "hardness": 1.8, "minStateId": 8915, "maxStateId": 8915, "states": [], "drops": [426], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8915, "resistance": 1.8}, {"id": 548, "displayName": "Red Concrete", "name": "red_concrete", "hardness": 1.8, "minStateId": 8916, "maxStateId": 8916, "states": [], "drops": [427], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8916, "resistance": 1.8}, {"id": 549, "displayName": "Black Concrete", "name": "black_concrete", "hardness": 1.8, "minStateId": 8917, "maxStateId": 8917, "states": [], "drops": [428], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8917, "resistance": 1.8}, {"id": 550, "displayName": "White Concrete Powder", "name": "white_concrete_powder", "hardness": 0.5, "minStateId": 8918, "maxStateId": 8918, "states": [], "drops": [429], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8918, "resistance": 0.5}, {"id": 551, "displayName": "Orange Concrete Powder", "name": "orange_concrete_powder", "hardness": 0.5, "minStateId": 8919, "maxStateId": 8919, "states": [], "drops": [430], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8919, "resistance": 0.5}, {"id": 552, "displayName": "Magenta Concrete Powder", "name": "magenta_concrete_powder", "hardness": 0.5, "minStateId": 8920, "maxStateId": 8920, "states": [], "drops": [431], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8920, "resistance": 0.5}, {"id": 553, "displayName": "Light Blue Concrete Powder", "name": "light_blue_concrete_powder", "hardness": 0.5, "minStateId": 8921, "maxStateId": 8921, "states": [], "drops": [432], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8921, "resistance": 0.5}, {"id": 554, "displayName": "Yellow Concrete Powder", "name": "yellow_concrete_powder", "hardness": 0.5, "minStateId": 8922, "maxStateId": 8922, "states": [], "drops": [433], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8922, "resistance": 0.5}, {"id": 555, "displayName": "Lime Concrete <PERSON>", "name": "lime_concrete_powder", "hardness": 0.5, "minStateId": 8923, "maxStateId": 8923, "states": [], "drops": [434], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8923, "resistance": 0.5}, {"id": 556, "displayName": "Pink Concrete Powder", "name": "pink_concrete_powder", "hardness": 0.5, "minStateId": 8924, "maxStateId": 8924, "states": [], "drops": [435], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8924, "resistance": 0.5}, {"id": 557, "displayName": "<PERSON> Concre<PERSON>", "name": "gray_concrete_powder", "hardness": 0.5, "minStateId": 8925, "maxStateId": 8925, "states": [], "drops": [436], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8925, "resistance": 0.5}, {"id": 558, "displayName": "Light Gray Concrete Powder", "name": "light_gray_concrete_powder", "hardness": 0.5, "minStateId": 8926, "maxStateId": 8926, "states": [], "drops": [437], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8926, "resistance": 0.5}, {"id": 559, "displayName": "<PERSON><PERSON>", "name": "cyan_concrete_powder", "hardness": 0.5, "minStateId": 8927, "maxStateId": 8927, "states": [], "drops": [438], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8927, "resistance": 0.5}, {"id": 560, "displayName": "Purple Concrete Powder", "name": "purple_concrete_powder", "hardness": 0.5, "minStateId": 8928, "maxStateId": 8928, "states": [], "drops": [439], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8928, "resistance": 0.5}, {"id": 561, "displayName": "Blue Concrete Powder", "name": "blue_concrete_powder", "hardness": 0.5, "minStateId": 8929, "maxStateId": 8929, "states": [], "drops": [440], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8929, "resistance": 0.5}, {"id": 562, "displayName": "<PERSON> Concrete <PERSON>", "name": "brown_concrete_powder", "hardness": 0.5, "minStateId": 8930, "maxStateId": 8930, "states": [], "drops": [441], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8930, "resistance": 0.5}, {"id": 563, "displayName": "Green Concrete Powder", "name": "green_concrete_powder", "hardness": 0.5, "minStateId": 8931, "maxStateId": 8931, "states": [], "drops": [442], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8931, "resistance": 0.5}, {"id": 564, "displayName": "Red Concrete Powder", "name": "red_concrete_powder", "hardness": 0.5, "minStateId": 8932, "maxStateId": 8932, "states": [], "drops": [443], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8932, "resistance": 0.5}, {"id": 565, "displayName": "Black Concrete Powder", "name": "black_concrete_powder", "hardness": 0.5, "minStateId": 8933, "maxStateId": 8933, "states": [], "drops": [444], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "dirt", "defaultState": 8933, "resistance": 0.5}, {"id": 566, "displayName": "<PERSON><PERSON><PERSON>", "name": "kelp", "hardness": 0, "minStateId": 8934, "maxStateId": 8959, "states": [{"name": "age", "type": "int", "num_values": 26}], "drops": [612], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8934, "resistance": 0}, {"id": 567, "displayName": "Kelp Plant", "name": "kelp_plant", "hardness": 0, "minStateId": 8960, "maxStateId": 8960, "states": [], "drops": [612], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8960, "resistance": 0}, {"id": 568, "displayName": "Dried Kelp Block", "name": "dried_kelp_block", "hardness": 0.5, "minStateId": 8961, "maxStateId": 8961, "states": [], "drops": [613], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 8961, "resistance": 2.5}, {"id": 569, "displayName": "Turtle Egg", "name": "turtle_egg", "hardness": 0.5, "minStateId": 8962, "maxStateId": 8973, "states": [{"name": "eggs", "type": "enum", "num_values": 4, "values": ["1", "2", "3", "4"]}, {"name": "hatch", "type": "int", "num_values": 3}], "drops": [445], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 8962, "resistance": 0.5}, {"id": 570, "displayName": "Dead Tube Coral Block", "name": "dead_tube_coral_block", "hardness": 1.5, "minStateId": 8974, "maxStateId": 8974, "states": [], "drops": [446], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8974, "resistance": 6}, {"id": 571, "displayName": "Dead Brain Coral Block", "name": "dead_brain_coral_block", "hardness": 1.5, "minStateId": 8975, "maxStateId": 8975, "states": [], "drops": [447], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8975, "resistance": 6}, {"id": 572, "displayName": "Dead Bubble Coral Block", "name": "dead_bubble_coral_block", "hardness": 1.5, "minStateId": 8976, "maxStateId": 8976, "states": [], "drops": [448], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8976, "resistance": 6}, {"id": 573, "displayName": "Dead Fire Coral Block", "name": "dead_fire_coral_block", "hardness": 1.5, "minStateId": 8977, "maxStateId": 8977, "states": [], "drops": [449], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8977, "resistance": 6}, {"id": 574, "displayName": "Dead Horn Coral Block", "name": "dead_horn_coral_block", "hardness": 1.5, "minStateId": 8978, "maxStateId": 8978, "states": [], "drops": [450], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8978, "resistance": 6}, {"id": 575, "displayName": "Tube Coral Block", "name": "tube_coral_block", "hardness": 1.5, "minStateId": 8979, "maxStateId": 8979, "states": [], "drops": [451, 446], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8979, "resistance": 6}, {"id": 576, "displayName": "Brain <PERSON>", "name": "brain_coral_block", "hardness": 1.5, "minStateId": 8980, "maxStateId": 8980, "states": [], "drops": [452, 447], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8980, "resistance": 6}, {"id": 577, "displayName": "Bubble Coral Block", "name": "bubble_coral_block", "hardness": 1.5, "minStateId": 8981, "maxStateId": 8981, "states": [], "drops": [453, 448], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8981, "resistance": 6}, {"id": 578, "displayName": "Fire Coral Block", "name": "fire_coral_block", "hardness": 1.5, "minStateId": 8982, "maxStateId": 8982, "states": [], "drops": [454, 449], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8982, "resistance": 6}, {"id": 579, "displayName": "Horn Coral Block", "name": "horn_coral_block", "hardness": 1.5, "minStateId": 8983, "maxStateId": 8983, "states": [], "drops": [455, 450], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 8983, "resistance": 6}, {"id": 580, "displayName": "Dead Tube Coral", "name": "dead_tube_coral", "hardness": 0, "minStateId": 8984, "maxStateId": 8985, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [465], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8984, "resistance": 0}, {"id": 581, "displayName": "Dead Brain Coral", "name": "dead_brain_coral", "hardness": 0, "minStateId": 8986, "maxStateId": 8987, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [461], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8986, "resistance": 0}, {"id": 582, "displayName": "Dead Bubble Coral", "name": "dead_bubble_coral", "hardness": 0, "minStateId": 8988, "maxStateId": 8989, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [462], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8988, "resistance": 0}, {"id": 583, "displayName": "Dead Fire Coral", "name": "dead_fire_coral", "hardness": 0, "minStateId": 8990, "maxStateId": 8991, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [463], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8990, "resistance": 0}, {"id": 584, "displayName": "Dead Horn Coral", "name": "dead_horn_coral", "hardness": 0, "minStateId": 8992, "maxStateId": 8993, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [464], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8992, "resistance": 0}, {"id": 585, "displayName": "Tube Coral", "name": "tube_coral", "hardness": 0, "minStateId": 8994, "maxStateId": 8995, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [456], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8994, "resistance": 0}, {"id": 586, "displayName": "Brain Coral", "name": "brain_coral", "hardness": 0, "minStateId": 8996, "maxStateId": 8997, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [457], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8996, "resistance": 0}, {"id": 587, "displayName": "Bubble Coral", "name": "bubble_coral", "hardness": 0, "minStateId": 8998, "maxStateId": 8999, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [458], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 8998, "resistance": 0}, {"id": 588, "displayName": "Fire Coral", "name": "fire_coral", "hardness": 0, "minStateId": 9000, "maxStateId": 9001, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [459], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 9000, "resistance": 0}, {"id": 589, "displayName": "Horn Coral", "name": "horn_coral", "hardness": 0, "minStateId": 9002, "maxStateId": 9003, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [460], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 9002, "resistance": 0}, {"id": 590, "displayName": "Dead Tube Coral Fan", "name": "dead_tube_coral_fan", "hardness": 0, "minStateId": 9004, "maxStateId": 9005, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [471], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 9004, "resistance": 0}, {"id": 591, "displayName": "Dead Brain Coral Fan", "name": "dead_brain_coral_fan", "hardness": 0, "minStateId": 9006, "maxStateId": 9007, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [472], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 9006, "resistance": 0}, {"id": 592, "displayName": "Dead Bubble Coral Fan", "name": "dead_bubble_coral_fan", "hardness": 0, "minStateId": 9008, "maxStateId": 9009, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [473], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 9008, "resistance": 0}, {"id": 593, "displayName": "Dead Fire Coral Fan", "name": "dead_fire_coral_fan", "hardness": 0, "minStateId": 9010, "maxStateId": 9011, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [474], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 9010, "resistance": 0}, {"id": 594, "displayName": "Dead Horn Coral Fan", "name": "dead_horn_coral_fan", "hardness": 0, "minStateId": 9012, "maxStateId": 9013, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [475], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 9012, "resistance": 0}, {"id": 595, "displayName": "Tube Coral Fan", "name": "tube_coral_fan", "hardness": 0, "minStateId": 9014, "maxStateId": 9015, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [466], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 9014, "resistance": 0}, {"id": 596, "displayName": "Brain Coral Fan", "name": "brain_coral_fan", "hardness": 0, "minStateId": 9016, "maxStateId": 9017, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [467], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 9016, "resistance": 0}, {"id": 597, "displayName": "Bubble Coral Fan", "name": "bubble_coral_fan", "hardness": 0, "minStateId": 9018, "maxStateId": 9019, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [468], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 9018, "resistance": 0}, {"id": 598, "displayName": "Fire Coral Fan", "name": "fire_coral_fan", "hardness": 0, "minStateId": 9020, "maxStateId": 9021, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [469], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 9020, "resistance": 0}, {"id": 599, "displayName": "Horn Coral Fan", "name": "horn_coral_fan", "hardness": 0, "minStateId": 9022, "maxStateId": 9023, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [470], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 9022, "resistance": 0}, {"id": 600, "displayName": "Dead Tube Coral Wall Fan", "name": "dead_tube_coral_wall_fan", "hardness": 0, "minStateId": 9024, "maxStateId": 9031, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 9024, "resistance": 0}, {"id": 601, "displayName": "Dead Brain Coral Wall Fan", "name": "dead_brain_coral_wall_fan", "hardness": 0, "minStateId": 9032, "maxStateId": 9039, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 9032, "resistance": 0}, {"id": 602, "displayName": "Dead Bubble Coral Wall Fan", "name": "dead_bubble_coral_wall_fan", "hardness": 0, "minStateId": 9040, "maxStateId": 9047, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 9040, "resistance": 0}, {"id": 603, "displayName": "Dead Fire Coral Wall Fan", "name": "dead_fire_coral_wall_fan", "hardness": 0, "minStateId": 9048, "maxStateId": 9055, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 9048, "resistance": 0}, {"id": 604, "displayName": "Dead Horn Coral Wall Fan", "name": "dead_horn_coral_wall_fan", "hardness": 0, "minStateId": 9056, "maxStateId": 9063, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 9056, "resistance": 0}, {"id": 605, "displayName": "Tube Coral Wall Fan", "name": "tube_coral_wall_fan", "hardness": 0, "minStateId": 9064, "maxStateId": 9071, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 9064, "resistance": 0}, {"id": 606, "displayName": "Brain <PERSON>", "name": "brain_coral_wall_fan", "hardness": 0, "minStateId": 9072, "maxStateId": 9079, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 9072, "resistance": 0}, {"id": 607, "displayName": "Bubble Coral Wall Fan", "name": "bubble_coral_wall_fan", "hardness": 0, "minStateId": 9080, "maxStateId": 9087, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 9080, "resistance": 0}, {"id": 608, "displayName": "Fire Coral Wall Fan", "name": "fire_coral_wall_fan", "hardness": 0, "minStateId": 9088, "maxStateId": 9095, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 9088, "resistance": 0}, {"id": 609, "displayName": "Horn <PERSON> Wall Fan", "name": "horn_coral_wall_fan", "hardness": 0, "minStateId": 9096, "maxStateId": 9103, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 9096, "resistance": 0}, {"id": 610, "displayName": "<PERSON>", "name": "sea_pickle", "hardness": 0, "minStateId": 9104, "maxStateId": 9111, "states": [{"name": "pickles", "type": "enum", "num_values": 4, "values": ["1", "2", "3", "4"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [80], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 9104, "resistance": 0}, {"id": 611, "displayName": "Blue Ice", "name": "blue_ice", "hardness": 2.8, "minStateId": 9112, "maxStateId": 9112, "states": [], "drops": [476], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 9112, "resistance": 2.8}, {"id": 612, "displayName": "Conduit", "name": "conduit", "hardness": 3, "minStateId": 9113, "maxStateId": 9114, "states": [{"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [477], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "defaultState": 9113, "resistance": 3}, {"id": 613, "displayName": "Bamboo Sapling", "name": "bamboo_sapling", "hardness": 1, "minStateId": 9115, "maxStateId": 9115, "states": [], "drops": [614], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "material": "UNKNOWN_MATERIAL", "defaultState": 9115, "resistance": 1}, {"id": 614, "displayName": "Bamboo", "name": "bamboo", "hardness": 1, "minStateId": 9116, "maxStateId": 9127, "states": [{"name": "age", "type": "int", "num_values": 2}, {"name": "leaves", "type": "enum", "num_values": 3, "values": ["none", "small", "large"]}, {"name": "stage", "type": "int", "num_values": 2}], "drops": [614], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "UNKNOWN_MATERIAL", "defaultState": 9116, "resistance": 1}, {"id": 615, "displayName": "Potted Bamboo", "name": "potted_bamboo", "hardness": 0, "minStateId": 9128, "maxStateId": 9128, "states": [], "drops": [762, 614], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 9128, "resistance": 0}, {"id": 616, "displayName": "Void Air", "name": "void_air", "hardness": 0, "minStateId": 9129, "maxStateId": 9129, "states": [], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 0, "defaultState": 9129, "resistance": 0}, {"id": 617, "displayName": "Cave Air", "name": "cave_air", "hardness": 0, "minStateId": 9130, "maxStateId": 9130, "states": [], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 0, "defaultState": 9130, "resistance": 0}, {"id": 618, "displayName": "Bubble Column", "name": "bubble_column", "hardness": 0, "minStateId": 9131, "maxStateId": 9132, "states": [{"name": "drag", "type": "bool", "num_values": 2}], "drops": [], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 0, "defaultState": 9131, "resistance": 0}, {"id": 619, "displayName": "Polished Granite Stairs", "name": "polished_granite_stairs", "hardness": 1.5, "minStateId": 9133, "maxStateId": 9212, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [478], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "defaultState": 9144, "resistance": 6}, {"id": 620, "displayName": "Smooth Red Sandstone Stairs", "name": "smooth_red_sandstone_stairs", "hardness": 2, "minStateId": 9213, "maxStateId": 9292, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [479], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "defaultState": 9224, "resistance": 6}, {"id": 621, "displayName": "Mossy Stone Brick Stairs", "name": "mossy_stone_brick_stairs", "hardness": 1.5, "minStateId": 9293, "maxStateId": 9372, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [480], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "defaultState": 9304, "resistance": 6}, {"id": 622, "displayName": "Polished Diorite Stairs", "name": "polished_diorite_stairs", "hardness": 1.5, "minStateId": 9373, "maxStateId": 9452, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [481], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "defaultState": 9384, "resistance": 6}, {"id": 623, "displayName": "Mossy Cobblestone Stairs", "name": "mossy_cobblestone_stairs", "hardness": 2, "minStateId": 9453, "maxStateId": 9532, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [482], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "defaultState": 9464, "resistance": 6}, {"id": 624, "displayName": "End Stone Brick Stairs", "name": "end_stone_brick_stairs", "hardness": 3, "minStateId": 9533, "maxStateId": 9612, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [483], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "defaultState": 9544, "resistance": 9}, {"id": 625, "displayName": "Stone Stairs", "name": "stone_stairs", "hardness": 1.5, "minStateId": 9613, "maxStateId": 9692, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [484], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "defaultState": 9624, "resistance": 6}, {"id": 626, "displayName": "Smooth Sandstone Stairs", "name": "smooth_sandstone_stairs", "hardness": 2, "minStateId": 9693, "maxStateId": 9772, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [485], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "defaultState": 9704, "resistance": 6}, {"id": 627, "displayName": "Smooth Quartz Stairs", "name": "smooth_quartz_stairs", "hardness": 2, "minStateId": 9773, "maxStateId": 9852, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [486], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "defaultState": 9784, "resistance": 6}, {"id": 628, "displayName": "Granite Stairs", "name": "granite_stairs", "hardness": 1.5, "minStateId": 9853, "maxStateId": 9932, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [487], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "defaultState": 9864, "resistance": 6}, {"id": 629, "displayName": "Andesite Stairs", "name": "andesite_stairs", "hardness": 1.5, "minStateId": 9933, "maxStateId": 10012, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [488], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "defaultState": 9944, "resistance": 6}, {"id": 630, "displayName": "Red Nether Brick Stairs", "name": "red_nether_brick_stairs", "hardness": 2, "minStateId": 10013, "maxStateId": 10092, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [489], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10024, "resistance": 6}, {"id": 631, "displayName": "Polished Andesite Stairs", "name": "polished_andesite_stairs", "hardness": 1.5, "minStateId": 10093, "maxStateId": 10172, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [490], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "defaultState": 10104, "resistance": 6}, {"id": 632, "displayName": "Diorite Stairs", "name": "diorite_stairs", "hardness": 1.5, "minStateId": 10173, "maxStateId": 10252, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "half", "type": "enum", "num_values": 2, "values": ["top", "bottom"]}, {"name": "shape", "type": "enum", "num_values": 5, "values": ["straight", "inner_left", "inner_right", "outer_left", "outer_right"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [491], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "defaultState": 10184, "resistance": 6}, {"id": 633, "displayName": "Polished Granite Slab", "name": "polished_granite_slab", "hardness": 1.5, "minStateId": 10253, "maxStateId": 10258, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [492], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10256, "resistance": 6}, {"id": 634, "displayName": "Smooth Red Sandstone Slab", "name": "smooth_red_sandstone_slab", "hardness": 2, "minStateId": 10259, "maxStateId": 10264, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [493], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10262, "resistance": 6}, {"id": 635, "displayName": "Mossy Stone Brick Slab", "name": "mossy_stone_brick_slab", "hardness": 1.5, "minStateId": 10265, "maxStateId": 10270, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [494], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10268, "resistance": 6}, {"id": 636, "displayName": "Polished Diorite S<PERSON>b", "name": "polished_diorite_slab", "hardness": 1.5, "minStateId": 10271, "maxStateId": 10276, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [495], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10274, "resistance": 6}, {"id": 637, "displayName": "<PERSON><PERSON> Slab", "name": "mossy_cobblestone_slab", "hardness": 2, "minStateId": 10277, "maxStateId": 10282, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [496], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10280, "resistance": 6}, {"id": 638, "displayName": "End Stone Brick Slab", "name": "end_stone_brick_slab", "hardness": 3, "minStateId": 10283, "maxStateId": 10288, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [497], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10286, "resistance": 9}, {"id": 639, "displayName": "Smooth Sandstone Slab", "name": "smooth_sandstone_slab", "hardness": 2, "minStateId": 10289, "maxStateId": 10294, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [498], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10292, "resistance": 6}, {"id": 640, "displayName": "Smooth Quartz Slab", "name": "smooth_quartz_slab", "hardness": 2, "minStateId": 10295, "maxStateId": 10300, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [499], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10298, "resistance": 6}, {"id": 641, "displayName": "Granite Slab", "name": "granite_slab", "hardness": 1.5, "minStateId": 10301, "maxStateId": 10306, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [500], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10304, "resistance": 6}, {"id": 642, "displayName": "Andesite Slab", "name": "andesite_slab", "hardness": 1.5, "minStateId": 10307, "maxStateId": 10312, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [501], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10310, "resistance": 6}, {"id": 643, "displayName": "Red Nether Brick Slab", "name": "red_nether_brick_slab", "hardness": 2, "minStateId": 10313, "maxStateId": 10318, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [502], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10316, "resistance": 6}, {"id": 644, "displayName": "Polished Andesite Slab", "name": "polished_andesite_slab", "hardness": 1.5, "minStateId": 10319, "maxStateId": 10324, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [503], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10322, "resistance": 6}, {"id": 645, "displayName": "Diorite Slab", "name": "diorite_slab", "hardness": 1.5, "minStateId": 10325, "maxStateId": 10330, "states": [{"name": "type", "type": "enum", "num_values": 3, "values": ["top", "bottom", "double"]}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [504], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10328, "resistance": 6}, {"id": 646, "displayName": "Brick Wall", "name": "brick_wall", "hardness": 2, "minStateId": 10331, "maxStateId": 10394, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [247], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10390, "resistance": 6}, {"id": 647, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "prismarine_wall", "hardness": 1.5, "minStateId": 10395, "maxStateId": 10458, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [248], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10454, "resistance": 6}, {"id": 648, "displayName": "Red Sandstone Wall", "name": "red_sandstone_wall", "hardness": 0.8, "minStateId": 10459, "maxStateId": 10522, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [249], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10518, "resistance": 0.8}, {"id": 649, "displayName": "Mossy Stone Brick Wall", "name": "mossy_stone_brick_wall", "hardness": 1.5, "minStateId": 10523, "maxStateId": 10586, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [250], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10582, "resistance": 6}, {"id": 650, "displayName": "Granite Wall", "name": "granite_wall", "hardness": 1.5, "minStateId": 10587, "maxStateId": 10650, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [251], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10646, "resistance": 6}, {"id": 651, "displayName": "Stone Brick Wall", "name": "stone_brick_wall", "hardness": 1.5, "minStateId": 10651, "maxStateId": 10714, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [252], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10710, "resistance": 6}, {"id": 652, "displayName": "Nether Brick Wall", "name": "nether_brick_wall", "hardness": 2, "minStateId": 10715, "maxStateId": 10778, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [253], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10774, "resistance": 6}, {"id": 653, "displayName": "Andesite Wall", "name": "andesite_wall", "hardness": 1.5, "minStateId": 10779, "maxStateId": 10842, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [254], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10838, "resistance": 6}, {"id": 654, "displayName": "Red Nether Brick Wall", "name": "red_nether_brick_wall", "hardness": 2, "minStateId": 10843, "maxStateId": 10906, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [255], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10902, "resistance": 6}, {"id": 655, "displayName": "Sandstone Wall", "name": "sandstone_wall", "hardness": 0.8, "minStateId": 10907, "maxStateId": 10970, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [256], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 10966, "resistance": 0.8}, {"id": 656, "displayName": "End Stone Brick Wall", "name": "end_stone_brick_wall", "hardness": 3, "minStateId": 10971, "maxStateId": 11034, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [257], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 11030, "resistance": 9}, {"id": 657, "displayName": "Diorite Wall", "name": "diorite_wall", "hardness": 1.5, "minStateId": 11035, "maxStateId": 11098, "states": [{"name": "east", "type": "bool", "num_values": 2}, {"name": "north", "type": "bool", "num_values": 2}, {"name": "south", "type": "bool", "num_values": 2}, {"name": "up", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}, {"name": "west", "type": "bool", "num_values": 2}], "drops": [258], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 11094, "resistance": 6}, {"id": 658, "displayName": "Scaffolding", "name": "scaffolding", "hardness": 0, "minStateId": 11099, "maxStateId": 11130, "states": [{"name": "bottom", "type": "bool", "num_values": 2}, {"name": "distance", "type": "int", "num_values": 8}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [505], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 11130, "resistance": 0}, {"id": 659, "displayName": "Loom", "name": "loom", "hardness": 2.5, "minStateId": 11131, "maxStateId": 11134, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [859], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 11131, "resistance": 2.5}, {"id": 660, "displayName": "Barrel", "name": "barrel", "hardness": 2.5, "minStateId": 11135, "maxStateId": 11146, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}, {"name": "open", "type": "bool", "num_values": 2}], "drops": [865], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 11136, "resistance": 2.5}, {"id": 661, "displayName": "Smoker", "name": "smoker", "hardness": 3.5, "minStateId": 11147, "maxStateId": 11154, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "lit", "type": "bool", "num_values": 2}], "drops": [866], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 11148, "resistance": 3.5}, {"id": 662, "displayName": "Blast Furnace", "name": "blast_furnace", "hardness": 3.5, "minStateId": 11155, "maxStateId": 11162, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "lit", "type": "bool", "num_values": 2}], "drops": [867], "diggable": true, "transparent": true, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 11156, "resistance": 3.5}, {"id": 663, "displayName": "Cartography Table", "name": "cartography_table", "hardness": 2.5, "minStateId": 11163, "maxStateId": 11163, "states": [], "drops": [868], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 11163, "resistance": 2.5}, {"id": 664, "displayName": "Fletching Table", "name": "fletching_table", "hardness": 2.5, "minStateId": 11164, "maxStateId": 11164, "states": [], "drops": [869], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 11164, "resistance": 2.5}, {"id": 665, "displayName": "Grindstone", "name": "grindstone", "hardness": 2, "minStateId": 11165, "maxStateId": 11176, "states": [{"name": "face", "type": "enum", "num_values": 3, "values": ["floor", "wall", "ceiling"]}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [870], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 11169, "resistance": 6}, {"id": 666, "displayName": "Lectern", "name": "lectern", "hardness": 2.5, "minStateId": 11177, "maxStateId": 11192, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "has_book", "type": "bool", "num_values": 2}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [871], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 11180, "resistance": 2.5}, {"id": 667, "displayName": "Smithing Table", "name": "smithing_table", "hardness": 2.5, "minStateId": 11193, "maxStateId": 11193, "states": [], "drops": [872], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 11193, "resistance": 2.5}, {"id": 668, "displayName": "<PERSON><PERSON><PERSON>", "name": "stonecutter", "hardness": 3.5, "minStateId": 11194, "maxStateId": 11197, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}], "drops": [873], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "defaultState": 11194, "resistance": 3.5}, {"id": 669, "displayName": "Bell", "name": "bell", "hardness": 5, "minStateId": 11198, "maxStateId": 11229, "states": [{"name": "attachment", "type": "enum", "num_values": 4, "values": ["floor", "ceiling", "single_wall", "double_wall"]}, {"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "powered", "type": "bool", "num_values": 2}], "drops": [874], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "defaultState": 11199, "resistance": 5}, {"id": 670, "displayName": "Lantern", "name": "lantern", "hardness": 3.5, "minStateId": 11230, "maxStateId": 11231, "states": [{"name": "hanging", "type": "bool", "num_values": 2}], "drops": [875], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "rock", "harvestTools": {"521": true, "535": true, "539": true, "543": true, "550": true}, "defaultState": 11231, "resistance": 3.5}, {"id": 671, "displayName": "Campfire", "name": "campfire", "hardness": 2, "minStateId": 11232, "maxStateId": 11263, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "lit", "type": "bool", "num_values": 2}, {"name": "signal_fire", "type": "bool", "num_values": 2}, {"name": "waterlogged", "type": "bool", "num_values": 2}], "drops": [877, 528], "diggable": true, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 1, "material": "wood", "defaultState": 11235, "resistance": 2}, {"id": 672, "displayName": "Sweet <PERSON>", "name": "sweet_berry_bush", "hardness": 0, "minStateId": 11264, "maxStateId": 11267, "states": [{"name": "age", "type": "int", "num_values": 4}], "drops": [876], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "empty", "stackSize": 64, "defaultState": 11264, "resistance": 0}, {"id": 673, "displayName": "Structure Block", "name": "structure_block", "hardness": null, "minStateId": 11268, "maxStateId": 11271, "states": [{"name": "mode", "type": "enum", "num_values": 4, "values": ["save", "load", "corner", "data"]}], "drops": [], "diggable": false, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 11268, "resistance": 3600000}, {"id": 674, "displayName": "Jigsaw Block", "name": "jigsaw", "hardness": null, "minStateId": 11272, "maxStateId": 11277, "states": [{"name": "facing", "type": "enum", "num_values": 6, "values": ["north", "east", "south", "west", "up", "down"]}], "drops": [], "diggable": false, "transparent": false, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "defaultState": 11276, "resistance": 3600000}, {"id": 675, "displayName": "Composter", "name": "composter", "hardness": 0.6, "minStateId": 11278, "maxStateId": 11286, "states": [{"name": "level", "type": "int", "num_values": 9}], "drops": [517, 646], "diggable": true, "transparent": true, "filterLight": 0, "emitLight": 0, "boundingBox": "block", "stackSize": 64, "material": "wood", "defaultState": 11278, "resistance": 0.6}, {"id": 676, "displayName": "Bee Nest", "name": "bee_nest", "hardness": 0.3, "minStateId": 11287, "maxStateId": 11310, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "honey_level", "type": "int", "num_values": 6}], "drops": [879], "diggable": true, "transparent": false, "stackSize": 64, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "material": "wood", "defaultState": 11287, "resistance": 0.3}, {"id": 677, "displayName": "Beehive", "name": "beehive", "hardness": 0.6, "minStateId": 11311, "maxStateId": 11334, "states": [{"name": "facing", "type": "enum", "num_values": 4, "values": ["north", "south", "west", "east"]}, {"name": "honey_level", "type": "int", "num_values": 6}], "drops": [880], "diggable": true, "transparent": false, "stackSize": 64, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "material": "wood", "defaultState": 11311, "resistance": 0.6}, {"id": 678, "displayName": "Honey Block", "name": "honey_block", "hardness": 0, "minStateId": 11335, "maxStateId": 11335, "states": [], "drops": [882], "diggable": true, "transparent": true, "stackSize": 64, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "defaultState": 11335, "resistance": 0}, {"id": 679, "displayName": "Honeycomb Block", "name": "honeycomb_block", "hardness": 0.6, "minStateId": 11336, "maxStateId": 11336, "states": [], "drops": [883], "diggable": true, "transparent": false, "stackSize": 64, "filterLight": 15, "emitLight": 0, "boundingBox": "block", "defaultState": 11336, "resistance": 0.6}]