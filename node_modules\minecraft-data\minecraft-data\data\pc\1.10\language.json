{"language.name": "English", "language.region": "United States", "language.code": "en_US", "gui.done": "Done", "gui.cancel": "Cancel", "gui.back": "Back", "gui.toTitle": "Back to title screen", "gui.toMenu": "Back to server list", "gui.up": "Up", "gui.down": "Down", "gui.yes": "Yes", "gui.no": "No", "gui.none": "None", "gui.all": "All", "translation.test.none": "Hello, world!", "translation.test.complex": "Prefix, %s%2$s again %s and %1$s lastly %s and also %1$s again!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "hi %", "translation.test.invalid2": "hi %  s", "translation.test.args": "%s %s", "translation.test.world": "world", "menu.game": "Game menu", "menu.singleplayer": "Singleplayer", "menu.multiplayer": "Multiplayer", "menu.online": "Minecraft Realms", "menu.options": "Options...", "menu.quit": "Quit Game", "menu.returnToMenu": "Save and Quit to Title", "menu.disconnect": "Disconnect", "menu.returnToGame": "Back to Game", "menu.generatingLevel": "Generating world", "menu.loadingLevel": "Loading world", "menu.generatingTerrain": "Building terrain", "menu.convertingLevel": "Converting world", "menu.respawning": "Respawning", "menu.shareToLan": "Open to LAN", "selectWorld.title": "Select World", "selectWorld.empty": "empty", "selectWorld.world": "World", "selectWorld.select": "Play Selected World", "selectWorld.create": "Create New World", "selectWorld.recreate": "Re-Create", "selectWorld.createDemo": "Play New Demo World", "selectWorld.delete": "Delete", "selectWorld.edit": "Edit", "selectWorld.edit.title": "Edit World", "selectWorld.edit.resetIcon": "Reset Icon", "selectWorld.edit.openFolder": "Open Folder", "selectWorld.edit.save": "Save World", "selectWorld.deleteQuestion": "Are you sure you want to delete this world?", "selectWorld.deleteWarning": "will be lost forever! (A long time!)", "selectWorld.deleteButton": "Delete", "selectWorld.conversion": "Must be converted!", "selectWorld.newWorld": "New World", "selectWorld.newWorld.copyOf": "Copy of %s", "selectWorld.enterName": "World Name", "selectWorld.resultFolder": "Will be saved in:", "selectWorld.enterSeed": "Seed for the World Generator", "selectWorld.seedInfo": "Leave blank for a random seed", "selectWorld.cheats": "Cheats", "selectWorld.customizeType": "Customize", "selectWorld.version": "Version:", "selectWorld.versionUnknown": "unknown", "selectWorld.versionQuestion": "Do you really want to load this world?", "selectWorld.versionWarning": "This world was saved in version '%s' and loading it in this version could cause corruption!", "selectWorld.versionJoinButton": "Use anyway", "selectWorld.tooltip.fromNewerVersion1": "World was saved in a newer version,", "selectWorld.tooltip.fromNewerVersion2": "loading this world could cause problems!", "selectWorld.tooltip.snapshot1": "Don't forget to backup this world", "selectWorld.tooltip.snapshot2": "before you load it in this snapshot.", "createWorld.customize.presets": "Presets", "createWorld.customize.presets.title": "Select a Preset", "createWorld.customize.presets.select": "Use Preset", "createWorld.customize.presets.share": "Want to share your preset with someone? Use the below box!", "createWorld.customize.presets.list": "Alternatively, here's some we made earlier!", "createWorld.customize.flat.title": "Superflat Customization", "createWorld.customize.flat.tile": "Layer Material", "createWorld.customize.flat.height": "Height", "createWorld.customize.flat.addLayer": "Add Layer", "createWorld.customize.flat.editLayer": "Edit Layer", "createWorld.customize.flat.removeLayer": "<PERSON><PERSON><PERSON>er", "createWorld.customize.flat.layer.top": "Top - %d", "createWorld.customize.flat.layer": "%d", "createWorld.customize.flat.layer.bottom": "Bottom - %d", "createWorld.customize.flat.air": "Air", "createWorld.customize.custom.page0": "Basic Settings", "createWorld.customize.custom.page1": "<PERSON><PERSON>", "createWorld.customize.custom.page2": "Advanced Settings (Expert Users Only!)", "createWorld.customize.custom.page3": "Extra Advanced Settings (Expert Users Only!)", "createWorld.customize.custom.randomize": "Randomize", "createWorld.customize.custom.prev": "Previous Page", "createWorld.customize.custom.next": "Next Page", "createWorld.customize.custom.defaults": "De<PERSON>ults", "createWorld.customize.custom.confirm1": "This will overwrite your current", "createWorld.customize.custom.confirm2": "settings and cannot be undone.", "createWorld.customize.custom.confirmTitle": "Warning!", "createWorld.customize.custom.mainNoiseScaleX": "Main Noise Scale X", "createWorld.customize.custom.mainNoiseScaleY": "Main Noise Scale Y", "createWorld.customize.custom.mainNoiseScaleZ": "Main Noise Scale Z", "createWorld.customize.custom.depthNoiseScaleX": "Depth Noise Scale X", "createWorld.customize.custom.depthNoiseScaleZ": "Depth Noise Scale Z", "createWorld.customize.custom.depthNoiseScaleExponent": "Depth Noise Exponent", "createWorld.customize.custom.baseSize": "Depth Base Size", "createWorld.customize.custom.coordinateScale": "Coordinate Scale", "createWorld.customize.custom.heightScale": "Height Scale", "createWorld.customize.custom.stretchY": "Height Stretch", "createWorld.customize.custom.upperLimitScale": "Upper Limit Scale", "createWorld.customize.custom.lowerLimitScale": "Lower Limit Scale", "createWorld.customize.custom.biomeDepthWeight": "Biome <PERSON>", "createWorld.customize.custom.biomeDepthOffset": "Biome Depth Offset", "createWorld.customize.custom.biomeScaleWeight": "Biome Scale Weight", "createWorld.customize.custom.biomeScaleOffset": "Biome Scale Offset", "createWorld.customize.custom.seaLevel": "Sea Level", "createWorld.customize.custom.useCaves": "Caves", "createWorld.customize.custom.useStrongholds": "Strongholds", "createWorld.customize.custom.useVillages": "Villages", "createWorld.customize.custom.useMineShafts": "Mineshafts", "createWorld.customize.custom.useTemples": "Temples", "createWorld.customize.custom.useMonuments": "Ocean Monuments", "createWorld.customize.custom.useRavines": "<PERSON><PERSON>", "createWorld.customize.custom.useDungeons": "Dungeons", "createWorld.customize.custom.dungeonChance": "Dungeon Count", "createWorld.customize.custom.useWaterLakes": "Water Lakes", "createWorld.customize.custom.waterLakeChance": "Water Lake Rarity", "createWorld.customize.custom.useLavaLakes": "Lava Lakes", "createWorld.customize.custom.lavaLakeChance": "Lava Lake Rarity", "createWorld.customize.custom.useLavaOceans": "Lava Oceans", "createWorld.customize.custom.fixedBiome": "Biome", "createWorld.customize.custom.biomeSize": "Biome <PERSON>", "createWorld.customize.custom.riverSize": "River Size", "createWorld.customize.custom.size": " Spawn Size", "createWorld.customize.custom.count": " Spawn Tries", "createWorld.customize.custom.minHeight": " Min. Height", "createWorld.customize.custom.maxHeight": " <PERSON>. Height", "createWorld.customize.custom.center": " Center Height", "createWorld.customize.custom.spread": " Spread Height", "createWorld.customize.custom.presets.title": "Customize World Presets", "createWorld.customize.custom.presets": "Presets", "createWorld.customize.custom.preset.waterWorld": "Water World", "createWorld.customize.custom.preset.isleLand": "Isle Land", "createWorld.customize.custom.preset.caveDelight": "Caver's Delight", "createWorld.customize.custom.preset.mountains": "Mountain Madness", "createWorld.customize.custom.preset.drought": "Drought", "createWorld.customize.custom.preset.caveChaos": "Caves of Chaos", "createWorld.customize.custom.preset.goodLuck": "Good Luck", "gameMode.survival": "Survival Mode", "gameMode.creative": "Creative Mode", "gameMode.adventure": "Adventure Mode", "gameMode.spectator": "Spectator Mode", "gameMode.hardcore": "Hardcore Mode!", "gameMode.changed": "Your game mode has been updated to %s", "selectWorld.gameMode": "Game Mode", "selectWorld.gameMode.survival": "Survival", "selectWorld.gameMode.survival.line1": "Search for resources, crafting, gain", "selectWorld.gameMode.survival.line2": "levels, health and hunger", "selectWorld.gameMode.creative": "Creative", "selectWorld.gameMode.creative.line1": "Unlimited resources, free flying and", "selectWorld.gameMode.creative.line2": "destroy blocks instantly", "selectWorld.gameMode.spectator": "Spectator", "selectWorld.gameMode.spectator.line1": "You can look but don't touch", "selectWorld.gameMode.hardcore": "Hardcore", "selectWorld.gameMode.hardcore.line1": "Same as survival mode, locked at hardest", "selectWorld.gameMode.hardcore.line2": "difficulty, and one life only", "selectWorld.gameMode.adventure": "Adventure", "selectWorld.gameMode.adventure.line1": "Same as survival mode, but blocks can't", "selectWorld.gameMode.adventure.line2": "be added or removed", "selectWorld.moreWorldOptions": "More World Options...", "selectWorld.mapFeatures": "Generate Structures:", "selectWorld.mapFeatures.info": "Villages, dungeons etc", "selectWorld.mapType": "World Type:", "selectWorld.mapType.normal": "Normal", "selectWorld.allowCommands": "Allow Cheats:", "selectWorld.allowCommands.info": "Commands like /gamemode, /xp", "selectWorld.hardcoreMode": "Hardcore:", "selectWorld.hardcoreMode.info": "World is deleted upon death", "selectWorld.bonusItems": "Bonus Chest:", "generator.default": "<PERSON><PERSON><PERSON>", "generator.flat": "Superflat", "generator.largeBiomes": "Large Biomes", "generator.amplified": "AMPLIFIED", "generator.customized": "Customized", "generator.debug_all_block_states": "Debug Mode", "generator.amplified.info": "Notice: Just for fun, requires beefy computer", "selectServer.title": "Select Server", "selectServer.empty": "empty", "selectServer.select": "Join <PERSON>", "selectServer.direct": "Direct Connect", "selectServer.edit": "Edit", "selectServer.delete": "Delete", "selectServer.add": "Add server", "selectServer.defaultName": "Minecraft Server", "selectServer.deleteQuestion": "Are you sure you want to remove this server?", "selectServer.deleteWarning": "will be lost forever! (A long time!)", "selectServer.deleteButton": "Delete", "selectServer.refresh": "Refresh", "selectServer.hiddenAddress": "(Hidden)", "addServer.title": "Edit Server Info", "addServer.enterName": "Server Name", "addServer.enterIp": "Server Address", "addServer.add": "Done", "addServer.hideAddress": "Hide Address", "addServer.resourcePack": "Server Resource Packs", "addServer.resourcePack.enabled": "Enabled", "addServer.resourcePack.disabled": "Disabled", "addServer.resourcePack.prompt": "Prompt", "lanServer.title": "LAN World", "lanServer.scanning": "Scanning for games on your local network", "lanServer.start": "Start LAN World", "lanServer.otherPlayers": "Settings for Other Players", "mcoServer.title": "Minecraft Online World", "multiplayer.title": "Play Multiplayer", "multiplayer.connect": "Connect", "multiplayer.ipinfo": "Enter the IP of a server to connect to it:", "multiplayer.texturePrompt.line1": "This server recommends the use of a custom resource pack.", "multiplayer.texturePrompt.line2": "Would you like to download and install it automagically?", "multiplayer.downloadingTerrain": "Loading terrain", "multiplayer.downloadingStats": "Downloading statistics & achievements...", "multiplayer.stopSleeping": "Leave Bed", "multiplayer.player.joined": "%s joined the game", "multiplayer.player.joined.renamed": "%s (formerly known as %s) joined the game", "multiplayer.player.left": "%s left the game", "chat.cannotSend": "Cannot send chat message", "chat.type.text": "<%s> %s", "chat.type.emote": "* %s %s", "chat.type.announcement": "[%s] %s", "chat.type.admin": "[%s: %s]", "chat.type.achievement": "%s has just earned the achievement %s", "chat.type.achievement.taken": "%s has lost the achievement %s", "chat.link.confirm": "Are you sure you want to open the following website?", "chat.link.warning": "Never open links from people that you don't trust!", "chat.copy": "Copy to Clipboard", "chat.link.confirmTrusted": "Do you want to open this link or copy it to your clipboard?", "chat.link.open": "Open in browser", "menu.playdemo": "Play Demo World", "menu.resetdemo": "Reset Demo World", "demo.day.1": "This demo will last five game days, do your best!", "demo.day.2": "Day Two", "demo.day.3": "Day Three", "demo.day.4": "Day Four", "demo.day.5": "This is your last day!", "demo.day.warning": "Your time is almost up!", "demo.day.6": "You have passed your fifth day, use F2 to save a screenshot of your creation", "demo.reminder": "The demo time has expired, buy the game to continue or start a new world!", "demo.remainingTime": "Remaining time: %s", "demo.demoExpired": "Demo time's up!", "demo.help.movement": "Use %1$s, %2$s, %3$s, %4$s and the mouse to move around", "demo.help.movementShort": "Move by pressing %1$s, %2$s, %3$s, %4$s", "demo.help.movementMouse": "Look around using the mouse", "demo.help.jump": "Jump by pressing %1$s", "demo.help.inventory": "Use %1$s to open your inventory", "demo.help.title": "Minecraft Demo Mode", "demo.help.fullWrapped": "This demo will last 5 ingame days (about 1 hour and 40 minutes of real time). Check the achievements for hints! Have fun!", "demo.help.buy": "Purchase Now!", "demo.help.later": "Continue Playing!", "connect.connecting": "Connecting to the server...", "connect.authorizing": "Logging in...", "connect.failed": "Failed to connect to the server", "disconnect.genericReason": "%s", "disconnect.disconnected": "Disconnected by <PERSON>", "disconnect.lost": "Connection Lost", "disconnect.kicked": "Was kicked from the game", "disconnect.timeout": "Timed out", "disconnect.closed": "Connection closed", "disconnect.loginFailed": "Failed to login", "disconnect.loginFailedInfo": "Failed to login: %s", "disconnect.loginFailedInfo.serversUnavailable": "The authentication servers are currently down for maintenance.", "disconnect.loginFailedInfo.invalidSession": "Invalid session (Try restarting your game)", "disconnect.quitting": "Quitting", "disconnect.endOfStream": "End of stream", "disconnect.overflow": "Buffer overflow", "disconnect.spam": "Kicked for spamming", "soundCategory.master": "Master Volume", "soundCategory.music": "Music", "soundCategory.record": "Jukebox/Noteblocks", "soundCategory.weather": "Weather", "soundCategory.hostile": "Hostile Creatures", "soundCategory.neutral": "Friendly Creatures", "soundCategory.player": "Players", "soundCategory.block": "Blocks", "soundCategory.ambient": "Ambient/Environment", "soundCategory.voice": "Voice/Speech", "record.nowPlaying": "Now playing: %s", "options.off": "OFF", "options.on": "ON", "options.visible": "Shown", "options.hidden": "Hidden", "options.title": "Options", "options.controls": "Controls...", "options.video": "Video Settings...", "options.language": "Language...", "options.sounds": "Music & Sounds...", "options.sounds.title": "Music & Sound Options", "options.languageWarning": "Language translations may not be 100%% accurate", "options.videoTitle": "Video Settings", "options.customizeTitle": "Customize World Settings", "options.music": "Music", "options.sound": "Sound", "options.invertMouse": "Invert Mouse", "options.fov": "FOV", "options.fov.min": "Normal", "options.fov.max": "Quake Pro", "options.saturation": "Saturation", "options.gamma": "Brightness", "options.gamma.min": "<PERSON>", "options.gamma.max": "<PERSON>", "options.sensitivity": "Sensitivity", "options.sensitivity.min": "*yawn*", "options.sensitivity.max": "HYPERSPEED!!!", "options.renderDistance": "Render Distance", "options.viewBobbing": "View Bobbing", "options.ao": "Smooth Lighting", "options.ao.off": "OFF", "options.ao.min": "Minimum", "options.ao.max": "Maximum", "options.anaglyph": "3D Anaglyph", "options.framerateLimit": "<PERSON>", "options.framerateLimit.max": "Unlimited", "options.difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.difficulty.peaceful": "Peaceful", "options.difficulty.easy": "Easy", "options.difficulty.normal": "Normal", "options.difficulty.hard": "Hard", "options.difficulty.hardcore": "Hardcore", "options.graphics": "Graphics", "options.graphics.fancy": "Fancy", "options.graphics.fast": "Fast", "options.guiScale": "GUI Scale", "options.guiScale.auto": "Auto", "options.guiScale.small": "Small", "options.guiScale.normal": "Normal", "options.guiScale.large": "Large", "options.postProcessEnable": "Enable Post-Processing", "options.renderClouds": "Clouds", "options.postButton": "Post-Processing Settings...", "options.postVideoTitle": "Post-Processing Settings", "options.farWarning1": "A 64 bit Java installation is recommended", "options.farWarning2": "for 'Far' render distance (you have 32 bit)", "options.particles": "Particles", "options.particles.all": "All", "options.particles.decreased": "Decreased", "options.particles.minimal": "Minimal", "options.multiplayer.title": "Multiplayer Settings...", "options.chat.title": "Cha<PERSON>s...", "options.chat.visibility": "Cha<PERSON>", "options.chat.visibility.full": "Shown", "options.chat.visibility.system": "Commands Only", "options.chat.visibility.hidden": "Hidden", "options.chat.color": "Colors", "options.chat.opacity": "Opacity", "options.chat.links": "Web Links", "options.chat.links.prompt": "Prompt on Links", "options.chat.scale": "Scale", "options.chat.width": "<PERSON><PERSON><PERSON>", "options.chat.height.focused": "Focused Height", "options.chat.height.unfocused": "Unfocused Height", "options.skinCustomisation": "Skin Customization...", "options.skinCustomisation.title": "Skin Customization", "options.modelPart.cape": "Cape", "options.modelPart.hat": "Hat", "options.modelPart.jacket": "Jacket", "options.modelPart.left_sleeve": "Left Sleeve", "options.modelPart.right_sleeve": "Right Sleeve", "options.modelPart.left_pants_leg": "Left Pants Leg", "options.modelPart.right_pants_leg": "Right Pants Leg", "options.snooper": "<PERSON>ow Snooper", "options.snooper.view": "Snooper Settings...", "options.snooper.title": "Machine Specs Collection", "options.snooper.desc": "We want to collect information about your machine to help improve Minecraft by knowing what we can support and where the biggest problems are. All of this information is completely anonymous and viewable below. We promise we won't do anything bad with this data, but if you want to opt out then feel free to toggle it off!", "options.resourcepack": "Resource Packs...", "options.fullscreen": "Fullscreen", "options.vsync": "Use VSync", "options.vbo": "Use VBOs", "options.touchscreen": "Touchscreen Mode", "options.reducedDebugInfo": "Reduced Debug Info", "options.entityShadows": "Entity Shadows", "options.mainHand": "Main Hand", "options.mainHand.left": "Left", "options.mainHand.right": "Right", "options.attackIndicator": "Attack Indicator", "options.attack.crosshair": "<PERSON><PERSON><PERSON>", "options.attack.hotbar": "Hotbar", "options.showSubtitles": "Show Subtitles", "options.realmsNotifications": "Realms Notifications", "options.autoJump": "Auto-jump", "options.mipmapLevels": "Mipmap Levels", "options.forceUnicodeFont": "Force Unicode Font", "difficulty.lock.title": "Lock World Difficulty", "difficulty.lock.question": "Are you sure you want to lock the difficulty of this world? This will set this world to always be %1$s, and you will never be able to change that again.", "title.oldgl1": "Old graphics card detected; this may prevent you from", "title.oldgl2": "playing in the future as OpenGL 2.0 will be required.", "controls.title": "Controls", "controls.reset": "Reset", "controls.resetAll": "Reset Keys", "key.sprint": "Sprint", "key.forward": "Walk Forwards", "key.left": "Strafe Left", "key.back": "Walk Backwards", "key.right": "Strafe Right", "key.jump": "Jump", "key.inventory": "Open/Close Inventory", "key.drop": "Drop Selected Item", "key.swapHands": "<PERSON><PERSON><PERSON> <PERSON>em In Hands", "key.chat": "Open Chat", "key.sneak": "<PERSON><PERSON><PERSON>", "key.playerlist": "List Players", "key.attack": "Attack/Destroy", "key.use": "Use Item/Place Block", "key.pickItem": "Pick Block", "key.mouseButton": "Button %1$s", "key.command": "Open Command", "key.screenshot": "Take Screenshot", "key.togglePerspective": "Toggle Perspective", "key.smoothCamera": "Toggle Cinematic Camera", "key.fullscreen": "Toggle Fullscreen", "key.spectatorOutlines": "Highlight Players (Spectators)", "key.hotbar.1": "Hotbar Slot 1", "key.hotbar.2": "Hotbar Slot 2", "key.hotbar.3": "Hotbar Slot 3", "key.hotbar.4": "Hotbar Slot 4", "key.hotbar.5": "Hotbar Slot 5", "key.hotbar.6": "Hotbar Slot 6", "key.hotbar.7": "Hotbar Slot 7", "key.hotbar.8": "Hotbar Slot 8", "key.hotbar.9": "Hotbar Slot 9", "key.categories.movement": "Movement", "key.categories.misc": "Miscellaneous", "key.categories.multiplayer": "Multiplayer", "key.categories.gameplay": "Gameplay", "key.categories.ui": "Game Interface", "key.categories.inventory": "Inventory", "resourcePack.openFolder": "Open resource pack folder", "resourcePack.title": "Select Resource Packs", "resourcePack.available.title": "Available Resource Packs", "resourcePack.selected.title": "Selected Resource Packs", "resourcePack.folderInfo": "(Place resource pack files here)", "resourcePack.incompatible": "Incompatible", "resourcePack.incompatible.old": "(Made for an older version of Minecraft)", "resourcePack.incompatible.new": "(Made for a newer version of Minecraft)", "resourcePack.incompatible.confirm.title": "Are you sure you want to load this resource pack?", "resourcePack.incompatible.confirm.old": "This resource pack was made for an older version of Minecraft and may no longer work correctly.", "resourcePack.incompatible.confirm.new": "This resource pack was made for a newer version of Minecraft and may no longer work correctly.", "sign.edit": "Edit sign message", "book.pageIndicator": "Page %1$s of %2$s", "book.byAuthor": "by %1$s", "book.signButton": "Sign", "book.editTitle": "Enter Book Title:", "book.finalizeButton": "Sign and Close", "book.finalizeWarning": "Note! When you sign the book, it will no longer be editable.", "book.generation.0": "Original", "book.generation.1": "Copy of original", "book.generation.2": "Copy of a copy", "book.generation.3": "Tattered", "merchant.deprecated": "Trade something else to unlock!", "tile.barrier.name": "Barrier", "tile.stone.stone.name": "Stone", "tile.stone.granite.name": "Granite", "tile.stone.graniteSmooth.name": "Polished Granite", "tile.stone.diorite.name": "Diorite", "tile.stone.dioriteSmooth.name": "Polished Diorite", "tile.stone.andesite.name": "Andesite", "tile.stone.andesiteSmooth.name": "Polished Andesite", "tile.hayBlock.name": "<PERSON>", "tile.grass.name": "Grass Block", "tile.dirt.name": "Dirt", "tile.dirt.default.name": "Dirt", "tile.dirt.coarse.name": "Coarse Dirt", "tile.dirt.podzol.name": "Podzol", "tile.stonebrick.name": "Cobblestone", "tile.wood.name": "Wooden Planks", "tile.wood.oak.name": "Oak Wood Planks", "tile.wood.spruce.name": "Spruce Wood Planks", "tile.wood.birch.name": "Birch Wood Planks", "tile.wood.jungle.name": "Jungle Wood Planks", "tile.wood.acacia.name": "Acacia Wood Planks", "tile.wood.big_oak.name": "Dark Oak Wood Planks", "tile.sapling.oak.name": "Oak Sapling", "tile.sapling.spruce.name": "Spruce Sapling", "tile.sapling.birch.name": "Birch Sapling", "tile.sapling.jungle.name": "Jungle Sapling", "tile.sapling.acacia.name": "Acacia Sapling", "tile.sapling.big_oak.name": "Dark Oak Sapling", "tile.deadbush.name": "Dead Bush", "tile.bedrock.name": "Bedrock", "tile.water.name": "Water", "tile.lava.name": "<PERSON><PERSON>", "tile.sand.name": "Sand", "tile.sand.default.name": "Sand", "tile.sand.red.name": "Red Sand", "tile.sandStone.name": "Sandstone", "tile.sandStone.default.name": "Sandstone", "tile.sandStone.chiseled.name": "Chiseled Sandstone", "tile.sandStone.smooth.name": "Smooth Sandstone", "tile.redSandStone.name": "Red Sandstone", "tile.redSandStone.default.name": "Red Sandstone", "tile.redSandStone.chiseled.name": "Chiseled Red Sandstone", "tile.redSandStone.smooth.name": "Smooth Red Sandstone", "tile.gravel.name": "<PERSON>l", "tile.oreGold.name": "Gold Ore", "tile.oreIron.name": "Iron Ore", "tile.oreCoal.name": "Coal Ore", "tile.log.name": "<PERSON>", "tile.log.oak.name": "Oak Wood", "tile.log.spruce.name": "Spruce Wood", "tile.log.birch.name": "Birch Wood", "tile.log.jungle.name": "Jungle Wood", "tile.log.acacia.name": "Acacia Wood", "tile.log.big_oak.name": "Dark Oak Wood", "tile.leaves.name": "Leaves", "tile.leaves.oak.name": "Oak Leaves", "tile.leaves.spruce.name": "Spruce Leaves", "tile.leaves.birch.name": "Birch Leaves", "tile.leaves.jungle.name": "Jungle Leaves", "tile.leaves.acacia.name": "Acacia Leaves", "tile.leaves.big_oak.name": "Dark Oak Leaves", "tile.tallgrass.name": "Grass", "tile.tallgrass.shrub.name": "<PERSON><PERSON><PERSON>", "tile.tallgrass.grass.name": "Grass", "tile.tallgrass.fern.name": "Fern", "tile.sponge.dry.name": "Sponge", "tile.sponge.wet.name": "Wet Sponge", "tile.glass.name": "Glass", "tile.stainedGlass.name": "Stained Glass", "tile.stainedGlass.black.name": "Black Stained Glass", "tile.stainedGlass.red.name": "Red Stained Glass", "tile.stainedGlass.green.name": "Green Stained Glass", "tile.stainedGlass.brown.name": "<PERSON> Stained Glass", "tile.stainedGlass.blue.name": "Blue Stained Glass", "tile.stainedGlass.purple.name": "Purple Stained Glass", "tile.stainedGlass.cyan.name": "<PERSON><PERSON>", "tile.stainedGlass.silver.name": "Light Gray Stained Glass", "tile.stainedGlass.gray.name": "<PERSON> Stained Glass", "tile.stainedGlass.pink.name": "Pink Stained Glass", "tile.stainedGlass.lime.name": "Lime Stained Glass", "tile.stainedGlass.yellow.name": "Yellow Stained Glass", "tile.stainedGlass.lightBlue.name": "Light Blue Stained Glass", "tile.stainedGlass.magenta.name": "Magenta Stained Glass", "tile.stainedGlass.orange.name": "Orange Stained Glass", "tile.stainedGlass.white.name": "White Stained Glass", "tile.thinStainedGlass.name": "Stained Glass Pane", "tile.thinStainedGlass.black.name": "Black Stained Glass Pane", "tile.thinStainedGlass.red.name": "Red Stained Glass Pane", "tile.thinStainedGlass.green.name": "Green Stained Glass Pane", "tile.thinStainedGlass.brown.name": "<PERSON> Stained Glass Pane", "tile.thinStainedGlass.blue.name": "Blue Stained Glass Pane", "tile.thinStainedGlass.purple.name": "Purple Stained Glass Pane", "tile.thinStainedGlass.cyan.name": "<PERSON><PERSON> Stained Glass Pane", "tile.thinStainedGlass.silver.name": "Light Gray Stained Glass Pane", "tile.thinStainedGlass.gray.name": "Gray Stained Glass Pane", "tile.thinStainedGlass.pink.name": "Pink Stained Glass Pane", "tile.thinStainedGlass.lime.name": "Lime Stained Glass Pane", "tile.thinStainedGlass.yellow.name": "Yellow Stained Glass Pane", "tile.thinStainedGlass.lightBlue.name": "Light Blue Stained Glass Pane", "tile.thinStainedGlass.magenta.name": "Magenta Stained Glass Pane", "tile.thinStainedGlass.orange.name": "Orange Stained Glass Pane", "tile.thinStainedGlass.white.name": "White Stained Glass Pane", "tile.thinGlass.name": "Glass Pane", "tile.cloth.name": "Wool", "tile.flower1.name": "Flower", "tile.flower1.dandelion.name": "Dandelion", "tile.flower2.name": "Flower", "tile.flower2.poppy.name": "<PERSON><PERSON>", "tile.flower2.blueOrchid.name": "Blue Orchid", "tile.flower2.allium.name": "Allium", "tile.flower2.houstonia.name": "Azure Bluet", "tile.flower2.tulipRed.name": "<PERSON>lip", "tile.flower2.tulipOrange.name": "Orange Tulip", "tile.flower2.tulipWhite.name": "White Tulip", "tile.flower2.tulipPink.name": "<PERSON> Tulip", "tile.flower2.oxeyeDaisy.name": "Oxeye Daisy", "tile.doublePlant.name": "Plant", "tile.doublePlant.sunflower.name": "Sunflower", "tile.doublePlant.syringa.name": "Lilac", "tile.doublePlant.grass.name": "Double Tallgrass", "tile.doublePlant.fern.name": "Large Fern", "tile.doublePlant.rose.name": "<PERSON>", "tile.doublePlant.paeonia.name": "Peony", "tile.mushroom.name": "Mushroom", "tile.blockGold.name": "Block of Gold", "tile.blockIron.name": "Block of Iron", "tile.stoneSlab.name": "<PERSON> Slab", "tile.stoneSlab.stone.name": "<PERSON> Slab", "tile.stoneSlab.sand.name": "Sandstone Slab", "tile.stoneSlab.wood.name": "<PERSON><PERSON>", "tile.stoneSlab.cobble.name": "Cobblestone Slab", "tile.stoneSlab.brick.name": "Bricks Slab", "tile.stoneSlab.smoothStoneBrick.name": "Stone Bricks Slab", "tile.stoneSlab.netherBrick.name": "Nether Brick Slab", "tile.stoneSlab.quartz.name": "Quartz Slab", "tile.stoneSlab2.red_sandstone.name": "Red Sandstone Slab", "tile.woodSlab.name": "<PERSON>b", "tile.woodSlab.oak.name": "Oak Wood Slab", "tile.woodSlab.spruce.name": "Spruce Wood Slab", "tile.woodSlab.birch.name": "<PERSON> Slab", "tile.woodSlab.jungle.name": "Jungle Wood Slab", "tile.woodSlab.acacia.name": "Acacia <PERSON> Slab", "tile.woodSlab.big_oak.name": "Dark Oak Wood Slab", "tile.brick.name": "Bricks", "tile.tnt.name": "TNT", "tile.bookshelf.name": "Bookshelf", "tile.stoneMoss.name": "<PERSON>", "tile.obsidian.name": "Obsidian", "tile.torch.name": "<PERSON>ch", "tile.fire.name": "Fire", "tile.mobSpawner.name": "Monster Spawner", "tile.stairsWood.name": "Oak Wood Stairs", "tile.stairsWoodSpruce.name": "Spruce Wood Stairs", "tile.stairsWoodBirch.name": "Birch Wood Stairs", "tile.stairsWoodJungle.name": "Jungle Wood Stairs", "tile.stairsWoodAcacia.name": "Acacia Wood Stairs", "tile.stairsWoodDarkOak.name": "Dark Oak Wood Stairs", "tile.chest.name": "Chest", "tile.chestTrap.name": "Trapped Chest", "tile.redstoneDust.name": "Redstone Dust", "tile.oreDiamond.name": "Diamond Ore", "tile.blockCoal.name": "Block of Coal", "tile.blockDiamond.name": "Block of Diamond", "tile.workbench.name": "Crafting Table", "tile.crops.name": "Crops", "tile.farmland.name": "Farmland", "tile.furnace.name": "Furnace", "tile.sign.name": "Sign", "tile.doorWood.name": "Wooden Door", "tile.ladder.name": "Ladder", "tile.rail.name": "Rail", "tile.goldenRail.name": "Powered Rail", "tile.activatorRail.name": "Activator Rail", "tile.detectorRail.name": "Detector Rail", "tile.stairsStone.name": "Cobblestone Stairs", "tile.stairsSandStone.name": "Sandstone Stairs", "tile.stairsRedSandStone.name": "Red Sandstone Stairs", "tile.lever.name": "Lever", "tile.pressurePlateStone.name": "Stone Pressure Plate", "tile.pressurePlateWood.name": "Wooden Pressure Plate", "tile.weightedPlate_light.name": "Weighted Pressure Plate (Light)", "tile.weightedPlate_heavy.name": "Weighted Pressure Plate (Heavy)", "tile.doorIron.name": "Iron Door", "tile.oreRedstone.name": "Redstone Ore", "tile.notGate.name": "Redstone Torch", "tile.button.name": "<PERSON><PERSON>", "tile.snow.name": "Snow", "tile.woolCarpet.name": "Carpet", "tile.woolCarpet.black.name": "Black Carpet", "tile.woolCarpet.red.name": "Red Carpet", "tile.woolCarpet.green.name": "Green Carpet", "tile.woolCarpet.brown.name": "Brown Carpet", "tile.woolCarpet.blue.name": "Blue Carpet", "tile.woolCarpet.purple.name": "Purple Carpet", "tile.woolCarpet.cyan.name": "<PERSON><PERSON>", "tile.woolCarpet.silver.name": "Light Gray Carpet", "tile.woolCarpet.gray.name": "<PERSON> Carpet", "tile.woolCarpet.pink.name": "Pink Carpet", "tile.woolCarpet.lime.name": "Lime Carpet", "tile.woolCarpet.yellow.name": "Yellow Carpet", "tile.woolCarpet.lightBlue.name": "Light Blue Carpet", "tile.woolCarpet.magenta.name": "Magenta Carpet", "tile.woolCarpet.orange.name": "Orange Carpet", "tile.woolCarpet.white.name": "Carpet", "tile.ice.name": "Ice", "tile.frostedIce.name": "Frosted Ice", "tile.icePacked.name": "Packed Ice", "tile.cactus.name": "Cactus", "tile.clay.name": "<PERSON>", "tile.clayHardenedStained.name": "Stained Hardened Clay", "tile.clayHardenedStained.black.name": "Black Hardened Clay", "tile.clayHardenedStained.red.name": "Red Hardened Clay", "tile.clayHardenedStained.green.name": "Green Hardened Clay", "tile.clayHardenedStained.brown.name": "<PERSON> Hardened Clay", "tile.clayHardenedStained.blue.name": "Blue Hardened Clay", "tile.clayHardenedStained.purple.name": "Purple Hardened Clay", "tile.clayHardenedStained.cyan.name": "<PERSON><PERSON>ened <PERSON>", "tile.clayHardenedStained.silver.name": "<PERSON> Gray Hardened Clay", "tile.clayHardenedStained.gray.name": "<PERSON> Hardened Clay", "tile.clayHardenedStained.pink.name": "Pink Hardened Clay", "tile.clayHardenedStained.lime.name": "Lime Hardened Clay", "tile.clayHardenedStained.yellow.name": "Yellow Hardened Clay", "tile.clayHardenedStained.lightBlue.name": "Light Blue Hardened Clay", "tile.clayHardenedStained.magenta.name": "<PERSON><PERSON><PERSON> Hardened Clay", "tile.clayHardenedStained.orange.name": "Orange Hardened Clay", "tile.clayHardenedStained.white.name": "White Hardened Clay", "tile.clayHardened.name": "Hardened Clay", "tile.reeds.name": "Sugar cane", "tile.jukebox.name": "Jukebox", "tile.fence.name": "Oak Fence", "tile.spruceFence.name": "Spruce Fence", "tile.birchFence.name": "<PERSON>", "tile.jungleFence.name": "Jungle Fence", "tile.darkOakFence.name": "Dark Oak Fence", "tile.acaciaFence.name": "Acacia Fence", "tile.fenceGate.name": "Oak Fence Gate", "tile.spruceFenceGate.name": "Spruce Fence Gate", "tile.birchFenceGate.name": "Birch Fence Gate", "tile.jungleFenceGate.name": "Jungle Fence Gate", "tile.darkOakFenceGate.name": "Dark Oak Fence Gate", "tile.acaciaFenceGate.name": "Acacia Fence Gate", "tile.pumpkinStem.name": "<PERSON><PERSON><PERSON>", "tile.pumpkin.name": "<PERSON><PERSON><PERSON>", "tile.litpumpkin.name": "<PERSON>'<PERSON>", "tile.hellrock.name": "Netherrack", "tile.hellsand.name": "Soul Sand", "tile.lightgem.name": "Glowstone", "tile.portal.name": "Portal", "tile.cloth.black.name": "Black Wool", "tile.cloth.red.name": "Red Wool", "tile.cloth.green.name": "Green Wool", "tile.cloth.brown.name": "Brown Wool", "tile.cloth.blue.name": "Blue Wool", "tile.cloth.purple.name": "Purple Wool", "tile.cloth.cyan.name": "<PERSON><PERSON>", "tile.cloth.silver.name": "Light Gray Wool", "tile.cloth.gray.name": "Gray <PERSON>", "tile.cloth.pink.name": "Pink Wool", "tile.cloth.lime.name": "Lime Wool", "tile.cloth.yellow.name": "Yellow Wool", "tile.cloth.lightBlue.name": "Light Blue Wool", "tile.cloth.magenta.name": "Magenta Wool", "tile.cloth.orange.name": "Orange Wool", "tile.cloth.white.name": "Wool", "tile.oreLapis.name": "Lapis <PERSON> Ore", "tile.blockLapis.name": "Lapis <PERSON>", "tile.dispenser.name": "Dispenser", "tile.dropper.name": "Dropper", "tile.musicBlock.name": "Note Block", "tile.cake.name": "Cake", "tile.bed.name": "Bed", "tile.bed.occupied": "This bed is occupied", "tile.bed.noSleep": "You can only sleep at night", "tile.bed.notSafe": "You may not rest now, there are monsters nearby", "tile.bed.notValid": "Your home bed was missing or obstructed", "tile.lockedchest.name": "Locked chest", "tile.trapdoor.name": "<PERSON><PERSON>", "tile.ironTrapdoor.name": "Iron Trapdoor", "tile.web.name": "Cobweb", "tile.stonebricksmooth.name": "Stone Bricks", "tile.stonebricksmooth.default.name": "Stone Bricks", "tile.stonebricksmooth.mossy.name": "Mossy Stone Bricks", "tile.stonebricksmooth.cracked.name": "Cracked Stone Bricks", "tile.stonebricksmooth.chiseled.name": "Chiseled Stone Bricks", "tile.monsterStoneEgg.name": "Stone Monster Egg", "tile.monsterStoneEgg.stone.name": "Stone Monster Egg", "tile.monsterStoneEgg.cobble.name": "Cobblestone Monster Egg", "tile.monsterStoneEgg.brick.name": "Stone Brick Monster Egg", "tile.monsterStoneEgg.mossybrick.name": "<PERSON><PERSON> Brick Monster Egg", "tile.monsterStoneEgg.crackedbrick.name": "Cracked Stone Brick Monster Egg", "tile.monsterStoneEgg.chiseledbrick.name": "Chiseled Stone Brick Monster Egg", "tile.pistonBase.name": "<PERSON><PERSON>", "tile.pistonStickyBase.name": "<PERSON><PERSON>", "tile.fenceIron.name": "Iron Bars", "tile.melon.name": "Melon", "tile.stairsBrick.name": "Brick Stairs", "tile.stairsStoneBrickSmooth.name": "Stone Brick Stairs", "tile.vine.name": "Vines", "tile.netherBrick.name": "Nether Brick", "tile.netherFence.name": "Nether Brick Fence", "tile.stairsNetherBrick.name": "Nether Brick Stairs", "tile.netherStalk.name": "Nether Wart", "tile.cauldron.name": "<PERSON><PERSON><PERSON>", "tile.enchantmentTable.name": "Enchantment Table", "tile.anvil.name": "An<PERSON>", "tile.anvil.intact.name": "An<PERSON>", "tile.anvil.slightlyDamaged.name": "Slightly Damaged Anvil", "tile.anvil.veryDamaged.name": "Very Damaged Anvil", "tile.whiteStone.name": "End Stone", "tile.endPortalFrame.name": "End Portal", "tile.mycel.name": "Mycelium", "tile.waterlily.name": "<PERSON>", "tile.dragonEgg.name": "Dragon Egg", "tile.redstoneLight.name": "Redstone Lamp", "tile.cocoa.name": "Cocoa", "tile.enderChest.name": "<PERSON><PERSON> Chest", "tile.oreEmerald.name": "Emerald Ore", "tile.blockEmerald.name": "Block of Emerald", "tile.blockRedstone.name": "Block of Redstone", "tile.tripWire.name": "Tripwire", "tile.tripWireSource.name": "Tripwire Hook", "tile.commandBlock.name": "Command Block", "tile.repeatingCommandBlock.name": "Repeating Command Block", "tile.chainCommandBlock.name": "Chain Command Block", "tile.beacon.name": "Beacon", "tile.beacon.primary": "Primary Power", "tile.beacon.secondary": "Secondary Power", "tile.cobbleWall.normal.name": "Cobblestone Wall", "tile.cobbleWall.mossy.name": "<PERSON><PERSON>", "tile.carrots.name": "Carrots", "tile.potatoes.name": "Potatoes", "tile.daylightDetector.name": "Daylight Sensor", "tile.netherquartz.name": "<PERSON><PERSON>", "tile.hopper.name": "<PERSON>", "tile.quartzBlock.name": "Block of Quartz", "tile.quartzBlock.default.name": "Block of Quartz", "tile.quartzBlock.chiseled.name": "Chiseled Quartz Block", "tile.quartzBlock.lines.name": "Pillar Quartz Block", "tile.stairsQuartz.name": "Quartz Stairs", "tile.slime.name": "Slime Block", "tile.prismarine.rough.name": "<PERSON><PERSON><PERSON><PERSON>", "tile.prismarine.bricks.name": "Prismarine <PERSON>s", "tile.prismarine.dark.name": "<PERSON>", "tile.seaLantern.name": "Sea Lantern", "tile.endRod.name": "End Rod", "tile.chorusPlant.name": "Chorus Plant", "tile.chorusFlower.name": "Chorus Flower", "tile.purpurBlock.name": "Purpur Block", "tile.purpurPillar.name": "Purpur Pillar", "tile.stairsPurpur.name": "Purpur Stairs", "tile.purpurSlab.name": "Purpur Slab", "tile.endBricks.name": "End Stone Bricks", "tile.beetroots.name": "Beetroots", "tile.grassPath.name": "Grass Path", "tile.magma.name": "Magma Block", "tile.netherWartBlock.name": "Nether Wart Block", "tile.redNetherBrick.name": "Red Nether Brick", "tile.boneBlock.name": "Bone Block", "tile.structureVoid.name": "Structure Void", "tile.structureBlock.name": "Structure Block", "item.nameTag.name": "Name Tag", "item.leash.name": "Lead", "item.shovelIron.name": "Iron Shovel", "item.pickaxeIron.name": "Iron Pickaxe", "item.hatchetIron.name": "Iron Axe", "item.flintAndSteel.name": "Flint and Steel", "item.apple.name": "Apple", "item.cookie.name": "<PERSON><PERSON>", "item.bow.name": "Bow", "item.arrow.name": "Arrow", "item.spectral_arrow.name": "Spectral Arrow", "item.tipped_arrow.name": "Tipped Arrow", "item.coal.name": "Coal", "item.charcoal.name": "Charc<PERSON>l", "item.diamond.name": "Diamond", "item.emerald.name": "Emerald", "item.ingotIron.name": "Iron Ingot", "item.ingotGold.name": "Gold Ingot", "item.swordIron.name": "Iron Sword", "item.swordWood.name": "Wooden Sword", "item.shovelWood.name": "<PERSON><PERSON>", "item.pickaxeWood.name": "<PERSON><PERSON> Pick<PERSON>e", "item.hatchetWood.name": "Wooden Axe", "item.swordStone.name": "Stone Sword", "item.shovelStone.name": "<PERSON>el", "item.pickaxeStone.name": "<PERSON>", "item.hatchetStone.name": "Stone Axe", "item.swordDiamond.name": "Diamond Sword", "item.shovelDiamond.name": "Diamond Shovel", "item.pickaxeDiamond.name": "Diamond Pickaxe", "item.hatchetDiamond.name": "Diamond Axe", "item.stick.name": "Stick", "item.bowl.name": "Bowl", "item.mushroomStew.name": "Mushroom Stew", "item.swordGold.name": "Golden Sword", "item.shovelGold.name": "Golden Shovel", "item.pickaxeGold.name": "Golden Pickaxe", "item.hatchetGold.name": "Golden Axe", "item.string.name": "String", "item.feather.name": "<PERSON><PERSON>", "item.sulphur.name": "Gunpowder", "item.hoeWood.name": "<PERSON><PERSON>e", "item.hoeStone.name": "Stone Hoe", "item.hoeIron.name": "Iron Hoe", "item.hoeDiamond.name": "Diamond Hoe", "item.hoeGold.name": "Golden Hoe", "item.seeds.name": "Seeds", "item.seeds_pumpkin.name": "<PERSON><PERSON><PERSON> Seeds", "item.seeds_melon.name": "<PERSON>on Seeds", "item.melon.name": "Melon", "item.wheat.name": "Wheat", "item.bread.name": "Bread", "item.helmetCloth.name": "Leather Cap", "item.chestplateCloth.name": "<PERSON><PERSON>", "item.leggingsCloth.name": "<PERSON><PERSON>", "item.bootsCloth.name": "<PERSON><PERSON>", "item.helmetChain.name": "Chain Helmet", "item.chestplateChain.name": "Chain Chestplate", "item.leggingsChain.name": "Chain Leggings", "item.bootsChain.name": "Chain Boots", "item.helmetIron.name": "Iron Helmet", "item.chestplateIron.name": "Iron Chestplate", "item.leggingsIron.name": "Iron Leggings", "item.bootsIron.name": "Iron Boots", "item.helmetDiamond.name": "Diamond Helmet", "item.chestplateDiamond.name": "Diamond Chestplate", "item.leggingsDiamond.name": "Diamond Leggings", "item.bootsDiamond.name": "Diamond Boots", "item.helmetGold.name": "Golden Helmet", "item.chestplateGold.name": "Golden Chestplate", "item.leggingsGold.name": "Golden Leggings", "item.bootsGold.name": "Golden Boots", "item.flint.name": "Flint", "item.porkchopRaw.name": "Raw Porkchop", "item.porkchopCooked.name": "Cooked Porkchop", "item.chickenRaw.name": "Raw Chicken", "item.chickenCooked.name": "Cooked Chicken", "item.muttonRaw.name": "<PERSON>", "item.muttonCooked.name": "Cooked <PERSON>tton", "item.rabbitRaw.name": "Raw Rabbit", "item.rabbitCooked.name": "Cooked Rabbit", "item.rabbitStew.name": "Rabbit Stew", "item.rabbitFoot.name": "<PERSON>'s Foot", "item.rabbitHide.name": "<PERSON>", "item.beefRaw.name": "Raw Beef", "item.beefCooked.name": "Steak", "item.painting.name": "Painting", "item.frame.name": "<PERSON><PERSON>", "item.appleGold.name": "Golden Apple", "item.sign.name": "Sign", "item.doorOak.name": "Oak Door", "item.doorSpruce.name": "Spruce Door", "item.doorBirch.name": "<PERSON>", "item.doorJungle.name": "Jungle Door", "item.doorAcacia.name": "Acacia Door", "item.doorDarkOak.name": "Dark Oak Door", "item.bucket.name": "Bucket", "item.bucketWater.name": "Water Bucket", "item.bucketLava.name": "<PERSON><PERSON>et", "item.minecart.name": "Minecart", "item.saddle.name": "Saddle", "item.doorIron.name": "Iron Door", "item.redstone.name": "Redstone", "item.snowball.name": "Snowball", "item.boat.oak.name": "Oak Boat", "item.boat.spruce.name": "Spruce Boat", "item.boat.birch.name": "<PERSON> Boat", "item.boat.jungle.name": "Jungle Boat", "item.boat.acacia.name": "Acacia Boat", "item.boat.dark_oak.name": "Dark Oak Boat", "item.leather.name": "Leather", "item.milk.name": "Milk", "item.brick.name": "Brick", "item.clay.name": "<PERSON>", "item.reeds.name": "Sugar Canes", "item.paper.name": "Paper", "item.book.name": "Book", "item.slimeball.name": "Slimeball", "item.minecartChest.name": "Minecart with Chest", "item.minecartFurnace.name": "Minecart with Furnace", "item.minecartTnt.name": "Minecart with TNT", "item.minecartHopper.name": "Minecart with <PERSON>", "item.minecartCommandBlock.name": "Minecart with Command Block", "item.egg.name": "Egg", "item.compass.name": "<PERSON>mp<PERSON>", "item.fishingRod.name": "Fishing Rod", "item.clock.name": "Clock", "item.yellowDust.name": "Glowstone Dust", "item.fish.cod.raw.name": "Raw Fish", "item.fish.salmon.raw.name": "Raw Salmon", "item.fish.pufferfish.raw.name": "Pufferfish", "item.fish.clownfish.raw.name": "Clownfish", "item.fish.cod.cooked.name": "Cooked Fish", "item.fish.salmon.cooked.name": "Cooked Salmon", "item.record.name": "Music Disc", "item.record.13.desc": "C418 - 13", "item.record.cat.desc": "C418 - cat", "item.record.blocks.desc": "C418 - blocks", "item.record.chirp.desc": "C418 - chirp", "item.record.far.desc": "C418 - far", "item.record.mall.desc": "C418 - mall", "item.record.mellohi.desc": "C418 - me<PERSON><PERSON>", "item.record.stal.desc": "C418 - stal", "item.record.strad.desc": "C418 - strad", "item.record.ward.desc": "C418 - ward", "item.record.11.desc": "C418 - 11", "item.record.wait.desc": "C418 - wait", "item.bone.name": "Bone", "item.dyePowder.black.name": "Ink Sac", "item.dyePowder.red.name": "Rose Red", "item.dyePowder.green.name": "Cactus <PERSON>", "item.dyePowder.brown.name": "Cocoa Beans", "item.dyePowder.blue.name": "<PERSON><PERSON>", "item.dyePowder.purple.name": "Purple Dye", "item.dyePowder.cyan.name": "<PERSON><PERSON>", "item.dyePowder.silver.name": "Light Gray D<PERSON>", "item.dyePowder.gray.name": "<PERSON>", "item.dyePowder.pink.name": "Pink Dye", "item.dyePowder.lime.name": "Lime Dye", "item.dyePowder.yellow.name": "Dandelion Yellow", "item.dyePowder.lightBlue.name": "Light Blue Dye", "item.dyePowder.magenta.name": "<PERSON><PERSON><PERSON>", "item.dyePowder.orange.name": "Orange Dye", "item.dyePowder.white.name": "<PERSON>", "item.sugar.name": "Sugar", "item.cake.name": "Cake", "item.bed.name": "Bed", "item.diode.name": "Redstone Repeater", "item.comparator.name": "Redstone Comparator", "item.map.name": "Map", "item.leaves.name": "Leaves", "item.shears.name": "Shears", "item.rottenFlesh.name": "Rotten Flesh", "item.enderPearl.name": "<PERSON><PERSON>", "item.blazeRod.name": "<PERSON>", "item.ghastTear.name": "Ghast Tear", "item.netherStalkSeeds.name": "Nether Wart", "item.potion.name": "Potion", "item.end_crystal.name": "End Crystal", "item.goldNugget.name": "Gold Nugget", "item.glassBottle.name": "Glass Bottle", "item.spiderEye.name": "Spider Eye", "item.fermentedSpiderEye.name": "Fermented Spider Eye", "item.blazePowder.name": "<PERSON>", "item.magmaCream.name": "Magma Cream", "item.cauldron.name": "<PERSON><PERSON><PERSON>", "item.brewingStand.name": "Brewing Stand", "item.eyeOfEnder.name": "Eye of <PERSON>er", "item.speckledMelon.name": "Glistering <PERSON><PERSON>", "item.monsterPlacer.name": "Spawn", "item.expBottle.name": "Bottle o' Enchanting", "item.fireball.name": "Fire Charge", "item.writingBook.name": "Book and Quill", "item.writtenBook.name": "Written Book", "item.flowerPot.name": "Flower Pot", "item.emptyMap.name": "Empty Map", "item.carrots.name": "Carrot", "item.carrotGolden.name": "Golden Carrot", "item.potato.name": "Potato", "item.potatoBaked.name": "Baked Potato", "item.potatoPoisonous.name": "Poisonous Potato", "item.skull.skeleton.name": "Skeleton Skull", "item.skull.wither.name": "Wither Skeleton Skull", "item.skull.zombie.name": "Zombie Head", "item.skull.char.name": "Head", "item.skull.player.name": "%s's Head", "item.skull.creeper.name": "Creeper Head", "item.skull.dragon.name": "Dragon Head", "item.carrotOnAStick.name": "Carrot on a Stick", "item.netherStar.name": "Nether Star", "item.pumpkinPie.name": "Pumpkin Pie", "item.enchantedBook.name": "Enchanted Book", "item.fireworks.name": "Firework Rocket", "item.fireworks.flight": "Flight Duration:", "item.fireworksCharge.name": "Firework Star", "item.fireworksCharge.black": "Black", "item.fireworksCharge.red": "Red", "item.fireworksCharge.green": "Green", "item.fireworksCharge.brown": "<PERSON>", "item.fireworksCharge.blue": "Blue", "item.fireworksCharge.purple": "Purple", "item.fireworksCharge.cyan": "<PERSON><PERSON>", "item.fireworksCharge.silver": "Light Gray", "item.fireworksCharge.gray": "<PERSON>", "item.fireworksCharge.pink": "Pink", "item.fireworksCharge.lime": "Lime", "item.fireworksCharge.yellow": "Yellow", "item.fireworksCharge.lightBlue": "Light Blue", "item.fireworksCharge.magenta": "Ma<PERSON><PERSON>", "item.fireworksCharge.orange": "Orange", "item.fireworksCharge.white": "White", "item.fireworksCharge.customColor": "Custom", "item.fireworksCharge.fadeTo": "Fade to", "item.fireworksCharge.flicker": "Twinkle", "item.fireworksCharge.trail": "Trail", "item.fireworksCharge.type.0": "Small Ball", "item.fireworksCharge.type.1": "Large Ball", "item.fireworksCharge.type.2": "Star-shaped", "item.fireworksCharge.type.3": "Creeper-shaped", "item.fireworksCharge.type.4": "<PERSON><PERSON><PERSON>", "item.fireworksCharge.type": "Unknown <PERSON><PERSON>pe", "item.netherbrick.name": "Nether Brick", "item.netherquartz.name": "<PERSON><PERSON>", "item.armorStand.name": "Armor Stand", "item.horsearmormetal.name": "Iron Horse Armor", "item.horsearmorgold.name": "Gold Horse Armor", "item.horsearmordiamond.name": "Diamond Horse Armor", "item.prismarineShard.name": "<PERSON><PERSON><PERSON><PERSON>", "item.prismarineCrystals.name": "Prismarine Crystals", "item.chorusFruit.name": "Chorus Fruit", "item.chorusFruitPopped.name": "Popped Chorus Fruit", "item.beetroot.name": "Beetroot", "item.beetroot_seeds.name": "Beetroot Seeds", "item.beetroot_soup.name": "Beetroot Soup", "item.dragon_breath.name": "Dragon's Breath", "item.elytra.name": "Elytra", "container.inventory": "Inventory", "container.hopper": "<PERSON><PERSON>", "container.crafting": "Crafting", "container.dispenser": "Dispenser", "container.dropper": "Dropper", "container.furnace": "Furnace", "container.enchant": "Enchant", "container.enchant.lapis.one": "1 Lapis Lazuli", "container.enchant.lapis.many": "%d <PERSON><PERSON>", "container.enchant.level.one": "1 Enchantment Level", "container.enchant.level.many": "%d Enchantment Levels", "container.enchant.clue": "%s . . . ?", "container.repair": "Repair & Name", "container.repair.cost": "Enchantment Cost: %1$d", "container.repair.expensive": "Too Expensive!", "container.creative": "Item Selection", "container.brewing": "Brewing Stand", "container.chest": "Chest", "container.chestDouble": "Large Chest", "container.enderchest": "<PERSON><PERSON> Chest", "container.beacon": "Beacon", "container.spectatorCantOpen": "Unable to open. Loot not generated yet.", "container.isLocked": "%s is locked!", "structure_block.save_success": "Structure saved as '%s'", "structure_block.save_failure": "Unable to save structure '%s'", "structure_block.load_success": "Structure loaded from '%s'", "structure_block.load_prepare": "Structure '%s' position prepared", "structure_block.load_not_found": "Structure '%s' is not available", "structure_block.size_success": "<PERSON><PERSON> successfully detected for '%s'", "structure_block.size_failure": "Unable to detect structure size, add corners with matching structure names", "structure_block.mode.save": "[S]", "structure_block.mode.load": "[L]", "structure_block.mode.data": "[D]", "structure_block.mode.corner": "[C]", "structure_block.hover.save": "Save: %s", "structure_block.hover.load": "Load: %s", "structure_block.hover.data": "Data: %s", "structure_block.hover.corner": "Corner: %s", "structure_block.mode_info.save": "Save mode - write to file", "structure_block.mode_info.load": "Load mode - load from file", "structure_block.mode_info.data": "Data mode - game logic marker", "structure_block.mode_info.corner": "Corner mode - placement and size marker", "structure_block.structure_name": "Structure Name", "structure_block.custom_data": "Custom Data Tag Name", "structure_block.position": "Relative Position", "structure_block.size": "Structure Size", "structure_block.integrity": "Structure Integrity and Seed", "structure_block.include_entities": "Include entities:", "structure_block.detect_size": "Detect structure size and position:", "structure_block.button.detect_size": "DETECT", "structure_block.button.save": "SAVE", "structure_block.button.load": "LOAD", "structure_block.show_air": "Show invisible blocks:", "structure_block.show_boundingbox": "Show bounding box:", "item.dyed": "Dyed", "item.unbreakable": "Unbreakable", "item.canBreak": "Can break:", "item.canPlace": "Can be placed on:", "entity.Item.name": "<PERSON><PERSON>", "entity.XPOrb.name": "Experience Orb", "entity.SmallFireball.name": "Small Fireball", "entity.Fireball.name": "Fireball", "entity.DragonFireball.name": "Dragon Fireball", "entity.ThrownPotion.name": "Potion", "entity.Arrow.name": "Arrow", "entity.Snowball.name": "Snowball", "entity.Painting.name": "Painting", "entity.ArmorStand.name": "Armor Stand", "entity.Mob.name": "<PERSON><PERSON>", "entity.Monster.name": "Monster", "entity.Creeper.name": "C<PERSON>per", "entity.Skeleton.name": "Skeleton", "entity.WitherSkeleton.name": "<PERSON><PERSON>", "entity.Stray.name": "Stray", "entity.Spider.name": "Spider", "entity.Giant.name": "Giant", "entity.Zombie.name": "Zombie", "entity.Husk.name": "Husk", "entity.Slime.name": "Slime", "entity.Ghast.name": "<PERSON><PERSON><PERSON>", "entity.PigZombie.name": "Zombie Pigman", "entity.Enderman.name": "<PERSON><PERSON>", "entity.Endermite.name": "Endermite", "entity.Silverfish.name": "Silverfish", "entity.CaveSpider.name": "<PERSON> Spider", "entity.Blaze.name": "Blaze", "entity.LavaSlime.name": "Magma Cube", "entity.MushroomCow.name": "Mooshroom", "entity.Villager.name": "Villager", "entity.VillagerGolem.name": "Iron Golem", "entity.SnowMan.name": "Snow Golem", "entity.EnderDragon.name": "<PERSON><PERSON>", "entity.WitherBoss.name": "<PERSON>er", "entity.Witch.name": "Witch", "entity.Guardian.name": "Guardian", "entity.Shulker.name": "<PERSON><PERSON><PERSON>", "entity.PolarBear.name": "Polar Bear", "entity.Villager.farmer": "<PERSON>", "entity.Villager.fisherman": "Fisherman", "entity.Villager.shepherd": "<PERSON>", "entity.Villager.fletcher": "<PERSON>", "entity.Villager.librarian": "Librarian", "entity.Villager.cleric": "Cleric", "entity.Villager.armor": "<PERSON><PERSON><PERSON>", "entity.Villager.weapon": "<PERSON><PERSON>", "entity.Villager.tool": "<PERSON><PERSON>", "entity.Villager.butcher": "<PERSON>", "entity.Villager.leather": "Leatherworker", "entity.Pig.name": "Pig", "entity.Sheep.name": "Sheep", "entity.Cow.name": "Cow", "entity.Chicken.name": "Chicken", "entity.Squid.name": "Squid", "entity.Wolf.name": "<PERSON>", "entity.Ozelot.name": "Ocelot", "entity.Cat.name": "Cat", "entity.Bat.name": "Bat", "entity.EntityHorse.name": "Horse", "entity.Donkey.name": "<PERSON><PERSON>", "entity.Mule.name": "<PERSON><PERSON>", "entity.SkeletonHorse.name": "Skeleton Horse", "entity.ZombieHorse.name": "Zombie Horse", "entity.Rabbit.name": "Rabbit", "entity.KillerBunny.name": "The Killer Bunny", "entity.PrimedTnt.name": "Block of TNT", "entity.FallingSand.name": "Falling Block", "entity.Minecart.name": "Minecart", "entity.MinecartHopper.name": "Minecart with <PERSON>", "entity.MinecartChest.name": "Minecart with Chest", "entity.Boat.name": "Boat", "entity.generic.name": "unknown", "death.fell.accident.ladder": "%1$s fell off a ladder", "death.fell.accident.vines": "%1$s fell off some vines", "death.fell.accident.water": "%1$s fell out of the water", "death.fell.accident.generic": "%1$s fell from a high place", "death.fell.killer": "%1$s was doomed to fall", "death.fell.assist": "%1$s was doomed to fall by %2$s", "death.fell.assist.item": "%1$s was doomed to fall by %2$s using %3$s", "death.fell.finish": "%1$s fell too far and was finished by %2$s", "death.fell.finish.item": "%1$s fell too far and was finished by %2$s using %3$s", "death.attack.lightningBolt": "%1$s was struck by lightning", "death.attack.inFire": "%1$s went up in flames", "death.attack.inFire.player": "%1$s walked into fire whilst fighting %2$s", "death.attack.onFire": "%1$s burned to death", "death.attack.onFire.player": "%1$s was burnt to a crisp whilst fighting %2$s", "death.attack.lava": "%1$s tried to swim in lava", "death.attack.lava.player": "%1$s tried to swim in lava to escape %2$s", "death.attack.hotFloor": "%1$s discovered floor was lava", "death.attack.hotFloor.player": "%1$s walked into danger zone due to %2$s", "death.attack.inWall": "%1$s suffocated in a wall", "death.attack.drown": "%1$s drowned", "death.attack.drown.player": "%1$s drowned whilst trying to escape %2$s", "death.attack.starve": "%1$s starved to death", "death.attack.cactus": "%1$s was pricked to death", "death.attack.cactus.player": "%1$s walked into a cactus whilst trying to escape %2$s", "death.attack.generic": "%1$s died", "death.attack.explosion": "%1$s blew up", "death.attack.explosion.player": "%1$s was blown up by %2$s", "death.attack.magic": "%1$s was killed by magic", "death.attack.wither": "%1$s withered away", "death.attack.anvil": "%1$s was squashed by a falling anvil", "death.attack.fallingBlock": "%1$s was squashed by a falling block", "death.attack.mob": "%1$s was slain by %2$s", "death.attack.player": "%1$s was slain by %2$s", "death.attack.player.item": "%1$s was slain by %2$s using %3$s", "death.attack.arrow": "%1$s was shot by %2$s", "death.attack.arrow.item": "%1$s was shot by %2$s using %3$s", "death.attack.fireball": "%1$s was fireballed by %2$s", "death.attack.fireball.item": "%1$s was fireballed by %2$s using %3$s", "death.attack.thrown": "%1$s was pummeled by %2$s", "death.attack.thrown.item": "%1$s was pummeled by %2$s using %3$s", "death.attack.indirectMagic": "%1$s was killed by %2$s using magic", "death.attack.indirectMagic.item": "%1$s was killed by %2$s using %3$s", "death.attack.thorns": "%1$s was killed trying to hurt %2$s", "death.attack.fall": "%1$s hit the ground too hard", "death.attack.outOfWorld": "%1$s fell out of the world", "death.attack.dragonBreath": "%1$s was roasted in dragon breath", "death.attack.flyIntoWall": "%1$s experienced kinetic energy", "deathScreen.respawn": "Respawn", "deathScreen.spectate": "Spectate world", "deathScreen.deleteWorld": "Delete world", "deathScreen.titleScreen": "Title screen", "deathScreen.score": "Score", "deathScreen.title.hardcore": "Game over!", "deathScreen.title": "You died!", "deathScreen.leaveServer": "Leave server", "deathScreen.quit.confirm": "Are you sure you want to quit?", "effect.none": "No Effects", "effect.moveSpeed": "Speed", "effect.moveSlowdown": "Slowness", "effect.digSpeed": "<PERSON><PERSON>", "effect.digSlowDown": "Mining Fatigue", "effect.damageBoost": "Strength", "effect.heal": "Instant Health", "effect.harm": "Instant Damage", "effect.jump": "Jump Boost", "effect.confusion": "<PERSON><PERSON><PERSON>", "effect.regeneration": "Regeneration", "effect.resistance": "Resistance", "effect.fireResistance": "Fire Resistance", "effect.waterBreathing": "Water Breathing", "effect.invisibility": "Invisibility", "effect.blindness": "Blindness", "effect.nightVision": "Night Vision", "effect.hunger": "Hunger", "effect.weakness": "Weakness", "effect.poison": "Poison", "effect.wither": "<PERSON>er", "effect.healthBoost": "Health Boost", "effect.absorption": "Absorption", "effect.saturation": "Saturation", "effect.glowing": "Glowing", "effect.luck": "Luck", "effect.unluck": "Bad Luck", "effect.levitation": "Levitation", "tipped_arrow.effect.empty": "Tipped Arrow", "tipped_arrow.effect.water": "Arrow of Splashing", "tipped_arrow.effect.mundane": "Tipped Arrow", "tipped_arrow.effect.thick": "Tipped Arrow", "tipped_arrow.effect.awkward": "Tipped Arrow", "tipped_arrow.effect.night_vision": "Arrow of Night Vision", "tipped_arrow.effect.invisibility": "Arrow of Invisibility", "tipped_arrow.effect.leaping": "Arrow of Leaping", "tipped_arrow.effect.fire_resistance": "Arrow of Fire Resistance", "tipped_arrow.effect.swiftness": "Arrow of Swiftness", "tipped_arrow.effect.slowness": "Arrow of Slowness", "tipped_arrow.effect.water_breathing": "Arrow of Water Breathing", "tipped_arrow.effect.healing": "Arrow of Healing", "tipped_arrow.effect.harming": "Arrow of Harming", "tipped_arrow.effect.poison": "Arrow of Poison", "tipped_arrow.effect.regeneration": "Arrow of Regeneration", "tipped_arrow.effect.strength": "Arrow of Strength", "tipped_arrow.effect.weakness": "Arrow of Weakness", "tipped_arrow.effect.levitation": "Arrow of Levitation", "tipped_arrow.effect.luck": "Arrow of Luck", "potion.whenDrank": "When Applied:", "potion.effect.empty": "Uncraftable Potion", "potion.effect.water": "Water Bottle", "potion.effect.mundane": "<PERSON><PERSON>ne <PERSON>", "potion.effect.thick": "Thick Potion", "potion.effect.awkward": "Awkward Potion", "potion.effect.night_vision": "Potion of Night Vision", "potion.effect.invisibility": "Potion of Invisibility", "potion.effect.leaping": "Potion of Leaping", "potion.effect.fire_resistance": "Potion of Fire Resistance", "potion.effect.swiftness": "Potion of Swiftness", "potion.effect.slowness": "Potion of Slowness", "potion.effect.water_breathing": "Potion of Water Breathing", "potion.effect.healing": "Potion of Healing", "potion.effect.harming": "Potion of Harming", "potion.effect.poison": "Potion of Poison", "potion.effect.regeneration": "Potion of Regeneration", "potion.effect.strength": "Potion of Strength", "potion.effect.weakness": "Potion of Weakness", "potion.effect.levitation": "Potion of Levitation", "potion.effect.luck": "Potion of Luck", "splash_potion.effect.empty": "Splash Uncraftable Potion", "splash_potion.effect.water": "Splash Water Bottle", "splash_potion.effect.mundane": "<PERSON><PERSON>ne Splash Potion", "splash_potion.effect.thick": "T<PERSON>k Splash Potion", "splash_potion.effect.awkward": "Awkward Splash Potion", "splash_potion.effect.night_vision": "Splash Potion of Night Vision", "splash_potion.effect.invisibility": "Splash Potion of Invisibility", "splash_potion.effect.leaping": "Splash Potion of Leaping", "splash_potion.effect.fire_resistance": "Splash Potion of Fire Resistance", "splash_potion.effect.swiftness": "Splash Potion of Swiftness", "splash_potion.effect.slowness": "Splash Potion of Slowness", "splash_potion.effect.water_breathing": "Splash Potion of Water Breathing", "splash_potion.effect.healing": "Splash Potion of Healing", "splash_potion.effect.harming": "Splash Potion of Harming", "splash_potion.effect.poison": "Splash Potion of Poison", "splash_potion.effect.regeneration": "Splash Potion of Regeneration", "splash_potion.effect.strength": "Splash Potion of Strength", "splash_potion.effect.weakness": "Splash Potion of Weakness", "splash_potion.effect.levitation": "Splash Potion of Levitation", "splash_potion.effect.luck": "Splash Potion of Luck", "lingering_potion.effect.empty": "Lingering Uncraftable Potion", "lingering_potion.effect.water": "Lingering Water Bottle", "lingering_potion.effect.mundane": "Mu<PERSON>ne Lingering Potion", "lingering_potion.effect.thick": "T<PERSON>k Lingering Potion", "lingering_potion.effect.awkward": "Awkward Lingering Potion", "lingering_potion.effect.night_vision": "Lingering Potion of Night Vision", "lingering_potion.effect.invisibility": "Lingering Potion of Invisibility", "lingering_potion.effect.leaping": "Lingering Potion of Leaping", "lingering_potion.effect.fire_resistance": "Lingering Potion of Fire Resistance", "lingering_potion.effect.swiftness": "Lingering Potion of Swiftness", "lingering_potion.effect.slowness": "Lingering Potion of Slowness", "lingering_potion.effect.water_breathing": "Lingering Potion of Water Breathing", "lingering_potion.effect.healing": "Lingering Potion of Healing", "lingering_potion.effect.harming": "Lingering Potion of Harming", "lingering_potion.effect.poison": "Lingering Potion of Poison", "lingering_potion.effect.regeneration": "Lingering Potion of Regeneration", "lingering_potion.effect.strength": "Lingering Potion of Strength", "lingering_potion.effect.weakness": "Lingering Potion of Weakness", "lingering_potion.effect.levitation": "Lingering Potion of Levitation", "lingering_potion.effect.luck": "Lingering Potion of Luck", "potion.potency.1": "II", "potion.potency.2": "III", "enchantment.damage.all": "Sharpness", "enchantment.damage.undead": "Smite", "enchantment.damage.arthropods": "Bane of Arthropods", "enchantment.knockback": "K<PERSON><PERSON>", "enchantment.fire": "Fire Aspect", "enchantment.protect.all": "Protection", "enchantment.protect.fire": "Fire Protection", "enchantment.protect.fall": "Feather Falling", "enchantment.protect.explosion": "Blast Protection", "enchantment.protect.projectile": "Projectile Protection", "enchantment.oxygen": "Respiration", "enchantment.waterWorker": "Aqua Affinity", "enchantment.waterWalker": "Depth Strider", "enchantment.frostWalker": "<PERSON>", "enchantment.digging": "Efficiency", "enchantment.untouching": "Silk Touch", "enchantment.durability": "Unbreaking", "enchantment.lootBonus": "Looting", "enchantment.lootBonusDigger": "Fortune", "enchantment.lootBonusFishing": "Luck of the Sea", "enchantment.fishingSpeed": "<PERSON><PERSON>", "enchantment.arrowDamage": "Power", "enchantment.arrowFire": "Flame", "enchantment.arrowKnockback": "Punch", "enchantment.arrowInfinite": "Infinity", "enchantment.thorns": "Thorns", "enchantment.mending": "Mending", "enchantment.level.1": "I", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.level.10": "X", "gui.achievements": "Achievements", "gui.stats": "Statistics", "stats.tooltip.type.achievement": "Achievement", "stats.tooltip.type.statistic": "Statistic", "stat.generalButton": "General", "stat.blocksButton": "Blocks", "stat.itemsButton": "Items", "stat.mobsButton": "<PERSON><PERSON>", "stat.used": "Times Used", "stat.mined": "Times Mined", "stat.depleted": "Times Depleted", "stat.crafted": "Times Crafted", "stat.entityKills": "You killed %d %s", "stat.entityKilledBy": "%s killed you %d time(s)", "stat.entityKills.none": "You have never killed %s", "stat.entityKilledBy.none": "You have never been killed by %s", "stat.startGame": "Times played", "stat.createWorld": "Worlds created", "stat.loadWorld": "Saves loaded", "stat.joinMultiplayer": "Multiplayer joins", "stat.leaveGame": "Games quit", "stat.playOneMinute": "Minutes Played", "stat.timeSinceDeath": "Since Last Death", "stat.sneakTime": "Sneak Time", "stat.walkOneCm": "Distance Walked", "stat.crouchOneCm": "Distance Crouched", "stat.sprintOneCm": "Distance Sprinted", "stat.fallOneCm": "Distance Fallen", "stat.swimOneCm": "Distance Swum", "stat.flyOneCm": "Distance Flown", "stat.climbOneCm": "Distance Climbed", "stat.diveOneCm": "Distance Dove", "stat.minecartOneCm": "Distance by Minecart", "stat.boatOneCm": "Distance by Boat", "stat.pigOneCm": "Distance by Pig", "stat.horseOneCm": "Distance by Horse", "stat.aviateOneCm": "Distance by Elytra", "stat.jump": "Jumps", "stat.drop": "Items Dropped", "stat.dropped": "Dropped", "stat.pickup": "Picked Up", "stat.damageDealt": "Damage Dealt", "stat.damageTaken": "Damage Taken", "stat.deaths": "Number of Deaths", "stat.mobKills": "<PERSON><PERSON>", "stat.animalsBred": "Animals Bred", "stat.playerKills": "Player Kills", "stat.fishCaught": "<PERSON> Caught", "stat.treasureFished": "Treasure Fished", "stat.junkFished": "Jun<PERSON> Fished", "stat.talkedToVillager": "Talked to Villagers", "stat.tradedWithVillager": "Traded with Villagers", "stat.cakeSlicesEaten": "Cake Slices Eaten", "stat.cauldronFilled": "Cauldrons Filled", "stat.cauldronUsed": "Water Taken from Cauldron", "stat.armorCleaned": "Armor Pieces Cleaned", "stat.bannerCleaned": "Banners Cleaned", "stat.brewingstandInteraction": "Interactions with Brewing Stand", "stat.beaconInteraction": "Interactions with Beacon", "stat.dropperInspected": "Droppers Searched", "stat.hopperInspected": "Hoppers Searched", "stat.dispenserInspected": "Dispensers Searched", "stat.noteblockPlayed": "Noteblocks played", "stat.noteblockTuned": "Noteblocks tuned", "stat.flowerPotted": "Plants potted", "stat.trappedChestTriggered": "Trapped Chests Triggered", "stat.enderchestOpened": "<PERSON><PERSON> Chests Opened", "stat.itemEnchanted": "Items Enchanted", "stat.recordPlayed": "Records Played", "stat.furnaceInteraction": "Interactions with Furnace", "stat.workbenchInteraction": "Interactions with Crafting Table", "stat.chestOpened": "Chests Opened", "stat.sleepInBed": "Times Slept in a Bed", "stat.mineBlock": "%1$s Mined", "stat.craftItem": "%1$s Crafted", "stat.useItem": "%1$s Used", "stat.breakItem": "%1$s Depleted", "achievement.get": "Achievement get!", "achievement.taken": "Taken!", "achievement.unknown": "???", "achievement.requires": "Requires '%1$s'", "achievement.openInventory": "Taking Inventory", "achievement.openInventory.desc": "Press '%1$s' to open your inventory", "achievement.mineWood": "<PERSON>", "achievement.mineWood.desc": "Attack a tree until a block of wood pops out", "achievement.buildWorkBench": "Benchmarking", "achievement.buildWorkBench.desc": "Craft a workbench with four blocks of planks", "achievement.buildPickaxe": "Time to Mine!", "achievement.buildPickaxe.desc": "Use planks and sticks to make a pickaxe", "achievement.buildFurnace": "Hot Topic", "achievement.buildFurnace.desc": "Construct a furnace out of eight cobblestone blocks", "achievement.acquireIron": "Acquire Hardware", "achievement.acquireIron.desc": "Smelt an iron ingot", "achievement.buildHoe": "Time to Farm!", "achievement.buildHoe.desc": "Use planks and sticks to make a hoe", "achievement.makeBread": "Bake B<PERSON>", "achievement.makeBread.desc": "Turn wheat into bread", "achievement.bakeCake": "The Lie", "achievement.bakeCake.desc": "Wheat, sugar, milk and eggs!", "achievement.buildBetterPickaxe": "Getting an Upgrade", "achievement.buildBetterPickaxe.desc": "Construct a better pickaxe", "achievement.overpowered": "Overpowered", "achievement.overpowered.desc": "Eat a Notch apple", "achievement.cookFish": "Delicious Fish", "achievement.cookFish.desc": "Catch and cook fish!", "achievement.onARail": "On A Rail", "achievement.onARail.desc": "Travel by minecart at least 1 km from where you started", "achievement.buildSword": "Time to Strike!", "achievement.buildSword.desc": "Use planks and sticks to make a sword", "achievement.killEnemy": "Monster Hunter", "achievement.killEnemy.desc": "Attack and destroy a monster", "achievement.killCow": "Cow Tipper", "achievement.killCow.desc": "Harvest some leather", "achievement.breedCow": "Repopulation", "achievement.breedCow.desc": "Breed two cows with wheat", "achievement.flyPig": "When Pigs Fly", "achievement.flyPig.desc": "Fly a pig off a cliff", "achievement.snipeSkeleton": "<PERSON><PERSON><PERSON>", "achievement.snipeSkeleton.desc": "Kill a skeleton with an arrow from more than 50 meters", "achievement.diamonds": "DIAMONDS!", "achievement.diamonds.desc": "Acquire diamonds with your iron tools", "achievement.diamondsToYou": "Diamonds to you!", "achievement.diamondsToYou.desc": "Throw diamonds at another player", "achievement.portal": "We Need to Go Deeper", "achievement.portal.desc": "Build a portal to the Nether", "achievement.ghast": "Return to Sender", "achievement.ghast.desc": "<PERSON><PERSON>y a <PERSON><PERSON><PERSON> with a fireball", "achievement.blazeRod": "Into Fire", "achievement.blazeRod.desc": "Relieve a Blaze of its rod", "achievement.potion": "Local Brewery", "achievement.potion.desc": "Brew a potion", "achievement.theEnd": "The End?", "achievement.theEnd.desc": "Locate the End", "achievement.theEnd2": "The End.", "achievement.theEnd2.desc": "Defeat the Ender Dragon", "achievement.spawnWither": "The Beginning?", "achievement.spawnWither.desc": "Spawn the Wither", "achievement.killWither": "The Beginning.", "achievement.killWither.desc": "<PERSON> the Wither", "achievement.fullBeacon": "Beaconator", "achievement.fullBeacon.desc": "Create a full beacon", "achievement.exploreAllBiomes": "Adventuring Time", "achievement.exploreAllBiomes.desc": "Discover all biomes", "achievement.enchantments": "Enchanter", "achievement.enchantments.desc": "Use a book, obsidian and diamonds to construct an enchantment table", "achievement.overkill": "Overkill", "achievement.overkill.desc": "Deal nine hearts of damage in a single hit", "achievement.bookcase": "Librarian", "achievement.bookcase.desc": "Build some bookshelves to improve your enchantment table", "commands.generic.exception": "An unknown error occurred while attempting to perform this command", "commands.generic.permission": "You do not have permission to use this command", "commands.generic.syntax": "Invalid command syntax", "commands.generic.player.notFound": "That player cannot be found", "commands.generic.entity.notFound": "That entity cannot be found", "commands.generic.entity.invalidUuid": "The entity UUID provided is in an invalid format", "commands.generic.entity.invalidType": "Entity type '%s' is invalid", "commands.generic.notFound": "Unknown command. Try /help for a list of commands", "commands.generic.parameter.invalid": "'%s' is not a valid parameter", "commands.generic.num.invalid": "'%s' is not a valid number", "commands.generic.boolean.invalid": "'%s' is not true or false", "commands.generic.num.tooSmall": "The number you have entered (%d) is too small, it must be at least %d", "commands.generic.num.tooBig": "The number you have entered (%d) is too big, it must be at most %d", "commands.generic.double.tooSmall": "The number you have entered (%.2f) is too small, it must be at least %.2f", "commands.generic.double.tooBig": "The number you have entered (%.2f) is too big, it must be at most %.2f", "commands.generic.usage": "Usage: %s", "commands.setidletimeout.usage": "/setidletimeout <Minutes until kick>", "commands.setidletimeout.success": "Successfully set the idle timeout to %d minutes.", "commands.xp.failure.widthdrawXp": "Cannot give player negative experience points", "commands.xp.success": "Given %d experience to %s", "commands.xp.success.levels": "Given %d levels to %s", "commands.xp.success.negative.levels": "Taken %d levels from %s", "commands.xp.usage": "/xp <amount> [player] OR /xp <amount>L [player]", "commands.playsound.usage": "/playsound <sound> <source> <player> [x] [y] [z] [volume] [pitch] [minimumVolume]", "commands.playsound.success": "Played sound '%s' to %s", "commands.playsound.playerTooFar": "Player %s is too far away to hear the sound", "commands.playsound.unknownSoundSource": "Source %s is unknown", "commands.give.usage": "/give <player> <item> [amount] [data] [dataTag]", "commands.give.item.notFound": "There is no such item with name %d", "commands.give.block.notFound": "There is no such block with name %d", "commands.give.success": "Given %s * %d to %s", "commands.give.tagError": "Data tag parsing failed: %s", "commands.replaceitem.usage": "/replaceitem <entity|block> ...", "commands.replaceitem.entity.usage": "/replaceitem entity <selector> <slot> <item> [amount] [data] [dataTag]", "commands.replaceitem.block.usage": "/replaceitem block <x> <y> <z> <slot> <item> [amount] [data] [dataTag]", "commands.replaceitem.tagError": "Data tag parsing failed: %s", "commands.replaceitem.noContainer": "Block at %d, %d, %d is not a container", "commands.replaceitem.failed": "Could not replace slot %d with %d * %s", "commands.replaceitem.success": "Replaced slot %d with %d * %s", "commands.stats.usage": "/stats <entity|block> ...", "commands.stats.entity.usage": "/stats entity <selector> <mode> ...", "commands.stats.entity.set.usage": "/stats entity <selector> set <stat> <selector> <objective>", "commands.stats.entity.clear.usage": "/stats entity <selector> clear <stat>", "commands.stats.block.usage": "/stats block <x> <y> <z> <mode> ...", "commands.stats.block.set.usage": "/stats block <x> <y> <z> set <stat> <selector> <objective>", "commands.stats.block.clear.usage": "/stats block <x> <y> <z> clear <stat>", "commands.stats.noCompatibleBlock": "Block at %d, %d, %d can not track stats", "commands.stats.failed": "Invalid parameters", "commands.stats.cleared": "Cleared %s stats", "commands.stats.success": "Storing %s stats in %s on %s", "commands.summon.usage": "/summon <EntityName> [x] [y] [z] [dataTag]", "commands.summon.success": "Object successfully summoned", "commands.summon.failed": "Unable to summon object", "commands.summon.tagError": "Data tag parsing failed: %s", "commands.summon.outOfWorld": "Cannot summon the object out of the world", "commands.testforblock.usage": "/testforblock <x> <y> <z> <TileName> [dataValue] [dataTag]", "commands.testforblock.failed.tile": "The block at %d, %d, %d is %s (expected: %s)", "commands.testforblock.failed.data": "The block at %d, %d, %d had the data value of %s (expected: %s)", "commands.testforblock.failed.nbt": "The block at %d, %d, %d did not have the required NBT keys", "commands.testforblock.failed.tileEntity": "The block at %d, %d, %d is not a block entity and cannot support tag matching", "commands.testforblock.success": "Successfully found the block at %d, %d, %d", "commands.testforblock.outOfWorld": "Cannot test for block outside of the world", "commands.setblock.usage": "/setblock <x> <y> <z> <TileName> [dataValue] [oldBlockHandling] [dataTag]", "commands.setblock.success": "Block placed", "commands.setblock.failed": "Unable to place block", "commands.setblock.tagError": "Data tag parsing failed: %s", "commands.setblock.outOfWorld": "Cannot place block outside of the world", "commands.setblock.notFound": "There is no such block with ID/name %s", "commands.setblock.noChange": "The block couldn't be placed", "commands.fill.usage": "/fill <x1> <y1> <z1> <x2> <y2> <z2> <TileName> [dataValue] [oldBlockHandling] [dataTag]", "commands.fill.outOfWorld": "Cannot place blocks outside of the world", "commands.fill.tagError": "Data tag parsing failed: %s", "commands.fill.success": "%d blocks filled", "commands.fill.failed": "No blocks filled", "commands.fill.tooManyBlocks": "Too many blocks in the specified area (%d > %d)", "commands.clone.usage": "/clone <x1> <y1> <z1> <x2> <y2> <z2> <x> <y> <z> [maskMode] [cloneMode]", "commands.clone.outOfWorld": "Cannot access blocks outside of the world", "commands.clone.noOverlap": "Source and destination can not overlap", "commands.clone.success": "%d blocks cloned", "commands.clone.failed": "No blocks cloned", "commands.clone.tooManyBlocks": "Too many blocks in the specified area (%d > %d)", "commands.compare.usage": "/testforblocks <x1> <y1> <z1> <x2> <y2> <z2> <x> <y> <z> [mode]", "commands.compare.outOfWorld": "Cannot access blocks outside of the world", "commands.compare.failed": "Source and destination are not identical", "commands.compare.success": "%d blocks compared", "commands.compare.tooManyBlocks": "Too many blocks in the specified area (%d > %d)", "commands.blockdata.usage": "/blockdata <x> <y> <z> <dataTag>", "commands.blockdata.success": "Block data updated to: %s", "commands.blockdata.tagError": "Data tag parsing failed: %s", "commands.blockdata.outOfWorld": "Cannot change block outside of the world", "commands.blockdata.notValid": "The target block is not a data holder block", "commands.blockdata.failed": "The data tag did not change: %s", "commands.entitydata.usage": "/entitydata <entity> <dataTag>", "commands.entitydata.success": "Entity data updated to: %s", "commands.entitydata.tagError": "Data tag parsing failed: %s", "commands.entitydata.noPlayers": "%s is a player and cannot be changed", "commands.entitydata.failed": "The data tag did not change: %s", "commands.effect.usage": "/effect <player> <effect> [seconds] [amplifier] [hideParticles] OR /effect <player> clear", "commands.effect.notFound": "There is no such mob effect with ID %d", "commands.effect.success": "Given %1$s (ID %2$d) * %3$d to %4$s for %5$d seconds", "commands.effect.success.removed": "Took %1$s from %2$s", "commands.effect.success.removed.all": "Took all effects from %s", "commands.effect.failure.notActive": "Couldn't take %1$s from %2$s as they do not have the effect", "commands.effect.failure.notActive.all": "Couldn't take any effects from %s as they do not have any", "commands.enchant.usage": "/enchant <player> <enchantment ID> [level]", "commands.enchant.notFound": "There is no such enchantment with ID %d", "commands.enchant.noItem": "The target doesn't hold an item", "commands.enchant.cantEnchant": "The selected enchantment can't be added to the target item", "commands.enchant.cantCombine": "%1$s can't be combined with %2$s", "commands.enchant.success": "Enchanting succeeded", "commands.particle.usage": "/particle <name> <x> <y> <z> <xd> <yd> <zd> <speed> [count] [mode] [player] [params]", "commands.particle.success": "Playing effect %s for %d times", "commands.particle.notFound": "Unknown effect name (%s)", "commands.particle.invalidParam": "Invalid param given (%s)", "commands.clear.usage": "/clear [player] [item] [data] [maxCount] [dataTag]", "commands.clear.success": "Cleared the inventory of %s, removing %d items", "commands.clear.testing": "%s has %d items that match the criteria", "commands.clear.failure": "Could not clear the inventory of %s, no items to remove", "commands.clear.tagError": "Data tag parsing failed: %s", "commands.downfall.usage": "/toggledownfall", "commands.downfall.success": "Toggled downfall", "commands.time.usage": "/time <set|add|query> <value>", "commands.time.added": "Added %d to the time", "commands.time.set": "Set the time to %d", "commands.time.query": "Time is %d", "commands.players.usage": "/list", "commands.players.list": "There are %d/%d players online:", "commands.banlist.ips": "There are %d total banned IP addresses:", "commands.banlist.players": "There are %d total banned players:", "commands.banlist.usage": "/banlist [ips|players]", "commands.kill.usage": "/kill [player|entity]", "commands.kill.successful": "Killed %s", "commands.kick.success": "Kicked %s from the game", "commands.kick.success.reason": "Kicked %s from the game: '%s'", "commands.kick.usage": "/kick <player> [reason ...]", "commands.op.success": "Opped %s", "commands.op.failed": "Could not op %s", "commands.op.usage": "/op <player>", "commands.deop.success": "De-opped %s", "commands.deop.failed": "Could not de-op %s", "commands.deop.usage": "/deop <player>", "commands.say.usage": "/say <message ...>", "commands.ban.success": "Banned player %s", "commands.ban.failed": "Could not ban player %s", "commands.ban.usage": "/ban <name> [reason ...]", "commands.unban.success": "Unbanned player %s", "commands.unban.failed": "Could not unban player %s", "commands.unban.usage": "/pardon <name>", "commands.banip.invalid": "You have entered an invalid IP address or a player that is not online", "commands.banip.success": "Banned IP address %s", "commands.banip.success.players": "Banned IP address %s belonging to %s", "commands.banip.usage": "/ban-ip <address|name> [reason ...]", "commands.unbanip.invalid": "You have entered an invalid IP address", "commands.unbanip.success": "Unbanned IP address %s", "commands.unbanip.usage": "/pardon-ip <address>", "commands.save.usage": "/save-all", "commands.save-on.alreadyOn": "Saving is already turned on", "commands.save-on.usage": "/save-on", "commands.save-off.alreadyOff": "Saving is already turned off", "commands.save-off.usage": "/save-off", "commands.save.enabled": "Turned on world auto-saving", "commands.save.disabled": "Turned off world auto-saving", "commands.save.start": "Saving...", "commands.save.success": "Saved the world", "commands.save.failed": "Saving failed: %s", "commands.stop.usage": "/stop", "commands.stop.start": "Stopping the server", "commands.tp.usage": "/tp [target player] <destination player> OR /tp [target player] <x> <y> <z> [<yaw> <pitch>]", "commands.tp.success": "Teleported %s to %s", "commands.tp.success.coordinates": "Teleported %s to %s, %s, %s", "commands.tp.notSameDimension": "Unable to teleport because players are not in the same dimension", "commands.teleport.usage": "/teleport <entity> <x> <y> <z> [<y-rot> <x-rot>]", "commands.teleport.success.coordinates": "Teleported %s to %s, %s, %s", "commands.whitelist.list": "There are %d (out of %d seen) whitelisted players:", "commands.whitelist.enabled": "Turned on the whitelist", "commands.whitelist.disabled": "Turned off the whitelist", "commands.whitelist.reloaded": "Reloaded the whitelist", "commands.whitelist.add.success": "Added %s to the whitelist", "commands.whitelist.add.failed": "Could not add %s to the whitelist", "commands.whitelist.add.usage": "/whitelist add <player>", "commands.whitelist.remove.success": "Removed %s from the whitelist", "commands.whitelist.remove.failed": "Could not remove %s from the whitelist", "commands.whitelist.remove.usage": "/whitelist remove <player>", "commands.whitelist.usage": "/whitelist <on|off|list|add|remove|reload>", "commands.scoreboard.usage": "/scoreboard <objectives|players|teams> ...", "commands.scoreboard.noMultiWildcard": "Only one user wildcard allowed", "commands.scoreboard.allMatchesFailed": "All matches failed", "commands.scoreboard.teamNotFound": "No team was found by the name '%s'", "commands.scoreboard.objectiveNotFound": "No objective was found by the name '%s'", "commands.scoreboard.objectiveReadOnly": "The objective '%s' is read-only and cannot be set", "commands.scoreboard.objectives.usage": "/scoreboard objectives <list|add|remove|setdisplay> ...", "commands.scoreboard.objectives.setdisplay.usage": "/scoreboard objectives setdisplay <slot> [objective]", "commands.scoreboard.objectives.setdisplay.invalidSlot": "No such display slot '%s'", "commands.scoreboard.objectives.setdisplay.successCleared": "Cleared objective display slot '%s'", "commands.scoreboard.objectives.setdisplay.successSet": "Set the display objective in slot '%s' to '%s'", "commands.scoreboard.objectives.add.usage": "/scoreboard objectives add <name> <criteriaType> [display name ...]", "commands.scoreboard.objectives.add.wrongType": "Invalid objective criteria type '%s'", "commands.scoreboard.objectives.add.alreadyExists": "An objective with the name '%s' already exists", "commands.scoreboard.objectives.add.tooLong": "The name '%s' is too long for an objective, it can be at most %d characters long", "commands.scoreboard.objectives.add.displayTooLong": "The display name '%s' is too long for an objective, it can be at most %d characters long", "commands.scoreboard.objectives.add.success": "Added new objective '%s' successfully", "commands.scoreboard.objectives.remove.usage": "/scoreboard objectives remove <name>", "commands.scoreboard.objectives.remove.success": "Removed objective '%s' successfully", "commands.scoreboard.objectives.list.count": "Showing %d objective(s) on scoreboard:", "commands.scoreboard.objectives.list.entry": "- %s: displays as '%s' and is type '%s'", "commands.scoreboard.objectives.list.empty": "There are no objectives on the scoreboard", "commands.scoreboard.players.usage": "/scoreboard players <set|add|remove|reset|list|enable|test|operation|tag> ...", "commands.scoreboard.players.name.tooLong": "The name '%s' is too long for a player, it can be at most %d characters long", "commands.scoreboard.players.set.success": "Set score of %s for player %s to %d", "commands.scoreboard.players.set.tagMismatch": "The dataTag does not match for %s", "commands.scoreboard.players.set.tagError": "Could not parse dataTag, reason: %s", "commands.scoreboard.players.set.usage": "/scoreboard players set <player> <objective> <score> [dataTag]", "commands.scoreboard.players.add.usage": "/scoreboard players add <player> <objective> <count> [dataTag]", "commands.scoreboard.players.remove.usage": "/scoreboard players remove <player> <objective> <count> [dataTag]", "commands.scoreboard.players.reset.usage": "/scoreboard players reset <player> [objective]", "commands.scoreboard.players.reset.success": "Reset scores of player %s", "commands.scoreboard.players.resetscore.success": "Reset score %s of player %s", "commands.scoreboard.players.list.usage": "/scoreboard players list [name]", "commands.scoreboard.players.list.count": "Showing %d tracked players on the scoreboard:", "commands.scoreboard.players.list.empty": "There are no tracked players on the scoreboard", "commands.scoreboard.players.list.player.count": "Showing %d tracked objective(s) for %s:", "commands.scoreboard.players.list.player.entry": "- %2$s: %1$d (%3$s)", "commands.scoreboard.players.list.player.empty": "Player %s has no scores recorded", "commands.scoreboard.players.enable.usage": "/scoreboard players enable <player> <trigger>", "commands.scoreboard.players.enable.success": "Enabled trigger %s for %s", "commands.scoreboard.players.enable.noTrigger": "Objective %s is not a trigger", "commands.scoreboard.players.test.usage": "/scoreboard players test <player> <objective> <min> [max]", "commands.scoreboard.players.test.notFound": "No %s score for %s found", "commands.scoreboard.players.test.failed": "Score %d is NOT in range %d to %d", "commands.scoreboard.players.test.success": "Score %d is in range %d to %d", "commands.scoreboard.players.operation.usage": "/scoreboard players operation <targetName> <targetObjective> <operation> <selector> <objective>", "commands.scoreboard.players.operation.notFound": "No %s score for %s found", "commands.scoreboard.players.operation.invalidOperation": "Invalid operation %s", "commands.scoreboard.players.operation.success": "Operation applied successfully", "commands.scoreboard.players.tag.usage": "/scoreboard players tag <player> <add|remove|list> <tagName> [dataTag]", "commands.scoreboard.players.tag.tagMismatch": "The dataTag does not match for %s", "commands.scoreboard.players.tag.tooMany": "Can't add more than %d tags to an entity", "commands.scoreboard.players.tag.notFound": "Entity didn't have the %s tag", "commands.scoreboard.players.tag.success.add": "Tag %s added", "commands.scoreboard.players.tag.success.remove": "Tag %s removed", "commands.scoreboard.players.tag.list": "Tags on entity %s are:", "commands.scoreboard.teams.usage": "/scoreboard teams <list|add|remove|empty|join|leave|option> ...", "commands.scoreboard.teams.add.usage": "/scoreboard teams add <name> [display name ...]", "commands.scoreboard.teams.add.alreadyExists": "A team with the name '%s' already exists", "commands.scoreboard.teams.add.tooLong": "The name '%s' is too long for a team, it can be at most %d characters long", "commands.scoreboard.teams.add.displayTooLong": "The display name '%s' is too long for a team, it can be at most %d characters long", "commands.scoreboard.teams.add.success": "Added new team '%s' successfully", "commands.scoreboard.teams.list.usage": "/scoreboard teams list [name]", "commands.scoreboard.teams.list.count": "Showing %d teams on the scoreboard:", "commands.scoreboard.teams.list.entry": "- %1$s: '%2$s' has %3$d players", "commands.scoreboard.teams.list.empty": "There are no teams registered on the scoreboard", "commands.scoreboard.teams.list.player.count": "Showing %d player(s) in team %s:", "commands.scoreboard.teams.list.player.entry": "- %2$s: %1$d (%3$s)", "commands.scoreboard.teams.list.player.empty": "Team %s has no players", "commands.scoreboard.teams.empty.usage": "/scoreboard teams empty <team>", "commands.scoreboard.teams.empty.alreadyEmpty": "Team %s is already empty, cannot remove nonexistant players", "commands.scoreboard.teams.empty.success": "Removed all %d player(s) from team %s", "commands.scoreboard.teams.remove.usage": "/scoreboard teams remove <name>", "commands.scoreboard.teams.remove.success": "Removed team %s", "commands.scoreboard.teams.join.usage": "/scoreboard teams join <team> [player]", "commands.scoreboard.teams.join.success": "Added %d player(s) to team %s: %s", "commands.scoreboard.teams.join.failure": "Could not add %d player(s) to team %s: %s", "commands.scoreboard.teams.leave.usage": "/scoreboard teams leave [player]", "commands.scoreboard.teams.leave.success": "Removed %d player(s) from their teams: %s", "commands.scoreboard.teams.leave.failure": "Could not remove %d player(s) from their teams: %s", "commands.scoreboard.teams.leave.noTeam": "You are not in a team", "commands.scoreboard.teams.option.usage": "/scoreboard teams option <team> <friendlyfire|color|seeFriendlyInvisibles|nametagVisibility|deathMessageVisibility|collisionRule> <value>", "commands.scoreboard.teams.option.noValue": "Valid values for option %s are: %s", "commands.scoreboard.teams.option.success": "Set option %s for team %s to %s", "commands.execute.usage": "/execute <entity> <x> <y> <z> <command> OR /execute <entity> <x> <y> <z> detect <x> <y> <z> <block> <data> <command>", "commands.execute.allInvocationsFailed": "All invocations failed: '%s'", "commands.execute.failed": "Failed to execute '%s' as %s", "commands.gamemode.success.self": "Set own game mode to %s", "commands.gamemode.success.other": "Set %s's game mode to %s", "commands.gamemode.usage": "/gamemode <mode> [player]", "commands.defaultgamemode.usage": "/defaultgamemode <mode>", "commands.defaultgamemode.success": "The world's default game mode is now %s", "commands.me.usage": "/me <action ...>", "commands.help.header": "--- Showing help page %d of %d (/help <page>) ---", "commands.help.footer": "Tip: Use the <tab> key while typing a command to auto-complete the command or its arguments", "commands.help.usage": "/help [page|command name]", "commands.trigger.usage": "/trigger <objective> <add|set> <value>", "commands.trigger.invalidObjective": "Invalid trigger name %s", "commands.trigger.invalidMode": "Invalid trigger mode %s", "commands.trigger.disabled": "Trigger %s is not enabled", "commands.trigger.invalidPlayer": "Only players can use the /trigger command", "commands.trigger.success": "Trigger %s changed with %s %s", "commands.publish.usage": "/publish", "commands.publish.started": "Local game hosted on port %s", "commands.publish.failed": "Unable to host local game", "commands.debug.start": "Started debug profiling", "commands.debug.stop": "Stopped debug profiling after %.2f seconds (%d ticks)", "commands.debug.notStarted": "Can't stop profiling when we haven't started yet!", "commands.debug.usage": "/debug <start|stop>", "commands.chunkinfo.usage": "/chunkinfo [<x> <y> <z>]", "commands.chunkinfo.location": "Chunk location: (%d, %d, %d)", "commands.chunkinfo.noChunk": "No chunk found at chunk position %d, %d, %d", "commands.chunkinfo.notEmpty": "Chunk is not empty.", "commands.chunkinfo.empty": "Chunk is empty.", "commands.chunkinfo.notCompiled": "Chunk is not compiled.", "commands.chunkinfo.compiled": "Chunk is compiled.", "commands.chunkinfo.hasNoRenderableLayers": "Chunk has no renderable layers.", "commands.chunkinfo.hasLayers": "Chunk has layers: %s", "commands.chunkinfo.isEmpty": "Chunk has empty layers: %s", "commands.chunkinfo.vertices": "%s layer's buffer contains %d vertices", "commands.chunkinfo.data": "First 64 vertices are: %s", "commands.tellraw.usage": "/tellraw <player> <raw json message>", "commands.tellraw.jsonException": "Invalid json: %s", "commands.message.usage": "/tell <player> <private message ...>", "commands.message.sameTarget": "You can't send a private message to yourself!", "commands.message.display.outgoing": "You whisper to %s: %s", "commands.message.display.incoming": "%s whispers to you: %s", "commands.difficulty.usage": "/difficulty <new difficulty>", "commands.difficulty.success": "Set game difficulty to %s", "commands.spawnpoint.usage": "/spawnpoint [player] [<x> <y> <z>]", "commands.spawnpoint.success": "Set %s's spawn point to (%d, %d, %d)", "commands.setworldspawn.usage": "/setworldspawn [<x> <y> <z>]", "commands.setworldspawn.success": "Set the world spawn point to (%d, %d, %d)", "commands.gamerule.usage": "/gamerule <rule name> [value]", "commands.gamerule.success": "Game rule %s has been updated to %s", "commands.gamerule.norule": "No game rule called '%s' is available", "commands.gamerule.nopermission": "Only server owners can change '%s'", "commands.weather.usage": "/weather <clear|rain|thunder> [duration in seconds]", "commands.weather.clear": "Changing to clear weather", "commands.weather.rain": "Changing to rainy weather", "commands.weather.thunder": "Changing to rain and thunder", "commands.testfor.usage": "/testfor <player> [dataTag]", "commands.testfor.failure": "%s did not match the required data structure", "commands.testfor.success": "Found %s", "commands.testfor.tagError": "Data tag parsing failed: %s", "commands.seed.usage": "/seed", "commands.seed.success": "Seed: %s", "commands.spreadplayers.usage": "/spreadplayers <x> <z> <spreadDistance> <maxRange> <respectTeams true|false> <player ...>", "commands.spreadplayers.spreading.teams": "Spreading %s teams %s blocks around %s,%s (min %s blocks apart)", "commands.spreadplayers.spreading.players": "Spreading %s players %s blocks around %s,%s (min %s blocks apart)", "commands.spreadplayers.success.teams": "Successfully spread %s teams around %s,%s", "commands.spreadplayers.success.players": "Successfully spread %s players around %s,%s", "commands.spreadplayers.info.teams": "(Average distance between teams is %s blocks apart after %s iterations)", "commands.spreadplayers.info.players": "(Average distance between players is %s blocks apart after %s iterations)", "commands.spreadplayers.failure.teams": "Could not spread %s teams around %s,%s (too many players for space - try using spread of at most %s)", "commands.spreadplayers.failure.players": "Could not spread %s players around %s,%s (too many players for space - try using spread of at most %s)", "commands.achievement.usage": "/achievement <give|take> <name|*> [player]", "commands.achievement.unknownAchievement": "Unknown achievement '%s'", "commands.achievement.alreadyHave": "Player %s already has achievement %s", "commands.achievement.dontHave": "Player %s doesn't have achievement %s", "commands.achievement.give.success.all": "Successfully given all achievements to %s", "commands.achievement.give.success.one": "Successfully given %s the achievement %s", "commands.achievement.take.success.all": "Successfully taken all achievements from %s", "commands.achievement.take.success.one": "Successfully taken the achievement %s from %s", "commands.worldborder.usage": "/worldborder <set|center|damage|warning|get|add> ...", "commands.worldborder.add.usage": "/worldborder add <sizeInBlocks> [timeInSeconds]", "commands.worldborder.set.usage": "/worldborder set <sizeInBlocks> [timeInSeconds]", "commands.worldborder.set.success": "Set world border to %s blocks wide (from %s blocks)", "commands.worldborder.get.success": "World border is currently %s blocks wide", "commands.worldborder.setSlowly.shrink.success": "Shrinking world border to %s blocks wide (down from %s blocks) over %s seconds", "commands.worldborder.setSlowly.grow.success": "Growing world border to %s blocks wide (up from %s blocks) over %s seconds", "commands.worldborder.center.usage": "/worldborder center <x> <z>", "commands.worldborder.center.success": "Set world border center to %s,%s", "commands.worldborder.damage.usage": "/worldborder damage <buffer|amount> ...", "commands.worldborder.damage.buffer.usage": "/worldborder damage buffer <sizeInBlocks>", "commands.worldborder.damage.buffer.success": "Set world border damage buffer to %s blocks (from %s blocks)", "commands.worldborder.damage.amount.usage": "/worldborder damage amount <damagePerBlock>", "commands.worldborder.damage.amount.success": "Set world border damage amount to %s per block (from %s per block)", "commands.worldborder.warning.usage": "/worldborder warning <time|distance> ...", "commands.worldborder.warning.time.usage": "/worldborder warning time <seconds>", "commands.worldborder.warning.time.success": "Set world border warning to %s seconds away (from %s seconds)", "commands.worldborder.warning.distance.usage": "/worldborder warning distance <distance>", "commands.worldborder.warning.distance.success": "Set world border warning to %s blocks away (from %s blocks)", "commands.title.usage": "/title <player> <title|subtitle|clear|reset|times> ...", "commands.title.usage.title": "/title <player> title|subtitle <raw json title>", "commands.title.usage.clear": "/title <player> clear|reset", "commands.title.usage.times": "/title <player> times <fadeIn> <stay> <fadeOut>", "commands.title.success": "Title command successfully executed", "commands.stopsound.usage": "/stopsound <player> [source] [sound]", "commands.stopsound.unknownSoundSource": "Source %s is unknown", "commands.stopsound.success.individualSound": "Stopped sound '%s' with source '%s' for %s", "commands.stopsound.success.soundSource": "Stopped source '%s' for %s", "commands.stopsound.success.all": "Stopped all sounds for %s", "itemGroup.buildingBlocks": "Building Blocks", "itemGroup.decorations": "Decoration Blocks", "itemGroup.redstone": "Redstone", "itemGroup.transportation": "Transportation", "itemGroup.misc": "Miscellaneous", "itemGroup.search": "Search Items", "itemGroup.food": "Foodstuffs", "itemGroup.tools": "Tools", "itemGroup.combat": "Combat", "itemGroup.brewing": "Brewing", "itemGroup.materials": "Materials", "itemGroup.inventory": "Survival Inventory", "inventory.binSlot": "<PERSON><PERSON><PERSON>em", "advMode.setCommand": "Set Console Command for Block", "advMode.setCommand.success": "Command set: %s", "advMode.command": "Console Command", "advMode.nearestPlayer": "Use \"@p\" to target nearest player", "advMode.randomPlayer": "Use \"@r\" to target random player", "advMode.allPlayers": "Use \"@a\" to target all players", "advMode.allEntities": "Use \"@e\" to target all entities", "advMode.previousOutput": "Previous Output", "advMode.mode.sequence": "Chain", "advMode.mode.auto": "Repeat", "advMode.mode.redstone": "Impulse", "advMode.mode.conditional": "Conditional", "advMode.mode.unconditional": "Unconditional", "advMode.mode.redstoneTriggered": "Needs <PERSON><PERSON>", "advMode.mode.autoexec.bat": "Always Active", "advMode.notEnabled": "Command blocks are not enabled on this server", "advMode.notAllowed": "Must be an opped player in creative mode", "mount.onboard": "Press %1$s to dismount", "build.tooHigh": "Height limit for building is %s blocks", "item.modifiers.mainhand": "When in main hand:", "item.modifiers.offhand": "When in off hand:", "item.modifiers.feet": "When on feet:", "item.modifiers.legs": "When on legs:", "item.modifiers.chest": "When on body:", "item.modifiers.head": "When on head:", "attribute.modifier.plus.0": "+%d %s", "attribute.modifier.plus.1": "+%d%% %s", "attribute.modifier.plus.2": "+%d%% %s", "attribute.modifier.take.0": "-%d %s", "attribute.modifier.take.1": "-%d%% %s", "attribute.modifier.take.2": "-%d%% %s", "attribute.modifier.equals.0": "%d %s", "attribute.modifier.equals.1": "%d%% %s", "attribute.modifier.equals.2": "%d%% %s", "attribute.name.horse.jumpStrength": "Horse Jump Strength", "attribute.name.zombie.spawnReinforcements": "Zombie Reinforcements", "attribute.name.generic.maxHealth": "Max Health", "attribute.name.generic.followRange": "<PERSON>b <PERSON> Range", "attribute.name.generic.knockbackResistance": "Knockback Resistance", "attribute.name.generic.movementSpeed": "Speed", "attribute.name.generic.attackDamage": "Attack Damage", "attribute.name.generic.attackSpeed": "Attack Speed", "attribute.name.generic.luck": "Luck", "attribute.name.generic.armor": "Armor", "attribute.name.generic.armorToughness": "<PERSON><PERSON>", "screenshot.success": "Saved screenshot as %s", "screenshot.failure": "Couldn't save screenshot: %s", "item.banner.black.name": "Black Banner", "item.banner.red.name": "Red Banner", "item.banner.green.name": "<PERSON> Banner", "item.banner.brown.name": "<PERSON>", "item.banner.blue.name": "Blue Banner", "item.banner.purple.name": "<PERSON> Banner", "item.banner.cyan.name": "<PERSON><PERSON>", "item.banner.silver.name": "<PERSON> Gray Banner", "item.banner.gray.name": "<PERSON>", "item.banner.pink.name": "Pink Banner", "item.banner.lime.name": "Lime Banner", "item.banner.yellow.name": "Yellow Banner", "item.banner.lightBlue.name": "Light Blue Banner", "item.banner.magenta.name": "Magenta Banner", "item.banner.orange.name": "Orange Banner", "item.banner.white.name": "White Banner", "item.shield.name": "Shield", "item.shield.black.name": "Black Shield", "item.shield.red.name": "Red Shield", "item.shield.green.name": "Green Shield", "item.shield.brown.name": "Brown Shield", "item.shield.blue.name": "Blue Shield", "item.shield.purple.name": "Purple Shield", "item.shield.cyan.name": "Cyan <PERSON>", "item.shield.silver.name": "Light Gray Shield", "item.shield.gray.name": "Gray Shield", "item.shield.pink.name": "Pink Shield", "item.shield.lime.name": "Lime Shield", "item.shield.yellow.name": "Yellow Shield", "item.shield.lightBlue.name": "Light Blue Shield", "item.shield.magenta.name": "Magenta Shield", "item.shield.orange.name": "Orange Shield", "item.shield.white.name": "White Shield", "item.banner.square_bottom_left.black": "Black Base Dexter Canton", "item.banner.square_bottom_left.red": "Red Base Dexter Canton", "item.banner.square_bottom_left.green": "Green Base Dexter Canton", "item.banner.square_bottom_left.brown": "Brown Base Dexter Canton", "item.banner.square_bottom_left.blue": "Blue Base Dexter Canton", "item.banner.square_bottom_left.purple": "Purple Base Dexter Canton", "item.banner.square_bottom_left.cyan": "Cyan Base Dexter Canton", "item.banner.square_bottom_left.silver": "Light Gray Base Dexter Canton", "item.banner.square_bottom_left.gray": "Gray Base Dexter Canton", "item.banner.square_bottom_left.pink": "Pink Base Dexter Canton", "item.banner.square_bottom_left.lime": "Lime Base Dexter Canton", "item.banner.square_bottom_left.yellow": "Yellow Base Dexter Canton", "item.banner.square_bottom_left.lightBlue": "Light Blue Base Dexter Canton", "item.banner.square_bottom_left.magenta": "Magenta Base Dexter Canton", "item.banner.square_bottom_left.orange": "Orange Base Dexter Canton", "item.banner.square_bottom_left.white": "White Base Dexter Canton", "item.banner.square_bottom_right.black": "Black Base Sinister Canton", "item.banner.square_bottom_right.red": "Red Base Sinister Canton", "item.banner.square_bottom_right.green": "Green Base Sinister Canton", "item.banner.square_bottom_right.brown": "Brown Base Sinister Canton", "item.banner.square_bottom_right.blue": "Blue Base Sinister Canton", "item.banner.square_bottom_right.purple": "Purple Base Sinister Canton", "item.banner.square_bottom_right.cyan": "Cyan Base Sinister Canton", "item.banner.square_bottom_right.silver": "Light Gray Base Sinister Canton", "item.banner.square_bottom_right.gray": "Gray Base Sinister Canton", "item.banner.square_bottom_right.pink": "Pink Base Sinister Canton", "item.banner.square_bottom_right.lime": "Lime Base Sinister Canton", "item.banner.square_bottom_right.yellow": "Yellow Base Sinister Canton", "item.banner.square_bottom_right.lightBlue": "Light Blue Base Sinister Canton", "item.banner.square_bottom_right.magenta": "Magenta Base Sinister Canton", "item.banner.square_bottom_right.orange": "Orange Base Sinister Canton", "item.banner.square_bottom_right.white": "White Base Sinister Canton", "item.banner.square_top_left.black": "Black Chief <PERSON>", "item.banner.square_top_left.red": "Red Chief <PERSON>", "item.banner.square_top_left.green": "Green Chief <PERSON>", "item.banner.square_top_left.brown": "Brown Chief <PERSON>", "item.banner.square_top_left.blue": "Blue Chief <PERSON>", "item.banner.square_top_left.purple": "Purple Chief <PERSON>", "item.banner.square_top_left.cyan": "Cyan Chief <PERSON>", "item.banner.square_top_left.silver": "Light Gray Chief <PERSON>", "item.banner.square_top_left.gray": "Gray Chief <PERSON>", "item.banner.square_top_left.pink": "Pink Chief <PERSON>", "item.banner.square_top_left.lime": "Lime Chief <PERSON>", "item.banner.square_top_left.yellow": "Yellow Chief <PERSON>", "item.banner.square_top_left.lightBlue": "Light Blue Chief <PERSON>", "item.banner.square_top_left.magenta": "Magenta Chief <PERSON>", "item.banner.square_top_left.orange": "Orange Chief <PERSON>", "item.banner.square_top_left.white": "White Chief <PERSON>", "item.banner.square_top_right.black": "Black Chief <PERSON>", "item.banner.square_top_right.red": "Red Chief Sin<PERSON>", "item.banner.square_top_right.green": "Green Chief <PERSON>", "item.banner.square_top_right.brown": "Brown Chief <PERSON><PERSON>", "item.banner.square_top_right.blue": "Blue Chief <PERSON>", "item.banner.square_top_right.purple": "Purple Chief <PERSON><PERSON>", "item.banner.square_top_right.cyan": "Cyan Chief <PERSON><PERSON>", "item.banner.square_top_right.silver": "Light Gray Chief <PERSON>", "item.banner.square_top_right.gray": "Gray Chief <PERSON>", "item.banner.square_top_right.pink": "Pink Chief <PERSON>", "item.banner.square_top_right.lime": "Lime Chief Sinister <PERSON>", "item.banner.square_top_right.yellow": "Yellow Chief Sinister <PERSON>", "item.banner.square_top_right.lightBlue": "Light Blue Chief <PERSON>", "item.banner.square_top_right.magenta": "Magenta Chief Sin<PERSON>", "item.banner.square_top_right.orange": "Orange Chief <PERSON>", "item.banner.square_top_right.white": "White Chief <PERSON>ister <PERSON>", "item.banner.stripe_bottom.black": "Black Base Fess", "item.banner.stripe_bottom.red": "Red Base Fess", "item.banner.stripe_bottom.green": "Green Base Fess", "item.banner.stripe_bottom.brown": "Brown Base Fess", "item.banner.stripe_bottom.blue": "Blue Base Fess", "item.banner.stripe_bottom.purple": "Purple Base Fess", "item.banner.stripe_bottom.cyan": "Cyan Base Fess", "item.banner.stripe_bottom.silver": "Light Gray Base Fess", "item.banner.stripe_bottom.gray": "Gray Base Fess", "item.banner.stripe_bottom.pink": "Pink Base Fess", "item.banner.stripe_bottom.lime": "Lime Base Fess", "item.banner.stripe_bottom.yellow": "Yellow Base Fess", "item.banner.stripe_bottom.lightBlue": "Light Blue Base Fess", "item.banner.stripe_bottom.magenta": "Magenta Base Fess", "item.banner.stripe_bottom.orange": "Orange Base Fess", "item.banner.stripe_bottom.white": "White Base Fess", "item.banner.stripe_top.black": "Black Chief <PERSON>", "item.banner.stripe_top.red": "Red Chief <PERSON>", "item.banner.stripe_top.green": "Green Chief <PERSON>", "item.banner.stripe_top.brown": "Brown Chief <PERSON>ss", "item.banner.stripe_top.blue": "Blue Chief <PERSON>", "item.banner.stripe_top.purple": "Purple Chief <PERSON>", "item.banner.stripe_top.cyan": "Cyan Chief <PERSON>", "item.banner.stripe_top.silver": "Light Gray Chief <PERSON>ss", "item.banner.stripe_top.gray": "Gray Chief <PERSON>", "item.banner.stripe_top.pink": "Pink Chief Fess", "item.banner.stripe_top.lime": "Lime Chief Fess", "item.banner.stripe_top.yellow": "Yellow Chief <PERSON>ss", "item.banner.stripe_top.lightBlue": "Light Blue Chief <PERSON>ss", "item.banner.stripe_top.magenta": "Magenta Chief <PERSON>", "item.banner.stripe_top.orange": "Orange Chief <PERSON>", "item.banner.stripe_top.white": "White Chief <PERSON>", "item.banner.stripe_left.black": "<PERSON>", "item.banner.stripe_left.red": "<PERSON>", "item.banner.stripe_left.green": "<PERSON>", "item.banner.stripe_left.brown": "<PERSON>", "item.banner.stripe_left.blue": "<PERSON> Dexter", "item.banner.stripe_left.purple": "<PERSON> Pale <PERSON>", "item.banner.stripe_left.cyan": "<PERSON><PERSON>", "item.banner.stripe_left.silver": "<PERSON> <PERSON>", "item.banner.stripe_left.gray": "<PERSON>", "item.banner.stripe_left.pink": "<PERSON>", "item.banner.stripe_left.lime": "<PERSON><PERSON>", "item.banner.stripe_left.yellow": "Yellow Pale Dexter", "item.banner.stripe_left.lightBlue": "Light Blue Pale Dexter", "item.banner.stripe_left.magenta": "<PERSON><PERSON><PERSON>", "item.banner.stripe_left.orange": "Orange Pale Dexter", "item.banner.stripe_left.white": "<PERSON>", "item.banner.stripe_right.black": "Black Pale Sinister", "item.banner.stripe_right.red": "Red Pale Sinister", "item.banner.stripe_right.green": "Green Pale Sinister", "item.banner.stripe_right.brown": "<PERSON> Pale <PERSON>", "item.banner.stripe_right.blue": "Blue Pale Sinister", "item.banner.stripe_right.purple": "Purple Pale Sinister", "item.banner.stripe_right.cyan": "<PERSON><PERSON>", "item.banner.stripe_right.silver": "<PERSON> Gray Pale <PERSON>", "item.banner.stripe_right.gray": "<PERSON>", "item.banner.stripe_right.pink": "Pink Pale Sinister", "item.banner.stripe_right.lime": "Lime Pale <PERSON>", "item.banner.stripe_right.yellow": "Yellow Pale Sinister", "item.banner.stripe_right.lightBlue": "Light Blue Pale Sinister", "item.banner.stripe_right.magenta": "<PERSON><PERSON><PERSON>", "item.banner.stripe_right.orange": "Orange Pale Sinister", "item.banner.stripe_right.white": "White Pale Sinister", "item.banner.stripe_center.black": "Black Pale", "item.banner.stripe_center.red": "<PERSON>", "item.banner.stripe_center.green": "Green Pale", "item.banner.stripe_center.brown": "<PERSON>", "item.banner.stripe_center.blue": "<PERSON> Pale", "item.banner.stripe_center.purple": "<PERSON> Pale", "item.banner.stripe_center.cyan": "<PERSON><PERSON>", "item.banner.stripe_center.silver": "<PERSON> <PERSON>", "item.banner.stripe_center.gray": "<PERSON>", "item.banner.stripe_center.pink": "<PERSON>", "item.banner.stripe_center.lime": "<PERSON>e <PERSON>", "item.banner.stripe_center.yellow": "Yellow Pale", "item.banner.stripe_center.lightBlue": "Light Blue Pale", "item.banner.stripe_center.magenta": "<PERSON><PERSON><PERSON>", "item.banner.stripe_center.orange": "Orange Pale", "item.banner.stripe_center.white": "White Pale", "item.banner.stripe_middle.black": "Black Fess", "item.banner.stripe_middle.red": "Red Fess", "item.banner.stripe_middle.green": "Green Fess", "item.banner.stripe_middle.brown": "Brown Fess", "item.banner.stripe_middle.blue": "Blue Fess", "item.banner.stripe_middle.purple": "Purple Fess", "item.banner.stripe_middle.cyan": "<PERSON><PERSON>", "item.banner.stripe_middle.silver": "Light Gray Fess", "item.banner.stripe_middle.gray": "<PERSON>", "item.banner.stripe_middle.pink": "Pink Fess", "item.banner.stripe_middle.lime": "Lime Fess", "item.banner.stripe_middle.yellow": "Yellow Fess", "item.banner.stripe_middle.lightBlue": "Light Blue Fess", "item.banner.stripe_middle.magenta": "<PERSON><PERSON><PERSON>", "item.banner.stripe_middle.orange": "Orange Fess", "item.banner.stripe_middle.white": "White Fess", "item.banner.stripe_downright.black": "Black Bend", "item.banner.stripe_downright.red": "Red Bend", "item.banner.stripe_downright.green": "Green Bend", "item.banner.stripe_downright.brown": "Brown Bend", "item.banner.stripe_downright.blue": "Blue Bend", "item.banner.stripe_downright.purple": "Purple Bend", "item.banner.stripe_downright.cyan": "<PERSON><PERSON>", "item.banner.stripe_downright.silver": "Light Gray Bend", "item.banner.stripe_downright.gray": "Gray Bend", "item.banner.stripe_downright.pink": "Pink Bend", "item.banner.stripe_downright.lime": "Lime Bend", "item.banner.stripe_downright.yellow": "Yellow Bend", "item.banner.stripe_downright.lightBlue": "Light Blue Bend", "item.banner.stripe_downright.magenta": "Magenta Bend", "item.banner.stripe_downright.orange": "Orange Bend", "item.banner.stripe_downright.white": "White Bend", "item.banner.stripe_downleft.black": "Black Bend Sinister", "item.banner.stripe_downleft.red": "Red Bend Sinister", "item.banner.stripe_downleft.green": "Green Bend Sinister", "item.banner.stripe_downleft.brown": "Brown Bend Sinister", "item.banner.stripe_downleft.blue": "Blue Bend Sinister", "item.banner.stripe_downleft.purple": "Purple Bend Sinister", "item.banner.stripe_downleft.cyan": "<PERSON><PERSON>", "item.banner.stripe_downleft.silver": "Light Gray Bend Sinister", "item.banner.stripe_downleft.gray": "Gray Bend Sinister", "item.banner.stripe_downleft.pink": "Pink Bend Sinister", "item.banner.stripe_downleft.lime": "Lime Bend Sinister", "item.banner.stripe_downleft.yellow": "Yellow Bend Sinister", "item.banner.stripe_downleft.lightBlue": "Light Blue Bend Sinister", "item.banner.stripe_downleft.magenta": "Magenta Bend Sinister", "item.banner.stripe_downleft.orange": "Orange Bend Sinister", "item.banner.stripe_downleft.white": "White Bend Sinister", "item.banner.small_stripes.black": "<PERSON> Paly", "item.banner.small_stripes.red": "<PERSON> Paly", "item.banner.small_stripes.green": "<PERSON> Paly", "item.banner.small_stripes.brown": "<PERSON>", "item.banner.small_stripes.blue": "Blue Paly", "item.banner.small_stripes.purple": "<PERSON> Paly", "item.banner.small_stripes.cyan": "<PERSON><PERSON>", "item.banner.small_stripes.silver": "<PERSON> Gray <PERSON>", "item.banner.small_stripes.gray": "<PERSON>", "item.banner.small_stripes.pink": "<PERSON> Paly", "item.banner.small_stripes.lime": "<PERSON>e <PERSON>", "item.banner.small_stripes.yellow": "Yellow Paly", "item.banner.small_stripes.lightBlue": "Light Blue Paly", "item.banner.small_stripes.magenta": "<PERSON><PERSON><PERSON>", "item.banner.small_stripes.orange": "Orange Paly", "item.banner.small_stripes.white": "<PERSON>ly", "item.banner.cross.black": "Black Saltire", "item.banner.cross.red": "Red Saltire", "item.banner.cross.green": "Green Saltire", "item.banner.cross.brown": "Brown Saltire", "item.banner.cross.blue": "Blue Saltire", "item.banner.cross.purple": "Purple Saltire", "item.banner.cross.cyan": "<PERSON><PERSON>", "item.banner.cross.silver": "Light Gray Saltire", "item.banner.cross.gray": "Gray Saltire", "item.banner.cross.pink": "Pink Saltire", "item.banner.cross.lime": "Lime Saltire", "item.banner.cross.yellow": "Yellow Saltire", "item.banner.cross.lightBlue": "Light Blue Saltire", "item.banner.cross.magenta": "Magenta Saltire", "item.banner.cross.orange": "Orange Saltire", "item.banner.cross.white": "White Saltire", "item.banner.triangle_bottom.black": "Black Chevron", "item.banner.triangle_bottom.red": "Red Chevron", "item.banner.triangle_bottom.green": "Green Chevron", "item.banner.triangle_bottom.brown": "<PERSON> Chevron", "item.banner.triangle_bottom.blue": "Blue Chevron", "item.banner.triangle_bottom.purple": "Purple Chevron", "item.banner.triangle_bottom.cyan": "<PERSON><PERSON>", "item.banner.triangle_bottom.silver": "Light Gray Chevron", "item.banner.triangle_bottom.gray": "<PERSON>", "item.banner.triangle_bottom.pink": "Pink Chevron", "item.banner.triangle_bottom.lime": "Lime Chevron", "item.banner.triangle_bottom.yellow": "Yellow Chevron", "item.banner.triangle_bottom.lightBlue": "Light Blue Chevron", "item.banner.triangle_bottom.magenta": "<PERSON><PERSON><PERSON>ron", "item.banner.triangle_bottom.orange": "Orange Chevron", "item.banner.triangle_bottom.white": "White Chevron", "item.banner.triangle_top.black": "Black Inverted Chevron", "item.banner.triangle_top.red": "Red Inverted Chevron", "item.banner.triangle_top.green": "Green Inverted Chevron", "item.banner.triangle_top.brown": "<PERSON> Inverted Chevron", "item.banner.triangle_top.blue": "Blue Inverted Chevron", "item.banner.triangle_top.purple": "Purple Inverted Chevron", "item.banner.triangle_top.cyan": "<PERSON><PERSON> In<PERSON> Chevron", "item.banner.triangle_top.silver": "Light Gray Inverted Chevron", "item.banner.triangle_top.gray": "Gray Inverted Chevron", "item.banner.triangle_top.pink": "Pink Inverted Chevron", "item.banner.triangle_top.lime": "Lime Inverted Chevron", "item.banner.triangle_top.yellow": "Yellow Inverted Chevron", "item.banner.triangle_top.lightBlue": "Light Blue Inverted Chevron", "item.banner.triangle_top.magenta": "Magenta Inverted Chevron", "item.banner.triangle_top.orange": "Orange Inverted Chevron", "item.banner.triangle_top.white": "White Inverted Chevron", "item.banner.triangles_bottom.black": "Black Base Indented", "item.banner.triangles_bottom.red": "Red Base Indented", "item.banner.triangles_bottom.green": "Green Base Indented", "item.banner.triangles_bottom.brown": "Brown Base Indented", "item.banner.triangles_bottom.blue": "Blue Base Indented", "item.banner.triangles_bottom.purple": "Purple Base Indented", "item.banner.triangles_bottom.cyan": "Cyan Base Indented", "item.banner.triangles_bottom.silver": "Light Gray Base Indented", "item.banner.triangles_bottom.gray": "Gray Base Indented", "item.banner.triangles_bottom.pink": "Pink Base Indented", "item.banner.triangles_bottom.lime": "Lime Base Indented", "item.banner.triangles_bottom.yellow": "Yellow Base Indented", "item.banner.triangles_bottom.lightBlue": "Light Blue Base Indented", "item.banner.triangles_bottom.magenta": "Magenta Base Indented", "item.banner.triangles_bottom.orange": "Orange Base Indented", "item.banner.triangles_bottom.white": "White Base Indented", "item.banner.triangles_top.black": "Black Chief Indented", "item.banner.triangles_top.red": "Red Chief Indented", "item.banner.triangles_top.green": "Green Chief Indented", "item.banner.triangles_top.brown": "<PERSON> Chief Indented", "item.banner.triangles_top.blue": "Blue Chief Indented", "item.banner.triangles_top.purple": "Purple Chief Indented", "item.banner.triangles_top.cyan": "Cyan Chief Indented", "item.banner.triangles_top.silver": "Light Gray Chief Indented", "item.banner.triangles_top.gray": "Gray Chief Indented", "item.banner.triangles_top.pink": "Pink Chief Indented", "item.banner.triangles_top.lime": "Lime Chief Indented", "item.banner.triangles_top.yellow": "Yellow Chief Indented", "item.banner.triangles_top.lightBlue": "Light Blue Chief Indented", "item.banner.triangles_top.magenta": "Magenta Chief Indented", "item.banner.triangles_top.orange": "Orange Chief Indented", "item.banner.triangles_top.white": "White Chief Indented", "item.banner.diagonal_left.black": "Black Per Bend Sinister", "item.banner.diagonal_left.red": "Red Per Bend Sinister", "item.banner.diagonal_left.green": "Green Per Bend Sinister", "item.banner.diagonal_left.brown": "<PERSON> Sinister", "item.banner.diagonal_left.blue": "Blue Per Bend Sinister", "item.banner.diagonal_left.purple": "Purple Per Bend Sinister", "item.banner.diagonal_left.cyan": "<PERSON><PERSON>", "item.banner.diagonal_left.silver": "Light Gray Per Bend Sinister", "item.banner.diagonal_left.gray": "<PERSON>ister", "item.banner.diagonal_left.pink": "Pink Per Bend Sinister", "item.banner.diagonal_left.lime": "Lime Per <PERSON>", "item.banner.diagonal_left.yellow": "Yellow Per Bend Sinister", "item.banner.diagonal_left.lightBlue": "Light Blue Per Bend Sinister", "item.banner.diagonal_left.magenta": "Ma<PERSON><PERSON> Per <PERSON>", "item.banner.diagonal_left.orange": "Orange Per Bend Sinister", "item.banner.diagonal_left.white": "White Per Bend Sinister", "item.banner.diagonal_right.black": "Black Per Bend", "item.banner.diagonal_right.red": "Red Per Bend", "item.banner.diagonal_right.green": "Green Per Bend", "item.banner.diagonal_right.brown": "<PERSON>", "item.banner.diagonal_right.blue": "Blue Per Bend", "item.banner.diagonal_right.purple": "Purple Per Bend", "item.banner.diagonal_right.cyan": "<PERSON><PERSON>", "item.banner.diagonal_right.silver": "Light Gray Per Bend", "item.banner.diagonal_right.gray": "<PERSON>", "item.banner.diagonal_right.pink": "Pink Per Bend", "item.banner.diagonal_right.lime": "Lime Per <PERSON>", "item.banner.diagonal_right.yellow": "Yellow Per Bend", "item.banner.diagonal_right.lightBlue": "Light Blue Per Bend", "item.banner.diagonal_right.magenta": "Magenta <PERSON>", "item.banner.diagonal_right.orange": "Orange Per Bend", "item.banner.diagonal_right.white": "White Per Bend", "item.banner.diagonal_up_left.black": "Black Per Bend Inverted", "item.banner.diagonal_up_left.red": "Red Per Bend Inverted", "item.banner.diagonal_up_left.green": "Green Per Bend Inverted", "item.banner.diagonal_up_left.brown": "<PERSON> Per Bend Inverted", "item.banner.diagonal_up_left.blue": "Blue Per Bend Inverted", "item.banner.diagonal_up_left.purple": "Purple Per Bend Inverted", "item.banner.diagonal_up_left.cyan": "<PERSON><PERSON> Inverted", "item.banner.diagonal_up_left.silver": "Light Gray Per Bend Inverted", "item.banner.diagonal_up_left.gray": "<PERSON> Inverted", "item.banner.diagonal_up_left.pink": "Pink Per Bend Inverted", "item.banner.diagonal_up_left.lime": "Lime Per Bend Inverted", "item.banner.diagonal_up_left.yellow": "Yellow Per Bend Inverted", "item.banner.diagonal_up_left.lightBlue": "Light Blue Per Bend Inverted", "item.banner.diagonal_up_left.magenta": "Magenta Per Bend Inverted", "item.banner.diagonal_up_left.orange": "Orange Per Bend Inverted", "item.banner.diagonal_up_left.white": "White Per Bend Inverted", "item.banner.diagonal_up_right.black": "Black Per Bend Sinister Inverted", "item.banner.diagonal_up_right.red": "Red Per Bend Sinister Inverted", "item.banner.diagonal_up_right.green": "Green Per Bend Sinister Inverted", "item.banner.diagonal_up_right.brown": "<PERSON> Per Bend Sinister Inverted", "item.banner.diagonal_up_right.blue": "Blue Per Bend Sinister Inverted", "item.banner.diagonal_up_right.purple": "Purple Per Bend Sinister Inverted", "item.banner.diagonal_up_right.cyan": "<PERSON><PERSON>ister Inverted", "item.banner.diagonal_up_right.silver": "Light Gray Per Bend Sinister Inverted", "item.banner.diagonal_up_right.gray": "<PERSON> Per Bend Sinister Inverted", "item.banner.diagonal_up_right.pink": "Pink Per Bend Sinister Inverted", "item.banner.diagonal_up_right.lime": "Lime Per Bend Sinister Inverted", "item.banner.diagonal_up_right.yellow": "Yellow Per Bend Sinister Inverted", "item.banner.diagonal_up_right.lightBlue": "Light Blue Per Bend Sinister Inverted", "item.banner.diagonal_up_right.magenta": "Magenta Per Bend Sinister Inverted", "item.banner.diagonal_up_right.orange": "Orange Per Bend Sinister Inverted", "item.banner.diagonal_up_right.white": "White Per Bend Sinister Inverted", "item.banner.circle.black": "Black Roundel", "item.banner.circle.red": "Red Roundel", "item.banner.circle.green": "Green Roundel", "item.banner.circle.brown": "<PERSON>", "item.banner.circle.blue": "Blue Roundel", "item.banner.circle.purple": "Purple Roundel", "item.banner.circle.cyan": "<PERSON><PERSON>", "item.banner.circle.silver": "<PERSON> <PERSON>", "item.banner.circle.gray": "<PERSON>", "item.banner.circle.pink": "Pink Roundel", "item.banner.circle.lime": "<PERSON><PERSON>", "item.banner.circle.yellow": "Yellow Roundel", "item.banner.circle.lightBlue": "Light Blue Roundel", "item.banner.circle.magenta": "<PERSON><PERSON><PERSON>", "item.banner.circle.orange": "Orange Roundel", "item.banner.circle.white": "White Roundel", "item.banner.rhombus.black": "Black Lozenge", "item.banner.rhombus.red": "Red Lozenge", "item.banner.rhombus.green": "Green Lozenge", "item.banner.rhombus.brown": "Brown Lozenge", "item.banner.rhombus.blue": "Blue Lozenge", "item.banner.rhombus.purple": "Purple Lozenge", "item.banner.rhombus.cyan": "<PERSON><PERSON>", "item.banner.rhombus.silver": "Light Gray Lozenge", "item.banner.rhombus.gray": "Gray Lozenge", "item.banner.rhombus.pink": "Pink Lozenge", "item.banner.rhombus.lime": "Lime Lozenge", "item.banner.rhombus.yellow": "Yellow Lozenge", "item.banner.rhombus.lightBlue": "Light Blue Lozenge", "item.banner.rhombus.magenta": "Magenta Lozenge", "item.banner.rhombus.orange": "Orange Lozenge", "item.banner.rhombus.white": "White Lozenge", "item.banner.half_vertical.black": "<PERSON> Per <PERSON>", "item.banner.half_vertical.red": "<PERSON> Per <PERSON>le", "item.banner.half_vertical.green": "<PERSON> Per <PERSON>", "item.banner.half_vertical.brown": "<PERSON>", "item.banner.half_vertical.blue": "<PERSON> Per <PERSON>", "item.banner.half_vertical.purple": "<PERSON> Per <PERSON>", "item.banner.half_vertical.cyan": "<PERSON><PERSON>", "item.banner.half_vertical.silver": "<PERSON> <PERSON>", "item.banner.half_vertical.gray": "<PERSON>", "item.banner.half_vertical.pink": "Pink Per <PERSON>", "item.banner.half_vertical.lime": "<PERSON><PERSON>", "item.banner.half_vertical.yellow": "Yellow Per Pale", "item.banner.half_vertical.lightBlue": "Light Blue Per Pale", "item.banner.half_vertical.magenta": "Ma<PERSON><PERSON>", "item.banner.half_vertical.orange": "Orange Per Pale", "item.banner.half_vertical.white": "<PERSON> <PERSON>", "item.banner.half_horizontal.black": "Black Per Fess", "item.banner.half_horizontal.red": "Red Per Fess", "item.banner.half_horizontal.green": "Green Per Fess", "item.banner.half_horizontal.brown": "<PERSON>", "item.banner.half_horizontal.blue": "Blue Per Fess", "item.banner.half_horizontal.purple": "Purple Per Fess", "item.banner.half_horizontal.cyan": "<PERSON><PERSON>", "item.banner.half_horizontal.silver": "Light Gray <PERSON>", "item.banner.half_horizontal.gray": "<PERSON>", "item.banner.half_horizontal.pink": "Pink Per Fess", "item.banner.half_horizontal.lime": "Lime <PERSON>", "item.banner.half_horizontal.yellow": "Yellow Per Fess", "item.banner.half_horizontal.lightBlue": "Light Blue Per Fess", "item.banner.half_horizontal.magenta": "Ma<PERSON><PERSON>", "item.banner.half_horizontal.orange": "Orange Per Fess", "item.banner.half_horizontal.white": "White Per Fess", "item.banner.half_vertical_right.black": "Black Per Pale Inverted", "item.banner.half_vertical_right.red": "Red Per Pale Inverted", "item.banner.half_vertical_right.green": "Green Per Pale Inverted", "item.banner.half_vertical_right.brown": "<PERSON> Inverted", "item.banner.half_vertical_right.blue": "Blue Per Pale Inverted", "item.banner.half_vertical_right.purple": "Purple Per <PERSON>le Inverted", "item.banner.half_vertical_right.cyan": "<PERSON><PERSON>", "item.banner.half_vertical_right.silver": "Light Gray Per <PERSON>le Inverted", "item.banner.half_vertical_right.gray": "<PERSON> Inverted", "item.banner.half_vertical_right.pink": "Pink Per Pale Inverted", "item.banner.half_vertical_right.lime": "Lime Per <PERSON> Inverted", "item.banner.half_vertical_right.yellow": "Yellow Per Pale Inverted", "item.banner.half_vertical_right.lightBlue": "Light Blue Per Pale Inverted", "item.banner.half_vertical_right.magenta": "Magenta Per <PERSON> Inverted", "item.banner.half_vertical_right.orange": "Orange Per Pale Inverted", "item.banner.half_vertical_right.white": "White Per Pale Inverted", "item.banner.half_horizontal_bottom.black": "Black Per Fess Inverted", "item.banner.half_horizontal_bottom.red": "Red Per Fess Inverted", "item.banner.half_horizontal_bottom.green": "Green Per Fess Inverted", "item.banner.half_horizontal_bottom.brown": "<PERSON> Per Fess Inverted", "item.banner.half_horizontal_bottom.blue": "Blue Per Fess Inverted", "item.banner.half_horizontal_bottom.purple": "Purple Per Fess Inverted", "item.banner.half_horizontal_bottom.cyan": "<PERSON><PERSON> Inverted", "item.banner.half_horizontal_bottom.silver": "Light Gray Per Fess Inverted", "item.banner.half_horizontal_bottom.gray": "<PERSON>ss Inverted", "item.banner.half_horizontal_bottom.pink": "Pink Per Fess Inverted", "item.banner.half_horizontal_bottom.lime": "Lime Per Fess Inverted", "item.banner.half_horizontal_bottom.yellow": "Yellow Per Fess Inverted", "item.banner.half_horizontal_bottom.lightBlue": "Light Blue Per Fess Inverted", "item.banner.half_horizontal_bottom.magenta": "Magenta Per Fess Inverted", "item.banner.half_horizontal_bottom.orange": "Orange Per Fess Inverted", "item.banner.half_horizontal_bottom.white": "White Per Fess Inverted", "item.banner.creeper.black": "Black Creeper Charge", "item.banner.creeper.red": "Red Creeper Charge", "item.banner.creeper.green": "Green Creeper Charge", "item.banner.creeper.brown": "Brown Creeper Charge", "item.banner.creeper.blue": "Blue Creeper Charge", "item.banner.creeper.purple": "Purple Creeper Charge", "item.banner.creeper.cyan": "<PERSON><PERSON>", "item.banner.creeper.silver": "Light Gray Creeper Charge", "item.banner.creeper.gray": "Gray <PERSON> Charge", "item.banner.creeper.pink": "Pink Creeper Charge", "item.banner.creeper.lime": "Lime Creeper Charge", "item.banner.creeper.yellow": "Yellow Creeper Charge", "item.banner.creeper.lightBlue": "Light Blue Creeper Charge", "item.banner.creeper.magenta": "Magenta Creeper Charge", "item.banner.creeper.orange": "Orange Creeper Charge", "item.banner.creeper.white": "White Creeper Charge", "item.banner.bricks.black": "Black Field Masoned", "item.banner.bricks.red": "<PERSON> Field Masoned", "item.banner.bricks.green": "Green Field Masoned", "item.banner.bricks.brown": "<PERSON> Field Masoned", "item.banner.bricks.blue": "Blue Field Masoned", "item.banner.bricks.purple": "<PERSON> Masoned", "item.banner.bricks.cyan": "<PERSON><PERSON>", "item.banner.bricks.silver": "<PERSON> <PERSON> Masoned", "item.banner.bricks.gray": "<PERSON>ed", "item.banner.bricks.pink": "<PERSON> Masoned", "item.banner.bricks.lime": "Lime Field Masoned", "item.banner.bricks.yellow": "Yellow Field Masoned", "item.banner.bricks.lightBlue": "Light Blue Field Masoned", "item.banner.bricks.magenta": "Magenta Field Masoned", "item.banner.bricks.orange": "Orange Field Masoned", "item.banner.bricks.white": "White Field Masoned", "item.banner.gradient.black": "Black Gradient", "item.banner.gradient.red": "<PERSON> Gradient", "item.banner.gradient.green": "Green Gradient", "item.banner.gradient.brown": "<PERSON>", "item.banner.gradient.blue": "Blue Gradient", "item.banner.gradient.purple": "Purple Gradient", "item.banner.gradient.cyan": "<PERSON><PERSON>", "item.banner.gradient.silver": "<PERSON> <PERSON>", "item.banner.gradient.gray": "<PERSON>", "item.banner.gradient.pink": "Pink Gradient", "item.banner.gradient.lime": "Lime G<PERSON>ient", "item.banner.gradient.yellow": "Yellow Gradient", "item.banner.gradient.lightBlue": "Light Blue Gradient", "item.banner.gradient.magenta": "<PERSON><PERSON><PERSON>", "item.banner.gradient.orange": "Orange Gradient", "item.banner.gradient.white": "White Gradient", "item.banner.gradient_up.black": "Black Base Gradient", "item.banner.gradient_up.red": "Red Base Gradient", "item.banner.gradient_up.green": "Green Base Gradient", "item.banner.gradient_up.brown": "Brown Base Gradient", "item.banner.gradient_up.blue": "Blue Base Gradient", "item.banner.gradient_up.purple": "Purple Base Gradient", "item.banner.gradient_up.cyan": "Cyan Base Gradient", "item.banner.gradient_up.silver": "Light Gray Base Gradient", "item.banner.gradient_up.gray": "Gray Base Gradient", "item.banner.gradient_up.pink": "Pink Base Gradient", "item.banner.gradient_up.lime": "Lime Base Gradient", "item.banner.gradient_up.yellow": "Yellow Base Gradient", "item.banner.gradient_up.lightBlue": "Light Blue Base Gradient", "item.banner.gradient_up.magenta": "Magenta Base Gradient", "item.banner.gradient_up.orange": "Orange Base Gradient", "item.banner.gradient_up.white": "White Base Gradient", "item.banner.skull.black": "Black Skull Charge", "item.banner.skull.red": "Red Skull Charge", "item.banner.skull.green": "Green Skull Charge", "item.banner.skull.brown": "Brown Skull Charge", "item.banner.skull.blue": "Blue Skull Charge", "item.banner.skull.purple": "Purple Skull Charge", "item.banner.skull.cyan": "<PERSON>an <PERSON>", "item.banner.skull.silver": "Light Gray Skull Charge", "item.banner.skull.gray": "Gray Skull Charge", "item.banner.skull.pink": "Pink Skull Charge", "item.banner.skull.lime": "Lime Skull Charge", "item.banner.skull.yellow": "Yellow Skull Charge", "item.banner.skull.lightBlue": "Light Blue Skull Charge", "item.banner.skull.magenta": "Magenta Skull Charge", "item.banner.skull.orange": "Orange Skull Charge", "item.banner.skull.white": "White Skull Charge", "item.banner.flower.black": "Black Flower Charge", "item.banner.flower.red": "Red Flower Charge", "item.banner.flower.green": "Green Flower Charge", "item.banner.flower.brown": "Brown Flower Charge", "item.banner.flower.blue": "Blue Flower Charge", "item.banner.flower.purple": "Purple Flower Charge", "item.banner.flower.cyan": "<PERSON><PERSON>", "item.banner.flower.silver": "Light Gray Flower Charge", "item.banner.flower.gray": "Gray Flower Charge", "item.banner.flower.pink": "Pink Flower Charge", "item.banner.flower.lime": "Lime Flower Charge", "item.banner.flower.yellow": "Yellow Flower Charge", "item.banner.flower.lightBlue": "Light Blue Flower Charge", "item.banner.flower.magenta": "Magenta Flower Charge", "item.banner.flower.orange": "Orange Flower Charge", "item.banner.flower.white": "White Flower Charge", "item.banner.border.black": "Black Bordure", "item.banner.border.red": "Red Bordure", "item.banner.border.green": "Green Bordure", "item.banner.border.brown": "<PERSON>", "item.banner.border.blue": "Blue Bordure", "item.banner.border.purple": "Purple Bordure", "item.banner.border.cyan": "<PERSON><PERSON>", "item.banner.border.silver": "Light Gray <PERSON>", "item.banner.border.gray": "<PERSON>", "item.banner.border.pink": "Pink Bordure", "item.banner.border.lime": "Lime <PERSON>", "item.banner.border.yellow": "Yellow Bordure", "item.banner.border.lightBlue": "Light Blue Bordure", "item.banner.border.magenta": "<PERSON><PERSON><PERSON>", "item.banner.border.orange": "Orange Bordure", "item.banner.border.white": "White Bordure", "item.banner.curly_border.black": "Black Bordure Indented", "item.banner.curly_border.red": "Red Bordure Indented", "item.banner.curly_border.green": "Green Bordure Indented", "item.banner.curly_border.brown": "Brown Bordure Indented", "item.banner.curly_border.blue": "Blue Bordure Indented", "item.banner.curly_border.purple": "Purple Bordure Indented", "item.banner.curly_border.cyan": "<PERSON><PERSON> Indented", "item.banner.curly_border.silver": "Light Gray Bordure Indented", "item.banner.curly_border.gray": "Gray Bordure Indented", "item.banner.curly_border.pink": "Pink Bordure Indented", "item.banner.curly_border.lime": "Lime Bordure Indented", "item.banner.curly_border.yellow": "Yellow Bordure Indented", "item.banner.curly_border.lightBlue": "Light Blue Bordure Indented", "item.banner.curly_border.magenta": "Magenta Bordure Indented", "item.banner.curly_border.orange": "Orange Bordure Indented", "item.banner.curly_border.white": "White Bordure Indented", "item.banner.mojang.black": "Black Thing", "item.banner.mojang.red": "Red Thing", "item.banner.mojang.green": "Green Thing", "item.banner.mojang.brown": "<PERSON>", "item.banner.mojang.blue": "Blue Thing", "item.banner.mojang.purple": "Purple Thing", "item.banner.mojang.cyan": "<PERSON><PERSON>", "item.banner.mojang.silver": "Light Gray Thing", "item.banner.mojang.gray": "<PERSON>", "item.banner.mojang.pink": "Pink Thing", "item.banner.mojang.lime": "Lime Thing", "item.banner.mojang.yellow": "Yellow Thing", "item.banner.mojang.lightBlue": "Light Blue Thing", "item.banner.mojang.magenta": "Magenta Thing", "item.banner.mojang.orange": "Orange Thing", "item.banner.mojang.white": "White Thing", "item.banner.straight_cross.black": "Black Cross", "item.banner.straight_cross.red": "Red Cross", "item.banner.straight_cross.green": "Green Cross", "item.banner.straight_cross.brown": "<PERSON>", "item.banner.straight_cross.blue": "Blue Cross", "item.banner.straight_cross.purple": "Purple Cross", "item.banner.straight_cross.cyan": "<PERSON><PERSON>", "item.banner.straight_cross.silver": "Light Gray Cross", "item.banner.straight_cross.gray": "<PERSON>", "item.banner.straight_cross.pink": "Pink Cross", "item.banner.straight_cross.lime": "Lime Cross", "item.banner.straight_cross.yellow": "Yellow Cross", "item.banner.straight_cross.lightBlue": "Light Blue Cross", "item.banner.straight_cross.magenta": "Magenta Cross", "item.banner.straight_cross.orange": "Orange Cross", "item.banner.straight_cross.white": "White Cross", "subtitles.ambient.cave": "Eerie noise", "subtitles.block.anvil.destroy": "Anvil destroyed", "subtitles.block.anvil.land": "Anvil landed", "subtitles.block.anvil.use": "Anvil used", "subtitles.block.brewing_stand.brew": "Brewing Stand bubbles", "subtitles.block.button.click": "<PERSON><PERSON> clicks", "subtitles.block.chest.close": "Chest closes", "subtitles.block.chest.locked": "Chest locked", "subtitles.block.chest.open": "Chest opens", "subtitles.block.chorus_flower.death": "Chorus Flower withers", "subtitles.block.chorus_flower.grow": "Chorus Flower grows", "subtitles.block.comparator.click": "Comparator clicks", "subtitles.block.dispenser.dispense": "Dispensed item", "subtitles.block.dispenser.fail": "Dispenser failed", "subtitles.block.door.toggle": "Door creaks", "subtitles.block.fence_gate.toggle": "Fence Gate creaks", "subtitles.block.fire.ambient": "Fire crackles", "subtitles.block.fire.extinguish": "Fire extinguished", "subtitles.block.furnace.fire_crackle": "Furnace crackles", "subtitles.block.generic.break": "Block broken", "subtitles.block.generic.footsteps": "Footsteps", "subtitles.block.generic.hit": "Block breaking", "subtitles.block.generic.place": "Block placed", "subtitles.block.iron_trapdoor.close": "Trapdoor opens", "subtitles.block.iron_trapdoor.open": "Trapdoor closes", "subtitles.block.lava.ambient": "Lava pops", "subtitles.block.lava.extinguish": "<PERSON><PERSON> hisses", "subtitles.block.lever.click": "Lever clicks", "subtitles.block.note.note": "Noteblock plays", "subtitles.block.piston.move": "<PERSON><PERSON> moves", "subtitles.block.portal.ambient": "Portal whooshes", "subtitles.block.pressure_plate.click": "Pressure Plate clicks", "subtitles.block.redstone_torch.burnout": "Torch fizzes", "subtitles.block.trapdoor.toggle": "Trapdoor creaks", "subtitles.block.tripwire.attach": "Tripwire attaches", "subtitles.block.tripwire.click": "Tripwire clicks", "subtitles.block.tripwire.detach": "Tripwire detaches", "subtitles.block.water.ambient": "Water flows", "subtitles.enchant.thorns.hit": "<PERSON><PERSON> prick", "subtitles.entity.armorstand.fall": "Something fell", "subtitles.entity.arrow.hit": "Arrow hits", "subtitles.entity.arrow.hit_player": "Player hit", "subtitles.entity.arrow.shoot": "Arrow fired", "subtitles.entity.bat.ambient": "<PERSON> screeches", "subtitles.entity.bat.death": "<PERSON> dies", "subtitles.entity.bat.hurt": "Bat hurts", "subtitles.entity.bat.takeoff": "<PERSON> takes off", "subtitles.entity.blaze.ambient": "<PERSON> breathes", "subtitles.entity.blaze.burn": "<PERSON> crackles", "subtitles.entity.blaze.death": "<PERSON> dies", "subtitles.entity.blaze.hurt": "Blaze hurts", "subtitles.entity.blaze.shoot": "Blaze shoots", "subtitles.entity.bobber.splash": "Fishing hook splashes", "subtitles.entity.bobber.throw": "<PERSON><PERSON> thrown", "subtitles.entity.cat.ambient": "<PERSON> meows", "subtitles.entity.cat.death": "<PERSON> dies", "subtitles.entity.cat.hurt": "Cat hurts", "subtitles.entity.chicken.ambient": "Chicken clucks", "subtitles.entity.chicken.death": "<PERSON> dies", "subtitles.entity.chicken.egg": "Chicken plops", "subtitles.entity.chicken.hurt": "Chicken hurts", "subtitles.entity.cow.ambient": "Cow moos", "subtitles.entity.cow.death": "<PERSON>w dies", "subtitles.entity.cow.hurt": "Cow hurts", "subtitles.entity.cow.milk": "Cow gets milked", "subtitles.entity.creeper.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.creeper.hurt": "<PERSON><PERSON><PERSON> hurts", "subtitles.entity.creeper.primed": "<PERSON><PERSON><PERSON> hisses", "subtitles.entity.donkey.ambient": "Donkey hee-haws", "subtitles.entity.donkey.angry": "Donkey neighs", "subtitles.entity.donkey.chest": "Donkey Chest equips", "subtitles.entity.donkey.death": "<PERSON><PERSON> dies", "subtitles.entity.donkey.hurt": "Donkey hurts", "subtitles.entity.egg.throw": "Egg flies", "subtitles.entity.enderdragon.ambient": "Dragon roars", "subtitles.entity.enderdragon.death": "<PERSON> dies", "subtitles.entity.enderdragon.flap": "Dragon flaps", "subtitles.entity.enderdragon.growl": "Dragon growls", "subtitles.entity.enderdragon.hurt": "Dragon hurts", "subtitles.entity.enderdragon.shoot": "Dragon shoots", "subtitles.entity.endereye.launch": "Eye of <PERSON><PERSON> shoots", "subtitles.entity.enderman.ambient": "<PERSON><PERSON> vwoops", "subtitles.entity.enderman.death": "<PERSON><PERSON> dies", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> hurts", "subtitles.entity.enderman.stare": "<PERSON><PERSON> cries out", "subtitles.entity.enderman.teleport": "Enderman teleports", "subtitles.entity.endermite.ambient": "Endermite scuttles", "subtitles.entity.endermite.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.endermite.hurt": "Endermite hurts", "subtitles.entity.enderpearl.throw": "<PERSON><PERSON> flies", "subtitles.entity.experience_orb.pickup": "Experience gained", "subtitles.entity.firework.blast": "Firework blasts", "subtitles.entity.firework.launch": "Firework launches", "subtitles.entity.firework.twinkle": "Firework twinkles", "subtitles.entity.generic.big_fall": "Something fell", "subtitles.entity.generic.burn": "Burning", "subtitles.entity.generic.death": "Dying", "subtitles.entity.generic.drink": "Sipping", "subtitles.entity.generic.eat": "Eating", "subtitles.entity.generic.explode": "Explosion", "subtitles.entity.generic.extinguish_fire": "Fire extinguishes", "subtitles.entity.generic.hurt": "Something hurts", "subtitles.entity.generic.small_fall": "Something tripped", "subtitles.entity.generic.splash": "Splashing", "subtitles.entity.generic.swim": "Swimming", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON> cries", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> hurts", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> shoots", "subtitles.entity.guardian.ambient.land": "Guardian flaps", "subtitles.entity.guardian.ambient": "Guardian moans", "subtitles.entity.guardian.attack": "Guardian shoots", "subtitles.entity.guardian.curse": "Guardian curses", "subtitles.entity.guardian.death": "Guardian dies", "subtitles.entity.guardian.flop": "Guardian flops", "subtitles.entity.guardian.hurt": "Guardian hurts", "subtitles.entity.horse.ambient": "Horse neighs", "subtitles.entity.horse.angry": "Horse neighs", "subtitles.entity.horse.armor": "Horse armor equips", "subtitles.entity.horse.breathe": "Horse breathes", "subtitles.entity.horse.death": "Horse dies", "subtitles.entity.horse.eat": "Horse eats", "subtitles.entity.horse.gallop": "Horse gallops", "subtitles.entity.horse.hurt": "Horse hurts", "subtitles.entity.horse.jump": "Horse jumps", "subtitles.entity.horse.saddle": "Saddle equips", "subtitles.entity.husk.ambient": "<PERSON><PERSON> groans", "subtitles.entity.husk.death": "<PERSON><PERSON> dies", "subtitles.entity.husk.hurt": "Husk hurts", "subtitles.entity.iron_golem.attack": "Iron Golem attacks", "subtitles.entity.iron_golem.death": "Iron Golem dies", "subtitles.entity.iron_golem.hurt": "Iron Golem hurts", "subtitles.entity.item.break": "<PERSON>em breaks", "subtitles.entity.item.pickup": "Item plops", "subtitles.entity.itemframe.add_item": "<PERSON><PERSON>", "subtitles.entity.itemframe.break": "<PERSON><PERSON>", "subtitles.entity.itemframe.place": "<PERSON><PERSON> placed", "subtitles.entity.itemframe.remove_item": "Item Frame empties", "subtitles.entity.itemframe.rotate_item": "<PERSON><PERSON> clicks", "subtitles.entity.leashknot.break": "Leash knot breaks", "subtitles.entity.leashknot.place": "Leash knot tied", "subtitles.entity.lightning.impact": "Lighting strikes", "subtitles.entity.lightning.thunder": "Thunder roars", "subtitles.entity.magmacube.death": "Magma Cube dies", "subtitles.entity.magmacube.hurt": "Magma Cube hurts", "subtitles.entity.magmacube.squish": "Magma Cube squishes", "subtitles.entity.minecart.riding": "Minecart rolls", "subtitles.entity.mule.ambient": "Mule hee-haws", "subtitles.entity.mule.death": "<PERSON><PERSON> dies", "subtitles.entity.mule.hurt": "<PERSON><PERSON> hurts", "subtitles.entity.painting.break": "Painting breaks", "subtitles.entity.painting.place": "Painting placed", "subtitles.entity.pig.ambient": "Pig oinks", "subtitles.entity.pig.death": "Pig dies", "subtitles.entity.pig.hurt": "Pig hurts", "subtitles.entity.pig.saddle": "Saddle equips", "subtitles.entity.player.burp": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.death": "Player dies", "subtitles.entity.player.hurt": "Player hurts", "subtitles.entity.player.levelup": "Player dings", "subtitles.entity.polar_bear.ambient": "Polar Bear groans", "subtitles.entity.polar_bear.baby_ambient": "Polar Bear hums", "subtitles.entity.polar_bear.death": "Polar Bear dies", "subtitles.entity.polar_bear.hurt": "Polar Bear hurts", "subtitles.entity.polar_bear.warning": "Polar Bear roars", "subtitles.entity.potion.splash": "Bottle smashes", "subtitles.entity.potion.throw": "Bottle thrown", "subtitles.entity.rabbit.ambient": "Rabbit squeaks", "subtitles.entity.rabbit.attack": "Rabbit attacks", "subtitles.entity.rabbit.death": "<PERSON> dies", "subtitles.entity.rabbit.hurt": "Rabbit hurts", "subtitles.entity.rabbit.jump": "Rabbit hops", "subtitles.entity.sheep.ambient": "Sheep baahs", "subtitles.entity.sheep.death": "Sheep dies", "subtitles.entity.sheep.hurt": "Sheep hurts", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON> lurks", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> closes", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> hurts", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> opens", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> shoots", "subtitles.entity.shulker.teleport": "Shulker teleports", "subtitles.entity.shulker_bullet.hit": "Shulker bullet explodes", "subtitles.entity.shulker_bullet.hurt": "Shulker bullet breaks", "subtitles.entity.silverfish.ambient": "Silverfish hisses", "subtitles.entity.silverfish.death": "Silverfish dies", "subtitles.entity.silverfish.hurt": "Silverfish hurts", "subtitles.entity.skeleton.ambient": "Skeleton rattles", "subtitles.entity.skeleton.death": "Skel<PERSON> dies", "subtitles.entity.skeleton.hurt": "Skeleton hurts", "subtitles.entity.skeleton.shoot": "Skeleton shoots", "subtitles.entity.skeleton_horse.ambient": "Skeleton Horse cries", "subtitles.entity.skeleton_horse.death": "Skeleton Horse dies", "subtitles.entity.skeleton_horse.hurt": "Skeleton Horse hurts", "subtitles.entity.slime.attack": "Slime attacks", "subtitles.entity.slime.death": "<PERSON><PERSON> dies", "subtitles.entity.slime.hurt": "Slime hurts", "subtitles.entity.slime.squish": "Slime squishes", "subtitles.entity.snowball.throw": "Snowball flies", "subtitles.entity.snowman.death": "<PERSON><PERSON> dies", "subtitles.entity.snowman.hurt": "Snow Golem hurts", "subtitles.entity.spider.ambient": "Spider hisses", "subtitles.entity.spider.death": "<PERSON> dies", "subtitles.entity.spider.hurt": "<PERSON> hurts", "subtitles.entity.squid.ambient": "Squid swims", "subtitles.entity.squid.death": "<PERSON><PERSON> dies", "subtitles.entity.squid.hurt": "Squid hurts", "subtitles.entity.stray.ambient": "Stray rattles", "subtitles.entity.stray.death": "<PERSON><PERSON> dies", "subtitles.entity.stray.hurt": "Stray hurts", "subtitles.entity.tnt.primed": "TNT fizzes", "subtitles.entity.villager.ambient": "Villager mumbles", "subtitles.entity.villager.death": "Villager dies", "subtitles.entity.villager.hurt": "Villager hurts", "subtitles.entity.villager.no": "Villager disagrees", "subtitles.entity.villager.trading": "Villager trades", "subtitles.entity.villager.yes": "Villager agrees", "subtitles.entity.witch.ambient": "Witch giggles", "subtitles.entity.witch.death": "Witch dies", "subtitles.entity.witch.drink": "Witch drinks", "subtitles.entity.witch.hurt": "Witch hurts", "subtitles.entity.witch.throw": "Witch throws", "subtitles.entity.wither.ambient": "Wither angers", "subtitles.entity.wither.death": "<PERSON><PERSON> dies", "subtitles.entity.wither.hurt": "Wither hurts", "subtitles.entity.wither.shoot": "Wither attacks", "subtitles.entity.wither.spawn": "<PERSON><PERSON> released", "subtitles.entity.wither_skeleton.ambient": "Wither Skeleton rattles", "subtitles.entity.wither_skeleton.death": "<PERSON><PERSON> dies", "subtitles.entity.wither_skeleton.hurt": "Wither <PERSON> hurts", "subtitles.entity.wolf.ambient": "Wolf pants", "subtitles.entity.wolf.death": "<PERSON> dies", "subtitles.entity.wolf.growl": "Wolf growls", "subtitles.entity.wolf.hurt": "<PERSON> hurts", "subtitles.entity.wolf.shake": "Wolf shakes", "subtitles.entity.zombie.ambient": "Zombie groans", "subtitles.entity.zombie.death": "<PERSON> dies", "subtitles.entity.zombie.hurt": "Zombie hurts", "subtitles.entity.zombie.infect": "Zombie infects", "subtitles.entity.zombie_horse.ambient": "Zombie Horse cries", "subtitles.entity.zombie_horse.death": "<PERSON> Horse dies", "subtitles.entity.zombie_horse.hurt": "Zombie Horse hurts", "subtitles.entity.zombie_pigman.ambient": "Zombie Pigman grunts", "subtitles.entity.zombie_pigman.angry": "Zombie Pigman angers", "subtitles.entity.zombie_pigman.death": "Zombie Pigman dies", "subtitles.entity.zombie_pigman.hurt": "Zombie Pigman hurts", "subtitles.entity.zombie_villager.converted": "Zombie vociferates", "subtitles.entity.zombie_villager.cure": "Zombie snuffles", "subtitles.item.armor.equip": "Gear equipped", "subtitles.item.armor.equip_chain": "Chain armor jingles", "subtitles.item.armor.equip_diamond": "Diamond armor clangs", "subtitles.item.armor.equip_gold": "Gold armor clinks", "subtitles.item.armor.equip_iron": "Iron armor clanks", "subtitles.item.armor.equip_leather": "Leather armor rustles", "subtitles.item.bottle.fill": "Bottle fills", "subtitles.item.bucket.empty": "Bucket empties", "subtitles.item.bucket.fill": "Bucket fills", "subtitles.item.chorus_fruit.teleport": "Player teleports", "subtitles.item.firecharge.use": "Fireball whooshes", "subtitles.item.flintandsteel.use": "Flint and Steel click", "subtitles.item.hoe.till": "Hoe tills", "subtitles.item.shear": "Shears click", "subtitles.item.shield.block": "Shield blocks", "subtitles.item.shovel.flatten": "<PERSON><PERSON><PERSON> flattens", "subtitles.weather.rain": "Rain falls"}