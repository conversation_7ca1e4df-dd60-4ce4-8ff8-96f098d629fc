[{"id": 1, "displayName": "Stone", "name": "stone", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 2, "displayName": "Granite", "name": "granite", "stackSize": 64}, {"metadata": 2, "id": 3, "displayName": "Polished Granite", "name": "polished_granite", "stackSize": 64}, {"metadata": 3, "id": 4, "displayName": "Diorite", "name": "diorite", "stackSize": 64}, {"metadata": 4, "id": 5, "displayName": "Polished Diorite", "name": "polished_diorite", "stackSize": 64}, {"metadata": 5, "id": 6, "displayName": "Andesite", "name": "andesite", "stackSize": 64}, {"metadata": 6, "id": 7, "displayName": "Polished Andesite", "name": "polished_andesite", "stackSize": 64}]}, {"id": 8, "stackSize": 64, "displayName": "Deepslate", "name": "deepslate"}, {"id": 9, "stackSize": 64, "displayName": "Cobbled Deepslate", "name": "cobbled_deepslate"}, {"id": 10, "stackSize": 64, "displayName": "Polished Deepslate", "name": "polished_deepslate"}, {"id": 11, "stackSize": 64, "displayName": "Calcite", "name": "calcite"}, {"id": 12, "stackSize": 64, "displayName": "<PERSON><PERSON>", "name": "tuff"}, {"id": 13, "stackSize": 64, "displayName": "Dripstone Block", "name": "dripstone_block"}, {"id": 14, "stackSize": 64, "displayName": "Grass Block", "name": "grass"}, {"id": 15, "displayName": "Dirt", "name": "dirt", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 16, "displayName": "Coarse Dirt", "name": "coarse_dirt", "stackSize": 64}]}, {"id": 17, "stackSize": 64, "displayName": "Podzol", "name": "podzol"}, {"id": 18, "stackSize": 64, "displayName": "Rooted Dirt", "name": "dirt_with_roots"}, {"id": 19, "stackSize": 64, "displayName": "Crimson Nylium", "name": "crimson_nylium"}, {"id": 20, "stackSize": 64, "displayName": "Warped Nylium", "name": "warped_nylium"}, {"id": 21, "stackSize": 64, "displayName": "Cobblestone", "name": "cobblestone"}, {"id": 22, "displayName": "Oak Planks", "name": "planks", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 23, "displayName": "Spruce Planks", "name": "spruce_planks", "stackSize": 64}, {"metadata": 2, "id": 24, "displayName": "Birch Planks", "name": "birch_planks", "stackSize": 64}, {"metadata": 3, "id": 25, "displayName": "Jungle Planks", "name": "jungle_planks", "stackSize": 64}, {"metadata": 4, "id": 26, "displayName": "Acacia Planks", "name": "acacia_planks", "stackSize": 64}, {"metadata": 5, "id": 27, "displayName": "Dark Oak Planks", "name": "dark_oak_planks", "stackSize": 64}]}, {"id": 28, "stackSize": 64, "displayName": "Crimson Planks", "name": "crimson_planks"}, {"id": 29, "stackSize": 64, "displayName": "Warped Planks", "name": "warped_planks"}, {"id": 30, "displayName": "Oak Sapling", "name": "sapling", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 31, "displayName": "Spruce Sapling", "name": "spruce_sapling", "stackSize": 64}, {"metadata": 2, "id": 32, "displayName": "Birch Sapling", "name": "birch_sapling", "stackSize": 64}, {"metadata": 3, "id": 33, "displayName": "Jungle Sapling", "name": "jungle_sapling", "stackSize": 64}, {"metadata": 4, "id": 34, "displayName": "Acacia Sapling", "name": "acacia_sapling", "stackSize": 64}, {"metadata": 5, "id": 35, "displayName": "Dark Oak Sapling", "name": "dark_oak_sapling", "stackSize": 64}]}, {"id": 36, "stackSize": 64, "displayName": "Bedrock", "name": "bedrock"}, {"id": 37, "displayName": "Sand", "name": "sand", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 38, "displayName": "Red Sand", "name": "red_sand", "stackSize": 64}]}, {"id": 39, "stackSize": 64, "displayName": "<PERSON>l", "name": "gravel"}, {"id": 40, "stackSize": 64, "displayName": "Coal Ore", "name": "coal_ore"}, {"id": 41, "stackSize": 64, "displayName": "Deepslate Coal Ore", "name": "deepslate_coal_ore"}, {"id": 42, "stackSize": 64, "displayName": "Iron Ore", "name": "iron_ore"}, {"id": 43, "stackSize": 64, "displayName": "Deepslate Iron Ore", "name": "deepslate_iron_ore"}, {"id": 44, "stackSize": 64, "displayName": "Copper Ore", "name": "copper_ore"}, {"id": 45, "stackSize": 64, "displayName": "Deepslate Copper Ore", "name": "deepslate_copper_ore"}, {"id": 46, "stackSize": 64, "displayName": "Gold Ore", "name": "gold_ore"}, {"id": 47, "stackSize": 64, "displayName": "Deepslate Gold Ore", "name": "deepslate_gold_ore"}, {"id": 48, "stackSize": 64, "displayName": "Redstone Ore", "name": "redstone_ore"}, {"id": 49, "stackSize": 64, "displayName": "Deepslate Redstone Ore", "name": "deepslate_redstone_ore"}, {"id": 50, "stackSize": 64, "displayName": "Emerald Ore", "name": "emerald_ore"}, {"id": 51, "stackSize": 64, "displayName": "Deepslate Emerald Ore", "name": "deepslate_emerald_ore"}, {"id": 52, "stackSize": 64, "displayName": "Lapis <PERSON> Ore", "name": "lapis_ore"}, {"id": 53, "stackSize": 64, "displayName": "Deepslate Lapis Lazuli Ore", "name": "deepslate_lapis_ore"}, {"id": 54, "stackSize": 64, "displayName": "Diamond Ore", "name": "diamond_ore"}, {"id": 55, "stackSize": 64, "displayName": "Deepslate Diamond Ore", "name": "deepslate_diamond_ore"}, {"id": 56, "stackSize": 64, "displayName": "Nether Gold Ore", "name": "nether_gold_ore"}, {"id": 57, "stackSize": 64, "displayName": "<PERSON><PERSON>", "name": "quartz_ore"}, {"id": 58, "stackSize": 64, "displayName": "Ancient Debris", "name": "ancient_debris"}, {"id": 59, "stackSize": 64, "displayName": "Block of Coal", "name": "coal_block"}, {"id": 60, "stackSize": 64, "displayName": "Block of Raw Iron", "name": "raw_iron_block"}, {"id": 61, "stackSize": 64, "displayName": "Block of Raw Copper", "name": "raw_copper_block"}, {"id": 62, "stackSize": 64, "displayName": "Block of Raw Gold", "name": "raw_gold_block"}, {"id": 63, "stackSize": 64, "displayName": "Block of Amethyst", "name": "amethyst_block"}, {"id": 64, "stackSize": 64, "displayName": "Budding Amethyst", "name": "budding_amethyst"}, {"id": 65, "stackSize": 64, "displayName": "Block of Iron", "name": "iron_block"}, {"id": 66, "stackSize": 64, "displayName": "Block of Copper", "name": "copper_block"}, {"id": 67, "stackSize": 64, "displayName": "Block of Gold", "name": "gold_block"}, {"id": 68, "stackSize": 64, "displayName": "Block of Diamond", "name": "diamond_block"}, {"id": 69, "stackSize": 64, "displayName": "Block of Netherite", "name": "netherite_block"}, {"id": 70, "stackSize": 64, "displayName": "Exposed Copper", "name": "exposed_copper"}, {"id": 71, "stackSize": 64, "displayName": "Weathered Copper", "name": "weathered_copper"}, {"id": 72, "stackSize": 64, "displayName": "Oxidized Copper", "name": "oxidized_copper"}, {"id": 73, "stackSize": 64, "displayName": "Cut Copper", "name": "cut_copper"}, {"id": 74, "stackSize": 64, "displayName": "Exposed Cut Copper", "name": "exposed_cut_copper"}, {"id": 75, "stackSize": 64, "displayName": "Weathered Cut Copper", "name": "weathered_cut_copper"}, {"id": 76, "stackSize": 64, "displayName": "Oxidized Cut Copper", "name": "oxidized_cut_copper"}, {"id": 77, "stackSize": 64, "displayName": "Cut Copper Stairs", "name": "cut_copper_stairs"}, {"id": 78, "stackSize": 64, "displayName": "Exposed Cut Copper Stairs", "name": "exposed_cut_copper_stairs"}, {"id": 79, "stackSize": 64, "displayName": "Weathered Cut Copper Stairs", "name": "weathered_cut_copper_stairs"}, {"id": 80, "stackSize": 64, "displayName": "Oxidized Cut Copper Stairs", "name": "oxidized_cut_copper_stairs"}, {"id": 81, "stackSize": 64, "displayName": "Cut Copper Slab", "name": "cut_copper_slab"}, {"id": 82, "stackSize": 64, "displayName": "Exposed Cut Copper Slab", "name": "exposed_cut_copper_slab"}, {"id": 83, "stackSize": 64, "displayName": "Weathered Cut Copper Slab", "name": "weathered_cut_copper_slab"}, {"id": 84, "stackSize": 64, "displayName": "Oxidized Cut Copper Slab", "name": "oxidized_cut_copper_slab"}, {"id": 85, "stackSize": 64, "displayName": "Waxed Block of Copper", "name": "waxed_copper"}, {"id": 86, "stackSize": 64, "displayName": "Waxed Exposed Copper", "name": "waxed_exposed_copper"}, {"id": 87, "stackSize": 64, "displayName": "Waxed Weathered Copper", "name": "waxed_weathered_copper"}, {"id": 88, "stackSize": 64, "displayName": "Waxed Oxidized Copper", "name": "waxed_oxidized_copper"}, {"id": 89, "stackSize": 64, "displayName": "Waxed Cut Copper", "name": "waxed_cut_copper"}, {"id": 90, "stackSize": 64, "displayName": "Waxed Exposed Cut Copper", "name": "waxed_exposed_cut_copper"}, {"id": 91, "stackSize": 64, "displayName": "Waxed Weathered Cut Copper", "name": "waxed_weathered_cut_copper"}, {"id": 92, "stackSize": 64, "displayName": "Waxed Oxidized Cut Copper", "name": "waxed_oxidized_cut_copper"}, {"id": 93, "stackSize": 64, "displayName": "Waxed Cut Copper Stairs", "name": "waxed_cut_copper_stairs"}, {"id": 94, "stackSize": 64, "displayName": "Waxed Exposed Cut Copper Stairs", "name": "waxed_exposed_cut_copper_stairs"}, {"id": 95, "stackSize": 64, "displayName": "Waxed Weathered Cut Copper Stairs", "name": "waxed_weathered_cut_copper_stairs"}, {"id": 96, "stackSize": 64, "displayName": "Waxed Oxidized Cut Copper Stairs", "name": "waxed_oxidized_cut_copper_stairs"}, {"id": 97, "stackSize": 64, "displayName": "Waxed Cut Copper Slab", "name": "waxed_cut_copper_slab"}, {"id": 98, "stackSize": 64, "displayName": "Waxed Exposed Cut Copper Slab", "name": "waxed_exposed_cut_copper_slab"}, {"id": 99, "stackSize": 64, "displayName": "Waxed Weathered Cut Copper Slab", "name": "waxed_weathered_cut_copper_slab"}, {"id": 100, "stackSize": 64, "displayName": "Waxed Oxidized Cut Copper Slab", "name": "waxed_oxidized_cut_copper_slab"}, {"id": 101, "displayName": "Oak Log", "name": "log", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 102, "displayName": "Spruce Log", "name": "spruce_log", "stackSize": 64}, {"metadata": 2, "id": 103, "displayName": "Birch Log", "name": "birch_log", "stackSize": 64}, {"metadata": 3, "id": 104, "displayName": "Jungle Log", "name": "jungle_log", "stackSize": 64}]}, {"id": 105, "displayName": "Acacia Log", "name": "log2", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 106, "displayName": "Dark Oak Log", "name": "dark_oak_log", "stackSize": 64}]}, {"id": 107, "stackSize": 64, "displayName": "Crimson Stem", "name": "crimson_stem"}, {"id": 108, "stackSize": 64, "displayName": "Warped Stem", "name": "warped_stem"}, {"id": 109, "stackSize": 64, "displayName": "Stripped Oak Log", "name": "stripped_oak_log"}, {"id": 110, "stackSize": 64, "displayName": "Stripped Spruce Log", "name": "stripped_spruce_log"}, {"id": 111, "stackSize": 64, "displayName": "Stripped Birch Log", "name": "stripped_birch_log"}, {"id": 112, "stackSize": 64, "displayName": "Stripped Jungle Log", "name": "stripped_jungle_log"}, {"id": 113, "stackSize": 64, "displayName": "Stripped Acacia Log", "name": "stripped_acacia_log"}, {"id": 114, "stackSize": 64, "displayName": "Stripped Dark Oak Log", "name": "stripped_dark_oak_log"}, {"id": 115, "stackSize": 64, "displayName": "Stripped Crimson Stem", "name": "stripped_crimson_stem"}, {"id": 116, "stackSize": 64, "displayName": "Stripped Warped Stem", "name": "stripped_warped_stem"}, {"id": 123, "stackSize": 64, "displayName": "Stripped Crimson Hyphae", "name": "stripped_crimson_hyphae"}, {"id": 124, "stackSize": 64, "displayName": "Stripped Warped Hyphae", "name": "stripped_warped_hyphae"}, {"id": 125, "displayName": "Oak Wood", "name": "wood", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 126, "displayName": "Spruce Wood", "name": "spruce_wood", "stackSize": 64}, {"metadata": 2, "id": 127, "displayName": "Birch Wood", "name": "birch_wood", "stackSize": 64}, {"metadata": 3, "id": 128, "displayName": "Jungle Wood", "name": "jungle_wood", "stackSize": 64}, {"metadata": 4, "id": 129, "displayName": "Acacia Wood", "name": "acacia_wood", "stackSize": 64}, {"metadata": 5, "id": 130, "displayName": "Dark Oak Wood", "name": "dark_oak_wood", "stackSize": 64}, {"metadata": 8, "id": 117, "displayName": "Stripped Oak Wood", "name": "stripped_oak_wood", "stackSize": 64}, {"metadata": 9, "id": 118, "displayName": "Stripped Spruce Wood", "name": "stripped_spruce_wood", "stackSize": 64}, {"metadata": 10, "id": 119, "displayName": "Stripped Birch Wood", "name": "stripped_birch_wood", "stackSize": 64}, {"metadata": 11, "id": 120, "displayName": "Stripped Jungle Wood", "name": "stripped_jungle_wood", "stackSize": 64}, {"metadata": 12, "id": 121, "displayName": "Stripped Acacia Wood", "name": "stripped_acacia_wood", "stackSize": 64}, {"metadata": 13, "id": 122, "displayName": "Stripped Dark Oak Wood", "name": "stripped_dark_oak_wood", "stackSize": 64}]}, {"id": 131, "stackSize": 64, "displayName": "Crimson Hyphae", "name": "crimson_hyphae"}, {"id": 132, "stackSize": 64, "displayName": "Warped Hyphae", "name": "warped_hyphae"}, {"id": 133, "displayName": "Oak Leaves", "name": "leaves", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 134, "displayName": "Spruce Leaves", "name": "spruce_leaves", "stackSize": 64}, {"metadata": 2, "id": 135, "displayName": "Birch Leaves", "name": "birch_leaves", "stackSize": 64}, {"metadata": 3, "id": 136, "displayName": "Jungle Leaves", "name": "jungle_leaves", "stackSize": 64}]}, {"id": 137, "displayName": "Acacia Leaves", "name": "leaves2", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 138, "displayName": "Dark Oak Leaves", "name": "dark_oak_leaves", "stackSize": 64}]}, {"id": 139, "stackSize": 64, "displayName": "Azalea Leaves", "name": "azalea_leaves"}, {"id": 140, "stackSize": 64, "displayName": "Flowering Azalea Leaves", "name": "azalea_leaves_flowered"}, {"id": 141, "displayName": "Sponge", "name": "sponge", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 142, "displayName": "Wet Sponge", "name": "wet_sponge", "stackSize": 64}]}, {"id": 143, "stackSize": 64, "displayName": "Glass", "name": "glass"}, {"id": 144, "stackSize": 64, "displayName": "Tinted Glass", "name": "tinted_glass"}, {"id": 145, "stackSize": 64, "displayName": "Block of Lapis Lazuli", "name": "lapis_block"}, {"id": 146, "displayName": "Sandstone", "name": "sandstone", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 147, "displayName": "Chiseled Sandstone", "name": "chiseled_sandstone", "stackSize": 64}, {"metadata": 2, "id": 148, "displayName": "Cut Sandstone", "name": "cut_sandstone", "stackSize": 64}, {"metadata": 3, "id": 230, "displayName": "Smooth Sandstone", "name": "smooth_sandstone", "stackSize": 64}]}, {"id": 149, "stackSize": 64, "displayName": "Cobweb", "name": "web"}, {"id": 150, "displayName": "Grass", "name": "tallgrass", "stackSize": 64, "metadata": 1, "variations": [{"metadata": 2, "id": 151, "displayName": "Fern", "name": "fern", "stackSize": 64}]}, {"id": 152, "stackSize": 64, "displayName": "Azalea", "name": "azalea"}, {"id": 153, "stackSize": 64, "displayName": "Flowering Azalea", "name": "flowering_azalea"}, {"id": 154, "stackSize": 64, "displayName": "Dead Bush", "name": "deadbush"}, {"id": 155, "stackSize": 64, "displayName": "Seagrass", "name": "seagrass"}, {"id": 156, "stackSize": 64, "displayName": "<PERSON>", "name": "sea_pickle"}, {"id": 157, "displayName": "White Wool", "name": "wool", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 158, "displayName": "Orange Wool", "name": "orange_wool", "stackSize": 64}, {"metadata": 2, "id": 159, "displayName": "Magenta Wool", "name": "magenta_wool", "stackSize": 64}, {"metadata": 3, "id": 160, "displayName": "Light Blue Wool", "name": "light_blue_wool", "stackSize": 64}, {"metadata": 4, "id": 161, "displayName": "Yellow Wool", "name": "yellow_wool", "stackSize": 64}, {"metadata": 5, "id": 162, "displayName": "Lime Wool", "name": "lime_wool", "stackSize": 64}, {"metadata": 6, "id": 163, "displayName": "Pink Wool", "name": "pink_wool", "stackSize": 64}, {"metadata": 7, "id": 164, "displayName": "Gray <PERSON>", "name": "gray_wool", "stackSize": 64}, {"metadata": 8, "id": 165, "displayName": "Light Gray Wool", "name": "light_gray_wool", "stackSize": 64}, {"metadata": 9, "id": 166, "displayName": "<PERSON><PERSON>", "name": "cyan_wool", "stackSize": 64}, {"metadata": 10, "id": 167, "displayName": "Purple Wool", "name": "purple_wool", "stackSize": 64}, {"metadata": 11, "id": 168, "displayName": "Blue Wool", "name": "blue_wool", "stackSize": 64}, {"metadata": 12, "id": 169, "displayName": "Brown Wool", "name": "brown_wool", "stackSize": 64}, {"metadata": 13, "id": 170, "displayName": "Green Wool", "name": "green_wool", "stackSize": 64}, {"metadata": 14, "id": 171, "displayName": "Red Wool", "name": "red_wool", "stackSize": 64}, {"metadata": 15, "id": 172, "displayName": "Black Wool", "name": "black_wool", "stackSize": 64}]}, {"id": 173, "stackSize": 64, "displayName": "Dandelion", "name": "yellow_flower"}, {"id": 174, "displayName": "<PERSON><PERSON>", "name": "red_flower", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 175, "displayName": "Blue Orchid", "name": "blue_orchid", "stackSize": 64}, {"metadata": 2, "id": 176, "displayName": "Allium", "name": "allium", "stackSize": 64}, {"metadata": 3, "id": 177, "displayName": "Azure Bluet", "name": "azure_bluet", "stackSize": 64}, {"metadata": 4, "id": 178, "displayName": "<PERSON>lip", "name": "red_tulip", "stackSize": 64}, {"metadata": 5, "id": 179, "displayName": "Orange Tulip", "name": "orange_tulip", "stackSize": 64}, {"metadata": 6, "id": 180, "displayName": "White Tulip", "name": "white_tulip", "stackSize": 64}, {"metadata": 7, "id": 181, "displayName": "<PERSON> Tulip", "name": "pink_tulip", "stackSize": 64}, {"metadata": 8, "id": 182, "displayName": "Oxeye Daisy", "name": "oxeye_daisy", "stackSize": 64}, {"metadata": 9, "id": 183, "displayName": "Corn<PERSON>", "name": "cornflower", "stackSize": 64}, {"metadata": 10, "id": 184, "displayName": "Lily of the Valley", "name": "lily_of_the_valley", "stackSize": 64}]}, {"id": 185, "stackSize": 64, "displayName": "<PERSON><PERSON>", "name": "wither_rose"}, {"id": 186, "stackSize": 64, "displayName": "Spore Blossom", "name": "spore_blossom"}, {"id": 187, "stackSize": 64, "displayName": "Brown Mushroom", "name": "brown_mushroom"}, {"id": 188, "stackSize": 64, "displayName": "Red Mushroom", "name": "red_mushroom"}, {"id": 189, "stackSize": 64, "displayName": "Crimson Fungus", "name": "crimson_fungus"}, {"id": 190, "stackSize": 64, "displayName": "Warped Fungus", "name": "warped_fungus"}, {"id": 191, "stackSize": 64, "displayName": "Crimson Roots", "name": "crimson_roots"}, {"id": 192, "stackSize": 64, "displayName": "Warped Roots", "name": "warped_roots"}, {"id": 193, "stackSize": 64, "displayName": "Nether Sprouts", "name": "nether_sprouts"}, {"id": 194, "stackSize": 64, "displayName": "Weeping Vines", "name": "weeping_vines"}, {"id": 195, "stackSize": 64, "displayName": "Twisting Vines", "name": "twisting_vines"}, {"id": 196, "stackSize": 64, "displayName": "Sugar Cane", "name": "sugar_cane"}, {"id": 197, "stackSize": 64, "displayName": "<PERSON><PERSON><PERSON>", "name": "kelp"}, {"id": 197, "displayName": "Sugar Cane", "name": "reeds", "stackSize": 64}, {"id": 198, "stackSize": 64, "displayName": "Moss Carpet", "name": "moss_carpet"}, {"id": 199, "stackSize": 64, "displayName": "Moss Block", "name": "moss_block"}, {"id": 200, "stackSize": 64, "displayName": "Hanging Roots", "name": "hanging_roots"}, {"id": 201, "stackSize": 64, "displayName": "Big Dripleaf", "name": "big_dripleaf"}, {"id": 202, "stackSize": 64, "displayName": "Small Dripleaf", "name": "small_dripleaf_block"}, {"id": 203, "stackSize": 64, "displayName": "Bamboo", "name": "bamboo"}, {"id": 204, "displayName": "Oak Slab", "name": "wooden_slab", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 205, "displayName": "Spruce Slab", "name": "spruce_slab", "stackSize": 64}, {"metadata": 2, "id": 206, "displayName": "<PERSON>", "name": "birch_slab", "stackSize": 64}, {"metadata": 3, "id": 207, "displayName": "Jungle Slab", "name": "jungle_slab", "stackSize": 64}, {"metadata": 4, "id": 208, "displayName": "Acacia <PERSON>b", "name": "acacia_slab", "stackSize": 64}, {"metadata": 5, "id": 209, "displayName": "Dark Oak Slab", "name": "dark_oak_slab", "stackSize": 64}]}, {"id": 210, "stackSize": 64, "displayName": "Crimson Slab", "name": "crimson_slab"}, {"id": 211, "stackSize": 64, "displayName": "Warped Slab", "name": "warped_slab"}, {"id": 213, "displayName": "Smooth Stone Slab", "name": "double_stone_slab", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 214, "displayName": "Sandstone Slab", "name": "sandstone_slab", "stackSize": 64}, {"metadata": 2, "id": 216, "displayName": "Petrified Oak Slab", "name": "petrified_oak_slab", "stackSize": 64}, {"metadata": 3, "id": 217, "displayName": "Cobblestone Slab", "name": "cobblestone_slab", "stackSize": 64}, {"metadata": 4, "id": 218, "displayName": "Brick Slab", "name": "brick_slab", "stackSize": 64}, {"metadata": 5, "id": 219, "displayName": "Stone Brick Slab", "name": "stone_brick_slab", "stackSize": 64}, {"metadata": 6, "id": 221, "displayName": "Quartz Slab", "name": "quartz_slab", "stackSize": 64}, {"metadata": 7, "id": 220, "displayName": "Nether Brick Slab", "name": "nether_brick_slab", "stackSize": 64}]}, {"id": 222, "displayName": "Red Sandstone Slab", "name": "double_stone_slab2", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 224, "displayName": "Purpur Slab", "name": "purpur_slab", "stackSize": 64}, {"metadata": 2, "id": 225, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "prismarine_slab", "stackSize": 64}, {"metadata": 3, "id": 227, "displayName": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "name": "dark_prismarine_slab", "stackSize": 64}, {"metadata": 4, "id": 226, "displayName": "Prismarine Brick Slab", "name": "prismarine_brick_slab", "stackSize": 64}, {"metadata": 5, "id": 571, "displayName": "<PERSON><PERSON> Slab", "name": "mossy_cobblestone_slab", "stackSize": 64}, {"metadata": 6, "id": 573, "displayName": "Smooth Sandstone Slab", "name": "smooth_sandstone_slab", "stackSize": 64}, {"metadata": 7, "id": 577, "displayName": "Red Nether Brick Slab", "name": "red_nether_brick_slab", "stackSize": 64}]}, {"id": 231, "stackSize": 64, "displayName": "Smooth Stone", "name": "smooth_stone"}, {"id": 232, "stackSize": 64, "displayName": "Bricks", "name": "brick_block"}, {"id": 233, "stackSize": 64, "displayName": "Bookshelf", "name": "bookshelf"}, {"id": 234, "stackSize": 64, "displayName": "<PERSON><PERSON>", "name": "mossy_cobblestone"}, {"id": 235, "stackSize": 64, "displayName": "Obsidian", "name": "obsidian"}, {"id": 236, "stackSize": 64, "displayName": "<PERSON>ch", "name": "torch"}, {"id": 237, "stackSize": 64, "displayName": "End Rod", "name": "end_rod"}, {"id": 238, "stackSize": 64, "displayName": "Chorus Plant", "name": "chorus_plant"}, {"id": 239, "stackSize": 64, "displayName": "Chorus Flower", "name": "chorus_flower"}, {"id": 240, "displayName": "Purpur Block", "name": "purpur_block", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 2, "id": 241, "displayName": "Purpur Pillar", "name": "purpur_pillar", "stackSize": 64}]}, {"id": 242, "stackSize": 64, "displayName": "Purpur Stairs", "name": "purpur_stairs"}, {"id": 243, "stackSize": 64, "displayName": "Spawner", "name": "mob_spawner"}, {"id": 244, "stackSize": 64, "displayName": "Oak Stairs", "name": "oak_stairs"}, {"id": 245, "stackSize": 64, "displayName": "Chest", "name": "chest"}, {"id": 246, "stackSize": 64, "displayName": "Crafting Table", "name": "crafting_table"}, {"id": 247, "stackSize": 64, "displayName": "Farmland", "name": "farmland"}, {"id": 248, "stackSize": 64, "displayName": "Furnace", "name": "furnace"}, {"id": 249, "stackSize": 64, "displayName": "Ladder", "name": "ladder"}, {"id": 250, "stackSize": 64, "displayName": "Cobblestone Stairs", "name": "stone_stairs"}, {"id": 251, "stackSize": 64, "displayName": "Snow", "name": "snow_layer"}, {"id": 252, "stackSize": 64, "displayName": "Ice", "name": "ice"}, {"id": 253, "stackSize": 64, "displayName": "Snow Block", "name": "snow"}, {"id": 254, "stackSize": 64, "displayName": "Cactus", "name": "cactus"}, {"id": 255, "stackSize": 64, "displayName": "<PERSON>", "name": "clay"}, {"id": 256, "stackSize": 64, "displayName": "Jukebox", "name": "jukebox"}, {"id": 257, "displayName": "Oak Fence", "name": "fence", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 258, "displayName": "Spruce Fence", "name": "spruce_fence", "stackSize": 64}, {"metadata": 2, "id": 259, "displayName": "<PERSON>", "name": "birch_fence", "stackSize": 64}, {"metadata": 3, "id": 260, "displayName": "Jungle Fence", "name": "jungle_fence", "stackSize": 64}, {"metadata": 4, "id": 261, "displayName": "Acacia Fence", "name": "acacia_fence", "stackSize": 64}, {"metadata": 5, "id": 262, "displayName": "Dark Oak Fence", "name": "dark_oak_fence", "stackSize": 64}]}, {"id": 263, "stackSize": 64, "displayName": "<PERSON> Fence", "name": "crimson_fence"}, {"id": 264, "stackSize": 64, "displayName": "Warped <PERSON>", "name": "warped_fence"}, {"id": 265, "stackSize": 64, "displayName": "<PERSON><PERSON><PERSON>", "name": "pumpkin"}, {"id": 266, "stackSize": 64, "displayName": "<PERSON><PERSON>", "name": "carved_pumpkin", "enchantCategories": ["wearable", "vanishable"]}, {"id": 267, "stackSize": 64, "displayName": "<PERSON>'<PERSON>", "name": "lit_pumpkin"}, {"id": 268, "stackSize": 64, "displayName": "Netherrack", "name": "netherrack"}, {"id": 269, "stackSize": 64, "displayName": "Soul Sand", "name": "soul_sand"}, {"id": 270, "stackSize": 64, "displayName": "Soul Soil", "name": "soul_soil"}, {"id": 271, "stackSize": 64, "displayName": "Basalt", "name": "basalt"}, {"id": 272, "stackSize": 64, "displayName": "Polished Ba<PERSON>t", "name": "polished_basalt"}, {"id": 273, "stackSize": 64, "displayName": "Smooth Basalt", "name": "smooth_basalt"}, {"id": 274, "stackSize": 64, "displayName": "Soul Torch", "name": "soul_torch"}, {"id": 275, "stackSize": 64, "displayName": "Glowstone", "name": "glowstone"}, {"id": 276, "displayName": "Infested Stone", "name": "monster_egg", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 277, "displayName": "Infested Cobblestone", "name": "infested_cobblestone", "stackSize": 64}, {"metadata": 2, "id": 278, "displayName": "Infested Stone Bricks", "name": "infested_stone_bricks", "stackSize": 64}, {"metadata": 3, "id": 279, "displayName": "Infested Mossy Stone Bricks", "name": "infested_mossy_stone_bricks", "stackSize": 64}, {"metadata": 4, "id": 280, "displayName": "Infested Cracked Stone Bricks", "name": "infested_cracked_stone_bricks", "stackSize": 64}, {"metadata": 5, "id": 281, "displayName": "Infested Chiseled Stone Bricks", "name": "infested_chiseled_stone_bricks", "stackSize": 64}]}, {"id": 282, "stackSize": 64, "displayName": "Infested Deepslate", "name": "infested_deepslate"}, {"id": 283, "displayName": "Stone Bricks", "name": "stonebrick", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 284, "displayName": "Mossy Stone Bricks", "name": "mossy_stone_bricks", "stackSize": 64}, {"metadata": 2, "id": 285, "displayName": "Cracked Stone Bricks", "name": "cracked_stone_bricks", "stackSize": 64}, {"metadata": 3, "id": 286, "displayName": "Chiseled Stone Bricks", "name": "chiseled_stone_bricks", "stackSize": 64}]}, {"id": 287, "stackSize": 64, "displayName": "Deepslate Bricks", "name": "deepslate_bricks"}, {"id": 288, "stackSize": 64, "displayName": "Cracked Deepslate Bricks", "name": "cracked_deepslate_bricks"}, {"id": 289, "stackSize": 64, "displayName": "Deepslate Tiles", "name": "deepslate_tiles"}, {"id": 290, "stackSize": 64, "displayName": "Cracked Deepslate Tiles", "name": "cracked_deepslate_tiles"}, {"id": 291, "stackSize": 64, "displayName": "Chiseled Deepslate", "name": "chiseled_deepslate"}, {"id": 292, "displayName": "Brown Mushroom Block", "name": "brown_mushroom_block", "stackSize": 64, "metadata": 14, "variations": [{"metadata": 15, "id": 294, "displayName": "Mushroom Stem", "name": "mushroom_stem", "stackSize": 64}]}, {"id": 293, "stackSize": 64, "displayName": "Red Mushroom Block", "name": "red_mushroom_block"}, {"id": 295, "stackSize": 64, "displayName": "Iron Bars", "name": "iron_bars"}, {"id": 296, "stackSize": 64, "displayName": "Chain", "name": "chain"}, {"id": 297, "stackSize": 64, "displayName": "Glass Pane", "name": "glass_pane"}, {"id": 298, "stackSize": 64, "displayName": "Melon", "name": "melon_block"}, {"id": 299, "stackSize": 64, "displayName": "Vines", "name": "vine"}, {"id": 300, "stackSize": 64, "displayName": "Glow Lichen", "name": "glow_lichen"}, {"id": 301, "stackSize": 64, "displayName": "Brick Stairs", "name": "brick_stairs"}, {"id": 302, "stackSize": 64, "displayName": "Stone Brick Stairs", "name": "stone_brick_stairs"}, {"id": 303, "stackSize": 64, "displayName": "Mycelium", "name": "mycelium"}, {"id": 304, "stackSize": 64, "displayName": "<PERSON>", "name": "waterlily"}, {"id": 305, "stackSize": 64, "displayName": "Nether Bricks", "name": "nether_brick"}, {"id": 306, "stackSize": 64, "displayName": "Cracked Nether Bricks", "name": "cracked_nether_bricks"}, {"id": 307, "stackSize": 64, "displayName": "Chiseled Nether Bricks", "name": "chiseled_nether_bricks"}, {"id": 308, "stackSize": 64, "displayName": "Nether Brick Fence", "name": "nether_brick_fence"}, {"id": 309, "stackSize": 64, "displayName": "Nether Brick Stairs", "name": "nether_brick_stairs"}, {"id": 310, "stackSize": 64, "displayName": "Enchanting Table", "name": "enchanting_table"}, {"id": 311, "stackSize": 64, "displayName": "End Portal Frame", "name": "end_portal_frame"}, {"id": 312, "stackSize": 64, "displayName": "End Stone", "name": "end_stone"}, {"id": 313, "stackSize": 64, "displayName": "End Stone Bricks", "name": "end_bricks"}, {"id": 314, "stackSize": 64, "displayName": "Dragon Egg", "name": "dragon_egg"}, {"id": 315, "stackSize": 64, "displayName": "Sandstone Stairs", "name": "sandstone_stairs"}, {"id": 316, "stackSize": 64, "displayName": "<PERSON><PERSON> Chest", "name": "ender_chest"}, {"id": 317, "stackSize": 64, "displayName": "Block of Emerald", "name": "emerald_block"}, {"id": 318, "stackSize": 64, "displayName": "Spruce Stairs", "name": "spruce_stairs"}, {"id": 319, "stackSize": 64, "displayName": "<PERSON> Stairs", "name": "birch_stairs"}, {"id": 320, "stackSize": 64, "displayName": "Jungle Stairs", "name": "jungle_stairs"}, {"id": 321, "stackSize": 64, "displayName": "Crimson Stairs", "name": "crimson_stairs"}, {"id": 322, "stackSize": 64, "displayName": "Warped Stairs", "name": "warped_stairs"}, {"id": 323, "stackSize": 64, "displayName": "Command Block", "name": "command_block"}, {"id": 324, "stackSize": 64, "displayName": "Beacon", "name": "beacon"}, {"id": 325, "displayName": "Cobblestone Wall", "name": "cobblestone_wall", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 326, "displayName": "<PERSON><PERSON>", "name": "mossy_cobblestone_wall", "stackSize": 64}, {"metadata": 2, "id": 331, "displayName": "Granite Wall", "name": "granite_wall", "stackSize": 64}, {"metadata": 3, "id": 338, "displayName": "Diorite Wall", "name": "diorite_wall", "stackSize": 64}, {"metadata": 4, "id": 334, "displayName": "Andesite Wall", "name": "andesite_wall", "stackSize": 64}, {"metadata": 5, "id": 336, "displayName": "Sandstone Wall", "name": "sandstone_wall", "stackSize": 64}, {"metadata": 6, "id": 327, "displayName": "Brick Wall", "name": "brick_wall", "stackSize": 64}, {"metadata": 7, "id": 332, "displayName": "Stone Brick Wall", "name": "stone_brick_wall", "stackSize": 64}, {"metadata": 8, "id": 330, "displayName": "Mossy Stone Brick Wall", "name": "mossy_stone_brick_wall", "stackSize": 64}, {"metadata": 9, "id": 333, "displayName": "Nether Brick Wall", "name": "nether_brick_wall", "stackSize": 64}, {"metadata": 10, "id": 337, "displayName": "End Stone Brick Wall", "name": "end_stone_brick_wall", "stackSize": 64}, {"metadata": 11, "id": 328, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "prismarine_wall", "stackSize": 64}, {"metadata": 12, "id": 329, "displayName": "Red Sandstone Wall", "name": "red_sandstone_wall", "stackSize": 64}, {"metadata": 13, "id": 335, "displayName": "Red Nether Brick Wall", "name": "red_nether_brick_wall", "stackSize": 64}]}, {"id": 339, "stackSize": 64, "displayName": "Blackstone Wall", "name": "blackstone_wall"}, {"id": 340, "stackSize": 64, "displayName": "Polished Blackstone Wall", "name": "polished_blackstone_wall"}, {"id": 341, "stackSize": 64, "displayName": "Polished Blackstone Brick Wall", "name": "polished_blackstone_brick_wall"}, {"id": 342, "stackSize": 64, "displayName": "Cobbled Deepslate Wall", "name": "cobbled_deepslate_wall"}, {"id": 343, "stackSize": 64, "displayName": "Polished Deepslate Wall", "name": "polished_deepslate_wall"}, {"id": 344, "stackSize": 64, "displayName": "Deepslate Brick Wall", "name": "deepslate_brick_wall"}, {"id": 345, "stackSize": 64, "displayName": "Deepslate Tile Wall", "name": "deepslate_tile_wall"}, {"id": 346, "displayName": "An<PERSON>", "name": "anvil", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 4, "id": 347, "displayName": "Chipped Anvil", "name": "chipped_anvil", "stackSize": 64}, {"metadata": 8, "id": 348, "displayName": "Damaged Anvil", "name": "damaged_anvil", "stackSize": 64}]}, {"id": 350, "displayName": "Block of Quartz", "name": "quartz_block", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 349, "displayName": "Chiseled Quartz Block", "name": "chiseled_quartz_block", "stackSize": 64}, {"metadata": 2, "id": 352, "displayName": "Quartz <PERSON>", "name": "quartz_pillar", "stackSize": 64}, {"metadata": 3, "id": 228, "displayName": "Smooth Quartz Block", "name": "smooth_quartz", "stackSize": 64}]}, {"id": 351, "stackSize": 64, "displayName": "Quartz Bricks", "name": "quartz_bricks"}, {"id": 353, "stackSize": 64, "displayName": "Quartz Stairs", "name": "quartz_stairs"}, {"id": 354, "displayName": "White Terracotta", "name": "stained_hardened_clay", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 355, "displayName": "Orange Terracotta", "name": "orange_terracotta", "stackSize": 64}, {"metadata": 2, "id": 356, "displayName": "Magenta Terracotta", "name": "magenta_terracotta", "stackSize": 64}, {"metadata": 3, "id": 357, "displayName": "Light Blue Terracotta", "name": "light_blue_terracotta", "stackSize": 64}, {"metadata": 4, "id": 358, "displayName": "Yellow Terracotta", "name": "yellow_terracotta", "stackSize": 64}, {"metadata": 5, "id": 359, "displayName": "Lime Terracotta", "name": "lime_terracotta", "stackSize": 64}, {"metadata": 6, "id": 360, "displayName": "Pink Terracotta", "name": "pink_terracotta", "stackSize": 64}, {"metadata": 7, "id": 361, "displayName": "Gray <PERSON>", "name": "gray_terracotta", "stackSize": 64}, {"metadata": 8, "id": 362, "displayName": "Light Gray Terracotta", "name": "light_gray_terracotta", "stackSize": 64}, {"metadata": 9, "id": 363, "displayName": "<PERSON><PERSON>", "name": "cyan_terracotta", "stackSize": 64}, {"metadata": 10, "id": 364, "displayName": "Purple Terracotta", "name": "purple_terracotta", "stackSize": 64}, {"metadata": 11, "id": 365, "displayName": "Blue Terracotta", "name": "blue_terracotta", "stackSize": 64}, {"metadata": 12, "id": 366, "displayName": "Brown Terracotta", "name": "brown_terracotta", "stackSize": 64}, {"metadata": 13, "id": 367, "displayName": "Green Terracotta", "name": "green_terracotta", "stackSize": 64}, {"metadata": 14, "id": 368, "displayName": "Red Terracotta", "name": "red_terracotta", "stackSize": 64}, {"metadata": 15, "id": 369, "displayName": "Black Terracotta", "name": "black_terracotta", "stackSize": 64}]}, {"id": 370, "stackSize": 64, "displayName": "Barrier", "name": "barrier"}, {"id": 371, "stackSize": 64, "displayName": "Light", "name": "light_block"}, {"id": 372, "stackSize": 64, "displayName": "<PERSON>", "name": "hay_block"}, {"id": 373, "displayName": "White Carpet", "name": "carpet", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 374, "displayName": "Orange Carpet", "name": "orange_carpet", "stackSize": 64}, {"metadata": 2, "id": 375, "displayName": "Magenta Carpet", "name": "magenta_carpet", "stackSize": 64}, {"metadata": 3, "id": 376, "displayName": "Light Blue Carpet", "name": "light_blue_carpet", "stackSize": 64}, {"metadata": 4, "id": 377, "displayName": "Yellow Carpet", "name": "yellow_carpet", "stackSize": 64}, {"metadata": 5, "id": 378, "displayName": "Lime Carpet", "name": "lime_carpet", "stackSize": 64}, {"metadata": 6, "id": 379, "displayName": "Pink Carpet", "name": "pink_carpet", "stackSize": 64}, {"metadata": 7, "id": 380, "displayName": "<PERSON> Carpet", "name": "gray_carpet", "stackSize": 64}, {"metadata": 8, "id": 381, "displayName": "Light Gray Carpet", "name": "light_gray_carpet", "stackSize": 64}, {"metadata": 9, "id": 382, "displayName": "<PERSON><PERSON>", "name": "cyan_carpet", "stackSize": 64}, {"metadata": 10, "id": 383, "displayName": "Purple Carpet", "name": "purple_carpet", "stackSize": 64}, {"metadata": 11, "id": 384, "displayName": "Blue Carpet", "name": "blue_carpet", "stackSize": 64}, {"metadata": 12, "id": 385, "displayName": "Brown Carpet", "name": "brown_carpet", "stackSize": 64}, {"metadata": 13, "id": 386, "displayName": "Green Carpet", "name": "green_carpet", "stackSize": 64}, {"metadata": 14, "id": 387, "displayName": "Red Carpet", "name": "red_carpet", "stackSize": 64}, {"metadata": 15, "id": 388, "displayName": "Black Carpet", "name": "black_carpet", "stackSize": 64}]}, {"id": 389, "stackSize": 64, "displayName": "Terracotta", "name": "hardened_clay"}, {"id": 390, "stackSize": 64, "displayName": "Packed Ice", "name": "packed_ice"}, {"id": 391, "stackSize": 64, "displayName": "Acacia Stairs", "name": "acacia_stairs"}, {"id": 392, "stackSize": 64, "displayName": "Dark Oak Stairs", "name": "dark_oak_stairs"}, {"id": 393, "stackSize": 64, "displayName": "Dirt Path", "name": "grass_path"}, {"id": 394, "displayName": "Sunflower", "name": "double_plant", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 395, "displayName": "Lilac", "name": "lilac", "stackSize": 64}, {"metadata": 2, "id": 398, "displayName": "Tall Grass", "name": "tall_grass", "stackSize": 64}, {"metadata": 3, "id": 399, "displayName": "Large Fern", "name": "large_fern", "stackSize": 64}, {"metadata": 4, "id": 396, "displayName": "<PERSON>", "name": "rose_bush", "stackSize": 64}, {"metadata": 5, "id": 397, "displayName": "Peony", "name": "peony", "stackSize": 64}]}, {"id": 400, "displayName": "White Stained Glass", "name": "stained_glass", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 401, "displayName": "Orange Stained Glass", "name": "orange_stained_glass", "stackSize": 64}, {"metadata": 2, "id": 402, "displayName": "Magenta Stained Glass", "name": "magenta_stained_glass", "stackSize": 64}, {"metadata": 3, "id": 403, "displayName": "Light Blue Stained Glass", "name": "light_blue_stained_glass", "stackSize": 64}, {"metadata": 4, "id": 404, "displayName": "Yellow Stained Glass", "name": "yellow_stained_glass", "stackSize": 64}, {"metadata": 5, "id": 405, "displayName": "Lime Stained Glass", "name": "lime_stained_glass", "stackSize": 64}, {"metadata": 6, "id": 406, "displayName": "Pink Stained Glass", "name": "pink_stained_glass", "stackSize": 64}, {"metadata": 7, "id": 407, "displayName": "<PERSON> Stained Glass", "name": "gray_stained_glass", "stackSize": 64}, {"metadata": 8, "id": 408, "displayName": "Light Gray Stained Glass", "name": "light_gray_stained_glass", "stackSize": 64}, {"metadata": 9, "id": 409, "displayName": "<PERSON><PERSON>", "name": "cyan_stained_glass", "stackSize": 64}, {"metadata": 10, "id": 410, "displayName": "Purple Stained Glass", "name": "purple_stained_glass", "stackSize": 64}, {"metadata": 11, "id": 411, "displayName": "Blue Stained Glass", "name": "blue_stained_glass", "stackSize": 64}, {"metadata": 12, "id": 412, "displayName": "<PERSON> Stained Glass", "name": "brown_stained_glass", "stackSize": 64}, {"metadata": 13, "id": 413, "displayName": "Green Stained Glass", "name": "green_stained_glass", "stackSize": 64}, {"metadata": 14, "id": 414, "displayName": "Red Stained Glass", "name": "red_stained_glass", "stackSize": 64}, {"metadata": 15, "id": 415, "displayName": "Black Stained Glass", "name": "black_stained_glass", "stackSize": 64}]}, {"id": 416, "displayName": "White Stained Glass Pane", "name": "stained_glass_pane", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 417, "displayName": "Orange Stained Glass Pane", "name": "orange_stained_glass_pane", "stackSize": 64}, {"metadata": 2, "id": 418, "displayName": "Magenta Stained Glass Pane", "name": "magenta_stained_glass_pane", "stackSize": 64}, {"metadata": 3, "id": 419, "displayName": "Light Blue Stained Glass Pane", "name": "light_blue_stained_glass_pane", "stackSize": 64}, {"metadata": 4, "id": 420, "displayName": "Yellow Stained Glass Pane", "name": "yellow_stained_glass_pane", "stackSize": 64}, {"metadata": 5, "id": 421, "displayName": "Lime Stained Glass Pane", "name": "lime_stained_glass_pane", "stackSize": 64}, {"metadata": 6, "id": 422, "displayName": "Pink Stained Glass Pane", "name": "pink_stained_glass_pane", "stackSize": 64}, {"metadata": 7, "id": 423, "displayName": "Gray Stained Glass Pane", "name": "gray_stained_glass_pane", "stackSize": 64}, {"metadata": 8, "id": 424, "displayName": "Light Gray Stained Glass Pane", "name": "light_gray_stained_glass_pane", "stackSize": 64}, {"metadata": 9, "id": 425, "displayName": "<PERSON><PERSON> Stained Glass Pane", "name": "cyan_stained_glass_pane", "stackSize": 64}, {"metadata": 10, "id": 426, "displayName": "Purple Stained Glass Pane", "name": "purple_stained_glass_pane", "stackSize": 64}, {"metadata": 11, "id": 427, "displayName": "Blue Stained Glass Pane", "name": "blue_stained_glass_pane", "stackSize": 64}, {"metadata": 12, "id": 428, "displayName": "<PERSON> Stained Glass Pane", "name": "brown_stained_glass_pane", "stackSize": 64}, {"metadata": 13, "id": 429, "displayName": "Green Stained Glass Pane", "name": "green_stained_glass_pane", "stackSize": 64}, {"metadata": 14, "id": 430, "displayName": "Red Stained Glass Pane", "name": "red_stained_glass_pane", "stackSize": 64}, {"metadata": 15, "id": 431, "displayName": "Black Stained Glass Pane", "name": "black_stained_glass_pane", "stackSize": 64}]}, {"id": 432, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "prismarine", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 434, "displayName": "<PERSON>", "name": "dark_prismarine", "stackSize": 64}, {"metadata": 2, "id": 433, "displayName": "Prismarine <PERSON>s", "name": "prismarine_bricks", "stackSize": 64}]}, {"id": 435, "stackSize": 64, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "prismarine_stairs"}, {"id": 436, "stackSize": 64, "displayName": "Prismarine Brick Stairs", "name": "prismarine_bricks_stairs"}, {"id": 437, "stackSize": 64, "displayName": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "name": "dark_prismarine_stairs"}, {"id": 438, "stackSize": 64, "displayName": "Sea Lantern", "name": "sea_lantern"}, {"id": 439, "displayName": "Red Sandstone", "name": "red_sandstone", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 440, "displayName": "Chiseled Red Sandstone", "name": "chiseled_red_sandstone", "stackSize": 64}, {"metadata": 2, "id": 441, "displayName": "Cut Red Sandstone", "name": "cut_red_sandstone", "stackSize": 64}, {"metadata": 3, "id": 229, "displayName": "Smooth Red Sandstone", "name": "smooth_red_sandstone", "stackSize": 64}]}, {"id": 442, "stackSize": 64, "displayName": "Red Sandstone Stairs", "name": "red_sandstone_stairs"}, {"id": 443, "stackSize": 64, "displayName": "Repeating Command Block", "name": "repeating_command_block"}, {"id": 444, "stackSize": 64, "displayName": "Chain Command Block", "name": "chain_command_block"}, {"id": 445, "stackSize": 64, "displayName": "Magma Block", "name": "magma"}, {"id": 446, "stackSize": 64, "displayName": "Nether Wart Block", "name": "nether_wart_block"}, {"id": 447, "stackSize": 64, "displayName": "Warped Wart Block", "name": "warped_wart_block"}, {"id": 448, "stackSize": 64, "displayName": "Red Nether Bricks", "name": "red_nether_brick"}, {"id": 449, "stackSize": 64, "displayName": "Bone Block", "name": "bone_block"}, {"id": 450, "stackSize": 64, "displayName": "Structure Void", "name": "structure_void"}, {"id": 451, "stackSize": 1, "displayName": "Shulker Box", "name": "undyed_shulker_box"}, {"id": 452, "displayName": "White Shulker Box", "name": "shulker_box", "stackSize": 1, "metadata": 0, "variations": [{"metadata": 1, "id": 453, "displayName": "Orange Shulker Box", "name": "orange_shulker_box", "stackSize": 1}, {"metadata": 2, "id": 454, "displayName": "<PERSON><PERSON>a <PERSON>er Box", "name": "magenta_shulker_box", "stackSize": 1}, {"metadata": 3, "id": 455, "displayName": "Light Blue Shulker Box", "name": "light_blue_shulker_box", "stackSize": 1}, {"metadata": 4, "id": 456, "displayName": "Yellow Shulker Box", "name": "yellow_shulker_box", "stackSize": 1}, {"metadata": 5, "id": 457, "displayName": "<PERSON>e <PERSON>er Box", "name": "lime_shulker_box", "stackSize": 1}, {"metadata": 6, "id": 458, "displayName": "Pink Shulker Box", "name": "pink_shulker_box", "stackSize": 1}, {"metadata": 7, "id": 459, "displayName": "<PERSON>", "name": "gray_shulker_box", "stackSize": 1}, {"metadata": 8, "id": 460, "displayName": "Light Gray Shulker Box", "name": "light_gray_shulker_box", "stackSize": 1}, {"metadata": 9, "id": 461, "displayName": "<PERSON><PERSON>", "name": "cyan_shulker_box", "stackSize": 1}, {"metadata": 10, "id": 462, "displayName": "Purple Shulker Box", "name": "purple_shulker_box", "stackSize": 1}, {"metadata": 11, "id": 463, "displayName": "Blue Shulker Box", "name": "blue_shulker_box", "stackSize": 1}, {"metadata": 12, "id": 464, "displayName": "<PERSON> Shulker Box", "name": "brown_shulker_box", "stackSize": 1}, {"metadata": 13, "id": 465, "displayName": "Green Shulker Box", "name": "green_shulker_box", "stackSize": 1}, {"metadata": 14, "id": 466, "displayName": "Red Shulker Box", "name": "red_shulker_box", "stackSize": 1}, {"metadata": 15, "id": 467, "displayName": "Black Shulker Box", "name": "black_shulker_box", "stackSize": 1}]}, {"id": 468, "stackSize": 64, "displayName": "White Glazed Terracotta", "name": "white_glazed_terracotta"}, {"id": 469, "stackSize": 64, "displayName": "Orange Glazed Terracotta", "name": "orange_glazed_terracotta"}, {"id": 470, "stackSize": 64, "displayName": "Magenta Glazed Terracotta", "name": "magenta_glazed_terracotta"}, {"id": 471, "stackSize": 64, "displayName": "Light Blue Glazed Terracotta", "name": "light_blue_glazed_terracotta"}, {"id": 472, "stackSize": 64, "displayName": "Yellow Glazed Terracotta", "name": "yellow_glazed_terracotta"}, {"id": 473, "stackSize": 64, "displayName": "Lime Glazed Terracotta", "name": "lime_glazed_terracotta"}, {"id": 473, "displayName": "Smooth Stone Slab", "name": "stone_slab", "stackSize": 64}, {"id": 474, "stackSize": 64, "displayName": "Pink Glazed Terracotta", "name": "pink_glazed_terracotta"}, {"id": 475, "stackSize": 64, "displayName": "Gray Glazed Terracotta", "name": "gray_glazed_terracotta"}, {"id": 476, "stackSize": 64, "displayName": "Light Gray Glazed Terracotta", "name": "silver_glazed_terracotta"}, {"id": 477, "stackSize": 64, "displayName": "<PERSON><PERSON>zed Terracotta", "name": "cyan_glazed_terracotta"}, {"id": 478, "stackSize": 64, "displayName": "Purple Glazed Terracotta", "name": "purple_glazed_terracotta"}, {"id": 479, "stackSize": 64, "displayName": "Blue Glazed Terracotta", "name": "blue_glazed_terracotta"}, {"id": 480, "stackSize": 64, "displayName": "Brown Glazed Terracotta", "name": "brown_glazed_terracotta"}, {"id": 481, "stackSize": 64, "displayName": "Green Glazed Terracotta", "name": "green_glazed_terracotta"}, {"id": 482, "stackSize": 64, "displayName": "Red Glazed Terracotta", "name": "red_glazed_terracotta"}, {"id": 483, "stackSize": 64, "displayName": "Black Glazed Terracotta", "name": "black_glazed_terracotta"}, {"id": 484, "displayName": "White Concrete", "name": "concrete", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 485, "displayName": "Orange Concrete", "name": "orange_concrete", "stackSize": 64}, {"metadata": 2, "id": 486, "displayName": "Magenta Concrete", "name": "magenta_concrete", "stackSize": 64}, {"metadata": 3, "id": 487, "displayName": "Light Blue Concrete", "name": "light_blue_concrete", "stackSize": 64}, {"metadata": 4, "id": 488, "displayName": "Yellow Concrete", "name": "yellow_concrete", "stackSize": 64}, {"metadata": 5, "id": 489, "displayName": "Lime Concrete", "name": "lime_concrete", "stackSize": 64}, {"metadata": 6, "id": 490, "displayName": "Pink Concrete", "name": "pink_concrete", "stackSize": 64}, {"metadata": 7, "id": 491, "displayName": "<PERSON>", "name": "gray_concrete", "stackSize": 64}, {"metadata": 8, "id": 492, "displayName": "Light Gray Concrete", "name": "light_gray_concrete", "stackSize": 64}, {"metadata": 9, "id": 493, "displayName": "<PERSON><PERSON>", "name": "cyan_concrete", "stackSize": 64}, {"metadata": 10, "id": 494, "displayName": "Purple Concrete", "name": "purple_concrete", "stackSize": 64}, {"metadata": 11, "id": 495, "displayName": "Blue Concrete", "name": "blue_concrete", "stackSize": 64}, {"metadata": 12, "id": 496, "displayName": "<PERSON> Concrete", "name": "brown_concrete", "stackSize": 64}, {"metadata": 13, "id": 497, "displayName": "Green Concrete", "name": "green_concrete", "stackSize": 64}, {"metadata": 14, "id": 498, "displayName": "Red Concrete", "name": "red_concrete", "stackSize": 64}, {"metadata": 15, "id": 499, "displayName": "Black Concrete", "name": "black_concrete", "stackSize": 64}]}, {"id": 500, "displayName": "White Concrete Powder", "name": "concrete_powder", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 501, "displayName": "Orange Concrete Powder", "name": "orange_concrete_powder", "stackSize": 64}, {"metadata": 2, "id": 502, "displayName": "Magenta Concrete Powder", "name": "magenta_concrete_powder", "stackSize": 64}, {"metadata": 3, "id": 503, "displayName": "Light Blue Concrete Powder", "name": "light_blue_concrete_powder", "stackSize": 64}, {"metadata": 4, "id": 504, "displayName": "Yellow Concrete Powder", "name": "yellow_concrete_powder", "stackSize": 64}, {"metadata": 5, "id": 505, "displayName": "Lime Concrete <PERSON>", "name": "lime_concrete_powder", "stackSize": 64}, {"metadata": 6, "id": 506, "displayName": "Pink Concrete Powder", "name": "pink_concrete_powder", "stackSize": 64}, {"metadata": 7, "id": 507, "displayName": "<PERSON> Concre<PERSON>", "name": "gray_concrete_powder", "stackSize": 64}, {"metadata": 8, "id": 508, "displayName": "Light Gray Concrete Powder", "name": "light_gray_concrete_powder", "stackSize": 64}, {"metadata": 9, "id": 509, "displayName": "<PERSON><PERSON>", "name": "cyan_concrete_powder", "stackSize": 64}, {"metadata": 10, "id": 510, "displayName": "Purple Concrete Powder", "name": "purple_concrete_powder", "stackSize": 64}, {"metadata": 11, "id": 511, "displayName": "Blue Concrete Powder", "name": "blue_concrete_powder", "stackSize": 64}, {"metadata": 12, "id": 512, "displayName": "<PERSON> Concrete <PERSON>", "name": "brown_concrete_powder", "stackSize": 64}, {"metadata": 13, "id": 513, "displayName": "Green Concrete Powder", "name": "green_concrete_powder", "stackSize": 64}, {"metadata": 14, "id": 514, "displayName": "Red Concrete Powder", "name": "red_concrete_powder", "stackSize": 64}, {"metadata": 15, "id": 515, "displayName": "Black Concrete Powder", "name": "black_concrete_powder", "stackSize": 64}]}, {"id": 516, "stackSize": 64, "displayName": "Turtle Egg", "name": "turtle_egg"}, {"id": 522, "displayName": "Tube Coral Block", "name": "coral_block", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 523, "displayName": "Brain <PERSON>", "name": "brain_coral_block", "stackSize": 64}, {"metadata": 2, "id": 524, "displayName": "Bubble Coral Block", "name": "bubble_coral_block", "stackSize": 64}, {"metadata": 3, "id": 525, "displayName": "Fire Coral Block", "name": "fire_coral_block", "stackSize": 64}, {"metadata": 4, "id": 526, "displayName": "Horn Coral Block", "name": "horn_coral_block", "stackSize": 64}, {"metadata": 8, "id": 517, "displayName": "Dead Tube Coral Block", "name": "dead_tube_coral_block", "stackSize": 64}, {"metadata": 9, "id": 518, "displayName": "Dead Brain Coral Block", "name": "dead_brain_coral_block", "stackSize": 64}, {"metadata": 10, "id": 519, "displayName": "Dead Bubble Coral Block", "name": "dead_bubble_coral_block", "stackSize": 64}, {"metadata": 11, "id": 520, "displayName": "Dead Fire Coral Block", "name": "dead_fire_coral_block", "stackSize": 64}, {"metadata": 12, "id": 521, "displayName": "Dead Horn Coral Block", "name": "dead_horn_coral_block", "stackSize": 64}]}, {"id": 527, "displayName": "Tube Coral", "name": "coral", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 528, "displayName": "Brain Coral", "name": "brain_coral", "stackSize": 64}, {"metadata": 2, "id": 529, "displayName": "Bubble Coral", "name": "bubble_coral", "stackSize": 64}, {"metadata": 3, "id": 530, "displayName": "Fire Coral", "name": "fire_coral", "stackSize": 64}, {"metadata": 4, "id": 531, "displayName": "Horn Coral", "name": "horn_coral", "stackSize": 64}, {"metadata": 8, "id": 536, "displayName": "Dead Tube Coral", "name": "dead_tube_coral", "stackSize": 64}, {"metadata": 9, "id": 532, "displayName": "Dead Brain Coral", "name": "dead_brain_coral", "stackSize": 64}, {"metadata": 10, "id": 533, "displayName": "Dead Bubble Coral", "name": "dead_bubble_coral", "stackSize": 64}, {"metadata": 11, "id": 534, "displayName": "Dead Fire Coral", "name": "dead_fire_coral", "stackSize": 64}, {"metadata": 12, "id": 535, "displayName": "Dead Horn Coral", "name": "dead_horn_coral", "stackSize": 64}]}, {"id": 537, "displayName": "Tube Coral Fan", "name": "coral_fan", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 538, "displayName": "Brain Coral Fan", "name": "brain_coral_fan", "stackSize": 64}, {"metadata": 2, "id": 539, "displayName": "Bubble Coral Fan", "name": "bubble_coral_fan", "stackSize": 64}, {"metadata": 3, "id": 540, "displayName": "Fire Coral Fan", "name": "fire_coral_fan", "stackSize": 64}, {"metadata": 4, "id": 541, "displayName": "Horn Coral Fan", "name": "horn_coral_fan", "stackSize": 64}]}, {"id": 542, "displayName": "Dead Tube Coral Fan", "name": "coral_fan_dead", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 543, "displayName": "Dead Brain Coral Fan", "name": "dead_brain_coral_fan", "stackSize": 64}, {"metadata": 2, "id": 544, "displayName": "Dead Bubble Coral Fan", "name": "dead_bubble_coral_fan", "stackSize": 64}, {"metadata": 3, "id": 545, "displayName": "Dead Fire Coral Fan", "name": "dead_fire_coral_fan", "stackSize": 64}, {"metadata": 4, "id": 546, "displayName": "Dead Horn Coral Fan", "name": "dead_horn_coral_fan", "stackSize": 64}]}, {"id": 547, "stackSize": 64, "displayName": "Blue Ice", "name": "blue_ice"}, {"id": 548, "stackSize": 64, "displayName": "Conduit", "name": "conduit"}, {"id": 549, "stackSize": 64, "displayName": "Polished Granite Stairs", "name": "polished_granite_stairs"}, {"id": 550, "stackSize": 64, "displayName": "Smooth Red Sandstone Stairs", "name": "smooth_red_sandstone_stairs"}, {"id": 551, "stackSize": 64, "displayName": "Mossy Stone Brick Stairs", "name": "mossy_stone_brick_stairs"}, {"id": 552, "stackSize": 64, "displayName": "Polished Diorite Stairs", "name": "polished_diorite_stairs"}, {"id": 553, "stackSize": 64, "displayName": "Mossy Cobblestone Stairs", "name": "mossy_cobblestone_stairs"}, {"id": 554, "stackSize": 64, "displayName": "End Stone Brick Stairs", "name": "end_brick_stairs"}, {"id": 555, "stackSize": 64, "displayName": "Stone Stairs", "name": "normal_stone_stairs"}, {"id": 556, "stackSize": 64, "displayName": "Smooth Sandstone Stairs", "name": "smooth_sandstone_stairs"}, {"id": 557, "stackSize": 64, "displayName": "Smooth Quartz Stairs", "name": "smooth_quartz_stairs"}, {"id": 558, "stackSize": 64, "displayName": "Granite Stairs", "name": "granite_stairs"}, {"id": 559, "stackSize": 64, "displayName": "Andesite Stairs", "name": "andesite_stairs"}, {"id": 560, "stackSize": 64, "displayName": "Red Nether Brick Stairs", "name": "red_nether_brick_stairs"}, {"id": 561, "stackSize": 64, "displayName": "Polished Andesite Stairs", "name": "polished_andesite_stairs"}, {"id": 562, "stackSize": 64, "displayName": "Diorite Stairs", "name": "diorite_stairs"}, {"id": 563, "stackSize": 64, "displayName": "Cobbled Deepslate Stairs", "name": "cobbled_deepslate_stairs"}, {"id": 564, "stackSize": 64, "displayName": "Polished Deepslate Stairs", "name": "polished_deepslate_stairs"}, {"id": 565, "stackSize": 64, "displayName": "Deepslate Brick Stairs", "name": "deepslate_brick_stairs"}, {"id": 566, "stackSize": 64, "displayName": "Deepslate Tile Stairs", "name": "deepslate_tile_stairs"}, {"id": 569, "displayName": "Mossy Stone Brick Slab", "name": "double_stone_slab4", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 574, "displayName": "Smooth Quartz Slab", "name": "smooth_quartz_slab", "stackSize": 64}, {"metadata": 2, "id": 212, "displayName": "<PERSON> Slab", "name": "stone_slab", "stackSize": 64}, {"metadata": 3, "id": 215, "displayName": "Cut Sandstone Slab", "name": "cut_sandstone_slab", "stackSize": 64}, {"metadata": 4, "id": 223, "displayName": "Cut Red Sandstone Slab", "name": "cut_red_sandstone_slab", "stackSize": 64}]}, {"id": 572, "displayName": "End Stone Brick Slab", "name": "double_stone_slab3", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 568, "displayName": "Smooth Red Sandstone Slab", "name": "smooth_red_sandstone_slab", "stackSize": 64}, {"metadata": 2, "id": 578, "displayName": "Polished Andesite Slab", "name": "polished_andesite_slab", "stackSize": 64}, {"metadata": 3, "id": 576, "displayName": "Andesite Slab", "name": "andesite_slab", "stackSize": 64}, {"metadata": 4, "id": 579, "displayName": "Diorite Slab", "name": "diorite_slab", "stackSize": 64}, {"metadata": 5, "id": 570, "displayName": "Polished Diorite S<PERSON>b", "name": "polished_diorite_slab", "stackSize": 64}, {"metadata": 6, "id": 575, "displayName": "Granite Slab", "name": "granite_slab", "stackSize": 64}, {"metadata": 7, "id": 567, "displayName": "Polished Granite Slab", "name": "polished_granite_slab", "stackSize": 64}]}, {"id": 580, "stackSize": 64, "displayName": "Cobbled Deepslate Slab", "name": "cobbled_deepslate_slab"}, {"id": 581, "stackSize": 64, "displayName": "Polished Deepslate Slab", "name": "polished_deepslate_slab"}, {"id": 582, "stackSize": 64, "displayName": "Deepslate Brick Slab", "name": "deepslate_brick_slab"}, {"id": 583, "stackSize": 64, "displayName": "Deepslate Tile Slab", "name": "deepslate_tile_slab"}, {"id": 584, "stackSize": 64, "displayName": "Scaffolding", "name": "scaffolding"}, {"id": 585, "stackSize": 64, "displayName": "Redstone Dust", "name": "redstone"}, {"id": 586, "stackSize": 64, "displayName": "Redstone Torch", "name": "redstone_torch"}, {"id": 587, "stackSize": 64, "displayName": "Block of Redstone", "name": "redstone_block"}, {"id": 588, "stackSize": 64, "displayName": "Redstone Repeater", "name": "repeater"}, {"id": 589, "stackSize": 64, "displayName": "Redstone Comparator", "name": "comparator"}, {"id": 590, "stackSize": 64, "displayName": "<PERSON><PERSON>", "name": "piston"}, {"id": 591, "stackSize": 64, "displayName": "<PERSON><PERSON>", "name": "sticky_piston"}, {"id": 592, "stackSize": 64, "displayName": "Slime Block", "name": "slime"}, {"id": 593, "stackSize": 64, "displayName": "Honey Block", "name": "honey_block"}, {"id": 594, "stackSize": 64, "displayName": "Observer", "name": "observer"}, {"id": 595, "stackSize": 64, "displayName": "<PERSON>", "name": "hopper"}, {"id": 596, "stackSize": 64, "displayName": "Dispenser", "name": "dispenser"}, {"id": 597, "stackSize": 64, "displayName": "Dropper", "name": "dropper"}, {"id": 598, "stackSize": 64, "displayName": "Lectern", "name": "lectern"}, {"id": 599, "stackSize": 64, "displayName": "Target", "name": "target"}, {"id": 600, "stackSize": 64, "displayName": "Lever", "name": "lever"}, {"id": 601, "stackSize": 64, "displayName": "Lightning Rod", "name": "lightning_rod"}, {"id": 602, "stackSize": 64, "displayName": "Daylight Detector", "name": "daylight_detector"}, {"id": 603, "stackSize": 64, "displayName": "Sculk Sensor", "name": "sculk_sensor"}, {"id": 604, "stackSize": 64, "displayName": "Tripwire Hook", "name": "tripwire_hook"}, {"id": 605, "stackSize": 64, "displayName": "Trapped Chest", "name": "trapped_chest"}, {"id": 606, "stackSize": 64, "displayName": "TNT", "name": "tnt"}, {"id": 607, "stackSize": 64, "displayName": "Redstone Lamp", "name": "redstone_lamp"}, {"id": 608, "stackSize": 64, "displayName": "Note Block", "name": "noteblock"}, {"id": 609, "stackSize": 64, "displayName": "<PERSON>", "name": "stone_button"}, {"id": 610, "stackSize": 64, "displayName": "Polished Blackstone Button", "name": "polished_blackstone_button"}, {"id": 611, "stackSize": 64, "displayName": "Oak Button", "name": "wooden_button"}, {"id": 612, "stackSize": 64, "displayName": "Spruce Button", "name": "spruce_button"}, {"id": 613, "stackSize": 64, "displayName": "<PERSON>", "name": "birch_button"}, {"id": 614, "stackSize": 64, "displayName": "<PERSON>ton", "name": "jungle_button"}, {"id": 615, "stackSize": 64, "displayName": "Acacia <PERSON>", "name": "acacia_button"}, {"id": 616, "stackSize": 64, "displayName": "Dark Oak Button", "name": "dark_oak_button"}, {"id": 617, "stackSize": 64, "displayName": "<PERSON>", "name": "crimson_button"}, {"id": 618, "stackSize": 64, "displayName": "Warped <PERSON>", "name": "warped_button"}, {"id": 619, "stackSize": 64, "displayName": "Stone Pressure Plate", "name": "stone_pressure_plate"}, {"id": 620, "stackSize": 64, "displayName": "Polished Blackstone Pressure Plate", "name": "polished_blackstone_pressure_plate"}, {"id": 621, "stackSize": 64, "displayName": "Light Weighted Pressure Plate", "name": "light_weighted_pressure_plate"}, {"id": 622, "stackSize": 64, "displayName": "Heavy Weighted Pressure Plate", "name": "heavy_weighted_pressure_plate"}, {"id": 623, "stackSize": 64, "displayName": "Oak Pressure Plate", "name": "wooden_pressure_plate"}, {"id": 624, "stackSize": 64, "displayName": "Spruce Pressure Plate", "name": "spruce_pressure_plate"}, {"id": 625, "stackSize": 64, "displayName": "Birch Pressure Plate", "name": "birch_pressure_plate"}, {"id": 626, "stackSize": 64, "displayName": "Jungle Pressure Plate", "name": "jungle_pressure_plate"}, {"id": 627, "stackSize": 64, "displayName": "Acacia Pressure Plate", "name": "acacia_pressure_plate"}, {"id": 628, "stackSize": 64, "displayName": "Dark Oak Pressure Plate", "name": "dark_oak_pressure_plate"}, {"id": 629, "stackSize": 64, "displayName": "Crimson Pressure Plate", "name": "crimson_pressure_plate"}, {"id": 630, "stackSize": 64, "displayName": "Warped Pressure Plate", "name": "warped_pressure_plate"}, {"id": 631, "stackSize": 64, "displayName": "Iron Door", "name": "iron_door"}, {"id": 632, "stackSize": 64, "displayName": "Oak Door", "name": "wooden_door"}, {"id": 633, "stackSize": 64, "displayName": "Spruce Door", "name": "spruce_door"}, {"id": 634, "stackSize": 64, "displayName": "<PERSON>", "name": "birch_door"}, {"id": 635, "stackSize": 64, "displayName": "Jungle Door", "name": "jungle_door"}, {"id": 636, "stackSize": 64, "displayName": "Acacia Door", "name": "acacia_door"}, {"id": 637, "stackSize": 64, "displayName": "Dark Oak Door", "name": "dark_oak_door"}, {"id": 638, "stackSize": 64, "displayName": "Crimson Door", "name": "crimson_door"}, {"id": 639, "stackSize": 64, "displayName": "Warped Door", "name": "warped_door"}, {"id": 640, "stackSize": 64, "displayName": "Iron Trapdoor", "name": "iron_trapdoor"}, {"id": 641, "stackSize": 64, "displayName": "Oak Trapdoor", "name": "trapdoor"}, {"id": 642, "stackSize": 64, "displayName": "Spruce Trapdoor", "name": "spruce_trapdoor"}, {"id": 643, "stackSize": 64, "displayName": "<PERSON>", "name": "birch_trapdoor"}, {"id": 644, "stackSize": 64, "displayName": "Jungle Trapdoor", "name": "jungle_trapdoor"}, {"id": 645, "stackSize": 64, "displayName": "Acacia T<PERSON>door", "name": "acacia_trapdoor"}, {"id": 646, "stackSize": 64, "displayName": "Dark Oak Trapdoor", "name": "dark_oak_trapdoor"}, {"id": 647, "stackSize": 64, "displayName": "Crimson Trapdoor", "name": "crimson_trapdoor"}, {"id": 648, "stackSize": 64, "displayName": "Warped Trapdoor", "name": "warped_trapdoor"}, {"id": 649, "stackSize": 64, "displayName": "Oak Fence Gate", "name": "fence_gate"}, {"id": 650, "stackSize": 64, "displayName": "Spruce Fence Gate", "name": "spruce_fence_gate"}, {"id": 651, "stackSize": 64, "displayName": "Birch Fence Gate", "name": "birch_fence_gate"}, {"id": 652, "stackSize": 64, "displayName": "Jungle Fence Gate", "name": "jungle_fence_gate"}, {"id": 653, "stackSize": 64, "displayName": "Acacia Fence Gate", "name": "acacia_fence_gate"}, {"id": 654, "stackSize": 64, "displayName": "Dark Oak Fence Gate", "name": "dark_oak_fence_gate"}, {"id": 655, "stackSize": 64, "displayName": "Crimson Fence Gate", "name": "crimson_fence_gate"}, {"id": 656, "stackSize": 64, "displayName": "Warped Fence Gate", "name": "warped_fence_gate"}, {"id": 657, "stackSize": 64, "displayName": "Powered Rail", "name": "golden_rail"}, {"id": 658, "stackSize": 64, "displayName": "Detector Rail", "name": "detector_rail"}, {"id": 659, "stackSize": 64, "displayName": "Rail", "name": "rail"}, {"id": 660, "stackSize": 64, "displayName": "Activator Rail", "name": "activator_rail"}, {"id": 661, "stackSize": 1, "displayName": "Saddle", "name": "saddle"}, {"id": 662, "stackSize": 1, "displayName": "Minecart", "name": "minecart"}, {"id": 663, "stackSize": 1, "displayName": "Minecart with Chest", "name": "chest_minecart"}, {"id": 664, "displayName": "Minecart with Furnace", "name": "hopper_minecart", "stackSize": 1, "metadata": 0, "variations": [{"metadata": 0, "id": 666, "displayName": "Minecart with <PERSON>", "name": "hopper_minecart", "stackSize": 1}]}, {"id": 665, "stackSize": 1, "displayName": "Minecart with TNT", "name": "tnt_minecart"}, {"id": 667, "stackSize": 1, "displayName": "Carrot on a Stick", "name": "carrot_on_a_stick", "maxDurability": 25, "enchantCategories": ["breakable", "vanishable"]}, {"id": 668, "stackSize": 64, "displayName": "Warped Fungus on a Stick", "name": "warped_fungus_on_a_stick", "maxDurability": 100, "enchantCategories": ["breakable", "vanishable"]}, {"id": 669, "stackSize": 1, "displayName": "Elytra", "name": "elytra", "maxDurability": 432, "enchantCategories": ["breakable", "wearable", "vanishable"], "repairWith": ["phantom_membrane"]}, {"id": 670, "stackSize": 1, "displayName": "Oak Boat", "name": "oak_boat"}, {"id": 671, "stackSize": 1, "displayName": "Spruce Boat", "name": "spruce_boat"}, {"id": 672, "stackSize": 1, "displayName": "<PERSON> Boat", "name": "birch_boat"}, {"id": 673, "stackSize": 1, "displayName": "Jungle Boat", "name": "jungle_boat"}, {"id": 674, "stackSize": 1, "displayName": "Acacia Boat", "name": "acacia_boat"}, {"id": 675, "stackSize": 1, "displayName": "Dark Oak Boat", "name": "dark_oak_boat"}, {"id": 676, "stackSize": 64, "displayName": "Structure Block", "name": "structure_block"}, {"id": 677, "stackSize": 64, "displayName": "Jigsaw Block", "name": "jigsaw"}, {"id": 678, "stackSize": 1, "displayName": "Turtle Shell", "name": "turtle_helmet", "maxDurability": 275, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["scute"]}, {"id": 679, "stackSize": 64, "displayName": "<PERSON><PERSON>", "name": "scute"}, {"id": 680, "stackSize": 1, "displayName": "Flint and Steel", "name": "flint_and_steel", "maxDurability": 64, "enchantCategories": ["breakable", "vanishable"]}, {"id": 681, "stackSize": 64, "displayName": "Apple", "name": "apple"}, {"id": 682, "stackSize": 1, "displayName": "Bow", "name": "bow", "maxDurability": 384, "enchantCategories": ["breakable", "bow", "vanishable"]}, {"id": 683, "displayName": "Arrow", "name": "arrow", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 0, "id": 1006, "displayName": "Spectral Arrow", "name": "spectral_arrow", "stackSize": 64}, {"metadata": 0, "id": 1007, "displayName": "Tipped Arrow", "name": "tipped_arrow", "stackSize": 64}]}, {"id": 684, "stackSize": 64, "displayName": "Coal", "name": "coal"}, {"id": 685, "stackSize": 64, "displayName": "Charc<PERSON>l", "name": "charcoal"}, {"id": 686, "stackSize": 64, "displayName": "Diamond", "name": "diamond"}, {"id": 687, "stackSize": 64, "displayName": "Emerald", "name": "emerald"}, {"id": 688, "stackSize": 64, "displayName": "<PERSON><PERSON>", "name": "lapis_lazuli"}, {"id": 689, "stackSize": 64, "displayName": "<PERSON><PERSON>", "name": "quartz"}, {"id": 690, "stackSize": 64, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "amethyst_shard"}, {"id": 691, "stackSize": 64, "displayName": "Raw Iron", "name": "raw_iron"}, {"id": 692, "stackSize": 64, "displayName": "Iron Ingot", "name": "iron_ingot"}, {"id": 693, "stackSize": 64, "displayName": "Raw Copper", "name": "raw_copper"}, {"id": 694, "stackSize": 64, "displayName": "Copper Ingot", "name": "copper_ingot"}, {"id": 695, "stackSize": 64, "displayName": "Raw Gold", "name": "raw_gold"}, {"id": 696, "stackSize": 64, "displayName": "Gold Ingot", "name": "gold_ingot"}, {"id": 697, "stackSize": 64, "displayName": "Netherite Ingot", "name": "netherite_ingot"}, {"id": 698, "stackSize": 64, "displayName": "Netherite Scrap", "name": "netherite_scrap"}, {"id": 699, "stackSize": 1, "displayName": "Wooden Sword", "name": "wooden_sword", "maxDurability": 59, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"]}, {"id": 700, "stackSize": 1, "displayName": "<PERSON><PERSON>", "name": "wooden_shovel", "maxDurability": 59, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"]}, {"id": 701, "stackSize": 1, "displayName": "<PERSON><PERSON> Pick<PERSON>e", "name": "wooden_pickaxe", "maxDurability": 59, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"]}, {"id": 702, "stackSize": 1, "displayName": "Wooden Axe", "name": "wooden_axe", "maxDurability": 59, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"]}, {"id": 703, "stackSize": 1, "displayName": "<PERSON><PERSON>e", "name": "wooden_hoe", "maxDurability": 59, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"]}, {"id": 704, "stackSize": 1, "displayName": "Stone Sword", "name": "stone_sword", "maxDurability": 131, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["cobblestone", "blackstone"]}, {"id": 705, "stackSize": 1, "displayName": "<PERSON>el", "name": "stone_shovel", "maxDurability": 131, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["cobblestone", "blackstone"]}, {"id": 706, "stackSize": 1, "displayName": "<PERSON>", "name": "stone_pickaxe", "maxDurability": 131, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["cobblestone", "blackstone"]}, {"id": 707, "stackSize": 1, "displayName": "Stone Axe", "name": "stone_axe", "maxDurability": 131, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["cobblestone", "blackstone"]}, {"id": 708, "stackSize": 1, "displayName": "Stone Hoe", "name": "stone_hoe", "maxDurability": 131, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["cobblestone", "blackstone"]}, {"id": 709, "stackSize": 1, "displayName": "Golden Sword", "name": "golden_sword", "maxDurability": 32, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 710, "stackSize": 1, "displayName": "Golden Shovel", "name": "golden_shovel", "maxDurability": 32, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 711, "stackSize": 1, "displayName": "Golden Pickaxe", "name": "golden_pickaxe", "maxDurability": 32, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 712, "stackSize": 1, "displayName": "Golden Axe", "name": "golden_axe", "maxDurability": 32, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 713, "stackSize": 1, "displayName": "Golden Hoe", "name": "golden_hoe", "maxDurability": 32, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 714, "stackSize": 1, "displayName": "Iron Sword", "name": "iron_sword", "maxDurability": 250, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 715, "stackSize": 1, "displayName": "Iron Shovel", "name": "iron_shovel", "maxDurability": 250, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 716, "stackSize": 1, "displayName": "Iron Pickaxe", "name": "iron_pickaxe", "maxDurability": 250, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 717, "stackSize": 1, "displayName": "Iron Axe", "name": "iron_axe", "maxDurability": 250, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 718, "stackSize": 1, "displayName": "Iron Hoe", "name": "iron_hoe", "maxDurability": 250, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 719, "stackSize": 1, "displayName": "Diamond Sword", "name": "diamond_sword", "maxDurability": 1561, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["diamond"]}, {"id": 720, "stackSize": 1, "displayName": "Diamond Shovel", "name": "diamond_shovel", "maxDurability": 1561, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["diamond"]}, {"id": 721, "stackSize": 1, "displayName": "Diamond Pickaxe", "name": "diamond_pickaxe", "maxDurability": 1561, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["diamond"]}, {"id": 722, "stackSize": 1, "displayName": "Diamond Axe", "name": "diamond_axe", "maxDurability": 1561, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["diamond"]}, {"id": 723, "stackSize": 1, "displayName": "Diamond Hoe", "name": "diamond_hoe", "maxDurability": 1561, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["diamond"]}, {"id": 724, "stackSize": 1, "displayName": "Netherite Sword", "name": "netherite_sword", "maxDurability": 2031, "enchantCategories": ["weapon", "breakable", "vanishable"], "repairWith": ["netherite_ingot"]}, {"id": 725, "stackSize": 1, "displayName": "<PERSON><PERSON><PERSON>", "name": "netherite_shovel", "maxDurability": 2031, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["netherite_ingot"]}, {"id": 726, "stackSize": 1, "displayName": "Netherite Pickaxe", "name": "netherite_pickaxe", "maxDurability": 2031, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["netherite_ingot"]}, {"id": 727, "stackSize": 1, "displayName": "Netherite Axe", "name": "netherite_axe", "maxDurability": 2031, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["netherite_ingot"]}, {"id": 728, "stackSize": 1, "displayName": "Netherite Hoe", "name": "netherite_hoe", "maxDurability": 2031, "enchantCategories": ["digger", "breakable", "vanishable"], "repairWith": ["netherite_ingot"]}, {"id": 729, "displayName": "Stick", "name": "stick", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 0, "id": 1014, "displayName": "Debug Stick", "name": "debug_stick", "stackSize": 1}]}, {"id": 730, "stackSize": 64, "displayName": "Bowl", "name": "bowl"}, {"id": 731, "stackSize": 1, "displayName": "Mushroom Stew", "name": "mushroom_stew"}, {"id": 732, "stackSize": 64, "displayName": "String", "name": "string"}, {"id": 733, "stackSize": 64, "displayName": "<PERSON><PERSON>", "name": "feather"}, {"id": 734, "stackSize": 64, "displayName": "Gunpowder", "name": "gunpowder"}, {"id": 735, "stackSize": 64, "displayName": "Wheat Seeds", "name": "wheat_seeds"}, {"id": 736, "stackSize": 64, "displayName": "Wheat", "name": "wheat"}, {"id": 737, "stackSize": 64, "displayName": "Bread", "name": "bread"}, {"id": 738, "stackSize": 1, "displayName": "Leather Cap", "name": "leather_helmet", "maxDurability": 55, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["leather"]}, {"id": 739, "stackSize": 1, "displayName": "<PERSON><PERSON>", "name": "leather_chestplate", "maxDurability": 80, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["leather"]}, {"id": 740, "stackSize": 1, "displayName": "<PERSON><PERSON>", "name": "leather_leggings", "maxDurability": 75, "enchantCategories": ["armor", "breakable", "wearable", "vanishable"], "repairWith": ["leather"]}, {"id": 741, "stackSize": 1, "displayName": "<PERSON><PERSON>", "name": "leather_boots", "maxDurability": 65, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["leather"]}, {"id": 742, "stackSize": 1, "displayName": "Chainmail Helmet", "name": "chainmail_helmet", "maxDurability": 165, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 743, "stackSize": 1, "displayName": "Chainmail Chestplate", "name": "chainmail_chestplate", "maxDurability": 240, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 744, "stackSize": 1, "displayName": "Chainmail Leggings", "name": "chainmail_leggings", "maxDurability": 225, "enchantCategories": ["armor", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 745, "stackSize": 1, "displayName": "Chainmail Boots", "name": "chainmail_boots", "maxDurability": 195, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 746, "stackSize": 1, "displayName": "Iron Helmet", "name": "iron_helmet", "maxDurability": 165, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 747, "stackSize": 1, "displayName": "Iron Chestplate", "name": "iron_chestplate", "maxDurability": 240, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 748, "stackSize": 1, "displayName": "Iron Leggings", "name": "iron_leggings", "maxDurability": 225, "enchantCategories": ["armor", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 749, "stackSize": 1, "displayName": "Iron Boots", "name": "iron_boots", "maxDurability": 195, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["iron_ingot"]}, {"id": 750, "stackSize": 1, "displayName": "Diamond Helmet", "name": "diamond_helmet", "maxDurability": 363, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["diamond"]}, {"id": 751, "stackSize": 1, "displayName": "Diamond Chestplate", "name": "diamond_chestplate", "maxDurability": 528, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["diamond"]}, {"id": 752, "stackSize": 1, "displayName": "Diamond Leggings", "name": "diamond_leggings", "maxDurability": 495, "enchantCategories": ["armor", "breakable", "wearable", "vanishable"], "repairWith": ["diamond"]}, {"id": 753, "stackSize": 1, "displayName": "Diamond Boots", "name": "diamond_boots", "maxDurability": 429, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["diamond"]}, {"id": 754, "stackSize": 1, "displayName": "Golden Helmet", "name": "golden_helmet", "maxDurability": 77, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 755, "stackSize": 1, "displayName": "Golden Chestplate", "name": "golden_chestplate", "maxDurability": 112, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 756, "stackSize": 1, "displayName": "Golden Leggings", "name": "golden_leggings", "maxDurability": 105, "enchantCategories": ["armor", "breakable", "wearable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 757, "stackSize": 1, "displayName": "Golden Boots", "name": "golden_boots", "maxDurability": 91, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["gold_ingot"]}, {"id": 758, "stackSize": 1, "displayName": "Netherite Helmet", "name": "netherite_helmet", "maxDurability": 407, "enchantCategories": ["armor", "armor_head", "breakable", "wearable", "vanishable"], "repairWith": ["netherite_ingot"]}, {"id": 759, "stackSize": 1, "displayName": "Netherite Chestplate", "name": "netherite_chestplate", "maxDurability": 592, "enchantCategories": ["armor", "armor_chest", "breakable", "wearable", "vanishable"], "repairWith": ["netherite_ingot"]}, {"id": 760, "stackSize": 1, "displayName": "Netherite Leggings", "name": "netherite_leggings", "maxDurability": 555, "enchantCategories": ["armor", "breakable", "wearable", "vanishable"], "repairWith": ["netherite_ingot"]}, {"id": 761, "stackSize": 1, "displayName": "Netherite Boots", "name": "netherite_boots", "maxDurability": 481, "enchantCategories": ["armor", "armor_feet", "breakable", "wearable", "vanishable"], "repairWith": ["netherite_ingot"]}, {"id": 762, "stackSize": 64, "displayName": "Flint", "name": "flint"}, {"id": 763, "stackSize": 64, "displayName": "Raw Porkchop", "name": "porkchop"}, {"id": 764, "stackSize": 64, "displayName": "Cooked Porkchop", "name": "cooked_porkchop"}, {"id": 765, "stackSize": 64, "displayName": "Painting", "name": "painting"}, {"id": 766, "stackSize": 64, "displayName": "Golden Apple", "name": "golden_apple"}, {"id": 767, "stackSize": 64, "displayName": "Enchanted Golden Apple", "name": "enchanted_golden_apple"}, {"id": 768, "stackSize": 16, "displayName": "Oak Sign", "name": "oak_sign"}, {"id": 769, "stackSize": 16, "displayName": "Spruce Sign", "name": "spruce_sign"}, {"id": 770, "stackSize": 16, "displayName": "Birch Sign", "name": "birch_sign"}, {"id": 771, "stackSize": 16, "displayName": "Jungle Sign", "name": "jungle_sign"}, {"id": 772, "stackSize": 16, "displayName": "Acacia Sign", "name": "acacia_sign"}, {"id": 773, "stackSize": 16, "displayName": "Dark Oak Sign", "name": "dark_oak_sign"}, {"id": 774, "stackSize": 16, "displayName": "Crimson Sign", "name": "crimson_sign"}, {"id": 775, "stackSize": 16, "displayName": "Warped Sign", "name": "warped_sign"}, {"id": 776, "stackSize": 16, "displayName": "Bucket", "name": "bucket"}, {"id": 777, "stackSize": 1, "displayName": "Water Bucket", "name": "water_bucket"}, {"id": 778, "stackSize": 1, "displayName": "<PERSON><PERSON>et", "name": "lava_bucket"}, {"id": 779, "stackSize": 1, "displayName": "Powder Snow Bucket", "name": "powder_snow_bucket"}, {"id": 780, "stackSize": 16, "displayName": "Snowball", "name": "snowball"}, {"id": 781, "stackSize": 64, "displayName": "Leather", "name": "leather"}, {"id": 782, "stackSize": 1, "displayName": "Milk Bucket", "name": "milk_bucket"}, {"id": 783, "stackSize": 1, "displayName": "Bucket of Pufferfish", "name": "pufferfish_bucket"}, {"id": 784, "stackSize": 1, "displayName": "Bucket of Salmon", "name": "salmon_bucket"}, {"id": 785, "stackSize": 1, "displayName": "Bucket of Cod", "name": "cod_bucket"}, {"id": 786, "stackSize": 1, "displayName": "Bucket of Tropical Fish", "name": "tropical_fish_bucket"}, {"id": 787, "stackSize": 1, "displayName": "Bucket of Axolotl", "name": "axolotl_bucket"}, {"id": 788, "stackSize": 64, "displayName": "Brick", "name": "brick"}, {"id": 789, "stackSize": 64, "displayName": "<PERSON>", "name": "clay_ball"}, {"id": 790, "stackSize": 64, "displayName": "Dried Kelp Block", "name": "dried_kelp_block"}, {"id": 791, "stackSize": 64, "displayName": "Paper", "name": "paper"}, {"id": 792, "displayName": "Book", "name": "book", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 0, "id": 1013, "displayName": "Knowledge Book", "name": "knowledge_book", "stackSize": 1}]}, {"id": 793, "stackSize": 64, "displayName": "Slimeball", "name": "slime_ball"}, {"id": 794, "stackSize": 16, "displayName": "Egg", "name": "egg"}, {"id": 795, "stackSize": 64, "displayName": "<PERSON>mp<PERSON>", "name": "compass"}, {"id": 796, "displayName": "Bundle", "name": "shulker_shell", "stackSize": 1, "metadata": 0, "variations": [{"metadata": 0, "id": 1011, "displayName": "Shulker Shell", "name": "shulker_shell", "stackSize": 64}]}, {"id": 797, "stackSize": 1, "displayName": "Fishing Rod", "name": "fishing_rod", "maxDurability": 64, "enchantCategories": ["breakable", "fishing_rod", "vanishable"]}, {"id": 798, "stackSize": 64, "displayName": "Clock", "name": "clock"}, {"id": 799, "stackSize": 1, "displayName": "Spyglass", "name": "spyglass"}, {"id": 800, "stackSize": 64, "displayName": "Glowstone Dust", "name": "glowstone_dust"}, {"id": 801, "stackSize": 64, "displayName": "Raw Cod", "name": "cod"}, {"id": 802, "stackSize": 64, "displayName": "Raw Salmon", "name": "salmon"}, {"id": 803, "stackSize": 64, "displayName": "Tropical Fish", "name": "tropical_fish"}, {"id": 804, "stackSize": 64, "displayName": "Pufferfish", "name": "pufferfish"}, {"id": 805, "stackSize": 64, "displayName": "Cooked Cod", "name": "cooked_cod"}, {"id": 806, "stackSize": 64, "displayName": "Cooked Salmon", "name": "cooked_salmon"}, {"id": 807, "stackSize": 64, "displayName": "Ink Sac", "name": "ink_sac"}, {"id": 808, "stackSize": 64, "displayName": "Glow Ink Sac", "name": "glow_ink_sac"}, {"id": 809, "stackSize": 64, "displayName": "Cocoa Beans", "name": "cocoa_beans"}, {"id": 810, "stackSize": 64, "displayName": "White Dye", "name": "white_dye"}, {"id": 811, "stackSize": 64, "displayName": "Orange Dye", "name": "orange_dye"}, {"id": 812, "stackSize": 64, "displayName": "<PERSON><PERSON><PERSON>", "name": "magenta_dye"}, {"id": 813, "stackSize": 64, "displayName": "Light Blue Dye", "name": "light_blue_dye"}, {"id": 814, "stackSize": 64, "displayName": "Yellow Dye", "name": "yellow_dye"}, {"id": 815, "stackSize": 64, "displayName": "Lime Dye", "name": "lime_dye"}, {"id": 816, "stackSize": 64, "displayName": "Pink Dye", "name": "pink_dye"}, {"id": 817, "stackSize": 64, "displayName": "<PERSON>", "name": "gray_dye"}, {"id": 818, "stackSize": 64, "displayName": "Light Gray D<PERSON>", "name": "light_gray_dye"}, {"id": 819, "stackSize": 64, "displayName": "<PERSON><PERSON>", "name": "cyan_dye"}, {"id": 820, "stackSize": 64, "displayName": "Purple Dye", "name": "purple_dye"}, {"id": 821, "stackSize": 64, "displayName": "Blue Dye", "name": "blue_dye"}, {"id": 822, "stackSize": 64, "displayName": "<PERSON>", "name": "brown_dye"}, {"id": 823, "stackSize": 64, "displayName": "Green Dye", "name": "green_dye"}, {"id": 824, "stackSize": 64, "displayName": "Red Dye", "name": "red_dye"}, {"id": 825, "stackSize": 64, "displayName": "Black Dye", "name": "black_dye"}, {"id": 826, "stackSize": 64, "displayName": "<PERSON>", "name": "bone_meal"}, {"id": 827, "stackSize": 64, "displayName": "Bone", "name": "bone"}, {"id": 828, "stackSize": 64, "displayName": "Sugar", "name": "sugar"}, {"id": 829, "stackSize": 1, "displayName": "Cake", "name": "cake"}, {"id": 830, "displayName": "White Bed", "name": "bed", "stackSize": 1, "metadata": 0, "variations": [{"metadata": 1, "id": 831, "displayName": "Orange Bed", "name": "orange_bed", "stackSize": 1}, {"metadata": 2, "id": 832, "displayName": "Magenta Bed", "name": "magenta_bed", "stackSize": 1}, {"metadata": 3, "id": 833, "displayName": "Light Blue Bed", "name": "light_blue_bed", "stackSize": 1}, {"metadata": 4, "id": 834, "displayName": "Yellow Bed", "name": "yellow_bed", "stackSize": 1}, {"metadata": 5, "id": 835, "displayName": "Lime Bed", "name": "lime_bed", "stackSize": 1}, {"metadata": 6, "id": 836, "displayName": "Pink Bed", "name": "pink_bed", "stackSize": 1}, {"metadata": 7, "id": 837, "displayName": "Gray Bed", "name": "gray_bed", "stackSize": 1}, {"metadata": 8, "id": 838, "displayName": "Light Gray Bed", "name": "light_gray_bed", "stackSize": 1}, {"metadata": 9, "id": 839, "displayName": "<PERSON><PERSON>", "name": "cyan_bed", "stackSize": 1}, {"metadata": 10, "id": 840, "displayName": "Purple Bed", "name": "purple_bed", "stackSize": 1}, {"metadata": 11, "id": 841, "displayName": "Blue Bed", "name": "blue_bed", "stackSize": 1}, {"metadata": 12, "id": 842, "displayName": "Brown Bed", "name": "brown_bed", "stackSize": 1}, {"metadata": 13, "id": 843, "displayName": "Green Bed", "name": "green_bed", "stackSize": 1}, {"metadata": 14, "id": 844, "displayName": "Red Bed", "name": "red_bed", "stackSize": 1}, {"metadata": 15, "id": 845, "displayName": "Black Bed", "name": "black_bed", "stackSize": 1}]}, {"id": 846, "stackSize": 64, "displayName": "<PERSON><PERSON>", "name": "cookie"}, {"id": 847, "stackSize": 64, "displayName": "Map", "name": "filled_map"}, {"id": 848, "stackSize": 1, "displayName": "Shears", "name": "shears", "maxDurability": 238, "enchantCategories": ["breakable", "vanishable"]}, {"id": 849, "stackSize": 64, "displayName": "<PERSON><PERSON>", "name": "melon_slice"}, {"id": 850, "stackSize": 64, "displayName": "<PERSON><PERSON>", "name": "dried_kelp"}, {"id": 851, "stackSize": 64, "displayName": "<PERSON><PERSON><PERSON> Seeds", "name": "pumpkin_seeds"}, {"id": 852, "stackSize": 64, "displayName": "<PERSON>on Seeds", "name": "melon_seeds"}, {"id": 853, "stackSize": 64, "displayName": "Raw Beef", "name": "beef"}, {"id": 854, "stackSize": 64, "displayName": "Steak", "name": "cooked_beef"}, {"id": 855, "stackSize": 64, "displayName": "Raw Chicken", "name": "chicken"}, {"id": 856, "stackSize": 64, "displayName": "Cooked Chicken", "name": "cooked_chicken"}, {"id": 857, "stackSize": 64, "displayName": "Rotten Flesh", "name": "rotten_flesh"}, {"id": 858, "stackSize": 16, "displayName": "<PERSON><PERSON>", "name": "ender_pearl"}, {"id": 859, "stackSize": 64, "displayName": "<PERSON>", "name": "blaze_rod"}, {"id": 860, "stackSize": 64, "displayName": "Ghast Tear", "name": "ghast_tear"}, {"id": 861, "stackSize": 64, "displayName": "Gold Nugget", "name": "gold_nugget"}, {"id": 862, "stackSize": 64, "displayName": "Nether Wart", "name": "nether_wart"}, {"id": 863, "stackSize": 1, "displayName": "Potion", "name": "potion"}, {"id": 864, "stackSize": 64, "displayName": "Glass Bottle", "name": "glass_bottle"}, {"id": 865, "stackSize": 64, "displayName": "Spider Eye", "name": "spider_eye"}, {"id": 866, "stackSize": 64, "displayName": "Fermented Spider Eye", "name": "fermented_spider_eye"}, {"id": 867, "stackSize": 64, "displayName": "<PERSON>", "name": "blaze_powder"}, {"id": 868, "stackSize": 64, "displayName": "Magma Cream", "name": "magma_cream"}, {"id": 869, "stackSize": 64, "displayName": "Brewing Stand", "name": "brewing_stand"}, {"id": 870, "stackSize": 64, "displayName": "<PERSON><PERSON><PERSON>", "name": "cauldron"}, {"id": 871, "stackSize": 64, "displayName": "Eye of <PERSON>er", "name": "ender_eye"}, {"id": 872, "stackSize": 64, "displayName": "Glistering <PERSON><PERSON>", "name": "glistering_melon_slice"}, {"id": 873, "stackSize": 64, "displayName": "Axolotl Spawn Egg", "name": "axolotl_spawn_egg"}, {"id": 874, "stackSize": 64, "displayName": "Bat Spawn Egg", "name": "bat_spawn_egg"}, {"id": 875, "stackSize": 64, "displayName": "Bee Spawn Egg", "name": "bee_spawn_egg"}, {"id": 876, "stackSize": 64, "displayName": "Blaze Spawn Egg", "name": "blaze_spawn_egg"}, {"id": 877, "stackSize": 64, "displayName": "Cat Spawn Egg", "name": "cat_spawn_egg"}, {"id": 878, "stackSize": 64, "displayName": "Cave Spider Spawn Egg", "name": "cave_spider_spawn_egg"}, {"id": 879, "stackSize": 64, "displayName": "Chicken Spawn Egg", "name": "chicken_spawn_egg"}, {"id": 880, "stackSize": 64, "displayName": "Cod Spawn Egg", "name": "cod_spawn_egg"}, {"id": 881, "stackSize": 64, "displayName": "Cow Spawn Egg", "name": "cow_spawn_egg"}, {"id": 882, "stackSize": 64, "displayName": "Creeper Spawn Egg", "name": "creeper_spawn_egg"}, {"id": 883, "stackSize": 64, "displayName": "Dolphin Spawn Egg", "name": "dolphin_spawn_egg"}, {"id": 884, "stackSize": 64, "displayName": "Donkey Spawn Egg", "name": "donkey_spawn_egg"}, {"id": 885, "stackSize": 64, "displayName": "Drowned Spawn Egg", "name": "drowned_spawn_egg"}, {"id": 886, "stackSize": 64, "displayName": "Elder Guardian Spawn Egg", "name": "elder_guardian_spawn_egg"}, {"id": 887, "stackSize": 64, "displayName": "Enderman Spawn Egg", "name": "enderman_spawn_egg"}, {"id": 888, "stackSize": 64, "displayName": "Endermite Spawn Egg", "name": "endermite_spawn_egg"}, {"id": 889, "stackSize": 64, "displayName": "Evoker Spawn Egg", "name": "evoker_spawn_egg"}, {"id": 890, "stackSize": 64, "displayName": "Fox Spawn Egg", "name": "fox_spawn_egg"}, {"id": 891, "stackSize": 64, "displayName": "Ghast Spawn Egg", "name": "ghast_spawn_egg"}, {"id": 892, "stackSize": 64, "displayName": "Glow Squid Spawn Egg", "name": "glow_squid_spawn_egg"}, {"id": 893, "stackSize": 64, "displayName": "Goat Spawn Egg", "name": "goat_spawn_egg"}, {"id": 894, "stackSize": 64, "displayName": "Guardian Spawn Egg", "name": "guardian_spawn_egg"}, {"id": 895, "stackSize": 64, "displayName": "Hoglin Spawn Egg", "name": "hoglin_spawn_egg"}, {"id": 896, "stackSize": 64, "displayName": "Horse Spawn Egg", "name": "horse_spawn_egg"}, {"id": 897, "stackSize": 64, "displayName": "Husk Spawn Egg", "name": "husk_spawn_egg"}, {"id": 898, "displayName": "Llama Spawn Egg", "name": "llama_spawn_egg", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 0, "id": 925, "displayName": "Trader <PERSON>lama Spawn Egg", "name": "trader_llama_spawn_egg", "stackSize": 64}]}, {"id": 899, "stackSize": 64, "displayName": "Magma Cube Spawn Egg", "name": "magma_cube_spawn_egg"}, {"id": 900, "stackSize": 64, "displayName": "Mooshroom Spawn Egg", "name": "mooshroom_spawn_egg"}, {"id": 901, "stackSize": 64, "displayName": "Mule Spawn Egg", "name": "mule_spawn_egg"}, {"id": 902, "stackSize": 64, "displayName": "Ocelot Spawn Egg", "name": "ocelot_spawn_egg"}, {"id": 903, "stackSize": 64, "displayName": "Panda Spawn Egg", "name": "panda_spawn_egg"}, {"id": 904, "stackSize": 64, "displayName": "Parrot Spawn Egg", "name": "parrot_spawn_egg"}, {"id": 905, "stackSize": 64, "displayName": "Phantom Spawn Egg", "name": "phantom_spawn_egg"}, {"id": 906, "stackSize": 64, "displayName": "Pig Spawn Egg", "name": "pig_spawn_egg"}, {"id": 907, "stackSize": 64, "displayName": "Piglin Spawn Egg", "name": "piglin_spawn_egg"}, {"id": 908, "stackSize": 64, "displayName": "Piglin Brute Spawn Egg", "name": "piglin_brute_spawn_egg"}, {"id": 909, "stackSize": 64, "displayName": "Pillager Spawn Egg", "name": "pillager_spawn_egg"}, {"id": 910, "stackSize": 64, "displayName": "Polar Bear Spawn Egg", "name": "polar_bear_spawn_egg"}, {"id": 911, "stackSize": 64, "displayName": "Pufferfish Spawn Egg", "name": "pufferfish_spawn_egg"}, {"id": 912, "stackSize": 64, "displayName": "Rabbit Spawn Egg", "name": "rabbit_spawn_egg"}, {"id": 913, "stackSize": 64, "displayName": "Ravager Spawn Egg", "name": "ravager_spawn_egg"}, {"id": 914, "stackSize": 64, "displayName": "Salmon Spawn Egg", "name": "salmon_spawn_egg"}, {"id": 915, "stackSize": 64, "displayName": "Sheep Spawn Egg", "name": "sheep_spawn_egg"}, {"id": 916, "stackSize": 64, "displayName": "Shulker Spawn Egg", "name": "shulker_spawn_egg"}, {"id": 917, "stackSize": 64, "displayName": "Silverfish Spawn Egg", "name": "silverfish_spawn_egg"}, {"id": 918, "stackSize": 64, "displayName": "Skeleton Spawn Egg", "name": "skeleton_spawn_egg"}, {"id": 919, "stackSize": 64, "displayName": "Skeleton Horse Spawn Egg", "name": "skeleton_horse_spawn_egg"}, {"id": 920, "stackSize": 64, "displayName": "Slime Spawn Egg", "name": "slime_spawn_egg"}, {"id": 921, "stackSize": 64, "displayName": "Spider Spawn Egg", "name": "spider_spawn_egg"}, {"id": 922, "stackSize": 64, "displayName": "Squid Spawn Egg", "name": "squid_spawn_egg"}, {"id": 923, "stackSize": 64, "displayName": "Stray Spawn Egg", "name": "stray_spawn_egg"}, {"id": 924, "stackSize": 64, "displayName": "Strider Spawn Egg", "name": "strider_spawn_egg"}, {"id": 926, "stackSize": 64, "displayName": "Tropical Fish Spawn Egg", "name": "tropical_fish_spawn_egg"}, {"id": 927, "stackSize": 64, "displayName": "Turtle Spawn Egg", "name": "turtle_spawn_egg"}, {"id": 928, "stackSize": 64, "displayName": "Vex Spawn Egg", "name": "vex_spawn_egg"}, {"id": 929, "stackSize": 64, "displayName": "Villager Spawn Egg", "name": "villager_spawn_egg"}, {"id": 930, "stackSize": 64, "displayName": "Vindicator Spawn Egg", "name": "vindicator_spawn_egg"}, {"id": 931, "stackSize": 64, "displayName": "Wandering Trader Spawn Egg", "name": "wandering_trader_spawn_egg"}, {"id": 932, "stackSize": 64, "displayName": "Witch Spawn Egg", "name": "witch_spawn_egg"}, {"id": 933, "stackSize": 64, "displayName": "Wither Skeleton Spawn Egg", "name": "wither_skeleton_spawn_egg"}, {"id": 934, "stackSize": 64, "displayName": "Wolf Spawn Egg", "name": "wolf_spawn_egg"}, {"id": 935, "stackSize": 64, "displayName": "Zoglin Spawn Egg", "name": "zoglin_spawn_egg"}, {"id": 936, "stackSize": 64, "displayName": "Zombie Spawn Egg", "name": "zombie_spawn_egg"}, {"id": 937, "stackSize": 64, "displayName": "Zombie Horse Spawn Egg", "name": "zombie_horse_spawn_egg"}, {"id": 938, "stackSize": 64, "displayName": "Zombie Villager Spawn Egg", "name": "zombie_villager_spawn_egg"}, {"id": 939, "stackSize": 64, "displayName": "Zombified Piglin Spawn Egg", "name": "zombie_pigman_spawn_egg"}, {"id": 940, "stackSize": 64, "displayName": "Bottle o' Enchanting", "name": "experience_bottle"}, {"id": 941, "stackSize": 64, "displayName": "Fire Charge", "name": "fire_charge"}, {"id": 942, "stackSize": 1, "displayName": "Book and Quill", "name": "writable_book"}, {"id": 943, "stackSize": 16, "displayName": "Written Book", "name": "written_book"}, {"id": 944, "stackSize": 64, "displayName": "<PERSON><PERSON>", "name": "frame"}, {"id": 945, "stackSize": 64, "displayName": "G<PERSON> Item <PERSON>", "name": "glow_frame"}, {"id": 946, "stackSize": 64, "displayName": "Flower Pot", "name": "flower_pot"}, {"id": 947, "stackSize": 64, "displayName": "Carrot", "name": "carrot"}, {"id": 948, "stackSize": 64, "displayName": "Potato", "name": "potato"}, {"id": 949, "stackSize": 64, "displayName": "Baked Potato", "name": "baked_potato"}, {"id": 950, "stackSize": 64, "displayName": "Poisonous Potato", "name": "poisonous_potato"}, {"id": 951, "stackSize": 64, "displayName": "Empty Map", "name": "empty_map"}, {"id": 952, "stackSize": 64, "displayName": "Golden Carrot", "name": "golden_carrot"}, {"id": 953, "displayName": "Skeleton Skull", "name": "skull", "stackSize": 64, "metadata": 0, "variations": [{"metadata": 1, "id": 954, "displayName": "Wither Skeleton Skull", "name": "wither_skeleton_skull", "stackSize": 64}, {"metadata": 2, "id": 956, "displayName": "Zombie Head", "name": "zombie_head", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"metadata": 3, "id": 955, "displayName": "Player Head", "name": "player_head", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"metadata": 4, "id": 957, "displayName": "Creeper Head", "name": "creeper_head", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}, {"metadata": 5, "id": 958, "displayName": "Dragon Head", "name": "dragon_head", "stackSize": 64, "enchantCategories": ["wearable", "vanishable"]}]}, {"id": 959, "stackSize": 64, "displayName": "Nether Star", "name": "nether_star"}, {"id": 960, "stackSize": 64, "displayName": "Pumpkin Pie", "name": "pumpkin_pie"}, {"id": 961, "stackSize": 64, "displayName": "Firework Rocket", "name": "firework_rocket"}, {"id": 962, "stackSize": 64, "displayName": "Firework Star", "name": "firework_star"}, {"id": 963, "stackSize": 1, "displayName": "Enchanted Book", "name": "enchanted_book"}, {"id": 964, "stackSize": 64, "displayName": "Nether Brick", "name": "netherbrick"}, {"id": 965, "stackSize": 64, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "prismarine_shard"}, {"id": 966, "stackSize": 64, "displayName": "Prismarine Crystals", "name": "prismarine_crystals"}, {"id": 967, "stackSize": 64, "displayName": "Raw Rabbit", "name": "rabbit"}, {"id": 968, "stackSize": 64, "displayName": "Cooked Rabbit", "name": "cooked_rabbit"}, {"id": 969, "stackSize": 1, "displayName": "Rabbit Stew", "name": "rabbit_stew"}, {"id": 970, "stackSize": 64, "displayName": "<PERSON>'s Foot", "name": "rabbit_foot"}, {"id": 971, "stackSize": 64, "displayName": "<PERSON>", "name": "rabbit_hide"}, {"id": 972, "stackSize": 16, "displayName": "Armor Stand", "name": "armor_stand"}, {"id": 973, "stackSize": 1, "displayName": "Iron Horse Armor", "name": "iron_horse_armor"}, {"id": 974, "stackSize": 1, "displayName": "Golden Horse Armor", "name": "golden_horse_armor"}, {"id": 975, "stackSize": 1, "displayName": "Diamond Horse Armor", "name": "diamond_horse_armor"}, {"id": 976, "stackSize": 1, "displayName": "Leather Horse Armor", "name": "leather_horse_armor"}, {"id": 977, "stackSize": 64, "displayName": "Lead", "name": "lead"}, {"id": 978, "stackSize": 64, "displayName": "Name Tag", "name": "name_tag"}, {"id": 979, "stackSize": 1, "displayName": "Minecart with Command Block", "name": "command_block_minecart"}, {"id": 980, "stackSize": 64, "displayName": "<PERSON>", "name": "mutton"}, {"id": 981, "stackSize": 64, "displayName": "Cooked <PERSON>tton", "name": "cooked_mutton"}, {"id": 997, "displayName": "Black Banner", "name": "banner", "stackSize": 16, "metadata": 0, "variations": [{"metadata": 1, "id": 996, "displayName": "Red Banner", "name": "red_banner", "stackSize": 16}, {"metadata": 2, "id": 995, "displayName": "<PERSON> Banner", "name": "green_banner", "stackSize": 16}, {"metadata": 3, "id": 994, "displayName": "<PERSON>", "name": "brown_banner", "stackSize": 16}, {"metadata": 4, "id": 993, "displayName": "Blue Banner", "name": "blue_banner", "stackSize": 16}, {"metadata": 5, "id": 992, "displayName": "<PERSON> Banner", "name": "purple_banner", "stackSize": 16}, {"metadata": 6, "id": 991, "displayName": "<PERSON><PERSON>", "name": "cyan_banner", "stackSize": 16}, {"metadata": 7, "id": 990, "displayName": "<PERSON> Gray Banner", "name": "light_gray_banner", "stackSize": 16}, {"metadata": 8, "id": 989, "displayName": "<PERSON>", "name": "gray_banner", "stackSize": 16}, {"metadata": 9, "id": 988, "displayName": "Pink Banner", "name": "pink_banner", "stackSize": 16}, {"metadata": 10, "id": 987, "displayName": "Lime Banner", "name": "lime_banner", "stackSize": 16}, {"metadata": 11, "id": 986, "displayName": "Yellow Banner", "name": "yellow_banner", "stackSize": 16}, {"metadata": 12, "id": 985, "displayName": "Light Blue Banner", "name": "light_blue_banner", "stackSize": 16}, {"metadata": 13, "id": 984, "displayName": "Magenta Banner", "name": "magenta_banner", "stackSize": 16}, {"metadata": 14, "id": 983, "displayName": "Orange Banner", "name": "orange_banner", "stackSize": 16}, {"metadata": 15, "id": 982, "displayName": "White Banner", "name": "white_banner", "stackSize": 16}]}, {"id": 998, "stackSize": 64, "displayName": "End Crystal", "name": "end_crystal"}, {"id": 999, "stackSize": 64, "displayName": "Chorus Fruit", "name": "chorus_fruit"}, {"id": 1000, "stackSize": 64, "displayName": "Popped Chorus Fruit", "name": "popped_chorus_fruit"}, {"id": 1001, "stackSize": 64, "displayName": "Beetroot", "name": "beetroot"}, {"id": 1002, "stackSize": 64, "displayName": "Beetroot Seeds", "name": "beetroot_seeds"}, {"id": 1003, "stackSize": 1, "displayName": "Beetroot Soup", "name": "beetroot_soup"}, {"id": 1004, "stackSize": 64, "displayName": "Dragon's Breath", "name": "dragon_breath"}, {"id": 1005, "stackSize": 1, "displayName": "Splash Potion", "name": "splash_potion"}, {"id": 1008, "stackSize": 1, "displayName": "Lingering Potion", "name": "lingering_potion"}, {"id": 1009, "stackSize": 1, "displayName": "Shield", "name": "shield", "maxDurability": 336, "enchantCategories": ["breakable", "vanishable"], "repairWith": ["oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "crimson_planks", "warped_planks"]}, {"id": 1010, "stackSize": 1, "displayName": "Totem of Undying", "name": "totem_of_undying"}, {"id": 1012, "stackSize": 64, "displayName": "Iron Nugget", "name": "iron_nugget"}, {"id": 1015, "stackSize": 1, "displayName": "13 Disc", "name": "music_disc_13"}, {"id": 1016, "stackSize": 1, "displayName": "Cat Disc", "name": "music_disc_cat"}, {"id": 1017, "stackSize": 1, "displayName": "Blocks Disc", "name": "music_disc_blocks"}, {"id": 1018, "stackSize": 1, "displayName": "Chirp Disc", "name": "music_disc_chirp"}, {"id": 1019, "stackSize": 1, "displayName": "Far Disc", "name": "music_disc_far"}, {"id": 1020, "stackSize": 1, "displayName": "Mall Disc", "name": "music_disc_mall"}, {"id": 1021, "stackSize": 1, "displayName": "Mellohi Disc", "name": "music_disc_mellohi"}, {"id": 1022, "stackSize": 1, "displayName": "Stal Disc", "name": "music_disc_stal"}, {"id": 1023, "stackSize": 1, "displayName": "Strad Disc", "name": "music_disc_strad"}, {"id": 1024, "stackSize": 1, "displayName": "Ward Disc", "name": "music_disc_ward"}, {"id": 1025, "stackSize": 1, "displayName": "11 Disc", "name": "music_disc_11"}, {"id": 1026, "stackSize": 1, "displayName": "Wait Disc", "name": "music_disc_wait"}, {"id": 1027, "stackSize": 1, "displayName": "Music Disc", "name": "music_disc_otherside"}, {"id": 1028, "stackSize": 1, "displayName": "Music Disc", "name": "music_disc_pigstep"}, {"id": 1029, "stackSize": 1, "displayName": "Trident", "name": "trident", "maxDurability": 250, "enchantCategories": ["breakable", "vanishable", "trident"]}, {"id": 1030, "stackSize": 64, "displayName": "Phantom Membrane", "name": "phantom_membrane"}, {"id": 1031, "stackSize": 64, "displayName": "Nautilus Shell", "name": "nautilus_shell"}, {"id": 1032, "stackSize": 64, "displayName": "Heart of the Sea", "name": "heart_of_the_sea"}, {"id": 1033, "stackSize": 1, "displayName": "Crossbow", "name": "crossbow", "maxDurability": 326, "enchantCategories": ["breakable", "vanishable", "crossbow"]}, {"id": 1034, "stackSize": 1, "displayName": "Suspicious Stew", "name": "suspicious_stew"}, {"id": 1035, "stackSize": 64, "displayName": "Loom", "name": "loom"}, {"id": 1036, "stackSize": 1, "displayName": "<PERSON>", "name": "flower_banner_pattern"}, {"id": 1037, "stackSize": 1, "displayName": "<PERSON>", "name": "creeper_banner_pattern"}, {"id": 1038, "stackSize": 1, "displayName": "<PERSON>", "name": "skull_banner_pattern"}, {"id": 1039, "stackSize": 1, "displayName": "<PERSON>", "name": "mojang_banner_pattern"}, {"id": 1040, "stackSize": 1, "displayName": "<PERSON>", "name": "globe_banner_pattern"}, {"id": 1041, "stackSize": 1, "displayName": "<PERSON>", "name": "piglin_banner_pattern"}, {"id": 1042, "stackSize": 64, "displayName": "Composter", "name": "composter"}, {"id": 1043, "stackSize": 64, "displayName": "Barrel", "name": "barrel"}, {"id": 1044, "stackSize": 64, "displayName": "Smoker", "name": "smoker"}, {"id": 1045, "stackSize": 64, "displayName": "Blast Furnace", "name": "blast_furnace"}, {"id": 1046, "stackSize": 64, "displayName": "Cartography Table", "name": "cartography_table"}, {"id": 1047, "stackSize": 64, "displayName": "Fletching Table", "name": "fletching_table"}, {"id": 1048, "stackSize": 64, "displayName": "Grindstone", "name": "grindstone"}, {"id": 1049, "stackSize": 64, "displayName": "Smithing Table", "name": "smithing_table"}, {"id": 1050, "stackSize": 64, "displayName": "<PERSON><PERSON><PERSON>", "name": "stonecutter_block"}, {"id": 1051, "stackSize": 64, "displayName": "Bell", "name": "bell"}, {"id": 1052, "stackSize": 64, "displayName": "Lantern", "name": "lantern"}, {"id": 1053, "stackSize": 64, "displayName": "Soul Lantern", "name": "soul_lantern"}, {"id": 1054, "stackSize": 64, "displayName": "Sweet Berries", "name": "sweet_berries"}, {"id": 1055, "stackSize": 64, "displayName": "Glow Berries", "name": "glow_berries"}, {"id": 1056, "stackSize": 64, "displayName": "Campfire", "name": "campfire"}, {"id": 1057, "stackSize": 64, "displayName": "Soul Campfire", "name": "soul_campfire"}, {"id": 1058, "stackSize": 64, "displayName": "Shroomlight", "name": "shroomlight"}, {"id": 1059, "stackSize": 64, "displayName": "Honeycomb", "name": "honeycomb"}, {"id": 1060, "stackSize": 64, "displayName": "Bee Nest", "name": "bee_nest"}, {"id": 1061, "stackSize": 64, "displayName": "Beehive", "name": "beehive"}, {"id": 1062, "stackSize": 16, "displayName": "<PERSON>", "name": "honey_bottle"}, {"id": 1063, "stackSize": 64, "displayName": "Honeycomb Block", "name": "honeycomb_block"}, {"id": 1064, "stackSize": 64, "displayName": "Lodestone", "name": "lodestone"}, {"id": 1065, "stackSize": 64, "displayName": "Crying Obsidian", "name": "crying_obsidian"}, {"id": 1066, "stackSize": 64, "displayName": "Blackstone", "name": "blackstone"}, {"id": 1067, "stackSize": 64, "displayName": "Blackstone Slab", "name": "blackstone_slab"}, {"id": 1068, "stackSize": 64, "displayName": "Blackstone Stairs", "name": "blackstone_stairs"}, {"id": 1069, "stackSize": 64, "displayName": "Gilded Blackstone", "name": "gilded_blackstone"}, {"id": 1070, "stackSize": 64, "displayName": "Polished Blackstone", "name": "polished_blackstone"}, {"id": 1071, "stackSize": 64, "displayName": "Polished Blackstone Slab", "name": "polished_blackstone_slab"}, {"id": 1072, "stackSize": 64, "displayName": "Polished Blackstone Stairs", "name": "polished_blackstone_stairs"}, {"id": 1073, "stackSize": 64, "displayName": "Chiseled Polished Blackstone", "name": "chiseled_polished_blackstone"}, {"id": 1074, "stackSize": 64, "displayName": "Polished Blackstone Bricks", "name": "polished_blackstone_bricks"}, {"id": 1075, "stackSize": 64, "displayName": "Polished Blackstone Brick Slab", "name": "polished_blackstone_brick_slab"}, {"id": 1076, "stackSize": 64, "displayName": "Polished Blackstone Brick Stairs", "name": "polished_blackstone_brick_stairs"}, {"id": 1077, "stackSize": 64, "displayName": "Cracked Polished Blackstone Bricks", "name": "cracked_polished_blackstone_bricks"}, {"id": 1078, "stackSize": 64, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "respawn_anchor"}, {"id": 1079, "stackSize": 64, "displayName": "Candle", "name": "candle"}, {"id": 1080, "stackSize": 64, "displayName": "White Candle", "name": "white_candle"}, {"id": 1081, "stackSize": 64, "displayName": "Orange Candle", "name": "orange_candle"}, {"id": 1082, "stackSize": 64, "displayName": "<PERSON><PERSON><PERSON>", "name": "magenta_candle"}, {"id": 1083, "stackSize": 64, "displayName": "Light Blue Candle", "name": "light_blue_candle"}, {"id": 1084, "stackSize": 64, "displayName": "Yellow Candle", "name": "yellow_candle"}, {"id": 1085, "stackSize": 64, "displayName": "<PERSON><PERSON>", "name": "lime_candle"}, {"id": 1086, "stackSize": 64, "displayName": "Pink Candle", "name": "pink_candle"}, {"id": 1087, "stackSize": 64, "displayName": "<PERSON>", "name": "gray_candle"}, {"id": 1088, "stackSize": 64, "displayName": "Light Gray Candle", "name": "light_gray_candle"}, {"id": 1089, "stackSize": 64, "displayName": "<PERSON><PERSON>", "name": "cyan_candle"}, {"id": 1090, "stackSize": 64, "displayName": "Purple Candle", "name": "purple_candle"}, {"id": 1091, "stackSize": 64, "displayName": "Blue Candle", "name": "blue_candle"}, {"id": 1092, "stackSize": 64, "displayName": "<PERSON> Candle", "name": "brown_candle"}, {"id": 1093, "stackSize": 64, "displayName": "Green Candle", "name": "green_candle"}, {"id": 1094, "stackSize": 64, "displayName": "<PERSON> Candle", "name": "red_candle"}, {"id": 1095, "stackSize": 64, "displayName": "Black Candle", "name": "black_candle"}, {"id": 1096, "stackSize": 64, "displayName": "Small Amethyst Bud", "name": "small_amethyst_bud"}, {"id": 1097, "stackSize": 64, "displayName": "Medium Amethyst Bud", "name": "medium_amethyst_bud"}, {"id": 1098, "stackSize": 64, "displayName": "Large Amethyst Bud", "name": "large_amethyst_bud"}, {"id": 1099, "stackSize": 64, "displayName": "Amethyst Cluster", "name": "amethyst_cluster"}, {"id": 1100, "stackSize": 64, "displayName": "Pointed Dripstone", "name": "pointed_dripstone"}, {"id": 8011, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "name": "stone_slab2", "stackSize": 64}, {"id": 8013, "displayName": "<PERSON> Slab", "name": "stone_slab4", "stackSize": 64}, {"id": 8015, "displayName": "Polished Granite Slab", "name": "stone_slab3", "stackSize": 64}, {"id": 9006, "stackSize": 1, "name": "air", "displayName": "Air"}, {"id": 9010, "stackSize": 1, "name": "element_25", "displayName": "Element 25"}, {"id": 9011, "stackSize": 1, "name": "mangrove_leaves", "displayName": "Mangrove Leaves"}, {"id": 9017, "stackSize": 1, "name": "element_50", "displayName": "Element 50"}, {"id": 9026, "stackSize": 1, "name": "element_15", "displayName": "Element 15"}, {"id": 9028, "stackSize": 1, "name": "item.dark_oak_door", "displayName": "Dark Oak Door"}, {"id": 9031, "stackSize": 1, "name": "portal", "displayName": "Portal"}, {"id": 9033, "stackSize": 1, "name": "oak_chest_boat", "displayName": "Oak Chest Boat"}, {"id": 9039, "stackSize": 1, "name": "element_7", "displayName": "Element 7"}, {"id": 9048, "stackSize": 1, "name": "item.iron_door", "displayName": "Iron Door"}, {"id": 9063, "stackSize": 1, "name": "sparkler", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 9065, "stackSize": 1, "name": "element_61", "displayName": "Element 61"}, {"id": 9072, "stackSize": 1, "name": "item.campfire", "displayName": "Campfire"}, {"id": 9073, "stackSize": 1, "name": "magenta_candle_cake", "displayName": "Ma<PERSON>a Candle Cake"}, {"id": 9077, "stackSize": 1, "name": "element_1", "displayName": "Element 1"}, {"id": 9079, "stackSize": 1, "name": "fire", "displayName": "Fire"}, {"id": 9093, "stackSize": 1, "name": "element_62", "displayName": "Element 62"}, {"id": 9109, "stackSize": 1, "name": "element_13", "displayName": "Element 13"}, {"id": 9117, "stackSize": 1, "name": "element_43", "displayName": "Element 43"}, {"id": 9126, "stackSize": 1, "name": "element_2", "displayName": "Element 2"}, {"id": 9128, "stackSize": 1, "name": "item.crimson_door", "displayName": "Crimson Door"}, {"id": 9130, "stackSize": 1, "name": "spawn_egg", "displayName": "Spawn Egg"}, {"id": 9134, "stackSize": 1, "name": "element_35", "displayName": "Element 35"}, {"id": 9137, "stackSize": 1, "name": "element_104", "displayName": "Element 104"}, {"id": 9144, "stackSize": 1, "name": "real_double_stone_slab2", "displayName": "Real Double Stone Slab2"}, {"id": 9147, "stackSize": 1, "name": "carrots", "displayName": "Carrots"}, {"id": 9167, "stackSize": 1, "name": "flowing_water", "displayName": "Flowing Water"}, {"id": 9170, "stackSize": 1, "name": "lit_deepslate_redstone_ore", "displayName": "Lit Deepslate Redstone Ore"}, {"id": 9174, "stackSize": 1, "name": "lit_redstone_lamp", "displayName": "Lit Redstone Lamp"}, {"id": 9176, "stackSize": 1, "name": "element_52", "displayName": "Element 52"}, {"id": 9177, "stackSize": 1, "name": "real_double_stone_slab4", "displayName": "Real Double Stone Slab4"}, {"id": 9188, "stackSize": 1, "name": "element_86", "displayName": "Element 86"}, {"id": 9202, "stackSize": 1, "name": "end_gateway", "displayName": "End Gateway"}, {"id": 9204, "stackSize": 1, "name": "item.beetroot", "displayName": "Beetroot"}, {"id": 9205, "stackSize": 1, "name": "element_101", "displayName": "Element 101"}, {"id": 9207, "stackSize": 1, "name": "element_49", "displayName": "Element 49"}, {"id": 9216, "stackSize": 1, "name": "real_double_stone_slab3", "displayName": "Real Double Stone Slab3"}, {"id": 9230, "stackSize": 1, "name": "element_51", "displayName": "Element 51"}, {"id": 9231, "stackSize": 1, "name": "double_wooden_slab", "displayName": "Double Wooden Slab"}, {"id": 9232, "stackSize": 1, "name": "hard_stained_glass", "displayName": "Hard Stained Glass"}, {"id": 9233, "stackSize": 1, "name": "element_84", "displayName": "Element 84"}, {"id": 9240, "stackSize": 1, "name": "flowing_lava", "displayName": "Flowing Lava"}, {"id": 9242, "stackSize": 1, "name": "agent_spawn_egg", "displayName": "Agent Spawn Egg"}, {"id": 9248, "stackSize": 1, "name": "element_55", "displayName": "Element 55"}, {"id": 9253, "stackSize": 1, "name": "tadpole_bucket", "displayName": "Tadpole Bucket"}, {"id": 9256, "stackSize": 1, "name": "element_74", "displayName": "Element 74"}, {"id": 9267, "stackSize": 1, "name": "item.birch_door", "displayName": "<PERSON>"}, {"id": 9270, "stackSize": 1, "name": "item.nether_wart", "displayName": "Nether Wart"}, {"id": 9277, "stackSize": 1, "name": "element_116", "displayName": "Element 116"}, {"id": 9287, "stackSize": 1, "name": "element_97", "displayName": "Element 97"}, {"id": 9291, "stackSize": 1, "name": "chemistry_table", "displayName": "Chemistry Table"}, {"id": 9307, "stackSize": 1, "name": "element_23", "displayName": "Element 23"}, {"id": 9309, "stackSize": 1, "name": "tadpole_spawn_egg", "displayName": "Tadpole Spawn Egg"}, {"id": 9313, "stackSize": 1, "name": "mud_bricks", "displayName": "Mud Bricks"}, {"id": 9318, "stackSize": 1, "name": "item.reeds", "displayName": "<PERSON><PERSON>"}, {"id": 9323, "stackSize": 1, "name": "reserved6", "displayName": "Reserved6"}, {"id": 9332, "stackSize": 1, "name": "deny", "displayName": "<PERSON><PERSON>"}, {"id": 9333, "stackSize": 1, "name": "mud_brick_slab", "displayName": "Mud <PERSON> Slab"}, {"id": 9338, "stackSize": 1, "name": "item.cake", "displayName": "Cake"}, {"id": 9339, "stackSize": 1, "name": "dye", "displayName": "Dye"}, {"id": 9344, "stackSize": 1, "name": "sticky_piston_arm_collision", "displayName": "Sticky Piston Arm Collision"}, {"id": 9346, "stackSize": 1, "name": "element_41", "displayName": "Element 41"}, {"id": 9360, "stackSize": 1, "name": "item.flower_pot", "displayName": "Flower Pot"}, {"id": 9362, "stackSize": 1, "name": "unknown", "displayName": "Unknown"}, {"id": 9364, "stackSize": 1, "name": "camera", "displayName": "Camera"}, {"id": 9381, "stackSize": 1, "name": "unpowered_comparator", "displayName": "Unpowered Comparator"}, {"id": 9388, "stackSize": 1, "name": "element_44", "displayName": "Element 44"}, {"id": 9390, "stackSize": 1, "name": "green_candle_cake", "displayName": "Green Candle Cake"}, {"id": 9399, "stackSize": 1, "name": "element_111", "displayName": "Element 111"}, {"id": 9404, "stackSize": 1, "name": "firefly_spawn_egg", "displayName": "Firefly Spawn Egg"}, {"id": 9410, "stackSize": 1, "name": "colored_torch_rg", "displayName": "Colored Torch Rg"}, {"id": 9411, "stackSize": 1, "name": "bleach", "displayName": "Bleach"}, {"id": 9413, "stackSize": 1, "name": "element_21", "displayName": "Element 21"}, {"id": 9418, "stackSize": 1, "name": "powered_comparator", "displayName": "Powered Comparator"}, {"id": 9420, "stackSize": 1, "name": "element_0", "displayName": "Element 0"}, {"id": 9435, "stackSize": 1, "name": "pink_candle_cake", "displayName": "Pink Candle Cake"}, {"id": 9437, "stackSize": 1, "name": "glowingobsidian", "displayName": "Glowingobsidian"}, {"id": 9442, "stackSize": 1, "name": "element_109", "displayName": "Element 109"}, {"id": 9447, "stackSize": 1, "name": "npc_spawn_egg", "displayName": "Npc Spawn Egg"}, {"id": 9449, "stackSize": 1, "name": "element_80", "displayName": "Element 80"}, {"id": 9451, "stackSize": 1, "name": "powder_snow", "displayName": "Powder Snow"}, {"id": 9453, "stackSize": 1, "name": "spruce_wall_sign", "displayName": "Spruce Wall Sign"}, {"id": 9455, "stackSize": 1, "name": "warden_spawn_egg", "displayName": "Warden Spawn Egg"}, {"id": 9456, "stackSize": 1, "name": "daylight_detector_inverted", "displayName": "Daylight Detector Inverted"}, {"id": 9460, "stackSize": 1, "name": "rapid_fertilizer", "displayName": "Rapid Fertilizer"}, {"id": 9464, "stackSize": 1, "name": "standing_sign", "displayName": "Standing Sign"}, {"id": 9467, "stackSize": 1, "name": "item.frame", "displayName": "<PERSON>ame"}, {"id": 9475, "stackSize": 1, "name": "double_cut_copper_slab", "displayName": "Double Cut Copper Slab"}, {"id": 9476, "stackSize": 1, "name": "element_28", "displayName": "Element 28"}, {"id": 9479, "stackSize": 1, "name": "item.acacia_door", "displayName": "Acacia Door"}, {"id": 9490, "stackSize": 1, "name": "orange_candle_cake", "displayName": "Orange Candle Cake"}, {"id": 9501, "stackSize": 1, "name": "sculk_vein", "displayName": "Sculk Vein"}, {"id": 9507, "stackSize": 1, "name": "element_47", "displayName": "Element 47"}, {"id": 9512, "stackSize": 1, "name": "element_69", "displayName": "Element 69"}, {"id": 9514, "stackSize": 1, "name": "deepslate_brick_double_slab", "displayName": "Deepslate Brick Double Slab"}, {"id": 9519, "stackSize": 1, "name": "colored_torch_bp", "displayName": "Colored Torch Bp"}, {"id": 9521, "stackSize": 1, "name": "element_102", "displayName": "Element 102"}, {"id": 9525, "stackSize": 1, "name": "element_63", "displayName": "Element 63"}, {"id": 9540, "stackSize": 1, "name": "info_update2", "displayName": "Info Update2"}, {"id": 9550, "stackSize": 1, "name": "element_42", "displayName": "Element 42"}, {"id": 9554, "stackSize": 1, "name": "element_73", "displayName": "Element 73"}, {"id": 9560, "stackSize": 1, "name": "coral_fan_hang2", "displayName": "Coral Fan Hang2"}, {"id": 9561, "stackSize": 1, "name": "balloon", "displayName": "Balloon"}, {"id": 9567, "stackSize": 1, "name": "frog_spawn_egg", "displayName": "Frog Spawn Egg"}, {"id": 9568, "stackSize": 1, "name": "field_masoned_banner_pattern", "displayName": "Field Masoned Banner Pattern"}, {"id": 9571, "stackSize": 1, "name": "bordure_indented_banner_pattern", "displayName": "Bordure Indented Banner Pattern"}, {"id": 9572, "stackSize": 1, "name": "purple_candle_cake", "displayName": "Purple Candle Cake"}, {"id": 9574, "stackSize": 1, "name": "potatoes", "displayName": "Potatoes"}, {"id": 9575, "stackSize": 1, "name": "element_78", "displayName": "Element 78"}, {"id": 9580, "stackSize": 1, "name": "compound", "displayName": "Compound"}, {"id": 9581, "stackSize": 1, "name": "ice_bomb", "displayName": "Ice Bomb"}, {"id": 9582, "stackSize": 1, "name": "medicine", "displayName": "Medicine"}, {"id": 9585, "stackSize": 1, "name": "element_92", "displayName": "Element 92"}, {"id": 9586, "stackSize": 1, "name": "glow_stick", "displayName": "Glow Stick"}, {"id": 9588, "stackSize": 1, "name": "lodestone_compass", "displayName": "Lodestone Compass"}, {"id": 9589, "stackSize": 1, "name": "element_83", "displayName": "Element 83"}, {"id": 9594, "stackSize": 1, "name": "polished_deepslate_double_slab", "displayName": "Polished Deepslate Double Slab"}, {"id": 9595, "stackSize": 1, "name": "item.warped_door", "displayName": "Warped Door"}, {"id": 9599, "stackSize": 1, "name": "mud", "displayName": "Mud"}, {"id": 9601, "stackSize": 1, "name": "black_candle_cake", "displayName": "Black Candle Cake"}, {"id": 9614, "stackSize": 1, "name": "hard_glass", "displayName": "Hard Glass"}, {"id": 9616, "stackSize": 1, "name": "element_76", "displayName": "Element 76"}, {"id": 9618, "stackSize": 1, "name": "birch_chest_boat", "displayName": "Birch Chest Boat"}, {"id": 9619, "stackSize": 1, "name": "element_33", "displayName": "Element 33"}, {"id": 9621, "stackSize": 1, "name": "frog_spawn", "displayName": "Frog Spawn"}, {"id": 9622, "stackSize": 1, "name": "allay_spawn_egg", "displayName": "Allay Spawn Egg"}, {"id": 9623, "stackSize": 1, "name": "waxed_weathered_double_cut_copper_slab", "displayName": "Waxed Weathered Double Cut Copper Slab"}, {"id": 9626, "stackSize": 1, "name": "mangrove_propagule", "displayName": "Mangrove Propagule"}, {"id": 9629, "stackSize": 1, "name": "cyan_candle_cake", "displayName": "<PERSON><PERSON>"}, {"id": 9630, "stackSize": 1, "name": "candle_cake", "displayName": "Candle Cake"}, {"id": 9632, "stackSize": 1, "name": "jungle_chest_boat", "displayName": "Jungle Chest Boat"}, {"id": 9633, "stackSize": 1, "name": "spruce_chest_boat", "displayName": "Spruce Chest Boat"}, {"id": 9634, "stackSize": 1, "name": "acacia_chest_boat", "displayName": "Acacia Chest Boat"}, {"id": 9635, "stackSize": 1, "name": "dark_oak_chest_boat", "displayName": "Dark Oak Chest Boat"}, {"id": 9636, "stackSize": 1, "name": "chest_boat", "displayName": "Chest Boat"}, {"id": 9639, "stackSize": 1, "name": "element_53", "displayName": "Element 53"}, {"id": 9640, "stackSize": 1, "name": "lit_blast_furnace", "displayName": "Lit Blast Furnace"}, {"id": 9641, "stackSize": 1, "name": "element_100", "displayName": "Element 100"}, {"id": 9644, "stackSize": 1, "name": "real_double_stone_slab", "displayName": "Real Double Stone Slab"}, {"id": 9651, "stackSize": 1, "name": "polished_blackstone_double_slab", "displayName": "Polished Blackstone Double Slab"}, {"id": 9652, "stackSize": 1, "name": "blackstone_double_slab", "displayName": "Blackstone Double Slab"}, {"id": 9656, "stackSize": 1, "name": "soul_fire", "displayName": "Soul Fire"}, {"id": 9661, "stackSize": 1, "name": "element_103", "displayName": "Element 103"}, {"id": 9664, "stackSize": 1, "name": "element_22", "displayName": "Element 22"}, {"id": 9665, "stackSize": 1, "name": "blue_candle_cake", "displayName": "Blue Candle Cake"}, {"id": 9668, "stackSize": 1, "name": "spruce_standing_sign", "displayName": "Spruce Standing Sign"}, {"id": 9676, "stackSize": 1, "name": "item.hopper", "displayName": "<PERSON>"}, {"id": 9677, "stackSize": 1, "name": "mud_brick_double_slab", "displayName": "Mud Brick Double Slab"}, {"id": 9679, "stackSize": 1, "name": "hard_stained_glass_pane", "displayName": "Hard Stained Glass Pane"}, {"id": 9692, "stackSize": 1, "name": "birch_wall_sign", "displayName": "<PERSON> Sign"}, {"id": 9695, "stackSize": 1, "name": "element_3", "displayName": "Element 3"}, {"id": 9696, "stackSize": 1, "name": "element_4", "displayName": "Element 4"}, {"id": 9698, "stackSize": 1, "name": "element_5", "displayName": "Element 5"}, {"id": 9700, "stackSize": 1, "name": "element_6", "displayName": "Element 6"}, {"id": 9701, "stackSize": 1, "name": "element_8", "displayName": "Element 8"}, {"id": 9702, "stackSize": 1, "name": "element_9", "displayName": "Element 9"}, {"id": 9704, "stackSize": 1, "name": "element_10", "displayName": "Element 10"}, {"id": 9705, "stackSize": 1, "name": "element_11", "displayName": "Element 11"}, {"id": 9706, "stackSize": 1, "name": "element_12", "displayName": "Element 12"}, {"id": 9707, "stackSize": 1, "name": "element_14", "displayName": "Element 14"}, {"id": 9708, "stackSize": 1, "name": "element_16", "displayName": "Element 16"}, {"id": 9709, "stackSize": 1, "name": "element_17", "displayName": "Element 17"}, {"id": 9710, "stackSize": 1, "name": "client_request_placeholder_block", "displayName": "Client Request Placeholder Block"}, {"id": 9711, "stackSize": 1, "name": "element_18", "displayName": "Element 18"}, {"id": 9712, "stackSize": 1, "name": "element_19", "displayName": "Element 19"}, {"id": 9713, "stackSize": 1, "name": "element_20", "displayName": "Element 20"}, {"id": 9714, "stackSize": 1, "name": "pearlescent_froglight", "displayName": "Pearlescent Froglight"}, {"id": 9715, "stackSize": 1, "name": "element_24", "displayName": "Element 24"}, {"id": 9716, "stackSize": 1, "name": "element_26", "displayName": "Element 26"}, {"id": 9717, "stackSize": 1, "name": "element_27", "displayName": "Element 27"}, {"id": 9718, "stackSize": 1, "name": "element_29", "displayName": "Element 29"}, {"id": 9719, "stackSize": 1, "name": "element_30", "displayName": "Element 30"}, {"id": 9720, "stackSize": 1, "name": "element_31", "displayName": "Element 31"}, {"id": 9721, "stackSize": 1, "name": "element_32", "displayName": "Element 32"}, {"id": 9722, "stackSize": 1, "name": "element_34", "displayName": "Element 34"}, {"id": 9723, "stackSize": 1, "name": "element_36", "displayName": "Element 36"}, {"id": 9725, "stackSize": 1, "name": "element_37", "displayName": "Element 37"}, {"id": 9726, "stackSize": 1, "name": "element_38", "displayName": "Element 38"}, {"id": 9727, "stackSize": 1, "name": "element_39", "displayName": "Element 39"}, {"id": 9728, "stackSize": 1, "name": "element_40", "displayName": "Element 40"}, {"id": 9729, "stackSize": 1, "name": "element_45", "displayName": "Element 45"}, {"id": 9730, "stackSize": 1, "name": "element_46", "displayName": "Element 46"}, {"id": 9731, "stackSize": 1, "name": "element_48", "displayName": "Element 48"}, {"id": 9732, "stackSize": 1, "name": "element_54", "displayName": "Element 54"}, {"id": 9733, "stackSize": 1, "name": "element_56", "displayName": "Element 56"}, {"id": 9734, "stackSize": 1, "name": "element_57", "displayName": "Element 57"}, {"id": 9736, "stackSize": 1, "name": "lit_redstone_ore", "displayName": "Lit Redstone Ore"}, {"id": 9738, "stackSize": 1, "name": "element_58", "displayName": "Element 58"}, {"id": 9739, "stackSize": 1, "name": "element_59", "displayName": "Element 59"}, {"id": 9740, "stackSize": 1, "name": "element_60", "displayName": "Element 60"}, {"id": 9741, "stackSize": 1, "name": "light_gray_candle_cake", "displayName": "Light Gray Candle Cake"}, {"id": 9742, "stackSize": 1, "name": "element_64", "displayName": "Element 64"}, {"id": 9743, "stackSize": 1, "name": "element_65", "displayName": "Element 65"}, {"id": 9744, "stackSize": 1, "name": "element_66", "displayName": "Element 66"}, {"id": 9745, "stackSize": 1, "name": "element_67", "displayName": "Element 67"}, {"id": 9746, "stackSize": 1, "name": "element_68", "displayName": "Element 68"}, {"id": 9747, "stackSize": 1, "name": "element_70", "displayName": "Element 70"}, {"id": 9748, "stackSize": 1, "name": "element_71", "displayName": "Element 71"}, {"id": 9750, "stackSize": 1, "name": "element_72", "displayName": "Element 72"}, {"id": 9751, "stackSize": 1, "name": "element_75", "displayName": "Element 75"}, {"id": 9753, "stackSize": 1, "name": "element_77", "displayName": "Element 77"}, {"id": 9755, "stackSize": 1, "name": "element_79", "displayName": "Element 79"}, {"id": 9757, "stackSize": 1, "name": "element_81", "displayName": "Element 81"}, {"id": 9758, "stackSize": 1, "name": "element_82", "displayName": "Element 82"}, {"id": 9760, "stackSize": 1, "name": "element_85", "displayName": "Element 85"}, {"id": 9761, "stackSize": 1, "name": "element_87", "displayName": "Element 87"}, {"id": 9762, "stackSize": 1, "name": "element_88", "displayName": "Element 88"}, {"id": 9763, "stackSize": 1, "name": "element_89", "displayName": "Element 89"}, {"id": 9764, "stackSize": 1, "name": "element_90", "displayName": "Element 90"}, {"id": 9765, "stackSize": 1, "name": "element_91", "displayName": "Element 91"}, {"id": 9766, "stackSize": 1, "name": "element_93", "displayName": "Element 93"}, {"id": 9767, "stackSize": 1, "name": "element_94", "displayName": "Element 94"}, {"id": 9768, "stackSize": 1, "name": "element_95", "displayName": "Element 95"}, {"id": 9769, "stackSize": 1, "name": "element_96", "displayName": "Element 96"}, {"id": 9771, "stackSize": 1, "name": "element_98", "displayName": "Element 98"}, {"id": 9772, "stackSize": 1, "name": "element_99", "displayName": "Element 99"}, {"id": 9774, "stackSize": 1, "name": "element_105", "displayName": "Element 105"}, {"id": 9775, "stackSize": 1, "name": "element_106", "displayName": "Element 106"}, {"id": 9777, "stackSize": 1, "name": "element_107", "displayName": "Element 107"}, {"id": 9778, "stackSize": 1, "name": "element_108", "displayName": "Element 108"}, {"id": 9779, "stackSize": 1, "name": "element_110", "displayName": "Element 110"}, {"id": 9780, "stackSize": 1, "name": "element_112", "displayName": "Element 112"}, {"id": 9782, "stackSize": 1, "name": "element_113", "displayName": "Element 113"}, {"id": 9783, "stackSize": 1, "name": "element_114", "displayName": "Element 114"}, {"id": 9787, "stackSize": 1, "name": "element_115", "displayName": "Element 115"}, {"id": 9788, "stackSize": 1, "name": "element_117", "displayName": "Element 117"}, {"id": 9789, "stackSize": 1, "name": "element_118", "displayName": "Element 118"}, {"id": 9791, "stackSize": 1, "name": "white_candle_cake", "displayName": "White Candle Cake"}, {"id": 9794, "stackSize": 1, "name": "boat", "displayName": "Boat"}, {"id": 9795, "stackSize": 1, "name": "banner_pattern", "displayName": "<PERSON>"}, {"id": 9800, "stackSize": 1, "name": "birch_standing_sign", "displayName": "<PERSON> Standing Sign"}, {"id": 9801, "stackSize": 1, "name": "item.glow_frame", "displayName": "Glow Frame"}, {"id": 9804, "stackSize": 1, "name": "piston_arm_collision", "displayName": "Piston Arm Collision"}, {"id": 9808, "stackSize": 1, "name": "gray_candle_cake", "displayName": "Gray Candle Cake"}, {"id": 9809, "stackSize": 1, "name": "crimson_wall_sign", "displayName": "Crimson Wall Sign"}, {"id": 9815, "stackSize": 1, "name": "info_update", "displayName": "Info Update"}, {"id": 9816, "stackSize": 1, "name": "sculk_shrieker", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 9823, "stackSize": 1, "name": "warped_wall_sign", "displayName": "Warped Wall Sign"}, {"id": 9825, "stackSize": 1, "name": "coral_fan_hang", "displayName": "Coral Fan Hang"}, {"id": 9827, "stackSize": 1, "name": "packed_mud", "displayName": "Packed Mud"}, {"id": 9829, "stackSize": 1, "name": "light_blue_candle_cake", "displayName": "Light Blue Candle Cake"}, {"id": 9835, "stackSize": 1, "name": "deepslate_tile_double_slab", "displayName": "Deepslate Tile Double Slab"}, {"id": 9836, "stackSize": 1, "name": "oxidized_double_cut_copper_slab", "displayName": "Oxidized Double Cut Copper Slab"}, {"id": 9839, "stackSize": 1, "name": "lava_cauldron", "displayName": "<PERSON><PERSON>"}, {"id": 9840, "stackSize": 1, "name": "exposed_double_cut_copper_slab", "displayName": "Exposed Double Cut Copper Slab"}, {"id": 9843, "stackSize": 1, "name": "hard_glass_pane", "displayName": "Hard Glass Pane"}, {"id": 9844, "stackSize": 1, "name": "polished_blackstone_brick_double_slab", "displayName": "Polished Blackstone Brick Double Slab"}, {"id": 9845, "stackSize": 1, "name": "crimson_double_slab", "displayName": "Crimson Double Slab"}, {"id": 9849, "stackSize": 1, "name": "mud_brick_wall", "displayName": "Mud Brick Wall"}, {"id": 9854, "stackSize": 1, "name": "stonecutter", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 9856, "stackSize": 1, "name": "invisible_bedrock", "displayName": "Invisible Bedrock"}, {"id": 9858, "stackSize": 1, "name": "warped_standing_sign", "displayName": "Warped Standing Sign"}, {"id": 9860, "stackSize": 1, "name": "wall_sign", "displayName": "Wall Sign"}, {"id": 9867, "stackSize": 1, "name": "underwater_torch", "displayName": "Underwater Torch"}, {"id": 9868, "stackSize": 1, "name": "ochre_froglight", "displayName": "Ochre Froglight"}, {"id": 9871, "stackSize": 1, "name": "moving_block", "displayName": "Moving Block"}, {"id": 9875, "stackSize": 1, "name": "cave_vines_body_with_berries", "displayName": "Cave Vines Body With Berries"}, {"id": 9876, "stackSize": 1, "name": "sculk_catalyst", "displayName": "Sculk Catalyst"}, {"id": 9877, "stackSize": 1, "name": "brown_candle_cake", "displayName": "Brown Candle Cake"}, {"id": 9879, "stackSize": 1, "name": "acacia_wall_sign", "displayName": "Acacia Wall Sign"}, {"id": 9884, "stackSize": 1, "name": "item.wooden_door", "displayName": "Wooden Door"}, {"id": 9887, "stackSize": 1, "name": "redstone_wire", "displayName": "Redstone Wire"}, {"id": 9888, "stackSize": 1, "name": "lava", "displayName": "<PERSON><PERSON>"}, {"id": 9890, "stackSize": 1, "name": "coral_fan_hang3", "displayName": "Coral Fan Hang3"}, {"id": 9904, "stackSize": 1, "name": "warped_double_slab", "displayName": "Warped Double Slab"}, {"id": 9905, "stackSize": 1, "name": "jungle_wall_sign", "displayName": "Jungle Wall Sign"}, {"id": 9911, "stackSize": 1, "name": "powered_repeater", "displayName": "Powered Repeater"}, {"id": 9915, "stackSize": 1, "name": "mangrove_propagule_hanging", "displayName": "Mangrove Propagule Hanging"}, {"id": 9916, "stackSize": 1, "name": "yellow_candle_cake", "displayName": "Yellow Candle Cake"}, {"id": 9922, "stackSize": 1, "name": "item.wheat", "displayName": "Wheat"}, {"id": 9923, "stackSize": 1, "name": "item.spruce_door", "displayName": "Spruce Door"}, {"id": 9924, "stackSize": 1, "name": "frosted_ice", "displayName": "Frosted Ice"}, {"id": 9927, "stackSize": 1, "name": "cave_vines", "displayName": "Cave Vines"}, {"id": 9929, "stackSize": 1, "name": "melon_stem", "displayName": "Melon Stem"}, {"id": 9931, "stackSize": 1, "name": "border_block", "displayName": "Border Block"}, {"id": 9935, "stackSize": 1, "name": "item.skull", "displayName": "Skull"}, {"id": 9936, "stackSize": 1, "name": "darkoak_wall_sign", "displayName": "Darkoak Wall Sign"}, {"id": 9939, "stackSize": 1, "name": "waxed_double_cut_copper_slab", "displayName": "Waxed Double Cut Copper Slab"}, {"id": 9941, "stackSize": 1, "name": "item.kelp", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 9943, "stackSize": 1, "name": "water", "displayName": "Water"}, {"id": 9944, "stackSize": 1, "name": "chemical_heat", "displayName": "Chemical Heat"}, {"id": 9945, "stackSize": 1, "name": "mud_brick_stairs", "displayName": "Mud Brick Stairs"}, {"id": 9946, "stackSize": 1, "name": "unpowered_repeater", "displayName": "Unpowered Repeater"}, {"id": 9951, "stackSize": 1, "name": "wall_banner", "displayName": "<PERSON>"}, {"id": 9954, "stackSize": 1, "name": "bubble_column", "displayName": "Bubble Column"}, {"id": 9956, "stackSize": 1, "name": "reinforced_deepslate", "displayName": "Reinforced Deepslate"}, {"id": 9958, "stackSize": 1, "name": "cobbled_deepslate_double_slab", "displayName": "Cobbled Deepslate Double Slab"}, {"id": 9959, "stackSize": 1, "name": "item.nether_sprouts", "displayName": "Nether Sprouts"}, {"id": 9964, "stackSize": 1, "name": "sweet_berry_bush", "displayName": "Sweet <PERSON>"}, {"id": 9965, "stackSize": 1, "name": "jungle_standing_sign", "displayName": "Jungle Standing Sign"}, {"id": 9968, "stackSize": 1, "name": "item.jungle_door", "displayName": "Jungle Door"}, {"id": 9974, "stackSize": 1, "name": "acacia_standing_sign", "displayName": "Acacia Standing Sign"}, {"id": 9976, "stackSize": 1, "name": "pumpkin_stem", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 9980, "stackSize": 1, "name": "verdant_froglight", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 9983, "stackSize": 1, "name": "cocoa", "displayName": "Cocoa"}, {"id": 9988, "stackSize": 1, "name": "item.bed", "displayName": "Bed"}, {"id": 9991, "stackSize": 1, "name": "waxed_exposed_double_cut_copper_slab", "displayName": "Waxed Exposed Double Cut Copper Slab"}, {"id": 9994, "stackSize": 1, "name": "bamboo_sapling", "displayName": "Bamboo Sapling"}, {"id": 9995, "stackSize": 1, "name": "standing_banner", "displayName": "Standing Banner"}, {"id": 10003, "stackSize": 1, "name": "sculk", "displayName": "Sculk"}, {"id": 10004, "stackSize": 1, "name": "weathered_double_cut_copper_slab", "displayName": "Weathered Double Cut Copper Slab"}, {"id": 10006, "stackSize": 1, "name": "allow", "displayName": "Allow"}, {"id": 10007, "stackSize": 1, "name": "item.chain", "displayName": "Chain"}, {"id": 10009, "stackSize": 1, "name": "lit_furnace", "displayName": "Lit Furnace"}, {"id": 10014, "stackSize": 1, "name": "item.camera", "displayName": "Camera"}, {"id": 10017, "stackSize": 1, "name": "crimson_standing_sign", "displayName": "Crimson Standing Sign"}, {"id": 10018, "stackSize": 1, "name": "darkoak_standing_sign", "displayName": "Darkoak Standing Sign"}, {"id": 10022, "stackSize": 1, "name": "netherreactor", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 10025, "stackSize": 1, "name": "trip_wire", "displayName": "<PERSON>"}, {"id": 10026, "stackSize": 1, "name": "item.cauldron", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 10027, "stackSize": 1, "name": "cave_vines_head_with_berries", "displayName": "Cave Vines Head With Berries"}, {"id": 10030, "stackSize": 1, "name": "item.brewing_stand", "displayName": "Brewing Stand"}, {"id": 10031, "stackSize": 1, "name": "end_portal", "displayName": "End Portal"}, {"id": 10033, "stackSize": 1, "name": "lit_smoker", "displayName": "Lit Smoker"}, {"id": 10035, "stackSize": 1, "name": "red_candle_cake", "displayName": "Red Candle Cake"}, {"id": 10037, "stackSize": 1, "name": "waxed_oxidized_double_cut_copper_slab", "displayName": "Waxed Oxidized Double Cut Copper Slab"}, {"id": 10044, "stackSize": 1, "name": "item.soul_campfire", "displayName": "Soul Campfire"}, {"id": 10046, "stackSize": 1, "name": "lime_candle_cake", "displayName": "Lime Candle Cake"}, {"id": 10050, "stackSize": 1, "name": "unlit_redstone_torch", "displayName": "Unlit Redstone Torch"}]