[{"id": 0, "name": "ambient.cave"}, {"id": 1, "name": "ambient.basalt_deltas.additions"}, {"id": 2, "name": "ambient.basalt_deltas.loop"}, {"id": 3, "name": "ambient.basalt_deltas.mood"}, {"id": 4, "name": "ambient.crimson_forest.additions"}, {"id": 5, "name": "ambient.crimson_forest.loop"}, {"id": 6, "name": "ambient.crimson_forest.mood"}, {"id": 7, "name": "ambient.nether_wastes.additions"}, {"id": 8, "name": "ambient.nether_wastes.loop"}, {"id": 9, "name": "ambient.nether_wastes.mood"}, {"id": 10, "name": "ambient.soul_sand_valley.additions"}, {"id": 11, "name": "ambient.soul_sand_valley.loop"}, {"id": 12, "name": "ambient.soul_sand_valley.mood"}, {"id": 13, "name": "ambient.warped_forest.additions"}, {"id": 14, "name": "ambient.warped_forest.loop"}, {"id": 15, "name": "ambient.warped_forest.mood"}, {"id": 16, "name": "ambient.underwater.enter"}, {"id": 17, "name": "ambient.underwater.exit"}, {"id": 18, "name": "ambient.underwater.loop"}, {"id": 19, "name": "ambient.underwater.loop.additions"}, {"id": 20, "name": "ambient.underwater.loop.additions.rare"}, {"id": 21, "name": "ambient.underwater.loop.additions.ultra_rare"}, {"id": 22, "name": "block.amethyst_block.break"}, {"id": 23, "name": "block.amethyst_block.chime"}, {"id": 24, "name": "block.amethyst_block.fall"}, {"id": 25, "name": "block.amethyst_block.hit"}, {"id": 26, "name": "block.amethyst_block.place"}, {"id": 27, "name": "block.amethyst_block.step"}, {"id": 28, "name": "block.amethyst_cluster.break"}, {"id": 29, "name": "block.amethyst_cluster.fall"}, {"id": 30, "name": "block.amethyst_cluster.hit"}, {"id": 31, "name": "block.amethyst_cluster.place"}, {"id": 32, "name": "block.amethyst_cluster.step"}, {"id": 33, "name": "block.ancient_debris.break"}, {"id": 34, "name": "block.ancient_debris.step"}, {"id": 35, "name": "block.ancient_debris.place"}, {"id": 36, "name": "block.ancient_debris.hit"}, {"id": 37, "name": "block.ancient_debris.fall"}, {"id": 38, "name": "block.anvil.break"}, {"id": 39, "name": "block.anvil.destroy"}, {"id": 40, "name": "block.anvil.fall"}, {"id": 41, "name": "block.anvil.hit"}, {"id": 42, "name": "block.anvil.land"}, {"id": 43, "name": "block.anvil.place"}, {"id": 44, "name": "block.anvil.step"}, {"id": 45, "name": "block.anvil.use"}, {"id": 46, "name": "item.armor.equip_chain"}, {"id": 47, "name": "item.armor.equip_diamond"}, {"id": 48, "name": "item.armor.equip_elytra"}, {"id": 49, "name": "item.armor.equip_generic"}, {"id": 50, "name": "item.armor.equip_gold"}, {"id": 51, "name": "item.armor.equip_iron"}, {"id": 52, "name": "item.armor.equip_leather"}, {"id": 53, "name": "item.armor.equip_netherite"}, {"id": 54, "name": "item.armor.equip_turtle"}, {"id": 55, "name": "entity.armor_stand.break"}, {"id": 56, "name": "entity.armor_stand.fall"}, {"id": 57, "name": "entity.armor_stand.hit"}, {"id": 58, "name": "entity.armor_stand.place"}, {"id": 59, "name": "entity.arrow.hit"}, {"id": 60, "name": "entity.arrow.hit_player"}, {"id": 61, "name": "entity.arrow.shoot"}, {"id": 62, "name": "item.axe.strip"}, {"id": 63, "name": "item.axe.scrape"}, {"id": 64, "name": "item.axe.wax_off"}, {"id": 65, "name": "entity.axolotl.attack"}, {"id": 66, "name": "entity.axolotl.death"}, {"id": 67, "name": "entity.axolotl.hurt"}, {"id": 68, "name": "entity.axolotl.idle_air"}, {"id": 69, "name": "entity.axolotl.idle_water"}, {"id": 70, "name": "entity.axolotl.splash"}, {"id": 71, "name": "entity.axolotl.swim"}, {"id": 72, "name": "block.azalea.break"}, {"id": 73, "name": "block.azalea.fall"}, {"id": 74, "name": "block.azalea.hit"}, {"id": 75, "name": "block.azalea.place"}, {"id": 76, "name": "block.azalea.step"}, {"id": 77, "name": "block.azalea_leaves.break"}, {"id": 78, "name": "block.azalea_leaves.fall"}, {"id": 79, "name": "block.azalea_leaves.hit"}, {"id": 80, "name": "block.azalea_leaves.place"}, {"id": 81, "name": "block.azalea_leaves.step"}, {"id": 82, "name": "block.bamboo.break"}, {"id": 83, "name": "block.bamboo.fall"}, {"id": 84, "name": "block.bamboo.hit"}, {"id": 85, "name": "block.bamboo.place"}, {"id": 86, "name": "block.bamboo.step"}, {"id": 87, "name": "block.bamboo_sapling.break"}, {"id": 88, "name": "block.bamboo_sapling.hit"}, {"id": 89, "name": "block.bamboo_sapling.place"}, {"id": 90, "name": "block.barrel.close"}, {"id": 91, "name": "block.barrel.open"}, {"id": 92, "name": "block.basalt.break"}, {"id": 93, "name": "block.basalt.step"}, {"id": 94, "name": "block.basalt.place"}, {"id": 95, "name": "block.basalt.hit"}, {"id": 96, "name": "block.basalt.fall"}, {"id": 97, "name": "entity.bat.ambient"}, {"id": 98, "name": "entity.bat.death"}, {"id": 99, "name": "entity.bat.hurt"}, {"id": 100, "name": "entity.bat.loop"}, {"id": 101, "name": "entity.bat.takeoff"}, {"id": 102, "name": "block.beacon.activate"}, {"id": 103, "name": "block.beacon.ambient"}, {"id": 104, "name": "block.beacon.deactivate"}, {"id": 105, "name": "block.beacon.power_select"}, {"id": 106, "name": "entity.bee.death"}, {"id": 107, "name": "entity.bee.hurt"}, {"id": 108, "name": "entity.bee.loop_aggressive"}, {"id": 109, "name": "entity.bee.loop"}, {"id": 110, "name": "entity.bee.sting"}, {"id": 111, "name": "entity.bee.pollinate"}, {"id": 112, "name": "block.beehive.drip"}, {"id": 113, "name": "block.beehive.enter"}, {"id": 114, "name": "block.beehive.exit"}, {"id": 115, "name": "block.beehive.shear"}, {"id": 116, "name": "block.beehive.work"}, {"id": 117, "name": "block.bell.use"}, {"id": 118, "name": "block.bell.resonate"}, {"id": 119, "name": "block.big_dripleaf.break"}, {"id": 120, "name": "block.big_dripleaf.fall"}, {"id": 121, "name": "block.big_dripleaf.hit"}, {"id": 122, "name": "block.big_dripleaf.place"}, {"id": 123, "name": "block.big_dripleaf.step"}, {"id": 124, "name": "entity.blaze.ambient"}, {"id": 125, "name": "entity.blaze.burn"}, {"id": 126, "name": "entity.blaze.death"}, {"id": 127, "name": "entity.blaze.hurt"}, {"id": 128, "name": "entity.blaze.shoot"}, {"id": 129, "name": "entity.boat.paddle_land"}, {"id": 130, "name": "entity.boat.paddle_water"}, {"id": 131, "name": "block.bone_block.break"}, {"id": 132, "name": "block.bone_block.fall"}, {"id": 133, "name": "block.bone_block.hit"}, {"id": 134, "name": "block.bone_block.place"}, {"id": 135, "name": "block.bone_block.step"}, {"id": 136, "name": "item.bone_meal.use"}, {"id": 137, "name": "item.book.page_turn"}, {"id": 138, "name": "item.book.put"}, {"id": 139, "name": "block.blastfurnace.fire_crackle"}, {"id": 140, "name": "item.bottle.empty"}, {"id": 141, "name": "item.bottle.fill"}, {"id": 142, "name": "item.bottle.fill_dragonbreath"}, {"id": 143, "name": "block.brewing_stand.brew"}, {"id": 144, "name": "block.bubble_column.bubble_pop"}, {"id": 145, "name": "block.bubble_column.upwards_ambient"}, {"id": 146, "name": "block.bubble_column.upwards_inside"}, {"id": 147, "name": "block.bubble_column.whirlpool_ambient"}, {"id": 148, "name": "block.bubble_column.whirlpool_inside"}, {"id": 149, "name": "item.bucket.empty"}, {"id": 150, "name": "item.bucket.empty_axolotl"}, {"id": 151, "name": "item.bucket.empty_fish"}, {"id": 152, "name": "item.bucket.empty_lava"}, {"id": 153, "name": "item.bucket.empty_powder_snow"}, {"id": 154, "name": "item.bucket.fill"}, {"id": 155, "name": "item.bucket.fill_axolotl"}, {"id": 156, "name": "item.bucket.fill_fish"}, {"id": 157, "name": "item.bucket.fill_lava"}, {"id": 158, "name": "item.bucket.fill_powder_snow"}, {"id": 159, "name": "item.bundle.drop_contents"}, {"id": 160, "name": "item.bundle.insert"}, {"id": 161, "name": "item.bundle.remove_one"}, {"id": 162, "name": "block.cake.add_candle"}, {"id": 163, "name": "block.calcite.break"}, {"id": 164, "name": "block.calcite.step"}, {"id": 165, "name": "block.calcite.place"}, {"id": 166, "name": "block.calcite.hit"}, {"id": 167, "name": "block.calcite.fall"}, {"id": 168, "name": "block.campfire.crackle"}, {"id": 169, "name": "block.candle.ambient"}, {"id": 170, "name": "block.candle.break"}, {"id": 171, "name": "block.candle.extinguish"}, {"id": 172, "name": "block.candle.fall"}, {"id": 173, "name": "block.candle.hit"}, {"id": 174, "name": "block.candle.place"}, {"id": 175, "name": "block.candle.step"}, {"id": 176, "name": "entity.cat.ambient"}, {"id": 177, "name": "entity.cat.stray_ambient"}, {"id": 178, "name": "entity.cat.death"}, {"id": 179, "name": "entity.cat.eat"}, {"id": 180, "name": "entity.cat.hiss"}, {"id": 181, "name": "entity.cat.beg_for_food"}, {"id": 182, "name": "entity.cat.hurt"}, {"id": 183, "name": "entity.cat.purr"}, {"id": 184, "name": "entity.cat.purreow"}, {"id": 185, "name": "block.cave_vines.break"}, {"id": 186, "name": "block.cave_vines.fall"}, {"id": 187, "name": "block.cave_vines.hit"}, {"id": 188, "name": "block.cave_vines.place"}, {"id": 189, "name": "block.cave_vines.step"}, {"id": 190, "name": "block.cave_vines.pick_berries"}, {"id": 191, "name": "block.chain.break"}, {"id": 192, "name": "block.chain.fall"}, {"id": 193, "name": "block.chain.hit"}, {"id": 194, "name": "block.chain.place"}, {"id": 195, "name": "block.chain.step"}, {"id": 196, "name": "block.chest.close"}, {"id": 197, "name": "block.chest.locked"}, {"id": 198, "name": "block.chest.open"}, {"id": 199, "name": "entity.chicken.ambient"}, {"id": 200, "name": "entity.chicken.death"}, {"id": 201, "name": "entity.chicken.egg"}, {"id": 202, "name": "entity.chicken.hurt"}, {"id": 203, "name": "entity.chicken.step"}, {"id": 204, "name": "block.chorus_flower.death"}, {"id": 205, "name": "block.chorus_flower.grow"}, {"id": 206, "name": "item.chorus_fruit.teleport"}, {"id": 207, "name": "entity.cod.ambient"}, {"id": 208, "name": "entity.cod.death"}, {"id": 209, "name": "entity.cod.flop"}, {"id": 210, "name": "entity.cod.hurt"}, {"id": 211, "name": "block.comparator.click"}, {"id": 212, "name": "block.composter.empty"}, {"id": 213, "name": "block.composter.fill"}, {"id": 214, "name": "block.composter.fill_success"}, {"id": 215, "name": "block.composter.ready"}, {"id": 216, "name": "block.conduit.activate"}, {"id": 217, "name": "block.conduit.ambient"}, {"id": 218, "name": "block.conduit.ambient.short"}, {"id": 219, "name": "block.conduit.attack.target"}, {"id": 220, "name": "block.conduit.deactivate"}, {"id": 221, "name": "block.copper.break"}, {"id": 222, "name": "block.copper.step"}, {"id": 223, "name": "block.copper.place"}, {"id": 224, "name": "block.copper.hit"}, {"id": 225, "name": "block.copper.fall"}, {"id": 226, "name": "block.coral_block.break"}, {"id": 227, "name": "block.coral_block.fall"}, {"id": 228, "name": "block.coral_block.hit"}, {"id": 229, "name": "block.coral_block.place"}, {"id": 230, "name": "block.coral_block.step"}, {"id": 231, "name": "entity.cow.ambient"}, {"id": 232, "name": "entity.cow.death"}, {"id": 233, "name": "entity.cow.hurt"}, {"id": 234, "name": "entity.cow.milk"}, {"id": 235, "name": "entity.cow.step"}, {"id": 236, "name": "entity.creeper.death"}, {"id": 237, "name": "entity.creeper.hurt"}, {"id": 238, "name": "entity.creeper.primed"}, {"id": 239, "name": "block.crop.break"}, {"id": 240, "name": "item.crop.plant"}, {"id": 241, "name": "item.crossbow.hit"}, {"id": 242, "name": "item.crossbow.loading_end"}, {"id": 243, "name": "item.crossbow.loading_middle"}, {"id": 244, "name": "item.crossbow.loading_start"}, {"id": 245, "name": "item.crossbow.quick_charge_1"}, {"id": 246, "name": "item.crossbow.quick_charge_2"}, {"id": 247, "name": "item.crossbow.quick_charge_3"}, {"id": 248, "name": "item.crossbow.shoot"}, {"id": 249, "name": "block.deepslate_bricks.break"}, {"id": 250, "name": "block.deepslate_bricks.fall"}, {"id": 251, "name": "block.deepslate_bricks.hit"}, {"id": 252, "name": "block.deepslate_bricks.place"}, {"id": 253, "name": "block.deepslate_bricks.step"}, {"id": 254, "name": "block.deepslate.break"}, {"id": 255, "name": "block.deepslate.fall"}, {"id": 256, "name": "block.deepslate.hit"}, {"id": 257, "name": "block.deepslate.place"}, {"id": 258, "name": "block.deepslate.step"}, {"id": 259, "name": "block.deepslate_tiles.break"}, {"id": 260, "name": "block.deepslate_tiles.fall"}, {"id": 261, "name": "block.deepslate_tiles.hit"}, {"id": 262, "name": "block.deepslate_tiles.place"}, {"id": 263, "name": "block.deepslate_tiles.step"}, {"id": 264, "name": "block.dispenser.dispense"}, {"id": 265, "name": "block.dispenser.fail"}, {"id": 266, "name": "block.dispenser.launch"}, {"id": 267, "name": "entity.dolphin.ambient"}, {"id": 268, "name": "entity.dolphin.ambient_water"}, {"id": 269, "name": "entity.dolphin.attack"}, {"id": 270, "name": "entity.dolphin.death"}, {"id": 271, "name": "entity.dolphin.eat"}, {"id": 272, "name": "entity.dolphin.hurt"}, {"id": 273, "name": "entity.dolphin.jump"}, {"id": 274, "name": "entity.dolphin.play"}, {"id": 275, "name": "entity.dolphin.splash"}, {"id": 276, "name": "entity.dolphin.swim"}, {"id": 277, "name": "entity.donkey.ambient"}, {"id": 278, "name": "entity.donkey.angry"}, {"id": 279, "name": "entity.donkey.chest"}, {"id": 280, "name": "entity.donkey.death"}, {"id": 281, "name": "entity.donkey.eat"}, {"id": 282, "name": "entity.donkey.hurt"}, {"id": 283, "name": "block.dripstone_block.break"}, {"id": 284, "name": "block.dripstone_block.step"}, {"id": 285, "name": "block.dripstone_block.place"}, {"id": 286, "name": "block.dripstone_block.hit"}, {"id": 287, "name": "block.dripstone_block.fall"}, {"id": 288, "name": "block.pointed_dripstone.break"}, {"id": 289, "name": "block.pointed_dripstone.step"}, {"id": 290, "name": "block.pointed_dripstone.place"}, {"id": 291, "name": "block.pointed_dripstone.hit"}, {"id": 292, "name": "block.pointed_dripstone.fall"}, {"id": 293, "name": "block.pointed_dripstone.land"}, {"id": 294, "name": "block.pointed_dripstone.drip_lava"}, {"id": 295, "name": "block.pointed_dripstone.drip_water"}, {"id": 296, "name": "block.pointed_dripstone.drip_lava_into_cauldron"}, {"id": 297, "name": "block.pointed_dripstone.drip_water_into_cauldron"}, {"id": 298, "name": "block.big_dripleaf.tilt_down"}, {"id": 299, "name": "block.big_dripleaf.tilt_up"}, {"id": 300, "name": "entity.drowned.ambient"}, {"id": 301, "name": "entity.drowned.ambient_water"}, {"id": 302, "name": "entity.drowned.death"}, {"id": 303, "name": "entity.drowned.death_water"}, {"id": 304, "name": "entity.drowned.hurt"}, {"id": 305, "name": "entity.drowned.hurt_water"}, {"id": 306, "name": "entity.drowned.shoot"}, {"id": 307, "name": "entity.drowned.step"}, {"id": 308, "name": "entity.drowned.swim"}, {"id": 309, "name": "item.dye.use"}, {"id": 310, "name": "entity.egg.throw"}, {"id": 311, "name": "entity.elder_guardian.ambient"}, {"id": 312, "name": "entity.elder_guardian.ambient_land"}, {"id": 313, "name": "entity.elder_guardian.curse"}, {"id": 314, "name": "entity.elder_guardian.death"}, {"id": 315, "name": "entity.elder_guardian.death_land"}, {"id": 316, "name": "entity.elder_guardian.flop"}, {"id": 317, "name": "entity.elder_guardian.hurt"}, {"id": 318, "name": "entity.elder_guardian.hurt_land"}, {"id": 319, "name": "item.elytra.flying"}, {"id": 320, "name": "block.enchantment_table.use"}, {"id": 321, "name": "block.ender_chest.close"}, {"id": 322, "name": "block.ender_chest.open"}, {"id": 323, "name": "entity.ender_dragon.ambient"}, {"id": 324, "name": "entity.ender_dragon.death"}, {"id": 325, "name": "entity.dragon_fireball.explode"}, {"id": 326, "name": "entity.ender_dragon.flap"}, {"id": 327, "name": "entity.ender_dragon.growl"}, {"id": 328, "name": "entity.ender_dragon.hurt"}, {"id": 329, "name": "entity.ender_dragon.shoot"}, {"id": 330, "name": "entity.ender_eye.death"}, {"id": 331, "name": "entity.ender_eye.launch"}, {"id": 332, "name": "entity.enderman.ambient"}, {"id": 333, "name": "entity.enderman.death"}, {"id": 334, "name": "entity.enderman.hurt"}, {"id": 335, "name": "entity.enderman.scream"}, {"id": 336, "name": "entity.enderman.stare"}, {"id": 337, "name": "entity.enderman.teleport"}, {"id": 338, "name": "entity.endermite.ambient"}, {"id": 339, "name": "entity.endermite.death"}, {"id": 340, "name": "entity.endermite.hurt"}, {"id": 341, "name": "entity.endermite.step"}, {"id": 342, "name": "entity.ender_pearl.throw"}, {"id": 343, "name": "block.end_gateway.spawn"}, {"id": 344, "name": "block.end_portal_frame.fill"}, {"id": 345, "name": "block.end_portal.spawn"}, {"id": 346, "name": "entity.evoker.ambient"}, {"id": 347, "name": "entity.evoker.cast_spell"}, {"id": 348, "name": "entity.evoker.celebrate"}, {"id": 349, "name": "entity.evoker.death"}, {"id": 350, "name": "entity.evoker_fangs.attack"}, {"id": 351, "name": "entity.evoker.hurt"}, {"id": 352, "name": "entity.evoker.prepare_attack"}, {"id": 353, "name": "entity.evoker.prepare_summon"}, {"id": 354, "name": "entity.evoker.prepare_wololo"}, {"id": 355, "name": "entity.experience_bottle.throw"}, {"id": 356, "name": "entity.experience_orb.pickup"}, {"id": 357, "name": "block.fence_gate.close"}, {"id": 358, "name": "block.fence_gate.open"}, {"id": 359, "name": "item.firecharge.use"}, {"id": 360, "name": "entity.firework_rocket.blast"}, {"id": 361, "name": "entity.firework_rocket.blast_far"}, {"id": 362, "name": "entity.firework_rocket.large_blast"}, {"id": 363, "name": "entity.firework_rocket.large_blast_far"}, {"id": 364, "name": "entity.firework_rocket.launch"}, {"id": 365, "name": "entity.firework_rocket.shoot"}, {"id": 366, "name": "entity.firework_rocket.twinkle"}, {"id": 367, "name": "entity.firework_rocket.twinkle_far"}, {"id": 368, "name": "block.fire.ambient"}, {"id": 369, "name": "block.fire.extinguish"}, {"id": 370, "name": "entity.fish.swim"}, {"id": 371, "name": "entity.fishing_bobber.retrieve"}, {"id": 372, "name": "entity.fishing_bobber.splash"}, {"id": 373, "name": "entity.fishing_bobber.throw"}, {"id": 374, "name": "item.flintandsteel.use"}, {"id": 375, "name": "block.flowering_azalea.break"}, {"id": 376, "name": "block.flowering_azalea.fall"}, {"id": 377, "name": "block.flowering_azalea.hit"}, {"id": 378, "name": "block.flowering_azalea.place"}, {"id": 379, "name": "block.flowering_azalea.step"}, {"id": 380, "name": "entity.fox.aggro"}, {"id": 381, "name": "entity.fox.ambient"}, {"id": 382, "name": "entity.fox.bite"}, {"id": 383, "name": "entity.fox.death"}, {"id": 384, "name": "entity.fox.eat"}, {"id": 385, "name": "entity.fox.hurt"}, {"id": 386, "name": "entity.fox.screech"}, {"id": 387, "name": "entity.fox.sleep"}, {"id": 388, "name": "entity.fox.sniff"}, {"id": 389, "name": "entity.fox.spit"}, {"id": 390, "name": "entity.fox.teleport"}, {"id": 391, "name": "block.roots.break"}, {"id": 392, "name": "block.roots.step"}, {"id": 393, "name": "block.roots.place"}, {"id": 394, "name": "block.roots.hit"}, {"id": 395, "name": "block.roots.fall"}, {"id": 396, "name": "block.furnace.fire_crackle"}, {"id": 397, "name": "entity.generic.big_fall"}, {"id": 398, "name": "entity.generic.burn"}, {"id": 399, "name": "entity.generic.death"}, {"id": 400, "name": "entity.generic.drink"}, {"id": 401, "name": "entity.generic.eat"}, {"id": 402, "name": "entity.generic.explode"}, {"id": 403, "name": "entity.generic.extinguish_fire"}, {"id": 404, "name": "entity.generic.hurt"}, {"id": 405, "name": "entity.generic.small_fall"}, {"id": 406, "name": "entity.generic.splash"}, {"id": 407, "name": "entity.generic.swim"}, {"id": 408, "name": "entity.ghast.ambient"}, {"id": 409, "name": "entity.ghast.death"}, {"id": 410, "name": "entity.ghast.hurt"}, {"id": 411, "name": "entity.ghast.scream"}, {"id": 412, "name": "entity.ghast.shoot"}, {"id": 413, "name": "entity.ghast.warn"}, {"id": 414, "name": "block.gilded_blackstone.break"}, {"id": 415, "name": "block.gilded_blackstone.fall"}, {"id": 416, "name": "block.gilded_blackstone.hit"}, {"id": 417, "name": "block.gilded_blackstone.place"}, {"id": 418, "name": "block.gilded_blackstone.step"}, {"id": 419, "name": "block.glass.break"}, {"id": 420, "name": "block.glass.fall"}, {"id": 421, "name": "block.glass.hit"}, {"id": 422, "name": "block.glass.place"}, {"id": 423, "name": "block.glass.step"}, {"id": 424, "name": "item.glow_ink_sac.use"}, {"id": 425, "name": "entity.glow_item_frame.add_item"}, {"id": 426, "name": "entity.glow_item_frame.break"}, {"id": 427, "name": "entity.glow_item_frame.place"}, {"id": 428, "name": "entity.glow_item_frame.remove_item"}, {"id": 429, "name": "entity.glow_item_frame.rotate_item"}, {"id": 430, "name": "entity.glow_squid.ambient"}, {"id": 431, "name": "entity.glow_squid.death"}, {"id": 432, "name": "entity.glow_squid.hurt"}, {"id": 433, "name": "entity.glow_squid.squirt"}, {"id": 434, "name": "entity.goat.ambient"}, {"id": 435, "name": "entity.goat.death"}, {"id": 436, "name": "entity.goat.eat"}, {"id": 437, "name": "entity.goat.hurt"}, {"id": 438, "name": "entity.goat.long_jump"}, {"id": 439, "name": "entity.goat.milk"}, {"id": 440, "name": "entity.goat.prepare_ram"}, {"id": 441, "name": "entity.goat.ram_impact"}, {"id": 442, "name": "entity.goat.screaming.ambient"}, {"id": 443, "name": "entity.goat.screaming.death"}, {"id": 444, "name": "entity.goat.screaming.eat"}, {"id": 445, "name": "entity.goat.screaming.hurt"}, {"id": 446, "name": "entity.goat.screaming.long_jump"}, {"id": 447, "name": "entity.goat.screaming.milk"}, {"id": 448, "name": "entity.goat.screaming.prepare_ram"}, {"id": 449, "name": "entity.goat.screaming.ram_impact"}, {"id": 450, "name": "entity.goat.step"}, {"id": 451, "name": "block.grass.break"}, {"id": 452, "name": "block.grass.fall"}, {"id": 453, "name": "block.grass.hit"}, {"id": 454, "name": "block.grass.place"}, {"id": 455, "name": "block.grass.step"}, {"id": 456, "name": "block.gravel.break"}, {"id": 457, "name": "block.gravel.fall"}, {"id": 458, "name": "block.gravel.hit"}, {"id": 459, "name": "block.gravel.place"}, {"id": 460, "name": "block.gravel.step"}, {"id": 461, "name": "block.grindstone.use"}, {"id": 462, "name": "block.growing_plant.crop"}, {"id": 463, "name": "entity.guardian.ambient"}, {"id": 464, "name": "entity.guardian.ambient_land"}, {"id": 465, "name": "entity.guardian.attack"}, {"id": 466, "name": "entity.guardian.death"}, {"id": 467, "name": "entity.guardian.death_land"}, {"id": 468, "name": "entity.guardian.flop"}, {"id": 469, "name": "entity.guardian.hurt"}, {"id": 470, "name": "entity.guardian.hurt_land"}, {"id": 471, "name": "block.hanging_roots.break"}, {"id": 472, "name": "block.hanging_roots.fall"}, {"id": 473, "name": "block.hanging_roots.hit"}, {"id": 474, "name": "block.hanging_roots.place"}, {"id": 475, "name": "block.hanging_roots.step"}, {"id": 476, "name": "item.hoe.till"}, {"id": 477, "name": "entity.hoglin.ambient"}, {"id": 478, "name": "entity.hoglin.angry"}, {"id": 479, "name": "entity.hoglin.attack"}, {"id": 480, "name": "entity.hoglin.converted_to_zombified"}, {"id": 481, "name": "entity.hoglin.death"}, {"id": 482, "name": "entity.hoglin.hurt"}, {"id": 483, "name": "entity.hoglin.retreat"}, {"id": 484, "name": "entity.hoglin.step"}, {"id": 485, "name": "block.honey_block.break"}, {"id": 486, "name": "block.honey_block.fall"}, {"id": 487, "name": "block.honey_block.hit"}, {"id": 488, "name": "block.honey_block.place"}, {"id": 489, "name": "block.honey_block.slide"}, {"id": 490, "name": "block.honey_block.step"}, {"id": 491, "name": "item.honeycomb.wax_on"}, {"id": 492, "name": "item.honey_bottle.drink"}, {"id": 493, "name": "entity.horse.ambient"}, {"id": 494, "name": "entity.horse.angry"}, {"id": 495, "name": "entity.horse.armor"}, {"id": 496, "name": "entity.horse.breathe"}, {"id": 497, "name": "entity.horse.death"}, {"id": 498, "name": "entity.horse.eat"}, {"id": 499, "name": "entity.horse.gallop"}, {"id": 500, "name": "entity.horse.hurt"}, {"id": 501, "name": "entity.horse.jump"}, {"id": 502, "name": "entity.horse.land"}, {"id": 503, "name": "entity.horse.saddle"}, {"id": 504, "name": "entity.horse.step"}, {"id": 505, "name": "entity.horse.step_wood"}, {"id": 506, "name": "entity.hostile.big_fall"}, {"id": 507, "name": "entity.hostile.death"}, {"id": 508, "name": "entity.hostile.hurt"}, {"id": 509, "name": "entity.hostile.small_fall"}, {"id": 510, "name": "entity.hostile.splash"}, {"id": 511, "name": "entity.hostile.swim"}, {"id": 512, "name": "entity.husk.ambient"}, {"id": 513, "name": "entity.husk.converted_to_zombie"}, {"id": 514, "name": "entity.husk.death"}, {"id": 515, "name": "entity.husk.hurt"}, {"id": 516, "name": "entity.husk.step"}, {"id": 517, "name": "entity.illusioner.ambient"}, {"id": 518, "name": "entity.illusioner.cast_spell"}, {"id": 519, "name": "entity.illusioner.death"}, {"id": 520, "name": "entity.illusioner.hurt"}, {"id": 521, "name": "entity.illusioner.mirror_move"}, {"id": 522, "name": "entity.illusioner.prepare_blindness"}, {"id": 523, "name": "entity.illusioner.prepare_mirror"}, {"id": 524, "name": "item.ink_sac.use"}, {"id": 525, "name": "block.iron_door.close"}, {"id": 526, "name": "block.iron_door.open"}, {"id": 527, "name": "entity.iron_golem.attack"}, {"id": 528, "name": "entity.iron_golem.damage"}, {"id": 529, "name": "entity.iron_golem.death"}, {"id": 530, "name": "entity.iron_golem.hurt"}, {"id": 531, "name": "entity.iron_golem.repair"}, {"id": 532, "name": "entity.iron_golem.step"}, {"id": 533, "name": "block.iron_trapdoor.close"}, {"id": 534, "name": "block.iron_trapdoor.open"}, {"id": 535, "name": "entity.item_frame.add_item"}, {"id": 536, "name": "entity.item_frame.break"}, {"id": 537, "name": "entity.item_frame.place"}, {"id": 538, "name": "entity.item_frame.remove_item"}, {"id": 539, "name": "entity.item_frame.rotate_item"}, {"id": 540, "name": "entity.item.break"}, {"id": 541, "name": "entity.item.pickup"}, {"id": 542, "name": "block.ladder.break"}, {"id": 543, "name": "block.ladder.fall"}, {"id": 544, "name": "block.ladder.hit"}, {"id": 545, "name": "block.ladder.place"}, {"id": 546, "name": "block.ladder.step"}, {"id": 547, "name": "block.lantern.break"}, {"id": 548, "name": "block.lantern.fall"}, {"id": 549, "name": "block.lantern.hit"}, {"id": 550, "name": "block.lantern.place"}, {"id": 551, "name": "block.lantern.step"}, {"id": 552, "name": "block.large_amethyst_bud.break"}, {"id": 553, "name": "block.large_amethyst_bud.place"}, {"id": 554, "name": "block.lava.ambient"}, {"id": 555, "name": "block.lava.extinguish"}, {"id": 556, "name": "block.lava.pop"}, {"id": 557, "name": "entity.leash_knot.break"}, {"id": 558, "name": "entity.leash_knot.place"}, {"id": 559, "name": "block.lever.click"}, {"id": 560, "name": "entity.lightning_bolt.impact"}, {"id": 561, "name": "entity.lightning_bolt.thunder"}, {"id": 562, "name": "entity.lingering_potion.throw"}, {"id": 563, "name": "entity.llama.ambient"}, {"id": 564, "name": "entity.llama.angry"}, {"id": 565, "name": "entity.llama.chest"}, {"id": 566, "name": "entity.llama.death"}, {"id": 567, "name": "entity.llama.eat"}, {"id": 568, "name": "entity.llama.hurt"}, {"id": 569, "name": "entity.llama.spit"}, {"id": 570, "name": "entity.llama.step"}, {"id": 571, "name": "entity.llama.swag"}, {"id": 572, "name": "entity.magma_cube.death_small"}, {"id": 573, "name": "block.lodestone.break"}, {"id": 574, "name": "block.lodestone.step"}, {"id": 575, "name": "block.lodestone.place"}, {"id": 576, "name": "block.lodestone.hit"}, {"id": 577, "name": "block.lodestone.fall"}, {"id": 578, "name": "item.lodestone_compass.lock"}, {"id": 579, "name": "entity.magma_cube.death"}, {"id": 580, "name": "entity.magma_cube.hurt"}, {"id": 581, "name": "entity.magma_cube.hurt_small"}, {"id": 582, "name": "entity.magma_cube.jump"}, {"id": 583, "name": "entity.magma_cube.squish"}, {"id": 584, "name": "entity.magma_cube.squish_small"}, {"id": 585, "name": "block.medium_amethyst_bud.break"}, {"id": 586, "name": "block.medium_amethyst_bud.place"}, {"id": 587, "name": "block.metal.break"}, {"id": 588, "name": "block.metal.fall"}, {"id": 589, "name": "block.metal.hit"}, {"id": 590, "name": "block.metal.place"}, {"id": 591, "name": "block.metal_pressure_plate.click_off"}, {"id": 592, "name": "block.metal_pressure_plate.click_on"}, {"id": 593, "name": "block.metal.step"}, {"id": 594, "name": "entity.minecart.inside.underwater"}, {"id": 595, "name": "entity.minecart.inside"}, {"id": 596, "name": "entity.minecart.riding"}, {"id": 597, "name": "entity.mooshroom.convert"}, {"id": 598, "name": "entity.mooshroom.eat"}, {"id": 599, "name": "entity.mooshroom.milk"}, {"id": 600, "name": "entity.mooshroom.suspicious_milk"}, {"id": 601, "name": "entity.mooshroom.shear"}, {"id": 602, "name": "block.moss_carpet.break"}, {"id": 603, "name": "block.moss_carpet.fall"}, {"id": 604, "name": "block.moss_carpet.hit"}, {"id": 605, "name": "block.moss_carpet.place"}, {"id": 606, "name": "block.moss_carpet.step"}, {"id": 607, "name": "block.moss.break"}, {"id": 608, "name": "block.moss.fall"}, {"id": 609, "name": "block.moss.hit"}, {"id": 610, "name": "block.moss.place"}, {"id": 611, "name": "block.moss.step"}, {"id": 612, "name": "entity.mule.ambient"}, {"id": 613, "name": "entity.mule.angry"}, {"id": 614, "name": "entity.mule.chest"}, {"id": 615, "name": "entity.mule.death"}, {"id": 616, "name": "entity.mule.eat"}, {"id": 617, "name": "entity.mule.hurt"}, {"id": 618, "name": "music.creative"}, {"id": 619, "name": "music.credits"}, {"id": 620, "name": "music_disc.11"}, {"id": 621, "name": "music_disc.13"}, {"id": 622, "name": "music_disc.blocks"}, {"id": 623, "name": "music_disc.cat"}, {"id": 624, "name": "music_disc.chirp"}, {"id": 625, "name": "music_disc.far"}, {"id": 626, "name": "music_disc.mall"}, {"id": 627, "name": "music_disc.mellohi"}, {"id": 628, "name": "music_disc.pigstep"}, {"id": 629, "name": "music_disc.stal"}, {"id": 630, "name": "music_disc.strad"}, {"id": 631, "name": "music_disc.wait"}, {"id": 632, "name": "music_disc.ward"}, {"id": 633, "name": "music_disc.otherside"}, {"id": 634, "name": "music.dragon"}, {"id": 635, "name": "music.end"}, {"id": 636, "name": "music.game"}, {"id": 637, "name": "music.menu"}, {"id": 638, "name": "music.nether.basalt_deltas"}, {"id": 639, "name": "music.nether.crimson_forest"}, {"id": 640, "name": "music.overworld.dripstone_caves"}, {"id": 641, "name": "music.overworld.grove"}, {"id": 642, "name": "music.overworld.jagged_peaks"}, {"id": 643, "name": "music.overworld.lush_caves"}, {"id": 644, "name": "music.overworld.meadow"}, {"id": 645, "name": "music.nether.nether_wastes"}, {"id": 646, "name": "music.overworld.frozen_peaks"}, {"id": 647, "name": "music.overworld.snowy_slopes"}, {"id": 648, "name": "music.nether.soul_sand_valley"}, {"id": 649, "name": "music.overworld.stony_peaks"}, {"id": 650, "name": "music.nether.warped_forest"}, {"id": 651, "name": "music.under_water"}, {"id": 652, "name": "block.nether_bricks.break"}, {"id": 653, "name": "block.nether_bricks.step"}, {"id": 654, "name": "block.nether_bricks.place"}, {"id": 655, "name": "block.nether_bricks.hit"}, {"id": 656, "name": "block.nether_bricks.fall"}, {"id": 657, "name": "block.nether_wart.break"}, {"id": 658, "name": "item.nether_wart.plant"}, {"id": 659, "name": "block.stem.break"}, {"id": 660, "name": "block.stem.step"}, {"id": 661, "name": "block.stem.place"}, {"id": 662, "name": "block.stem.hit"}, {"id": 663, "name": "block.stem.fall"}, {"id": 664, "name": "block.nylium.break"}, {"id": 665, "name": "block.nylium.step"}, {"id": 666, "name": "block.nylium.place"}, {"id": 667, "name": "block.nylium.hit"}, {"id": 668, "name": "block.nylium.fall"}, {"id": 669, "name": "block.nether_sprouts.break"}, {"id": 670, "name": "block.nether_sprouts.step"}, {"id": 671, "name": "block.nether_sprouts.place"}, {"id": 672, "name": "block.nether_sprouts.hit"}, {"id": 673, "name": "block.nether_sprouts.fall"}, {"id": 674, "name": "block.fungus.break"}, {"id": 675, "name": "block.fungus.step"}, {"id": 676, "name": "block.fungus.place"}, {"id": 677, "name": "block.fungus.hit"}, {"id": 678, "name": "block.fungus.fall"}, {"id": 679, "name": "block.weeping_vines.break"}, {"id": 680, "name": "block.weeping_vines.step"}, {"id": 681, "name": "block.weeping_vines.place"}, {"id": 682, "name": "block.weeping_vines.hit"}, {"id": 683, "name": "block.weeping_vines.fall"}, {"id": 684, "name": "block.wart_block.break"}, {"id": 685, "name": "block.wart_block.step"}, {"id": 686, "name": "block.wart_block.place"}, {"id": 687, "name": "block.wart_block.hit"}, {"id": 688, "name": "block.wart_block.fall"}, {"id": 689, "name": "block.netherite_block.break"}, {"id": 690, "name": "block.netherite_block.step"}, {"id": 691, "name": "block.netherite_block.place"}, {"id": 692, "name": "block.netherite_block.hit"}, {"id": 693, "name": "block.netherite_block.fall"}, {"id": 694, "name": "block.netherrack.break"}, {"id": 695, "name": "block.netherrack.step"}, {"id": 696, "name": "block.netherrack.place"}, {"id": 697, "name": "block.netherrack.hit"}, {"id": 698, "name": "block.netherrack.fall"}, {"id": 699, "name": "block.note_block.basedrum"}, {"id": 700, "name": "block.note_block.bass"}, {"id": 701, "name": "block.note_block.bell"}, {"id": 702, "name": "block.note_block.chime"}, {"id": 703, "name": "block.note_block.flute"}, {"id": 704, "name": "block.note_block.guitar"}, {"id": 705, "name": "block.note_block.harp"}, {"id": 706, "name": "block.note_block.hat"}, {"id": 707, "name": "block.note_block.pling"}, {"id": 708, "name": "block.note_block.snare"}, {"id": 709, "name": "block.note_block.xylophone"}, {"id": 710, "name": "block.note_block.iron_xylophone"}, {"id": 711, "name": "block.note_block.cow_bell"}, {"id": 712, "name": "block.note_block.didgeridoo"}, {"id": 713, "name": "block.note_block.bit"}, {"id": 714, "name": "block.note_block.banjo"}, {"id": 715, "name": "entity.ocelot.hurt"}, {"id": 716, "name": "entity.ocelot.ambient"}, {"id": 717, "name": "entity.ocelot.death"}, {"id": 718, "name": "entity.painting.break"}, {"id": 719, "name": "entity.painting.place"}, {"id": 720, "name": "entity.panda.pre_sneeze"}, {"id": 721, "name": "entity.panda.sneeze"}, {"id": 722, "name": "entity.panda.ambient"}, {"id": 723, "name": "entity.panda.death"}, {"id": 724, "name": "entity.panda.eat"}, {"id": 725, "name": "entity.panda.step"}, {"id": 726, "name": "entity.panda.cant_breed"}, {"id": 727, "name": "entity.panda.aggressive_ambient"}, {"id": 728, "name": "entity.panda.worried_ambient"}, {"id": 729, "name": "entity.panda.hurt"}, {"id": 730, "name": "entity.panda.bite"}, {"id": 731, "name": "entity.parrot.ambient"}, {"id": 732, "name": "entity.parrot.death"}, {"id": 733, "name": "entity.parrot.eat"}, {"id": 734, "name": "entity.parrot.fly"}, {"id": 735, "name": "entity.parrot.hurt"}, {"id": 736, "name": "entity.parrot.imitate.blaze"}, {"id": 737, "name": "entity.parrot.imitate.creeper"}, {"id": 738, "name": "entity.parrot.imitate.drowned"}, {"id": 739, "name": "entity.parrot.imitate.elder_guardian"}, {"id": 740, "name": "entity.parrot.imitate.ender_dragon"}, {"id": 741, "name": "entity.parrot.imitate.endermite"}, {"id": 742, "name": "entity.parrot.imitate.evoker"}, {"id": 743, "name": "entity.parrot.imitate.ghast"}, {"id": 744, "name": "entity.parrot.imitate.guardian"}, {"id": 745, "name": "entity.parrot.imitate.hoglin"}, {"id": 746, "name": "entity.parrot.imitate.husk"}, {"id": 747, "name": "entity.parrot.imitate.illusioner"}, {"id": 748, "name": "entity.parrot.imitate.magma_cube"}, {"id": 749, "name": "entity.parrot.imitate.phantom"}, {"id": 750, "name": "entity.parrot.imitate.piglin"}, {"id": 751, "name": "entity.parrot.imitate.piglin_brute"}, {"id": 752, "name": "entity.parrot.imitate.pillager"}, {"id": 753, "name": "entity.parrot.imitate.ravager"}, {"id": 754, "name": "entity.parrot.imitate.shulker"}, {"id": 755, "name": "entity.parrot.imitate.silverfish"}, {"id": 756, "name": "entity.parrot.imitate.skeleton"}, {"id": 757, "name": "entity.parrot.imitate.slime"}, {"id": 758, "name": "entity.parrot.imitate.spider"}, {"id": 759, "name": "entity.parrot.imitate.stray"}, {"id": 760, "name": "entity.parrot.imitate.vex"}, {"id": 761, "name": "entity.parrot.imitate.vindicator"}, {"id": 762, "name": "entity.parrot.imitate.witch"}, {"id": 763, "name": "entity.parrot.imitate.wither"}, {"id": 764, "name": "entity.parrot.imitate.wither_skeleton"}, {"id": 765, "name": "entity.parrot.imitate.zoglin"}, {"id": 766, "name": "entity.parrot.imitate.zombie"}, {"id": 767, "name": "entity.parrot.imitate.zombie_villager"}, {"id": 768, "name": "entity.parrot.step"}, {"id": 769, "name": "entity.phantom.ambient"}, {"id": 770, "name": "entity.phantom.bite"}, {"id": 771, "name": "entity.phantom.death"}, {"id": 772, "name": "entity.phantom.flap"}, {"id": 773, "name": "entity.phantom.hurt"}, {"id": 774, "name": "entity.phantom.swoop"}, {"id": 775, "name": "entity.pig.ambient"}, {"id": 776, "name": "entity.pig.death"}, {"id": 777, "name": "entity.pig.hurt"}, {"id": 778, "name": "entity.pig.saddle"}, {"id": 779, "name": "entity.pig.step"}, {"id": 780, "name": "entity.piglin.admiring_item"}, {"id": 781, "name": "entity.piglin.ambient"}, {"id": 782, "name": "entity.piglin.angry"}, {"id": 783, "name": "entity.piglin.celebrate"}, {"id": 784, "name": "entity.piglin.death"}, {"id": 785, "name": "entity.piglin.jealous"}, {"id": 786, "name": "entity.piglin.hurt"}, {"id": 787, "name": "entity.piglin.retreat"}, {"id": 788, "name": "entity.piglin.step"}, {"id": 789, "name": "entity.piglin.converted_to_zombified"}, {"id": 790, "name": "entity.piglin_brute.ambient"}, {"id": 791, "name": "entity.piglin_brute.angry"}, {"id": 792, "name": "entity.piglin_brute.death"}, {"id": 793, "name": "entity.piglin_brute.hurt"}, {"id": 794, "name": "entity.piglin_brute.step"}, {"id": 795, "name": "entity.piglin_brute.converted_to_zombified"}, {"id": 796, "name": "entity.pillager.ambient"}, {"id": 797, "name": "entity.pillager.celebrate"}, {"id": 798, "name": "entity.pillager.death"}, {"id": 799, "name": "entity.pillager.hurt"}, {"id": 800, "name": "block.piston.contract"}, {"id": 801, "name": "block.piston.extend"}, {"id": 802, "name": "entity.player.attack.crit"}, {"id": 803, "name": "entity.player.attack.knockback"}, {"id": 804, "name": "entity.player.attack.nodamage"}, {"id": 805, "name": "entity.player.attack.strong"}, {"id": 806, "name": "entity.player.attack.sweep"}, {"id": 807, "name": "entity.player.attack.weak"}, {"id": 808, "name": "entity.player.big_fall"}, {"id": 809, "name": "entity.player.breath"}, {"id": 810, "name": "entity.player.burp"}, {"id": 811, "name": "entity.player.death"}, {"id": 812, "name": "entity.player.hurt"}, {"id": 813, "name": "entity.player.hurt_drown"}, {"id": 814, "name": "entity.player.hurt_freeze"}, {"id": 815, "name": "entity.player.hurt_on_fire"}, {"id": 816, "name": "entity.player.hurt_sweet_berry_bush"}, {"id": 817, "name": "entity.player.levelup"}, {"id": 818, "name": "entity.player.small_fall"}, {"id": 819, "name": "entity.player.splash"}, {"id": 820, "name": "entity.player.splash.high_speed"}, {"id": 821, "name": "entity.player.swim"}, {"id": 822, "name": "entity.polar_bear.ambient"}, {"id": 823, "name": "entity.polar_bear.ambient_baby"}, {"id": 824, "name": "entity.polar_bear.death"}, {"id": 825, "name": "entity.polar_bear.hurt"}, {"id": 826, "name": "entity.polar_bear.step"}, {"id": 827, "name": "entity.polar_bear.warning"}, {"id": 828, "name": "block.polished_deepslate.break"}, {"id": 829, "name": "block.polished_deepslate.fall"}, {"id": 830, "name": "block.polished_deepslate.hit"}, {"id": 831, "name": "block.polished_deepslate.place"}, {"id": 832, "name": "block.polished_deepslate.step"}, {"id": 833, "name": "block.portal.ambient"}, {"id": 834, "name": "block.portal.travel"}, {"id": 835, "name": "block.portal.trigger"}, {"id": 836, "name": "block.powder_snow.break"}, {"id": 837, "name": "block.powder_snow.fall"}, {"id": 838, "name": "block.powder_snow.hit"}, {"id": 839, "name": "block.powder_snow.place"}, {"id": 840, "name": "block.powder_snow.step"}, {"id": 841, "name": "entity.puffer_fish.ambient"}, {"id": 842, "name": "entity.puffer_fish.blow_out"}, {"id": 843, "name": "entity.puffer_fish.blow_up"}, {"id": 844, "name": "entity.puffer_fish.death"}, {"id": 845, "name": "entity.puffer_fish.flop"}, {"id": 846, "name": "entity.puffer_fish.hurt"}, {"id": 847, "name": "entity.puffer_fish.sting"}, {"id": 848, "name": "block.pumpkin.carve"}, {"id": 849, "name": "entity.rabbit.ambient"}, {"id": 850, "name": "entity.rabbit.attack"}, {"id": 851, "name": "entity.rabbit.death"}, {"id": 852, "name": "entity.rabbit.hurt"}, {"id": 853, "name": "entity.rabbit.jump"}, {"id": 854, "name": "event.raid.horn"}, {"id": 855, "name": "entity.ravager.ambient"}, {"id": 856, "name": "entity.ravager.attack"}, {"id": 857, "name": "entity.ravager.celebrate"}, {"id": 858, "name": "entity.ravager.death"}, {"id": 859, "name": "entity.ravager.hurt"}, {"id": 860, "name": "entity.ravager.step"}, {"id": 861, "name": "entity.ravager.stunned"}, {"id": 862, "name": "entity.ravager.roar"}, {"id": 863, "name": "block.nether_gold_ore.break"}, {"id": 864, "name": "block.nether_gold_ore.fall"}, {"id": 865, "name": "block.nether_gold_ore.hit"}, {"id": 866, "name": "block.nether_gold_ore.place"}, {"id": 867, "name": "block.nether_gold_ore.step"}, {"id": 868, "name": "block.nether_ore.break"}, {"id": 869, "name": "block.nether_ore.fall"}, {"id": 870, "name": "block.nether_ore.hit"}, {"id": 871, "name": "block.nether_ore.place"}, {"id": 872, "name": "block.nether_ore.step"}, {"id": 873, "name": "block.redstone_torch.burnout"}, {"id": 874, "name": "block.respawn_anchor.ambient"}, {"id": 875, "name": "block.respawn_anchor.charge"}, {"id": 876, "name": "block.respawn_anchor.deplete"}, {"id": 877, "name": "block.respawn_anchor.set_spawn"}, {"id": 878, "name": "block.rooted_dirt.break"}, {"id": 879, "name": "block.rooted_dirt.fall"}, {"id": 880, "name": "block.rooted_dirt.hit"}, {"id": 881, "name": "block.rooted_dirt.place"}, {"id": 882, "name": "block.rooted_dirt.step"}, {"id": 883, "name": "entity.salmon.ambient"}, {"id": 884, "name": "entity.salmon.death"}, {"id": 885, "name": "entity.salmon.flop"}, {"id": 886, "name": "entity.salmon.hurt"}, {"id": 887, "name": "block.sand.break"}, {"id": 888, "name": "block.sand.fall"}, {"id": 889, "name": "block.sand.hit"}, {"id": 890, "name": "block.sand.place"}, {"id": 891, "name": "block.sand.step"}, {"id": 892, "name": "block.scaffolding.break"}, {"id": 893, "name": "block.scaffolding.fall"}, {"id": 894, "name": "block.scaffolding.hit"}, {"id": 895, "name": "block.scaffolding.place"}, {"id": 896, "name": "block.scaffolding.step"}, {"id": 897, "name": "block.sculk_sensor.clicking"}, {"id": 898, "name": "block.sculk_sensor.clicking_stop"}, {"id": 899, "name": "block.sculk_sensor.break"}, {"id": 900, "name": "block.sculk_sensor.fall"}, {"id": 901, "name": "block.sculk_sensor.hit"}, {"id": 902, "name": "block.sculk_sensor.place"}, {"id": 903, "name": "block.sculk_sensor.step"}, {"id": 904, "name": "entity.sheep.ambient"}, {"id": 905, "name": "entity.sheep.death"}, {"id": 906, "name": "entity.sheep.hurt"}, {"id": 907, "name": "entity.sheep.shear"}, {"id": 908, "name": "entity.sheep.step"}, {"id": 909, "name": "item.shield.block"}, {"id": 910, "name": "item.shield.break"}, {"id": 911, "name": "block.shroomlight.break"}, {"id": 912, "name": "block.shroomlight.step"}, {"id": 913, "name": "block.shroomlight.place"}, {"id": 914, "name": "block.shroomlight.hit"}, {"id": 915, "name": "block.shroomlight.fall"}, {"id": 916, "name": "item.shovel.flatten"}, {"id": 917, "name": "entity.shulker.ambient"}, {"id": 918, "name": "block.shulker_box.close"}, {"id": 919, "name": "block.shulker_box.open"}, {"id": 920, "name": "entity.shulker_bullet.hit"}, {"id": 921, "name": "entity.shulker_bullet.hurt"}, {"id": 922, "name": "entity.shulker.close"}, {"id": 923, "name": "entity.shulker.death"}, {"id": 924, "name": "entity.shulker.hurt"}, {"id": 925, "name": "entity.shulker.hurt_closed"}, {"id": 926, "name": "entity.shulker.open"}, {"id": 927, "name": "entity.shulker.shoot"}, {"id": 928, "name": "entity.shulker.teleport"}, {"id": 929, "name": "entity.silverfish.ambient"}, {"id": 930, "name": "entity.silverfish.death"}, {"id": 931, "name": "entity.silverfish.hurt"}, {"id": 932, "name": "entity.silverfish.step"}, {"id": 933, "name": "entity.skeleton.ambient"}, {"id": 934, "name": "entity.skeleton.converted_to_stray"}, {"id": 935, "name": "entity.skeleton.death"}, {"id": 936, "name": "entity.skeleton_horse.ambient"}, {"id": 937, "name": "entity.skeleton_horse.death"}, {"id": 938, "name": "entity.skeleton_horse.hurt"}, {"id": 939, "name": "entity.skeleton_horse.swim"}, {"id": 940, "name": "entity.skeleton_horse.ambient_water"}, {"id": 941, "name": "entity.skeleton_horse.gallop_water"}, {"id": 942, "name": "entity.skeleton_horse.jump_water"}, {"id": 943, "name": "entity.skeleton_horse.step_water"}, {"id": 944, "name": "entity.skeleton.hurt"}, {"id": 945, "name": "entity.skeleton.shoot"}, {"id": 946, "name": "entity.skeleton.step"}, {"id": 947, "name": "entity.slime.attack"}, {"id": 948, "name": "entity.slime.death"}, {"id": 949, "name": "entity.slime.hurt"}, {"id": 950, "name": "entity.slime.jump"}, {"id": 951, "name": "entity.slime.squish"}, {"id": 952, "name": "block.slime_block.break"}, {"id": 953, "name": "block.slime_block.fall"}, {"id": 954, "name": "block.slime_block.hit"}, {"id": 955, "name": "block.slime_block.place"}, {"id": 956, "name": "block.slime_block.step"}, {"id": 957, "name": "block.small_amethyst_bud.break"}, {"id": 958, "name": "block.small_amethyst_bud.place"}, {"id": 959, "name": "block.small_dripleaf.break"}, {"id": 960, "name": "block.small_dripleaf.fall"}, {"id": 961, "name": "block.small_dripleaf.hit"}, {"id": 962, "name": "block.small_dripleaf.place"}, {"id": 963, "name": "block.small_dripleaf.step"}, {"id": 964, "name": "block.soul_sand.break"}, {"id": 965, "name": "block.soul_sand.step"}, {"id": 966, "name": "block.soul_sand.place"}, {"id": 967, "name": "block.soul_sand.hit"}, {"id": 968, "name": "block.soul_sand.fall"}, {"id": 969, "name": "block.soul_soil.break"}, {"id": 970, "name": "block.soul_soil.step"}, {"id": 971, "name": "block.soul_soil.place"}, {"id": 972, "name": "block.soul_soil.hit"}, {"id": 973, "name": "block.soul_soil.fall"}, {"id": 974, "name": "particle.soul_escape"}, {"id": 975, "name": "block.spore_blossom.break"}, {"id": 976, "name": "block.spore_blossom.fall"}, {"id": 977, "name": "block.spore_blossom.hit"}, {"id": 978, "name": "block.spore_blossom.place"}, {"id": 979, "name": "block.spore_blossom.step"}, {"id": 980, "name": "entity.strider.ambient"}, {"id": 981, "name": "entity.strider.happy"}, {"id": 982, "name": "entity.strider.retreat"}, {"id": 983, "name": "entity.strider.death"}, {"id": 984, "name": "entity.strider.hurt"}, {"id": 985, "name": "entity.strider.step"}, {"id": 986, "name": "entity.strider.step_lava"}, {"id": 987, "name": "entity.strider.eat"}, {"id": 988, "name": "entity.strider.saddle"}, {"id": 989, "name": "entity.slime.death_small"}, {"id": 990, "name": "entity.slime.hurt_small"}, {"id": 991, "name": "entity.slime.jump_small"}, {"id": 992, "name": "entity.slime.squish_small"}, {"id": 993, "name": "block.smithing_table.use"}, {"id": 994, "name": "block.smoker.smoke"}, {"id": 995, "name": "entity.snowball.throw"}, {"id": 996, "name": "block.snow.break"}, {"id": 997, "name": "block.snow.fall"}, {"id": 998, "name": "entity.snow_golem.ambient"}, {"id": 999, "name": "entity.snow_golem.death"}, {"id": 1000, "name": "entity.snow_golem.hurt"}, {"id": 1001, "name": "entity.snow_golem.shoot"}, {"id": 1002, "name": "entity.snow_golem.shear"}, {"id": 1003, "name": "block.snow.hit"}, {"id": 1004, "name": "block.snow.place"}, {"id": 1005, "name": "block.snow.step"}, {"id": 1006, "name": "entity.spider.ambient"}, {"id": 1007, "name": "entity.spider.death"}, {"id": 1008, "name": "entity.spider.hurt"}, {"id": 1009, "name": "entity.spider.step"}, {"id": 1010, "name": "entity.splash_potion.break"}, {"id": 1011, "name": "entity.splash_potion.throw"}, {"id": 1012, "name": "item.spyglass.use"}, {"id": 1013, "name": "item.spyglass.stop_using"}, {"id": 1014, "name": "entity.squid.ambient"}, {"id": 1015, "name": "entity.squid.death"}, {"id": 1016, "name": "entity.squid.hurt"}, {"id": 1017, "name": "entity.squid.squirt"}, {"id": 1018, "name": "block.stone.break"}, {"id": 1019, "name": "block.stone_button.click_off"}, {"id": 1020, "name": "block.stone_button.click_on"}, {"id": 1021, "name": "block.stone.fall"}, {"id": 1022, "name": "block.stone.hit"}, {"id": 1023, "name": "block.stone.place"}, {"id": 1024, "name": "block.stone_pressure_plate.click_off"}, {"id": 1025, "name": "block.stone_pressure_plate.click_on"}, {"id": 1026, "name": "block.stone.step"}, {"id": 1027, "name": "entity.stray.ambient"}, {"id": 1028, "name": "entity.stray.death"}, {"id": 1029, "name": "entity.stray.hurt"}, {"id": 1030, "name": "entity.stray.step"}, {"id": 1031, "name": "block.sweet_berry_bush.break"}, {"id": 1032, "name": "block.sweet_berry_bush.place"}, {"id": 1033, "name": "block.sweet_berry_bush.pick_berries"}, {"id": 1034, "name": "enchant.thorns.hit"}, {"id": 1035, "name": "entity.tnt.primed"}, {"id": 1036, "name": "item.totem.use"}, {"id": 1037, "name": "item.trident.hit"}, {"id": 1038, "name": "item.trident.hit_ground"}, {"id": 1039, "name": "item.trident.return"}, {"id": 1040, "name": "item.trident.riptide_1"}, {"id": 1041, "name": "item.trident.riptide_2"}, {"id": 1042, "name": "item.trident.riptide_3"}, {"id": 1043, "name": "item.trident.throw"}, {"id": 1044, "name": "item.trident.thunder"}, {"id": 1045, "name": "block.tripwire.attach"}, {"id": 1046, "name": "block.tripwire.click_off"}, {"id": 1047, "name": "block.tripwire.click_on"}, {"id": 1048, "name": "block.tripwire.detach"}, {"id": 1049, "name": "entity.tropical_fish.ambient"}, {"id": 1050, "name": "entity.tropical_fish.death"}, {"id": 1051, "name": "entity.tropical_fish.flop"}, {"id": 1052, "name": "entity.tropical_fish.hurt"}, {"id": 1053, "name": "block.tuff.break"}, {"id": 1054, "name": "block.tuff.step"}, {"id": 1055, "name": "block.tuff.place"}, {"id": 1056, "name": "block.tuff.hit"}, {"id": 1057, "name": "block.tuff.fall"}, {"id": 1058, "name": "entity.turtle.ambient_land"}, {"id": 1059, "name": "entity.turtle.death"}, {"id": 1060, "name": "entity.turtle.death_baby"}, {"id": 1061, "name": "entity.turtle.egg_break"}, {"id": 1062, "name": "entity.turtle.egg_crack"}, {"id": 1063, "name": "entity.turtle.egg_hatch"}, {"id": 1064, "name": "entity.turtle.hurt"}, {"id": 1065, "name": "entity.turtle.hurt_baby"}, {"id": 1066, "name": "entity.turtle.lay_egg"}, {"id": 1067, "name": "entity.turtle.shamble"}, {"id": 1068, "name": "entity.turtle.shamble_baby"}, {"id": 1069, "name": "entity.turtle.swim"}, {"id": 1070, "name": "ui.button.click"}, {"id": 1071, "name": "ui.loom.select_pattern"}, {"id": 1072, "name": "ui.loom.take_result"}, {"id": 1073, "name": "ui.cartography_table.take_result"}, {"id": 1074, "name": "ui.stonecutter.take_result"}, {"id": 1075, "name": "ui.stonecutter.select_recipe"}, {"id": 1076, "name": "ui.toast.challenge_complete"}, {"id": 1077, "name": "ui.toast.in"}, {"id": 1078, "name": "ui.toast.out"}, {"id": 1079, "name": "entity.vex.ambient"}, {"id": 1080, "name": "entity.vex.charge"}, {"id": 1081, "name": "entity.vex.death"}, {"id": 1082, "name": "entity.vex.hurt"}, {"id": 1083, "name": "entity.villager.ambient"}, {"id": 1084, "name": "entity.villager.celebrate"}, {"id": 1085, "name": "entity.villager.death"}, {"id": 1086, "name": "entity.villager.hurt"}, {"id": 1087, "name": "entity.villager.no"}, {"id": 1088, "name": "entity.villager.trade"}, {"id": 1089, "name": "entity.villager.yes"}, {"id": 1090, "name": "entity.villager.work_armorer"}, {"id": 1091, "name": "entity.villager.work_butcher"}, {"id": 1092, "name": "entity.villager.work_cartographer"}, {"id": 1093, "name": "entity.villager.work_cleric"}, {"id": 1094, "name": "entity.villager.work_farmer"}, {"id": 1095, "name": "entity.villager.work_fisherman"}, {"id": 1096, "name": "entity.villager.work_fletcher"}, {"id": 1097, "name": "entity.villager.work_leatherworker"}, {"id": 1098, "name": "entity.villager.work_librarian"}, {"id": 1099, "name": "entity.villager.work_mason"}, {"id": 1100, "name": "entity.villager.work_shepherd"}, {"id": 1101, "name": "entity.villager.work_toolsmith"}, {"id": 1102, "name": "entity.villager.work_weaponsmith"}, {"id": 1103, "name": "entity.vindicator.ambient"}, {"id": 1104, "name": "entity.vindicator.celebrate"}, {"id": 1105, "name": "entity.vindicator.death"}, {"id": 1106, "name": "entity.vindicator.hurt"}, {"id": 1107, "name": "block.vine.break"}, {"id": 1108, "name": "block.vine.fall"}, {"id": 1109, "name": "block.vine.hit"}, {"id": 1110, "name": "block.vine.place"}, {"id": 1111, "name": "block.vine.step"}, {"id": 1112, "name": "block.lily_pad.place"}, {"id": 1113, "name": "entity.wandering_trader.ambient"}, {"id": 1114, "name": "entity.wandering_trader.death"}, {"id": 1115, "name": "entity.wandering_trader.disappeared"}, {"id": 1116, "name": "entity.wandering_trader.drink_milk"}, {"id": 1117, "name": "entity.wandering_trader.drink_potion"}, {"id": 1118, "name": "entity.wandering_trader.hurt"}, {"id": 1119, "name": "entity.wandering_trader.no"}, {"id": 1120, "name": "entity.wandering_trader.reappeared"}, {"id": 1121, "name": "entity.wandering_trader.trade"}, {"id": 1122, "name": "entity.wandering_trader.yes"}, {"id": 1123, "name": "block.water.ambient"}, {"id": 1124, "name": "weather.rain"}, {"id": 1125, "name": "weather.rain.above"}, {"id": 1126, "name": "block.wet_grass.break"}, {"id": 1127, "name": "block.wet_grass.fall"}, {"id": 1128, "name": "block.wet_grass.hit"}, {"id": 1129, "name": "block.wet_grass.place"}, {"id": 1130, "name": "block.wet_grass.step"}, {"id": 1131, "name": "entity.witch.ambient"}, {"id": 1132, "name": "entity.witch.celebrate"}, {"id": 1133, "name": "entity.witch.death"}, {"id": 1134, "name": "entity.witch.drink"}, {"id": 1135, "name": "entity.witch.hurt"}, {"id": 1136, "name": "entity.witch.throw"}, {"id": 1137, "name": "entity.wither.ambient"}, {"id": 1138, "name": "entity.wither.break_block"}, {"id": 1139, "name": "entity.wither.death"}, {"id": 1140, "name": "entity.wither.hurt"}, {"id": 1141, "name": "entity.wither.shoot"}, {"id": 1142, "name": "entity.wither_skeleton.ambient"}, {"id": 1143, "name": "entity.wither_skeleton.death"}, {"id": 1144, "name": "entity.wither_skeleton.hurt"}, {"id": 1145, "name": "entity.wither_skeleton.step"}, {"id": 1146, "name": "entity.wither.spawn"}, {"id": 1147, "name": "entity.wolf.ambient"}, {"id": 1148, "name": "entity.wolf.death"}, {"id": 1149, "name": "entity.wolf.growl"}, {"id": 1150, "name": "entity.wolf.howl"}, {"id": 1151, "name": "entity.wolf.hurt"}, {"id": 1152, "name": "entity.wolf.pant"}, {"id": 1153, "name": "entity.wolf.shake"}, {"id": 1154, "name": "entity.wolf.step"}, {"id": 1155, "name": "entity.wolf.whine"}, {"id": 1156, "name": "block.wooden_door.close"}, {"id": 1157, "name": "block.wooden_door.open"}, {"id": 1158, "name": "block.wooden_trapdoor.close"}, {"id": 1159, "name": "block.wooden_trapdoor.open"}, {"id": 1160, "name": "block.wood.break"}, {"id": 1161, "name": "block.wooden_button.click_off"}, {"id": 1162, "name": "block.wooden_button.click_on"}, {"id": 1163, "name": "block.wood.fall"}, {"id": 1164, "name": "block.wood.hit"}, {"id": 1165, "name": "block.wood.place"}, {"id": 1166, "name": "block.wooden_pressure_plate.click_off"}, {"id": 1167, "name": "block.wooden_pressure_plate.click_on"}, {"id": 1168, "name": "block.wood.step"}, {"id": 1169, "name": "block.wool.break"}, {"id": 1170, "name": "block.wool.fall"}, {"id": 1171, "name": "block.wool.hit"}, {"id": 1172, "name": "block.wool.place"}, {"id": 1173, "name": "block.wool.step"}, {"id": 1174, "name": "entity.zoglin.ambient"}, {"id": 1175, "name": "entity.zoglin.angry"}, {"id": 1176, "name": "entity.zoglin.attack"}, {"id": 1177, "name": "entity.zoglin.death"}, {"id": 1178, "name": "entity.zoglin.hurt"}, {"id": 1179, "name": "entity.zoglin.step"}, {"id": 1180, "name": "entity.zombie.ambient"}, {"id": 1181, "name": "entity.zombie.attack_wooden_door"}, {"id": 1182, "name": "entity.zombie.attack_iron_door"}, {"id": 1183, "name": "entity.zombie.break_wooden_door"}, {"id": 1184, "name": "entity.zombie.converted_to_drowned"}, {"id": 1185, "name": "entity.zombie.death"}, {"id": 1186, "name": "entity.zombie.destroy_egg"}, {"id": 1187, "name": "entity.zombie_horse.ambient"}, {"id": 1188, "name": "entity.zombie_horse.death"}, {"id": 1189, "name": "entity.zombie_horse.hurt"}, {"id": 1190, "name": "entity.zombie.hurt"}, {"id": 1191, "name": "entity.zombie.infect"}, {"id": 1192, "name": "entity.zombified_piglin.ambient"}, {"id": 1193, "name": "entity.zombified_piglin.angry"}, {"id": 1194, "name": "entity.zombified_piglin.death"}, {"id": 1195, "name": "entity.zombified_piglin.hurt"}, {"id": 1196, "name": "entity.zombie.step"}, {"id": 1197, "name": "entity.zombie_villager.ambient"}, {"id": 1198, "name": "entity.zombie_villager.converted"}, {"id": 1199, "name": "entity.zombie_villager.cure"}, {"id": 1200, "name": "entity.zombie_villager.death"}, {"id": 1201, "name": "entity.zombie_villager.hurt"}, {"id": 1202, "name": "entity.zombie_villager.step"}]